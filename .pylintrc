[MASTER]
# Use multiple processes to speed up Pylint
jobs=0

[MESSAGES CONTROL]
# Disable specific warnings that match your VS Code settings
disable=C0114,  # missing-module-docstring
        C0115,  # missing-class-docstring
        C0116,  # missing-function-docstring
        C0103,  # invalid-name (for variables like 'e' in except blocks)
        C0301,  # line-too-long
        R0903,  # too-few-public-methods
        R0913,  # too-many-arguments
        R0914,  # too-many-locals
        W0621,  # redefined-outer-name
        W0703   # broad-except

[REPORTS]
# Set the output format
output-format=text

# Include a brief explanation of the error
msg-template={path}:{line}: [{msg_id}({symbol}), {obj}] {msg}

[BASIC]
# Regular expression which should only match function or class names
good-names=i,j,k,ex,Run,_,e

# Maximum number of characters for a line
max-line-length=120

# Regular expression which should only match correct variable names
variable-rgx=[a-z_][a-z0-9_]{2,30}$

# Regular expression which should only match correct function names
function-rgx=[a-z_][a-z0-9_]{2,30}$

# Regular expression which should only match correct class names
class-rgx=[A-Z_][a-zA-Z0-9_]+$

# Regular expression which should only match correct module names
module-rgx=(([a-z_][a-z0-9_]*)|([A-Z][a-zA-Z0-9]+))$

# Regular expression which should only match correct method names
method-rgx=[a-z_][a-z0-9_]{2,30}$

# Regular expression which should only match correct argument names
argument-rgx=[a-z_][a-z0-9_]{2,30}$

# Regular expression which should only match correct variable names
attr-rgx=[a-z_][a-z0-9_]{2,30}$

# Regular expression which should only match correct list comprehension / generator expression variable names
inlinevar-rgx=[A-Za-z_][A-Za-z0-9_]*$

# Regular expression which should only match correct constant names
const-rgx=(([A-Z_][A-Z0-9_]*)|(__.*__))$



[FORMAT]
# Maximum number of characters on a single line
max-line-length=120

# Maximum number of lines in a module
max-module-lines=1000

# String used as indentation unit
indent-string='    '

[SIMILARITIES]
# Minimum lines number of a similarity
min-similarity-lines=4

# Ignore imports when computing similarities
ignore-imports=yes

[MISCELLANEOUS]
# List of note tags to take into consideration
notes=
