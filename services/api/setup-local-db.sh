#!/bin/bash

set -e  # Exit on any error

# Script configuration
ENV=${1:-dev}
RESET=${2:-false}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

show_help() {
    echo "Usage: $0 [environment] [reset]"
    echo
    echo "This script sets up local PostgreSQL databases for Control Tower development."
    echo
    echo "Arguments:"
    echo "  environment: Environment config to use (default: dev)"
    echo "  reset:       'true' to reset existing databases (default: false)"
    echo
    echo "Examples:"
    echo "  $0                    # Setup dev environment"
    echo "  $0 dev                # Setup dev environment"
    echo "  $0 dev true           # Reset and setup dev environment"
    echo
    echo "Prerequisites:"
    echo "  - PostgreSQL running on localhost:5432"
    echo "  - .env file configured in this directory"
    echo "  - psql command available"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if help was requested
    if [[ "$1" == "-h" ]] || [[ "$1" == "--help" ]] || [[ "$1" == "help" ]]; then
        show_help
        exit 0
    fi

    # Check if psql is available
    if ! command -v psql >/dev/null 2>&1; then
        log_error "PostgreSQL client (psql) is not installed or not in PATH"
        echo
        echo "To install PostgreSQL client:"
        echo "  • macOS (Homebrew):    brew install postgresql@17"
        echo "  • macOS (MacPorts):    sudo port install postgresql17"
        echo "  • Windows:             Download from https://www.postgresql.org/download/windows/"
        echo
        exit 1
    fi

    # Check psql client version for PostgreSQL 17 compatibility
    local psql_version=$(psql --version | awk '{print $3}' | cut -d. -f1)
    if [[ "$psql_version" -lt 17 ]]; then
        log_warning "PostgreSQL client version $psql_version detected"
        log_warning "PostgreSQL 17+ client recommended for best compatibility with PostgreSQL 17 server"
        echo
        echo "To upgrade PostgreSQL client:"
        echo "  • macOS (Homebrew):    brew unlink postgresql@$psql_version && brew install postgresql@17 && brew link postgresql@17"
        echo
        echo "Continue anyway? (older clients may experience connection issues)"
        read -p "Press Enter to continue or Ctrl+C to abort..."
    fi

    # Check if yarn is available
    if ! command -v yarn >/dev/null 2>&1; then
        log_error "Yarn is required but not installed"
        echo "Install Yarn: https://classic.yarnpkg.com/en/docs/install"
        exit 1
    fi

    # Check if jq is available for JSON parsing
    if ! command -v jq >/dev/null 2>&1; then
        log_error "jq is required but not installed"
        echo "Install jq: https://jqlang.github.io/jq/download/"
        echo "  • macOS (Homebrew):    brew install jq"
        echo "  • Ubuntu/Debian:       sudo apt-get install jq"
        echo "  • Windows (Chocolatey): choco install jq"
        echo "  • Windows (winget):     winget install jqlang.jq"
        exit 1
    fi

    # Check if .env file exists
    if [[ ! -f ".env" ]]; then
        log_error "Missing .env file in services/api directory"
        echo "Please create .env with your PostgreSQL configuration:"
        echo "  POSTGRES_USERNAME=postgres"
        echo "  POSTGRES_PASSWORD=your_password"
        exit 1
    fi

    # Load environment variables
    source .env

    # Check required environment variables
    if [[ -z "$POSTGRES_USERNAME" ]] || [[ -z "$POSTGRES_PASSWORD" ]]; then
        log_error "Missing required environment variables in .env"
        echo "Required variables: POSTGRES_USERNAME, POSTGRES_PASSWORD"
        exit 1
    fi

    # Test database connection
    log_info "Testing database connection..."
    if ! PGPASSWORD=$POSTGRES_PASSWORD psql -h localhost -p 5432 -U $POSTGRES_USERNAME -c "SELECT 1;" >/dev/null 2>&1; then
        log_error "Cannot connect to PostgreSQL on localhost:5432"
        echo
        echo "Please ensure:"
        echo "  • PostgreSQL is running on localhost:5432"
        echo "  • Username '$POSTGRES_USERNAME' exists"
        echo "  • Password is correct in .env file"
        echo "  • Database server accepts connections"
        echo
        echo "To start PostgreSQL:"
        echo "  • Docker:     docker-compose up"
        exit 1
    fi

    log_success "Prerequisites check passed"
}

check_tenant_config() {
    local tenant_config_file="../../config/tenants/${ENV}.tenants.json"
    
    if [[ ! -f "$tenant_config_file" ]]; then
        log_error "Tenant config file not found: $tenant_config_file"
        echo "Available configs:"
        ls -la ../../config/tenants/*.tenants.json 2>/dev/null || echo "  No tenant config files found"
        exit 1
    fi
    
    # Extract tenant names from JSON
    local tenants=$(jq -r '.[].name' "$tenant_config_file" | tr '\n' ' ')
    if [[ -z "$tenants" ]]; then
        log_error "No tenants found in $tenant_config_file"
        exit 1
    fi
    
    log_info "Using environment: $ENV"
    log_info "Tenants to create: $tenants"
}

generate_sql_inserts() {
    log_info "Generating default configuration SQL..."
    
    cd libs/api/ict-api-schema
    
    # Generate settings SQL
    if ! yarn run db:build-insert; then
        log_error "Failed to generate settings SQL"
        exit 1
    fi
    
    # Generate process flow SQL  
    if ! yarn run db:build-insert-process-flow; then
        log_error "Failed to generate process flow SQL"
        exit 1
    fi

    # Generate tenant configs SQL
    if ! yarn run db:build-tenant-configs; then
        log_error "Failed to generate tenant configs SQL"
        exit 1
    fi
    
    cd "$SCRIPT_DIR"
    log_success "SQL generation complete"
}

setup_databases() {
    log_info "Setting up tenant databases..."
    
    # Create tenant databases
    if ! ./local-postgres-ops.sh create-tenants "$ENV"; then
        log_error "Failed to create tenant databases"
        exit 1
    fi
    
    # Run migrations
    log_info "Running database migrations..."
    if ! ./local-postgres-ops.sh migrate "$ENV"; then
        log_error "Failed to run migrations"
        exit 1
    fi
    
    # Seed data
    log_info "Seeding default data..."
    if ! ./local-postgres-ops.sh seed "$ENV"; then
        log_error "Failed to seed data"
        exit 1
    fi
    
    log_success "Database setup complete"
}

reset_databases() {
    log_warning "Resetting databases (this will delete all data!)"
    
    # Load environment variables
    source .env
    
    # Get tenant list
    local tenant_config_file="../../config/tenants/${ENV}.tenants.json"
    local tenants=$(jq -r '.[].name' "$tenant_config_file" | tr '\n' ' ')
    
    # Drop each tenant database
    for tenant in $tenants; do
        log_info "Dropping database: $tenant"
        # Check if database exists before dropping
        if PGPASSWORD=$POSTGRES_PASSWORD psql -h localhost -p 5432 -U $POSTGRES_USERNAME -lqt | cut -d \| -f 1 | grep -qw $tenant; then
            PGPASSWORD=$POSTGRES_PASSWORD psql -h localhost -p 5432 -U $POSTGRES_USERNAME -c "DROP DATABASE $tenant;" 2>/dev/null || true
        else
            log_info "Database $tenant does not exist, skipping drop"
        fi
    done
    
    log_success "Database reset complete"
}

main() {
    echo "🚀 Control Tower Database Setup"
    echo "================================"
    
    check_prerequisites "$@"
    check_tenant_config
    
    if [[ "$RESET" == "true" ]]; then
        reset_databases
    fi
    
    generate_sql_inserts
    setup_databases
    
    echo
    log_success "🎉 Database setup completed successfully!"
    echo
    echo "Next steps:"
    echo "  • Start your API services"
    echo "  • Run your application"
    echo "  • Check database connections"
}

# Run main function with all arguments
main "$@" 