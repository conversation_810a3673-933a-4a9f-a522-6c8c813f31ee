ARTIFACTORY_AUTH_TOKEN=xxx

# Toggle this to true to enable Redis for local development. Note that unit tests will fail if this is set to true.
REDIS_REQUEST_CACHE_OVERRIDE=false
# Metric API - Metrics Endpoint
METRIC_PROCESSOR_METRICS_API_URL="http://ict-metric-processor-metric-processor-1:8085"

# Data Explorer 
# Run this in google cloud shell paste result below -> gcloud auth print-identity-token --impersonate-service-account=SERVICE_ACCOUNT_EMAIL --audiences=AUDIENCE
# LOCAL_ID_TOKEN=xxx

# Simulation API
SIMULATION_API_URL="https://canvas-gateway-6uhnuu6m.uc.gateway.dev"
SIMULATION_API_KEY=projects/ict-d-api/secrets/ict-dev-simulation-api-key/versions/latest

AIML_DATA_EXPLORER_URL="https://opendataqnadev-v1-ai.ops.dematic.dev"
AGENT_AI_API_URL="https://weather-time-agent-************.us-central1.run.app"

# Admin API
ADMIN_API_URL="https://dev-zmogq9vd.us.auth0.com"
ADMIN_API_KEY=projects/ict-d-api/secrets/ict-dev-auth0-management-client-id/versions/latest

# Agent AI API
GOLD_QUESTIONS_URL="https://ragengine.ops.dematic.dev"
GOLD_QUESTIONS_AUDIENCE_URL="https://ragengine-************.us-central1.run.app"
AGENT_SEARCH_URL="https://dematic-chat.ops.dematic.dev"
AGENT_SEARCH_AUDIENCE_URL="https://dematic-chat-************.us-central1.run.app"

# Availability API
SYSTEM_AVAILABILITY_PROJECT_ID="edp-d-us-east2-etl"
SYSTEM_AVAILABILITY_URL="http://host.docker.internal:3000"
