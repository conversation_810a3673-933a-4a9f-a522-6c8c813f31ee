import js from '@eslint/js';
import tsParser from '@typescript-eslint/parser';
import tsPlugin from '@typescript-eslint/eslint-plugin';
import nPlugin from 'eslint-plugin-n';
import importPlugin from 'eslint-plugin-import';
import prettierConfig from 'eslint-config-prettier';
import globals from 'globals';

/** @type {import('eslint').Linter.Config[]} */
export default [
  // Global ignores
  {
    ignores: [
      "**/node_modules/",
      "**/dist/",
      "**/build/",
      "**/coverage/",
      "**/*.generated.ts",
      "**/migrations/",
      "**/test-results/",
      "**/*.d.ts"
    ]
  },

  // Base JavaScript configuration
  js.configs.recommended,

  // TypeScript files configuration
  {
    files: ["**/*.ts", "**/*.mts", "**/*.cts"],
    languageOptions: {
      ecmaVersion: 2020,
      sourceType: "module",
      parser: tsParser,
      parserOptions: {
        ecmaVersion: 2020,
        sourceType: "module"
      },
      globals: {
        ...globals.node,
        ...globals.es2020
      }
    },
    plugins: {
      "@typescript-eslint": tsPlugin,
      "n": nPlugin,
      "import": importPlugin
    },
    rules: {
      // Apply TypeScript recommended rules
      ...tsPlugin.configs.recommended.rules,

      // Original custom rules from .eslintrc.json
      "n/no-extraneous-import": "off",
      "class-methods-use-this": "off",
      "no-use-before-define": "off",
      "no-shadow": "off", // fixes incorrect no-shadow errors when using typescript
      "@typescript-eslint/no-shadow": "error",
      "no-return-await": "off", // deprecated rule as of eslint v8.46.0
      "prefer-destructuring": "off",
      "no-useless-constructor": "off", // handles our service and store constructors
      "no-empty-function": ["error", { "allow": ["constructors"] }],
      "prefer-object-spread": "off",
      "lines-between-class-members": "off",
      "guard-for-in": "off",
      "no-plusplus": ["error", { "allowForLoopAfterthoughts": true }],
      "no-restricted-syntax": "off",
      "camelcase": "off",
      "@typescript-eslint/prefer-namespace-keyword": "off",
      "@typescript-eslint/no-namespace": "off",
      "default-param-last": "off", // TODO: Decide if we are ok disabling this rule?  If not, create ticket to fix this and then renable rule.

      // Additional rules from the extends configs
      "n/no-unpublished-import": "off",
      "no-unused-vars": "off",
      "@typescript-eslint/no-unused-vars": [
        "error",
        {
          "argsIgnorePattern": "^_",
          "varsIgnorePattern": "^_",
          "caughtErrorsIgnorePattern": "^_"
        }
      ],

      // Relax some newer TypeScript rules for compatibility
      "@typescript-eslint/no-empty-object-type": "off",
      "@typescript-eslint/no-unused-expressions": "off",
      "@typescript-eslint/ban-types": "off", // Disable ban-types to allow existing code patterns
      
      // Allow console and process for Node.js environment
      "no-console": "off",
      "no-undef": "off" // Disabled since we're using TypeScript
    }
  },

  // Test files configuration
  {
    files: ["**/*.spec.ts", "**/*.test.ts", "**/test/**/*.ts"],
    languageOptions: {
      ecmaVersion: 2020,
      sourceType: "module",
      parser: tsParser,
      parserOptions: {
        ecmaVersion: 2020,
        sourceType: "module"
      },
      globals: {
        ...globals.node,
        ...globals.es2020,
        ...globals.mocha
      }
    },
    plugins: {
      "@typescript-eslint": tsPlugin,
      "n": nPlugin,
      "import": importPlugin
    },
    rules: {
      // Apply TypeScript recommended rules
      ...tsPlugin.configs.recommended.rules,

      // Test-specific rule overrides
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-unused-expressions": "off",
      "@typescript-eslint/ban-ts-comment": "off",
      "n/no-extraneous-import": "off",
      "n/no-unpublished-import": "off",
      "no-unused-vars": "off",
      "@typescript-eslint/no-unused-vars": "off",
      "no-undef": "off",
      "no-console": "off",
      "@typescript-eslint/no-empty-object-type": "off",
      "@typescript-eslint/ban-types": "off" // Disable ban-types for test files too
    }
  },

  // JavaScript/MJS files configuration  
  {
    files: ["**/*.js", "**/*.mjs", "**/*.cjs"],
    languageOptions: {
      ecmaVersion: 2020,
      sourceType: "module",
      globals: {
        ...globals.node,
        ...globals.es2020
      }
    },
    plugins: {
      "n": nPlugin
    },
    rules: {
      "n/no-extraneous-import": "off",
      "class-methods-use-this": "off",
      "no-use-before-define": "off",
      "prefer-destructuring": "off",
      "no-useless-constructor": "off",
      "no-empty-function": ["error", { "allow": ["constructors"] }],
      "prefer-object-spread": "off",
      "lines-between-class-members": "off",
      "guard-for-in": "off",
      "no-plusplus": ["error", { "allowForLoopAfterthoughts": true }],
      "no-restricted-syntax": "off",
      "camelcase": "off",
      "n/no-unpublished-import": "off",
      "no-console": "off"
    }
  },

  // Prettier configuration (must be last)
  prettierConfig
]; 