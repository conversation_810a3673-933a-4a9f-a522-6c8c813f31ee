events {}

http {
    resolver 127.0.0.11;
    client_max_body_size 100M;     
    server {
        listen 80;

        location /ai/ {
            proxy_pass http://api:8080$request_uri;
        }

        location /config/ {
            proxy_pass http://api:8081$request_uri;
        }

        location /data-explorer/ {
            proxy_pass http://api:8082$request_uri;
        }

        location /diagnostics/ {
            proxy_pass http://api:8083$request_uri;
        }

        location /equipment/ {
            proxy_pass http://api:8084$request_uri;
        }

        location /inventory/ {
            proxy_pass http://api:8085$request_uri;
        }

        location /migrations/ {
            proxy_pass http://api:8086$request_uri;
        }

        location /operators/ {
            proxy_pass http://api:8087$request_uri;
        }

        location /orders/ {
            proxy_pass http://api:8088$request_uri;
        }

        location /tableau/ {
            proxy_pass http://api:8089$request_uri;
        }

        location /mock/ {
            proxy_pass http://api:8090$request_uri;
        }

        location /workstation/ {
            proxy_pass http://api:8091$request_uri;
        }

        location /simulation/ {
            proxy_pass http://api:8092$request_uri;
        }

        location /admin/ {
            proxy_pass http://api:8093$request_uri;
        }

        location /availability/ {
            proxy_pass http://api:8094$request_uri;
        }
    }
}