import {
  ApiResponseArray,
  ExtractedPaginatedRequestNoDates,
} from 'ict-api-foundations';
import {Role} from '@ict/sdk-foundations/types/defs/admin-defs.ts';

export interface Auth0User {
  user_id: string;
  email: string;
  email_verified: boolean;
  name?: string;
  picture?: string;
  created_at: string;
  updated_at: string;
  last_login?: string;
  logins_count: number;
  app_metadata?: Record<string, unknown>;
  user_metadata?: Record<string, unknown>;
  identities?: Auth0Identity[];
  roles?: string[];
}

export interface Auth0Identity {
  connection: string;
  user_id: string;
  provider: string;
  isSocial: boolean;
}

export interface Auth0Role {
  id: string;
  name: string;
  description?: string;
}

export interface Auth0UsersResponse {
  users: Auth0User[];
  start: number;
  limit: number;
  length: number;
  total: number;
}

export interface Auth0Organization {
  id: string;
  name: string;
  display_name: string;
}

export interface UserInfo {
  id: string;
  email: string;
  name?: string;
  emailVerified: boolean;
  createdAt: string;
  lastLogin?: string;
  loginCount: number;
  /**
   * Roles are not returned in the list view, but are returned in the GetUser response
   */
  roles?: Role[];
  isSocialAuth: boolean;
}

export interface UsersListQueryParams extends ExtractedPaginatedRequestNoDates {
  // No additional parameters needed, all comes from ExtractedPaginatedRequestNoDates
}

export type UsersListData = ApiResponseArray<
  UserInfo[],
  {
    page: number;
    limit: number;
    totalResults: number;
  }
>;

export interface AssignRoleRequest {
  roleNames: string[];
}

export interface RemoveRoleResponse {
  success: boolean;
  message: string;
}

export interface AssignRoleResponse {
  success: boolean;
  message: string;
  assignedRoles: Role[];
}
