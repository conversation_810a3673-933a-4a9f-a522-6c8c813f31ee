import {GetRolesResponse} from '@ict/sdk-foundations/types/defs/admin-defs.ts';
import {
  Container,
  ContextService,
  IctError,
  extractPaginatedRequestNoDates,
  type PaginatedRequestNoDates,
  ResendEmailVerificationResponse,
  WinstonLogger,
} from 'ict-api-foundations';
import {
  Body,
  Controller,
  Delete,
  Get,
  OperationId,
  Path,
  Post,
  Route,
  SuccessResponse,
  Tags,
} from 'tsoa';
import {
  UsersListData,
  UserInfo,
  type AssignRoleRequest,
  type AssignRoleResponse,
  type RemoveRoleResponse,
} from '../defs/users-list-def.ts';
import {AuthManagementService} from '../services/auth-management-service.ts';
import {RoleService} from '../services/role-service.ts';
import {UsersService} from '../services/users-service.ts';

@Route('admin/auth')
export class AuthManagementController extends Controller {
  private logger: WinstonLogger;

  constructor() {
    super();
    this.logger = Container.get(WinstonLogger);
  }

  @SuccessResponse('200', 'Email verification resent successfully')
  @Post('/resend-verification')
  @OperationId('ResendEmailVerification')
  @Tags('admin')
  public async resendEmailVerification(): Promise<ResendEmailVerificationResponse> {
    const context = Container.get(ContextService);
    const userId = context.userId;

    if (!userId) {
      throw IctError.unauthorized('User ID not found in context');
    }

    this.logger.info('Resending email verification', {
      userId,
      context: {
        hasUserId: !!context.userId,
        rawContext: context,
      },
    });

    const authManagementService = Container.get(AuthManagementService);
    const result = await authManagementService.resendEmailVerification(userId);

    return result;
  }

  @SuccessResponse('200', 'Roles retrieved successfully')
  @Get('/roles')
  @OperationId('GetAssignableRoles')
  @Tags('admin')
  public async getRoles(): Promise<GetRolesResponse> {
    const context = Container.get(ContextService);
    const userId = context.userId;

    if (!userId) {
      throw IctError.unauthorized('User ID not found in context');
    }

    this.logger.info('Getting assignable roles', {
      userId,
    });

    const roleService = Container.get(RoleService);
    const roles = await roleService.getAssignableRoles();

    return roles;
  }

  @SuccessResponse('200', 'Users retrieved successfully')
  @Post('/users/list')
  @OperationId('GetUsersList')
  @Tags('admin')
  public async getUsersList(
    @Body() paginatedRequest: PaginatedRequestNoDates,
  ): Promise<UsersListData> {
    const context = Container.get(ContextService);
    const userId = context.userId;

    if (!userId) {
      throw IctError.unauthorized('User ID not found in context');
    }

    this.logger.info('Getting users list', {
      userId,
    });

    const usersService = Container.get(UsersService);
    const queryParams = extractPaginatedRequestNoDates(paginatedRequest);
    const result = await usersService.getUsersList(queryParams);

    return result;
  }

  @SuccessResponse('200', 'User retrieved successfully')
  @Get('/users/{id}')
  @OperationId('GetUser')
  @Tags('admin')
  public async getUser(@Path() id: string): Promise<UserInfo> {
    const context = Container.get(ContextService);
    const userId = context.userId;

    if (!userId) {
      throw IctError.unauthorized('User ID not found in context');
    }

    this.logger.info('Getting user by ID', {
      userId,
      targetUserId: id,
    });

    const usersService = Container.get(UsersService);
    const result = await usersService.getUserById(id);

    return result;
  }

  @SuccessResponse('200', 'Roles assigned successfully')
  @Post('/users/{userId}/roles')
  @OperationId('AssignUserRoles')
  @Tags('admin')
  public async assignUserRoles(
    @Path() userId: string,
    @Body() request: AssignRoleRequest,
  ): Promise<AssignRoleResponse> {
    const context = Container.get(ContextService);
    const currentUserId = context.userId;

    if (!currentUserId) {
      throw IctError.unauthorized('User ID not found in context');
    }

    this.logger.info('Assigning user roles', {
      currentUserId,
      targetUserId: userId,
      roleNames: request.roleNames,
    });

    const roleService = Container.get(RoleService);
    const result = await roleService.assignUserOrganizationRoles(
      userId,
      request.roleNames,
    );

    return result;
  }

  @SuccessResponse('200', 'Role removed successfully')
  @Delete('/users/{userId}/roles/{roleName}')
  @OperationId('RemoveUserRole')
  @Tags('admin')
  public async removeUserRole(
    @Path() userId: string,
    @Path() roleName: string,
  ): Promise<RemoveRoleResponse> {
    const context = Container.get(ContextService);
    const currentUserId = context.userId;

    if (!currentUserId) {
      throw IctError.unauthorized('User ID not found in context');
    }

    this.logger.info('Removing user role', {
      currentUserId,
      targetUserId: userId,
      roleName,
    });

    const roleService = Container.get(RoleService);
    const result = await roleService.removeUserOrganizationRole(
      userId,
      roleName,
    );

    return result;
  }
}
