import {BaseHealthCheck} from 'ict-api-foundations';
import {Get, Route, OperationId, Tags} from 'tsoa';

@Route('/admin/healthcheck')
export class HealthCheckController {
  @Get()
  @OperationId('GetAdminBasicHealthCheck')
  @Tags('admin')
  public getAdminBasicHealthCheck() {
    return BaseHealthCheck.getBasicHealthCheck();
  }

  @Get('/full')
  @OperationId('GetAdminFullHealthCheck')
  @Tags('admin')
  public getAdminFullHealthCheck() {
    return BaseHealthCheck.getDeepHealthCheck(['auth0', 'bigQuery', 'redis']);
  }
}
