import {
  DiService,
  EnvironmentService,
  Ict<PERSON>rror,
  <PERSON><PERSON><PERSON><PERSON>,
  Con<PERSON>er,
  GCPSecretManager,
  GCPSecretManagerQuery,
  HttpStatusCodes,
} from 'ict-api-foundations';
import axios, {AxiosResponse} from 'axios';
import {
  Auth0ManagementApiCredentials,
  Auth0TokenResponse,
  Auth0VerificationResponse,
  EmailVerificationResult,
} from '../defs/auth-management';

const AUTH0_API_VERSION = 'v2';
const AUTH0_TOKEN_PATH = 'oauth/token';
const AUTH0_VERIFICATION_PATH = 'jobs/verification-email';

@DiService()
export class AuthManagementService {
  private managementApiToken: string | null = null;
  private tokenExpiry = 0;
  private logger: WinstonLogger;
  private envService: EnvironmentService;
  private secretManager: GCPSecretManager;

  constructor() {
    this.logger = Container.get(WinstonLogger);
    this.envService = Container.get(EnvironmentService);
    this.secretManager = Container.get(GCPSecretManager);
    this.logger.info('AuthManagementService initialized');
  }

  private validateAuth0Config(): void {
    const authDomain = process.env.ADMIN_API_URL;
    const adminApiKey = process.env.ADMIN_API_KEY;

    if (!authDomain) {
      this.logger.error('ADMIN_API_URL environment variable is not set');
      throw IctError.internalServerError(
        'ADMIN_API_URL environment variable is required but not configured',
      );
    }

    if (!adminApiKey) {
      this.logger.error('ADMIN_API_KEY environment variable is not set');
      throw IctError.internalServerError(
        'ADMIN_API_KEY environment variable is required but not configured',
      );
    }
  }

  private async getAuth0Secrets(): Promise<Auth0ManagementApiCredentials> {
    const env = process.env.ENVIRONMENT;
    const adminApiKey = process.env.ADMIN_API_KEY;

    if (!adminApiKey) {
      this.logger.error('ADMIN_API_KEY environment variable is not set');
      throw IctError.internalServerError(
        'ADMIN_API_KEY environment variable is required but not configured',
      );
    }

    let credentialsJson: string;

    if (!this.secretManager) {
      this.logger.error('Secret manager not available');
      throw IctError.internalServerError(
        'A secret manager is required for accessing Auth0 secrets.',
      );
    }

    this.logger.info(`Retrieving Auth0 secret: ${adminApiKey}`, {
      environment: env || 'undefined',
    });

    try {
      const secretQuery = new GCPSecretManagerQuery();
      secretQuery.name = adminApiKey;
      credentialsJson = await this.secretManager.get(secretQuery);
    } catch (error) {
      this.logger.error(`Failed to retrieve Auth0 secret ${adminApiKey}`, {
        error,
        environment: env || 'undefined',
      });
      throw IctError.internalServerError(
        `Failed to retrieve Auth0 credentials from secret manager: ${adminApiKey}`,
        error,
      );
    }

    if (!credentialsJson) {
      this.logger.error('Auth0 credentials are empty after retrieval');
      throw IctError.internalServerError(
        'Auth0 credentials are empty - check secret configuration',
      );
    }

    try {
      const credentials = JSON.parse(
        credentialsJson,
      ) as Auth0ManagementApiCredentials;

      if (!credentials.clientId || !credentials.clientSecret) {
        this.logger.error(
          'Invalid Auth0 Management API credentials format in Secret Manager',
        );
        throw IctError.internalServerError(
          'Invalid Auth0 Management API credentials in Secret Manager',
        );
      }

      return credentials;
    } catch (parseError) {
      this.logger.error('Failed to parse Auth0 credentials JSON', {
        error: parseError,
      });
      throw IctError.internalServerError(
        'Failed to parse Auth0 credentials - invalid JSON format',
      );
    }
  }

  public async getManagementApiToken(): Promise<string> {
    const now = Date.now();

    if (this.managementApiToken && now < this.tokenExpiry) {
      return this.managementApiToken;
    }

    this.validateAuth0Config();
    const authDomain = process.env.ADMIN_API_URL;

    try {
      const credentials = await this.getAuth0Secrets();

      const tokenUrl = `${authDomain}/${AUTH0_TOKEN_PATH}`;
      const audienceUrl = `${authDomain}/api/v2/`;

      this.logger.debug('Requesting Auth0 Management API token', {
        environment: this.envService.app.env,
      });

      const response: AxiosResponse<Auth0TokenResponse> = await axios.post(
        tokenUrl,
        {
          client_id: credentials.clientId,
          client_secret: credentials.clientSecret,
          audience: audienceUrl,
          grant_type: 'client_credentials',
          scope: 'read:users update:users create:user_tickets',
        },
      );

      this.managementApiToken = response.data.access_token;
      if (!this.managementApiToken) {
        throw IctError.internalServerError(
          'Failed to get valid token from Auth0',
        );
      }
      this.tokenExpiry = now + response.data.expires_in * 1000;
      return this.managementApiToken;
    } catch (error) {
      this.logger.error('Failed to get Auth0 Management API token', {
        error: {
          message: error instanceof Error ? error.message : 'Unknown error',
          status: axios.isAxiosError(error)
            ? error.response?.status
            : undefined,
          data: axios.isAxiosError(error)
            ? {
                error: error.response?.data?.error,
                message: error.response?.data?.message,
              }
            : undefined,
        },
        environment: this.envService.app.env,
      });

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          throw IctError.unauthorized('Invalid Auth0 credentials');
        }
        if (error.response?.status === 403) {
          this.logger.error('Auth0 Management API permission error', {
            error: {
              status: error.response.status,
              data: {
                error: error.response.data?.error,
                message: error.response.data?.message,
              },
            },
          });
          throw IctError.forbidden(
            'Insufficient permissions for Auth0 Management API',
          );
        }
        if (error.response?.status === 429) {
          const headers = error.response.headers;
          this.logger.error('Auth0 rate limit exceeded', {
            error: {
              status: error.response.status,
              data: {
                error: error.response.data?.error,
                message: error.response.data?.message,
              },
              rateLimit: {
                limit: headers['x-ratelimit-limit'],
                remaining: headers['x-ratelimit-remaining'],
                reset: headers['x-ratelimit-reset'],
              },
            },
          });
          throw new IctError(
            HttpStatusCodes.TOO_MANY_REQUESTS,
            'Too many verification email requests. Please try again in 24 hours.',
            error.response.data,
          );
        }
        if (
          error.response?.status === 400 &&
          error.response?.data?.errorCode === 'operation_not_supported'
        ) {
          throw IctError.badRequest(
            'Operation not supported - user may have social connection',
          );
        }
      }

      throw IctError.internalServerError('Failed to authenticate with Auth0');
    }
  }

  public async resendEmailVerification(
    userId: string,
  ): Promise<EmailVerificationResult> {
    try {
      this.validateAuth0Config();
      const authDomain = process.env.ADMIN_API_URL;

      // Construct the full verification URL with /api/v2/ path
      const verificationUrl = `${authDomain}/api/${AUTH0_API_VERSION}/${AUTH0_VERIFICATION_PATH}`;

      const token = await this.getManagementApiToken();

      this.logger.debug('Making Auth0 verification request', {
        userId,
      });

      const response: AxiosResponse<Auth0VerificationResponse> =
        await axios.post(
          verificationUrl,
          {
            user_id: userId,
          },
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          },
        );

      if (!response.data || !response.data.id) {
        throw IctError.internalServerError(
          'Invalid response from Auth0 verification email request',
        );
      }

      this.logger.info('Email verification resent successfully', {
        userId,
        verificationJobId: response.data.id,
        status: response.data.status,
      });

      return {
        success: true,
        message: 'Verification email sent successfully',
      };
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (
          error.response?.status === 400 &&
          error.response?.data?.errorCode === 'operation_not_supported'
        ) {
          this.logger.info(
            'User has social connection, email already verified',
            {userId},
          );
          return {
            success: true,
            message:
              'Your email is already verified through your social provider',
            isSocialAuth: true,
          };
        }
      }

      this.logger.error('Failed to resend email verification', {
        error: {
          message: error instanceof Error ? error.message : 'Unknown error',
          status: axios.isAxiosError(error)
            ? error.response?.status
            : undefined,
          data: axios.isAxiosError(error)
            ? {
                error: error.response?.data?.error,
                message: error.response?.data?.message,
              }
            : undefined,
        },
        userId,
        environment: this.envService.app.env,
      });

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 404) {
          throw IctError.notFound('User not found in Auth0');
        }
        if (error.response?.status === 401) {
          throw IctError.unauthorized('Invalid Auth0 credentials');
        }
        if (error.response?.status === 429) {
          const headers = error.response.headers;
          this.logger.error('Auth0 rate limit exceeded', {
            error: {
              status: error.response.status,
              data: {
                error: error.response.data?.error,
                message: error.response.data?.message,
              },
              rateLimit: {
                limit: headers['x-ratelimit-limit'],
                remaining: headers['x-ratelimit-remaining'],
                reset: headers['x-ratelimit-reset'],
              },
            },
          });
          throw new IctError(
            HttpStatusCodes.TOO_MANY_REQUESTS,
            'Too many verification email requests. Please try again in 24 hours.',
            error.response.data,
          );
        }
      }

      throw IctError.internalServerError(
        'Failed to resend email verification. Please try again or contact support.',
      );
    }
  }
}
