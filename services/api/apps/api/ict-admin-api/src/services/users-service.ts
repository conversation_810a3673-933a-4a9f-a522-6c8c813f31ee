import axios, {AxiosResponse} from 'axios';
import {
  BaseFilterType,
  Container,
  ContextService,
  DiService,
  HttpStatusCodes,
  IctError,
  SingleFilterObject,
  SortField,
  WinstonLogger,
} from 'ict-api-foundations';
import {
  Auth0Organization,
  Auth0Role,
  Auth0User,
  Auth0UsersResponse,
  UserInfo,
  UsersListData,
  UsersListQueryParams,
} from '../defs/users-list-def.ts';
import {AuthManagementService} from './auth-management-service.ts';
import {RoleService} from './role-service.ts';

// Domain deny list for normal users
const DOMAIN_DENY_LIST = ['dematic.com', 'kiongroup.com'];

@DiService()
export class UsersService {
  private logger: WinstonLogger;
  private contextService: ContextService;
  private authManagementService: AuthManagementService;
  private roleService: RoleService;

  constructor() {
    this.logger = Container.get(WinstonLogger);
    this.contextService = Container.get(ContextService);
    this.authManagementService = Container.get(AuthManagementService);
    this.roleService = Container.get(RoleService);
    this.logger.info('UsersService initialized');
  }

  /**
   * Gets a single user by ID from Auth0
   */
  public async getUserById(userId: string): Promise<UserInfo> {
    try {
      const userRoles = this.contextService.userRoles;
      const currentUserId = this.contextService.userId;
      const organizationId = this.contextService.organization.id;

      if (!userRoles || !currentUserId) {
        throw IctError.unauthorized('User context not found');
      }

      const isInternalUser = this.roleService.isInternalUser(userRoles);
      const isFacilityAdmin = this.roleService.isFacilityAdmin(userRoles);

      if (!isInternalUser && !isFacilityAdmin) {
        throw IctError.forbidden('Insufficient permissions to view user');
      }

      this.logger.info('Getting user by ID', {
        currentUserId,
        targetUserId: userId,
        isInternalUser,
        isFacilityAdmin,
        userRoles,
      });

      // Fetch user, user organizations, organization roles, and assignable roles from Auth0
      const [auth0User, userOrganizations, auth0Roles, assignableRoles] =
        await Promise.all([
          this.fetchAuth0UserById(userId),
          this.fetchAuth0UserOrganizations(userId),
          this.roleService.fetchAuth0UserOrganizationRoles(
            userId,
            organizationId,
          ),
          this.roleService.getAssignableRoles(),
        ]);

      // Check if user belongs to the current organization
      const belongsToOrg = userOrganizations.some(
        org => org.id === organizationId,
      );

      if (!belongsToOrg) {
        throw IctError.forbidden('User does not belong to your organization');
      }

      // Create a set of assignable role names for quick lookup
      const assignableRoleNames = new Set(
        assignableRoles.map(role => role.name),
      );

      // Transform to user info and include organization roles
      const userInfo = this.mapAuth0UserToUserInfo(auth0User);
      // Map Auth0 roles to Role objects with name, displayName, description, and assignable
      userInfo.roles = auth0Roles.map((role: Auth0Role) => ({
        name: role.name,
        displayName: role.name,
        description: role.description || `Role: ${role.name}`,
        roleType: this.roleService.determineRoleType(role.name),
        assignable: assignableRoleNames.has(role.name),
      }));

      return userInfo;
    } catch (error) {
      this.logger.error('Failed to get user by ID', {
        userId,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
        },
      });

      if (error instanceof IctError) {
        throw error;
      }

      throw IctError.internalServerError('Failed to retrieve user');
    }
  }

  /**
   * Gets a paginated list of users with filtering and sorting using Auth0 server-side capabilities
   */
  public async getUsersList(
    queryParams: UsersListQueryParams,
  ): Promise<UsersListData> {
    try {
      const userRoles = this.contextService.userRoles;
      const userId = this.contextService.userId;
      const organizationId = this.contextService.organization.id;

      if (!userRoles || !userId) {
        throw IctError.unauthorized('User context not found');
      }

      const isInternalUser = this.roleService.isInternalUser(userRoles);
      const isFacilityAdmin = this.roleService.isFacilityAdmin(userRoles);

      if (!isInternalUser && !isFacilityAdmin) {
        throw IctError.forbidden('Insufficient permissions to view users');
      }

      this.logger.info('Getting users list with server-side pagination', {
        userId,
        isInternalUser,
        isFacilityAdmin,
        userRoles,
        queryParams: {
          page: queryParams.page,
          limit: queryParams.limit,
          searchString: queryParams.searchString,
          sortFields: queryParams.sortFields,
        },
      });

      // Build Auth0 query string
      const auth0Query = this.buildAuth0Query(
        queryParams,
        isInternalUser,
        organizationId,
      );

      // Build Auth0 sort string
      const auth0Sort = this.buildAuth0Sort(queryParams.sortFields);

      // Fetch users from Auth0 with server-side pagination and assignable roles
      const [auth0Response] = await Promise.all([
        this.fetchAuth0UsersWithPagination({
          page: queryParams.page,
          limit: queryParams.limit,
          query: auth0Query,
          sort: auth0Sort,
        }),
      ]);

      // Transform to user info with assignable roles
      const users = this.mapAuth0UsersToUserInfo(auth0Response.users);

      const response: UsersListData = {
        data: users,
        metadata: {
          page: queryParams.page,
          limit: queryParams.limit,
          totalResults: auth0Response.total,
        },
      };

      return response;
    } catch (error) {
      this.logger.error('Failed to get users list', {
        error: {
          message: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
        },
      });

      if (error instanceof IctError) {
        throw error;
      }

      throw IctError.internalServerError('Failed to retrieve users list');
    }
  }

  /**
   * Fetches user organizations from Auth0 using the organizations endpoint
   */
  private async fetchAuth0UserOrganizations(
    userId: string,
  ): Promise<Auth0Organization[]> {
    const authDomain = process.env.ADMIN_API_URL;
    if (!authDomain) {
      throw IctError.internalServerError(
        'ADMIN_API_URL environment variable not set',
      );
    }

    const token = await this.authManagementService.getManagementApiToken();
    const organizationsUrl = `${authDomain}/api/v2/users/${encodeURIComponent(userId)}/organizations`;

    this.logger.debug('Fetching user organizations from Auth0', {
      url: organizationsUrl,
      userId,
    });

    try {
      const response: AxiosResponse<Auth0Organization[]> = await axios.get(
        organizationsUrl,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      );

      return response.data;
    } catch (error) {
      this.logger.error('Failed to fetch user organizations from Auth0', {
        userId,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error',
          status: axios.isAxiosError(error)
            ? error.response?.status
            : undefined,
          data: axios.isAxiosError(error) ? error.response?.data : undefined,
        },
      });

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          throw IctError.unauthorized('Invalid Auth0 credentials');
        }
        if (error.response?.status === 403) {
          throw IctError.forbidden(
            'Insufficient permissions for Auth0 Management API',
          );
        }
        if (error.response?.status === 404) {
          throw IctError.notFound('User organizations not found');
        }
        if (error.response?.status === 429) {
          throw new IctError(
            HttpStatusCodes.TOO_MANY_REQUESTS,
            'Too many requests to Auth0. Please try again later.',
            error.response.data,
          );
        }
      }

      throw IctError.internalServerError(
        'Failed to fetch user organizations from Auth0',
      );
    }
  }

  /**
   * Fetches a single user from Auth0 by user ID
   */
  private async fetchAuth0UserById(userId: string): Promise<Auth0User> {
    const authDomain = process.env.ADMIN_API_URL;
    if (!authDomain) {
      throw IctError.internalServerError(
        'ADMIN_API_URL environment variable not set',
      );
    }

    const token = await this.authManagementService.getManagementApiToken();
    const userUrl = `${authDomain}/api/v2/users/${encodeURIComponent(userId)}`;

    this.logger.debug('Fetching user from Auth0 by ID', {
      url: userUrl,
      userId,
    });

    try {
      const response: AxiosResponse<Auth0User> = await axios.get(userUrl, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      return response.data;
    } catch (error) {
      this.logger.error('Failed to fetch user from Auth0', {
        userId,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error',
          status: axios.isAxiosError(error)
            ? error.response?.status
            : undefined,
          data: axios.isAxiosError(error) ? error.response?.data : undefined,
        },
      });

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          throw IctError.unauthorized('Invalid Auth0 credentials');
        }
        if (error.response?.status === 403) {
          throw IctError.forbidden(
            'Insufficient permissions for Auth0 Management API',
          );
        }
        if (error.response?.status === 404) {
          throw IctError.notFound('User not found');
        }
        if (error.response?.status === 429) {
          throw new IctError(
            HttpStatusCodes.TOO_MANY_REQUESTS,
            'Too many requests to Auth0. Please try again later.',
            error.response.data,
          );
        }
      }

      throw IctError.internalServerError('Failed to fetch user from Auth0');
    }
  }

  /**
   * Fetches users from Auth0 with server-side pagination, filtering, and sorting
   */
  private async fetchAuth0UsersWithPagination(options: {
    page: number;
    limit: number;
    query?: string;
    sort?: string;
  }): Promise<Auth0UsersResponse> {
    const authDomain = process.env.ADMIN_API_URL;
    if (!authDomain) {
      throw IctError.internalServerError(
        'ADMIN_API_URL environment variable not set',
      );
    }

    const token = await this.authManagementService.getManagementApiToken();
    const usersUrl = `${authDomain}/api/v2/users`;

    this.logger.debug('Fetching users from Auth0 with pagination', {
      url: usersUrl,
      page: options.page,
      limit: options.limit,
      query: options.query,
      sort: options.sort,
    });

    try {
      const params: Record<string, unknown> = {
        include_totals: true,
        per_page: options.limit,
        page: options.page,
        search_engine: 'v3',
      };

      if (options.query) {
        params.q = options.query;
      }

      if (options.sort) {
        params.sort = options.sort;
      }

      const response: AxiosResponse<Auth0UsersResponse> = await axios.get(
        usersUrl,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          params,
        },
      );

      return response.data;
    } catch (error) {
      this.logger.error('Failed to fetch users from Auth0', {
        error: {
          message: error instanceof Error ? error.message : 'Unknown error',
          status: axios.isAxiosError(error)
            ? error.response?.status
            : undefined,
          data: axios.isAxiosError(error) ? error.response?.data : undefined,
        },
      });

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          throw IctError.unauthorized('Invalid Auth0 credentials');
        }
        if (error.response?.status === 403) {
          throw IctError.forbidden(
            'Insufficient permissions for Auth0 Management API',
          );
        }
        if (error.response?.status === 429) {
          throw new IctError(
            HttpStatusCodes.TOO_MANY_REQUESTS,
            'Too many requests to Auth0. Please try again later.',
            error.response.data,
          );
        }
      }

      throw IctError.internalServerError('Failed to fetch users from Auth0');
    }
  }

  /**
   * Builds Auth0 query string using Lucene syntax
   */
  private buildAuth0Query(
    queryParams: UsersListQueryParams,
    isInternalUser: boolean,
    organizationId: string,
  ): string {
    const queryParts: string[] = [];

    // Filter by organization ID - all users must belong to the current organization
    queryParts.push(`organization_id:"${organizationId}"`);

    // Add domain restrictions for non-admin users
    if (!isInternalUser) {
      const domainRestrictions = DOMAIN_DENY_LIST.map(
        domain => `NOT email.domain:"${domain}"`,
      ).join(' AND ');
      queryParts.push(`(${domainRestrictions})`);
    }

    // Add search string (searches across email and name)
    if (queryParams.searchString) {
      const searchTerm = queryParams.searchString.replace(/"/g, '\\"');
      queryParts.push(`(email:*${searchTerm}* OR name:*${searchTerm}*)`);
    }

    // Add specific filters
    if (queryParams.filters) {
      const filterQuery = this.buildFilterQuery(queryParams.filters);
      if (filterQuery) {
        queryParts.push(filterQuery);
      }
    }

    // If no query parts, return empty string to get all users (with domain restrictions if applicable)
    if (queryParts.length === 0) {
      return '';
    }

    return queryParts.join(' AND ');
  }

  /**
   * Builds filter query for Auth0 Lucene syntax
   */
  private buildFilterQuery(filters: BaseFilterType): string | undefined {
    if (filters.type === 'single') {
      const filter = filters as SingleFilterObject;
      if (filter.name === 'email' && filter.value) {
        const emailValue = filter.value.replace(/"/g, '\\"');
        return `email:*${emailValue}*`;
      }
      if (
        filter.name === 'emailVerified' &&
        typeof filter.value === 'boolean'
      ) {
        return `email_verified:${filter.value}`;
      }
      if (filter.name === 'blocked' && typeof filter.value === 'boolean') {
        return `blocked:${filter.value}`;
      }
    }

    // For more complex filters, we can extend this logic
    return undefined;
  }

  /**
   * Builds Auth0 sort string
   */
  private buildAuth0Sort(sortFields?: SortField[]): string | undefined {
    if (!sortFields || sortFields.length === 0) {
      return undefined;
    }

    // Auth0 only supports sorting by one field at a time
    const primarySort = sortFields[0];
    const {columnName, isDescending} = primarySort;

    let auth0FieldName: string;
    switch (columnName) {
      case 'email':
        auth0FieldName = 'email';
        break;
      case 'name':
        auth0FieldName = 'name';
        break;
      case 'createdAt':
      case 'created_at':
        auth0FieldName = 'created_at';
        break;
      case 'lastLogin':
      case 'last_login':
        auth0FieldName = 'last_login';
        break;
      case 'loginCount':
      case 'logins_count':
        auth0FieldName = 'logins_count';
        break;
      default:
        // Default to email if field is not supported
        auth0FieldName = 'email';
    }

    const order = isDescending ? '-1' : '1';
    return `${auth0FieldName}:${order}`;
  }

  private mapAuth0UsersToUserInfo(auth0Users: Auth0User[]): UserInfo[] {
    return auth0Users.map(user => ({
      id: user.user_id,
      email: user.email,
      name: user.name,
      emailVerified: user.email_verified,
      createdAt: user.created_at,
      lastLogin: user.last_login,
      loginCount: user.logins_count,
      isSocialAuth:
        user.identities?.some(identity => identity.isSocial) || false,
    }));
  }

  private mapAuth0UserToUserInfo(auth0User: Auth0User): UserInfo {
    return {
      id: auth0User.user_id,
      email: auth0User.email,
      name: auth0User.name,
      emailVerified: auth0User.email_verified,
      createdAt: auth0User.created_at,
      lastLogin: auth0User.last_login,
      loginCount: auth0User.logins_count,
      roles: [], // Will be populated separately with full Role objects
      isSocialAuth:
        auth0User.identities?.some(identity => identity.isSocial) || false,
    };
  }
}
