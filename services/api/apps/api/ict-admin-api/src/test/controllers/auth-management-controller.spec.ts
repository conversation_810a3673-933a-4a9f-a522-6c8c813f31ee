import 'reflect-metadata';
import * as sinon from 'sinon';
import request from 'supertest';
import {
  Container,
  HttpStatusCodes,
  IctError,
  appSetup,
  ContextService,
  SecurityRoles,
} from 'ict-api-foundations';
import {expect} from 'chai';

import {RegisterRoutes} from '../../../build/routes.ts';
import {AuthManagementService} from '../../services/auth-management-service.ts';
import {RoleService} from '../../services/role-service.ts';
import {UsersService} from '../../services/users-service.ts';
import {RoleType, Role} from '@ict/sdk-foundations/types/defs/admin-defs.ts';
import {UsersListData, UserInfo} from '../../defs/users-list-def.ts';

describe('AuthManagementController', () => {
  let contextServiceRolesStub: sinon.SinonStub;
  let contextServiceUserIdStub: sinon.SinonStub;
  const app = appSetup(RegisterRoutes);

  afterEach(() => {
    sinon.restore();
  });

  describe('auth route', () => {
    let authManagementServiceStub: sinon.SinonStubbedInstance<AuthManagementService>;

    beforeEach(() => {
      authManagementServiceStub = sinon.createStubInstance(
        AuthManagementService,
      );
      Container.set(AuthManagementService, authManagementServiceStub);

      const contextService = Container.get(ContextService);
      contextServiceRolesStub = sinon.stub(contextService, 'userRoles');
      contextServiceUserIdStub = sinon.stub(contextService, 'userId');
      contextServiceRolesStub.value([SecurityRoles.CT_ENGINEERS]);
      contextServiceUserIdStub.value('test-user-id');
    });

    describe('POST /resend-verification', () => {
      const url = '/admin/auth/resend-verification';

      it('successfully resends verification email', async () => {
        authManagementServiceStub.resendEmailVerification.resolves({
          success: true,
          message: 'Verification email sent successfully',
        });

        const response = await request(app).post(url);

        expect(response.status).to.equal(200);
        expect(
          authManagementServiceStub.resendEmailVerification.calledOnceWith(
            'test-user-id',
          ),
        ).to.be.true;
      });

      it('handles social auth case', async () => {
        authManagementServiceStub.resendEmailVerification.resolves({
          success: true,
          message:
            'Your email is already verified through your social provider',
          isSocialAuth: true,
        });

        const response = await request(app).post(url);

        expect(response.status).to.equal(200);
        expect(
          authManagementServiceStub.resendEmailVerification.calledOnceWith(
            'test-user-id',
          ),
        ).to.be.true;
      });

      it('throws 401 if user ID not found in context', async () => {
        contextServiceUserIdStub.value(undefined);
        authManagementServiceStub.resendEmailVerification.rejects(
          IctError.unauthorized('User ID not found in context'),
        );

        const response = await request(app).post(url);

        expect(response.status).to.equal(IctError.unauthorized().statusCode);
        expect(response.body.detail).to.equal('User ID not found in context');
        expect(authManagementServiceStub.resendEmailVerification.notCalled).to
          .be.true;
      });

      it('throws 401 if user is not authenticated', async () => {
        contextServiceUserIdStub.value(undefined);

        const response = await request(app).post(url);

        expect(response.status).to.equal(IctError.unauthorized().statusCode);
        expect(response.body.detail).to.equal('User ID not found in context');
        expect(authManagementServiceStub.resendEmailVerification.notCalled).to
          .be.true;
      });

      it('throws 404 if user not found in Auth0', async () => {
        authManagementServiceStub.resendEmailVerification.rejects(
          IctError.notFound('User not found in Auth0'),
        );

        const response = await request(app).post(url);

        expect(response.status).to.equal(IctError.notFound().statusCode);
        expect(response.body.detail).to.equal('User not found in Auth0');
        expect(
          authManagementServiceStub.resendEmailVerification.calledOnceWith(
            'test-user-id',
          ),
        ).to.be.true;
      });

      it('throws 429 if rate limit exceeded', async () => {
        authManagementServiceStub.resendEmailVerification.rejects(
          new IctError(
            HttpStatusCodes.TOO_MANY_REQUESTS,
            'Too many verification email requests. Please try again in 24 hours.',
          ),
        );

        const response = await request(app).post(url);

        expect(response.status).to.equal(HttpStatusCodes.TOO_MANY_REQUESTS);
        expect(response.body.detail).to.equal(
          'Too many verification email requests. Please try again in 24 hours.',
        );
        expect(
          authManagementServiceStub.resendEmailVerification.calledOnceWith(
            'test-user-id',
          ),
        ).to.be.true;
      });

      it('throws 500 if service encounters an error', async () => {
        authManagementServiceStub.resendEmailVerification.rejects(
          IctError.internalServerError('Failed to resend email verification'),
        );

        const response = await request(app).post(url);

        expect(response.status).to.equal(
          IctError.internalServerError().statusCode,
        );
        expect(response.body.detail).to.equal(
          'Failed to resend email verification',
        );
        expect(
          authManagementServiceStub.resendEmailVerification.calledOnceWith(
            'test-user-id',
          ),
        ).to.be.true;
      });
    });

    describe('GET /roles', () => {
      let roleServiceStub: sinon.SinonStubbedInstance<RoleService>;
      const url = '/admin/auth/roles';

      beforeEach(() => {
        roleServiceStub = sinon.createStubInstance(RoleService);
        Container.set(RoleService, roleServiceStub);
      });

      it('successfully returns assignable roles', async () => {
        const mockRoles: Role[] = [
          {
            name: SecurityRoles.CT_CONFIGURATORS,
            displayName: 'Control Tower Configurator',
            description: 'Can configure Control Tower settings',
            roleType: RoleType.INTERNAL,
          },
          {
            name: 'facility_a',
            displayName: 'Facility A',
            description: 'Access to Facility A facility',
            roleType: RoleType.FACILITY,
          },
        ];

        roleServiceStub.getAssignableRoles.resolves(mockRoles);

        const response = await request(app).get(url);

        expect(response.status).to.equal(200);
        expect(response.body).to.deep.equal(mockRoles);
        expect(roleServiceStub.getAssignableRoles.calledOnce).to.be.true;
      });

      it('throws 401 if user ID not found in context', async () => {
        contextServiceUserIdStub.value(undefined);

        const response = await request(app).get(url);

        expect(response.status).to.equal(IctError.unauthorized().statusCode);
        expect(response.body.detail).to.equal('User ID not found in context');
        expect(roleServiceStub.getAssignableRoles.notCalled).to.be.true;
      });

      it('throws 401 if user roles not found', async () => {
        roleServiceStub.getAssignableRoles.rejects(
          IctError.unauthorized('User roles not found in context'),
        );

        const response = await request(app).get(url);

        expect(response.status).to.equal(IctError.unauthorized().statusCode);
        expect(response.body.detail).to.equal(
          'User roles not found in context',
        );
        expect(roleServiceStub.getAssignableRoles.calledOnce).to.be.true;
      });

      it('throws 500 if role service encounters an error', async () => {
        roleServiceStub.getAssignableRoles.rejects(
          IctError.internalServerError('Failed to load facility configuration'),
        );

        const response = await request(app).get(url);

        expect(response.status).to.equal(
          IctError.internalServerError().statusCode,
        );
        expect(response.body.detail).to.equal(
          'Failed to load facility configuration',
        );
        expect(roleServiceStub.getAssignableRoles.calledOnce).to.be.true;
      });

      it('returns empty array for users with no assignable roles', async () => {
        roleServiceStub.getAssignableRoles.resolves([]);

        const response = await request(app).get(url);

        expect(response.status).to.equal(200);
        expect(response.body).to.deep.equal([]);
        expect(roleServiceStub.getAssignableRoles.calledOnce).to.be.true;
      });
    });

    describe('POST /users/list', () => {
      let usersServiceStub: sinon.SinonStubbedInstance<UsersService>;
      const url = '/admin/auth/users/list';

      const mockUsers: UserInfo[] = [
        {
          id: 'user1',
          email: '<EMAIL>',
          name: 'User One',
          emailVerified: true,
          createdAt: '2023-01-01T00:00:00.000Z',
          lastLogin: '2023-12-01T00:00:00.000Z',
          loginCount: 5,
          roles: [
            {
              name: 'facility_a',
              displayName: 'Facility A',
              description: 'Role: facility_a',
              roleType: RoleType.FACILITY,
            },
          ],
          isSocialAuth: false,
        },
        {
          id: 'user2',
          email: '<EMAIL>',
          name: 'User Two',
          emailVerified: false,
          createdAt: '2023-02-01T00:00:00.000Z',
          lastLogin: '2023-11-01T00:00:00.000Z',
          loginCount: 2,
          roles: [
            {
              name: 'facility_b_facility_admin',
              displayName: 'Facility B Admin',
              description: 'Role: facility_b_facility_admin',
              roleType: RoleType.ADMIN,
            },
          ],
          isSocialAuth: true,
        },
      ];

      const mockUsersListData: UsersListData = {
        data: mockUsers,
        metadata: {
          page: 0,
          limit: 10,
          totalResults: 2,
        },
      };

      beforeEach(() => {
        usersServiceStub = sinon.createStubInstance(UsersService);
        Container.set(UsersService, usersServiceStub);
      });

      it('successfully returns users list with pagination', async () => {
        usersServiceStub.getUsersList.resolves(mockUsersListData);

        const requestBody = {
          page: 0,
          limit: 10,
          sortFields: [],
        };

        const response = await request(app).post(url).send(requestBody);

        expect(response.status).to.equal(200);
        expect(response.body).to.deep.equal(mockUsersListData);
        expect(usersServiceStub.getUsersList.calledOnce).to.be.true;

        const callArgs = usersServiceStub.getUsersList.getCall(0).args[0];
        expect(callArgs.page).to.equal(0);
        expect(callArgs.limit).to.equal(10);
        expect(callArgs.sortFields).to.deep.equal([]);
      });

      it('successfully returns users list with search', async () => {
        usersServiceStub.getUsersList.resolves(mockUsersListData);

        const requestBody = {
          page: 0,
          limit: 10,
          sortFields: [],
          searchString: 'test search',
        };

        const response = await request(app).post(url).send(requestBody);

        expect(response.status).to.equal(200);
        expect(response.body).to.deep.equal(mockUsersListData);
        expect(usersServiceStub.getUsersList.calledOnce).to.be.true;

        const callArgs = usersServiceStub.getUsersList.getCall(0).args[0];
        expect(callArgs.searchString).to.equal('test search');
      });

      it('successfully returns users list with sort fields', async () => {
        usersServiceStub.getUsersList.resolves(mockUsersListData);

        const requestBody = {
          page: 0,
          limit: 10,
          sortFields: [{columnName: 'email', isDescending: true}],
        };

        const response = await request(app).post(url).send(requestBody);

        expect(response.status).to.equal(200);
        expect(response.body).to.deep.equal(mockUsersListData);
        expect(usersServiceStub.getUsersList.calledOnce).to.be.true;

        const callArgs = usersServiceStub.getUsersList.getCall(0).args[0];
        expect(callArgs.sortFields).to.deep.equal([
          {columnName: 'email', isDescending: true},
        ]);
      });

      it('successfully returns users list with filters', async () => {
        usersServiceStub.getUsersList.resolves(mockUsersListData);

        const requestBody = {
          page: 0,
          limit: 10,
          sortFields: [],
          filters: {
            type: 'single',
            comparison: 'contains',
            name: 'email',
            value: '<EMAIL>',
          },
        };

        const response = await request(app).post(url).send(requestBody);

        expect(response.status).to.equal(200);
        expect(response.body).to.deep.equal(mockUsersListData);
        expect(usersServiceStub.getUsersList.calledOnce).to.be.true;

        const callArgs = usersServiceStub.getUsersList.getCall(0).args[0];
        expect(callArgs.filters).to.deep.equal({
          type: 'single',
          comparison: 'contains',
          name: 'email',
          value: '<EMAIL>',
        });
      });

      it('throws 401 if user ID not found in context', async () => {
        contextServiceUserIdStub.value(undefined);

        const requestBody = {
          page: 0,
          limit: 10,
          sortFields: [],
        };

        const response = await request(app).post(url).send(requestBody);

        expect(response.status).to.equal(IctError.unauthorized().statusCode);
        expect(response.body.detail).to.equal('User ID not found in context');
        expect(usersServiceStub.getUsersList.notCalled).to.be.true;
      });

      it('throws 401 if user context not found', async () => {
        usersServiceStub.getUsersList.rejects(
          IctError.unauthorized('User context not found'),
        );

        const requestBody = {
          page: 0,
          limit: 10,
          sortFields: [],
        };

        const response = await request(app).post(url).send(requestBody);

        expect(response.status).to.equal(IctError.unauthorized().statusCode);
        expect(response.body.detail).to.equal('User context not found');
        expect(usersServiceStub.getUsersList.calledOnce).to.be.true;
      });

      it('throws 403 if user has insufficient permissions', async () => {
        usersServiceStub.getUsersList.rejects(
          IctError.forbidden('Insufficient permissions to view users'),
        );

        const requestBody = {
          page: 0,
          limit: 10,
          sortFields: [],
        };

        const response = await request(app).post(url).send(requestBody);

        expect(response.status).to.equal(IctError.forbidden().statusCode);
        expect(response.body.detail).to.equal(
          'Insufficient permissions to view users',
        );
        expect(usersServiceStub.getUsersList.calledOnce).to.be.true;
      });

      it('throws 401 if Auth0 credentials are invalid', async () => {
        usersServiceStub.getUsersList.rejects(
          IctError.unauthorized('Invalid Auth0 credentials'),
        );

        const requestBody = {
          page: 0,
          limit: 10,
          sortFields: [],
        };

        const response = await request(app).post(url).send(requestBody);

        expect(response.status).to.equal(IctError.unauthorized().statusCode);
        expect(response.body.detail).to.equal('Invalid Auth0 credentials');
        expect(usersServiceStub.getUsersList.calledOnce).to.be.true;
      });

      it('throws 429 if rate limit exceeded', async () => {
        usersServiceStub.getUsersList.rejects(
          new IctError(
            HttpStatusCodes.TOO_MANY_REQUESTS,
            'Too many requests to Auth0. Please try again later.',
          ),
        );

        const requestBody = {
          page: 0,
          limit: 10,
          sortFields: [],
        };

        const response = await request(app).post(url).send(requestBody);

        expect(response.status).to.equal(HttpStatusCodes.TOO_MANY_REQUESTS);
        expect(response.body.detail).to.equal(
          'Too many requests to Auth0. Please try again later.',
        );
        expect(usersServiceStub.getUsersList.calledOnce).to.be.true;
      });

      it('throws 500 if service encounters an error', async () => {
        usersServiceStub.getUsersList.rejects(
          IctError.internalServerError('Failed to retrieve users list'),
        );

        const requestBody = {
          page: 0,
          limit: 10,
          sortFields: [],
        };

        const response = await request(app).post(url).send(requestBody);

        expect(response.status).to.equal(
          IctError.internalServerError().statusCode,
        );
        expect(response.body.detail).to.equal('Failed to retrieve users list');
        expect(usersServiceStub.getUsersList.calledOnce).to.be.true;
      });

      it('returns empty users list when no users found', async () => {
        const emptyUsersListData: UsersListData = {
          data: [],
          metadata: {
            page: 0,
            limit: 10,
            totalResults: 0,
          },
        };

        usersServiceStub.getUsersList.resolves(emptyUsersListData);

        const requestBody = {
          page: 0,
          limit: 10,
          sortFields: [],
        };

        const response = await request(app).post(url).send(requestBody);

        expect(response.status).to.equal(200);
        expect(response.body).to.deep.equal(emptyUsersListData);
        expect(usersServiceStub.getUsersList.calledOnce).to.be.true;
      });
    });

    describe('POST /users/{userId}/roles', () => {
      let roleServiceStub: sinon.SinonStubbedInstance<RoleService>;
      const userId = 'test-user-123';
      const url = `/admin/auth/users/${userId}/roles`;

      beforeEach(() => {
        roleServiceStub = sinon.createStubInstance(RoleService);
        Container.set(RoleService, roleServiceStub);
      });

      it('successfully assigns roles to user', async () => {
        const mockResponse = {
          success: true,
          message: 'Successfully assigned 1 role(s)',
          assignedRoles: [
            {
              name: 'facility_a',
              displayName: 'Facility A',
              description: 'Access to Facility A facility',
              roleType: RoleType.FACILITY,
            },
          ],
        };

        roleServiceStub.assignUserOrganizationRoles.resolves(mockResponse);

        const requestBody = {
          roleNames: ['facility_a'],
        };

        const response = await request(app).post(url).send(requestBody);

        expect(response.status).to.equal(200);
        expect(response.body).to.deep.equal(mockResponse);
        expect(
          roleServiceStub.assignUserOrganizationRoles.calledOnceWith(userId, [
            'facility_a',
          ]),
        ).to.be.true;
      });

      it('throws 401 if user ID not found in context', async () => {
        contextServiceUserIdStub.value(undefined);

        const requestBody = {
          roleNames: ['facility_a'],
        };

        const response = await request(app).post(url).send(requestBody);

        expect(response.status).to.equal(IctError.unauthorized().statusCode);
        expect(response.body.detail).to.equal('User ID not found in context');
        expect(roleServiceStub.assignUserOrganizationRoles.notCalled).to.be
          .true;
      });

      it('throws 403 if insufficient permissions', async () => {
        roleServiceStub.assignUserOrganizationRoles.rejects(
          IctError.forbidden('Insufficient permissions to assign roles'),
        );

        const requestBody = {
          roleNames: ['facility_a'],
        };

        const response = await request(app).post(url).send(requestBody);

        expect(response.status).to.equal(IctError.forbidden().statusCode);
        expect(response.body.detail).to.equal(
          'Insufficient permissions to assign roles',
        );
        expect(roleServiceStub.assignUserOrganizationRoles.calledOnce).to.be
          .true;
      });
    });

    describe('DELETE /users/{userId}/roles/{roleName}', () => {
      let roleServiceStub: sinon.SinonStubbedInstance<RoleService>;
      const userId = 'test-user-123';
      const roleName = 'facility_a';
      const url = `/admin/auth/users/${userId}/roles/${roleName}`;

      beforeEach(() => {
        roleServiceStub = sinon.createStubInstance(RoleService);
        Container.set(RoleService, roleServiceStub);
      });

      it('successfully removes role from user', async () => {
        const mockResponse = {
          success: true,
          message: 'Successfully removed role: facility_a',
        };

        roleServiceStub.removeUserOrganizationRole.resolves(mockResponse);

        const response = await request(app).delete(url);

        expect(response.status).to.equal(200);
        expect(response.body).to.deep.equal(mockResponse);
        expect(
          roleServiceStub.removeUserOrganizationRole.calledOnceWith(
            userId,
            roleName,
          ),
        ).to.be.true;
      });

      it('throws 401 if user ID not found in context', async () => {
        contextServiceUserIdStub.value(undefined);

        const response = await request(app).delete(url);

        expect(response.status).to.equal(IctError.unauthorized().statusCode);
        expect(response.body.detail).to.equal('User ID not found in context');
        expect(roleServiceStub.removeUserOrganizationRole.notCalled).to.be.true;
      });

      it('throws 403 if insufficient permissions', async () => {
        roleServiceStub.removeUserOrganizationRole.rejects(
          IctError.forbidden('Insufficient permissions to remove roles'),
        );

        const response = await request(app).delete(url);

        expect(response.status).to.equal(IctError.forbidden().statusCode);
        expect(response.body.detail).to.equal(
          'Insufficient permissions to remove roles',
        );
        expect(roleServiceStub.removeUserOrganizationRole.calledOnce).to.be
          .true;
      });
    });
  });
});
