import * as sinon from 'sinon';
import * as chai from 'chai';
import chaiAsPromised from 'chai-as-promised';
import axios from 'axios';
import {AuthManagementService} from '../../services/auth-management-service.ts';

const expect = chai.expect;
chai.use(chaiAsPromised);

interface TestLogger {
  info: sinon.SinonStub;
  error: sinon.SinonStub;
  debug: sinon.SinonStub;
}

interface TestEnvService {
  app: {authDomain?: string; env: string};
  apiProject: {projectId: string};
}

interface TestSecretManager {
  get: sinon.SinonStub;
}

describe('AuthManagementService', () => {
  let service: AuthManagementService;
  let loggerStub: TestLogger;
  let envServiceStub: TestEnvService;
  let secretManagerStub: TestSecretManager;
  let axiosStub: sinon.SinonStub;

  const mockAuthDomain = 'test.auth0.com';
  const mockProjectId = 'test-project';
  const mockEnv = 'dev';
  const mockCredentials = {
    clientId: 'test-client-id',
    clientSecret: 'test-client-secret',
  };
  const mockTokenResponse = {
    access_token: 'test-access-token',
    expires_in: 3600,
    token_type: 'Bearer',
  };
  const mockVerificationResponse = {
    id: 'test-verification-id',
    status: 'pending',
  };

  beforeEach(() => {
    // Set up environment variables
    process.env.ADMIN_API_URL = `https://${mockAuthDomain}`;
    process.env.ADMIN_API_KEY = 'test-admin-api-key';

    // Create stubs
    loggerStub = {
      info: sinon.stub(),
      error: sinon.stub(),
      debug: sinon.stub(),
    };
    envServiceStub = {
      app: {
        authDomain: mockAuthDomain,
        env: mockEnv,
      },
      apiProject: {
        projectId: mockProjectId,
      },
    };
    secretManagerStub = {
      get: sinon.stub(),
    };
    axiosStub = sinon.stub(axios, 'post');

    // Create service instance with stubs
    service = new AuthManagementService();
    (service as unknown as {logger: TestLogger}).logger = loggerStub;
    (service as unknown as {envService: TestEnvService}).envService =
      envServiceStub;
    (service as unknown as {secretManager: TestSecretManager}).secretManager =
      secretManagerStub;
  });

  afterEach(() => {
    sinon.restore();
    // Clean up environment variables
    delete process.env.ADMIN_API_URL;
    delete process.env.ADMIN_API_KEY;
  });

  describe('getManagementApiToken', () => {
    it('should return cached token if not expired', async () => {
      // Set up cached token
      (service as unknown as {managementApiToken: string}).managementApiToken =
        'cached-token';
      (service as unknown as {tokenExpiry: number}).tokenExpiry =
        Date.now() + 3600000; // 1 hour from now

      const token = await (
        service as unknown as {getManagementApiToken(): Promise<string>}
      ).getManagementApiToken();
      expect(token).to.equal('cached-token');
      expect(axiosStub.called).to.be.false;
    });

    it('should fetch new token if cached token is expired', async () => {
      // Set up expired token
      (service as unknown as {managementApiToken: string}).managementApiToken =
        'expired-token';
      (service as unknown as {tokenExpiry: number}).tokenExpiry =
        Date.now() - 1000; // 1 second ago

      // Mock secret manager response
      secretManagerStub.get.resolves(JSON.stringify(mockCredentials));

      // Mock Auth0 token response
      axiosStub.resolves({data: mockTokenResponse});

      const token = await (
        service as unknown as {getManagementApiToken(): Promise<string>}
      ).getManagementApiToken();

      expect(token).to.equal(mockTokenResponse.access_token);
      expect(axiosStub.calledOnce).to.be.true;
      expect(secretManagerStub.get.calledOnce).to.be.true;
    });

    it('should throw error if Auth0 domain is not configured', async () => {
      // Mock process.env to not have ADMIN_API_URL
      const originalEnv = process.env.ADMIN_API_URL;
      delete process.env.ADMIN_API_URL;

      try {
        await expect(
          (
            service as unknown as {getManagementApiToken(): Promise<string>}
          ).getManagementApiToken(),
        ).to.be.rejectedWith(
          'ADMIN_API_URL environment variable is required but not configured',
        );
      } finally {
        // Restore original environment variable
        if (originalEnv !== undefined) {
          process.env.ADMIN_API_URL = originalEnv;
        }
      }
    });

    it('should throw error if credentials are invalid', async () => {
      // Mock secret manager to return invalid credentials (missing clientId/clientSecret)
      secretManagerStub.get.resolves(JSON.stringify({invalid: 'credentials'}));

      // The error from getAuth0Secrets gets caught and re-thrown as generic auth error
      await expect(
        (
          service as unknown as {getManagementApiToken(): Promise<string>}
        ).getManagementApiToken(),
      ).to.be.rejectedWith('Failed to authenticate with Auth0');
    });

    it('should handle rate limiting', async () => {
      secretManagerStub.get.resolves(JSON.stringify(mockCredentials));
      axiosStub.rejects({
        isAxiosError: true,
        response: {
          status: 429,
          data: {error: 'rate_limit_exceeded'},
          headers: {
            'x-ratelimit-limit': '100',
            'x-ratelimit-remaining': '0',
            'x-ratelimit-reset': '3600',
          },
        },
      });

      await expect(
        (
          service as unknown as {getManagementApiToken(): Promise<string>}
        ).getManagementApiToken(),
      ).to.be.rejectedWith(
        'Too many verification email requests. Please try again in 24 hours.',
      );
    });
  });

  describe('resendEmailVerification', () => {
    const userId = 'test-user-id';

    beforeEach(() => {
      // Set up valid token
      (service as unknown as {managementApiToken: string}).managementApiToken =
        'valid-token';
      (service as unknown as {tokenExpiry: number}).tokenExpiry =
        Date.now() + 3600000;
    });

    it('should successfully resend verification email', async () => {
      axiosStub.resolves({data: mockVerificationResponse});

      const result = await service.resendEmailVerification(userId);

      expect(result).to.deep.equal({
        success: true,
        message: 'Verification email sent successfully',
      });
      expect(axiosStub.calledOnce).to.be.true;
    });

    it('should handle social auth case', async () => {
      axiosStub.rejects({
        isAxiosError: true,
        response: {
          status: 400,
          data: {
            errorCode: 'operation_not_supported',
          },
        },
      });

      const result = await service.resendEmailVerification(userId);

      expect(result).to.deep.equal({
        success: true,
        message: 'Your email is already verified through your social provider',
        isSocialAuth: true,
      });
    });

    it('should handle user not found', async () => {
      axiosStub.rejects({
        isAxiosError: true,
        response: {
          status: 404,
          data: {error: 'user_not_found'},
        },
      });

      await expect(service.resendEmailVerification(userId)).to.be.rejectedWith(
        'User not found in Auth0',
      );
    });

    it('should handle rate limiting', async () => {
      axiosStub.rejects({
        isAxiosError: true,
        response: {
          status: 429,
          data: {error: 'rate_limit_exceeded'},
          headers: {
            'x-ratelimit-limit': '100',
            'x-ratelimit-remaining': '0',
            'x-ratelimit-reset': '3600',
          },
        },
      });

      await expect(service.resendEmailVerification(userId)).to.be.rejectedWith(
        'Too many verification email requests. Please try again in 24 hours.',
      );
    });

    it('should handle invalid response', async () => {
      axiosStub.resolves({data: {}});

      await expect(service.resendEmailVerification(userId)).to.be.rejectedWith(
        'Failed to resend email verification. Please try again or contact support.',
      );
    });
  });
});
