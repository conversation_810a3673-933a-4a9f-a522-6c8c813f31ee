import 'reflect-metadata';
import express from 'express';
import {
  AuthMiddleware,
  SecurityMiddleware,
  RequestLoggerMiddleware,
  CorsMiddleware,
  ContextMiddleware,
  Container,
  EnvironmentService,
  WinstonLogger,
} from 'ict-api-foundations';

import {RegisterRoutes} from '../build/routes.ts';

const app = express();

// setup middlewares
app.use(
  express.urlencoded({
    extended: true,
  }),
);

app.use(express.json());

const logger = Container.get(WinstonLogger);
const envService = Container.get(EnvironmentService);
const corsMiddleware = Container.get(CorsMiddleware);
const authMiddleware = Container.get(AuthMiddleware);
const securityMiddleware = Container.get(SecurityMiddleware);
const contextMiddleware = Container.get(ContextMiddleware);
const requestLoggerMiddleware = Container.get(RequestLoggerMiddleware);

app.use(securityMiddleware.use);
app.use(corsMiddleware.use);
app.use(authMiddleware.use);
app.use(contextMiddleware.use);
app.use(requestLoggerMiddleware.use);

RegisterRoutes(app);

const port = envService.ports.admin || 8080;
app.listen(port, () =>
  logger.info(`Admin API listening on local port ${port}.`),
);
