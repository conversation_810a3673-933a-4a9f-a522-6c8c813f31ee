FROM ict-base-shared AS builder
FROM ict-base-distroless

WORKDIR /usr/app/apps/api/ict-admin-api

COPY --from=builder /usr/app/apps/api/starter.ts ./starter.ts
COPY --from=builder /usr/app/apps/api/ict-admin-api/build/routes.ts ./build/routes.ts
COPY --from=builder /usr/app/apps/api/ict-admin-api/package.json ./package.json
COPY --from=builder /usr/app/apps/api/ict-admin-api/tsconfig.json ./tsconfig.json
COPY --from=builder /usr/app/apps/api/ict-admin-api/src ./src

EXPOSE 8080

CMD ["--import", "/usr/app/apps/api/register-tsnode.mjs", "--enable-source-maps", "/usr/app/apps/api/ict-admin-api/starter.ts"]

