{"name": "ict-admin-api", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/ict-admin-api/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "ict-admin-api:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "ict-admin-api:build:development"}, "production": {"buildTarget": "ict-admin-api:build:production"}}}}}