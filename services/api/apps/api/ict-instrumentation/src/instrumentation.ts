import { NodeSDK } from '@opentelemetry/sdk-node';
import { diag } from '@opentelemetry/api';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { TypeormInstrumentation } from 'opentelemetry-instrumentation-typeorm';

const instrumentations = getNodeAutoInstrumentations();
instrumentations.push(new TypeormInstrumentation());

const sdk = new NodeSDK({ instrumentations });

try {
  sdk.start();
  diag.info('OpenTelemetry automatic instrumentation started successfully');
} catch (error) {
  diag.error(
    'Error initializing OpenTelemetry SDK. Your application is not instrumented and will not produce telemetry',
    error,
  );
}

// Gracefully shut down the SDK to flush telemetry when the program exits
process.on('SIGTERM', async () => {
  try {
    await sdk.shutdown();
    diag.debug('OpenTelemetry SDK terminated');
  } catch (error) {
    diag.error('Error terminating OpenTelemetry SDK', error);
  }
});
