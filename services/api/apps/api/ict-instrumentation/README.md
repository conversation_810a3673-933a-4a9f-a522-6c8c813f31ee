# ICT Instrumentation

## Overview

The ICT Instrumentation library provides OpenTelemetry instrumentation for the Control Tower platform. This shared library enables consistent observability across all API services through standardized telemetry collection.

## Purpose

This library serves as a centralized instrumentation solution for OpenTelemetry, providing consistent tracing, metrics, and logging capabilities across all Control Tower services.

## Key Features

- **OpenTelemetry Integration**: Standardized telemetry collection across services
- **Distributed Tracing**: Request tracing across service boundaries
- **Metrics Collection**: Performance and business metrics
- **Structured Logging**: Consistent logging format with correlation IDs
- **Service Discovery**: Automatic service identification and registration

## Architecture

The library is designed as a shared dependency:

```bash
src/
├── instrumentation.ts    # Main instrumentation setup
├── tracing.ts           # Tracing configuration
├── metrics.ts           # Metrics configuration
├── logging.ts           # Logging configuration
└── types.ts             # TypeScript type definitions
```

## Usage

### Service Integration

To use this instrumentation in a service:

```typescript
// In your service's index.ts
import { setupInstrumentation } from '../ict-instrumentation/src/instrumentation';

// Setup instrumentation before starting the server
setupInstrumentation({
  serviceName: 'your-service-name',
  serviceVersion: '1.0.0',
});
```

### Build Process

The library is built using TypeScript and esbuild:

```bash
# Build the instrumentation library
yarn build
```

This creates both TypeScript declarations and a CommonJS build for compatibility.

## Configuration

### Environment Variables

- `OTEL_SERVICE_NAME`: Service name for telemetry identification
- `OTEL_SERVICE_VERSION`: Service version for telemetry
- `OTEL_EXPORTER_OTLP_ENDPOINT`: OTLP exporter endpoint
- `OTEL_TRACES_SAMPLER`: Trace sampling configuration
- `OTEL_METRICS_EXPORTER`: Metrics exporter configuration

### Service Configuration

Each service can configure its own instrumentation settings while maintaining consistency through the shared library.

## Development

### Prerequisites

- Node.js >= 22.0.0
- Yarn package manager

### Local Development

```bash
# Install dependencies
yarn install

# Build the library
yarn build

# Run linting
yarn lint

# Clean build artifacts
yarn clean
```

### Testing

```bash
# Run linting
yarn lint

# Fix linting issues
yarn fix
```

## Deployment

The library is built and distributed as a shared dependency. Services reference the built artifacts:

```bash
# Build for production
yarn build
```

The built files are then available for import by other services.

## Observability Features

### Distributed Tracing

- Automatic trace propagation across service boundaries
- Custom span creation for business operations
- Trace sampling and filtering

### Metrics

- HTTP request metrics (count, duration, error rates)
- Database operation metrics
- Custom business metrics
- Resource utilization metrics

### Logging

- Structured JSON logging
- Correlation ID propagation
- Log level management
- Context-aware logging

## Integration with Services

### Service Startup

Services integrate the instrumentation during startup:

```typescript
// Start with instrumentation
yarn start:otel
```

### Health Checks

Instrumentation includes health check endpoints for monitoring:

- Service availability
- Telemetry exporter health
- Resource utilization

## Best Practices

### Service Naming

Use consistent service naming conventions:

- Format: `ict-{domain}-api`
- Examples: `ict-orders-api`, `ict-inventory-api`

### Trace Propagation

Ensure trace context is properly propagated:

- HTTP headers for service-to-service communication
- Database query tracing
- External API call tracing

### Metrics Naming

Follow consistent metrics naming:

- Use dot notation for hierarchical names
- Include service name prefix
- Use descriptive, action-oriented names

## Related Services

This library is used by all Control Tower API services:

- **ict-admin-api**: Administrative functions
- **ict-orders-api**: Order management
- **ict-inventory-api**: Inventory tracking
- **ict-equipment-api**: Equipment monitoring
- **ict-operators-api**: Operator management
- **ict-workstation-api**: Workstation metrics
- **ict-ai-api**: AI/ML services
- **ict-config-api**: Configuration management
- **ict-dataexplorer-api**: Data exploration
- **ict-diagnostics-api**: System diagnostics
- **ict-simulation-api**: Simulation services
- **ict-tableau-management-api**: Tableau integration
- **ict-tableau-proxy**: Tableau proxy services
