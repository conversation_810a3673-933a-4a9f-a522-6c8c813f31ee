{"name": "ict-data-api", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/ict-data-api/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "ict-data-api:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "ict-data-api:build:development"}, "production": {"buildTarget": "ict-data-api:build:production"}}}}}