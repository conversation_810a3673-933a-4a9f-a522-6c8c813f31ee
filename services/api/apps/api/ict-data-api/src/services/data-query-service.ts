/* eslint-disable @typescript-eslint/no-unused-vars */
import {DiService} from 'ict-api-foundations';
import {DataQuery} from 'ict-api-schema';
import {DataQueryValueResult} from '../defs';
import {DataQueryQueryParams} from '../defs/data-query-query-params';
import {DataQueryResult} from '../defs/data-query-result';

@DiService()
export class DataQueryService {
  public async executeDataQuery(
    dataQuery: DataQuery,
    queryParams: DataQueryQueryParams,
  ): Promise<DataQueryResult> {
    return {
      data: [],
      value: null,
      metadata: {},
    };
  }

  public async getDataQueryValue(
    dataQueryId: string,
    queryParams: DataQueryQueryParams,
  ): Promise<DataQueryValueResult> {
    return {
      metadata: {
        id: '1',
        label: 'Test',
        description: 'Test',
        category: 'Test',
      },
      value: null,
    };
  }
}
