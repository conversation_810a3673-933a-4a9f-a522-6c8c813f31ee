import {expect} from 'chai';
import sinon from 'sinon';
import {Container, ContextService, ConfigStore} from 'ict-api-foundations';
import {DataQuery} from 'ict-api-schema';
import {DataQueryController} from '../../controllers/data-query-controller.ts';
import {DataQueryService} from '../../services/data-query-service.ts';
import {
  type DataQueryQueryParams,
  type DataQueryResult,
  type DataQueryValueResult,
} from '../../defs/index.ts';

describe('DataQueryController', () => {
  let controller: DataQueryController;
  let configStore: sinon.SinonStubbedInstance<ConfigStore>;
  let contextService: sinon.SinonStubbedInstance<ContextService>;
  let dataQueryService: sinon.SinonStubbedInstance<DataQueryService>;

  beforeEach(() => {
    configStore = sinon.createStubInstance(ConfigStore);
    contextService = sinon.createStubInstance(ContextService);
    dataQueryService = sinon.createStubInstance(DataQueryService);

    // Set up default context service behavior
    contextService.userId = 'test-user';

    Container.set(ConfigStore, configStore);
    Container.set(ContextService, contextService);
    Container.set(DataQueryService, dataQueryService);

    controller = new DataQueryController();
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('executeDataQuery', () => {
    const mockDataQuery: DataQuery = {
      id: 'test-query',
      label: 'Test Query',
      description: 'Test description',
      metadata: {
        unit: 'units',
        category: 'test-category',
      },
    } as DataQuery;

    const mockQueryParams: DataQueryQueryParams = {
      start_date: '2023-01-01',
      end_date: '2023-01-02',
    };

    const mockResult: DataQueryResult = {
      data: [{id: 1, value: 100}],
      value: 100,
      metadata: {total: 1},
    };

    beforeEach(() => {
      // Mock feature flag as enabled
      configStore.findMostRelevantSettingForUser.resolves({
        id: 'ict-data-integration-configuration',
        name: 'ict-data-integration-configuration',
        dataType: 'boolean',
        value: true,
      });
      dataQueryService.executeDataQuery.resolves(mockResult);
    });

    it('should execute data query successfully', async () => {
      const result = await controller.executeDataQuery(
        mockDataQuery,
        mockQueryParams,
      );

      expect(result).to.deep.equal(mockResult);
      sinon.assert.calledOnceWithExactly(
        dataQueryService.executeDataQuery,
        mockDataQuery,
        mockQueryParams,
      );
    });

    it('should throw error when feature flag is disabled', async () => {
      configStore.findMostRelevantSettingForUser.resolves({
        id: 'ict-data-integration-configuration',
        name: 'ict-data-integration-configuration',
        dataType: 'boolean',
        value: false,
      });

      try {
        await controller.executeDataQuery(mockDataQuery, mockQueryParams);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        sinon.assert.notCalled(dataQueryService.executeDataQuery);
      }
    });
  });

  describe('getDataQueryValue', () => {
    const dataQueryId = 'test-query-id';
    const mockQueryParams: DataQueryQueryParams = {
      start_date: '2023-01-01',
      end_date: '2023-01-02',
    };

    const mockDataQuery: DataQuery = {
      id: 'test-query',
      label: 'Test Query',
      description: 'Test description',
      metadata: {
        unit: 'units',
        category: 'test-category',
      },
    } as DataQuery;

    const mockExecuteResult: DataQueryResult = {
      data: [{id: 1, value: 100}],
      value: 100,
      metadata: {total: 1},
    };

    const expectedResult: DataQueryValueResult = {
      metadata: {
        id: mockDataQuery.id,
        label: mockDataQuery.label,
        description: mockDataQuery.description,
        unit: mockDataQuery.metadata.unit,
        category: mockDataQuery.metadata.category,
      },
      value: mockExecuteResult.value,
    };

    beforeEach(() => {
      // Mock feature flag as enabled, then data query config
      configStore.findMostRelevantSettingForUser
        .onFirstCall()
        .resolves({
          id: 'ict-data-integration-configuration',
          name: 'ict-data-integration-configuration',
          dataType: 'boolean',
          value: true,
        })
        .onSecondCall()
        .resolves({
          id: dataQueryId,
          name: dataQueryId,
          dataType: 'json',
          value: mockDataQuery,
        });

      dataQueryService.executeDataQuery.resolves(mockExecuteResult);
    });

    it('should get data query value successfully', async () => {
      const result = await controller.getDataQueryValue(
        dataQueryId,
        mockQueryParams,
      );

      expect(result).to.deep.equal(expectedResult);
      sinon.assert.calledTwice(configStore.findMostRelevantSettingForUser);
      sinon.assert.calledOnceWithExactly(
        dataQueryService.executeDataQuery,
        mockDataQuery,
        mockQueryParams,
      );
    });

    it('should throw error when data query configuration not found', async () => {
      configStore.findMostRelevantSettingForUser
        .onFirstCall()
        .resolves({
          id: 'ict-data-integration-configuration',
          name: 'ict-data-integration-configuration',
          dataType: 'boolean',
          value: true,
        })
        .onSecondCall()
        .resolves(undefined);

      try {
        await controller.getDataQueryValue(dataQueryId, mockQueryParams);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect((error as Error).message).to.include(
          `Data query configuration not found for ID: ${dataQueryId}`,
        );
      }
    });

    it('should throw error when feature flag is disabled', async () => {
      // Reset the stubs and set feature flag as disabled
      configStore.findMostRelevantSettingForUser.reset();
      dataQueryService.executeDataQuery.reset();

      configStore.findMostRelevantSettingForUser.resolves({
        id: 'ict-data-integration-configuration',
        name: 'ict-data-integration-configuration',
        dataType: 'boolean',
        value: false,
      });

      try {
        await controller.getDataQueryValue(dataQueryId, mockQueryParams);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        sinon.assert.notCalled(dataQueryService.executeDataQuery);
      }
    });
  });
});
