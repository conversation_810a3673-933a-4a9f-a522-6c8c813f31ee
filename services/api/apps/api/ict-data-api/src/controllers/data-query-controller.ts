import {
  ConfigStore,
  Container,
  ContextService,
  IctError,
  SecurityRoles,
  TsoaSecurityAllRoles,
} from 'ict-api-foundations';
import {type DataQuery} from 'ict-api-schema';
import {
  Body,
  Get,
  OperationId,
  Path,
  Post,
  Queries,
  Route,
  Security,
  Tags,
} from 'tsoa';
import {DataQueryService} from '../services/data-query-service.ts';
import {
  type DataQueryQueryParams,
  type DataQueryResult,
  type DataQueryValueResult,
} from '../defs/index.ts';

@Route('/data')
export class DataQueryController {
  @Post('/query')
  @OperationId('ExecuteDataQuery')
  @Security(TsoaSecurityAllRoles, [SecurityRoles.CT_CONFIGURATORS])
  @Tags('data')
  public async executeDataQuery(
    @Body() dataQuery: DataQuery,
    @Queries() queryParams: DataQueryQueryParams,
  ): Promise<DataQueryResult> {
    await this.checkDataIntegrationConfigurationEnabled();

    const dataQueryService = Container.get(DataQueryService);
    return await dataQueryService.executeDataQuery(dataQuery, queryParams);
  }

  @Get('/{dataQueryId}/value')
  @OperationId('GetDataQueryValue')
  @Security(TsoaSecurityAllRoles, [SecurityRoles.CT_CONFIGURATORS])
  @Tags('data')
  public async getDataQueryValue(
    @Path() dataQueryId: string,
    @Queries() queryParams: DataQueryQueryParams,
  ): Promise<DataQueryValueResult> {
    await this.checkDataIntegrationConfigurationEnabled();

    const configStore = Container.get(ConfigStore);
    const contextService = Container.get(ContextService);

    // Read the DataQuery config by config name (dataQueryId)
    const configSetting = await configStore.findMostRelevantSettingForUser(
      contextService.userId,
      undefined, // settingId (not used)
      dataQueryId,
    );

    if (!configSetting || !configSetting.value) {
      throw new Error(
        `Data query configuration not found for ID: ${dataQueryId}`,
      );
    }

    const dataQuery = configSetting.value as DataQuery;
    const dataQueryService = Container.get(DataQueryService);
    const result = await dataQueryService.executeDataQuery(
      dataQuery,
      queryParams,
    );

    return {
      metadata: {
        id: dataQuery.id,
        label: dataQuery.label,
        description: dataQuery.description,
        unit: dataQuery.metadata.unit,
        category: dataQuery.metadata.category,
      },
      value: result.value,
    };
  }

  /**
   * Feature Flag check for Data Integration Configuration
   * Will be removed once we have fully tested API + UI
   */
  private async checkDataIntegrationConfigurationEnabled(): Promise<void> {
    const configStore = Container.get(ConfigStore);
    const contextService = Container.get(ContextService);
    const configSetting = await configStore.findMostRelevantSettingForUser(
      contextService.userId,
      undefined,
      'ict-data-integration-configuration',
    );

    const isEnabled = configSetting?.value === true;
    if (!isEnabled) {
      throw IctError.notImplemented(
        'Data Integration Configuration is not enabled',
      );
    }
  }
}
