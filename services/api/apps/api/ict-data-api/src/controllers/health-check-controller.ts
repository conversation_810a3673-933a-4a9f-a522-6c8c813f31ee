import {BaseHealthCheck} from 'ict-api-foundations';
import {Get, Route, OperationId, Tags} from 'tsoa';

@Route('/data/healthcheck')
export class HealthCheckController {
  @Get()
  @OperationId('GetDataBasicHealthCheck')
  @Tags('data')
  public getDataBasicHealthCheck() {
    return BaseHealthCheck.getBasicHealthCheck();
  }

  @Get('/full')
  @OperationId('GetDataFullHealthCheck')
  @Tags('data')
  public getDataFullHealthCheck() {
    return BaseHealthCheck.getDeepHealthCheck(['auth0', 'bigQuery', 'redis']);
  }
}
