FROM ict-base-shared AS builder
FROM ict-base-distroless

WORKDIR /usr/app/apps/api/ict-tableau-proxy

COPY --from=builder /usr/app/apps/api/starter.ts ./starter.ts
COPY --from=builder /usr/app/apps/api/ict-tableau-proxy/package.json ./package.json
COPY --from=builder /usr/app/apps/api/ict-tableau-proxy/tsconfig.json ./tsconfig.json
COPY --from=builder /usr/app/apps/api/ict-tableau-proxy/src ./src

EXPOSE 8080

CMD ["--import", "/usr/app/apps/api/register-tsnode.mjs", "--enable-source-maps", "/usr/app/apps/api/ict-tableau-proxy/starter.ts"]
