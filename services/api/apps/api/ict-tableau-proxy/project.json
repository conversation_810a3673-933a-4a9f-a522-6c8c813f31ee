{"name": "ict-tableau-proxy", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/ict-tableau-proxy/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "ict-tableau-proxy:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "ict-tableau-proxy:build:development"}, "production": {"buildTarget": "ict-tableau-proxy:build:production"}}}}}