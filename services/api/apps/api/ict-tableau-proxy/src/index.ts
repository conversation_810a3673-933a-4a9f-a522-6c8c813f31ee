// needed for dependency injection
import 'reflect-metadata';

import express from 'express';
import {
  ApiMiddleware,
  AvailableServices,
  Container,
  EnvironmentService,
  HealthCheckHandler,
  WinstonLogger,
} from 'ict-api-foundations';
import proxy from 'express-http-proxy';
import {PathUtils} from './utils/path-utils.ts';

const apiBasePath = '/tableau';

const origin = '*';
const methods = 'GET, POST, PUT, OPTIONS';
const headers = ['content-type', 'authorization', 'X-Tableau-Auth'];

const app = express();

// get the logger from DI
const logger = Container.get(WinstonLogger);

// use a special CORS middleware that adds the tableau auth header to be allowed
app.use((req, res, next) => {
  res.setHeader('Access-Control-Allow-Origin', origin);
  res.setHeader('Access-Control-Allow-Methods', methods);
  res.setHeader('Access-Control-Allow-Headers', headers);

  // If this is a preflight request, return immediately (don't call the remaining express middlewares)
  if (req.method === 'OPTIONS') {
    res.end();
  } else {
    next();
  }
});

app.get(
  ['/healthcheck', `${apiBasePath}/healthcheck`],
  HealthCheckHandler.getBasicHealthCheck
);
app.get(
  ['/healthcheck/full', `${apiBasePath}/healthcheck/full`],
  (req, res, next) => {
    const reqWServices = req as typeof req & {services: AvailableServices[]};
    reqWServices.services = ['auth0'];
    return HealthCheckHandler.getDeepHealthCheck(req, res, next);
  }
);

// setup the proxy endpoint by forwarding every request to the configured tableau server
// the tableau server url probably needs to be part of the tenant configuration, but using an env variable
// for now as the config db has not been merged yet, but then should we add ICT auth on top as well? (11/13/23)
app.use(
  `${apiBasePath}/*`, // hardcoding this for now, but should we make this a generic proxy instead of tableau specific?
  proxy(process.env.TABLEAU_SERVER_URL ?? 'http://localhost:80', {
    proxyReqPathResolver: (req): string =>
      PathUtils.getTableauPathFromProxyPath(req.originalUrl, logger),
  })
);

ApiMiddleware.applyErrorMiddlewares(app);
const envService = Container.get(EnvironmentService);
const port = envService.ports.tableauProxy || 8080;
app.listen(port, () =>
  logger.info(`Tableau Proxy API listening on local port ${port}.`)
);
