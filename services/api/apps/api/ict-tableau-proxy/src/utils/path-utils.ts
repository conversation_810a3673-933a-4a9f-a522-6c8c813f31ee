import {Logger} from 'ict-api-foundations';

export class PathUtils {
  /**
   * Takes the originalURL form an express request and removes the first prefix of the path, expected to be /tableau.
   * @param proxyPath Path that includes the /tableau/ prefix
   * @param logger Logger object to use
   * @returns String containing the path without the /tableau prefix
   */
  static getTableauPathFromProxyPath(
    proxyPath: string,
    logger: Logger
  ): string {
    // ensure to remove the /tableau prefix from the proxied request path
    const parts = proxyPath.split('/');
    const newPath = `/${parts.slice(2).join('/')}`;
    logger.info(`Proxying request path from ${proxyPath} to ${newPath}`);
    return newPath;
  }
}
