import {expect} from 'chai';
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>} from 'ict-api-foundations';
import {PathUtils} from '../utils/path-utils.ts';

const logger = Container.get(WinstonLogger);

describe('PathUtils', () => {
  describe('getTableauPathFromProxyPath', () => {
    it('should remove the /tableau prefix from the proxied request path', () => {
      const proxyPath = '/tableau/path/to/tableau';
      const expectedPath = '/path/to/tableau';
      const actualPath = PathUtils.getTableauPathFromProxyPath(
        proxyPath,
        logger
      );
      expect(actualPath).to.equal(expectedPath);
    });
  });
});
