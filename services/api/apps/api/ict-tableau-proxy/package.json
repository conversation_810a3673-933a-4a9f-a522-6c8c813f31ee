{"name": "ict-tableau-proxy", "version": "1.0.0", "description": "Proxy for contacting a Tableau Server's API that doesn't have CORS enabled.", "author": "Dematic", "license": "ISC", "type": "module", "main": "src/index.ts", "engines": {"node": ">=22.0.0"}, "scripts": {"build": "npx tsc", "test": "yarn run -T mocha", "test:watch": "mocha --watch", "start": "node --import ../register-tsnode.mjs --enable-source-maps ./src/index.ts", "start:otel": "node --require ../ict-instrumentation/build/instrumentation.cjs --import ../register-tsnode.mjs ./src/index.ts", "lint": "yarn run -T gts lint", "clean": "yarn run -T gts clean", "fix": "yarn run -T gts fix", "posttest": "yarn lint", "debug": "nodemon --exec \"node --inspect=0.0.0.0:9229 --import ../register-tsnode.mjs ./src/index.ts\"", "watch": "node --watch --no-warnings=ExperimentalWarning --import ../register-tsnode.mjs ./src/index.ts | yarn run -T pino-pretty"}}