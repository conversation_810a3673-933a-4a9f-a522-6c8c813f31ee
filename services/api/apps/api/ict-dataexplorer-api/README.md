# ICT Data Explorer API

## Overview

The ICT Data Explorer API provides data exploration and querying capabilities for the Control Tower platform. This service enables users to perform ad-hoc data queries, generate reports, and access data visualization support.

## Purpose

This API serves as the logic layer for handling business logic for the Data Explorer within Control Tower, providing flexible data access and analytics capabilities.

## Key Features

- **Ad-hoc Data Queries**: Execute custom queries against various data sources
- **Report Generation**: Create and manage custom reports
- **Data Visualization Support**: Provide data in formats suitable for visualization tools
- **Enterprise Search Integration**: Leverage enterprise search for data discovery
- **Recommendations**: Provide data-driven recommendations based on query patterns

## API Endpoints

### Health Check

- `GET /health/basic` - Basic service health check

### Data Exploration

- `POST /data-explorer/search` - Search and query data
- `GET /data-explorer/results` - Retrieve query results
- `POST /data-explorer/recommendations` - Get data exploration recommendations

### Agent Operations

- `POST /data-explorer/agent` - Interact with data exploration agent

## Architecture

The service follows the standard Control Tower API architecture:

```bash
src/
├── controllers/     # TSOA controllers with route definitions
│   ├── data-explorer-agent-controller.ts
│   ├── data-explorer-health-check-controller.ts
│   ├── data-explorer-recommendations-controller.ts
│   ├── data-explorer-result-controller.ts
│   └── data-explorer-search-controller.ts
├── services/        # Business logic services
├── stores/          # Data access layer
├── defs/           # TypeScript type definitions
├── test/           # Unit tests
└── index.ts        # Service entry point
```

## Dependencies

- **Enterprise Search**: Integration with enterprise search for data discovery
- **Database**: PostgreSQL for structured data queries
- **BigQuery**: Analytics database for large-scale data processing
- **Redis**: Caching for query results and recommendations

## Environment Variables

- `ENTERPRISE_SEARCH_SECRETS_NAME`: Secret name for enterprise search credentials
- `ENTERPRISE_SEARCH_URL`: Enterprise search service URL
- `AUTH_AUDIENCE`: Authentication audience for JWT validation
- `AUTH_DOMAIN`: Auth0 domain for authentication

## Development

### Prerequisites

- Node.js >= 22.0.0
- Yarn package manager
- Access to enterprise search service

### Local Development

```bash
# Install dependencies
yarn install

# Start development server
yarn watch

# Run tests
yarn test

# Build for production
yarn build
```

### Testing

```bash
# Run all tests
yarn test

# Run tests in watch mode
yarn test:watch

# Run linting
yarn lint
```

## Deployment

The service is containerized using Docker and can be deployed to Google Cloud Run or other container platforms.

### Docker Build

```bash
docker build -t ict-dataexplorer-api .
```

### Environment Configuration

Ensure all required environment variables are configured for the deployment environment.

## Monitoring

- **Health Checks**: Available at `/health/basic`
- **Metrics**: Service metrics exported for monitoring
- **Logging**: Structured JSON logging with correlation IDs

## Related Services

- **ict-ai-api**: AI/ML services for enhanced data analysis
- **ict-admin-api**: Administrative functions and user management
- **ict-config-api**: Configuration management for data explorer settings
