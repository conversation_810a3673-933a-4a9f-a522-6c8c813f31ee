import sinon from 'sinon';
import axios from 'axios';
import {expect} from 'chai';
import {
  Container,
  ContextService,
  Environment,
  GCPSecretManager,
  RedisClient,
} from 'ict-api-foundations';
import {AIService} from '../../services/ai-service.ts';

describe('ai-service', () => {
  let secretManagerStub: sinon.SinonStubbedInstance<GCPSecretManager>;
  let axiosGetStub: sinon.SinonStub;
  let aiService: AIService;
  let contextService: ContextService;
  let redisClient: sinon.SinonStubbedInstance<RedisClient>;

  beforeEach(() => {
    contextService = new ContextService();
    sinon.stub(contextService, 'datasetId').get(() => 'fake_dataset');

    redisClient = sinon.createStubInstance(RedisClient);

    aiService = new AIService(contextService, redisClient);
    axiosGetStub = sinon.stub(axios, 'get');
    process.env.ENVIRONMENT = Environment.local;
    process.env.AIML_INVOCATION_URL = 'fake_url';
    process.env.LOCAL_ID_TOKEN = 'fake_token';
    secretManagerStub = sinon.createStubInstance(GCPSecretManager);
    secretManagerStub.get.resolves('fake_secret');
    Container.set(GCPSecretManager, secretManagerStub);
  });

  afterEach(() => {
    delete process.env.ENVIRONMENT;
    delete process.env.AIML_INVOCATION_URL;
    delete process.env.LOCAL_ID_TOKEN;
    sinon.restore();
  });

  describe('getRecommendations', async () => {
    afterEach(() => {
      axiosGetStub.restore();
    });

    beforeEach(() => {
      // Default act as if no cache has been set
      redisClient.get.resolves(null);

      redisClient.ttl.resolves(300);
      redisClient.setEx.resolves();
    });

    it('should get recommendations from Data Explorer', async () => {
      axiosGetStub.resolves({
        status: 200,
        data: ['fake question?'],
      });
      const results = await aiService.getRecommendations();
      expect(results).to.deep.equal(['fake question?']);
    });

    it('should return an empty array for any errors', async () => {
      axiosGetStub.resolves({status: 500});
      const result = await aiService.getRecommendations();
      expect(result).to.deep.equal([]);
    });

    it('should cache data on a successful response', async () => {
      const cacheKey = 'fake_dataset:data_explorer_recommended_questions';
      axiosGetStub.resolves({
        status: 200,
        data: ['Is this a fake question?'],
      });
      await aiService.getRecommendations();
      expect(redisClient.setEx.calledOnce).to.be.true;
      expect(redisClient.setEx.firstCall.args[0]).to.equal(cacheKey);
      expect(redisClient.setEx.firstCall.args[2]).to.equal(
        JSON.stringify(['Is this a fake question?']),
      );
    });

    it('should return cached data if there is any set', async () => {
      redisClient.get.resolves(JSON.stringify(['fake question?']));
      const result = await aiService.getRecommendations();
      expect(redisClient.get.calledOnce).to.be.true;
      expect(result).to.deep.equal(['fake question?']);
    });
  });

  describe('getGoldQuestions', async () => {
    afterEach(() => {
      axiosGetStub.restore();
    });

    beforeEach(() => {
      // Default act as if no cache has been set
      redisClient.get.resolves(null);

      redisClient.ttl.resolves(300);
      redisClient.setEx.resolves();
    });

    it('should get gold questions from external service', async () => {
      axiosGetStub.resolves({
        status: 200,
        data: ['fake gold question?'],
      });
      const results = await aiService.getGoldQuestions();
      expect(results).to.deep.equal(['fake gold question?']);
    });

    it('should return an empty array for any errors', async () => {
      axiosGetStub.resolves({status: 500});
      const result = await aiService.getGoldQuestions();
      expect(result).to.deep.equal([]);
    });

    it('should cache data on a successful response', async () => {
      const cacheKey = 'fake_dataset:dematic_chat_gold_questions';
      axiosGetStub.resolves({
        status: 200,
        data: ['Is this a fake gold question?'],
      });
      await aiService.getGoldQuestions();
      expect(redisClient.setEx.calledOnce).to.be.true;
      expect(redisClient.setEx.firstCall.args[0]).to.equal(cacheKey);
      expect(redisClient.setEx.firstCall.args[2]).to.equal(
        JSON.stringify(['Is this a fake gold question?']),
      );
    });

    it('should return cached data if there is any set', async () => {
      redisClient.get.resolves(JSON.stringify(['fake gold question?']));
      const result = await aiService.getGoldQuestions();
      expect(redisClient.get.calledOnce).to.be.true;
      expect(result).to.deep.equal(['fake gold question?']);
    });

    it('should handle Redis cache errors gracefully', async () => {
      const cacheError = new Error('Redis connection failed');
      redisClient.setEx.throws(cacheError);
      axiosGetStub.resolves({
        status: 200,
        data: ['gold question with cache error'],
      });

      // Should not throw error and still return data
      const result = await aiService.getGoldQuestions();
      expect(result).to.deep.equal(['gold question with cache error']);
    });
  });
  describe('search', () => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let axiosPostStub: sinon.SinonStub;
    const fakeRequest = {
      user_question: 'fake question',
      user_database: 'fake_database',
    };

    beforeEach(() => {
      axiosPostStub = sinon.stub(axios, 'post');
    });

    afterEach(() => {
      axiosPostStub.restore();
    });

    it('should return a successful search response from AIML service', async () => {
      const mockResponseData = {
        Error: null,
        KnownDB: true,
        ResponseCode: 200,
        echart_code: "{'key': 'value'}",
        generated_sql_query: 'SELECT * FROM fake_table',
        question: 'fake question',
        sql_query_output: '[{"col1": "val1"}]',
        summary_response: 'This is a summary',
      };
      axiosPostStub.resolves({data: mockResponseData});

      const result = await aiService.search(fakeRequest);

      expect(axiosPostStub.calledOnce).to.be.true;
      expect(result).to.deep.equal({
        error: null,
        known_DB: true,
        response_code: 200,
        echart_code: "{'key': 'value'}",
        parsed_echart_code: {key: 'value'},
        generated_sql_query: 'SELECT * FROM fake_table',
        question: 'fake question',
        sql_query_output: [{col1: 'val1'}],
        summary_response: 'This is a summary',
        debug_data: mockResponseData,
      });
    });

    it('should handle AIML service error response', async () => {
      const mockErrorResponseData = {
        Error: 'AIML Error',
        KnownDB: false,
        ResponseCode: 500,
        echart_code: null,
        generated_sql_query: null,
        question: 'fake question',
        sql_query_output: null,
        summary_response: null,
      };
      axiosPostStub.resolves({data: mockErrorResponseData});

      const result = await aiService.search(fakeRequest);
      expect(result.error).to.equal('AIML Error');
      expect(result.response_code).to.equal(500);
    });

    it('should throw an error if secret manager is not available in non-local environment', async () => {
      process.env.ENVIRONMENT = Environment.dev;
      const containerGetStub = sinon.stub(Container, 'get');
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      containerGetStub.withArgs(GCPSecretManager as any).returns(undefined);

      try {
        await aiService.search(fakeRequest);
        expect.fail('Should have thrown an error');
      } catch (e) {
        const error = e as Error;
        expect(error.message).to.equal(
          'A secret manager is required for accessing the AIML Invocation url.',
        );
      } finally {
        containerGetStub.restore();
        delete process.env.ENVIRONMENT;
      }
    });

    it('should correctly parse echart_code when it is a stringified JSON', async () => {
      const mockResponseData = {
        ResponseCode: 200,
        echart_code: '{"title": {"text": "Test Chart"}}',
      };
      axiosPostStub.resolves({data: mockResponseData});
      const result = await aiService.search(fakeRequest);
      expect(result.parsed_echart_code).to.deep.equal({
        title: {text: 'Test Chart'},
      });
    });

    it('should correctly parse echart_code with "option =" prefix and markdown', async () => {
      const mockResponseData = {
        ResponseCode: 200,
        echart_code:
          '```json\noption = {\n  "title": {\n    "text": "Test Chart"\n  }\n}\n```',
      };
      axiosPostStub.resolves({data: mockResponseData});
      const result = await aiService.search(fakeRequest);
      expect(result.parsed_echart_code).to.deep.equal({
        title: {text: 'Test Chart'},
      });
    });

    it('should return null for parsed_echart_code if echart_code is invalid', async () => {
      const mockResponseData = {
        ResponseCode: 200,
        echart_code: 'this is not json',
      };
      axiosPostStub.resolves({data: mockResponseData});
      const result = await aiService.search(fakeRequest);
      expect(result.parsed_echart_code).to.be.null;
    });

    it('should correctly parse sql_query_output when it is a stringified JSON array', async () => {
      const mockResponseData = {
        ResponseCode: 200,
        sql_query_output: '[{"id": 1, "name": "Test"}]',
      };
      axiosPostStub.resolves({data: mockResponseData});
      const result = await aiService.search(fakeRequest);
      expect(result.sql_query_output).to.deep.equal([{id: 1, name: 'Test'}]);
    });

    it('should return empty array for sql_query_output if it is not a valid JSON array', async () => {
      const mockResponseData = {
        ResponseCode: 200,
        sql_query_output: 'this is not a json array',
      };
      axiosPostStub.resolves({data: mockResponseData});
      const result = await aiService.search(fakeRequest);
      expect(result.sql_query_output).to.deep.equal([]);
    });

    it('should return empty array for sql_query_output if it is a JSON object but not an array', async () => {
      const mockResponseData = {
        ResponseCode: 200,
        sql_query_output: '{"data": "not an array"}',
      };
      axiosPostStub.resolves({data: mockResponseData});
      const result = await aiService.search(fakeRequest);
      expect(result.sql_query_output).to.deep.equal([]);
    });
  });
  describe('getAgentSearch', () => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let axiosPostStub: sinon.SinonStub;
    const fakeRequest = {
      user_question: 'example question',
      user_database: 'fake_database',
    };
    const fakeAgentSearchUrl = 'http://fake-data-explorer-url';
    const fakeAgentSearchAudienceUrl = 'http://fake-agent-ai-url';
    const fakeAccessToken = 'fake-access-token';
    const fakeSessionId = 'fake-session-id123';

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let mockStream: any;

    beforeEach(() => {
      axiosPostStub = sinon.stub(axios, 'post');
      process.env.ENVIRONMENT = Environment.local;
      process.env.LOCAL_ID_TOKEN = fakeAccessToken;
      process.env.AGENT_SEARCH_AUDIENCE_URL = fakeAgentSearchAudienceUrl;
      process.env.AGENT_SEARCH_URL = fakeAgentSearchUrl;

      mockStream = {
        on: sinon.stub(),
        // eslint-disable-next-line @typescript-eslint/no-empty-function, no-empty-function
        destroy: () => {},
      };
      mockStream.on.returns(mockStream);
    });

    afterEach(() => {
      axiosPostStub.restore();
      delete process.env.AGENT_SEARCH_AUDIENCE_URL;
      delete process.env.AGENT_SEARCH_URL;
      delete process.env.LOCAL_ID_TOKEN;
    });

    it('should successfully create session if no sessionId provided, stream query, and return messages', async () => {
      axiosPostStub.onFirstCall().resolves({
        status: 200,
        data: {},
      });
      axiosPostStub.onSecondCall().resolves({
        status: 200,
        data: mockStream,
        headers: {'content-type': 'text/event-stream'},
      });

      setImmediate(() => {
        const dataCb = mockStream.on.withArgs('data').firstCall.args[1];
        dataCb(
          Buffer.from('data: {"content": {"parts": [{"text": "Hello "}]}}\n\n'),
        );
        dataCb(
          Buffer.from('data: {"content": {"parts": [{"text": "World!"}]}}\n\n'),
        );
        const endCb = mockStream.on.withArgs('end').firstCall.args[1];
        endCb();
      });

      const requestWithoutSession = {
        ...fakeRequest,
        userSessionId: undefined,
      };
      const result = await aiService.getAgentSearch(requestWithoutSession);

      expect(axiosPostStub.calledTwice).to.be.true;

      const sessionCallArgs = axiosPostStub.firstCall.args;
      expect(sessionCallArgs[0]).to.include(
        `${process.env.AGENT_SEARCH_URL}/apps/data_explorer_agent/users/${contextService.userEmail}/sessions/`,
      );
      expect(sessionCallArgs[1]).to.deep.equal({state: {}});
      expect(sessionCallArgs[2].headers.Authorization).to.equal(
        `Bearer ${fakeAccessToken}`,
      );

      const streamCallArgs = axiosPostStub.secondCall.args;
      expect(streamCallArgs[0]).to.equal(
        `${process.env.AGENT_SEARCH_URL}/run_sse`,
      );
      expect(streamCallArgs[1].app_name).to.equal('data_explorer_agent');
      expect(streamCallArgs[1].user_id).to.equal(contextService.userEmail);
      expect(streamCallArgs[1].new_message.parts[0].text).to.equal(
        fakeRequest.user_question,
      );
      expect(streamCallArgs[2].headers.Authorization).to.equal(
        `Bearer ${fakeAccessToken}`,
      );
      expect(result.sessionId).to.be.a('string');
      expect(result.status).to.equal('completed');
      expect(result.messages).to.deep.equal(['Hello ', 'World!']);
    });

    it('should use existing sessionId if provided, stream query, and return messages', async () => {
      axiosPostStub.onFirstCall().resolves({
        status: 200,
        data: mockStream,
        headers: {'content-type': 'text/event-stream'},
      });

      setImmediate(() => {
        const dataCb = mockStream.on.withArgs('data').firstCall.args[1];
        dataCb(
          Buffer.from('data: {"content": {"parts": [{"text": "Test "}]}}\n\n'),
        );
        const endCb = mockStream.on.withArgs('end').firstCall.args[1];
        endCb();
      });

      const requestWithSession = {...fakeRequest, userSessionId: fakeSessionId};
      const result = await aiService.getAgentSearch(requestWithSession);

      expect(axiosPostStub.calledOnce).to.be.true;

      const streamCallArgs = axiosPostStub.firstCall.args;
      expect(streamCallArgs[0]).to.equal(
        `${process.env.AGENT_SEARCH_URL}/run_sse`,
      );
      expect(streamCallArgs[1].session_id).to.equal(fakeSessionId);
      expect(streamCallArgs[1].new_message.parts[0].text).to.equal(
        fakeRequest.user_question,
      );
      expect(streamCallArgs[2].headers.Authorization).to.equal(
        `Bearer ${fakeAccessToken}`,
      );

      expect(result.sessionId).to.equal(fakeSessionId);
      expect(result.status).to.equal('completed');
      expect(result.messages).to.deep.equal(['Test ']);
    });

    it('should handle session creation POST failure', async () => {
      axiosPostStub.onFirstCall().resolves({
        status: 500,
        data: {message: 'Session creation failed'},
      });

      try {
        await aiService.getAgentSearch({
          ...fakeRequest,
          userSessionId: undefined,
        });
        expect.fail(
          'Should have thrown an error due to session creation POST failure',
        );
      } catch (e) {
        const error = e as Error;
        expect(error.message).to.contain(
          'Failed to interact with agent session endpoint. Status: 500',
        );
      }
      expect(axiosPostStub.calledOnce).to.be.true;
    });

    it('should handle stream query POST non-200 status', async () => {
      axiosPostStub.onFirstCall().resolves({
        status: 200,
        data: {},
      });
      axiosPostStub.onSecondCall().resolves({
        status: 500,
        statusText: 'Internal Server Error',
        data: mockStream,
        headers: {'content-type': 'text/event-stream'},
      });

      setImmediate(() => {
        const endCb = mockStream.on.withArgs('end').firstCall?.args[1];
        if (endCb) {
          endCb();
        } else {
          const errorCb = mockStream.on.withArgs('error').firstCall?.args[1];
          if (errorCb) {
            errorCb(new Error('Stream error due to status'));
          }
        }
      });
      const requestWithoutSession = {
        ...fakeRequest,
        userSessionId: undefined,
      };
      const result = await aiService.getAgentSearch(requestWithoutSession);
      expect(result.status).to.equal('error');
      expect(result.messages).to.deep.equal(['Request failed with status 500']);
    });

    it('should handle stream error event during streaming', async () => {
      axiosPostStub.onFirstCall().resolves({status: 200, data: {}});
      axiosPostStub.onSecondCall().resolves({
        status: 200,
        data: mockStream,
        headers: {'content-type': 'text/event-stream'},
      });

      setImmediate(() => {
        const errorCb = mockStream.on.withArgs('error').firstCall.args[1];
        errorCb(new Error('Stream connection failed mid-stream'));
      });
      const requestWithoutSession = {
        ...fakeRequest,
        userSessionId: undefined,
      };
      const result = await aiService.getAgentSearch(requestWithoutSession);
      expect(result.status).to.equal('error');
      expect(result.messages).to.deep.equal([]);
    });
    it('should throw error if LOCAL_ID_TOKEN is not set in local env', async () => {
      process.env.ENVIRONMENT = Environment.local;
      delete process.env.LOCAL_ID_TOKEN;

      axiosPostStub.restore();

      try {
        await aiService.getAgentSearch(fakeRequest);
        expect.fail('Should have thrown due to missing LOCAL_ID_TOKEN');
      } catch (e) {
        const error = e as Error;
        expect(error.message).to.equal(
          'LOCAL_ID_TOKEN environment variable is required for local development',
        );
      }
    });

    it('should throw error if AGENT_SEARCH_AUDIENCE_URL or AGENT_SEARCH_URL is not set', async () => {
      delete process.env.AGENT_SEARCH_AUDIENCE_URL;
      try {
        await aiService.getAgentSearch(fakeRequest);
        expect.fail(
          'Should have thrown due to missing AGENT_SEARCH_AUDIENCE_URL',
        );
      } catch (e) {
        const error = e as Error;
        expect(error.message).to.equal(
          'AGENT_SEARCH_AUDIENCE_URL and AGENT_SEARCH_URL environment variables are required.',
        );
      }

      process.env.AGENT_SEARCH_AUDIENCE_URL = fakeAgentSearchAudienceUrl;
      delete process.env.AGENT_SEARCH_URL;
      try {
        await aiService.getAgentSearch(fakeRequest);
        expect.fail('Should have thrown due to missing AGENT_SEARCH_URL');
      } catch (e) {
        const error = e as Error;
        expect(error.message).to.equal(
          'AGENT_SEARCH_AUDIENCE_URL and AGENT_SEARCH_URL environment variables are required.',
        );
      }
    });
  });
});
