import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
import {DataExplorerAgentController} from '../../controllers/data-explorer-agent-controller.ts';
import {DataExplorerService} from '../../services/dataexplorer-service.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';

describe('DataExplorerAgentController', () => {
  const url: string = '/data-explorer/agentsearch';

  const app = appSetup(RegisterRoutes);

  let dataExplorerServiceStub: sinon.SinonStubbedInstance<DataExplorerService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Data Explorer Agent Controller Error scenarios', () => {
    it('should validate that searchText param is passed', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details.searchText.message).to.equal(
        "'searchText' is required"
      );
    });

    it('should error when the service throws an error', async () => {
      dataExplorerServiceStub = sinon.createStubInstance(DataExplorerService);
      Container.set(DataExplorerService, dataExplorerServiceStub);
      dataExplorerServiceStub.getAgentSearch.rejects(
        new Error('Something bad happened in example service')
      );
      const searchText = 'What is an example?';
      const response = await request(app).get(url).query({
        searchText,
      });

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWith(
        dataExplorerServiceStub.getAgentSearch,
        searchText
      );
    });
  });
  describe('Data Explorer Agent Controller success scenarios', () => {
    beforeEach(() => {
      dataExplorerServiceStub = sinon.createStubInstance(DataExplorerService);
      Container.set(DataExplorerService, dataExplorerServiceStub);
      dataExplorerServiceStub.getAgentSearch.resolves(
        DataExplorerAgentController.exampleAgentSearchResponse
      );
    });

    it('should call "DataExplorerService.getAgentSearch" and return the data', async () => {
      const searchText = 'Show me an example.';
      const response = await request(app).get(url).query({
        searchText,
      });

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        JSON.parse(
          JSON.stringify(DataExplorerAgentController.exampleAgentSearchResponse)
        )
      );

      sinon.assert.calledWith(
        dataExplorerServiceStub.getAgentSearch,
        searchText
      );
    });
  });
});
