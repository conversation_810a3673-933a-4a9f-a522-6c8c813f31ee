import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
import {DataExplorerSearchController} from '../../controllers/data-explorer-search-controller.ts';
import {DataExplorerService} from '../../services/dataexplorer-service.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';

describe('DataExplorerSearchController', () => {
  const url: string = '/data-explorer/search';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the DataExplorer Service. */
  let dataExplorerServiceStub: sinon.SinonStubbedInstance<DataExplorerService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Data Explorer Search Controller Error scenarios', () => {
    it('should validate that searchText param is passed', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details.searchText.message).to.equal(
        "'searchText' is required"
      );
    });

    it('should error when the service throws an error', async () => {
      dataExplorerServiceStub = sinon.createStubInstance(DataExplorerService);
      Container.set(DataExplorerService, dataExplorerServiceStub);
      dataExplorerServiceStub.search.rejects(
        new Error('Something bad happened')
      );
      const searchText = 'What is the answer to everything?';
      const response = await request(app).get(url).query({
        searchText,
      });

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWith(dataExplorerServiceStub.search, searchText);
    });
  });
  describe('Data Explorer Search Controller success scenarios', () => {
    beforeEach(() => {
      dataExplorerServiceStub = sinon.createStubInstance(DataExplorerService);
      Container.set(DataExplorerService, dataExplorerServiceStub);
      dataExplorerServiceStub.search.resolves(
        DataExplorerSearchController.exampleData
      );
    });

    it('should call "DataExplorerService.search" and return the data', async () => {
      const searchText = 'What is the answer to everything?';
      const response = await request(app).get(url).query({
        searchText,
      });

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        JSON.parse(JSON.stringify(DataExplorerSearchController.exampleResponse))
      );

      sinon.assert.calledWith(dataExplorerServiceStub.search, searchText);
    });
  });
  describe('Data Explorer Search Controller success scenarios', () => {
    beforeEach(() => {
      dataExplorerServiceStub = sinon.createStubInstance(DataExplorerService);
      Container.set(DataExplorerService, dataExplorerServiceStub);
      dataExplorerServiceStub.search.resolves(
        DataExplorerSearchController.exampleData
      );
    });

    it('should call "DataExplorerService.search" and return the data', async () => {
      const searchText = 'What is the answer to everything?';
      const response = await request(app).get(url).query({
        searchText,
      });

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        JSON.parse(JSON.stringify(DataExplorerSearchController.exampleResponse))
      );

      sinon.assert.calledWith(dataExplorerServiceStub.search, searchText);
    });
  });
});
