import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
import {DataExplorerResultController} from '../../controllers/data-explorer-result-controller.ts';
import {DataExplorerService} from '../../services/dataexplorer-service.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';

describe('DataExplorerResultController', () => {
  const url: string = '/data-explorer/results';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the DataExplorer Service. */
  let dataExplorerServiceStub: sinon.SinonStubbedInstance<DataExplorerService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Data Explorer Result Controller Error scenarios', () => {
    it('should validate that startDate and endDate params are passed', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      console.log(response.body);
      expect(response.body.details.start_date.message).to.equal(
        "'start_date' is required"
      );
      expect(response.body.details.end_date.message).to.equal(
        "'end_date' is required"
      );
    });

    it('should validate that startDate and endDate params are valid ISO8601 dates', async () => {
      const response = await request(app).get(url).query({
        start_date: '20-01-01',
        end_date: '01/01/2020 00:00:00',
      });

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details.start_date.message).to.equal(
        'invalid ISO 8601 datetime format, i.e. YYYY-MM-DDTHH:mm:ss'
      );
      expect(response.body.details.end_date.message).to.equal(
        'invalid ISO 8601 datetime format, i.e. YYYY-MM-DDTHH:mm:ss'
      );
    });

    it('should validate that startDate comes before endDate', async () => {
      dataExplorerServiceStub = sinon.createStubInstance(DataExplorerService);
      Container.set(DataExplorerService, dataExplorerServiceStub);

      const response = await request(app).get(url).query({
        start_date: '2023-01-02T00:00:00',
        end_date: '2023-01-01T00:00:00',
      });

      expect(response.status).to.equal(422);
      expect(response.body.status).to.equal(422);
      expect(response.body.title).to.equal('Internal Server Error');
      expect(response.body.detail).to.equal(
        'start_date must be before end_date'
      );
    });

    it('should error when getResults throws an error', async () => {
      dataExplorerServiceStub = sinon.createStubInstance(DataExplorerService);
      Container.set(DataExplorerService, dataExplorerServiceStub);
      dataExplorerServiceStub.getResults.rejects(
        new Error('Something bad happened')
      );

      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';

      const response = await request(app).get(url).query({
        start_date: startDate,
        end_date: endDate,
      });

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWith(
        dataExplorerServiceStub.getResults,
        new Date(startDate),
        new Date(endDate),
        10,
        undefined
      );
    });

    it('should error when getResult throws an error', async () => {
      dataExplorerServiceStub = sinon.createStubInstance(DataExplorerService);
      Container.set(DataExplorerService, dataExplorerServiceStub);
      dataExplorerServiceStub.getResult.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app).get(
        `${url}/${DataExplorerResultController.exampleItem.id}`
      );

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWith(
        dataExplorerServiceStub.getResult,
        DataExplorerResultController.exampleItem.id
      );
    });

    it('should error when deleteResult is called with a missing request', async () => {
      dataExplorerServiceStub = sinon.createStubInstance(DataExplorerService);
      Container.set(DataExplorerService, dataExplorerServiceStub);
      dataExplorerServiceStub.deleteResult.rejects(
        new Error('Something bad happened')
      );
      const response = await request(app).delete(
        `${url}/${DataExplorerResultController.exampleItem.id}`
      );
      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
      sinon.assert.calledWith(
        dataExplorerServiceStub.deleteResult,
        DataExplorerResultController.exampleItem.id
      );
    });

    it('should error when updateResult throws an error', async () => {
      dataExplorerServiceStub = sinon.createStubInstance(DataExplorerService);
      Container.set(DataExplorerService, dataExplorerServiceStub);
      dataExplorerServiceStub.updateResult.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app)
        .put(`${url}/${DataExplorerResultController.exampleItem.id}`)
        .send(DataExplorerResultController.exampleItem);

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWith(
        dataExplorerServiceStub.updateResult,
        DataExplorerResultController.exampleItem.id,
        DataExplorerResultController.exampleItem
      );
    });

    it('should error when updating with invalid json body', async () => {
      dataExplorerServiceStub = sinon.createStubInstance(DataExplorerService);
      Container.set(DataExplorerService, dataExplorerServiceStub);
      dataExplorerServiceStub.updateResult.resolves(
        DataExplorerResultController.exampleItem
      );

      const response = await request(app)
        .put(`${url}/${DataExplorerResultController.exampleItem.id}`)
        .send({hello: 'world'});

      expect(response.status).to.equal(422);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details.dataExplorerResult.message).to.equal(
        '"hello" is an excess property and therefore is not allowed'
      );

      sinon.assert.notCalled(dataExplorerServiceStub.updateResult);
    });
  });
  describe('Data Explorer Result Controller success scenarios', () => {
    beforeEach(() => {
      dataExplorerServiceStub = sinon.createStubInstance(DataExplorerService);
      Container.set(DataExplorerService, dataExplorerServiceStub);
    });

    it('should call "DataExplorerService.getResults" and return the data', async () => {
      dataExplorerServiceStub.getResults.resolves(
        DataExplorerResultController.exampleResponse
      );
      const startDate = '2023-01-01T00:00:00.000Z';
      const endDate = '2023-01-02T00:00:00.000Z';
      const response = await request(app).get(url).query({
        start_date: startDate,
        end_date: endDate,
      });

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        JSON.parse(JSON.stringify(DataExplorerResultController.exampleResponse))
      );

      sinon.assert.calledWith(
        dataExplorerServiceStub.getResults,
        new Date(startDate),
        new Date(endDate),
        10,
        undefined
      );
    });

    it('should call "DataExplorerService.getResult" and return the data', async () => {
      dataExplorerServiceStub = sinon.createStubInstance(DataExplorerService);
      Container.set(DataExplorerService, dataExplorerServiceStub);
      dataExplorerServiceStub.getResult.resolves(
        DataExplorerResultController.exampleItem
      );

      const response = await request(app).get(
        `${url}/${DataExplorerResultController.exampleItem.id}`
      );

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        JSON.parse(
          JSON.stringify(DataExplorerResultController.exampleItemResponse)
        )
      );

      sinon.assert.calledWith(
        dataExplorerServiceStub.getResult,
        DataExplorerResultController.exampleItem.id
      );
    });

    it('should call "DataExplorerService.deleteResult" and return the data', async () => {
      dataExplorerServiceStub = sinon.createStubInstance(DataExplorerService);
      Container.set(DataExplorerService, dataExplorerServiceStub);
      dataExplorerServiceStub.deleteResult.resolves();

      const response = await request(app).delete(
        `${url}/${DataExplorerResultController.exampleItem.id}`
      );

      expect(response.status).to.equal(200);

      sinon.assert.calledWith(
        dataExplorerServiceStub.deleteResult,
        DataExplorerResultController.exampleItem.id
      );
    });

    it('should call saveResult when updateResult endpoint called', async () => {
      dataExplorerServiceStub.updateResult.resolves(
        DataExplorerResultController.exampleItem
      );

      const response = await request(app)
        .put(`${url}/${DataExplorerResultController.exampleItem.id}`)
        .send(DataExplorerResultController.exampleItem);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        JSON.parse(
          JSON.stringify(DataExplorerResultController.exampleItemResponse)
        )
      );

      sinon.assert.calledWith(
        dataExplorerServiceStub.updateResult,
        DataExplorerResultController.exampleItem.id,
        DataExplorerResultController.exampleItem
      );
    });
  });
});
