import {expect} from 'chai';
import {Container, appSetup} from 'ict-api-foundations';
import sinon from 'sinon';
import request from 'supertest';
import {HttpStatusCode} from 'axios';
import {
  DataExplorerRecommendations,
  DataExplorerRecommendationsData,
} from '../../defs/data-explorer-recommendations-def.ts';
import {DataExplorerService} from '../../services/dataexplorer-service.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';

describe('DataExplorerRecommendationController', () => {
  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Equipment Service. */
  let dataExplorerServiceStub: sinon.SinonStubbedInstance<DataExplorerService>;

  beforeEach(() => {
    dataExplorerServiceStub = sinon.createStubInstance(DataExplorerService);
    Container.set(DataExplorerService, dataExplorerServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getDataExplorerRecommendations', () => {
    let mockServiceResponse: DataExplorerRecommendationsData[];
    let mockRecommendationsData: DataExplorerRecommendations;

    beforeEach(() => {
      mockServiceResponse = [
        {
          prompt: 'Hallo?',
        },
      ];
      mockRecommendationsData = {
        recommendations: mockServiceResponse,
      };
      dataExplorerServiceStub.getRecommendations.resolves(mockServiceResponse);
    });

    it('should return the correct data', async () => {
      const response = await request(app).get('/data-explorer/recommendations');
      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal(mockRecommendationsData);
    });

    it('should return an error response when an error is thrown', async () => {
      const theError = new Error('This error should be handled');
      dataExplorerServiceStub.getRecommendations.throws(theError);
      const response = await request(app).get('/data-explorer/recommendations');

      expect(response.body.status).to.equal(HttpStatusCode.InternalServerError);
      expect(response.body.title).to.equal('Internal Server Error');
    });
  });

  describe('getDematicChatGoldQuestions', () => {
    let mockServiceResponse: DataExplorerRecommendationsData[];
    let mockGoldQuestionsData: {
      goldQuestions: DataExplorerRecommendationsData[];
    };

    beforeEach(() => {
      mockServiceResponse = [
        {
          prompt:
            'What are the most efficient pick locations in our warehouse?',
        },
        {
          prompt:
            'How can we optimize our inventory distribution across zones?',
        },
      ];
      mockGoldQuestionsData = {
        goldQuestions: mockServiceResponse,
      };
      dataExplorerServiceStub.getGoldQuestions.resolves(mockServiceResponse);
    });

    it('should return the correct gold questions data', async () => {
      const response = await request(app).get('/data-explorer/gold-questions');
      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal(mockGoldQuestionsData);
    });

    it('should return an error response when an error is thrown', async () => {
      const theError = new Error('Gold questions error should be handled');
      dataExplorerServiceStub.getGoldQuestions.throws(theError);
      const response = await request(app).get('/data-explorer/gold-questions');

      expect(response.body.status).to.equal(HttpStatusCode.InternalServerError);
      expect(response.body.title).to.equal('Internal Server Error');
    });
  });
});
