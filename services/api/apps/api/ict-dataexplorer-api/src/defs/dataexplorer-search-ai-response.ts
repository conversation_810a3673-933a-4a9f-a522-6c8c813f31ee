export interface DataExplorerSearchAIResponse {
  error?: string;
  known_DB?: string;
  response_code: number;
  echart_code?: unknown | null;
  parsed_echart_code?: unknown | null;
  generated_sql_query?: string;
  question?: string;
  sql_query_output: unknown[];
  summary_response?: string;
  debug_data: AIMLResponse;
}

export type DataExplorerGoldQuestionsResponse = string[];

export interface AIMLResponse {
  Error?: string;
  KnownDB?: string;
  ResponseCode: number;
  echart_code?: unknown | null;
  generated_sql_query?: string;
  question?: string;
  sql_query_output?: string;
  summary_response?: string;
}

export interface DataExplorerAgentSearchResponse {
  sessionId: string;
  status: string;
  messages: string[];
}
