import {Recommended} from './dataexplorer-recommended';
import {AIMLResponse} from './dataexplorer-search-ai-response';

export type DataExplorerResult = {
  id: string;
  prompt: string;
  answer?: string;
  isBookmarked?: boolean;
  isRecommended: Recommended;
  queryResults?: unknown[];
  timestamp: Date;
  eChartCode?: unknown | null;
  debugData?: AIMLResponse;
};

export type DataExplorerResults = {
  data: DataExplorerResult[];
};
