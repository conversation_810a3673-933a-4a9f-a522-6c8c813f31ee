import {ContextService, DiService, PostgresDatabase} from 'ict-api-foundations';
import {DataExplorerResultEntity} from 'ict-api-schema';
import {LessThanOrEqual, MoreThanOrEqual} from 'typeorm';
import {DataExplorerSearchAIResponse} from '../defs/dataexplorer-search-ai-response';
import {DataExplorerSearchAIRequest} from '../defs/dataexplorer-search-ai-request';

@DiService()
export class DataExplorerStore {
  public static readonly type: string = 'data-explorer-store';

  constructor(private context: ContextService) {}

  get postgres(): PostgresDatabase {
    return this.context.dbProvider.dataExplorerPostgres;
  }

  async getResults(
    startDateTime: Date,
    endDateTime: Date,
    limit?: number,
    bookmark?: boolean
  ): Promise<DataExplorerResultEntity[]> {
    const resultRepo = this.postgres.datasource.getRepository(
      DataExplorerResultEntity
    );

    return await resultRepo.find({
      where: {
        userId: this.context.userId,
        createdAt: MoreThanOrEqual(startDateTime),
        updatedAt: LessThanOrEqual(endDateTime),
        isBookmarked: bookmark,
      },
      order: {
        updatedAt: 'DESC',
      },
      take: limit,
    });
  }

  async getResult(
    resultId: string
  ): Promise<DataExplorerResultEntity | undefined> {
    const resultRepo = this.postgres.datasource.getRepository(
      DataExplorerResultEntity
    );

    const result = await resultRepo.findOneBy({
      id: resultId,
      userId: this.context.userId,
    });

    if (!result) {
      return undefined;
    }

    return result;
  }

  async deleteResult(resultId: string) {
    const resultRepo = this.postgres.datasource.getRepository(
      DataExplorerResultEntity
    );

    await resultRepo.delete({id: resultId, userId: this.context.userId});
  }

  async saveResult(existing: DataExplorerResultEntity) {
    const resultRepo = this.postgres.datasource.getRepository(
      DataExplorerResultEntity
    );

    return await resultRepo.save(existing);
  }

  async saveSearchResult(
    request: DataExplorerSearchAIRequest,
    response: DataExplorerSearchAIResponse
  ) {
    const resultRepo = this.postgres.datasource.getRepository(
      DataExplorerResultEntity
    );

    const entity = resultRepo.create({
      userId: this.context.userId,
      question: response.question || request.user_question,
      answer: response.summary_response,
      jsonOutput: response.sql_query_output,
      isBookmarked: false,
      eChartCode: response.parsed_echart_code,
      debugData: response.debug_data,
    });

    return await resultRepo.save(entity);
  }

  public getType(): string {
    return DataExplorerStore.type;
  }
}
