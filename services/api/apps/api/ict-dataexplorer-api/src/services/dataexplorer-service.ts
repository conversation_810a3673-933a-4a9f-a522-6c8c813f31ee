import {
  ConfigS<PERSON>,
  Container,
  ContextService,
  DiService,
  EnvironmentService,
  IctError,
  SecurityRoles,
  WinstonLogger,
} from 'ict-api-foundations';
import {DataExplorerResultEntity} from 'ict-api-schema';
import {DataExplorerRecommendationsData} from '../defs/data-explorer-recommendations-def.ts';
import {
  DataExplorerResult,
  DataExplorerResults,
} from '../defs/dataexplorer-search-result.ts';
import {Recommended} from '../defs/dataexplorer-recommended.ts';
import {DataExplorerSearchAIRequest} from '../defs/dataexplorer-search-ai-request.ts';
import {DataExplorerStore} from '../stores/dataexplorer-store.ts';
import {AIService} from './ai-service.ts';
import {DataExplorerAgentSearchResponse} from '../defs/dataexplorer-search-ai-response.ts';

@DiService()
export class DataExplorerService {
  private logger: WinstonLogger;

  constructor(
    private dataExplorerStore: DataExplorerStore,
    private aiService: AIService,
    private contextService: ContextService,
    private envService: EnvironmentService,
    private configStore: ConfigStore,
  ) {
    this.logger = Container.get(WinstonLogger);
    this.logger.info('DataExplorerService initialized');
  }

  async getRecommendations(): Promise<DataExplorerRecommendationsData[]> {
    const goldQuestions = await this.aiService.getRecommendations();
    if (goldQuestions.length > 0) {
      return goldQuestions.map(q => ({
        prompt: q,
      }));
    }
    // Keep hard-coded questions as a fallback
    return [
      {
        prompt:
          'What are the top 2 locations having load units with maximum inventory?',
      },
      {
        prompt: 'What are the top 5 busiest locations in the last 12 months?',
      },
      {
        prompt:
          'Give me a monthly breakdown of the number of completed pick orders for last 3 months.',
      },
      {
        prompt: 'How many orders were picked yesterday?',
      },
    ];
  }

  async getGoldQuestions(): Promise<DataExplorerRecommendationsData[]> {
    const goldQuestions = await this.aiService.getGoldQuestions();
    if (goldQuestions.length > 0) {
      return goldQuestions.map(q => ({
        prompt: q,
      }));
    }
    // Keep hard-coded gold questions as a fallback
    return [
      {
        prompt: 'What are the most efficient pick locations in our warehouse?',
      },
      {
        prompt: 'How can we optimize our inventory distribution across zones?',
      },
      {
        prompt: 'What patterns do you see in our peak operational hours?',
      },
      {
        prompt: 'Which equipment shows the highest utilization rates?',
      },
    ];
  }

  async search(searchText: string): Promise<DataExplorerResult> {
    const request: DataExplorerSearchAIRequest = {
      user_question: searchText,
      user_database: `${this.contextService.datasetId}`,
    };

    this.logger.info('Querying AIML', request);

    const response = await this.aiService.search(request);

    this.logger.info('AIML Response', {
      response_code: response.response_code,
      summary_response: response.summary_response,
      error: response.error,
      known_DB: response.known_DB,
      has_echart_code: !!response.echart_code,
      has_sql_output: !!response.sql_query_output,
    });
    if (
      !response.summary_response ||
      response.response_code !== 200 ||
      response.error
    ) {
      let errorMessage: string | undefined;
      if (response.error) {
        try {
          const errorObject = JSON.parse(response.error);
          errorMessage = `${errorObject.error_classification}: ${errorObject.error_description}`;
        } catch (e) {
          errorMessage = response.error.trim();
        }
      }

      throw IctError.noContent(errorMessage);
    }

    const savedResponse = await this.dataExplorerStore.saveSearchResult(
      request,
      response,
    );

    const result = DataExplorerService.toDataExplorerResult(savedResponse);

    const dataExplorerDebugDataConfig =
      await this.configStore.findMostRelevantSettingForUser(
        this.contextService.userId,
        undefined,
        'data-explorer-debug-data',
      );

    if (
      dataExplorerDebugDataConfig?.value &&
      this.contextService.userRoles.includes(SecurityRoles.CT_ENGINEERS)
    ) {
      result.debugData = response.debug_data;
    }

    return result;
  }

  async getAgentSearch(
    searchText: string,
    userSessionId?: string,
  ): Promise<DataExplorerAgentSearchResponse> {
    const request: DataExplorerSearchAIRequest = {
      user_question: searchText,
      user_database: `${this.contextService.datasetId}`,
      userSessionId,
    };

    this.logger.info('Querying AIML Agent', request);
    const data = await this.aiService.getAgentSearch(request);
    return data || [];
  }

  public async getResults(
    startDate: Date,
    endDate: Date,
    limit: number,
    bookmark?: boolean,
  ): Promise<DataExplorerResults> {
    const results = await this.dataExplorerStore.getResults(
      startDate,
      endDate,
      limit,
      bookmark,
    );

    return {data: results.map(DataExplorerService.toDataExplorerResult)};
  }

  async getResult(id: string): Promise<DataExplorerResult | undefined> {
    const result = await this.dataExplorerStore.getResult(id);
    if (result) {
      return DataExplorerService.toDataExplorerResult(result);
    }
    return undefined;
  }

  async deleteResult(id: string): Promise<void> {
    // .getResult only takes an id parameter but uses the context user's id as part of the query
    const result = await this.dataExplorerStore.getResult(id);
    if (result === undefined) {
      throw IctError.notFound();
    }

    await this.dataExplorerStore.deleteResult(id);
  }

  async updateResult(
    id: string,
    result: DataExplorerResult,
  ): Promise<DataExplorerResult> {
    const resultId = result.id ?? id;

    if (!resultId) throw IctError.notFound();

    const existing = await this.dataExplorerStore.getResult(resultId);
    if (existing === undefined) {
      throw IctError.notFound();
    }

    existing.isBookmarked = !!result.isBookmarked;
    existing.isRecommended = DataExplorerService.toRecommended(
      result.isRecommended,
    );
    existing.updatedAt = new Date();

    const saved = await this.dataExplorerStore.saveResult(existing);

    return DataExplorerService.toDataExplorerResult(saved);
  }

  static toDataExplorerResult(
    result: DataExplorerResultEntity,
  ): DataExplorerResult {
    const recommend: Recommended = DataExplorerService.getRecommendation(
      result.isRecommended,
    );

    return {
      id: result.id,
      prompt: result.question,
      answer: result.answer,
      isBookmarked: result.isBookmarked,
      isRecommended: recommend,
      queryResults: result.jsonOutput,
      timestamp: result.updatedAt,
      eChartCode: result.eChartCode,
    };
  }

  static toRecommended(recommeded: Recommended): boolean | null {
    switch (recommeded) {
      case Recommended.isRecommended:
        return true;
      case Recommended.notRecommended:
        return false;
      case Recommended.notProvided:
      default:
        return null;
    }
  }

  static getRecommendation(flag: boolean | null): Recommended {
    switch (flag) {
      case true:
        return Recommended.isRecommended;
      case false:
        return Recommended.notRecommended;
      default:
        return Recommended.notProvided;
    }
  }
}
