import {
  Container,
  DatabaseTypes,
  IctError,
  PostgresDatabaseOptions,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Body,
  Delete,
  Example,
  Get,
  Middlewares,
  Path,
  Put,
  Query,
  Response,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {EntityTypes} from 'ict-api-schema';
import {DataExplorerService} from '../services/dataexplorer-service.ts';
import type {
  DataExplorerResult,
  DataExplorerResults,
} from '../defs/dataexplorer-search-result.ts';
import {Recommended} from '../defs/dataexplorer-recommended.ts';

const dbOptions: PostgresDatabaseOptions = {
  entityType: EntityTypes.DataExplorer,
};

@Route('/data-explorer')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [
      {
        type: DatabaseTypes.Postgres,
        options: dbOptions,
      },
    ],
  }),
])
export class DataExplorerResultController {
  static readonly exampleItem: DataExplorerResult = {
    answer: 'answer',
    id: '2ef6813b-250b-4e0d-8622-cb7fe8ad88c8',
    prompt: 'prompt',
    queryResults: [],
    timestamp: new Date('2024-01-01T00:00:00.000Z'),
    isRecommended: Recommended.notProvided,
    eChartCode: null,
  };

  static readonly exampleItemResponse: DataExplorerResult =
    DataExplorerResultController.exampleItem;

  static readonly exampleResponse: DataExplorerResults = {
    data: [DataExplorerResultController.exampleItem],
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @param {number} limit
   * @param {boolean} bookmark
   * @isDateTime start_date
   * @isDateTime end_date
   * @isInt limit
   * @returns {Promise<DataExplorerResult>} contract
   * @throws IctError
   */
  @Example<DataExplorerResults>(DataExplorerResultController.exampleResponse)
  @SuccessResponse('200')
  @Get('/results')
  @OperationId('GetDataExplorerResults')
  @Tags('data-explorer')
  public async getResults(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
    @Query() limit: number = 10,
    @Query() bookmark?: boolean
  ): Promise<DataExplorerResults> {
    const dataExplorerService: DataExplorerService =
      Container.get(DataExplorerService);

    if (startDate.getTime() >= endDate.getTime()) {
      throw IctError.unprocessableRequest('start_date must be before end_date');
    }

    return await dataExplorerService.getResults(
      startDate,
      endDate,
      limit,
      bookmark
    );
  }

  /**
   * @param {string} resultId
   * @pattern resultId ^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$ Result UUID Required
   * @returns {Promise<DataExplorerResult>[]} contract
   * @throws IctError
   */
  @Example<DataExplorerResult>(DataExplorerResultController.exampleItemResponse)
  @SuccessResponse('200')
  @Get('/results/{resultId}')
  @OperationId('GetDataExplorerResult')
  @Tags('data-explorer')
  public async getResultById(
    @Path() resultId: string
  ): Promise<DataExplorerResult> {
    const dataExplorerService = Container.get(DataExplorerService);
    const result = await dataExplorerService.getResult(resultId);
    if (!result) {
      throw IctError.notFound(`No record available for ${resultId}`);
    }

    return result;
  }

  /**
   * @throws IctError
   */
  @Response(
    '404',
    'Result does not exist or user does not have permission to specified result'
  )
  @SuccessResponse('200', 'Result had been removed from the database')
  @Delete('/results/{resultId}')
  @OperationId('DeleteDataExplorerResult')
  @Tags('data-explorer')
  public async deleteResultById(@Path() resultId: string): Promise<void> {
    const dataExplorerService = Container.get(DataExplorerService);
    await dataExplorerService.deleteResult(resultId);
  }

  /**
   *
   * @returns {Promise<DataExplorerResult>} contract
   * @throws IctError
   */
  @Example<DataExplorerResult>(DataExplorerResultController.exampleItemResponse)
  @Response(
    '404',
    'Result does not exist or user does not have permission to specified result'
  )
  @SuccessResponse('200')
  @Put('/results/{resultId}')
  @OperationId('PutDataExplorerResult')
  @Tags('data-explorer')
  public async updateResult(
    @Path() resultId: string,
    @Body() dataExplorerResult: DataExplorerResult
  ): Promise<DataExplorerResult> {
    const dataExplorerService = Container.get(DataExplorerService);

    return await dataExplorerService.updateResult(resultId, dataExplorerResult);
  }
}
