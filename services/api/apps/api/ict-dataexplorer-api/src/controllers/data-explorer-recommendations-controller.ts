import {
  Container,
  DatabaseTypes,
  PostgresDatabaseOptions,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {EntityTypes} from 'ict-api-schema';
import {
  Example,
  Get,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {
  DataExplorerRecommendations,
  DataExplorerRecommendationsData,
} from '../defs/data-explorer-recommendations-def.ts';
import {DataExplorerService} from '../services/dataexplorer-service.ts';

const dbOptions: PostgresDatabaseOptions = {
  entityType: EntityTypes.DataExplorer,
};

@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [
      {
        type: DatabaseTypes.Postgres,
        options: dbOptions,
      },
    ],
  }),
])
@Route('/data-explorer')
export class DataExplorerRecommendationsController {
  @Example<DataExplorerRecommendationsData[]>([
    {prompt: "What's a good recommendation?"},
  ])
  @SuccessResponse('200')
  @Get('/recommendations')
  @OperationId('GetDataExplorerRecommendations')
  @Tags('data-explorer')
  public async getDataExplorerRecommendations(): Promise<DataExplorerRecommendations> {
    const dataExplorerService = Container.get(DataExplorerService);
    const recommendations = await dataExplorerService.getRecommendations();
    return {recommendations};
  }

  @Example<DataExplorerRecommendationsData[]>([
    {prompt: "What's a good gold question?"},
  ])
  @SuccessResponse('200')
  @Get('/gold-questions')
  @OperationId('GetDematicChatGoldQuestions')
  @Tags('data-explorer')
  public async getDematicChatGoldQuestions(): Promise<{
    goldQuestions: DataExplorerRecommendationsData[];
  }> {
    const dataExplorerService = Container.get(DataExplorerService);
    const goldQuestions = await dataExplorerService.getGoldQuestions();
    return {goldQuestions};
  }
}
