import {
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  PostgresDatabaseOptions,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {EntityTypes} from 'ict-api-schema';
import {
  Example,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Get,
} from 'tsoa';
import {DataExplorerService} from '../services/dataexplorer-service.ts';
import {DataExplorerAgentSearchResponse} from '../defs/dataexplorer-search-ai-response.ts';

const dbOptions: PostgresDatabaseOptions = {
  entityType: EntityTypes.DataExplorer,
};

@Route('/data-explorer')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [
      {
        type: DatabaseTypes.Postgres,
        options: dbOptions,
      },
      configPostgresDatabase(),
    ],
  }),
])
export class DataExplorerAgentController {
  static readonly exampleAgentSearchResponse: DataExplorerAgentSearchResponse =
    {
      sessionId: 'example-session-id-agent-123',
      status: 'COMPLETED',
      messages: ['Agent search completed successfully.'],
    };
  /**
   * Retrieves an example data explorer result.
   * @param {String} exampleParam An example parameter for the request.
   * @returns {Promise<DataExplorerResult>} An example data explorer result.
   * @throws IctError
   */
  @Example<DataExplorerAgentSearchResponse>(
    DataExplorerAgentController.exampleAgentSearchResponse
  )
  @SuccessResponse('200', 'Successfully retrieved example data')
  @Get('/agentsearch')
  @OperationId('GetDataExplorerAgentSearch')
  @Tags('data-explorer')
  public async getDataExplorerAgentSearch(
    @Query() searchText: string,
    @Query() sessionId?: string
  ): Promise<DataExplorerAgentSearchResponse> {
    const dataExplorerService = Container.get(DataExplorerService);
    const response = await dataExplorerService.getAgentSearch(
      searchText,
      sessionId
    );
    return response;
  }
}
