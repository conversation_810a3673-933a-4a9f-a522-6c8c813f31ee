import {
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  PostgresDatabaseOptions,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {EntityTypes} from 'ict-api-schema';
import {
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {DataExplorerService} from '../services/dataexplorer-service.ts';
import {DataExplorerResult} from '../defs/dataexplorer-search-result.ts';
import {Recommended} from '../defs/dataexplorer-recommended.ts';

const dbOptions: PostgresDatabaseOptions = {
  entityType: EntityTypes.DataExplorer,
};

@Route('/data-explorer')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [
      {
        type: DatabaseTypes.Postgres,
        options: dbOptions,
      },
      configPostgresDatabase(),
    ],
  }),
])
export class DataExplorerSearchController {
  static readonly exampleData: DataExplorerResult = {
    answer: 'answer',
    id: 'id',
    prompt: 'prompt',
    queryResults: [],
    timestamp: new Date('2024-01-01T00:00:00.000Z'),
    isRecommended: Recommended.notProvided,
    eChartCode: null,
  };
  static readonly exampleResponse: DataExplorerResult =
    DataExplorerSearchController.exampleData;
  /**
   * @param {String} searchText
   * @returns {Promise<DataExplorerResult>} contract
   * @throws IctError
   */
  @Example<DataExplorerResult>(DataExplorerSearchController.exampleResponse)
  @SuccessResponse('200')
  @Get('/search')
  @OperationId('GetDataExplorerSearch')
  @Tags('data-explorer')
  public async getDataExplorerSearch(
    @Query() searchText: string
  ): Promise<DataExplorerResult> {
    const dataExplorerService = Container.get(DataExplorerService);
    const response = await dataExplorerService.search(searchText);
    return response;
  }
}
