import {BaseHealthCheck} from 'ict-api-foundations';
import {Get, Route, OperationId, Tags} from 'tsoa';

@Route('/data-explorer/healthcheck')
export class HealthCheckController {
  @Get()
  @OperationId('GetDataExplorerBasicHealthCheck')
  @Tags('data-explorer')
  public getDataExplorerBasicHealthCheck() {
    return BaseHealthCheck.getBasicHealthCheck();
  }

  @Get('/full')
  @OperationId('GetDataExplorerFullHealthCheck')
  @Tags('data-explorer')
  public getDataExplorerFullHealthCheck() {
    return BaseHealthCheck.getDeepHealthCheck(['auth0', 'bigQuery', 'redis']);
  }
}
