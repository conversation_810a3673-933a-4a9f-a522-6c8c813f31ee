{"name": "ict-dataexplorer-api", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/ict-dataexplorer-api/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "ict-dataexplorer-api:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "ict-dataexplorer-api:build:development"}, "production": {"buildTarget": "ict-dataexplorer-api:build:production"}}}}}