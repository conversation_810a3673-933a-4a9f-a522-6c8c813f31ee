{"name": "ict-workstation-api", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/ict-workstation-api/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "ict-workstation-api:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "ict-workstation-api:build:development"}, "production": {"buildTarget": "ict-workstation-api:build:production"}}}}}