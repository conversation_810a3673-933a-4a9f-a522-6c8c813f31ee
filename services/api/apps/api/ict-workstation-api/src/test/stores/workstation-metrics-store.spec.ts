import sinon, {SinonStub} from 'sinon';
import {
  BigQueryDatabase,
  ConfigStore,
  ContextService,
  DatabaseProvider,
  IctError,
  WinstonLogger,
} from 'ict-api-foundations';
import {expect, use} from 'chai';
import chaiAsPromised from 'chai-as-promised';
import {BigQuery} from '@google-cloud/bigquery';
import {AppConfigSetting} from '@ict/sdk-foundations/types';
import {WorkstationMetricsStore} from '../../stores/workstation-metrics-store.ts';
import {WorkstationStatsQueryResponse} from '../../defs/workstation-stats.ts';
import {WorkstationMetricsController} from '../../controllers/workstation-metrics-controller.ts';

describe('WorkstationMetricsStore', () => {
  use(chaiAsPromised);
  let workstationMetricsStore: WorkstationMetricsStore;
  let clientStub: sinon.SinonStubbedInstance<BigQuery>;
  let dbProvider: DatabaseProvider;
  let contextService: ContextService;
  let configStore: ConfigStore;
  let mockLogger: sinon.SinonStubbedInstance<WinstonLogger>;
  let executeMonitoredJobStub: SinonStub;
  const exampleWorkstationSetting: AppConfigSetting = {
    id: 'test',
    name: 'excluded-workstation-list',
    dataType: 'json',
    group: null,
    value: {
      workstations: [
        '10000000002',
        '10000000003',
        '10000000004',
        '10000000006',
        '10000000007',
      ],
    },
  };

  beforeEach(() => {
    clientStub = sinon.createStubInstance(BigQuery);
    dbProvider = new DatabaseProvider();
    const bqDatabase = new BigQueryDatabase(clientStub, 'testDataset');
    dbProvider.set(bqDatabase.getType(), bqDatabase);

    contextService = new ContextService();
    sinon.stub(contextService, 'dbProvider').get(() => dbProvider);
    executeMonitoredJobStub = sinon.stub(
      dbProvider.bigQuery,
      'executeMonitoredJob',
    );
    configStore = new ConfigStore(contextService);

    mockLogger = sinon.createStubInstance(WinstonLogger);
    mockLogger.info.resolves();

    workstationMetricsStore = new WorkstationMetricsStore(
      contextService,
      mockLogger,
      configStore,
    );
  });

  describe('getWorkstationStats', () => {
    const startDate = '2021-11-30T00:00:01';
    const endDate = '2021-11-30T23:59:59';
    let settingStub: sinon.SinonStub;

    beforeEach(() => {
      settingStub = sinon.stub(configStore, 'findMostRelevantSettingForUser');
      settingStub.resolves(exampleWorkstationSetting);
    });

    afterEach(() => {
      settingStub.restore();
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        workstationMetricsStore.getWorkstationStats(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should return the expected rows of workstation stats', async () => {
      const exampleQueryResponse: WorkstationStatsQueryResponse = {
        totalWorkstations: 10,
        workstationMode: [],
        active: 10,
      };
      executeMonitoredJobStub.resolves([[exampleQueryResponse]]);

      const outcome = await workstationMetricsStore.getWorkstationStats(
        startDate,
        endDate,
      );
      expect(outcome).to.deep.eq(exampleQueryResponse);
      expect(settingStub.calledOnce).to.be.true;
    });
  });

  describe('getWorkstationOperatorStats', () => {
    let settingStub: sinon.SinonStub;

    beforeEach(() => {
      settingStub = sinon.stub(configStore, 'findMostRelevantSettingForUser');
      settingStub.resolves(exampleWorkstationSetting);
    });

    afterEach(() => {
      settingStub.restore();
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        workstationMetricsStore.getWorkstationOperatorStats(),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should return the expected workstation operator stats', async () => {
      executeMonitoredJobStub.resolves([
        [
          WorkstationMetricsController.exampleWorkstationOperatorSummaryResponse,
        ],
      ]);

      const outcome =
        await workstationMetricsStore.getWorkstationOperatorStats();
      expect(outcome).to.deep.eq(
        WorkstationMetricsController.exampleWorkstationOperatorSummaryResponse,
      );
      expect(settingStub.calledOnce).to.be.true;
    });
  });

  describe('getWorkstationPerformanceStats', () => {
    const startDate = '2021-11-30T00:00:01';
    const endDate = '2021-11-30T23:59:59';
    let settingStub: sinon.SinonStub;

    beforeEach(() => {
      settingStub = sinon.stub(configStore, 'findMostRelevantSettingForUser');
      settingStub.resolves(exampleWorkstationSetting);
    });

    afterEach(() => {
      settingStub.restore();
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        workstationMetricsStore.getWorkstationPerformanceStats(
          startDate,
          endDate,
        ),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should return the expected workstation performance stats', async () => {
      executeMonitoredJobStub.resolves([
        [
          WorkstationMetricsController.exampleWorkstationPerformanceSummaryResponse,
        ],
      ]);

      const outcome =
        await workstationMetricsStore.getWorkstationPerformanceStats(
          startDate,
          endDate,
        );
      expect(outcome).to.deep.eq(
        WorkstationMetricsController.exampleWorkstationPerformanceSummaryResponse,
      );
      expect(settingStub.calledOnce).to.be.true;
    });
  });

  describe('getWorkstationHealthStats', () => {
    const startDate = '2021-11-30T00:00:01';
    const endDate = '2021-11-30T23:59:59';
    let settingStub: sinon.SinonStub;

    beforeEach(() => {
      settingStub = sinon.stub(configStore, 'findMostRelevantSettingForUser');
      settingStub.resolves(exampleWorkstationSetting);
    });

    afterEach(() => {
      settingStub.restore();
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        workstationMetricsStore.getWorkstationHealthStats(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should return the expected workstation health stats', async () => {
      executeMonitoredJobStub.resolves([
        [WorkstationMetricsController.exampleWorkstationHealthSummaryResponse],
      ]);

      const outcome = await workstationMetricsStore.getWorkstationHealthStats(
        startDate,
        endDate,
      );
      expect(outcome).to.deep.eq(
        WorkstationMetricsController.exampleWorkstationHealthSummaryResponse,
      );
      expect(settingStub.calledOnce).to.be.true;
    });
  });
});
