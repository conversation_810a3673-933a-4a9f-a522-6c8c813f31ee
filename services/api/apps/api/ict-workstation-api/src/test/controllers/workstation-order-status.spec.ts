import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';

import {RegisterRoutes} from '../../../build/routes.ts';
import {WorkstationOrdersStatusController} from '../../controllers/workstation-orders-status-controller.ts';
import {WorkstationService} from '../../services/workstation-service.ts';

describe(WorkstationOrdersStatusController.name, () => {
  const baseUrl = '/workstation';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the WorkstationService. */
  let workstationServiceStub: sinon.SinonStubbedInstance<WorkstationService>;

  const startDate = new Date('2023-01-01T00:00:00.000Z');
  const endDate = new Date('2023-01-02T00:00:00.000Z');

  beforeEach(() => {
    workstationServiceStub = sinon.createStubInstance(WorkstationService);
    Container.set(WorkstationService, workstationServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('Workstation Order Status Controller Error scenarios', () => {
    it('should error when the service throws an error - getWorkstationOrderStatus', async () => {
      const url = `${baseUrl}/orders/status`;
      workstationServiceStub.getWorkstationOrdersStatus.rejects(
        new Error('Something bad happened'),
      );

      const response = await request(app).get(url).query({
        start_date: startDate,
        end_date: endDate,
      });

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
    });
  });

  describe('Workstation Order Status Controller success scenarios', () => {
    it('should call "WorkstationService.getWorkstationOrderStatus" and return the data', async () => {
      const url = `${baseUrl}/orders/status`;
      const expectedData = {
        activeOrders: 23,
        delayedOrders: 2,
      };
      workstationServiceStub.getWorkstationOrdersStatus.resolves(expectedData);

      const response = await request(app).get(url).query({
        start_date: startDate,
        end_date: endDate,
      });

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(expectedData);
    });
  });
});
