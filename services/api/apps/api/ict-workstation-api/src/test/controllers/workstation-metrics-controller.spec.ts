import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';

// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {WorkstationMetricsService} from '../../services/workstation-metrics-service.ts';
import {WorkstationMetricsController} from '../../controllers/workstation-metrics-controller.ts';

describe('WorkstationMetricsController', () => {
  const baseUrl: string = '/workstation/metrics';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the WorkstationMetricsService. */
  let workstationMetricsServiceStub: sinon.SinonStubbedInstance<WorkstationMetricsService>;

  const startDate = new Date('2023-01-01T00:00:00.000Z');
  const endDate = new Date('2023-01-02T00:00:00.000Z');

  beforeEach(() => {
    workstationMetricsServiceStub = sinon.createStubInstance(
      WorkstationMetricsService,
    );
    Container.set(WorkstationMetricsService, workstationMetricsServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('Workstation Metrics Controller Error scenarios', () => {
    it('should error when the service throws an error - getStationSummary', async () => {
      const url = `${baseUrl}/summary`;
      workstationMetricsServiceStub.getStationSummary.rejects(
        new Error('Something bad happened'),
      );

      const response = await request(app).get(url).query({
        start_date: startDate,
        end_date: endDate,
      });

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
    });

    it('should error when the service throws an error - getOperatorSummary', async () => {
      const url = `${baseUrl}/operators`;
      workstationMetricsServiceStub.getOperatorSummary.rejects(
        new Error('Something bad happened'),
      );

      const response = await request(app).get(url);
      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
    });

    it('should error when the service throws an error - getPerformanceSummary', async () => {
      const url = `${baseUrl}/performance`;
      workstationMetricsServiceStub.getPerformanceSummary.rejects(
        new Error('Something bad happened'),
      );

      const response = await request(app).get(url).query({
        start_date: startDate,
        end_date: endDate,
      });
      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
    });

    it('should error when the service throws an error - getHealthSummary', async () => {
      const url = `${baseUrl}/health`;
      workstationMetricsServiceStub.getHealthSummary.rejects(
        new Error('Something bad happened'),
      );

      const response = await request(app).get(url).query({
        start_date: startDate,
        end_date: endDate,
      });
      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
    });
  });

  describe('Workstation Metrics Controller success scenarios', () => {
    it('should call "WorkstationMetricsService.getStationSummary" and return the data', async () => {
      const url = `${baseUrl}/summary`;
      workstationMetricsServiceStub.getStationSummary.resolves(
        WorkstationMetricsService.exampleWorkstationStatsResponse,
      );

      const response = await request(app).get(url).query({
        start_date: startDate,
        end_date: endDate,
      });

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        JSON.parse(
          JSON.stringify(
            WorkstationMetricsController.exampleWorkstationSummaryResponse,
          ),
        ),
      );
    });

    it('should call "WorkstationMetricsService.getOperatorSummary" and return the data', async () => {
      const url = `${baseUrl}/operators`;
      workstationMetricsServiceStub.getOperatorSummary.resolves(
        WorkstationMetricsService.exampleWorkstationOperatorStatsResponse,
      );

      const response = await request(app).get(url);
      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        JSON.parse(
          JSON.stringify(
            WorkstationMetricsController.exampleWorkstationOperatorSummaryResponse,
          ),
        ),
      );
    });

    it('should call "WorkstationMetricsService.getPerformanceSummary" and return the data', async () => {
      const url = `${baseUrl}/performance`;
      workstationMetricsServiceStub.getPerformanceSummary.resolves(
        WorkstationMetricsService.exampleWorkstationPerformanceStatsResponse,
      );

      const response = await request(app).get(url).query({
        start_date: startDate,
        end_date: endDate,
      });
      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        JSON.parse(
          JSON.stringify(
            WorkstationMetricsController.exampleWorkstationPerformanceSummaryResponse,
          ),
        ),
      );
    });

    it('should call "WorkstationMetricsService.getHealthSummary" and return the data', async () => {
      const url = `${baseUrl}/health`;
      workstationMetricsServiceStub.getHealthSummary.resolves(
        WorkstationMetricsService.exampleWorkstationHealthStatsResponse,
      );

      const response = await request(app).get(url).query({
        start_date: startDate,
        end_date: endDate,
      });
      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        JSON.parse(
          JSON.stringify(
            WorkstationMetricsController.exampleWorkstationHealthSummaryResponse,
          ),
        ),
      );
    });
  });
});
