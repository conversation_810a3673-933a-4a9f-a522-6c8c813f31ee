import sinon from 'sinon';
import request from 'supertest';
import {appSetup, Container} from 'ict-api-foundations';
import {expect} from 'chai';

import {RegisterRoutes} from '../../../build/routes.ts';
import {WorkstationContainersListController} from '../../controllers/workstation-containers-list-controller.ts';
import {WorkstationService} from '../../services/workstation-service.ts';

describe(WorkstationContainersListController.name, () => {
  const baseUrl = '/workstation';

  const app = appSetup(RegisterRoutes);

  let workstationServiceStub: sinon.SinonStubbedInstance<WorkstationService>;

  const startDate = new Date('2023-01-01T00:00:00.000Z');
  const endDate = new Date('2023-01-02T00:00:00.000Z');

  beforeEach(() => {
    workstationServiceStub = sinon.createStubInstance(WorkstationService);
    Container.set(WorkstationService, workstationServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  it('should return 422 and errors when missing required query parameters', async () => {
    const url = `${baseUrl}/containers/list`;
    const response = await request(app).post(url).send({
      page: 0,
      limit: 50,
    });
    expect(response.status).to.equal(422);
    expect(response.headers['content-type']).to.match(/json/);
    expect(response.body.message).to.equal('Validation Failed');
    expect(response.body.details).to.have.all.keys(
      'start_date',
      'end_date',
      'size',
      'style',
      'zone',
    );
    expect(response.body.details.start_date.message).to.equal(
      "'start_date' is required",
    );
    expect(response.body.details.end_date.message).to.equal(
      "'end_date' is required",
    );
    expect(response.body.details.size.message).to.equal("'size' is required");
    expect(response.body.details.style.message).to.equal("'style' is required");
    expect(response.body.details.zone.message).to.equal("'zone' is required");
  });

  it('should error when the service throws an error', async () => {
    const url = `${baseUrl}/containers/list`;
    workstationServiceStub.getWorkstationContainersList.rejects(
      new Error('Test error'),
    );
    const response = await request(app)
      .post(url)
      .query({
        start_date: startDate,
        end_date: endDate,
        size: 'L',
        style: 'style',
        zone: 'ASRS',
      })
      .send({
        page: 0,
        limit: 50,
      });

    expect(response.status).to.equal(500);
    expect(response.headers['content-type']).to.match(/json/);
    expect(response.body.status).to.equal(500);
    expect(response.body.title).to.equal('Internal Server Error');
  });

  it('should call "WorkstationService.getWorkstationContainersList" and return the data', async () => {
    const url = `${baseUrl}/containers/list`;
    const expectedData = {
      data: [
        {
          container: 'container',
          status: 'status',
          transport: '--',
          quantity: 5,
          lastLocation: 'lastLocation',
          eventTime: '2023-01-01T00:00:00.000',
        },
      ],
      metadata: {
        page: 0,
        limit: 50,
        totalResults: 1,
      },
    };
    workstationServiceStub.getWorkstationContainersList.resolves(expectedData);

    const response = await request(app)
      .post(url)
      .query({
        start_date: startDate,
        end_date: endDate,
        size: 'L',
        style: 'style',
        zone: 'ASRS',
      })
      .send({
        page: 0,
        limit: 50,
      });

    expect(response.status).to.equal(200);
    expect(response.headers['content-type']).to.match(/json/);
    expect(response.body).to.deep.equal(expectedData);
  });
});
