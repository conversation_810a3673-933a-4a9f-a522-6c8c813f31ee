import sinon from 'sinon';
import request from 'supertest';
import {appSetup, Container, ExportFormatter} from 'ict-api-foundations';
import {expect} from 'chai';

import {RegisterRoutes} from '../../../build/routes.ts';
import {WorkstationOrdersListController} from '../../controllers/workstation-orders-list-controller.ts';
import {WorkstationService} from '../../services/workstation-service.ts';

describe(WorkstationOrdersListController.name, () => {
  const baseUrl = '/workstation/orders/list';

  const app = appSetup(RegisterRoutes);

  let workstationServiceStub: sinon.SinonStubbedInstance<WorkstationService>;

  const startDate = new Date('2023-01-01T00:00:00.000Z');
  const endDate = new Date('2023-01-02T00:00:00.000Z');

  beforeEach(() => {
    workstationServiceStub = sinon.createStubInstance(WorkstationService);
    Container.set(WorkstationService, workstationServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getWorkstationOrdersListExport', () => {
    const exportUrl = `${baseUrl}/export`;

    it('should error when the service throws an error', async () => {
      workstationServiceStub.getWorkstationOrdersList.rejects(
        new Error('Test error'),
      );

      const response = await request(app)
        .post(exportUrl)
        .query({
          start_date: startDate,
          end_date: endDate,
        })
        .send({
          page: 0,
          limit: 50,
          sortFields: [
            {
              columnName: 'dwell_time',
              isDescending: 'true',
            },
          ],
          columns: {
            station: true,
            position: true,
            dwellTime: true,
            orderStatus: true,
            pickTask: true,
            orderId: true,
            container: true,
            completedPicks: true,
            arrivalTime: true,
          },
        });

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
    });

    it('should return an excel file', async () => {
      workstationServiceStub.getWorkstationOrdersList.resolves({
        data: [
          {
            station: 'Station 1',
            position: 'Position 1',
            dwellTime: 10,
            orderStatus: 'Active',
            pickTask: 'Pick Task 1',
            orderId: 'Order 1',
            container: 'Container 1',
            completedPicks: 2,
            totalPicks: 3,
            arrivalTime: '2023-01-01T00:00:00.000Z',
          },
        ],
        metadata: {
          page: 0,
          limit: 50,
          totalResults: 1,
        },
      });

      const mapResponseToColumnsStub = sinon.stub(
        ExportFormatter,
        'mapResponseToColumns',
      );
      const generateExcelFileStub = sinon.stub(
        ExportFormatter,
        'generateExcelFile',
      );

      const response = await request(app)
        .post(exportUrl)
        .query({
          start_date: startDate,
          end_date: endDate,
        })
        .send({
          page: 0,
          limit: 50,
          sortFields: [
            {
              columnName: 'dwell_time',
              isDescending: 'true',
            },
          ],
          columns: {
            station: true,
            position: true,
            dwellTime: true,
            orderStatus: true,
            pickTask: true,
            orderId: true,
            container: true,
            completedPicks: true,
            arrivalTime: true,
          },
        });

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/sheet/);
      expect(response.headers['content-disposition']).to.match(/.xlsx/);
      expect(mapResponseToColumnsStub.calledOnce).to.be.true; // Ensure the stub was called
      expect(generateExcelFileStub.calledOnce).to.be.true; // Ensure the stub was called

      mapResponseToColumnsStub.restore();
      generateExcelFileStub.restore();
    });

    describe('getWorkstationOrdersList', () => {
      it('should error when the service throws an error', async () => {
        const url = baseUrl;
        workstationServiceStub.getWorkstationOrdersList.rejects(
          new Error('Test error'),
        );

        const response = await request(app)
          .post(url)
          .query({
            start_date: startDate,
            end_date: endDate,
          })
          .send({
            page: 0,
            limit: 50,
            sortFields: [
              {
                columnName: 'dwell_time',
                isDescending: 'true',
              },
            ],
          });
        expect(response.status).to.equal(500);
        expect(response.headers['content-type']).to.match(/json/);
        expect(response.body.status).to.equal(500);
        expect(response.body.title).to.equal('Internal Server Error');
      });
    });

    it('should call "WorkstationService.getWorkstationOrdersList" and return the data', async () => {
      const url = baseUrl;
      const expectedData = {
        data: [
          {
            station: 'Station 1',
            position: 'Position 1',
            dwellTime: 10,
            orderStatus: 'Active',
            pickTask: 'Pick Task 1',
            orderId: 'Order 1',
            container: 'Container 1',
            completedPicks: 5,
            totalPicks: 6,
            arrivalTime: '2023-01-01T00:00:00.000Z',
          },
          {
            station: 'Station 2',
            position: 'Position 2',
            dwellTime: 20,
            orderStatus: 'Assigned',
            pickTask: '--',
            orderId: '--',
            container: 'Container 2',
            completedPicks: 0,
            totalPicks: 0,
            arrivalTime: '2023-01-01T01:00:00.000Z',
          },
        ],
        metadata: {
          page: 0,
          limit: 50,
          totalResults: 2,
        },
      };
      workstationServiceStub.getWorkstationOrdersList.resolves(expectedData);

      const response = await request(app)
        .post(url)
        .query({
          start_date: startDate,
          end_date: endDate,
        })
        .send({
          page: 0,
          limit: 50,
          sortFields: [
            {
              columnName: 'dwell_time',
              isDescending: 'true',
            },
          ],
        });

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(expectedData);
    });
  });
});
