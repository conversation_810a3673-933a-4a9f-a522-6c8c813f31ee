import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';

// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {WorkstationSeriesService} from '../../services/workstation-series-service.ts';
import {WorkstationSeriesCharts} from '../../defs/workstation-series-data.ts';

describe('WorkstationSeriesController', () => {
  const baseUrl: string = '/workstation/series/';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the WorkstationMetricsService. */
  let workstationSeriesServiceStub: sinon.SinonStubbedInstance<WorkstationSeriesService>;

  beforeEach(() => {
    workstationSeriesServiceStub = sinon.createStubInstance(
      WorkstationSeriesService
    );
    Container.set(WorkstationSeriesService, workstationSeriesServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('Workstation Series Controller Error scenarios', () => {
    it('should error when the service throws an error', async () => {
      workstationSeriesServiceStub.getWorkstationSeriesData.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app).get(baseUrl).query({
        chart: WorkstationSeriesCharts.LinesPerHour,
        id: 'station',
      });

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
    });
  });

  describe('Workstation Series Controller success scenarios', () => {
    it('should call "WorkstationSeriesService.getWorkstationSeriesData" and return the data', async () => {
      workstationSeriesServiceStub.getWorkstationSeriesData.resolves(
        WorkstationSeriesService.exampleSeriesData
      );

      const response = await request(app).get(baseUrl).query({
        chart: WorkstationSeriesCharts.QuantityPerHour,
        id: 'station',
      });

      sinon.assert.calledOnce(
        workstationSeriesServiceStub.getWorkstationSeriesData
      );

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        JSON.parse(JSON.stringify(WorkstationSeriesService.exampleSeriesData))
      );
    });
  });
});
