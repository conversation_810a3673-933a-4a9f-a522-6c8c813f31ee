import sinon from 'sinon';
import request from 'supertest';
import {appSetup, Container, ExportFormatter} from 'ict-api-foundations';
import {expect} from 'chai';

// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {WorkstationOrdersDetailsPicksListController} from '../../controllers/workstation-orders-details-picks-list-controller.ts';
import {WorkstationService} from '../../services/workstation-service.ts';

describe(WorkstationOrdersDetailsPicksListController.name, () => {
  const baseUrl = '/workstation';

  const app = appSetup(RegisterRoutes);

  let workstationServiceStub: sinon.SinonStubbedInstance<WorkstationService>;

  const startDate = new Date('2023-01-01T00:00:00.000Z');
  const endDate = new Date('2023-01-02T00:00:00.000Z');

  beforeEach(() => {
    workstationServiceStub = sinon.createStubInstance(WorkstationService);
    Container.set(WorkstationService, workstationServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('PostWorkstationOrdersDetailsPicksListExport', () => {
    const exportUrl = `${baseUrl}/orders/test-id/picks/list/export`;

    it('should error when the service throws an error', async () => {
      workstationServiceStub.getWorkstationOrdersDetailsPicksList.rejects(
        new Error('Test error')
      );

      const response = await request(app)
        .post(exportUrl)
        .query({
          start_date: startDate,
          end_date: endDate,
        })
        .send({
          page: 0,
          limit: 50,
          columns: {
            container: true,
            status: true,
            orderLine: true,
            style: true,
            size: true,
            qtyOrdered: true,
            qtyPicked: true,
            containers: true,
          },
        });

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
    });

    it('should return an excel file', async () => {
      workstationServiceStub.getWorkstationOrdersDetailsPicksList.resolves({
        data: {
          deliveryNumber: 'test delivery',
          tableData: [
            {
              container: 'containerId',
              status: 'status',
              orderLine: 'orderLine',
              style: 'style',
              size: 'size',
              qtyOrdered: 4,
              qtyPicked: 2,
              containers: 3,
            },
          ],
        },
        metadata: {
          orderId: 'test-id',
          page: 0,
          limit: 50,
          totalResults: 1,
        },
      });
      const mapResponseToColumnsStub = sinon.stub(
        ExportFormatter,
        'mapResponseToColumns'
      );
      const generateExcelFileStub = sinon.stub(
        ExportFormatter,
        'generateExcelFile'
      );

      const response = await request(app)
        .post(exportUrl)
        .query({
          start_date: startDate,
          end_date: endDate,
        })
        .send({
          page: 0,
          limit: 50,
          columns: {
            container: true,
            status: true,
            orderLine: true,
            style: true,
            size: true,
            qtyOrdered: true,
            qtyPicked: true,
            containers: true,
          },
        });

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/sheet/);
      expect(response.headers['content-disposition']).to.match(/.xlsx/);
      expect(mapResponseToColumnsStub.calledOnce).to.be.true; // Ensure the stub was called
      expect(generateExcelFileStub.calledOnce).to.be.true; // Ensure the stub was called

      mapResponseToColumnsStub.restore();
      generateExcelFileStub.restore();
    });
  });

  describe('PostWorkstationOrdersDetailsPicksList', () => {
    it('should error when the service throws an error', async () => {
      const url = `${baseUrl}/orders/test-id/picks/list`;
      workstationServiceStub.getWorkstationOrdersDetailsPicksList.rejects(
        new Error('Test error')
      );

      const response = await request(app)
        .post(url)
        .query({
          start_date: startDate,
          end_date: endDate,
        })
        .send({
          page: 0,
          limit: 50,
        });
      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
    });

    it('should call "WorkstationService.getWorkstationOrdersDetailsPicksList" and return the data', async () => {
      const url = `${baseUrl}/orders/test-id/picks/list`;
      const expectedData = {
        data: {
          deliveryNumber: 'test delivery',
          tableData: [
            {
              container: 'test container',
              status: 'Open',
              orderLine: '0004',
              style: 'ASC',
              size: 'S',
              qtyOrdered: 3,
              qtyPicked: 1,
              containers: 4,
            },
          ],
        },
        metadata: {
          orderId: 'test-order-id',
          page: 0,
          limit: 50,
          totalResults: 2,
        },
      };
      workstationServiceStub.getWorkstationOrdersDetailsPicksList.resolves(
        expectedData
      );

      const response = await request(app)
        .post(url)
        .query({
          start_date: startDate,
          end_date: endDate,
        })
        .send({
          page: 0,
          limit: 50,
        });

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(expectedData);
    });
  });
});
