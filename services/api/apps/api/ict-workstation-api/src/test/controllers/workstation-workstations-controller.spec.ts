import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';

// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {WorkstationService} from '../../services/workstation-service.ts';
import {WorkstationsController} from '../../controllers/workstation-workstations-controller.ts';

describe('WorkstationController', () => {
  const baseUrl: string = '/workstation';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the WorkstationMetricsService. */
  let workstationServiceStub: sinon.SinonStubbedInstance<WorkstationService>;

  beforeEach(() => {
    workstationServiceStub = sinon.createStubInstance(WorkstationService);
    Container.set(WorkstationService, workstationServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('Workstation Controller Error scenarios', () => {
    it('should error when the service throws an error - getWorkstations', async () => {
      const url = `${baseUrl}/workstations`;
      workstationServiceStub.getWorkstations.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app).get(url);
      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
    });
  });

  describe('Workstation Controller success scenarios', () => {
    it('should call "WorkstationService.getWorkstations" and return the data', async () => {
      const url = `${baseUrl}/workstations`;
      workstationServiceStub.getWorkstations.resolves(
        WorkstationsController.exampleWorkstations
      );

      const response = await request(app).get(url);
      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        JSON.parse(JSON.stringify(WorkstationsController.exampleWorkstations))
      );
    });
  });
});
