import sinon from 'sinon';
import request from 'supertest';
import {Container, ExportFormatter, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';

// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {WorkstationService} from '../../services/workstation-service.ts';
import {WorkstationController} from '../../controllers/workstation-controller.ts';

describe('WorkstationController', () => {
  const baseUrl: string = '/workstation';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the WorkstationMetricsService. */
  let workstationServiceStub: sinon.SinonStubbedInstance<WorkstationService>;

  beforeEach(() => {
    workstationServiceStub = sinon.createStubInstance(WorkstationService);
    Container.set(WorkstationService, workstationServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('WorkstationListExport', () => {
    const exportUrl = `${baseUrl}/list/export`;

    it('should error when the service throws an error', async () => {
      workstationServiceStub.getStationList.rejects(new Error('Test error'));
      const response = await request(app)
        .post(exportUrl)
        .send({
          columns: {
            workstation: true,
            status: true,
            workMode: true,
            workflowStatus: true,
            operatorId: true,
            activeTime: true,
            starvedTime: true,
            idleTime: true,
            blockedTime: true,
            quantityPerHour: true,
            weightedQuantityPerHour: true,
            linesPerHour: true,
            weightedLinesPerHour: true,
            donorTotesPerHour: true,
            orderTotesPerHour: true,
          },
        });

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
    });

    it('should return an excel file', async () => {
      workstationServiceStub.getStationList.resolves(
        WorkstationService.exampleWorkstationList,
      );

      const mapResponseToColumnsStub = sinon.stub(
        ExportFormatter,
        'mapResponseToColumns',
      );
      const generateExcelFileStub = sinon.stub(
        ExportFormatter,
        'generateExcelFile',
      );

      const response = await request(app)
        .post(exportUrl)
        .send({
          columns: {
            workstation: true,
            status: true,
            workMode: true,
            workflowStatus: true,
            operatorId: true,
            activeTime: true,
            starvedTime: true,
            idleTime: true,
            blockedTime: true,
            quantityPerHour: true,
          },
        });

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/sheet/);
      expect(response.headers['content-disposition']).to.match(/.xlsx/);
      expect(mapResponseToColumnsStub.calledOnce).to.be.true; // Ensure the stub was called
      expect(generateExcelFileStub.calledOnce).to.be.true; // Ensure the stub was called

      mapResponseToColumnsStub.restore();
      generateExcelFileStub.restore();
    });
  });

  describe('Workstation Controller Error scenarios', () => {
    it('should error when the service throws an error - getStationList', async () => {
      const url = `${baseUrl}/list`;
      workstationServiceStub.getStationList.rejects(
        new Error('Something bad happened'),
      );

      const response = await request(app).get(url);
      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
    });
  });

  describe('Workstation Controller success scenarios', () => {
    it('should call "WorkstationService.getStationList" and return the data', async () => {
      const url = `${baseUrl}/list`;
      workstationServiceStub.getStationList.resolves(
        WorkstationService.exampleWorkstationList,
      );

      const response = await request(app).get(url);
      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        JSON.parse(
          JSON.stringify(WorkstationController.exampleWorkstationList),
        ),
      );
    });
  });
});
