import sinon from 'sinon';
import request from 'supertest';
import {expect} from 'chai';
import {Container, ExportFormatter, appSetup} from 'ict-api-foundations';
import {WorkstationDailyPerformanceListController} from '../../controllers/workstation-daily-performance-list-controller.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {WorkstationService} from '../../services/workstation-service.ts';

describe(WorkstationDailyPerformanceListController.name, () => {
  const baseUrl = '/workstation/daily-performance/list';
  const app = appSetup(RegisterRoutes);

  let workstationServiceStub: sinon.SinonStubbedInstance<WorkstationService>;

  const startDate = new Date('2023-01-01T00:00:00.000Z');
  const endDate = new Date('2023-01-02T00:00:00.000Z');

  beforeEach(() => {
    workstationServiceStub = sinon.createStubInstance(WorkstationService);
    Container.set(WorkstationService, workstationServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('Workstation Daily Performance List Controller Error scenarios', () => {
    it('should error when the service throws an error', async () => {
      workstationServiceStub.getWorkstationDailyPerformanceList.rejects(
        new Error('Test Error'),
      );

      const response = await request(app).post(baseUrl).query({
        start_date: startDate,
        end_date: endDate,
      });

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
    });
  });

  describe('Workstation Daily Performance List Controller success scenarios', () => {
    it('should call "WorkstationService.GetWorkstationDailyPerformanceList" and return the data', async () => {
      workstationServiceStub.getWorkstationDailyPerformanceList.resolves(
        WorkstationDailyPerformanceListController.exampleData,
      );

      const response = await request(app).post(baseUrl).query({
        start_date: startDate,
        end_date: endDate,
      });

      expect(
        workstationServiceStub.getWorkstationDailyPerformanceList.calledOnce,
      ).to.be.true;

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        WorkstationDailyPerformanceListController.exampleResponse,
      );
    });
  });

  describe('WorkstationDailyPerformanceExport', () => {
    const exportUrl = `${baseUrl}/export`;

    it('should error when the service throws an error', async () => {
      workstationServiceStub.getWorkstationDailyPerformanceList.rejects(
        new Error('Test Error'),
      );

      const response = await request(app)
        .post(exportUrl)
        .query({
          start_date: startDate,
          end_date: endDate,
        })
        .send({
          filename: 'test_file',
          columns: {
            date: true,
            totalHours: true,
            idlePercent: true,
            starvedPercent: true,
            starvedHours: true,
            donorContainers: true,
            gtpContainers: true,
            linesPicked: true,
            qtyPerLine: true,
            pickLineQty: true,
            linesPerHour: true,
            avgLinesPickedPerHr1stShiftPercentage: true,
            avgLinesPickedPerHr2ndShiftPercentage: true,
            retrievalFromDMS: true,
            storageToDMS: true,
            retrievalFromASRS: true,
            storageToASRS: true,
          },
        });

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
    });

    it('should return an excel file', async () => {
      workstationServiceStub.getWorkstationDailyPerformanceList.resolves(
        WorkstationDailyPerformanceListController.exampleData,
      );
      const mapResponseToColumnsStub = sinon.stub(
        ExportFormatter,
        'mapResponseToColumns',
      );
      const generateExcelFileStub = sinon.stub(
        ExportFormatter,
        'generateExcelFile',
      );
      const response = await request(app)
        .post(exportUrl)
        .query({
          start_date: startDate,
          end_date: endDate,
        })
        .send({
          filename: 'test_file.xlsx',
          columns: {
            date: true,
            totalHours: true,
            idlePercent: true,
            starvedPercent: true,
            starvedHours: true,
            donorContainers: true,
            gtpContainers: true,
            linesPicked: true,
            qtyPerLine: true,
            pickLineQty: true,
            linesPerHour: true,
            avgLinesPickedPerHr1stShiftPercentage: true,
            avgLinesPickedPerHr2ndShiftPercentage: true,
            retrievalFromDMS: true,
            storageToDMS: true,
            retrievalFromASRS: true,
            storageToASRS: true,
          },
        });

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/sheet/);
      expect(response.headers['content-disposition']).to.match(/.xlsx/);
      expect(mapResponseToColumnsStub.calledOnce).to.be.true; // Ensure the stub was called
      expect(generateExcelFileStub.calledOnce).to.be.true; // Ensure the stub was called

      mapResponseToColumnsStub.restore();
      generateExcelFileStub.restore();
    });
  });
});
