import sinon from 'sinon';
import {expect} from 'chai';
import {PaginatedResults, <PERSON><PERSON><PERSON><PERSON>} from 'ict-api-foundations';
import {WorkstationService} from '../../services/workstation-service.ts';
import {WorkstationStore} from '../../stores/workstation-store.ts';
import {WorkstationsController} from '../../controllers/workstation-workstations-controller.ts';
import {WorkstationOrdersDetailsPicksListQueryResponse} from '../../defs/workstation-orders-details-picks-list.def.ts';
import {WorkstationContainersListQueryResponse} from '../../defs/workstation-containers-list-def.ts';

describe('WorkstationService', () => {
  // Workstation Store stub, intercepts calls made by the workstation service
  let workstationStoreStub: sinon.SinonStubbedInstance<WorkstationStore>;

  // Workstation service to use in tests
  let workstationService: WorkstationService;
  let mockLogger: sinon.SinonStubbedInstance<WinstonLogger>;

  beforeEach(() => {
    mockLogger = sinon.createStubInstance(WinstonLogger);
    mockLogger.info.resolves();

    // Setup workstation store stub
    workstationStoreStub = sinon.createStubInstance(WorkstationStore);
    workstationService = new WorkstationService(
      workstationStoreStub,
      mockLogger,
    );
  });

  describe('getWorkstationProgress', () => {
    it('should get total and current workstations', async () => {
      const mockStoreResponse = WorkstationsController.exampleWorkstations.map(
        workstation => {
          return {
            workstation,
          };
        },
      );

      workstationStoreStub.getWorkstations.resolves(mockStoreResponse);

      const result = await workstationService.getWorkstations();

      expect(result).to.deep.equal(WorkstationsController.exampleWorkstations);
    });
  });

  describe('getWorkstationContainersList', () => {
    it('should get a list of workstation containers', async () => {
      const nowDate = new Date().toISOString();
      const mockStoreResponse: PaginatedResults<
        WorkstationContainersListQueryResponse[]
      > = {
        list: [
          {
            container: 'Container 1',
            status: 'Status 1',
            transport: '--',
            quantity: 10,
            last_location: 'Location 1',
            event_time: {value: nowDate},
          },
        ],
        totalResults: 1,
      };
      workstationStoreStub.getWorkstationContainersList.resolves(
        mockStoreResponse,
      );

      const result = await workstationService.getWorkstationContainersList({
        startDate: new Date(),
        endDate: new Date(),
        size: 'L',
        style: 'style',
        zone: 'ASRS',
        page: 0,
        limit: 50,
        sortFields: [],
        filters: undefined,
        searchString: undefined,
      });

      expect(result).to.deep.equal({
        data: [
          {
            container: 'Container 1',
            status: 'Status 1',
            transport: '--',
            quantity: 10,
            lastLocation: 'Location 1',
            eventTime: nowDate,
          },
        ],
        metadata: {
          page: 0,
          limit: 50,
          totalResults: 1,
        },
      });
    });
  });

  describe('getWorkstationStatusOrdersDetailsPicksList', () => {
    it('should get the list of picks for the order', async () => {
      const mockStoreResponse: PaginatedResults<
        WorkstationOrdersDetailsPicksListQueryResponse[]
      > = {
        list: [
          {
            container: 'test container',
            status: 'Open',
            order_line: '0004',
            style: 'ASC',
            size: 'S',
            qty_ordered: 3,
            qty_picked: 1,
            containers: 4,
            delivery_number: 'test delivery',
          },
        ],
        totalResults: 1,
      };

      workstationStoreStub.getWorkstationOrdersDetailsPicksList.resolves(
        mockStoreResponse,
      );

      const result =
        await workstationService.getWorkstationOrdersDetailsPicksList({
          startDate: new Date('2025-04-01T00:00:00.000Z'),
          endDate: new Date('2025-04-01T00:00:00.000Z'),
          orderId: 'test-id',
          zone: 'ASRS',
          page: 0,
          limit: 50,
          sortFields: [],
        });

      expect(result).to.deep.equal({
        data: {
          deliveryNumber: 'test delivery',
          tableData: [
            {
              container: 'test container',
              status: 'Open',
              orderLine: '0004',
              style: 'ASC',
              size: 'S',
              qtyOrdered: 3,
              qtyPicked: 1,
              containers: 4,
            },
          ],
        },
        metadata: {
          orderId: 'test-id',
          page: 0,
          limit: 50,
          totalResults: 1,
        },
      });
    });
  });

  describe('getWorkstationOrdersDetailsPicksList', () => {
    it('should get the list of picks for the order', async () => {
      const mockStoreResponse: PaginatedResults<
        WorkstationOrdersDetailsPicksListQueryResponse[]
      > = {
        list: [
          {
            container: 'test container',
            status: 'Open',
            order_line: '0004',
            style: 'ASC',
            size: 'S',
            qty_ordered: 3,
            qty_picked: 1,
            containers: 4,
            delivery_number: 'test delivery',
          },
        ],
        totalResults: 1,
      };

      workstationStoreStub.getWorkstationOrdersDetailsPicksList.resolves(
        mockStoreResponse,
      );

      const result =
        await workstationService.getWorkstationOrdersDetailsPicksList({
          startDate: new Date('2025-04-01T00:00:00.000Z'),
          endDate: new Date('2025-04-01T00:00:00.000Z'),
          orderId: 'test-id',
          zone: 'ASRS',
          page: 0,
          limit: 50,
          sortFields: [],
        });

      expect(result).to.deep.equal({
        data: {
          deliveryNumber: 'test delivery',
          tableData: [
            {
              container: 'test container',
              status: 'Open',
              orderLine: '0004',
              style: 'ASC',
              size: 'S',
              qtyOrdered: 3,
              qtyPicked: 1,
              containers: 4,
            },
          ],
        },
        metadata: {
          orderId: 'test-id',
          page: 0,
          limit: 50,
          totalResults: 1,
        },
      });
    });
  });

  describe('getWorkstationOrderStatus', () => {
    it('should get active and delayed orders', async () => {
      const mockStoreResponse = {
        activeOrders: 10,
        delayedOrders: 5,
      };

      workstationStoreStub.getWorkstationOrdersStatus.resolves(
        mockStoreResponse,
      );

      const result = await workstationService.getWorkstationOrdersStatus(
        new Date(),
        new Date(),
      );

      expect(result).to.deep.equal(mockStoreResponse);
    });
  });
});
