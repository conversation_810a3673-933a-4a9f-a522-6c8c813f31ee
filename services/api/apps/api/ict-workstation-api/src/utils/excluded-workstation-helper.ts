import {ConfigStore, ContextService, IctError} from 'ict-api-foundations';
import {ExcludedWorkstations} from '../defs/workstation-list';

export async function getExcludedWorkstationList(
  configStore: ConfigStore,
  context: ContextService
): Promise<ExcludedWorkstations> {
  // Get the listing of workstations to exclude from workstation dashboard
  const stations = await configStore.findMostRelevantSettingForUser(
    context.userId,
    undefined,
    'excluded-workstation-list'
  );

  if (!stations?.value || !hasWorkstationsKey(stations.value)) {
    throw IctError.internalServerError('Excluded workstation list not found');
  }

  return stations.value;
}

/* eslint-disable @typescript-eslint/no-explicit-any */
function hasWorkstationsKey(obj: any): obj is {workstations: []} {
  return (
    obj &&
    typeof obj === 'object' &&
    'workstations' in obj &&
    Array.isArray(obj.workstations)
  );
}
