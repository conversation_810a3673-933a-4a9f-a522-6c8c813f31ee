import {
  ConfigStore,
  ContextService,
  DatabaseProvider,
  DiService,
  WinstonLogger,
} from 'ict-api-foundations';
import {
  WorkstationData,
  WorkstationSeriesCharts,
} from '../defs/workstation-series-data.ts';

@DiService()
export class WorkstationSeriesStore {
  constructor(
    private context: ContextService,
    private configStore: ConfigStore,
    private logger: WinstonLog<PERSON>
  ) {}

  get dbProvider(): DatabaseProvider {
    return this.context.dbProvider;
  }
  /**
   * Retrieve series data for the workstations, type provided by query param.
   * @returns Workstations rates by 15 minute intervals over the previous 3 hours.
   * Table data runs every 5 minutes.
   */
  async getWorkstationSeriesChartData(
    seriesType: WorkstationSeriesCharts
  ): Promise<WorkstationData[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    let seriesParam: string | undefined;

    switch (seriesType) {
      case WorkstationSeriesCharts.LinesPerHour:
        seriesParam = 'lines_per_hour';
        break;
      case WorkstationSeriesCharts.QuantityPerHour:
        seriesParam = 'picks_per_hour';
        break;
      case WorkstationSeriesCharts.SourceContainersPerHour:
        seriesParam = 'donor_totes_per_hour';
        break;
      case WorkstationSeriesCharts.DestinationContainersPerHour:
        seriesParam = 'order_totes_per_hour';
        break;
      case WorkstationSeriesCharts.WeightedLinesPerHour:
        seriesParam = 'weighted_lines_per_hour';
        break;
      case WorkstationSeriesCharts.WeightedQuantityPerHour:
        seriesParam = 'weighted_picks_per_hour';
        break;
      case WorkstationSeriesCharts.WeightedSourceContainersPerHour:
        seriesParam = 'weighted_donor_totes_per_hour';
        break;
      case WorkstationSeriesCharts.WeightedDestinationContainersPerHour:
        seriesParam = 'weighted_order_totes_per_hour';
        break;
      default:
        seriesParam = '';
        break;
    }

    const platWorkstationListing = this.dbProvider.bigQuery.getEdpFullTablePath(
      'platinum_workstation_listing'
    );

    const sql = `
      WITH workstationStats AS (
        SELECT
          workstation,
          ${seriesParam},
          EXTRACT(MINUTE FROM TIMESTAMP_SECONDS(5 * 60 * DIV(UNIX_SECONDS(CURRENT_TIMESTAMP()), 5 * 60))) as minute_offset,
          EXTRACT(MINUTE FROM TIMESTAMP_SECONDS(5 * 60 * DIV(UNIX_SECONDS(record_timestamp), 5 * 60))) AS minute_increments,
          TIMESTAMP_SECONDS(5 * 60 * DIV(UNIX_SECONDS(record_timestamp), 5 * 60)) as adjusted_increment,
        FROM \`${platWorkstationListing}\`
        WHERE record_timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 190 MINUTE)
      ),
      originalData AS (
        SELECT
          workstation AS id,
          CAST(adjusted_increment AS STRING) AS name,
          ${seriesParam} * 4 AS value,
        FROM workstationStats
        WHERE MOD(minute_increments - minute_offset, 15) = 0
        ORDER BY workstation, adjusted_increment DESC
      )
      SELECT
        id,
        ARRAY_AGG(STRUCT(name, value)) AS data,
      FROM originalData
      GROUP BY id
    `;

    const sqlOptions = {
      query: sql,
    };

    // execute the query and return the results
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    return rows;
  }

  public static readonly type: string = 'workstation-series-store';

  public getType(): string {
    return WorkstationSeriesStore.type;
  }
}
