import {
  ConfigStore,
  ContextService,
  DatabaseProvider,
  DiService,
  IctError,
  WinstonLogger,
} from 'ict-api-foundations';
import {WorkstationStatsQueryResponse} from '../defs/workstation-stats.ts';
import {WorkstationOperatorStats} from '../defs/workstation-operator-stats.ts';
import {WorkstationPerformanceStats} from '../defs/workstation-performance-stats.ts';
import {WorkstationHealthStats} from '../defs/workstation-health-stats.ts';
import {getExcludedWorkstationList} from '../utils/excluded-workstation-helper.ts';

@DiService()
export class WorkstationMetricsStore {
  constructor(
    private context: ContextService,
    private logger: WinstonLogger,
    private configStore: ConfigStore,
  ) {}

  get dbProvider(): DatabaseProvider {
    return this.context.dbProvider;
  }

  /**
   * Retrieve number of total workstations, status, and area if available.
   * @param startDate Start date for the date range.
   * @param endDate End date for the date range.
   * @returns Total GTP's, status and work mode
   */
  async getWorkstationStats(
    startDate: string,
    endDate: string,
  ): Promise<WorkstationStatsQueryResponse> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const excludedStations = (
      await getExcludedWorkstationList(this.configStore, this.context)
    ).workstations;

    const goldPickActivity =
      this.dbProvider.bigQuery.getEdpFullTablePath('gold_pick_activity');

    const goldPick = this.dbProvider.bigQuery.getEdpFullTablePath('gold_pick');

    const platinumWorkstationListing =
      this.dbProvider.bigQuery.getEdpFullTablePath(
        'platinum_workstation_listing',
      );

    const sql = `
        WITH recentPickData AS (
          SELECT 
            workstation_code, 
            work_type_code, 
            event_timestamp_utc
          FROM ${goldPick}
          WHERE event_timestamp_utc > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
            AND workstation_code NOT IN UNNEST(@excludedStations)
            AND COALESCE(workstation_code, '') != ''
        ),
        -- a list of each workstation, the type of work, and a count of instances for that type between the given dates
        stationActivity AS (
          SELECT 
            workstation_code, 
            work_type_code, 
            COUNT(event_timestamp_utc) AS instances 
          FROM (
                SELECT
                    workstation_code,
                    work_type_code,
                    event_timestamp_utc
                FROM recentPickData
                UNION ALL
                SELECT
                    workstation_code,
                    work_type_code,
                    event_timestamp_utc
                FROM ${goldPickActivity}
            ) unifiedPickData
          WHERE event_timestamp_utc BETWEEN @startDate AND @endDate 
            AND workstation_code NOT IN UNNEST(@excludedStations)
            AND COALESCE(workstation_code, '') != ''
          GROUP BY workstation_code, work_type_code
        ),
        -- aggregates stationActivity by summing number of instances for each station/worktype combo, and also puts a friendly name on the work type
        stationModes AS (
          SELECT 
            workstation_code, 
            CASE 
              WHEN UPPER(work_type_code) LIKE '%COUNT%' THEN 'Cycle Count'
              WHEN UPPER(work_type_code) LIKE '%INSPECT%' THEN 'Quality Control'
              ELSE 'Picking'
            END AS workType, 
            SUM(instances) AS instances 
          FROM stationActivity 
          GROUP BY workstation_code, workType
        ),
        -- Finds the top worktype for each workstation based on number of event instances
        topActivities AS (
          SELECT 
            workstation_code, 
            MAX_BY(workType, instances) as workType 
          FROM stationModes 
          GROUP BY workstation_code
          
          UNION ALL 
          
          --- Any non active workstation is considered work mode "OTHER"
          SELECT DISTINCT 
            workstation_code, 
            'Other' AS workType 
          FROM recentPickData 
          WHERE workstation_code NOT IN (SELECT DISTINCT workstation_code FROM stationModes)
        ),
        -- aggregates the different station types based on the top activity in each station
        stationTypes AS (
          SELECT
            COUNT(workstation_code) AS total_per_type,
            workType
          FROM topActivities
          GROUP BY workType
        ),
        inferredStats AS (
          SELECT
            (SELECT COUNT (DISTINCT workstation_code) FROM recentPickData) AS totalWorkstations,
            (SELECT COUNT (DISTINCT workstation_code) FROM stationModes) AS active,
            ARRAY_AGG ( STRUCT(workType AS type, total_per_type AS count) ) AS workstationMode
          FROM stationTypes
        ),
        workstationSpanListing AS (
          SELECT 
            workstation, 
            COALESCE(UPPER(status), 'CLOSED') as status, 
            CASE
              WHEN UPPER(workflow_status) = 'ENABLED' THEN work_mode
              ELSE 'OTHER'
            END AS work_mode,
            ROW_NUMBER() OVER (PARTITION BY workstation ORDER BY record_timestamp DESC) AS row_num 
          FROM \`${platinumWorkstationListing}\` 
          WHERE record_timestamp BETWEEN @startDate AND @endDate
        ),
        activeWSL AS (
          SELECT 
            COUNT(workstation) as totalWorkstations,
            COUNTIF (status != 'CLOSED') AS active
          FROM workstationSpanListing 
          WHERE row_num = 1 
            AND workstation IN (SELECT DISTINCT workstation_code FROM recentPickData)),
        typeWSL AS (
          SELECT 
            CASE 
              WHEN work_mode = 'PICKING' THEN 'Picking'
              WHEN work_mode = 'CONSOLIDATION' THEN 'Consolidation'
              ELSE 'Other'
            END AS work_mode,
            COUNT(workstation) AS mode_count
          FROM workstationSpanListing 
          WHERE row_num = 1 
            AND workstation IN (SELECT DISTINCT workstation_code FROM recentPickData)
          GROUP BY work_mode
        ),
        arrayWSL AS (
          SELECT 
            ARRAY_AGG ( STRUCT(work_mode AS type, mode_count AS count) ) AS workstationMode
          FROM typeWSL
        )
  
        SELECT 
          COALESCE(
            NULLIF(
              (SELECT totalWorkstations FROM activeWSL),0), 
              (SELECT totalWorkstations FROM inferredStats)) 
          AS totalWorkstations,
          COALESCE(
            NULLIF(
              (SELECT active FROM activeWSL),0), 
              (SELECT active FROM inferredStats)) 
          AS active,
          COALESCE(
            (SELECT workstationMode FROM arrayWSL), 
            (SELECT workstationMode FROM inferredStats)) 
          AS workstationMode
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
        excludedStations,
      },
      types: {
        excludedStations: ['string'],
      },
    };

    // execute the query and return the results
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length) {
      throw IctError.noContent();
    }

    return rows[0] as WorkstationStatsQueryResponse;
  }

  /**
   * Retrieve number of operators online, actual cumalative rate for lines/hour,
   * target rate for lines/hr based upon current days rate.
   * @returns Total operators and line pick quantity.
   */
  async getWorkstationOperatorStats(): Promise<WorkstationOperatorStats> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const excludedStations = (
      await getExcludedWorkstationList(this.configStore, this.context)
    ).workstations;

    /* This query is based off of a table that runs every 5 minutes but aggregates the data
    for the previous 15 minutes. This query finds the most recent entry time, rounded to 5,
    and then selects the prior entries that are on the same minute offset (2:55 pm, 2:40 pm,
    2:25 pm, etc) to use to sum up the total lines and total operator logged in time to
    calculate the avg rate over the course of the day.
    */
    const platinumWorkstationListing = bigQueryDb.getEdpFullTablePath(
      'platinum_workstation_listing',
    );

    const sql = `
        WITH workstationListing AS (
          SELECT 
            -- Round the minutes of the current timestamp to the nearest 5 minute increment
            EXTRACT(MINUTE FROM TIMESTAMP_SECONDS(5 * 60 * DIV(UNIX_SECONDS(CURRENT_TIMESTAMP()), 5 * 60))) as minute_offset, 
            -- Round the minutes of the table's record timestamp to the nearest 5 minute increment
            TIMESTAMP_SECONDS(5 * 60 * DIV(UNIX_SECONDS(record_timestamp), 5 * 60)) as adjusted_increment, 
            -- Select only the minutes from the table's record timestamp and round to the nearest 5 minutes
            EXTRACT(MINUTE FROM TIMESTAMP_SECONDS(5 * 60 * DIV(UNIX_SECONDS(record_timestamp), 5 * 60))) AS minute_increments,
            operator_id, 
            lines_per_hour, 
            logged_in_period, 
            ROW_NUMBER() OVER (PARTITION BY workstation ORDER BY record_timestamp DESC) AS row_num 
          FROM ${platinumWorkstationListing}
          WHERE record_timestamp > TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY)
            AND workstation NOT IN UNNEST(@excludedStations)
            AND COALESCE(workstation, '') != ''
        )
      
        SELECT COALESCE(
          (SELECT COUNT(DISTINCT(operator_id)) FROM workstationListing WHERE row_num = 1 AND operator_id IS NOT NULL), 0) AS totalOperators,
    
          -- To calculate current hourly activity rate, use the production from the most current 15 minutes x 4 
          COALESCE((SUM(lines_per_hour) * 4), 0) AS actualLinesPerHour, 
          
          -- Summation of all throughput records for the day divided by summation of operator logged in time to calculate 
          -- target throughput rate. Uses the minute offset to select records within the same time window (15-minute intervals).  
          COALESCE((
              SELECT ROUND(SUM(lines_per_hour/logged_in_period)) 
              FROM workstationListing 
              WHERE mod(minute_increments - minute_offset, 15) = 0
            ),
            0
          ) AS targetLinesPerHour 
        FROM workstationListing WHERE row_num = 1; 
          `;

    const sqlOptions = {
      query: sql,
      params: {
        excludedStations,
      },
      types: {
        excludedStations: ['string'],
      },
    };

    // execute the query and return the results
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length) {
      throw IctError.noContent();
    }

    return rows[0] as WorkstationOperatorStats;
  }

  /**
   * Retrieve length in minutes of active time and starved time for all workstations.
   * @returns Total active and starved times.
   */
  async getWorkstationPerformanceStats(
    startDate: string,
    endDate: string,
  ): Promise<WorkstationPerformanceStats> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const excludedStations = (
      await getExcludedWorkstationList(this.configStore, this.context)
    ).workstations;

    const platWorkstationListingTable =
      this.dbProvider.bigQuery.getEdpFullTablePath(
        'platinum_workstation_listing',
      );
    const platWorkstationOperatorsTable =
      this.dbProvider.bigQuery.getEdpFullTablePath(
        'platinum_workstation_operators',
      );
    const platWorkstationActivitiesTable =
      this.dbProvider.bigQuery.getEdpFullTablePath(
        'platinum_workstation_activities',
      );

    const sql = `
        WITH includedStations AS (
          SELECT
            DISTINCT workstation AS workstation_code,
          FROM \`${platWorkstationListingTable}\`
          WHERE workstation NOT IN UNNEST(@excludedStations)
            AND workstation != ''
        ),
        activeTime AS (
          SELECT
            COALESCE(SUM(logged_in_duration), 0) AS totalActiveTime,
          FROM \`${platWorkstationOperatorsTable}\`
          WHERE starting_timestamp BETWEEN @startDate AND @endDate
          AND workstation_code IN (SELECT workstation_code FROM includedStations)
        ),
        starvedTime AS (
          SELECT
            COALESCE(SUM(starvation_time), 0) AS totalStarvationTime,
          FROM \`${platWorkstationActivitiesTable}\`
          WHERE record_timestamp BETWEEN @startDate AND @endDate
            AND workstation_code IN (SELECT workstation_code FROM includedStations)
        ),
        workstationListing AS (
          SELECT
            MAX(active_time) AS active_time,
            SUM(starved_time) AS starved_time,
          FROM \`${platWorkstationListingTable}\`
          WHERE record_timestamp BETWEEN @startDate AND @endDate
            AND workstation IN (SELECT workstation_code FROM includedStations)
          GROUP BY workstation, status, work_mode
        )
        SELECT
          COALESCE(
            (SELECT ROUND(SUM(active_time)/60) FROM workstationListing),
            (SELECT totalActiveTime FROM activeTime),
            0
          ) AS totalActiveTime,
          COALESCE(
            (SELECT ROUND(SUM(starved_time)/60) FROM workstationListing),
            (SELECT totalStarvationTime FROM starvedTime),
            0
          ) as totalStarvationTime
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
        excludedStations,
      },
      types: {
        // type needed when array is empty
        excludedStations: ['string'],
      },
    };

    // execute the query and return the results
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length) {
      throw IctError.noContent();
    }

    return rows[0] as WorkstationPerformanceStats;
  }

  /**
   * Retrieve length in minutes of combined downtime for all workstations.
   * @returns Total downtime in minutes.
   */
  async getWorkstationHealthStats(
    startDate: string,
    endDate: string,
  ): Promise<WorkstationHealthStats> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const excludedStations = (
      await getExcludedWorkstationList(this.configStore, this.context)
    ).workstations;

    const platWorkstationActivitiesTable =
      this.dbProvider.bigQuery.getEdpFullTablePath(
        'platinum_workstation_activities',
      );

    const sql = `
        WITH distinctRecords AS (
          SELECT
            blocking_time,
            load_unit_type,
            next_load_unit_type,
            starvation_time,
          FROM \`${platWorkstationActivitiesTable}\`
          WHERE record_timestamp BETWEEN @startDate AND @endDate
            AND workstation_code NOT IN UNNEST(@excludedStations)
            AND workstation_code IS NOT NULL
            AND workstation_code != ''
        ),
        blockingTime AS (
          SELECT
            SUM(blocking_time) AS blocking_time,
          FROM distinctRecords
          WHERE (load_unit_type = 'DONOR' OR next_load_unit_type = 'DONOR')
            AND blocking_time > 0
        )
        SELECT
          COALESCE(SUM(starvation_time) + (SELECT blocking_time FROM blockingTime), 0) AS totalDowntime,
        FROM distinctRecords
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
        excludedStations,
      },
      types: {
        // type needed when array is empty
        excludedStations: ['string'],
      },
    };

    // execute the query and return the results
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length) {
      throw IctError.noContent();
    }

    return rows[0] as WorkstationHealthStats;
  }

  public static readonly type: string = 'workstation-metrics-store';

  public getType(): string {
    return WorkstationMetricsStore.type;
  }
}
