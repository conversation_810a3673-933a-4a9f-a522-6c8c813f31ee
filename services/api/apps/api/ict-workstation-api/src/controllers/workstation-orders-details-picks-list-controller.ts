import {
  Container,
  DatabaseTypes,
  ExportFormatter,
  extractPaginatedRequestNoDates,
  type PaginatedRequestNoDates,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Body,
  Controller,
  Middlewares,
  OperationId,
  Path,
  Post,
  Query,
  Route,
  SuccessResponse,
  Tags,
} from 'tsoa';
import {Readable} from 'stream';
import {WorkstationService} from '../services/workstation-service.ts';
import {WorkstationOrdersDetailsPicksListData} from '../defs/workstation-orders-details-picks-list.def.ts';

@Route('/workstation')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}],
  }),
])
export class WorkstationOrdersDetailsPicksListController extends Controller {
  @SuccessResponse('200')
  @Post('/orders/{orderId}/picks/list')
  @OperationId('PostWorkstationOrdersDetailsPicksList')
  @Tags('workstation')
  public async getWorkstationOrdersDetailsPicksList(
    @Path() orderId: string,
    @Query('zone') zone: string = 'ASRS',
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
    @Body() paginatedRequest: PaginatedRequestNoDates,
  ): Promise<WorkstationOrdersDetailsPicksListData> {
    const workstationService = Container.get(WorkstationService);

    const params = {
      ...extractPaginatedRequestNoDates(paginatedRequest),
      startDate,
      endDate,
      orderId,
      zone,
    };

    const result =
      await workstationService.getWorkstationOrdersDetailsPicksList(params);

    return result;
  }

  @SuccessResponse('200')
  @Post('/orders/{orderId}/picks/list/export')
  @OperationId('PostWorkstationOrdersDetailsPicksListExport')
  @Tags('workstation')
  public async getWorkstationOrdersDetailsPicksListExport(
    @Path() orderId: string,
    @Query('zone') zone: string = 'ASRS',
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
    @Body()
    paginatedRequest: PaginatedRequestNoDates & {
      columns: {[key: string]: boolean};
    },
  ): Promise<Readable> {
    const workstationService = Container.get(WorkstationService);

    const params = {
      ...extractPaginatedRequestNoDates(paginatedRequest),
      startDate,
      endDate,
      orderId,
      zone,
    };

    const response =
      await workstationService.getWorkstationOrdersDetailsPicksList(params);

    const updatedResponse = ExportFormatter.mapResponseToColumns(
      paginatedRequest.columns,
      response.data.tableData,
    );

    const buffer = ExportFormatter.generateExcelFile(updatedResponse);
    const today = new Date();
    const formattedDate = `${today.getMonth() + 1}-${today.getDate()}-${today.getFullYear()}`;
    const filename = `workstation_orders_details_picks_list_${formattedDate}.xlsx`;

    // Set headers for Excel file download
    this.setHeader('Content-Disposition', `attachment; filename=${filename}`);
    this.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );

    // Create a readable stream from the buffer
    const stream = new Readable();
    stream.push(buffer);
    stream.push(null); // Signal the end of the stream
    return stream;
  }
}
