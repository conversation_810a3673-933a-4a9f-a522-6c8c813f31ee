import {BaseHealthCheck} from 'ict-api-foundations';
import {Get, Route, OperationId, Tags} from 'tsoa';

@Route('/workstation/healthcheck')
export class HealthCheckController {
  @Get()
  @OperationId('GetWorkstationBasicHealthCheck')
  @Tags('workstation')
  public GetWorkstationBasicHealthCheck() {
    return BaseHealthCheck.getBasicHealthCheck();
  }

  @Get('/full')
  @OperationId('GetWorkstationFullHealthCheck')
  @Tags('workstation')
  public GetWorkstationFullHealthCheck() {
    return BaseHealthCheck.getDeepHealthCheck(['auth0', 'bigQuery', 'redis']);
  }
}
