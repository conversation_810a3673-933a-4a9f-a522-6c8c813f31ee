import {
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ExportFormatter,
  extractPaginatedRequestNoDates,
  type PaginatedRequestNoDates,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Body,
  Controller,
  Middlewares,
  OperationId,
  Post,
  Query,
  Route,
  SuccessResponse,
  Tags,
} from 'tsoa';
import {Readable} from 'stream';
import {WorkstationService} from '../services/workstation-service.ts';
import {
  WorkstationOrdersListData,
  WorkstationOrdersListQueryParams,
} from '../defs/workstation-orders-status-def.ts';

@Route('workstation')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
])
export class WorkstationOrdersListController extends Controller {
  @SuccessResponse('200')
  @Post('/orders/list')
  @OperationId('PostWorkstationOrdersList')
  @Tags('workstation')
  public async getWorkstationOrdersList(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
    @Body() paginatedRequest: PaginatedRequestNoDates,
  ): Promise<WorkstationOrdersListData> {
    const workstationService = Container.get(WorkstationService);

    const workstationOrderStatusListParams: WorkstationOrdersListQueryParams = {
      ...extractPaginatedRequestNoDates(paginatedRequest),
      startDate,
      endDate,
    };

    const result = await workstationService.getWorkstationOrdersList(
      workstationOrderStatusListParams,
    );

    return result;
  }

  @SuccessResponse('200')
  @Post('/orders/list/export')
  @OperationId('PostWorkstationOrdersListExport')
  @Tags('workstation')
  public async getWorkstationOrdersListExport(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
    @Body()
    paginatedRequest: PaginatedRequestNoDates & {
      columns: {[key: string]: boolean};
    },
  ): Promise<Readable> {
    const workstationService = Container.get(WorkstationService);

    const response = await workstationService.getWorkstationOrdersList({
      ...extractPaginatedRequestNoDates(paginatedRequest),
      startDate,
      endDate,
    });

    const updatedResponse = ExportFormatter.mapResponseToColumns(
      paginatedRequest.columns,
      response.data,
    );

    const buffer = ExportFormatter.generateExcelFile(updatedResponse);

    const today = new Date();
    const formattedDate = `${today.getMonth() + 1}-${today.getDate()}-${today.getFullYear()}`;
    const filename = `workstation_orders_list_${formattedDate}.xlsx`;

    // Set headers for Excel file download
    this.setHeader('Content-Disposition', `attachment; filename=${filename}`);
    this.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );

    // Create a readable stream from the buffer
    const stream = new Readable();
    stream.push(buffer);
    stream.push(null); // Signal the end of the stream
    return stream;
  }
}
