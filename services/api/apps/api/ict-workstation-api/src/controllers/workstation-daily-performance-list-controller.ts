import {
  Body,
  Example,
  Middlewares,
  OperationId,
  Post,
  Query,
  Route,
  SuccessResponse,
  Tags,
} from 'tsoa';
import {
  CacheMiddleware,
  Container,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {WorkstationService} from '../services/workstation-service.ts';
import type {
  WorkstationDailyPerformanceData,
  WorkstationDailyPerformanceList,
  WorkstationDailyPerformanceListRequestBody,
} from '../defs/workstation-daily-performance-list-def.ts';

@Route('/workstation/daily-performance/list')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class WorkstationDailyPerformanceListController {
  static readonly exampleData: WorkstationDailyPerformanceData[] = [
    {
      date: '2024-05-01',
      totalLoggedInHours: 128.1,
      idlePercentage: 7.9,
      starvedPercentage: 10.3,
      starvedHours: 13.2,
      donorContainers: 7880,
      gtpContainers: 4700,
      linesPicked: 10397,
      qtyPerLine: 3.5,
      pickLineQty: 36555,
      linesPerHour: 81.1,
      avgLinesPickedPerHr1stShiftPercentage: 92,
      avgLinesPickedPerHr2ndShiftPercentage: 70,
      retrievalFromDMS: 11012,
      storageToDMS: 11725,
      retrievalFromASRS: 1101,
      storageToASRS: 486,
    },
  ];

  static readonly exampleResponse: WorkstationDailyPerformanceList =
    WorkstationDailyPerformanceListController.exampleData;

  @Example<WorkstationDailyPerformanceList>(
    WorkstationDailyPerformanceListController.exampleResponse
  )
  @SuccessResponse('200')
  @Post('/')
  @OperationId('PostWorkstationDailyPerformanceList')
  @Tags('workstation')
  public async PostWorkstationDailyPerformanceList(
    @Body()
    body: WorkstationDailyPerformanceListRequestBody,
    @Query('start_date')
    startDate: Date,
    @Query('end_date')
    endDate: Date
  ): Promise<WorkstationDailyPerformanceList> {
    const workstationService = Container.get(WorkstationService);

    const workstations = body.workstations ?? [];

    return await workstationService.getWorkstationDailyPerformanceList(
      workstations,
      startDate,
      endDate
    );
  }
}
