import {
  Body,
  Controller,
  Example,
  Middlewares,
  OperationId,
  Post,
  Query,
  Route,
  SuccessResponse,
  Tags,
} from 'tsoa';
import {
  CacheMiddleware,
  Container,
  ExportFormatter,
  extractPaginatedRequestNoDates,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {Readable} from 'stream';
import {WorkstationService} from '../services/workstation-service.ts';
import type {
  WorkstationDailyPerformanceData,
  WorkstationDailyPerformanceList,
  WorkstationDailyPerformanceListRequestBody,
} from '../defs/workstation-daily-performance-list-def.ts';

@Route('/workstation/daily-performance/list')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class WorkstationDailyPerformanceListController extends Controller {
  static readonly exampleData: WorkstationDailyPerformanceData[] = [
    {
      date: '2024-05-01',
      totalLoggedInHours: 128.1,
      idlePercentage: 7.9,
      starvedPercentage: 10.3,
      starvedHours: 13.2,
      donorContainers: 7880,
      gtpContainers: 4700,
      linesPicked: 10397,
      qtyPerLine: 3.5,
      pickLineQty: 36555,
      linesPerHour: 81.1,
      avgLinesPickedPerHr1stShiftPercentage: 92,
      avgLinesPickedPerHr2ndShiftPercentage: 70,
      retrievalFromDMS: 11012,
      storageToDMS: 11725,
      retrievalFromASRS: 1101,
      storageToASRS: 486,
    },
  ];

  static readonly exampleResponse: WorkstationDailyPerformanceList =
    WorkstationDailyPerformanceListController.exampleData;

  @Example<WorkstationDailyPerformanceList>(
    WorkstationDailyPerformanceListController.exampleResponse,
  )
  @SuccessResponse('200')
  @Post('/')
  @OperationId('PostWorkstationDailyPerformanceList')
  @Tags('workstation')
  public async PostWorkstationDailyPerformanceList(
    @Body()
    body: WorkstationDailyPerformanceListRequestBody,
    @Query('start_date')
    startDate: Date,
    @Query('end_date')
    endDate: Date,
  ): Promise<WorkstationDailyPerformanceList> {
    const workstationService = Container.get(WorkstationService);

    const params = {
      ...extractPaginatedRequestNoDates(body),
      workstations: body.workstations ?? [],
      startDate,
      endDate,
    };

    return await workstationService.getWorkstationDailyPerformanceList(params);
  }

  @Post('/export')
  @OperationId('PostWorkstationDailyPerformanceListExport')
  @Tags('workstation')
  public async PostWorkstationDailyPerformanceListExport(
    @Body()
    body: WorkstationDailyPerformanceListRequestBody & {
      columns: {[key: string]: boolean};
      filename: string;
    },
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
  ): Promise<Readable> {
    const workstationService = Container.get(WorkstationService);

    const params = {
      ...extractPaginatedRequestNoDates(body),
      workstations: body.workstations ?? [],
      startDate,
      endDate,
    };

    const response =
      await workstationService.getWorkstationDailyPerformanceList(params);

    const updatedResponse = ExportFormatter.mapResponseToColumns(
      body.columns,
      response,
    );

    const buffer = ExportFormatter.generateExcelFile(updatedResponse);

    // Set headers for Excel file download
    this.setHeader(
      'Content-Disposition',
      `attachment; filename=${body.filename}`,
    );
    this.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );

    // Create a readable stream from the buffer
    const stream = new Readable();
    stream.push(buffer);
    stream.push(null); // Signal the end of the stream
    return stream;
  }
}
