import {
  CacheMiddleware,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Example,
  Get,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Middlewares,
  Controller,
  Query,
} from 'tsoa';
import {WorkstationMetricsService} from '../services/workstation-metrics-service.ts';
import {
  WorkstationHealthStats,
  WorkstationOperatorStats,
  WorkstationPerformanceStats,
  WorkstationStats,
} from '../defs/index.ts';

const dbMiddleware = {
  databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
};

@Route('/workstation/metrics')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, dbMiddleware),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class WorkstationMetricsController extends Controller {
  static readonly exampleWorkstationSummaryResponse: WorkstationStats =
    WorkstationMetricsService.exampleWorkstationStatsResponse;

  static readonly exampleWorkstationOperatorSummaryResponse: WorkstationOperatorStats =
    WorkstationMetricsService.exampleWorkstationOperatorStatsResponse;

  static readonly exampleWorkstationPerformanceSummaryResponse: WorkstationPerformanceStats =
    WorkstationMetricsService.exampleWorkstationPerformanceStatsResponse;

  static readonly exampleWorkstationHealthSummaryResponse: WorkstationHealthStats =
    WorkstationMetricsService.exampleWorkstationHealthStatsResponse;

  /**
   *
   * @returns {Promise<orkstationStationStats>} contract
   * @throws IctError
   */
  @Example<WorkstationStats>(
    WorkstationMetricsController.exampleWorkstationSummaryResponse,
  )
  @SuccessResponse('200')
  @Get('/summary')
  @OperationId('GetWorkstationSummary')
  @Tags('workstation')
  public async GetWorkstationSummary(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
  ): Promise<WorkstationStats> {
    const workstationMetricsService: WorkstationMetricsService = Container.get(
      WorkstationMetricsService,
    );

    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);

    return await workstationMetricsService.getStationSummary(
      startDate.toISOString(),
      endDate.toISOString(),
    );
  }

  /**
   *
   * returns {Promise<WorkstationOperatorStats>} contract
   * @throws IctError
   */
  @Example<WorkstationOperatorStats>(
    WorkstationMetricsController.exampleWorkstationOperatorSummaryResponse,
  )
  @SuccessResponse('200')
  @Get('/operators')
  @OperationId('GetWorkstationOperatorSummary')
  @Tags('workstation')
  public async GetWorkstationOperatorSummary(): Promise<WorkstationOperatorStats> {
    const workstationMetricsService: WorkstationMetricsService = Container.get(
      WorkstationMetricsService,
    );

    return await workstationMetricsService.getOperatorSummary();
  }

  /**
   *
   * returns {Promise<WorkstationPerformanceStats>} contract
   * @throws IctError
   */
  @Example<WorkstationPerformanceStats>(
    WorkstationMetricsController.exampleWorkstationPerformanceSummaryResponse,
  )
  @SuccessResponse('200')
  @Get('/performance')
  @OperationId('GetWorkstationPerformanceSummary')
  @Tags('workstation')
  public async GetWorkstationPerformanceSummary(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
  ): Promise<WorkstationPerformanceStats> {
    const workstationMetricsService: WorkstationMetricsService = Container.get(
      WorkstationMetricsService,
    );

    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);

    return await workstationMetricsService.getPerformanceSummary(
      startDate.toISOString(),
      endDate.toISOString(),
    );
  }

  /**
   *
   * returns {Promise<WorkstationHealthStats>} contract
   * @throws IctError
   */
  @Example<WorkstationHealthStats>(
    WorkstationMetricsController.exampleWorkstationHealthSummaryResponse,
  )
  @SuccessResponse('200')
  @Get('/health')
  @OperationId('GetWorkstationHealthSummary')
  @Tags('workstation')
  public async GetWorkstationHealthSummary(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
  ): Promise<WorkstationHealthStats> {
    const workstationMetricsService: WorkstationMetricsService = Container.get(
      WorkstationMetricsService,
    );

    return await workstationMetricsService.getHealthSummary(
      startDate.toISOString(),
      endDate.toISOString(),
    );
  }
}
