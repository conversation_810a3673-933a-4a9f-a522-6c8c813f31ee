import {
  CacheMiddleware,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  IctError,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Example,
  Get,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Middlewares,
  Query,
} from 'tsoa';
import {WorkstationSeriesData} from '../defs/index.ts';
import {WorkstationSeriesService} from '../services/workstation-series-service.ts';
import {WorkstationSeriesCharts} from '../defs/workstation-series-data.ts';

@Route('/workstation/series')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class WorkstationSeriesController {
  /**
   *
   * @returns {Promise<WorkstationSeriesData>} contract
   * @throws IctError
   */
  @Example<WorkstationSeriesData>(WorkstationSeriesService.exampleSeriesData)
  @SuccessResponse('200')
  @Get('/')
  @OperationId('GetWorkstationSeriesData')
  @Tags('workstation')
  public async GetWorkstationSeriesData(
    @Query() chart: WorkstationSeriesCharts
  ): Promise<WorkstationSeriesData> {
    const workstationSeriesService: WorkstationSeriesService = Container.get(
      WorkstationSeriesService
    );

    const seriesData =
      await workstationSeriesService.getWorkstationSeriesData(chart);

    if (!seriesData || seriesData.data.length === 0) {
      throw IctError.noContent(
        `No series data loaded for chart type ${chart}.`
      );
    }

    return seriesData;
  }
}
