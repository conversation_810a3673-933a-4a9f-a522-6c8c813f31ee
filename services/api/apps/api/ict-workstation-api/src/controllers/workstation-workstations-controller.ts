import {
  CacheMiddleware,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Example,
  Get,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Middlewares,
  Query,
} from 'tsoa';
import {WorkstationService} from '../services/workstation-service.ts';
import {WorkstationStringArray} from '../defs/workstation-list.ts';

@Route('/workstation')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class WorkstationsController {
  public static exampleWorkstations: WorkstationStringArray = [
    'Workstation 1',
    'Workstation 2',
  ];
  @Example<WorkstationStringArray>(WorkstationsController.exampleWorkstations)
  @SuccessResponse('200')
  @Get('/workstations')
  @OperationId('GetWorkstations')
  @Tags('workstation')
  public async getWorkstations(
    @Query('start_date') startDate?: Date,
    @Query('end_date') endDate?: Date
  ): Promise<WorkstationStringArray> {
    const workstationService: WorkstationService =
      Container.get(WorkstationService);

    if (startDate && endDate) startEndDateValidation(startDate, endDate);

    return await workstationService.getWorkstations(startDate, endDate);
  }
}
