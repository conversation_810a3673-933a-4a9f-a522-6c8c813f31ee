import {
  CacheMiddleware,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ExportFormatter,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Example,
  Get,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Middlewares,
  Body,
  Controller,
  Post,
} from 'tsoa';
import {Readable} from 'stream';
import {WorkstationList} from '../defs/workstation-list.ts';
import {WorkstationService} from '../services/workstation-service.ts';

@Route('/workstation')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class WorkstationController extends Controller {
  static readonly exampleWorkstationList: WorkstationList =
    WorkstationService.exampleWorkstationList;

  /**
   *
   * @returns {Promise<WorkstationList>} contract
   * @throws IctError
   */
  @Example<WorkstationList>(WorkstationController.exampleWorkstationList)
  @SuccessResponse('200')
  @Get('/list')
  @OperationId('GetWorkstationList')
  @Tags('workstation')
  public async GetWorkstationList(): Promise<WorkstationList> {
    const workstationService: WorkstationService =
      Container.get(WorkstationService);

    return await workstationService.getStationList();
  }

  @SuccessResponse('200')
  @Post('/list/export')
  @OperationId('PostWorkstationListExport')
  @Tags('workstation')
  public async WorkstationListExport(
    @Body() body: {columns: {[key: string]: boolean}},
  ): Promise<Readable> {
    const workstationService = Container.get(WorkstationService);

    const response = await workstationService.getStationList();

    const updatedResponse = ExportFormatter.mapResponseToColumns(
      body.columns,
      response.workstationList,
    );

    const buffer = ExportFormatter.generateExcelFile(updatedResponse);
    const today = new Date();
    const formattedDate = `${today.getMonth() + 1}-${today.getDate()}-${today.getFullYear()}`;
    const filename = `workstations_list_${formattedDate}.xlsx`;

    // Set headers for Excel file download
    this.setHeader('Content-Disposition', `attachment; filename=${filename}`);
    this.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );

    // Create a readable stream from the buffer
    const stream = new Readable();
    stream.push(buffer);
    stream.push(null); // Signal the end of the stream
    return stream;
  }
}
