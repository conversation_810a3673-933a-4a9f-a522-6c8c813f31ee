import {
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Get,
  Middlewares,
  OperationId,
  Query,
  Route,
  SuccessResponse,
  Tags,
} from 'tsoa';
import {WorkstationService} from '../services/workstation-service.ts';
import {WorkstationOrdersStatusResponse} from '../defs/workstation-orders-status-def.ts';

@Route('/workstation')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
])
export class WorkstationOrdersStatusController extends Controller {
  @SuccessResponse('200')
  @Get('/orders/status')
  @OperationId('GetWorkstationOrdersStatus')
  @Tags('workstation')
  public async getOrdersStatus(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
  ): Promise<WorkstationOrdersStatusResponse> {
    const workstationService = Container.get(WorkstationService);

    return await workstationService.getWorkstationOrdersStatus(
      startDate,
      endDate,
    );
  }
}
