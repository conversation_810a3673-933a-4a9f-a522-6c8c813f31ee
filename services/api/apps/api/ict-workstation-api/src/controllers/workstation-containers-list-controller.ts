import {
  Container,
  DatabaseTypes,
  extractPaginatedRequestNoDates,
  type PaginatedRequestNoDates,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Body,
  Controller,
  Middlewares,
  OperationId,
  Post,
  Query,
  Route,
  SuccessResponse,
  Tags,
} from 'tsoa';
import {WorkstationService} from '../services/workstation-service.ts';
import {
  WorkstationContainersListData,
  WorkstationContainersListQueryParams,
} from '../defs/workstation-containers-list-def.ts';

@Route('workstation')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}],
  }),
])
export class WorkstationContainersListController extends Controller {
  @SuccessResponse('200')
  @Post('/containers/list')
  @OperationId('PostWorkstationContainersList')
  @Tags('workstation')
  public async getWorkstationContainersList(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
    @Query('size') size: string,
    @Query('style') style: string,
    @Query('zone') zone: string,
    @Body() paginatedRequest: PaginatedRequestNoDates,
  ): Promise<WorkstationContainersListData> {
    const workstationService = Container.get(WorkstationService);

    const workstationContainerListParams: WorkstationContainersListQueryParams =
      {
        ...extractPaginatedRequestNoDates(paginatedRequest),
        startDate,
        endDate,
        size,
        style,
        zone,
      };

    const results = await workstationService.getWorkstationContainersList(
      workstationContainerListParams,
    );

    return results;
  }
}
