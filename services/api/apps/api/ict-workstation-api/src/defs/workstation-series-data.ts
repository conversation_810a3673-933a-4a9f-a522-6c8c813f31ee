import {ChartSeriesData} from 'ict-api-foundations';

export interface WorkstationSeriesData {
  data: WorkstationData[];
}

export type WorkstationData = {
  id: string;
  data: ChartSeriesData[];
};

export enum WorkstationSeriesCharts {
  LinesPerHour = 'LinesPerHour',
  QuantityPerHour = 'QuantityPerHour',
  SourceContainersPerHour = 'SourceContainersPerHour',
  DestinationContainersPerHour = 'DestinationContainersPerHour',
  WeightedLinesPerHour = 'WeightedLinesPerHour',
  WeightedQuantityPerHour = 'WeightedQuantityPerHour',
  WeightedSourceContainersPerHour = 'WeightedSourceContainersPerHour',
  WeightedDestinationContainersPerHour = 'WeightedDestinationContainersPerHour',
}
