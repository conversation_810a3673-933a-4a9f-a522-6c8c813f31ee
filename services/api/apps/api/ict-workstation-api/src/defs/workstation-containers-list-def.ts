import {BigQueryDatetime} from '@google-cloud/bigquery';
import {
  ApiResponseArray,
  BaseFilterType,
  ExtractedPaginatedRequestNoDates,
  FilterObjectParameters,
  SortField,
} from 'ict-api-foundations';

export interface WorkstationContainersListItem {
  container: string;
  status: string;
  transport: string;
  quantity: number;
  lastLocation: string;
  eventTime: string;
}

export type WorkstationContainersListData = ApiResponseArray<
  WorkstationContainersListItem[],
  {
    page: number;
    limit: number;
    totalResults: number;
  }
>;

export interface WorkstationContainersListQueryResponse {
  container: string;
  status: string;
  transport: string;
  quantity: number;
  last_location: string;
  event_time: BigQueryDatetime;
}

export interface WorkstationContainersListQueryParams
  extends ExtractedPaginatedRequestNoDates {
  startDate: Date;
  endDate: Date;
  size: string;
  style: string;
  zone: string;
}

export interface WorkstationContainersListParams {
  startDate: Date;
  endDate: Date;
  size: string;
  style: string;
  zone: string;
  limit: number;
  page: number;
  sortFields: SortField[];
  filters?: BaseFilterType;
  filterParams?: FilterObjectParameters;
  searchString?: string;
}
