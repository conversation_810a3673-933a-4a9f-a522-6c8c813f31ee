import {BigQueryDatetime} from '@google-cloud/bigquery';
import {
  BaseFilterType,
  FilterObjectParameters,
  PaginatedRequestNoDates,
  SortField,
} from 'ict-api-foundations';

export type WorkstationDailyPerformanceListRequestBody =
  PaginatedRequestNoDates & {
    workstations?: string[];
  };

export interface WorkstationDailyPerformanceListParams {
  workstations: string[];
  startDate: Date;
  endDate: Date;
  limit: number;
  page: number;
  sortFields: SortField[];
  filters?: BaseFilterType;
  filterParams?: FilterObjectParameters;
  searchString?: string;
}

export interface WorkstationDailyPerformanceListQueryData {
  date: BigQueryDatetime;
  total_logged_in_hours?: number;
  idle_percent?: number;
  starved_percent?: number;
  starved_hours?: number;
  donor_containers?: number;
  gtp_containers?: number;
  lines_picked?: number;
  qty_per_line?: string;
  pick_line_qty?: string;
  lines_per_hour?: string;
  avg_lines_1st_shift?: number;
  avg_lines_2nd_shift?: number;
  retrieval_from_dms?: number;
  storage_to_dms?: number;
  retrieval_from_asrs?: number;
  storage_to_asrs?: number;
}

export interface WorkstationDailyPerformanceData {
  date: string;
  totalLoggedInHours?: number;
  idlePercentage?: number;
  starvedPercentage?: number;
  starvedHours?: number;
  donorContainers?: number;
  gtpContainers?: number;
  linesPicked?: number;
  qtyPerLine?: number;
  pickLineQty?: number;
  linesPerHour?: number;
  avgLinesPickedPerHr1stShiftPercentage?: number;
  avgLinesPickedPerHr2ndShiftPercentage?: number;
  retrievalFromDMS?: number;
  storageToDMS?: number;
  retrievalFromASRS?: number;
  storageToASRS?: number;
}

export type WorkstationDailyPerformanceList = WorkstationDailyPerformanceData[];
