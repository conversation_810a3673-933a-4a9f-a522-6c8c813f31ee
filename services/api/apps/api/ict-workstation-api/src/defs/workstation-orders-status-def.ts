import {BigQueryDatetime} from '@google-cloud/bigquery';
import {
  ApiResponseArray,
  BaseFilterType,
  FilterObjectParameters,
  SortField,
} from 'ict-api-foundations';

export interface WorkstationOrdersStatusQueryResponse {
  activeOrders: number;
  delayedOrders: number;
}

export interface WorkstationOrdersStatusResponse {
  activeOrders: number;
  delayedOrders: number;
}

export interface WorkstationOrdersListQueryParams {
  startDate: Date;
  endDate: Date;
  page: number;
  limit: number;
  sortFields: SortField[];
  filters?: BaseFilterType;
  searchString?: string;
}

export interface WorkstationOrdersListParams {
  startDate: Date;
  endDate: Date;
  limit: number;
  page: number;
  sortFields: SortField[];
  filters?: BaseFilterType;
  filterParams?: FilterObjectParameters;
  searchString?: string;
}

export type WorkstationOrdersListQueryResponse = {
  station: string;
  position: string;
  /** Minutes */
  dwell_time: number;
  order_status: string;
  pick_task: string;
  order_id: string;
  container: string;
  completed_picks: number | null;
  total_picks: number | null;
  arrival_time: BigQueryDatetime;
};

export type WorkstationOrdersListItem = {
  station: string;
  position: string;
  /** Minutes */
  dwellTime: number;
  orderStatus: string;
  pickTask: string;
  orderId: string;
  container: string;
  completedPicks: number | null;
  totalPicks: number | null;
  arrivalTime: string;
};

export type WorkstationOrdersListData = ApiResponseArray<
  WorkstationOrdersListItem[],
  {
    page: number;
    limit: number;
    totalResults: number;
  }
>;
