export type WorkstationStatus = 'Available' | 'Closed' | 'Paused' | 'Unknown';

export type WorkstationMode =
  | 'Consolidation'
  | 'Counting'
  | 'Picking'
  | 'Unknown';

export type WorkstationWorkflowStatus = 'Disabled' | 'Enabled' | 'Unknown';

export type ExcludedWorkstations = {
  workstations: string[];
};

export type Workstation = {
  workstation: string;
  status: WorkstationStatus;
  workMode: WorkstationMode;
  workflowStatus: WorkstationWorkflowStatus;
  operatorId: string;
  activeTimeSecs: number; // number of seconds
  starvedTimeSecs: number; // number of seconds
  idleTimeSecs: number; // number of seconds
  blockedTimeSecs: number; // number of seconds
  quantityPerHour: number;
  weightedQuantityPerHour: number;
  linesPerHour: number;
  weightedLinesPerHour: number;
  donorTotesPerHour: number; // Source
  orderTotesPerHour: number; // Destination
};

export type WorkstationList = {
  workstationList: Workstation[];
};

export type WorkstationListResponse = {
  workstation: string;
  status: string;
  work_mode: string;
  workflow_status: string;
  operator_id: string;
  active_time: number; // number of seconds
  starved_time: number; // number of seconds
  idle_time: number; // number of seconds
  blocked_time: number; // number of seconds
  picks_per_hour: number; // This represents quantity per hour
  weighted_picks_per_hour: number;
  lines_per_hour: number;
  weighted_lines_per_hour: number;
  donor_totes_per_hour: number;
  weighted_donor_totes_per_hour: number;
  order_totes_per_hour: number;
  weighted_order_totes_per_hour: number;
};

export type WorkstationStringArray = string[];
