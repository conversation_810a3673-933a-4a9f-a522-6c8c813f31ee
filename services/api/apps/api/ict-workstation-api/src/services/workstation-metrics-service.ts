import {Container, DiSer<PERSON>, WinstonLogger} from 'ict-api-foundations';
import {
  WorkstationModeStats,
  WorkstationStats,
  WorkstationStatusStats,
} from '../defs/workstation-stats.ts';
import {WorkstationMetricsStore} from '../stores/workstation-metrics-store.ts';
import {WorkstationOperatorStats} from '../defs/workstation-operator-stats.ts';
import {WorkstationPerformanceStats} from '../defs/workstation-performance-stats.ts';
import {WorkstationHealthStats} from '../defs/workstation-health-stats.ts';

@DiService()
export class WorkstationMetricsService {
  constructor(
    private workstationMetricsStore: WorkstationMetricsStore,
    private logger: WinstonLogger,
  ) {
    this.logger = Container.get(WinstonLogger);
    this.workstationMetricsStore = workstationMetricsStore;
    this.logger.info('WorkstationMetricsService initialized');
  }

  static readonly exampleWorkstationStatsResponse: WorkstationStats = {
    totalWorkstations: 20,
    status: [
      {
        count: 1,
        type: 'Active',
      },
      {
        count: 2,
        type: 'Inactive',
      },
    ],
    workstationMode: [
      {
        count: 1,
        type: 'Picking',
      },
      {
        count: 2,
        type: 'Other',
      },
    ],
  };

  static readonly exampleWorkstationOperatorStatsResponse: WorkstationOperatorStats =
    {
      totalOperators: 10,
      actualLinesPerHour: 10,
      targetLinesPerHour: 20,
    };

  static readonly exampleWorkstationPerformanceStatsResponse: WorkstationPerformanceStats =
    {
      totalActiveTime: 250,
      totalStarvationTime: 13,
    };

  static readonly exampleWorkstationHealthStatsResponse: WorkstationHealthStats =
    {
      totalDowntime: 9,
    };

  async getStationSummary(
    startDate: string,
    endDate: string,
  ): Promise<WorkstationStats> {
    const queryResponse =
      await this.workstationMetricsStore.getWorkstationStats(
        startDate,
        endDate,
      );

    const totalStations = queryResponse.totalWorkstations;
    const activeStations = queryResponse.active;
    const mode = queryResponse.workstationMode;

    const workstationModeStats: WorkstationModeStats[] = mode
      // Prevent random ordering, ICT-4834
      .toSorted((a, b) => {
        // Always have "Other" as the last mode in the list
        if (a.type === 'Other') return 1;
        if (b.type === 'Other') return -1;
        return a.type.localeCompare(b.type);
      })
      .map(datum => ({
        type: datum.type,
        count: datum.count,
      }));

    const workstationStatusStats: WorkstationStatusStats[] = [
      {
        type: 'Active',
        count: activeStations,
      },
      {
        type: 'Inactive',
        count: totalStations - activeStations,
      },
    ];

    const workstationStats: WorkstationStats = {
      totalWorkstations: totalStations,
      status: workstationStatusStats,
      workstationMode: workstationModeStats,
    };

    return workstationStats;
  }

  async getOperatorSummary(): Promise<WorkstationOperatorStats> {
    return await this.workstationMetricsStore.getWorkstationOperatorStats();
  }

  async getPerformanceSummary(
    startDate: string,
    endDate: string,
  ): Promise<WorkstationPerformanceStats> {
    return await this.workstationMetricsStore.getWorkstationPerformanceStats(
      startDate,
      endDate,
    );
  }

  async getHealthSummary(
    startDate: string,
    endDate: string,
  ): Promise<WorkstationHealthStats> {
    return await this.workstationMetricsStore.getWorkstationHealthStats(
      startDate,
      endDate,
    );
  }
}
