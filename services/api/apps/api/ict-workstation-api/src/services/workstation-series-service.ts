import {Container, DiService, <PERSON><PERSON><PERSON>ger} from 'ict-api-foundations';
import {WorkstationSeriesData} from '../defs/index.ts';
import {WorkstationSeriesStore} from '../stores/workstation-series-store.ts';
import {
  WorkstationData,
  WorkstationSeriesCharts,
} from '../defs/workstation-series-data.ts';

@DiService()
export class WorkstationSeriesService {
  constructor(
    private workstationSeriesStore: WorkstationSeriesStore,
    private logger: <PERSON>Logger
  ) {
    this.logger = Container.get(WinstonLogger);
    this.workstationSeriesStore = workstationSeriesStore;
    this.logger.info('WorkstationSeriesService initialized');
  }

  static readonly exampleSeriesData: WorkstationSeriesData = {
    data: [
      {
        id: 'Station 1',
        data: [
          {name: 'Item 1', value: 2},
          {name: 'Item 2', value: 39},
          {name: 'Item 3', value: 84},
          {name: 'Item 4', value: 30},
          {name: 'Item 5', value: 23},
          {name: 'Item 6', value: 46},
          {name: 'Item 7', value: 48},
          {name: 'Item 8', value: 69},
          {name: 'Item 9', value: 37},
          {name: 'Item 10', value: 83},
        ],
      },
    ],
  };

  async getWorkstationSeriesData(
    seriesType: WorkstationSeriesCharts
  ): Promise<WorkstationSeriesData> {
    const queryResponse: WorkstationData[] =
      await this.workstationSeriesStore.getWorkstationSeriesChartData(
        seriesType
      );

    const response = queryResponse.map(item => ({
      id: item.id,
      data: item.data.map(datum => ({
        // Timestamp comes back in the following format: 2024-12-17 13:20:00+00
        // Convert to a format our charts can process: 2024-12-17T13:20:00.000Z
        name: new Date(datum.name).toISOString(),
        value: datum.value,
      })),
    }));

    return {data: response};
  }
}
