import {Container, DiService, WinstonLogger} from 'ict-api-foundations';
import {DateTime} from 'luxon';
import {randomInt} from 'crypto';
import {
  Workstation,
  WorkstationList,
  WorkstationMode,
  WorkstationStatus,
  WorkstationWorkflowStatus,
} from '../defs/workstation-list.ts';
import {WorkstationStore} from '../stores/workstation-store.ts';
import {WorkstationDailyPerformanceData} from '../defs/workstation-daily-performance-list-def.ts';
import {WorkstationOrdersDetailsPicksListQueryParams} from '../defs/workstation-orders-details-picks-list.def.ts';
import {WorkstationOrdersListQueryParams} from '../defs/workstation-orders-status-def.ts';
import {WorkstationContainersListQueryParams} from '../defs/workstation-containers-list-def.ts';

@DiService()
export class WorkstationService {
  constructor(
    private workstationStore: WorkstationStore,
    private logger: <PERSON>Logger,
  ) {
    this.logger = Container.get(WinstonLogger);
    this.workstationStore = workstationStore;
    this.logger.info('WorkstationService initialized');
  }

  static readonly exampleWorkstationList: WorkstationList = {
    workstationList: [
      WorkstationService.generateWorkstation('Station 1'),
      WorkstationService.generateWorkstation('Station 2'),
      WorkstationService.generateWorkstation('Station 3'),
      WorkstationService.generateWorkstation('Station 4'),
      WorkstationService.generateWorkstation('Station 5'),
    ],
  };

  static generateWorkstation(name: string): Workstation {
    const activeTime = randomInt(1440);
    const blockedTime = randomInt(1440 - activeTime);
    const idleTime = randomInt(1440 - blockedTime);
    const starvedTime = randomInt(1440 - idleTime);

    let status: WorkstationStatus;
    const random = Math.random();
    if (random > 0.5) {
      status = 'Available';
    } else if (random > 0.25) {
      status = 'Closed';
    } else {
      status = 'Paused';
    }

    let workMode: WorkstationMode;
    const modeRandom = Math.random();
    if (modeRandom > 0.75) {
      workMode = 'Consolidation';
    } else if (modeRandom > 0.5) {
      workMode = 'Counting';
    } else {
      workMode = 'Picking';
    }

    return {
      activeTimeSecs: activeTime,
      blockedTimeSecs: blockedTime,
      donorTotesPerHour: randomInt(50),
      orderTotesPerHour: randomInt(50),
      idleTimeSecs: idleTime,
      linesPerHour: randomInt(50),
      operatorId: `Operator ${randomInt(50)}`,
      quantityPerHour: randomInt(50),
      starvedTimeSecs: starvedTime,
      status,
      weightedLinesPerHour: randomInt(50),
      weightedQuantityPerHour: randomInt(50),
      workflowStatus: Math.random() > 0.2 ? 'Enabled' : 'Disabled',
      workstation: name,
      workMode,
    };
  }

  async getWorkstationContainersList(
    params: WorkstationContainersListQueryParams,
  ) {
    const results =
      await this.workstationStore.getWorkstationContainersList(params);

    const list = results.list.map(item => ({
      container: item.container,
      status: item.status,
      transport: item.transport,
      quantity: item.quantity,
      lastLocation: item.last_location,
      eventTime: item.event_time.value,
    }));

    const meta = {
      page: params.page,
      limit: params.limit,
      totalResults: results.totalResults,
    };

    return {
      data: list,
      metadata: meta,
    };
  }

  async getStationList(): Promise<WorkstationList> {
    const queryResponse = await this.workstationStore.getWorkstationList();

    const workstationListing = queryResponse.map(datum => ({
      workstation: datum.workstation,
      status: WorkstationService.getWorkstationStatus(datum.status),
      workMode: WorkstationService.getWorkstationMode(datum.work_mode),
      workflowStatus: WorkstationService.getWorkstationWorkflowStatus(
        datum.workflow_status,
      ),
      operatorId: datum.operator_id,
      activeTimeSecs: datum.active_time,
      starvedTimeSecs: datum.starved_time,
      idleTimeSecs: datum.idle_time,
      blockedTimeSecs: datum.blocked_time,
      quantityPerHour: datum.picks_per_hour,
      weightedQuantityPerHour: datum.weighted_picks_per_hour,
      linesPerHour: datum.lines_per_hour,
      weightedLinesPerHour: datum.weighted_lines_per_hour,
      donorTotesPerHour: datum.donor_totes_per_hour,
      orderTotesPerHour: datum.order_totes_per_hour,
    }));

    const response: WorkstationList = {
      workstationList: workstationListing,
    };

    return response;
  }

  async getWorkstationOrdersList(params: WorkstationOrdersListQueryParams) {
    const results =
      await this.workstationStore.getWorkstationOrdersList(params);

    const list = results.list.map(item => ({
      station: item.station,
      position: item.position,
      dwellTime: item.dwell_time,
      orderStatus: item.order_status,
      pickTask: item.pick_task,
      orderId: item.order_id,
      container: item.container,
      completedPicks: item.completed_picks,
      totalPicks: item.total_picks,
      arrivalTime: item.arrival_time.value,
    }));

    const config = {
      page: params.page,
      limit: params.limit,
      totalResults: results.totalResults,
    };

    return {
      data: list,
      metadata: config,
    };
  }

  async getWorkstationOrdersStatus(startDate: Date, endDate: Date) {
    return await this.workstationStore.getWorkstationOrdersStatus(
      startDate,
      endDate,
    );
  }

  async getWorkstationOrdersDetailsPicksList(
    params: WorkstationOrdersDetailsPicksListQueryParams,
  ) {
    const results =
      await this.workstationStore.getWorkstationOrdersDetailsPicksList(params);

    const list = results.list.map(item => ({
      container: item.container,
      status: item.status,
      orderLine: item.order_line,
      style: item.style,
      size: item.size,
      qtyOrdered: item.qty_ordered,
      qtyPicked: item.qty_picked,
      containers: item.containers,
    }));

    const meta = {
      orderId: params.orderId,
      page: params.page,
      limit: params.limit,
      totalResults: results.totalResults,
    };

    return {
      data: {
        deliveryNumber: results.list[0].delivery_number,
        tableData: list,
      },
      metadata: meta,
    };
  }

  async getWorkstationDailyPerformanceList(
    workstations: string[],
    startDate: Date,
    endDate: Date,
  ): Promise<WorkstationDailyPerformanceData[]> {
    const data = (
      await this.workstationStore.getWorkstationDailyPerformanceList(
        workstations,
        startDate,
        endDate,
      )
    ).map(datum => ({
      date: datum.date.value,
      totalLoggedInHours: datum.total_logged_in_hours,
      idlePercentage: datum.idle_percent,
      starvedPercentage: datum.starved_percent,
      starvedHours: datum.starved_hours,
      donorContainers: datum.donor_containers,
      gtpContainers: datum.gtp_containers,
      linesPicked: datum.lines_picked,
      qtyPerLine: Number(datum.qty_per_line),
      pickLineQty: Number(datum.pick_line_qty),
      linesPerHour: Number(datum.lines_per_hour),
      avgLinesPickedPerHr1stShiftPercentage: datum.avg_lines_1st_shift,
      avgLinesPickedPerHr2ndShiftPercentage: datum.avg_lines_2nd_shift,
      retrievalFromDMS: datum.retrieval_from_dms,
      storageToDMS: datum.storage_to_dms,
      retrievalFromASRS: datum.retrieval_from_asrs,
      storageToASRS: datum.storage_to_asrs,
    }));
    return data;
  }

  public async getWorkstations(
    startDate?: Date,
    endDate?: Date,
  ): Promise<string[]> {
    const normalizedEndDate = new Date(endDate ?? new Date());
    const normalizedStartDate = new Date(
      startDate ??
        DateTime.fromJSDate(normalizedEndDate).minus({days: 30}).toJSDate(),
    );
    const workstations = await this.workstationStore.getWorkstations(
      normalizedStartDate,
      normalizedEndDate,
    );
    return workstations.map(workstation => workstation.workstation);
  }

  static getWorkstationStatus(status: string): WorkstationStatus {
    switch (status) {
      case 'AVAILABLE':
        return 'Available';
      case 'PAUSED':
        return 'Paused';
      case 'CLOSED':
        return 'Closed';
      default:
        return 'Unknown';
    }
  }

  static getWorkstationMode(status: string): WorkstationMode {
    switch (status) {
      case 'COUNTING':
        return 'Counting';
      case 'PICKING':
        return 'Picking';
      case 'CONSOLIDATION':
        return 'Consolidation';
      default:
        return 'Unknown';
    }
  }

  static getWorkstationWorkflowStatus(
    status: string,
  ): WorkstationWorkflowStatus {
    switch (status) {
      case 'ENABLED':
        return 'Enabled';
      case 'DISABLED':
        return 'Disabled';
      default:
        return 'Unknown';
    }
  }
}
