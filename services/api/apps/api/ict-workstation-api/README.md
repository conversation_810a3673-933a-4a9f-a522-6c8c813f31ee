# ICT Workstation API

## Overview

The ICT Workstation API provides workstation metrics and control capabilities for the Control Tower platform. This service enables monitoring and management of workstation performance, productivity, and operational metrics.

## Purpose

This API serves as the logic layer for handling business logic for workstation metrics and workstation control within Control Tower, providing comprehensive workstation monitoring and analytics capabilities.

## Key Features

- **Workstation Monitoring**: Real-time monitoring of workstation performance
- **Performance Metrics**: Track productivity and efficiency metrics
- **Order Management**: Monitor order processing at workstations
- **Container Management**: Track container movements and status
- **Daily Performance**: Aggregate daily performance statistics
- **Series Analytics**: Time-series analysis of workstation data

## API Endpoints

### Health Check

- `GET /health/basic` - Basic service health check

### Workstation Management

- `GET /workstations` - List all workstations
- `GET /workstations/{id}` - Get specific workstation details
- `POST /workstations` - Create new workstation
- `PUT /workstations/{id}` - Update workstation
- `DELETE /workstations/{id}` - Delete workstation

### Performance Metrics

- `GET /workstations/{id}/metrics` - Get workstation metrics
- `GET /workstations/{id}/daily-performance` - Get daily performance data
- `GET /workstations/{id}/series` - Get time-series data

### Order Management

- `GET /workstations/{id}/orders` - Get workstation orders
- `GET /workstations/{id}/orders/{orderId}/details` - Get order details
- `GET /workstations/{id}/orders/{orderId}/picks` - Get order picks
- `GET /workstations/{id}/orders/status` - Get order status

### Container Management

- `GET /workstations/{id}/containers` - Get workstation containers

## Architecture

The service follows the standard Control Tower API architecture:

```bash
src/
├── controllers/     # TSOA controllers with route definitions
│   ├── workstation-controller.ts
│   ├── workstation-health-check-controller.ts
│   ├── workstation-metrics-controller.ts
│   ├── workstation-workstations-controller.ts
│   ├── workstation-daily-performance-list-controller.ts
│   ├── workstation-series-controller.ts
│   ├── workstation-orders-list-controller.ts
│   ├── workstation-orders-details-picks-list-controller.ts
│   ├── workstation-orders-status-controller.ts
│   └── workstation-containers-list-controller.ts
├── services/        # Business logic services
├── stores/          # Data access layer
├── defs/           # TypeScript type definitions
├── test/           # Unit tests
└── index.ts        # Service entry point
```

## Dependencies

- **Database**: PostgreSQL for storing workstation data and metrics
- **Redis**: Caching for real-time metrics and performance data
- **Enterprise Search**: For advanced search capabilities
- **BigQuery**: Analytics database for large-scale metrics processing

## Environment Variables

- `AUTH_AUDIENCE`: Authentication audience for JWT validation
- `AUTH_DOMAIN`: Auth0 domain for authentication
- `ENTERPRISE_SEARCH_SECRETS_NAME`: Secret name for enterprise search credentials
- `ENTERPRISE_SEARCH_URL`: Enterprise search service URL
- `DATABASE_URL`: PostgreSQL connection string
- `REDIS_URL`: Redis connection string

## Development

### Prerequisites

- Node.js >= 22.0.0
- Yarn package manager
- PostgreSQL database
- Redis instance
- Enterprise search access

### Local Development

```bash
# Install dependencies
yarn install

# Start development server
yarn watch

# Run tests
yarn test

# Build for production
yarn build
```

### Testing

```bash
# Run all tests
yarn test

# Run tests in watch mode
yarn test:watch

# Run linting
yarn lint
```

## Deployment

The service is containerized using Docker and can be deployed to Google Cloud Run or other container platforms.

### Docker Build

```bash
docker build -t ict-workstation-api .
```

### Environment Configuration

Ensure all required environment variables are configured for the deployment environment.

## Monitoring

- **Health Checks**: Available at `/health/basic`
- **Metrics**: Service metrics exported for monitoring
- **Logging**: Structured JSON logging with correlation IDs

## Use Cases

### Operations Management

- Real-time workstation monitoring
- Performance optimization
- Capacity planning
- Resource allocation

### Analytics and Reporting

- Productivity analysis
- Performance trending
- Efficiency reporting
- Benchmark comparisons

### Quality Control

- Order accuracy monitoring
- Pick performance tracking
- Error rate analysis
- Quality metrics

## Metrics Types

### Performance Metrics

- Orders processed per hour
- Pick accuracy rates
- Cycle times
- Utilization rates

### Operational Metrics

- Workstation availability
- Downtime tracking
- Maintenance schedules
- Resource utilization

### Quality Metrics

- Error rates
- Rework percentages
- Customer satisfaction
- Compliance metrics

## Related Services

- **ict-orders-api**: Order data for workstation processing
- **ict-operators-api**: Operator assignments and performance
- **ict-equipment-api**: Equipment status and maintenance
- **ict-inventory-api**: Inventory levels and availability
- **ict-dataexplorer-api**: Data exploration and analytics
