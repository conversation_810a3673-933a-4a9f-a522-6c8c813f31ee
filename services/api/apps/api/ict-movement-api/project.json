{"name": "ict-movement-api", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/ict-movement-api/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "ict-movement-api:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "ict-movement-api:build:development"}, "production": {"buildTarget": "ict-movement-api:build:production"}}}}}