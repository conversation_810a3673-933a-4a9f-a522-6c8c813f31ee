import {BaseHealthCheck} from 'ict-api-foundations';
import {Get, Route, OperationId, Tags} from 'tsoa';

@Route('/movement/healthcheck')
export class HealthCheckController {
  @Get()
  @OperationId('GetMovementBasicHealthCheck')
  @Tags('movement')
  public getMovementBasicHealthCheck() {
    return BaseHealthCheck.getBasicHealthCheck();
  }

  @Get('/full')
  @OperationId('GetMovementFullHealthCheck')
  @Tags('movement')
  public getMovementFullHealthCheck() {
    return BaseHealthCheck.getDeepHealthCheck(['auth0', 'redis']);
  }
}
