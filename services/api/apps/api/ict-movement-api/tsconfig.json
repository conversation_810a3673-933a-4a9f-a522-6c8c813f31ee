{
  "extends": "../../../node_modules/gts/tsconfig-google.json",
  "compilerOptions": {
    // Node Target Mapping: https://github.com/microsoft/TypeScript/wiki/Node-Target-Mapping
    "lib": ["ES2023"],
    "module": "ES2022",
    "target": "ES2022",

    // Not transpiling with Typescript (tsc)
    "allowImportingTsExtensions": true,
    "moduleResolution": "Bundler",
    "noEmit": true,

    "esModuleInterop": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    // Workaround for https://github.com/typestack/class-validator/issues/1947, cause is from ict--api-foundations package
    "skipLibCheck": true
  },
  "include": ["src/**/*.ts", "src/test/**/*spec.ts"],
  "exclude": ["node_modules"]
}
