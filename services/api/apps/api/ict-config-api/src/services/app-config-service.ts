import {DiService} from 'ict-api-foundations';
import * as pathResolver from 'path';
import {AppConfigStorageBucket} from '../stores/app-config-storage.ts';

const APP_CONFIG_BUCKET_PATH = 'config/app-config';
const APP_CONFIG_FILE = 'app-config.json';

/**
 * Service that handles proxying calls to config api.
 */
@DiService()
export class AppConfigService {
  constructor(private appConfigStorageBucket: AppConfigStorageBucket) {}

  /**
   * Get's the app-config file data
   * @param config query 'config' parameter value, if requested.
   * @returns The contents of the app-config file.
   */
  public async getAppConfigData(config?: unknown): Promise<string> {
    // Initialize storage client
    const bucket = this.appConfigStorageBucket;
    const filePath = this.getConfigFilePath(config);

    let healthCheck: boolean = await bucket.isAvailable();
    if (!healthCheck) {
      throw Error('App Config Storage Bucket Not Available!');
    }

    healthCheck = await bucket.fileExists(filePath);
    if (!healthCheck) {
      throw Error(`${filePath} File not found Storage Bucket!`);
    }

    // Download the config file from the bucket...
    const contents = await bucket.fileDownload(filePath);
    return contents.toString();
  }

  private getConfigFilePath(config?: unknown): string {
    if (config) {
      return pathResolver
        .join(APP_CONFIG_BUCKET_PATH, `${config}.json`)
        .replace(/\\/g, '/');
    }

    return pathResolver
      .join(APP_CONFIG_BUCKET_PATH, APP_CONFIG_FILE)
      .replace(/\\/g, '/');
  }
}
