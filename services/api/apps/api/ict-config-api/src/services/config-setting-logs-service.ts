import {
  DiService,
  AppConfigSettingLog,
  IctError,
  ConfigStore,
  ContextService,
} from 'ict-api-foundations';
import {SettingLog} from 'ict-api-schema';

@DiService()
export class ConfigSettingLogsService {
  public static type = 'config-setting-logs-service';
  constructor(
    private configStore: ConfigStore,
    private context: ContextService,
  ) {}

  public async getSettingLogs(
    settingId: string,
    limit: number,
  ): Promise<AppConfigSettingLog[]> {
    const rawSettingLogs: SettingLog[] = await this.configStore.findSettingLogs(
      this.context.userId,
      settingId,
      limit,
      this.context.selectedFacilityId ?? '',
    );
    if (rawSettingLogs.length === 0)
      throw IctError.noContent('There are no logs for this setting.');
    const formattedSettingLogs: AppConfigSettingLog[] = rawSettingLogs.map(
      rawSettingLog => {
        return {
          changedBy: rawSettingLog.changedBy,
          timestamp: rawSettingLog.timestamp,
          source: rawSettingLog.source,
          newValue: rawSettingLog.newValue,
        };
      },
    );

    return formattedSettingLogs;
  }
}
