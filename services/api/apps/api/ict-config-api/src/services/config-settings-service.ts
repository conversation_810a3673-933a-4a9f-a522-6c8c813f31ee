/* eslint-disable no-await-in-loop */
import {
  ConfigStore,
  ContextService,
  DiService,
  IctError,
  WinstonLogger,
  AppConfigSetting,
  AppConfigSettingSource,
} from 'ict-api-foundations';
import {
  Setting,
  StoredSetting,
  TenantSetting,
  FacilitySetting,
  UserSetting,
  DefaultConfigSettings,
} from 'ict-api-schema';
import {
  DefaultConfigSeedingResult,
  DeletedRecordsResult,
  UpdateOrCreateSettingOutcome,
  UpdateSettingData,
} from '../defs/app-configuration-setting.ts';

@DiService()
export class ConfigSettingsService {
  public static type = 'config-settings-service';
  constructor(
    private configStore: ConfigStore,
    private logger: WinstonLogger,
    private context: ContextService,
  ) {}

  /**
   * Iterates through all of the default settings that are defined in code and inserts them into the
   * config DB as default Settings.  First checks to ensure there are no existing settings with
   * the same name before inserting.
   * @returns an object describing the outcome: a list of successful inserts, a list of unsuccessful transactions,
   *    and a list of settings that were already in the database
   */
  async setDefaultConfigSettings(): Promise<DefaultConfigSeedingResult> {
    const successfulSettings: Setting[] = [];
    const unsuccessfulSettings: Setting[] = [];
    const existingSettings: Setting[] = [];

    const defaultConfigItems = this.getDefaultConfig();
    // Iterates through the default config objects and inserts them into the database if they don't exist yet
    for (let i = 0; i < defaultConfigItems.length; i++) {
      // Check to see if the setting exists before inserting it
      const existingSetting = await this.configStore.findSettingByName(
        defaultConfigItems[i].name,
      );
      this.logger.info(
        `${
          existingSetting
            ? `Found existing setting with Id: ${existingSetting.id}`
            : "Didn't find an existing setting"
        }`,
      );
      if (!existingSetting) {
        // The setting doesn't exist. Attempt to insert it
        try {
          const newSetting = await this.configStore.saveSetting(
            defaultConfigItems[i],
          );
          this.logger.info(`New setting ID is: ${newSetting.id}`);
          defaultConfigItems[i].id = newSetting.id;
          successfulSettings.push(newSetting);
        } catch (e: unknown) {
          this.logger.error(
            'Error attempting to save a new setting.',
            e as Error,
          );
          unsuccessfulSettings.push(defaultConfigItems[i]);
        }
      } else {
        existingSettings.push(existingSetting);
      }
    }

    return {
      successful: this.convertSettingValueToRelevantSetting(successfulSettings),
      unsuccessful:
        this.convertSettingValueToRelevantSetting(unsuccessfulSettings),
      existing: this.convertSettingValueToRelevantSetting(existingSettings),
    };
  }

  /**
   * Retrieves a single stored setting from the database based on the setting name and user Id.
   * If a type is specified, that specific setting value is returned if it exists.
   * If a type is not specified, checks for a setting in the user setting table first, then the tenant
   * setting table, then the default value on the setting if neither of those have a setting
   * @param settingId the setting id
   * @param settingName the setting name
   * @param settingType the string value passed in to be converted to a SettingValueSource enum.  If no match is found, ignore it
   * @returns the setting and value in a readable format
   */
  async getSingleSetting(
    settingId?: string,
    settingName?: string,
    settingType?: string,
    allStoredValues = false,
  ): Promise<AppConfigSetting | AppConfigSetting[] | undefined> {
    if (settingType) {
      const sourceType: AppConfigSettingSource =
        AppConfigSettingSource[
          settingType as keyof typeof AppConfigSettingSource
        ];
      if (sourceType) {
        const specificSetting: AppConfigSetting | undefined =
          await this.configStore.findSpecificSettingValue(
            sourceType,
            settingId,
            settingName,
            this.context.userId,
          );
        return specificSetting;
      }
    }
    if (allStoredValues) {
      // get all the setting values
      // get the default value
      const defaultSetting = await this.configStore.findSpecificSettingValue(
        AppConfigSettingSource.default,
        settingId,
        settingName,
      );
      if (!defaultSetting) return undefined;

      // if there's a default setting, get the tenant and user setting values
      const tenantSetting = await this.configStore.findTenantSetting(
        defaultSetting.id,
      );

      const facilitySetting = await this.configStore.findFacilitySetting(
        defaultSetting.id,
      );

      const userSetting = await this.configStore.findUserSetting(
        this.context.userId,
        defaultSetting.id,
      );

      // then convert them into the expected format
      const allSettingValues = [];
      if (tenantSetting) allSettingValues.push(tenantSetting);
      if (facilitySetting) allSettingValues.push(facilitySetting);
      if (userSetting) allSettingValues.push(userSetting);
      return [
        defaultSetting,
        ...this.convertSettingValueToRelevantSetting(allSettingValues),
      ];
    }
    const foundSetting = await this.configStore.findMostRelevantSettingForUser(
      this.context.userId,
      settingId,
      settingName,
    );
    return foundSetting;
  }

  /**
   * Method to retrieve all settings and their values
   * @returns a list of all relevant settings
   */
  public async getAllSettings(
    group?: string,
    allStoredValues: boolean = false,
  ): Promise<AppConfigSetting[]> {
    const allSettings: StoredSetting[] = [];
    const currentUser = this.context.userId;
    this.logger.info(`User is ${currentUser}`);
    if (currentUser !== null) {
      const userSettings = await this.configStore.findAllUserSettings(
        currentUser,
        group,
      );
      if (userSettings) allSettings.push(...userSettings);
    }

    const facilityId = this.context.selectedFacilityId;
    this.logger.info(`Selected Facility is: ${facilityId}`);
    if (facilityId !== null) {
      const facilitySettings =
        await this.configStore.findAllFacilitySettings(group);
      if (facilitySettings) {
        if (!allStoredValues) {
          const relevantSettings = facilitySettings.filter(facilitySetting => {
            return (
              allSettings.find(
                setting => setting.settingId === facilitySetting.settingId,
              ) === undefined
            );
          });

          allSettings.push(...relevantSettings);
        } else {
          allSettings.push(...facilitySettings);
        }
      }
    }

    const tenantSettings = await this.configStore.findAllTenantSettings(group);
    if (tenantSettings) {
      if (!allStoredValues) {
        // only keep tenant settings that don't already have a user setting in the list
        const relevantSettings = tenantSettings.filter(tenantSetting => {
          return (
            allSettings.find(
              setting => setting.settingId === tenantSetting.settingId,
            ) === undefined
          );
        });

        allSettings.push(...relevantSettings);
      } else {
        allSettings.push(...tenantSettings);
      }
    }

    const defaultSettings: Setting[] | undefined =
      await this.configStore.findAllSettings(group);
    let relevantDefaultSettings: AppConfigSetting[] = [];
    if (defaultSettings) {
      if (!allStoredValues) {
        // Remove the Settings from the list that we've already found a more specific setting value for
        relevantDefaultSettings = this.convertSettingValueToRelevantSetting(
          defaultSettings.filter(defSetting => {
            return (
              allSettings.find(
                setting => setting.settingId === defSetting.id,
              ) === undefined
            );
          }),
        );
      } else {
        // Add all the default Settings to the list
        relevantDefaultSettings =
          this.convertSettingValueToRelevantSetting(defaultSettings);
      }
    }

    const friendlySettings =
      this.convertSettingValueToRelevantSetting(allSettings);
    // Need to include the default settings as well
    friendlySettings.push(...relevantDefaultSettings);

    return friendlySettings;
  }

  /**
   * Updates a setting value given a setting ID or setting name.  If the setting doesn't exist, one is created
   * First checks if a setting exists for the given id; if no ID is passed, use the name
   * If a setting is found, update the value for the specified level to update (default, tenant, or user)
   * If a setting is not found, checks if the body has the minimum required values to create a new setting
   * @param settingUpdates the object containing the identifying setting info and update value
   * @returns the updated or created setting
   */
  async updateOrCreateSetting(
    settingUpdates: UpdateSettingData,
  ): Promise<UpdateOrCreateSettingOutcome> {
    if (!settingUpdates.levelToUpdate)
      throw IctError.badRequest("Missing required property 'levelToUpdate'!");

    let existingSetting: Setting | undefined;
    if (settingUpdates.id) {
      this.logger.debug(
        `Searching for an existing setting with ID ${settingUpdates.id}`,
      );
      existingSetting = await this.configStore.findSettingById(
        settingUpdates.id,
      );
      if (!existingSetting) {
        // if the caller specified an ID and it doesn't exist, then return a failure
        throw IctError.notFound(
          `Couldn't find a setting with ID ${settingUpdates.id} to update.  To create a new setting, remove the ID.`,
        );
      }
    } else if (settingUpdates.name) {
      this.logger.debug(
        `Searching for an existing setting with name ${settingUpdates.name}`,
      );
      existingSetting = await this.configStore.findSettingByName(
        settingUpdates.name,
      );
    } else {
      throw IctError.badRequest(
        'To update a setting, either an ID or Name property must be specified.',
      );
    }

    const createdNewSetting: boolean = !existingSetting;
    // An existing setting wasn't found.  Create it
    if (!existingSetting) {
      this.logger.info(
        'An existing setting was not found.  A new setting must be inserted.',
      );
      if (!settingUpdates.name)
        throw IctError.unprocessableRequest(
          "Missing required property 'name'!",
        );
      const newSetting: Setting = new Setting();
      newSetting.name = settingUpdates.name;
      newSetting.dataType = settingUpdates.dataType;
      if (settingUpdates.description)
        newSetting.description = settingUpdates.description;
      if (settingUpdates.levelToUpdate === 'default') {
        newSetting.defaultValue = settingUpdates.value;
        newSetting.group = settingUpdates.group ?? null;
      }
      existingSetting = await this.configStore.saveSetting(newSetting);
      this.logger.debug(
        `Created new setting: ${existingSetting.id} - ${existingSetting.name}`,
      );
      // we can return now if we're only updating the default value
      if (settingUpdates.levelToUpdate === 'default')
        return {
          setting:
            this.convertSettingValueToRelevantSetting(existingSetting)[0],
          created: true,
        };
    }

    let updatedSetting;
    this.logger.info(
      `Updating ${settingUpdates.levelToUpdate} setting value for existing setting: ${existingSetting.id} - ${existingSetting.name}`,
    );

    if (settingUpdates.description)
      existingSetting.description = settingUpdates.description;
    existingSetting.name = settingUpdates.name;
    // need to update the existing setting with only the new updates
    switch (settingUpdates.levelToUpdate) {
      case 'default': {
        // simply update the default value
        existingSetting.defaultValue = settingUpdates.value;
        existingSetting.group =
          settingUpdates.group ?? existingSetting.group ?? null;
        updatedSetting = await this.configStore.saveSetting(existingSetting);
        return {
          setting: this.convertSettingValueToRelevantSetting(updatedSetting)[0],
          created: createdNewSetting,
        };
      }
      case 'tenant': {
        // find the tenant setting and update it, or create a new one
        const tenantSettingToUpdate = await this.configStore.findTenantSetting(
          existingSetting.id,
        );
        if (tenantSettingToUpdate) {
          this.logger.info(
            `Updating tenant setting ID ${tenantSettingToUpdate.id} with value ${settingUpdates.value}`,
          );
          tenantSettingToUpdate.value = settingUpdates.value;
          tenantSettingToUpdate.lastChangedBy = this.context.userId;
          updatedSetting = await this.configStore.saveTenantSetting(
            tenantSettingToUpdate,
          );
        } else {
          const newTenantSetting = this.createNewStoredSetting(
            existingSetting,
            settingUpdates.value,
            AppConfigSettingSource.tenant,
            this.context.userId,
          );

          updatedSetting =
            await this.configStore.saveTenantSetting(newTenantSetting);
        }
        return {
          setting: this.convertSettingValueToRelevantSetting(updatedSetting)[0],
          created: createdNewSetting,
        };
      }
      case 'facility': {
        // find the tenant setting and update it, or create a new one
        const facilitySettingToUpdate =
          await this.configStore.findFacilitySetting(existingSetting.id);
        if (facilitySettingToUpdate) {
          this.logger.info(
            `Updating facility setting ID ${facilitySettingToUpdate.id} with value ${settingUpdates.value}`,
          );
          facilitySettingToUpdate.value = settingUpdates.value;
          facilitySettingToUpdate.lastChangedBy = this.context.userId;
          updatedSetting = await this.configStore.saveFacilitySetting(
            facilitySettingToUpdate,
          );
        } else {
          const newSetting = this.createNewStoredSetting(
            existingSetting,
            settingUpdates.value,
            AppConfigSettingSource.facility,
            this.context.userId,
          );

          const facilitySetting = newSetting as FacilitySetting;
          if (this.context.selectedFacilityId) {
            facilitySetting.facilityId = this.context.selectedFacilityId;
          } else {
            throw IctError.badRequest(
              'A facility ID must be selected to update a facility setting.',
            );
          }

          updatedSetting =
            await this.configStore.saveFacilitySetting(facilitySetting);
        }
        return {
          setting: this.convertSettingValueToRelevantSetting(updatedSetting)[0],
          created: createdNewSetting,
        };
      }
      case 'user': {
        const userSettingToUpdate = await this.configStore.findUserSetting(
          this.context.userId,
          existingSetting.id,
        );
        if (userSettingToUpdate) {
          this.logger.info(
            `Updating user setting ID ${userSettingToUpdate.id} with value ${settingUpdates.value}`,
          );
          userSettingToUpdate.value = settingUpdates.value;
          userSettingToUpdate.lastChangedBy = this.context.userId;
          updatedSetting =
            await this.configStore.saveUserSetting(userSettingToUpdate);
        } else {
          const newUserSetting = this.createNewStoredSetting(
            existingSetting,
            settingUpdates.value,
            AppConfigSettingSource.user,
            this.context.userId,
          ) as UserSetting;

          updatedSetting =
            await this.configStore.saveUserSetting(newUserSetting);
        }
        return {
          setting: this.convertSettingValueToRelevantSetting(updatedSetting)[0],
          created: createdNewSetting,
        };
      }
      default:
        throw IctError.internalServerError(
          `Updating a setting at the ${settingUpdates.levelToUpdate} level is not yet supported.`,
        );
    }
  }

  /**
   * Helper function to create a new StoredSetting instance
   * @param setting the Setting object to associate it to
   * @param value the value to set
   * @param storedSettingType tenant or user
   * @returns the new instance of TenantSetting or UserSetting
   */
  public createNewStoredSetting(
    setting: Setting,
    value: unknown,
    storedSettingType:
      | AppConfigSettingSource.tenant
      | AppConfigSettingSource.facility
      | AppConfigSettingSource.user,
    changedBy: string,
  ): StoredSetting {
    switch (storedSettingType) {
      case AppConfigSettingSource.tenant: {
        const newStoredSetting = new TenantSetting();
        newStoredSetting.setting = setting;
        newStoredSetting.settingId = setting.id;
        newStoredSetting.value = value;
        newStoredSetting.lastChangedBy = changedBy;
        return newStoredSetting;
      }
      case AppConfigSettingSource.facility: {
        const newStoredSetting = new FacilitySetting();
        newStoredSetting.setting = setting;
        newStoredSetting.settingId = setting.id;
        newStoredSetting.value = value;
        newStoredSetting.lastChangedBy = changedBy;
        if (this.context.selectedFacilityId)
          newStoredSetting.facilityId = this.context.selectedFacilityId;
        return newStoredSetting;
      }
      default: {
        const newStoredSetting = new UserSetting();
        newStoredSetting.setting = setting;
        newStoredSetting.settingId = setting.id;
        newStoredSetting.value = value;
        newStoredSetting.lastChangedBy = changedBy;
        newStoredSetting.userId = this.context.userId;
        return newStoredSetting;
      }
    }
  }

  /**
   * Accepts a single or list of Setting or StoredSetting and returns an object of type
   * RelevantSetting. RelevantSetting is the desired DTO to only return relevant data such as
   * the setting id, name, and value
   * @param settingList a list or single Setting or StoredSetting
   * @returns a list of RelevantSetting
   */
  public convertSettingValueToRelevantSetting(
    settingList: StoredSetting[] | StoredSetting | Setting[] | Setting,
  ): AppConfigSetting[] {
    const listOfSettings = Array.isArray(settingList)
      ? settingList
      : [settingList];

    return listOfSettings.map(setting => {
      const isSettingType = setting instanceof Setting;
      if (isSettingType) {
        const friendlySetting: AppConfigSetting = {
          id: setting.id,
          name: setting.name,
          group: setting.group,
          description: setting.description,
          dataType: setting.dataType,
          value: setting.defaultValue,
          source: AppConfigSettingSource.default,
          tags: setting.tags,
          parentSetting: setting.parentSetting,
        };
        return friendlySetting;
      }
      let settingSource: AppConfigSettingSource = AppConfigSettingSource.tenant;
      if (setting instanceof FacilitySetting)
        settingSource = AppConfigSettingSource.facility;

      if (setting instanceof UserSetting)
        settingSource = AppConfigSettingSource.user;
      const friendly: AppConfigSetting = {
        id: setting.settingId,
        name: setting.setting.name,
        group: setting.setting.group,
        description: setting.setting.description,
        dataType: setting.setting.dataType,
        value: setting.value,
        source: settingSource,
        tags: setting.setting.tags,
        parentSetting: setting.setting.parentSetting,
      };
      return friendly;
    });
  }

  /** helper function to create a message for the API based on which records were deleted */
  public generateResultMessage(
    defaultSetting: number,
    tenant: number,
    facility: number,
    user: number,
  ): string {
    if (defaultSetting + tenant + facility + user === 0)
      return 'No records were deleted.';
    return `The following setting values were deleted from the database: 
      UserSetting: ${user}
      TenantSetting: ${tenant}
      FacilitySetting: ${facility}
      Setting: ${defaultSetting}`;
  }

  /**
   * Deletes the given setting ID.  If no levelToDelete is passed, then the Setting and all related
   * StoredSettings are deleted (TenantSetting, UserSetting)
   * @param settingId id to delete
   * @param levelToDelete optionally passed to delete a specific value
   * @returns a message with info about the outcome
   */
  public async deleteSetting(
    settingId: string,
    levelToDelete?: AppConfigSettingSource,
    force: boolean = false,
  ): Promise<DeletedRecordsResult> {
    let tenantRecordsDeleted = 0;
    let facilityRecordsDeleted = 0;
    let userRecordsDeleted = 0;
    let defaultRecordsDeleted = 0;

    switch (levelToDelete) {
      case AppConfigSettingSource.tenant:
        // only delete tenant
        tenantRecordsDeleted =
          await this.configStore.deleteTenantSetting(settingId);
        return {
          recordsDeleted: tenantRecordsDeleted,
          message: this.generateResultMessage(0, tenantRecordsDeleted, 0, 0),
        };
      case AppConfigSettingSource.facility:
        if (!this.context.selectedFacilityId && !force) {
          throw IctError.badRequest(
            'A facility ID must be selected to delete a facility setting.',
          );
        }

        facilityRecordsDeleted = await this.configStore.deleteFacilitySetting(
          settingId,
          force ? null : this.context.selectedFacilityId,
        );
        return {
          recordsDeleted: tenantRecordsDeleted,
          message: this.generateResultMessage(0, 0, facilityRecordsDeleted, 0),
        };
      case AppConfigSettingSource.user:
        // only delete user
        userRecordsDeleted = await this.configStore.deleteUserSetting(
          settingId,
          force ? null : this.context.userId,
        );
        return {
          recordsDeleted: userRecordsDeleted,
          message: this.generateResultMessage(0, 0, 0, userRecordsDeleted),
        };
      default:
        // nothing was passed, so delete everything
        // delete everything
        userRecordsDeleted = await this.configStore.deleteUserSetting(
          settingId,
          force ? null : this.context.userId,
        );

        tenantRecordsDeleted =
          await this.configStore.deleteTenantSetting(settingId);

        facilityRecordsDeleted = await this.configStore.deleteFacilitySetting(
          settingId,
          force ? null : this.context.selectedFacilityId,
        );

        defaultRecordsDeleted = await this.configStore.deleteSetting(settingId);

        return {
          recordsDeleted:
            userRecordsDeleted +
            facilityRecordsDeleted +
            tenantRecordsDeleted +
            defaultRecordsDeleted,
          message: this.generateResultMessage(
            defaultRecordsDeleted,
            tenantRecordsDeleted,
            facilityRecordsDeleted,
            userRecordsDeleted,
          ),
        };
    }
  }

  public getDefaultConfig(): Setting[] {
    return DefaultConfigSettings as unknown as Setting[];
  }

  public async getSettingSchema(
    settingId?: string,
    settingName?: string,
  ): Promise<unknown> {
    const setting = await this.configStore.findSetting(settingId, settingName);

    if (setting == null) {
      throw IctError.notFound(
        `Setting with settingId: ${settingId}, settingName: ${settingName} not found`,
      );
    }

    return setting.jsonSchema;
  }

  getType(): string {
    return ConfigSettingsService.type;
  }
}
