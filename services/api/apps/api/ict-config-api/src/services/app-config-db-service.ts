import {DiService, IctError} from 'ict-api-foundations';
import {AppConfigDbStore} from '../stores/app-config-db-store.ts';
import {CuratedTableRow} from '../defs/curated-table-row.ts';

/**
 * Service that handles applying business logic for AppStore to store queries.
 */
@DiService()
export class AppConfigDbService {
  public static type = 'app-config-db-service';
  constructor(private appConfigDbStore: AppConfigDbStore) {}

  /**
   * Attempt to query the database for curatedDataset table names.
   * @param tableName start of the date range.
   * @returns Data Contract that contains the unknown[] with table names.
   */
  async getCuratedDataset(tableName: string): Promise<unknown[]> {
    const tableNames = await this.getCuratedTableNames();

    const checkTableName = tableNames?.indexOf(tableName);
    if (checkTableName === -1) {
      throw IctError.notFound('table name incorrect');
    }
    return await this.appConfigDbStore.getCuratedDataset(tableName);
  }

  async getCuratedDatasetRows(tableName: string): Promise<CuratedTableRow[]> {
    const tableNames = await this.getCuratedTableNames();

    const checkTableName = tableNames?.indexOf(tableName);
    if (checkTableName === -1) {
      throw IctError.notFound('table name incorrect');
    }
    return await this.appConfigDbStore.getCuratedDatasetRows(tableName);
  }

  async getCuratedTableNames(): Promise<string[] | undefined> {
    // return list of the given dataset
    const results = await this.appConfigDbStore.getCuratedTableNames();
    const tableList = results?.map(table => {
      return String(table.table_id);
    });
    return tableList;
  }

  getType(): string {
    return AppConfigDbService.type;
  }
}
