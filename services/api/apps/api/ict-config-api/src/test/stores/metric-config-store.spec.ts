import sinon from 'sinon';
import {expect} from 'chai';
import {ContextService, WinstonLogger} from 'ict-api-foundations';
import {
  DefaultMetricConfigurationEntity,
  CustomMetricConfigurationEntity,
} from 'ict-api-schema';
import {MetricConfigStore} from '../../stores/metric-config-store.ts';
import type {MetricConfigDetail} from '../../defs/metric-config-detail.ts';

describe('MetricConfigStore', () => {
  describe('getMetricConfigSummaries', () => {
    it('should return both default and custom metric configurations', async () => {
      const context = new ContextService();
      const logger = new WinstonLogger(context);
      const metricConfigStore = new MetricConfigStore(context, logger);

      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      mockDefaultConfig.id = '1';
      mockDefaultConfig.metricConfigName = 'test-metric';
      mockDefaultConfig.configType = 'node';
      mockDefaultConfig.nodeName = 'test-node';
      mockDefaultConfig.factType = 'test-fact';
      mockDefaultConfig.enabled = {default: true};
      mockDefaultConfig.active = {default: true};

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      mockCustomConfig.id = '2';
      mockCustomConfig.metricConfigName = 'test-metric';
      mockCustomConfig.configType = 'node';
      mockCustomConfig.nodeName = 'custom-node';
      mockCustomConfig.factType = 'custom-fact';
      mockCustomConfig.active = true;
      mockCustomConfig.facilityId = 'test-facility';

      const mockEntities = [mockDefaultConfig, mockCustomConfig];

      sinon
        .stub(metricConfigStore, 'getMetricConfigSummaries')
        .resolves(mockEntities);

      const filters = {
        metricName: 'test-metric',
        configType: 'node',
        entityType: 'process-flow',
      };

      const result = await metricConfigStore.getMetricConfigSummaries(filters);

      expect(result).to.deep.equal(mockEntities);
    });

    it('should handle table not found error', async () => {
      const context = new ContextService();
      const logger = new WinstonLogger(context);
      const metricConfigStore = new MetricConfigStore(context, logger);

      sinon
        .stub(metricConfigStore, 'getMetricConfigSummaries')
        .rejects(new Error('Failed to get metric configurations'));

      const filters = {
        metricName: 'test-metric',
        configType: 'node',
        entityType: 'process-flow',
      };

      try {
        await metricConfigStore.getMetricConfigSummaries(filters);
        expect.fail('Should have thrown an error');
      } catch (error: unknown) {
        if (!(error instanceof Error)) {
          throw error;
        }
        expect(error.message).to.equal('Failed to get metric configurations');
      }
    });

    it('should handle database authentication failure', async () => {
      const context = new ContextService();
      const logger = new WinstonLogger(context);
      const metricConfigStore = new MetricConfigStore(context, logger);

      sinon
        .stub(metricConfigStore, 'getMetricConfigSummaries')
        .rejects(new Error('Failed to get metric configurations'));

      const filters = {
        metricName: 'test-metric',
        configType: 'node',
        entityType: 'process-flow',
      };

      try {
        await metricConfigStore.getMetricConfigSummaries(filters);
        expect.fail('Should have thrown an error');
      } catch (error: unknown) {
        if (!(error instanceof Error)) {
          throw error;
        }
        expect(error.message).to.equal('Failed to get metric configurations');
      }
    });

    it('should handle invalid column name error', async () => {
      const context = new ContextService();
      const logger = new WinstonLogger(context);
      const metricConfigStore = new MetricConfigStore(context, logger);

      sinon
        .stub(metricConfigStore, 'getMetricConfigSummaries')
        .rejects(new Error('Failed to get metric configurations'));

      const filters = {
        metricName: 'test-metric',
        configType: 'node',
        entityType: 'process-flow',
      };

      try {
        await metricConfigStore.getMetricConfigSummaries(filters);
        expect.fail('Should have thrown an error');
      } catch (error: unknown) {
        if (!(error instanceof Error)) {
          throw error;
        }
        expect(error.message).to.equal('Failed to get metric configurations');
      }
    });
  });

  describe('putMetricConfig', () => {
    let context: ContextService;
    let logger: WinstonLogger;
    let metricConfigStore: MetricConfigStore;
    let mockEntityManager: any;
    let mockCustomRepo: any;

    beforeEach(() => {
      context = new ContextService();
      logger = new WinstonLogger(context);
      metricConfigStore = new MetricConfigStore(context, logger);

      // Mock the database provider and entity manager
      mockEntityManager = {
        getRepository: sinon.stub(),
      };

      mockCustomRepo = {
        findOne: sinon.stub(),
        save: sinon.stub(),
      };

      mockEntityManager.getRepository.returns(mockCustomRepo);

      // Mock the dbProvider
      sinon.stub(metricConfigStore, 'dbProvider').get(() => ({
        processFlowPostgres: {
          datasource: {
            manager: mockEntityManager,
          },
        },
      }));
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should create a new node metric configuration when none exists', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-node-metric',
        configType: 'node',
        views: ['facility', 'multishuttle'],
        matchConditions: {eventType: 'test-event'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Node Metric',
        acceptableLowRange: 0,
        acceptableHighRange: 100,
        exampleMessage: {test: 'data'},
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'stockTime',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        metricUnits: '/hr',
        graphOperation: 'area_node',
        redisParams: {rowHash: '{rowHash}'},
        label: 'Area',
        parentNodes: ['facility'],
      };

      const facilityId = 'test-facility';

      // Mock that no existing config is found
      mockCustomRepo.findOne.resolves(null);

      // Mock default repository (no default config exists)
      const mockDefaultRepo = {
        findOne: sinon.stub().resolves(null),
        save: sinon.stub(),
      };

      mockEntityManager.getRepository
        .withArgs(CustomMetricConfigurationEntity)
        .returns(mockCustomRepo);
      mockEntityManager.getRepository
        .withArgs(DefaultMetricConfigurationEntity)
        .returns(mockDefaultRepo);

      // Mock the save operation
      const savedConfig = new CustomMetricConfigurationEntity();
      Object.assign(savedConfig, {
        id: 'new-config-id',
        ...input,
        facilityId,
      });
      mockCustomRepo.save.resolves(savedConfig);

      const result = await metricConfigStore.putMetricConfig(input, facilityId);

      expect(result.isNew).to.be.true;
      expect(result.config).to.deep.equal(savedConfig);
      expect(
        mockCustomRepo.findOne.calledOnceWith({
          where: {
            metricConfigName: input.metricConfigName,
            facilityId,
          },
        }),
      ).to.be.true;
      expect(mockCustomRepo.save.calledOnce).to.be.true;
    });

    it('should update an existing node metric configuration', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'existing-node-metric',
        configType: 'node',
        views: ['facility', 'multishuttle'],
        matchConditions: {eventType: 'test-event'},
        factType: 'updated-fact',
        sourceSystem: 'diq',
        displayName: 'Updated Node Metric',
        acceptableLowRange: 10,
        acceptableHighRange: 90,
        exampleMessage: {updated: 'data'},
        enabled: false,
        active: false,
        isCustom: true,
        nodeName: 'updated-node',
        metricType: 'updatedType',
        timeWindow: '60m_set',
        aggregation: 'avg',
        redisOperation: 'event_set',
        metricUnits: '/min',
        graphOperation: 'area_node',
        redisParams: {updatedHash: '{updatedHash}'},
        label: 'Area',
        parentNodes: ['facility', 'zone'],
      };

      const facilityId = 'test-facility';

      // Mock existing config
      const existingConfig = new CustomMetricConfigurationEntity();
      existingConfig.id = 'existing-config-id';
      existingConfig.metricConfigName = input.metricConfigName;
      existingConfig.facilityId = facilityId;
      existingConfig.factType = 'old-fact';
      existingConfig.displayName = 'Old Display Name';

      mockCustomRepo.findOne.resolves(existingConfig);

      // Mock the save operation with updated config
      const updatedConfig = new CustomMetricConfigurationEntity();
      Object.assign(updatedConfig, {
        ...existingConfig,
        ...input,
        facilityId,
      });
      mockCustomRepo.save.resolves(updatedConfig);

      const result = await metricConfigStore.putMetricConfig(input, facilityId);

      expect(result.isNew).to.be.false;
      expect(result.config).to.deep.equal(updatedConfig);
      expect(
        mockCustomRepo.findOne.calledOnceWith({
          where: {
            metricConfigName: input.metricConfigName,
            facilityId,
          },
        }),
      ).to.be.true;
      expect(mockCustomRepo.save.calledOnceWith(existingConfig)).to.be.true;
    });

    it('should create a new inbound-edge metric configuration', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-inbound-metric',
        configType: 'inbound-edge',
        views: ['facility', 'multishuttle'],
        matchConditions: {eventType: 'arrival'},
        factType: 'inbound-fact',
        sourceSystem: 'diq',
        displayName: 'Test Inbound Metric',
        enabled: true,
        active: true,
        isCustom: true,
        huId: 'handlingUnitCode',
        inboundArea: 'multishuttle',
        redisOperation: 'event_set',
        metricUnits: 'units/hr',
        inboundParentNodes: ['facility'],
        graphOperation: 'area_edge',
        redisParams: {huId: '{handlingUnitCode}'},
        label: 'Area',
        parentNodes: ['facility'],
      };

      const facilityId = 'test-facility';

      mockCustomRepo.findOne.resolves(null);

      const savedConfig = new CustomMetricConfigurationEntity();
      Object.assign(savedConfig, {
        id: 'new-inbound-config-id',
        ...input,
        facilityId,
      });
      mockCustomRepo.save.resolves(savedConfig);

      const result = await metricConfigStore.putMetricConfig(input, facilityId);

      expect(result.isNew).to.be.true;
      expect(result.config).to.deep.equal(savedConfig);
    });

    it('should create a new outbound-edge metric configuration', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-outbound-metric',
        configType: 'outbound-edge',
        views: ['facility', 'multishuttle'],
        matchConditions: {eventType: 'departure'},
        factType: 'outbound-fact',
        sourceSystem: 'diq',
        displayName: 'Test Outbound Metric',
        enabled: true,
        active: true,
        isCustom: true,
        outboundArea: 'multishuttle',
        huId: 'handlingUnitCode',
        units: 'handlingUnit',
        outboundParentNodes: ['facility'],
        graphOperation: 'area_edge',
        redisParams: {huId: '{handlingUnitCode}'},
        label: 'Area',
        parentNodes: ['facility'],
      };

      const facilityId = 'test-facility';

      mockCustomRepo.findOne.resolves(null);

      const savedConfig = new CustomMetricConfigurationEntity();
      Object.assign(savedConfig, {
        id: 'new-outbound-config-id',
        ...input,
        facilityId,
      });
      mockCustomRepo.save.resolves(savedConfig);

      const result = await metricConfigStore.putMetricConfig(input, facilityId);

      expect(result.isNew).to.be.true;
      expect(result.config).to.deep.equal(savedConfig);
    });

    it('should create a new complete-edge metric configuration', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-complete-metric',
        configType: 'complete-edge',
        views: ['facility', 'multishuttle'],
        matchConditions: {eventType: 'complete'},
        factType: 'complete-fact',
        sourceSystem: 'diq',
        displayName: 'Test Complete Metric',
        enabled: true,
        active: true,
        isCustom: true,
        inboundArea: 'multishuttle',
        outboundArea: 'sortation',
        redisOperation: 'event_set',
        outboundNodeLabel: 'SortArea',
        inboundParentNodes: ['facility'],
        outboundParentNodes: ['facility'],
        metricUnits: 'units/hr',
        graphOperation: 'area_edge',
        redisParams: {edgeId: '{edgeId}'},
        label: 'Area',
        parentNodes: ['facility'],
      };

      const facilityId = 'test-facility';

      mockCustomRepo.findOne.resolves(null);

      const savedConfig = new CustomMetricConfigurationEntity();
      Object.assign(savedConfig, {
        id: 'new-complete-config-id',
        ...input,
        facilityId,
      });
      mockCustomRepo.save.resolves(savedConfig);

      const result = await metricConfigStore.putMetricConfig(input, facilityId);

      expect(result.isNew).to.be.true;
      expect(result.config).to.deep.equal(savedConfig);
    });

    it('should handle database save errors', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-error-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Error Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      const facilityId = 'test-facility';

      mockCustomRepo.findOne.resolves(null);
      mockCustomRepo.save.rejects(new Error('Database connection failed'));

      try {
        await metricConfigStore.putMetricConfig(input, facilityId);
        expect.fail('Should have thrown an error');
      } catch (error: unknown) {
        if (!(error instanceof Error)) {
          throw error;
        }
        expect(error.message).to.equal('Database connection failed');
      }
    });

    it('should handle database find errors', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-find-error-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Find Error Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      const facilityId = 'test-facility';

      mockCustomRepo.findOne.rejects(new Error('Database query failed'));

      try {
        await metricConfigStore.putMetricConfig(input, facilityId);
        expect.fail('Should have thrown an error');
      } catch (error: unknown) {
        if (!(error instanceof Error)) {
          throw error;
        }
        expect(error.message).to.equal('Database query failed');
      }
    });

    it('should handle configuration with minimal required fields', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'minimal-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Minimal Metric',
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      const facilityId = 'test-facility';

      mockCustomRepo.findOne.resolves(null);

      const savedConfig = new CustomMetricConfigurationEntity();
      Object.assign(savedConfig, {
        id: 'minimal-config-id',
        ...input,
        facilityId,
      });
      mockCustomRepo.save.resolves(savedConfig);

      const result = await metricConfigStore.putMetricConfig(input, facilityId);

      expect(result.isNew).to.be.true;
      expect(result.config).to.deep.equal(savedConfig);
    });

    it('should update default configuration enabled state when creating new custom configuration', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-disable-default',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Disable Default',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      const facilityId = 'test-facility';

      // Mock that no custom config exists
      mockCustomRepo.findOne.resolves(null);

      // Mock default config that should be disabled
      const defaultConfig = new DefaultMetricConfigurationEntity();
      defaultConfig.id = 'default-config-id';
      defaultConfig.metricConfigName = 'test-disable-default';
      defaultConfig.enabled = {'test-facility': true, 'other-facility': true};

      // Mock the repositories
      const mockDefaultRepo = {
        findOne: sinon.stub().resolves(defaultConfig),
        save: sinon.stub().resolves(defaultConfig),
      };

      mockEntityManager.getRepository
        .withArgs(CustomMetricConfigurationEntity)
        .returns(mockCustomRepo);
      mockEntityManager.getRepository
        .withArgs(DefaultMetricConfigurationEntity)
        .returns(mockDefaultRepo);

      const savedConfig = new CustomMetricConfigurationEntity();
      Object.assign(savedConfig, {
        id: 'new-custom-config-id',
        ...input,
        facilityId,
      });
      mockCustomRepo.save.resolves(savedConfig);

      const result = await metricConfigStore.putMetricConfig(input, facilityId);

      expect(result.isNew).to.be.true;
      expect(result.config).to.deep.equal(savedConfig);

      // Verify that the default config was found and updated
      expect(
        mockDefaultRepo.findOne.calledOnceWith({
          where: {metricConfigName: 'test-disable-default'},
        }),
      ).to.be.true;

      // Verify that the default config was saved with the facility state opposite to custom config
      expect(mockDefaultRepo.save.calledOnce).to.be.true;
      const savedDefaultConfig = mockDefaultRepo.save.firstCall.args[0];
      expect(savedDefaultConfig.enabled).to.deep.equal({
        'test-facility': false, // Custom config is enabled (true), so default should be disabled (false)
        'other-facility': true,
      });
    });

    it('should enable default configuration when creating disabled custom configuration', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-enable-default',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Enable Default',
        enabled: false, // Custom config is disabled
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      const facilityId = 'test-facility';

      // Mock that no custom config exists
      mockCustomRepo.findOne.resolves(null);

      // Mock default config that should be enabled
      const defaultConfig = new DefaultMetricConfigurationEntity();
      defaultConfig.id = 'default-config-id';
      defaultConfig.metricConfigName = 'test-enable-default';
      defaultConfig.enabled = {'test-facility': false, 'other-facility': true};

      // Mock the repositories
      const mockDefaultRepo = {
        findOne: sinon.stub().resolves(defaultConfig),
        save: sinon.stub().resolves(defaultConfig),
      };

      mockEntityManager.getRepository
        .withArgs(CustomMetricConfigurationEntity)
        .returns(mockCustomRepo);
      mockEntityManager.getRepository
        .withArgs(DefaultMetricConfigurationEntity)
        .returns(mockDefaultRepo);

      const savedConfig = new CustomMetricConfigurationEntity();
      Object.assign(savedConfig, {
        id: 'new-custom-config-id',
        ...input,
        facilityId,
      });
      mockCustomRepo.save.resolves(savedConfig);

      const result = await metricConfigStore.putMetricConfig(input, facilityId);

      expect(result.isNew).to.be.true;
      expect(result.config).to.deep.equal(savedConfig);

      // Verify that the default config was found and updated
      expect(
        mockDefaultRepo.findOne.calledOnceWith({
          where: {metricConfigName: 'test-enable-default'},
        }),
      ).to.be.true;

      // Verify that the default config was saved with the facility state opposite to custom config
      expect(mockDefaultRepo.save.calledOnce).to.be.true;
      const savedDefaultConfig = mockDefaultRepo.save.firstCall.args[0];
      expect(savedDefaultConfig.enabled).to.deep.equal({
        'test-facility': true, // Custom config is disabled (false), so default should be enabled (true)
        'other-facility': true,
      });
    });

    it('should create custom configuration successfully when no default config exists', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'standalone-custom-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Standalone Custom Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      const facilityId = 'test-facility';

      // Mock that no custom config exists
      mockCustomRepo.findOne.resolves(null);

      // Mock that no default config exists
      const mockDefaultRepo = {
        findOne: sinon.stub().resolves(null), // No default config found
        save: sinon.stub(), // Should not be called
      };

      mockEntityManager.getRepository
        .withArgs(CustomMetricConfigurationEntity)
        .returns(mockCustomRepo);
      mockEntityManager.getRepository
        .withArgs(DefaultMetricConfigurationEntity)
        .returns(mockDefaultRepo);

      const savedConfig = new CustomMetricConfigurationEntity();
      Object.assign(savedConfig, {
        id: 'standalone-config-id',
        ...input,
        facilityId,
      });
      mockCustomRepo.save.resolves(savedConfig);

      const result = await metricConfigStore.putMetricConfig(input, facilityId);

      expect(result.isNew).to.be.true;
      expect(result.config).to.deep.equal(savedConfig);

      // Verify that the default config was searched for
      expect(
        mockDefaultRepo.findOne.calledOnceWith({
          where: {metricConfigName: 'standalone-custom-metric'},
        }),
      ).to.be.true;

      // Verify that the default config save was NOT called (since no default config exists)
      expect(mockDefaultRepo.save.called).to.be.false;
    });
  });
});
