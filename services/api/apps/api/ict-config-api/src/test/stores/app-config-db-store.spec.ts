import {BigQuery} from '@google-cloud/bigquery';
import sinon from 'sinon';
import {expect} from 'chai';
import {
  BigQueryDatabase,
  ConfigStore,
  ContextService,
  DatabaseProvider,
  IctError,
} from 'ict-api-foundations';
import {AppConfigDbStore} from '../../stores/app-config-db-store.ts';
import {
  CuratedTableColumn,
  CuratedTableColumnValueRenderType,
} from '../../defs/curated-table-column.ts';
import {CuratedTableRow} from '../../defs/curated-table-row.ts';

describe('AppConfigDbStore', () => {
  describe('getCuratedTableNames', () => {
    it('should return a list of tables with table names as ids', async () => {
      const bigQueryClient = sinon.createStubInstance(BigQuery);
      const bigQueryDb = new BigQueryDatabase(bigQueryClient, 'test');
      const dbProvider = new DatabaseProvider();
      dbProvider.set(bigQueryDb.getType(), bigQueryDb);

      const context = new ContextService();
      sinon.stub(context, 'dbProvider').get(() => dbProvider);
      const executeMonitoredJobStub = sinon.stub(
        dbProvider.bigQuery,
        'executeMonitoredJob'
      );

      const configStore = new ConfigStore(context);
      const appConfigDbStore = new AppConfigDbStore(context, configStore);

      const expected = [
        {table_id: 'dim_table1'},
        {table_id: 'fct_table2'},
        {table_id: 'fct_table3'},
      ];
      executeMonitoredJobStub.resolves([expected]);

      const result = await appConfigDbStore.getCuratedTableNames();
      expect(result).to.deep.equal(expected);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      const bigQueryClient = sinon.createStubInstance(BigQuery);
      const bigQueryDb = new BigQueryDatabase(bigQueryClient, 'test');
      const dbProvider = new DatabaseProvider();
      dbProvider.set(bigQueryDb.getType(), bigQueryDb);

      const context = new ContextService();
      sinon.stub(context, 'dbProvider').get(() => dbProvider);
      const executeMonitoredJobStub = sinon.stub(
        dbProvider.bigQuery,
        'executeMonitoredJob'
      );

      const configStore = new ConfigStore(context);
      const appConfigDbStore = new AppConfigDbStore(context, configStore);

      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        appConfigDbStore.getCuratedTableNames()
      ).to.be.rejectedWith(IctError);
      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getCuratedDataset', () => {
    it('should return the data of the specified table', async () => {
      const bigQueryClient = sinon.createStubInstance(BigQuery);
      const bigQueryDb = new BigQueryDatabase(bigQueryClient, 'test');
      const dbProvider = new DatabaseProvider();
      dbProvider.set(bigQueryDb.getType(), bigQueryDb);

      const context = new ContextService();
      sinon.stub(context, 'dbProvider').get(() => dbProvider);
      const executeMonitoredJobStub = sinon.stub(
        dbProvider.bigQuery,
        'executeMonitoredJob'
      );

      const configStore = new ConfigStore(context);
      const appConfigDbStore = new AppConfigDbStore(context, configStore);

      const expected = [
        {test_column1: 'test_value1', test_column2: 'test_value2'},
        {test_column1: 'test_value2', test_column2: 'test_value3'},
        {test_column1: 'test_value5', test_column2: 'test_value4'},
      ];
      executeMonitoredJobStub.resolves([expected]);

      const result = await appConfigDbStore.getCuratedDataset('test_table');
      expect(result).to.deep.equal(expected);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      const bigQueryClient = sinon.createStubInstance(BigQuery);
      const bigQueryDb = new BigQueryDatabase(bigQueryClient, 'test');
      const dbProvider = new DatabaseProvider();
      dbProvider.set(bigQueryDb.getType(), bigQueryDb);

      const context = new ContextService();
      sinon.stub(context, 'dbProvider').get(() => dbProvider);
      const executeMonitoredJobStub = sinon.stub(
        dbProvider.bigQuery,
        'executeMonitoredJob'
      );
      const configStore = new ConfigStore(context);
      const appConfigDbStore = new AppConfigDbStore(context, configStore);

      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        appConfigDbStore.getCuratedDataset('test_table')
      ).to.be.rejectedWith(IctError);
      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('createDatabaseColumnValue', () => {
    it('should return a string column value from a BigQueryDate', () => {
      const testColumnName = 'test_column';
      const testBigQueryDate = BigQuery.date('2024-01-01');
      const expectedResult: CuratedTableColumn = {
        name: testColumnName,
        value: testBigQueryDate.value,
        renderType: CuratedTableColumnValueRenderType.String,
      };
      // eslint-disable-next-line dot-notation
      const result = AppConfigDbStore['createDatabaseColumnValue'](
        testColumnName,
        testBigQueryDate
      );
      expect(result).to.deep.equal(expectedResult);
    });

    it('should return a string column value from a BigQueryTimestamp', () => {
      const testColumnName = 'test_column';
      const testBigQueryTimestamp = BigQuery.timestamp('2024-01-01');
      const expectedResult: CuratedTableColumn = {
        name: testColumnName,
        value: testBigQueryTimestamp.value,
        renderType: CuratedTableColumnValueRenderType.String,
      };
      // eslint-disable-next-line dot-notation
      const result = AppConfigDbStore['createDatabaseColumnValue'](
        testColumnName,
        testBigQueryTimestamp
      );
      expect(result).to.deep.equal(expectedResult);
    });

    it('should return a string column value from a BigQueryDateTime', () => {
      const testColumnName = 'test_column';
      const testBigQueryDateTime = BigQuery.datetime('2024-01-01 00:00:00');
      const expectedResult: CuratedTableColumn = {
        name: testColumnName,
        value: testBigQueryDateTime.value,
        renderType: CuratedTableColumnValueRenderType.String,
      };
      // eslint-disable-next-line dot-notation
      const result = AppConfigDbStore['createDatabaseColumnValue'](
        testColumnName,
        testBigQueryDateTime
      );
      expect(result).to.deep.equal(expectedResult);
    });

    it('should return a string column value from a BigQueryTime', () => {
      const testColumnName = 'test_column';
      const testBigQueryGeography = BigQuery.time('00:00:00');
      const expectedResult: CuratedTableColumn = {
        name: testColumnName,
        value: testBigQueryGeography.value,
        renderType: CuratedTableColumnValueRenderType.String,
      };
      // eslint-disable-next-line dot-notation
      const result = AppConfigDbStore['createDatabaseColumnValue'](
        testColumnName,
        testBigQueryGeography
      );
      expect(result).to.deep.equal(expectedResult);
    });

    it('should return a strring column value from a native Date', () => {
      const testColumnName = 'test_column';
      const testDate = new Date();
      const expectedResult: CuratedTableColumn = {
        name: testColumnName,
        value: testDate.toISOString(),
        renderType: CuratedTableColumnValueRenderType.String,
      };
      // eslint-disable-next-line dot-notation
      const result = AppConfigDbStore['createDatabaseColumnValue'](
        testColumnName,
        testDate
      );
      expect(result).to.deep.equal(expectedResult);
    });

    it('should return a number column value from a BigQueryInt type', () => {
      const testColumnName = 'test_column';
      const testBigQueryInt = BigQuery.int(42);
      const expectedResult: CuratedTableColumn = {
        name: testColumnName,
        value: Number(testBigQueryInt),
        renderType: CuratedTableColumnValueRenderType.Number,
      };
      // eslint-disable-next-line dot-notation
      const result = AppConfigDbStore['createDatabaseColumnValue'](
        testColumnName,
        testBigQueryInt
      );
      expect(result).to.deep.equal(expectedResult);
    });

    it('should return a string column value of any object that is not a BigQuery type', () => {
      const testColumnName = 'test_column';
      const testObject = {test: 'test'};
      const expectedResult: CuratedTableColumn = {
        name: testColumnName,
        value: JSON.stringify(testObject),
        renderType: CuratedTableColumnValueRenderType.String,
      };
      // eslint-disable-next-line dot-notation
      const result = AppConfigDbStore['createDatabaseColumnValue'](
        testColumnName,
        testObject
      );
      expect(result).to.deep.equal(expectedResult);
    });

    it('should return a string column value of any array', () => {
      const testColumnName = 'test_column';
      const testObject = [{test: 'test'}];
      const expectedResult: CuratedTableColumn = {
        name: testColumnName,
        value: JSON.stringify(testObject),
        renderType: CuratedTableColumnValueRenderType.String,
      };
      // eslint-disable-next-line dot-notation
      const result = AppConfigDbStore['createDatabaseColumnValue'](
        testColumnName,
        testObject
      );
      expect(result).to.deep.equal(expectedResult);
    });

    it('should return a bool column value from any boolean value', () => {
      const testColumnName = 'test_column';
      const expectedResult: CuratedTableColumn = {
        name: testColumnName,
        value: true,
        renderType: CuratedTableColumnValueRenderType.Boolean,
      };
      // eslint-disable-next-line dot-notation
      const result = AppConfigDbStore['createDatabaseColumnValue'](
        testColumnName,
        true
      );
      expect(result).to.deep.equal(expectedResult);
    });

    it('should return a null column value from any null value', () => {
      const testColumnName = 'test_column';
      const expectedResult: CuratedTableColumn = {
        name: testColumnName,
        value: null,
        renderType: CuratedTableColumnValueRenderType.Null,
      };
      // eslint-disable-next-line dot-notation
      const result = AppConfigDbStore['createDatabaseColumnValue'](
        testColumnName,
        null
      );
      expect(result).to.deep.equal(expectedResult);
    });

    it('should return a number column value from any number value', () => {
      const testColumnName = 'test_column';
      const expectedResult: CuratedTableColumn = {
        name: testColumnName,
        value: 42,
        renderType: CuratedTableColumnValueRenderType.Number,
      };
      // eslint-disable-next-line dot-notation
      const result = AppConfigDbStore['createDatabaseColumnValue'](
        testColumnName,
        42
      );
      expect(result).to.deep.equal(expectedResult);
    });

    it('should return a string column value from any string value', () => {
      const testColumnName = 'test_column';
      const testString = 'test';
      const expectedResult: CuratedTableColumn = {
        name: testColumnName,
        value: testString,
        renderType: CuratedTableColumnValueRenderType.String,
      };
      // eslint-disable-next-line dot-notation
      const result = AppConfigDbStore['createDatabaseColumnValue'](
        testColumnName,
        testString
      );
      expect(result).to.deep.equal(expectedResult);
    });
  });

  describe('getCuratedTableRowsFromRawResults', () => {
    it('should return an array of CuratedTableRows from an array of raw results', () => {
      const testRawResults = [
        {
          test_column1: null,
          test_column2: new Date(),
          test_column3: 'test',
          test_column4: 42,
          test_column5: true,
        },
      ];
      const expectedResult: CuratedTableRow[] = [
        {
          columns: [
            {
              name: 'test_column1',
              value: null,
              renderType: CuratedTableColumnValueRenderType.Null,
            },
            {
              name: 'test_column2',
              value: testRawResults[0].test_column2.toISOString(),
              renderType: CuratedTableColumnValueRenderType.String,
            },
            {
              name: 'test_column3',
              value: testRawResults[0].test_column3,
              renderType: CuratedTableColumnValueRenderType.String,
            },
            {
              name: 'test_column4',
              value: testRawResults[0].test_column4,
              renderType: CuratedTableColumnValueRenderType.Number,
            },
            {
              name: 'test_column5',
              value: testRawResults[0].test_column5,
              renderType: CuratedTableColumnValueRenderType.Boolean,
            },
          ],
        },
      ];

      const result =
        // eslint-disable-next-line dot-notation
        AppConfigDbStore['getCuratedTableRowsFromRawResults'](testRawResults);
      expect(result).to.deep.equal(expectedResult);
    });
  });

  describe('getCuratedDatasetRows', async () => {
    it('should return CuratedTableRows from the specified table', async () => {
      const context = new ContextService();
      const configStore = new ConfigStore(context);
      const appConfigDbStore = new AppConfigDbStore(context, configStore);

      const testRawResults = [
        {
          test_column1: null,
          test_column2: new Date(),
          test_column3: 'test',
          test_column4: 42,
          test_column5: true,
        },
      ];
      const expectedResult: CuratedTableRow[] = [
        {
          columns: [
            {
              name: 'test_column1',
              value: null,
              renderType: CuratedTableColumnValueRenderType.Null,
            },
            {
              name: 'test_column2',
              value: testRawResults[0].test_column2.toISOString(),
              renderType: CuratedTableColumnValueRenderType.String,
            },
            {
              name: 'test_column3',
              value: testRawResults[0].test_column3,
              renderType: CuratedTableColumnValueRenderType.String,
            },
            {
              name: 'test_column4',
              value: testRawResults[0].test_column4,
              renderType: CuratedTableColumnValueRenderType.Number,
            },
            {
              name: 'test_column5',
              value: testRawResults[0].test_column5,
              renderType: CuratedTableColumnValueRenderType.Boolean,
            },
          ],
        },
      ];

      sinon
        .stub(appConfigDbStore, 'getCuratedDataset')
        .resolves(testRawResults);
      const result = await appConfigDbStore.getCuratedDatasetRows('test_table');
      expect(result).to.deep.equal(expectedResult);
    });
  });
});
