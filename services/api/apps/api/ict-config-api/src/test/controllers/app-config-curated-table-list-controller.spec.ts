import 'reflect-metadata';
import * as sinon from 'sinon';
import request from 'supertest';
import {
  Container,
  ContextService,
  SecurityRoles,
  appSetup,
} from 'ict-api-foundations';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {AppConfigDbService} from '../../services/app-config-db-service.ts';

describe('AppConfigCuratedTableListController', () => {
  const app = appSetup(RegisterRoutes);
  afterEach(() => {
    sinon.restore();
  });
  describe('config db route', () => {
    let appConfigDbServiceStub: sinon.SinonStubbedInstance<AppConfigDbService>;
    let contextServiceRolesStub: sinon.SinonStub;
    const url = '/config/curated-tables/list';
    beforeEach(() => {
      appConfigDbServiceStub = sinon.createStubInstance(AppConfigDbService);
      Container.set(AppConfigDbService, appConfigDbServiceStub);
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should return an error when the getCuratedTableNames service fails', async () => {
      const contextService = Container.get(ContextService);
      contextServiceRolesStub = sinon.stub(contextService, 'userRoles');
      contextServiceRolesStub.value([SecurityRoles.CT_ENGINEERS]);

      appConfigDbServiceStub.getCuratedTableNames.rejects(
        new Error('Something bad happened')
      );
      const response = await request(app).get(url);
      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      console.log('error body: ', response.body);
      expect(response.body.title).to.deep.equal('Internal Server Error');
      sinon.assert.calledWith(appConfigDbServiceStub.getCuratedTableNames);
    });

    it('should call "AppConfigDbService.getCuratedTablesList"', async () => {
      const contextService = Container.get(ContextService);
      contextServiceRolesStub = sinon.stub(contextService, 'userRoles');
      contextServiceRolesStub.value([SecurityRoles.CT_ENGINEERS]);

      appConfigDbServiceStub.getCuratedTableNames.resolves([
        'dim_table1',
        'fct_table2',
        'fct_table3',
      ]);
      const response = await request(app).get(url);
      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal([
        'dim_table1',
        'fct_table2',
        'fct_table3',
      ]);
      sinon.assert.calledWith(appConfigDbServiceStub.getCuratedTableNames);
    });

    it('should throw a forbidden error if the user does not have the CT_ENGINEERS role', async () => {
      const response = await request(app).get(url);
      expect(response.status).to.equal(401);
      expect(response.body.detail).to.deep.equal('Forbidden');
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.title).to.deep.equal('Unauthorized');
    });
  });
});
