import 'reflect-metadata';
import * as sinon from 'sinon';
import request from 'supertest';
import {
  Container,
  HttpStatusCodes,
  IctError,
  appSetup,
  AppConfigSettingSource,
  ContextService,
  SecurityRoles,
} from 'ict-api-foundations';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {ConfigSettingsService} from '../../services/config-settings-service.ts';
import {ConfigSettingsController} from '../../controllers/config-settings-controller.ts';

describe('ConfigSettingsController', () => {
  let contextServiceRolesStub: sinon.SinonStub;
  const app = appSetup(RegisterRoutes);

  afterEach(() => {
    sinon.restore();
  });

  describe('config route', () => {
    let appConfigServiceStub: sinon.SinonStubbedInstance<ConfigSettingsService>;

    beforeEach(() => {
      appConfigServiceStub = sinon.createStubInstance(ConfigSettingsService);
      Container.set(ConfigSettingsService, appConfigServiceStub);
    });

    describe('GET /settings', async () => {
      const url = '/config/settings';

      it('can be called with an allStoredValues param', async () => {
        appConfigServiceStub.getAllSettings.resolves([
          ConfigSettingsController.exampleRelevantSetting,
        ]);

        const response = await request(app).get(`${url}?allStoredValues=true`);

        expect(response.status).to.equal(200);
        expect(response.headers['content-type']).to.match(/json/);
        expect(response.body).to.deep.equal(
          ConfigSettingsController.exampleGetAllSettingsResponse,
        );
        expect(
          appConfigServiceStub.getAllSettings.calledOnceWith(undefined, true),
        ).to.be.true;
      });

      it('can be called with a group name param', async () => {
        appConfigServiceStub.getAllSettings.resolves([
          ConfigSettingsController.exampleRelevantSetting,
        ]);

        const groupName = 'some-group-name';
        const response = await request(app).get(`${url}?group=${groupName}`);

        expect(response.status).to.equal(200);
        expect(response.headers['content-type']).to.match(/json/);
        expect(response.body).to.deep.equal(
          ConfigSettingsController.exampleGetAllSettingsResponse,
        );
        expect(appConfigServiceStub.getAllSettings.calledOnceWith(groupName)).to
          .be.true;
      });

      it('can be called without any params', async () => {
        appConfigServiceStub.getAllSettings.resolves([
          ConfigSettingsController.exampleRelevantSetting,
        ]);

        const response = await request(app).get(url);

        expect(response.status).to.equal(200);
        expect(response.headers['content-type']).to.match(/json/);
        expect(response.body).to.deep.equal(
          ConfigSettingsController.exampleGetAllSettingsResponse,
        );
        expect(
          appConfigServiceStub.getAllSettings.calledOnceWith(
            undefined,
            undefined,
          ),
        ).to.be.true;
      });

      it('throws an error when the service throws an error', async () => {
        const errorMessage = 'bad things happening';
        appConfigServiceStub.getAllSettings.throws(
          IctError.badRequest(errorMessage),
        );

        const response = await request(app).get(url);

        expect(response.status).to.equal(IctError.badRequest().statusCode);
        expect(response.body.detail).to.equal(errorMessage);
        expect(appConfigServiceStub.getAllSettings.calledOnce).to.be.true;
      });

      it('fails with an incorrect type param', async () => {
        appConfigServiceStub.getSingleSetting.resolves(
          ConfigSettingsController.exampleRelevantSetting,
        );

        const response = await request(app).get(
          `${url}?settingId=${ConfigSettingsController.exampleRelevantSetting.id}&settingType=wrongType`,
        );

        expect(response.status).to.equal(422);
        expect(appConfigServiceStub.getSingleSetting.notCalled).to.be.true;
      });

      it('not found error thrown if no setting was found with name parameter', async () => {
        appConfigServiceStub.getSingleSetting.resolves(undefined);

        const settingName = 'test';
        const response = await request(app).get(
          `${url}?settingName=${settingName}`,
        );

        expect(response.status).to.equal(IctError.notFound().statusCode);
        expect(
          appConfigServiceStub.getSingleSetting.calledOnceWith(
            undefined,
            settingName,
          ),
        ).to.be.true;
      });

      it('not found error thrown if no setting was found with id parameter', async () => {
        appConfigServiceStub.getSingleSetting.resolves(undefined);

        const settingId = 'test';
        const response = await request(app).get(
          `${url}?settingId=${settingId}`,
        );

        expect(response.status).to.equal(IctError.notFound().statusCode);
        expect(appConfigServiceStub.getSingleSetting.calledOnceWith(settingId))
          .to.be.true;
      });

      it('can be called without a type param', async () => {
        appConfigServiceStub.getSingleSetting.resolves(
          ConfigSettingsController.exampleRelevantSetting,
        );

        const response = await request(app).get(
          `${url}?settingId=${ConfigSettingsController.exampleRelevantSetting.id}`,
        );

        expect(response.status).to.equal(200);
        expect(response.headers['content-type']).to.match(/json/);
        expect(response.body).to.deep.equal(
          ConfigSettingsController.exampleGetSettingResponse,
        );
        expect(
          appConfigServiceStub.getSingleSetting.calledOnceWith(
            ConfigSettingsController.exampleRelevantSetting.id,
          ),
        ).to.be.true;
      });

      it('can be called with a correct type param', async () => {
        appConfigServiceStub.getSingleSetting.resolves(
          ConfigSettingsController.exampleRelevantSetting,
        );

        const type = AppConfigSettingSource.tenant;
        const response = await request(app).get(
          `${url}?settingId=${ConfigSettingsController.exampleRelevantSetting.id}&settingType=${type}`,
        );

        expect(response.status).to.equal(200);
        expect(response.headers['content-type']).to.match(/json/);
        expect(response.body).to.deep.equal(
          ConfigSettingsController.exampleGetSettingResponse,
        );
        expect(
          appConfigServiceStub.getSingleSetting.calledOnceWith(
            ConfigSettingsController.exampleRelevantSetting.id,
            undefined,
            type,
          ),
        ).to.be.true;
      });

      it('throws an error when the service throws an error', async () => {
        const errorMessage = 'bad things happening';
        appConfigServiceStub.getSingleSetting.throws(
          IctError.badRequest(errorMessage),
        );
        const settingId = 'test';
        const response = await request(app).get(
          `${url}?settingId=${settingId}`,
        );

        expect(response.status).to.equal(IctError.badRequest().statusCode);
        expect(response.body.detail).to.equal(errorMessage);
        expect(appConfigServiceStub.getSingleSetting.calledOnceWith(settingId))
          .to.be.true;
      });
    });

    describe('GET settings/schema', async () => {
      const url = '/config/settings/schema';
      const settingId = 'my-setting-id';
      const settingName = 'my-setting-name';

      const contextService = Container.get(ContextService);
      contextServiceRolesStub = sinon.stub(contextService, 'userRoles');

      it('calls the ConfigSettingsService when requesting a schema with CT_CONFIGURATORS role', async () => {
        appConfigServiceStub.getSettingSchema.resolves(
          ConfigSettingsController.exampleSettingSchemaResponse,
        );

        contextServiceRolesStub.value([SecurityRoles.CT_CONFIGURATORS]);

        const response = await request(app).get(
          `${url}?settingId=${settingId}&settingName=${settingName}`,
        );

        expect(response.status).to.equal(200);
        expect(response.headers['content-type']).to.match(/json/);
        expect(response.body).to.deep.equal(
          ConfigSettingsController.exampleSettingSchemaResponse,
        );
        expect(
          appConfigServiceStub.getSettingSchema.calledOnceWith(
            settingId,
            settingName,
          ),
        );
      });

      it('calls the ConfigSettingsService when requesting a schema with CT_ENGINEERS role', async () => {
        appConfigServiceStub.getSettingSchema.resolves(
          ConfigSettingsController.exampleSettingSchemaResponse,
        );

        contextServiceRolesStub.value([SecurityRoles.CT_ENGINEERS]);

        const response = await request(app).get(
          `${url}?settingId=${settingId}&settingName=${settingName}`,
        );

        expect(response.status).to.equal(200);
        expect(response.headers['content-type']).to.match(/json/);
        expect(response.body).to.deep.equal(
          ConfigSettingsController.exampleSettingSchemaResponse,
        );
        expect(
          appConfigServiceStub.getSettingSchema.calledOnceWith(
            settingId,
            settingName,
          ),
        );
      });

      it('throws a 401 error if user has incorrect authorisation', async () => {
        contextServiceRolesStub.value([]);

        appConfigServiceStub.getSettingSchema.resolves(
          ConfigSettingsController.exampleSettingSchemaResponse,
        );

        const response = await request(app).get(
          `${url}?settingId=${settingId}&settingName=${settingName}`,
        );
        expect(response.status).to.equal(IctError.unauthorized().statusCode);
      });
    });

    describe('PUT /settings', async () => {
      const settingInfoVariations: {
        testDescription: string;
        settingInfo: unknown;
        success: boolean;
        serviceCalled: boolean;
        settingCreated: boolean;
      }[] = [
        {
          testDescription: 'valid setting info with setting ID',
          settingInfo: {
            id: '1234',
            dataType: 'json',
            name: 'test setting',
            value: {},
            levelToUpdate: AppConfigSettingSource.default,
          },
          success: true,
          serviceCalled: true,
          settingCreated: false,
        },
        {
          testDescription: 'valid setting info with setting name',
          settingInfo: {
            name: 'setting-name',
            dataType: 'string',
            value: 'val',
            levelToUpdate: AppConfigSettingSource.tenant,
          },
          success: true,
          serviceCalled: true,
          settingCreated: true,
        },
        {
          testDescription: 'invalid setting info with wrong levelToUpdate',
          settingInfo: {
            id: '12345',
            dataType: 'json',
            value: {},
            levelToUpdate: 'incorrectLevelToUpdate',
          },
          success: false,
          serviceCalled: false,
          settingCreated: false,
        },
        {
          testDescription: 'no setting info',
          settingInfo: undefined,
          success: false,
          serviceCalled: false,
          settingCreated: false,
        },
      ];

      settingInfoVariations.forEach(settingInfoVariationTest => {
        it(settingInfoVariationTest.testDescription, async () => {
          const contextService = Container.get(ContextService);
          contextServiceRolesStub = sinon.stub(contextService, 'userRoles');
          contextServiceRolesStub.value([SecurityRoles.CT_CONFIGURATORS]);

          appConfigServiceStub.updateOrCreateSetting.resolves({
            setting: ConfigSettingsController.exampleRelevantSetting,
            created: settingInfoVariationTest.settingCreated,
          });

          let expectedStatus;
          if (settingInfoVariationTest.success) {
            expectedStatus = settingInfoVariationTest.settingCreated
              ? HttpStatusCodes.CREATED
              : HttpStatusCodes.OK;
          } else {
            expectedStatus = 422;
          }

          const response = await request(app)
            .put('/config/settings')
            .send(settingInfoVariationTest.settingInfo as object);

          expect(response.status).to.equal(expectedStatus);
          expect(response.headers['content-type']).to.match(/json/);
          // only check the body if we're expecting a successful response
          if (settingInfoVariationTest.success) {
            expect(response.body).to.deep.equal(
              ConfigSettingsController.exampleGetSettingResponse,
            );
          }
          expect(
            appConfigServiceStub.updateOrCreateSetting.calledOnceWith(
              settingInfoVariationTest.settingInfo as object,
            ),
          ).to.equal(settingInfoVariationTest.success);
        });
      });

      it('throws a 401 if the user doesnt have the correct role', async () => {
        const contextService = Container.get(ContextService);
        contextServiceRolesStub = sinon.stub(contextService, 'userRoles');
        contextServiceRolesStub.value([]);

        appConfigServiceStub.updateOrCreateSetting.resolves(undefined);

        const response = await request(app)
          .put('/config/settings')
          .send(settingInfoVariations[0].settingInfo as object);
        expect(response.status).to.equal(IctError.unauthorized().statusCode);
      });
    });

    describe('POST /config/insert-default-config', async () => {
      it('returns the expected data when successful', async () => {
        appConfigServiceStub.setDefaultConfigSettings.resolves({
          successful: [ConfigSettingsController.exampleRelevantSetting],
          unsuccessful: [ConfigSettingsController.exampleRelevantSetting],
          existing: [ConfigSettingsController.exampleRelevantSetting],
        });

        const response = await request(app).post(
          '/config/insert-default-config',
        );

        expect(response.status).to.equal(200);
        expect(response.headers['content-type']).to.match(/json/);
        expect(response.body).to.deep.equal(
          ConfigSettingsController.exampleSetDefaultConfigResponse,
        );
        expect(appConfigServiceStub.setDefaultConfigSettings.calledOnce).to.be
          .true;
      });
    });

    describe('DELETE /settings', async () => {
      const url = '/config/settings';

      beforeEach(() => {
        const contextService = Container.get(ContextService);
        contextServiceRolesStub = sinon.stub(contextService, 'userRoles');
        contextServiceRolesStub.value([SecurityRoles.CT_CONFIGURATORS]);
      });

      afterEach(() => {
        sinon.restore();
      });

      it('calls the service with the correct levelToDelete param passed', async () => {
        appConfigServiceStub.deleteSetting.resolves(
          ConfigSettingsController.exampleDeleteSettingResponse,
        );

        const settingId = 'somesettingid';
        const level = AppConfigSettingSource.user;
        const response = await request(app).delete(
          `${url}/${settingId}?levelToDelete=${level}`,
        );

        expect(response.status).to.equal(200);
        expect(response.headers['content-type']).to.match(/json/);
        expect(response.body).to.deep.equal(
          ConfigSettingsController.exampleDeleteSettingResponse,
        );
        expect(
          appConfigServiceStub.deleteSetting.calledOnceWith(settingId, level),
        ).to.be.true;
      });

      it('calls the service without a levelToDelete param passed', async () => {
        appConfigServiceStub.deleteSetting.resolves(
          ConfigSettingsController.exampleDeleteSettingResponse,
        );

        const settingId = 'somesettingid';
        const response = await request(app).delete(`${url}/${settingId}`);

        expect(response.status).to.equal(200);
        expect(response.headers['content-type']).to.match(/json/);
        expect(response.body).to.deep.equal(
          ConfigSettingsController.exampleDeleteSettingResponse,
        );
        expect(appConfigServiceStub.deleteSetting.calledOnceWith(settingId)).to
          .be.true;
      });

      it('throws a 401 if the user doesnt have the correct role', async () => {
        contextServiceRolesStub.value([]);
        appConfigServiceStub.deleteSetting.resolves(undefined);

        const settingId = 'somesettingid';
        const response = await request(app).delete(`${url}/${settingId}`);
        expect(response.status).to.equal(IctError.unauthorized().statusCode);
      });
    });
  });
});
