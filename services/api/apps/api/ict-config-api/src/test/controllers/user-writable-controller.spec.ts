import {expect} from 'chai';
import sinon from 'sinon';
import {
  Container,
  ContextService,
  SecurityRoles,
  AppConfigSettingSource,
  AppConfigSettingDataType,
  HttpStatusCodes,
  appSetup,
} from 'ict-api-foundations';
import {UserWritableGroupType, USER_WRITABLE_GROUP_NAME} from 'ict-api-schema';
import request from 'supertest';
import {ConfigSettingsService} from '../../services/config-settings-service.ts';
import {UserWritableController} from '../../controllers/user-writable-controller.ts';

import {RegisterRoutes} from '../../../build/routes.ts';
import type {UpdateSettingData} from '../../defs/app-configuration-setting.ts';

const app = appSetup(RegisterRoutes);

describe('UserWritableController', () => {
  let controller: UserWritableController;
  let configSettingsService: sinon.SinonStubbedInstance<ConfigSettingsService>;
  let contextService: sinon.SinonStubbedInstance<ContextService>;

  beforeEach(() => {
    configSettingsService = sinon.createStubInstance(ConfigSettingsService);
    contextService = sinon.createStubInstance(ContextService);

    // Set up default context service behavior
    contextService.userId = 'test-user';
    sinon.stub(contextService, 'userRoles').value([]);

    Container.set(ConfigSettingsService, configSettingsService);
    Container.set(ContextService, contextService);

    controller = new UserWritableController();
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('updateUserWritable', () => {
    const testSetting = {
      id: 'test-setting-id',
      name: UserWritableGroupType.Favorites,
      group: USER_WRITABLE_GROUP_NAME,
      dataType: 'json' as AppConfigSettingDataType,
      value: {favorites: []},
      levelToUpdate: AppConfigSettingSource.user,
    };

    it('should allow configurators to create new settings', async () => {
      // Set up context service with configurator role
      sinon
        .stub(contextService, 'userRoles')
        .value([SecurityRoles.CT_CONFIGURATORS]);

      // Mock the service responses
      configSettingsService.getSingleSetting.resolves(undefined);
      configSettingsService.updateOrCreateSetting.resolves({
        setting: testSetting,
        created: true,
      });

      const result = await controller.updateUserWritable(testSetting);

      expect(result).to.deep.equal(testSetting);
      expect(configSettingsService.updateOrCreateSetting.called).to.be.true;
    });

    it('should allow regular users to update existing settings', async () => {
      // Set up context service with regular user role
      sinon.stub(contextService, 'userRoles').value([]);

      // Mock the service responses
      configSettingsService.getSingleSetting.resolves(testSetting);
      configSettingsService.updateOrCreateSetting.resolves({
        setting: testSetting,
        created: false,
      });

      const result = await controller.updateUserWritable(testSetting);

      expect(result).to.deep.equal(testSetting);
      expect(configSettingsService.updateOrCreateSetting.called).to.be.true;
    });

    it('should prevent regular users from creating new settings', async () => {
      // Set up context service with regular user role
      sinon.stub(contextService, 'userRoles').value([]);

      // Mock the service responses - no user setting and no default setting
      configSettingsService.getSingleSetting.onFirstCall().resolves(undefined);
      configSettingsService.getSingleSetting.onSecondCall().resolves(undefined);

      await expect(
        controller.updateUserWritable(testSetting),
      ).to.be.rejectedWith(
        'Setting does not exist. Only updates to existing settings are allowed.',
      );

      expect(configSettingsService.updateOrCreateSetting.called).to.be.false;
    });

    it('should allow regular users to create settings if default setting exists', async () => {
      // Set up context service with regular user role
      sinon.stub(contextService, 'userRoles').value([]);

      const defaultSetting = {
        ...testSetting,
        source: AppConfigSettingSource.default,
      };

      // Mock the service responses - no user setting but default setting exists
      configSettingsService.getSingleSetting.onFirstCall().resolves(undefined);
      configSettingsService.getSingleSetting
        .onSecondCall()
        .resolves(defaultSetting);
      configSettingsService.updateOrCreateSetting.resolves({
        setting: testSetting,
        created: true,
      });

      const result = await controller.updateUserWritable(testSetting);

      expect(result).to.deep.equal(testSetting);
      expect(configSettingsService.updateOrCreateSetting.called).to.be.true;
      expect(
        configSettingsService.updateOrCreateSetting.firstCall.args[0],
      ).to.deep.include({
        ...testSetting,
        value: defaultSetting.value,
      });
    });

    it('should handle array response from getSingleSetting', async () => {
      // Set up context service with regular user role
      sinon.stub(contextService, 'userRoles').value([]);

      const defaultSetting = {
        ...testSetting,
        source: AppConfigSettingSource.default,
      };

      // Mock the service responses - no user setting but default setting exists as array
      configSettingsService.getSingleSetting.onFirstCall().resolves(undefined);
      configSettingsService.getSingleSetting
        .onSecondCall()
        .resolves([defaultSetting]);
      configSettingsService.updateOrCreateSetting.resolves({
        setting: testSetting,
        created: true,
      });

      const result = await controller.updateUserWritable(testSetting);

      expect(result).to.deep.equal(testSetting);
      expect(configSettingsService.updateOrCreateSetting.called).to.be.true;
      expect(
        configSettingsService.updateOrCreateSetting.firstCall.args[0],
      ).to.deep.include({
        ...testSetting,
        value: defaultSetting.value,
      });
    });

    it('should reject settings not in the user-writable group', async () => {
      // Set up context service with configurator role
      sinon
        .stub(contextService, 'userRoles')
        .value([SecurityRoles.CT_CONFIGURATORS]);

      const invalidSetting = {
        ...testSetting,
        group: 'invalid-group',
      };

      await expect(
        controller.updateUserWritable(invalidSetting),
      ).to.be.rejectedWith(
        'Only settings in the user-writable group can be updated through this endpoint',
      );
    });

    it('should prevent users from updating other users settings', async () => {
      // Set up context service with regular user role
      sinon.stub(contextService, 'userRoles').value([]);

      const otherUserSetting = {
        ...testSetting,
        userId: 'other-user',
      };

      await expect(
        controller.updateUserWritable(otherUserSetting),
      ).to.be.rejectedWith(
        'You can only update your own user-writable settings.',
      );
    });
  });

  describe('PUT /config/user-writable', () => {
    let configSettingsServiceStub: sinon.SinonStubbedInstance<ConfigSettingsService>;
    let contextServiceStub: sinon.SinonStubbedInstance<ContextService>;

    beforeEach(() => {
      configSettingsServiceStub = sinon.createStubInstance(
        ConfigSettingsService,
      );
      contextServiceStub = sinon.createStubInstance(ContextService);

      // Set up default context service behavior
      contextServiceStub.userId = 'test-user';
      sinon.stub(contextServiceStub, 'userRoles').value([]);

      Container.set(ConfigSettingsService, configSettingsServiceStub);
      Container.set(ContextService, contextServiceStub);
    });

    it('should update a user writable setting', async () => {
      // Set up context service with configurator role
      sinon
        .stub(contextServiceStub, 'userRoles')
        .value([SecurityRoles.CT_CONFIGURATORS]);

      const settingInfo: UpdateSettingData & {userId?: string} = {
        name: UserWritableGroupType.Favorites,
        group: USER_WRITABLE_GROUP_NAME,
        dataType: 'json' as AppConfigSettingDataType,
        value: {
          favorites: ['route-1', 'route-2'],
        },
        levelToUpdate: AppConfigSettingSource.user,
      };

      // Mock the service responses
      configSettingsServiceStub.getSingleSetting.resolves({
        id: '0e0f42b-1b58-4a5c-a785-5e6af',
        name: UserWritableGroupType.Favorites,
        group: USER_WRITABLE_GROUP_NAME,
        dataType: 'json',
        value: {
          favorites: ['route-1', 'route-2'],
        },
        source: AppConfigSettingSource.user,
      });
      configSettingsServiceStub.updateOrCreateSetting.resolves({
        setting: {
          id: '0e0f42b-1b58-4a5c-a785-5e6af',
          name: UserWritableGroupType.Favorites,
          group: USER_WRITABLE_GROUP_NAME,
          dataType: 'json',
          value: {
            favorites: ['route-1', 'route-2'],
          },
          source: AppConfigSettingSource.user,
        },
        created: false,
      });

      const response = await request(app)
        .put('/config/user-writable')
        .send(settingInfo);

      expect(response.status).to.equal(HttpStatusCodes.OK);
      expect(response.body).to.deep.equal({
        id: '0e0f42b-1b58-4a5c-a785-5e6af',
        name: UserWritableGroupType.Favorites,
        group: USER_WRITABLE_GROUP_NAME,
        dataType: 'json',
        value: {
          favorites: ['route-1', 'route-2'],
        },
        source: AppConfigSettingSource.user,
      });

      expect(configSettingsServiceStub.updateOrCreateSetting.calledOnce).to.be
        .true;
      const callArgs =
        configSettingsServiceStub.updateOrCreateSetting.firstCall.args[0];
      expect(callArgs).to.deep.include(settingInfo);
      expect(callArgs).to.have.property('userId', undefined);
    });

    it('should create a new setting if it does not exist', async () => {
      // Set up context service with configurator role
      sinon
        .stub(contextServiceStub, 'userRoles')
        .value([SecurityRoles.CT_CONFIGURATORS]);

      const settingInfo: UpdateSettingData & {userId?: string} = {
        name: UserWritableGroupType.Favorites,
        group: USER_WRITABLE_GROUP_NAME,
        dataType: 'json' as AppConfigSettingDataType,
        value: {
          favorites: ['route-1', 'route-2'],
        },
        levelToUpdate: AppConfigSettingSource.user,
      };

      // Mock the service responses
      configSettingsServiceStub.getSingleSetting.resolves(undefined);
      configSettingsServiceStub.updateOrCreateSetting.resolves({
        setting: {
          id: '0e0f42b-1b58-4a5c-a785-5e6af',
          name: UserWritableGroupType.Favorites,
          group: USER_WRITABLE_GROUP_NAME,
          dataType: 'json',
          value: {
            favorites: ['route-1', 'route-2'],
          },
          source: AppConfigSettingSource.user,
        },
        created: true,
      });

      const response = await request(app)
        .put('/config/user-writable')
        .send(settingInfo);

      expect(response.status).to.equal(HttpStatusCodes.CREATED);
      expect(response.body).to.deep.equal({
        id: '0e0f42b-1b58-4a5c-a785-5e6af',
        name: UserWritableGroupType.Favorites,
        group: USER_WRITABLE_GROUP_NAME,
        dataType: 'json',
        value: {
          favorites: ['route-1', 'route-2'],
        },
        source: AppConfigSettingSource.user,
      });

      expect(configSettingsServiceStub.updateOrCreateSetting.calledOnce).to.be
        .true;
      const callArgs =
        configSettingsServiceStub.updateOrCreateSetting.firstCall.args[0];
      expect(callArgs).to.deep.include(settingInfo);
      expect(callArgs).to.have.property('userId', undefined);
    });
  });
});
