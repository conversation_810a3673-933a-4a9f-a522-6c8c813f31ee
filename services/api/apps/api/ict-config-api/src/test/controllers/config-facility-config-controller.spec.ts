import {expect} from 'chai';
import {
  Container,
  ContextService,
  HttpStatusCodes,
  SecurityRoles,
  WinstonLogger,
  appSetup,
} from 'ict-api-foundations';
import 'reflect-metadata';
import * as sinon from 'sinon';
import request from 'supertest';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {ConfigFacilityConfigController} from '../../controllers/config-facility-config-controller.ts';
import {ConfigSettingsService} from '../../services/config-settings-service.ts';

describe('ConfigFacilityConfigController', () => {
  let controller: ConfigFacilityConfigController;
  let configSettingsService: sinon.SinonStubbedInstance<ConfigSettingsService>;
  let contextService: ContextService;
  let logger: sinon.SinonStubbedInstance<WinstonLogger>;
  let selectedFacilityIdStub: sinon.SinonStub;
  let userRolesStub: sinon.SinonStub;
  let facilityMapsStub: sinon.SinonStub;
  const app = appSetup(RegisterRoutes);

  beforeEach(() => {
    configSettingsService = sinon.createStubInstance(ConfigSettingsService);
    logger = sinon.createStubInstance(WinstonLogger);

    Container.set(ConfigSettingsService, configSettingsService);
    Container.set(WinstonLogger, logger);

    // Get the actual context service from container and stub its properties
    contextService = Container.get(ContextService);
    selectedFacilityIdStub = sinon
      .stub(contextService, 'selectedFacilityId')
      .value('test-facility-id');
    userRolesStub = sinon.stub(contextService, 'userRoles').value([]);
    facilityMapsStub = sinon.stub(contextService, 'facilityMaps').value([]);

    controller = new ConfigFacilityConfigController();
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getConfigurationSettings', () => {
    it('should return all settings when facility is selected', async () => {
      const mockSettings =
        ConfigFacilityConfigController.exampleRelevantSetting;
      configSettingsService.getAllSettings.resolves(mockSettings);

      const result = await controller.getConfigurationSettings();

      expect(result).to.deep.equal(mockSettings);
      expect(configSettingsService.getAllSettings.calledOnce).to.be.true;
    });

    it('should find non-default but available facility when no facility is selected', async () => {
      const mockSettings =
        ConfigFacilityConfigController.exampleRelevantSetting;

      // Override the stub values for this test
      selectedFacilityIdStub.value(null);
      userRolesStub.value(['facility-2']);
      facilityMapsStub.value([
        {
          id: 'facility-1',
          default: true,
          name: 'Test Facility',
          dataset: 'test-dataset',
        },
        {
          id: 'facility-2',
          default: false,
          name: 'Test Facility 2',
          dataset: 'test-dataset-2',
        },
      ]);

      // Mock getSingleSetting to return undefined (no user setting)
      configSettingsService.getSingleSetting.resolves(undefined);
      configSettingsService.getAllSettings.resolves(mockSettings);

      const result = await controller.getConfigurationSettings();

      expect(result).to.deep.equal(mockSettings);
      expect(configSettingsService.getAllSettings.calledOnce).to.be.true;
    });

    it('should not use default facility when user has no roles', async () => {
      const mockSettings =
        ConfigFacilityConfigController.exampleRelevantSetting;

      // Override the stub values for this test
      selectedFacilityIdStub.value(null);
      userRolesStub.value([]);
      facilityMapsStub.value([
        {
          id: 'facility-1',
          default: true,
          name: 'Test Facility',
          dataset: 'test-dataset',
        },
      ]);

      // Mock getSingleSetting to return undefined (no user setting)
      configSettingsService.getSingleSetting.resolves(undefined);
      configSettingsService.getAllSettings.resolves(mockSettings);

      await expect(controller.getConfigurationSettings()).to.be.rejectedWith(
        'No Facility could be found!',
      );
    });

    it('should use user selected facility when available', async () => {
      const mockSettings =
        ConfigFacilityConfigController.exampleRelevantSetting;

      // Override the stub values for this test
      selectedFacilityIdStub.value(null);
      userRolesStub.value([]);
      facilityMapsStub.value([]);

      // Mock getSingleSetting to return user's selected facility
      configSettingsService.getSingleSetting.resolves({
        id: 'test-id',
        name: 'selected-facility-id',
        dataType: 'string',
        value: 'user-selected-facility-id',
      });
      configSettingsService.getAllSettings.resolves(mockSettings);

      const result = await controller.getConfigurationSettings();

      expect(result).to.deep.equal(mockSettings);
      expect(configSettingsService.getAllSettings.calledOnce).to.be.true;
    });

    it('should return all settings for internal users when no facility found', async () => {
      const mockSettings =
        ConfigFacilityConfigController.exampleRelevantSetting;

      // Override the stub values for this test
      selectedFacilityIdStub.value(null);
      userRolesStub.value([SecurityRoles.CT_CONFIGURATORS]);
      facilityMapsStub.value([]);

      // Mock getSingleSetting to return undefined (no user setting)
      configSettingsService.getSingleSetting.resolves(undefined);
      configSettingsService.getAllSettings.resolves(mockSettings);

      const result = await controller.getConfigurationSettings();

      expect(result).to.deep.equal(mockSettings);
      expect(configSettingsService.getAllSettings.calledOnce).to.be.true;
    });

    it('should throw not found error when no facility found for regular user', async () => {
      // Override the stub values for this test
      selectedFacilityIdStub.value(null);
      userRolesStub.value([]);
      facilityMapsStub.value([]);

      // Mock getSingleSetting to return undefined (no user setting)
      configSettingsService.getSingleSetting.resolves(undefined);

      await expect(controller.getConfigurationSettings()).to.be.rejectedWith(
        'No Facility could be found!',
      );

      expect(configSettingsService.getAllSettings.called).to.be.false;
    });
  });

  describe('GET /config/facility-config', () => {
    it('should return facility configuration settings', async () => {
      const mockSettings =
        ConfigFacilityConfigController.exampleRelevantSetting;
      configSettingsService.getAllSettings.resolves(mockSettings);

      const response = await request(app).get('/config/facility-config');

      expect(response.status).to.equal(HttpStatusCodes.OK);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(mockSettings);
    });

    it('should return 404 when no facility found for regular user', async () => {
      // Override the stub values for this test
      selectedFacilityIdStub.value(null);
      userRolesStub.value([]);
      facilityMapsStub.value([]);

      const response = await request(app).get('/config/facility-config');

      expect(response.status).to.equal(HttpStatusCodes.NOT_FOUND);
    });
  });
});
