import 'reflect-metadata';
import * as sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
import {AppConfigService} from '../../services/app-config-service.ts';
import {AppConfigController} from '../../controllers/app-config-controller.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {AppConfigDbService} from '../../services/app-config-db-service.ts';

describe('AppConfigController', () => {
  const app = appSetup(RegisterRoutes);

  afterEach(() => {
    sinon.restore();
  });

  describe('config route', () => {
    let appConfigServiceStub: sinon.SinonStubbedInstance<AppConfigService>;
    let appConfigDbServiceStub: sinon.SinonStubbedInstance<AppConfigDbService>;

    beforeEach(() => {
      appConfigServiceStub = sinon.createStubInstance(AppConfigService);
      appConfigDbServiceStub = sinon.createStubInstance(AppConfigDbService);
      Container.set(AppConfigService, appConfigServiceStub);
      Container.set(AppConfigDbService, appConfigDbServiceStub);
    });

    it('should call "AppConfigService.getApponfigData" without a config key', async () => {
      appConfigServiceStub.getAppConfigData.resolves(
        JSON.stringify(AppConfigController.exampleResponse)
      );

      const response = await request(app).get('/config/app-config');

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(AppConfigController.exampleResponse);

      sinon.assert.calledWithExactly(
        appConfigServiceStub.getAppConfigData,
        undefined
      );
    });

    it('should call "AppConfigService.getApponfigData" passing in the config key', async () => {
      const config = 'sample';

      appConfigServiceStub.getAppConfigData.resolves(
        JSON.stringify(AppConfigController.exampleResponse)
      );

      const response = await request(app)
        .get('/config/app-config')
        .query({config});

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(AppConfigController.exampleResponse);

      sinon.assert.calledWithExactly(
        appConfigServiceStub.getAppConfigData,
        config
      );
    });

    it('should error when the service throws an error', async () => {
      const errorMessage = 'Something bad happened';
      appConfigServiceStub.getAppConfigData.rejects(new Error(errorMessage));
      const response = await request(app).get('/config/app-config');

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.title).to.equal('Internal Server Error');
    });

    // ICT-2163: commented out for now until we can figure out how to test this with the @Middlewares decorator using ProtectedRouteMiddleware.apiProtectedDataRoute()
    /* it('should call "AppConfigDbService.getCuratedTableNames"', async () => {
      appConfigDbServiceStub.getCuratedTableNames.resolves(
        AppConfigCuratedTableListController.exampleResponse.data
      );

      const response = await request(app).get('/config/curated-tables/list');

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        JSON.parse(AppConfigController.exampleResponse)
      );

      sinon.assert.called(appConfigDbServiceStub.getCuratedTableNames);
    }); */
  });
});
