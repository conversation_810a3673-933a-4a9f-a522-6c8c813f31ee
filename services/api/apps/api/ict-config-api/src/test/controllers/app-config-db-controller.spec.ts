import 'reflect-metadata';
import * as sinon from 'sinon';
import request from 'supertest';
import {
  Container,
  ContextService,
  SecurityRoles,
  appSetup,
} from 'ict-api-foundations';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {AppConfigDbService} from '../../services/app-config-db-service.ts';

describe('AppConfigDbController', () => {
  const app = appSetup(RegisterRoutes);
  afterEach(() => {
    sinon.restore();
  });
  describe('config db route', () => {
    let appConfigDbServiceStub: sinon.SinonStubbedInstance<AppConfigDbService>;
    let contextServiceRolesStub: sinon.SinonStub;
    const queryParams = {table: 'dim_zone'};
    const url = '/config/curated-data';
    beforeEach(() => {
      appConfigDbServiceStub = sinon.createStubInstance(AppConfigDbService);
      Container.set(AppConfigDbService, appConfigDbServiceStub);
    });
    afterEach(() => {
      sinon.restore();
    });
    it('should fail when no table query sent', async () => {
      const contextService = Container.get(ContextService);
      contextServiceRolesStub = sinon.stub(contextService, 'userRoles');
      contextServiceRolesStub.value([SecurityRoles.CT_ENGINEERS]);

      const response = await request(app).get(url).query({});
      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.details.table.message).to.equal(
        "'table' is required"
      );
    });

    it('should return an error when the service fails', async () => {
      const contextService = Container.get(ContextService);
      contextServiceRolesStub = sinon.stub(contextService, 'userRoles');
      contextServiceRolesStub.value([SecurityRoles.CT_ENGINEERS]);

      appConfigDbServiceStub.getCuratedDataset.rejects(
        new Error('Something bad happened')
      );
      const response = await request(app).get(url).query(queryParams);
      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.title).to.deep.equal('Internal Server Error');

      sinon.assert.calledWith(
        appConfigDbServiceStub.getCuratedDataset,
        queryParams.table
      );
    });

    it('should call "AppConfigDbService.getCuratedDataset"', async () => {
      const contextService = Container.get(ContextService);
      contextServiceRolesStub = sinon.stub(contextService, 'userRoles');
      contextServiceRolesStub.value([SecurityRoles.CT_ENGINEERS]);

      appConfigDbServiceStub.getCuratedDataset.resolves([]);
      const response = await request(app).get(url).query(queryParams);
      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      sinon.assert.calledWith(
        appConfigDbServiceStub.getCuratedDataset,
        queryParams.table
      );
    });

    it('should throw a forbidden error if the user does not have the CT_ENGINEERS role', async () => {
      const response = await request(app).get(url);
      expect(response.status).to.equal(401);
      expect(response.body.detail).to.deep.equal('Forbidden');
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.title).to.deep.equal('Unauthorized');
    });
  });
});
