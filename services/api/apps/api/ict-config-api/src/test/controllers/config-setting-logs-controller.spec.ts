import {appSetup, Container, IctError} from 'ict-api-foundations';
import sinon from 'sinon';
import request from 'supertest';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {ConfigSettingLogsService} from '../../services/config-setting-logs-service.ts';

const testLogs = [
  {
    changedBy: 'testUser',
    timestamp: new Date('2024-08-14T12:26:29.001Z'),
    source: 'user',
    newValue: 'false',
  },
  {
    changedBy: 'testUser',
    timestamp: new Date('2024-08-14T12:26:28.261Z'),
    source: 'user',
    newValue: '33',
  },
  {
    changedBy: 'testUser',
    timestamp: new Date('2024-08-14T12:26:27.557Z'),
    source: 'tenant',
    newValue: 'testStringValue',
  },
  {
    changedBy: 'testUser',
    timestamp: new Date('2024-08-14T12:26:26.933Z'),
    source: 'tenant',
    newValue: '{json data}',
  },
];

describe('ConfigSettingLogsController', () => {
  const app = appSetup(RegisterRoutes);

  afterEach(() => {
    sinon.restore();
  });

  describe('config route', () => {
    let appConfigServiceStub: sinon.SinonStubbedInstance<ConfigSettingLogsService>;

    beforeEach(() => {
      appConfigServiceStub = sinon.createStubInstance(ConfigSettingLogsService);
      Container.set(ConfigSettingLogsService, appConfigServiceStub);
    });

    describe('GET /setting-logs', async () => {
      const url = '/config/setting-logs';

      it('error response if no settingId is passed', async () => {
        const response = await request(app).get(url);
        expect(response.status).to.equal(
          IctError.unprocessableRequest().statusCode
        );
        expect(appConfigServiceStub.getSettingLogs.calledOnce).to.be.false;
      });

      it('data response if a valid settingId is passed', async () => {
        appConfigServiceStub.getSettingLogs.resolves(testLogs);

        const response = await request(app).get(
          `${url}?setting_id=testChangedSetting`
        );

        expect(response.status).to.equal(200);
        expect(response.body.data.length).to.equal(testLogs.length);
        expect(appConfigServiceStub.getSettingLogs.calledOnce).to.be.true;
      });

      it('noContent response if there are no logs to return', async () => {
        appConfigServiceStub.getSettingLogs.throws(
          IctError.noContent('no logs match this settingId')
        );

        const response = await request(app).get(
          `${url}?setting_id=testChangedSetting`
        );

        expect(response.status).to.equal(IctError.noContent().statusCode);
        expect(response.body).to.be.empty;
        expect(appConfigServiceStub.getSettingLogs.calledOnce).to.be.true;
      });
    });
  });
});
