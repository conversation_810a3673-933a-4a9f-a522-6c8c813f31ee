import 'reflect-metadata';
import sinon from 'sinon';
import request from 'supertest';
import {
  Container,
  ContextService,
  appSetup,
  ApiMiddleware,
  EnvironmentService,
  Environment,
  ConfigStore,
  AppConfigSettingSource,
  IctError,
  SecurityRoles,
} from 'ict-api-foundations';
import {EntityTypes, CustomMetricConfigurationEntity} from 'ict-api-schema';
import {expect} from 'chai';
import {MetricConfigService} from '../../services/metric-config-service.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import type {MetricConfigDetail} from '../../defs/metric-config-detail.ts';

describe('MetricConfigController', () => {
  const app = appSetup(RegisterRoutes);
  let metricConfigServiceStub: sinon.SinonStubbedInstance<MetricConfigService>;
  let contextServiceRolesStub: sinon.SinonStub;

  before(async () => {
    // Mock environment service to return local environment
    const envService = Container.get(EnvironmentService);
    sinon.stub(envService, 'app').get(() => ({
      env: Environment.local,
      authDomain: 'test-domain',
      authAudience: 'test-audience',
      unitTest: 'true',
    }));

    // Mock ConfigStore to return facility maps
    const configStore = Container.get(ConfigStore);
    sinon.stub(configStore, 'findSpecificSettingValue').resolves({
      id: 'facility-maps',
      name: 'facility-maps',
      group: 'facility',
      dataType: 'json',
      value: [
        {
          id: 'test-facility',
          name: 'Test Facility',
          dataset: 'test-dataset',
          default: true,
        },
      ],
      source: AppConfigSettingSource.default,
    });

    // Apply all middlewares
    await ApiMiddleware.applyApiDefaultMiddlewares(app);
  });

  beforeEach(() => {
    metricConfigServiceStub = sinon.createStubInstance(MetricConfigService);
    Container.set(MetricConfigService, metricConfigServiceStub);

    // Mock context service roles
    const contextService = Container.get(ContextService);
    contextServiceRolesStub = sinon.stub(contextService, 'userRoles');
    contextServiceRolesStub.value([SecurityRoles.CT_ENGINEERS]);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getMetricConfigSummaries route', () => {
    const url = '/config/process-flow/metric-configs';
    const queryParams = {
      metricName: 'test-metric',
      configType: 'node',
      metricId: undefined,
      factType: undefined,
      nodeName: undefined,
      active: undefined,
      enabled: undefined,
    };

    it('should return 404 when no default facility exists', async () => {
      // Mock context service to return no default facility
      const contextService = Container.get(ContextService);
      sinon.stub(contextService, 'selectedFacilityId').get(() => undefined);
      sinon.stub(contextService, 'facilityMaps').get(() => []);

      // Stub the metric config service to throw facility error
      metricConfigServiceStub.getMetricConfigSummaries.rejects(
        IctError.notFound(
          'No default facility found in facility maps. Please ensure a default facility is configured.',
        ),
      );

      const response = await request(app).get(url).query(queryParams);

      expect(response.status).to.equal(IctError.notFound().statusCode);
      expect(response.body).to.have.property(
        'detail',
        'No default facility found in facility maps. Please ensure a default facility is configured.',
      );
      expect(response.headers['content-type']).to.match(/json/);
      sinon.assert.calledOnce(metricConfigServiceStub.getMetricConfigSummaries);
    });

    it('should return 404 when no metric configs are found', async () => {
      // Mock context service to return a default facility
      const contextService = Container.get(ContextService);
      sinon.stub(contextService, 'selectedFacilityId').get(() => undefined);
      sinon.stub(contextService, 'facilityMaps').get(() => [
        {
          id: 'default',
          name: 'Default Facility',
          dataset: 'test-dataset',
          default: true,
        },
      ]);

      metricConfigServiceStub.getMetricConfigSummaries.resolves([]);

      const response = await request(app).get(url).query(queryParams);

      expect(response.status).to.equal(IctError.notFound().statusCode);
      expect(response.body)
        .to.have.property('detail')
        .that.includes('No metric configurations found with filters');
      expect(response.headers['content-type']).to.match(/json/);
      sinon.assert.calledOnce(metricConfigServiceStub.getMetricConfigSummaries);
      sinon.assert.calledWith(
        metricConfigServiceStub.getMetricConfigSummaries,
        queryParams,
      );
    });

    it('should return metric configs when found', async () => {
      // Mock context service to return a default facility
      const contextService = Container.get(ContextService);
      sinon.stub(contextService, 'selectedFacilityId').get(() => undefined);
      sinon.stub(contextService, 'facilityMaps').get(() => [
        {
          id: 'default',
          name: 'Default Facility',
          dataset: 'test-dataset',
          default: true,
        },
      ]);

      const mockConfig = {
        id: 'test-config',
        metricName: 'test-metric',
        configType: 'node',
        value: {some: 'value'},
        isCustom: false,
        facilityId: 'default',
      };
      metricConfigServiceStub.getMetricConfigSummaries.resolves([mockConfig]);

      const response = await request(app).get(url).query(queryParams);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal([mockConfig]);
      sinon.assert.calledOnce(metricConfigServiceStub.getMetricConfigSummaries);
      sinon.assert.calledWith(
        metricConfigServiceStub.getMetricConfigSummaries,
        queryParams,
      );
    });

    it('should handle service errors', async () => {
      // Mock context service to return a default facility
      const contextService = Container.get(ContextService);
      sinon.stub(contextService, 'selectedFacilityId').get(() => undefined);
      sinon.stub(contextService, 'facilityMaps').get(() => [
        {
          id: 'default',
          name: 'Default Facility',
          dataset: 'test-dataset',
          default: true,
        },
      ]);

      metricConfigServiceStub.getMetricConfigSummaries.rejects(
        IctError.internalServerError('Service error'),
      );

      const response = await request(app).get(url).query(queryParams);

      expect(response.status).to.equal(500);
      expect(response.body).to.have.property('title', 'Internal Server Error');
      expect(response.headers['content-type']).to.match(/json/);
      sinon.assert.calledOnce(metricConfigServiceStub.getMetricConfigSummaries);
      sinon.assert.calledWith(
        metricConfigServiceStub.getMetricConfigSummaries,
        queryParams,
      );
    });
  });

  describe('putMetricConfig route', () => {
    const url = '/config/process-flow/metric-config';

    it('should create a new metric configuration and return 201', async () => {
      // Mock context service to return a default facility
      const contextService = Container.get(ContextService);
      sinon
        .stub(contextService, 'selectedFacilityId')
        .get(() => 'test-facility');
      sinon.stub(contextService, 'facilityMaps').get(() => [
        {
          id: 'test-facility',
          name: 'Test Facility',
          dataset: 'test-dataset',
          default: true,
        },
      ]);

      const input: MetricConfigDetail = {
        metricConfigName: 'test-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      const mockResult = {
        config: new CustomMetricConfigurationEntity(),
        isNew: true,
      };
      Object.assign(mockResult.config, {
        id: 'new-config-id',
        facilityId: 'test-facility',
        ...input,
      });

      metricConfigServiceStub.updateOrCreateMetricConfig.resolves(mockResult);

      const response = await request(app)
        .put(url)
        .send(input)
        .expect('Content-Type', /json/);

      expect(response.status).to.equal(201);
      expect(response.body).to.have.property('id', 'new-config-id');
      expect(response.body).to.have.property('metricConfigName', 'test-metric');
      expect(response.body).to.have.property('configType', 'node');
      expect(response.body).to.have.property('isCustom', true);
      expect(response.body).to.have.property('facilityId', 'test-facility');
      sinon.assert.calledOnce(
        metricConfigServiceStub.updateOrCreateMetricConfig,
      );
      sinon.assert.calledWith(
        metricConfigServiceStub.updateOrCreateMetricConfig,
        input,
      );
    });

    it('should update an existing metric configuration and return 200', async () => {
      // Mock context service to return a default facility
      const contextService = Container.get(ContextService);
      sinon
        .stub(contextService, 'selectedFacilityId')
        .get(() => 'test-facility');
      sinon.stub(contextService, 'facilityMaps').get(() => [
        {
          id: 'test-facility',
          name: 'Test Facility',
          dataset: 'test-dataset',
          default: true,
        },
      ]);

      const input: MetricConfigDetail = {
        metricConfigName: 'existing-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Existing Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      const mockResult = {
        config: new CustomMetricConfigurationEntity(),
        isNew: false,
      };
      Object.assign(mockResult.config, {
        id: 'existing-config-id',
        facilityId: 'test-facility',
        ...input,
      });

      metricConfigServiceStub.updateOrCreateMetricConfig.resolves(mockResult);

      const response = await request(app)
        .put(url)
        .send(input)
        .expect('Content-Type', /json/);

      expect(response.status).to.equal(200);
      expect(response.body).to.have.property('id', 'existing-config-id');
      expect(response.body).to.have.property(
        'metricConfigName',
        'existing-metric',
      );
      expect(response.body).to.have.property('configType', 'node');
      expect(response.body).to.have.property('isCustom', true);
      expect(response.body).to.have.property('facilityId', 'test-facility');
      sinon.assert.calledOnce(
        metricConfigServiceStub.updateOrCreateMetricConfig,
      );
      sinon.assert.calledWith(
        metricConfigServiceStub.updateOrCreateMetricConfig,
        input,
      );
    });

    it('should handle service errors gracefully', async () => {
      // Mock context service to return a default facility
      const contextService = Container.get(ContextService);
      sinon
        .stub(contextService, 'selectedFacilityId')
        .get(() => 'test-facility');
      sinon.stub(contextService, 'facilityMaps').get(() => [
        {
          id: 'test-facility',
          name: 'Test Facility',
          dataset: 'test-dataset',
          default: true,
        },
      ]);

      const input: MetricConfigDetail = {
        metricConfigName: 'test-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      metricConfigServiceStub.updateOrCreateMetricConfig.rejects(
        IctError.internalServerError('Database connection failed'),
      );

      const response = await request(app)
        .put(url)
        .send(input)
        .expect('Content-Type', /json/);

      expect(response.status).to.equal(500);
      expect(response.body).to.have.property('title', 'Internal Server Error');
      sinon.assert.calledOnce(
        metricConfigServiceStub.updateOrCreateMetricConfig,
      );
    });
  });
});
