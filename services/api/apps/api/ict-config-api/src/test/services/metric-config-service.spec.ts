import sinon from 'sinon';
import {expect} from 'chai';
import {ContextService, WinstonLogger, ConfigStore} from 'ict-api-foundations';
import {
  DefaultMetricConfigurationEntity,
  CustomMetricConfigurationEntity,
} from 'ict-api-schema';
import {MetricConfigService} from '../../services/metric-config-service.ts';
import {MetricConfigStore} from '../../stores/metric-config-store.ts';
import {MetricConfigSummaryFilters} from '../../defs/metric-config-filters.ts';
import type {MetricConfigDetail} from '../../defs/metric-config-detail.ts';

describe('MetricConfigService', () => {
  let service: MetricConfigService;
  let metricConfigStore: MetricConfigStore;
  let context: ContextService;
  let logger: WinstonLogger;
  let configStore: ConfigStore;

  beforeEach(() => {
    context = new ContextService();
    logger = new WinstonLogger(context);
    configStore = new ConfigStore(context);
    metricConfigStore = new MetricConfigStore(context, logger);
    service = new MetricConfigService(
      configStore,
      logger,
      context,
      metricConfigStore,
    );

    // Mock facility maps
    sinon.stub(context, 'facilityMaps').get(() => [
      {
        id: 'test-facility',
        name: 'Test Facility',
        dataset: 'test-dataset',
        default: true,
      },
    ]);

    // Mock store's getMetricConfigs method
    sinon.stub(metricConfigStore, 'getMetricConfigSummaries').resolves([]);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getMetricConfigSummaries', () => {
    it('should return both default and custom metric configurations', async () => {
      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      mockDefaultConfig.id = '1';
      mockDefaultConfig.metricConfigName = 'test-metric';
      mockDefaultConfig.configType = 'node';
      mockDefaultConfig.nodeName = 'test-node';
      mockDefaultConfig.factType = 'test-fact';
      mockDefaultConfig.enabled = {default: true};
      mockDefaultConfig.active = {default: true};

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      mockCustomConfig.id = '2';
      mockCustomConfig.metricConfigName = 'test-metric';
      mockCustomConfig.configType = 'node';
      mockCustomConfig.nodeName = 'custom-node';
      mockCustomConfig.factType = 'custom-fact';
      mockCustomConfig.active = true;
      mockCustomConfig.facilityId = 'test-facility';

      const mockConfigs = [mockDefaultConfig, mockCustomConfig];

      (metricConfigStore.getMetricConfigSummaries as sinon.SinonStub).resolves(
        mockConfigs,
      );
      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility');

      const filters: MetricConfigSummaryFilters = {
        metricName: 'test-metric',
        configType: 'node',
      };

      const result = await service.getMetricConfigSummaries(filters);

      expect(result).to.deep.equal(
        mockConfigs.map(config => ({
          id: config.id,
          metricName: config.metricConfigName,
          configType: config.configType,
          nodeName: config.nodeName,
          factType: config.factType,
          enabled:
            config instanceof DefaultMetricConfigurationEntity
              ? config.enabled
              : null,
          active:
            config instanceof CustomMetricConfigurationEntity
              ? config.active
              : config.active,
          isCustom: config instanceof CustomMetricConfigurationEntity,
          facilityId:
            config instanceof CustomMetricConfigurationEntity
              ? config.facilityId
              : 'default',
        })),
      );
    });

    it('should throw not found error when no facility ID is provided and no default facility exists', async () => {
      sinon.stub(context, 'facilityMaps').get(() => []);
      sinon.stub(context, 'selectedFacilityId').get(() => undefined);

      const filters: MetricConfigSummaryFilters = {
        metricName: 'test-metric',
        configType: 'node',
      };

      await expect(
        service.getMetricConfigSummaries(filters),
      ).to.be.rejectedWith(
        'No default facility found in facility maps. Please ensure a default facility is configured.',
      );
    });

    it('should filter out default configs that have custom versions', async () => {
      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      mockDefaultConfig.id = '1';
      mockDefaultConfig.metricConfigName = 'test-metric';
      mockDefaultConfig.configType = 'node';
      mockDefaultConfig.nodeName = 'test-node';
      mockDefaultConfig.factType = 'test-fact';
      mockDefaultConfig.enabled = {default: true};
      mockDefaultConfig.active = {default: true};

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      mockCustomConfig.id = '2';
      mockCustomConfig.metricConfigName = 'test-metric';
      mockCustomConfig.configType = 'node';
      mockCustomConfig.nodeName = 'custom-node';
      mockCustomConfig.factType = 'custom-fact';
      mockCustomConfig.active = true;
      mockCustomConfig.facilityId = 'test-facility';

      (metricConfigStore.getMetricConfigSummaries as sinon.SinonStub).resolves([
        mockCustomConfig,
      ]);
      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility');

      const filters: MetricConfigSummaryFilters = {
        metricName: 'test-metric',
        configType: 'node',
      };

      const result = await service.getMetricConfigSummaries(filters);

      expect(result).to.deep.equal([
        {
          id: mockCustomConfig.id,
          metricName: mockCustomConfig.metricConfigName,
          configType: mockCustomConfig.configType,
          nodeName: mockCustomConfig.nodeName,
          factType: mockCustomConfig.factType,
          enabled: null,
          active: mockCustomConfig.active,
          isCustom: true,
          facilityId: mockCustomConfig.facilityId,
        },
      ]);
    });

    it('should filter out disabled configs', async () => {
      const mockDefaultConfig = new DefaultMetricConfigurationEntity();
      mockDefaultConfig.id = '1';
      mockDefaultConfig.metricConfigName = 'test-metric';
      mockDefaultConfig.configType = 'node';
      mockDefaultConfig.nodeName = 'test-node';
      mockDefaultConfig.factType = 'test-fact';
      mockDefaultConfig.enabled = {default: false};
      mockDefaultConfig.active = {default: true};

      const mockCustomConfig = new CustomMetricConfigurationEntity();
      mockCustomConfig.id = '2';
      mockCustomConfig.metricConfigName = 'test-metric';
      mockCustomConfig.configType = 'node';
      mockCustomConfig.nodeName = 'custom-node';
      mockCustomConfig.factType = 'custom-fact';
      mockCustomConfig.active = true;
      mockCustomConfig.facilityId = 'test-facility';

      (metricConfigStore.getMetricConfigSummaries as sinon.SinonStub).resolves(
        [],
      );
      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility');

      const filters: MetricConfigSummaryFilters = {
        metricName: 'test-metric',
        configType: 'node',
      };

      await expect(
        service.getMetricConfigSummaries(filters),
      ).to.be.rejectedWith(
        'No metric configurations found matching the criteria',
      );
    });

    it('should handle store errors', async () => {
      (metricConfigStore.getMetricConfigSummaries as sinon.SinonStub).rejects(
        new Error('Store error'),
      );
      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility');

      const filters: MetricConfigSummaryFilters = {
        metricName: 'test-metric',
        configType: 'node',
      };

      await expect(
        service.getMetricConfigSummaries(filters),
      ).to.be.rejectedWith('Store error');
    });
  });

  describe('updateOrCreateMetricConfig', () => {
    it('should create a new metric configuration when using selected facility', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      const mockResult = {
        config: new CustomMetricConfigurationEntity(),
        isNew: true,
      };
      Object.assign(mockResult.config, {
        id: 'new-config-id',
        ...input,
      });

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility');
      sinon.stub(metricConfigStore, 'putMetricConfig').resolves(mockResult);

      const result = await service.updateOrCreateMetricConfig(input);

      expect(result).to.deep.equal(mockResult);
      expect(result.isNew).to.be.true;
      expect(result.config.id).to.equal('new-config-id');
    });

    it('should throw error when no facility ID is provided', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      sinon.stub(context, 'selectedFacilityId').get(() => undefined);

      await expect(
        service.updateOrCreateMetricConfig(input),
      ).to.be.rejectedWith(
        'No facility ID provided. Please ensure the ict-facility-id header is set.',
      );
    });

    it('should update an existing metric configuration', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'existing-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Existing Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      const mockResult = {
        config: new CustomMetricConfigurationEntity(),
        isNew: false,
      };
      Object.assign(mockResult.config, {
        id: 'existing-config-id',
        ...input,
      });

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility');
      sinon.stub(metricConfigStore, 'putMetricConfig').resolves(mockResult);

      const result = await service.updateOrCreateMetricConfig(input);

      expect(result).to.deep.equal(mockResult);
      expect(result.isNew).to.be.false;
      expect(result.config.id).to.equal('existing-config-id');
    });

    it('should handle store errors', async () => {
      const input: MetricConfigDetail = {
        metricConfigName: 'test-metric',
        configType: 'node',
        views: ['facility'],
        matchConditions: {eventType: 'test'},
        factType: 'test-fact',
        sourceSystem: 'diq',
        displayName: 'Test Metric',
        enabled: true,
        active: true,
        isCustom: true,
        nodeName: 'test-node',
        metricType: 'testType',
        timeWindow: '60m_set',
        aggregation: 'sum',
        redisOperation: 'event_set',
        graphOperation: 'area_node',
      };

      sinon.stub(context, 'selectedFacilityId').get(() => 'test-facility');
      sinon
        .stub(metricConfigStore, 'putMetricConfig')
        .rejects(new Error('Database connection failed'));

      await expect(
        service.updateOrCreateMetricConfig(input),
      ).to.be.rejectedWith('Database connection failed');
    });
  });
});
