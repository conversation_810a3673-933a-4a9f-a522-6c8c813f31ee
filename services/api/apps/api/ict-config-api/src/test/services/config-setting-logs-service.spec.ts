import {ConfigStore, ContextService, IctError} from 'ict-api-foundations';
import sinon from 'sinon';
import {expect} from 'chai';
import {SettingLog} from 'ict-api-schema';
import {ConfigSettingLogsService} from '../../services/config-setting-logs-service.ts';

const testLogs: SettingLog[] = [
  {
    id: 'testSetting1',
    description: 'this is a test setting',
    createdAt: new Date(),
    updatedAt: new Date(),
    settingId: 'testChangedSetting',
    changedBy: 'testUser',
    timestamp: new Date('2024-08-14T12:26:29.001Z'),
    source: 'user',
    newValue: 'false',
  },
  {
    id: 'testSetting2',
    description: 'this is a test setting',
    createdAt: new Date(),
    updatedAt: new Date(),
    settingId: 'testChangedSetting',
    changedBy: 'testUser',
    timestamp: new Date('2024-08-14T12:26:28.261Z'),
    source: 'user',
    newValue: '33',
  },
  {
    id: 'testSetting3',
    description: 'this is a test setting',
    createdAt: new Date(),
    updatedAt: new Date(),
    settingId: 'testChangedSetting',
    changedBy: 'testUser',
    timestamp: new Date('2024-08-14T12:26:27.557Z'),
    source: 'tenant',
    newValue: 'testStringValue',
  },
  {
    id: 'testSetting4',
    description: 'this is a test setting',
    createdAt: new Date(),
    updatedAt: new Date(),
    settingId: 'testChangedSetting',
    changedBy: 'testUser',
    timestamp: new Date('2024-08-14T12:26:26.933Z'),
    source: 'tenant',
    newValue: '{json data}',
  },
];

describe('config setting logs service', async () => {
  let service: ConfigSettingLogsService;
  let contextService: ContextService;
  let configStoreStub: sinon.SinonStubbedInstance<ConfigStore>;

  beforeEach(() => {
    contextService = new ContextService();
    configStoreStub = sinon.createStubInstance(ConfigStore);
    service = new ConfigSettingLogsService(configStoreStub, contextService);
  });

  describe('getSettingLogs', async () => {
    it('throws a no content error if there are no logs', async () => {
      configStoreStub.findSettingLogs.resolves([]);

      const outcome = await expect(
        service.getSettingLogs('testChangedSetting', 0)
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
      expect(configStoreStub.findSettingLogs.calledOnce).to.be.true;
    });

    it('returns formatted elements if there are logs', async () => {
      configStoreStub.findSettingLogs.resolves(testLogs);

      const outcome = await service.getSettingLogs('testChangedSetting', 0);

      expect(outcome.length).to.equal(testLogs.length);
      expect(configStoreStub.findSettingLogs.calledOnce).to.be.true;
    });
  });
});
