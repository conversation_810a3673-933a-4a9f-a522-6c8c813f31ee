import {
  ConfigStore,
  ContextService,
  IctError,
  WinstonLogger,
  AppConfigSetting,
  AppConfigSettingSource,
} from 'ict-api-foundations';
import * as sinon from 'sinon';
import * as chai from 'chai';
import chaiAsPromised from 'chai-as-promised';
import {Setting, TenantSetting, UserSetting} from 'ict-api-schema';
import {ConfigSettingsService} from '../../services/config-settings-service.ts';
import {
  DefaultConfigSeedingResult,
  UpdateSettingData,
} from '../../defs/app-configuration-setting.ts';

const expect = chai.expect;
chai.use(chaiAsPromised);

// Need to stub the config store and
describe('app configuration service', async () => {
  let contextService: ContextService;
  let service: ConfigSettingsService;
  let configStoreStub: sinon.SinonStubbedInstance<ConfigStore>;

  // test1Setting has a default, tenant, and user setting value
  const test1Setting = new Setting();
  test1Setting.id = 'test1Setting';
  test1Setting.dataType = 'string';
  test1Setting.minValue = null;
  test1Setting.maxValue = null;
  test1Setting.defaultValueNumber = null;
  test1Setting.defaultValueString = 'default value for test1Setting';
  test1Setting.name = 'test1Setting';
  test1Setting.description = 'test1Setting';
  test1Setting.createdAt = new Date();
  test1Setting.updatedAt = new Date();

  const test1UserSetting = new UserSetting();
  test1UserSetting.id = 'testUserSetting';
  test1UserSetting.userId = 'testUser';
  test1UserSetting.settingId = test1Setting.id;
  test1UserSetting.valueNumber = null;
  test1UserSetting.createdAt = new Date();
  test1UserSetting.updatedAt = new Date();
  test1UserSetting.setting = test1Setting;
  test1UserSetting.value = 'UserValue for test1Setting';
  test1UserSetting.description = 'testUserSetting';

  const test1TenantSetting = new TenantSetting();
  test1TenantSetting.id = 'testTenantSetting';
  test1TenantSetting.settingId = test1Setting.id;
  test1TenantSetting.valueNumber = null;
  test1TenantSetting.createdAt = new Date();
  test1TenantSetting.updatedAt = new Date();
  test1TenantSetting.setting = test1Setting;
  test1TenantSetting.value = 'TenantValue for test1Setting';
  test1TenantSetting.description = 'testTenantSetting';

  // test2Setting only has a default and tenant
  const test2Setting = new Setting();
  test2Setting.id = 'test2Setting';
  test2Setting.dataType = 'string';
  test2Setting.minValue = null;
  test2Setting.maxValue = null;
  test2Setting.defaultValueNumber = null;
  test2Setting.defaultValueString = 'default value for test2setting';
  test2Setting.name = 'test2Setting';
  test2Setting.description = 'test2Setting';
  test2Setting.createdAt = new Date();
  test2Setting.updatedAt = new Date();

  const test2TenantSetting = new TenantSetting();
  test2TenantSetting.id = 'testTenantSetting';
  test2TenantSetting.settingId = test2Setting.id;
  test2TenantSetting.valueNumber = null;
  test2TenantSetting.createdAt = new Date();
  test2TenantSetting.updatedAt = new Date();
  test2TenantSetting.setting = test2Setting;
  test2TenantSetting.value = 'TenantValue for test2Setting';
  test2TenantSetting.description = 'testTenantSetting';

  const changedBy = 'test0|12345';
  let relevantTest1Setting: AppConfigSetting;
  let relevantTest1UserSetting: AppConfigSetting;
  let relevantTest1TenantSetting: AppConfigSetting;
  let relevantTest2Setting: AppConfigSetting;
  let relevantTest2TenantSetting: AppConfigSetting;

  beforeEach(() => {
    contextService = new ContextService();
    const logger = new WinstonLogger(contextService);
    configStoreStub = sinon.createStubInstance(ConfigStore);

    service = new ConfigSettingsService(
      configStoreStub,
      logger,
      contextService,
    );

    relevantTest1Setting =
      service.convertSettingValueToRelevantSetting(test1Setting)[0];
    relevantTest1UserSetting =
      service.convertSettingValueToRelevantSetting(test1UserSetting)[0];
    relevantTest1TenantSetting =
      service.convertSettingValueToRelevantSetting(test1TenantSetting)[0];
    relevantTest2Setting =
      service.convertSettingValueToRelevantSetting(test2Setting)[0];
    relevantTest2TenantSetting =
      service.convertSettingValueToRelevantSetting(test2TenantSetting)[0];
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('setDefaultConfigSettings', async () => {
    const stubbedDefaultConfig: Setting[] = [test1Setting, test2Setting];
    let defaultConfigStub: sinon.SinonStub;

    beforeEach(() => {
      defaultConfigStub = sinon
        .stub(service, 'getDefaultConfig')
        .returns(stubbedDefaultConfig);
    });

    afterEach(() => {
      defaultConfigStub.restore();
    });

    it('successfully adds a setting if it does not exist and logs an existing setting', async () => {
      configStoreStub.findSettingByName.onCall(0).resolves(undefined);
      configStoreStub.findSettingByName.onCall(1).resolves(test2Setting);
      configStoreStub.saveSetting.resolves(test1Setting);
      const result = await service.setDefaultConfigSettings();

      const expected: DefaultConfigSeedingResult = {
        successful: [
          service.convertSettingValueToRelevantSetting(test1Setting)[0],
        ],
        unsuccessful: [],
        existing: [
          service.convertSettingValueToRelevantSetting(test2Setting)[0],
        ],
      };
      expect(result).to.deep.equal(expected);
      expect(configStoreStub.saveSetting.calledOnce).to.be.true;
      expect(configStoreStub.findSettingByName.callCount).to.equal(
        stubbedDefaultConfig.length,
      );
    });

    it('returns a list of settings that failed to insert when there is an unexpected issue', async () => {
      configStoreStub.findSettingByName.resolves(undefined);
      configStoreStub.saveSetting.throws('some error');
      const result = await service.setDefaultConfigSettings();

      const expected: DefaultConfigSeedingResult = {
        successful: [],
        unsuccessful:
          service.convertSettingValueToRelevantSetting(stubbedDefaultConfig),
        existing: [],
      };
      expect(result).to.deep.equal(expected);
      expect(configStoreStub.saveSetting.callCount).to.equal(
        stubbedDefaultConfig.length,
      );
      expect(configStoreStub.findSettingByName.callCount).to.equal(
        stubbedDefaultConfig.length,
      );
    });
  });

  describe('getSingleSetting', async () => {
    it('returns undefined if a setting doesnt exist', async () => {
      configStoreStub.findMostRelevantSettingForUser.resolves(undefined);
      const actualSetting = await service.getSingleSetting(
        'somesettingthatdoesntexist',
      );

      expect(actualSetting).to.be.undefined;
    });

    it('returns the requested setting value type if it is specified and there is more than one', async () => {
      configStoreStub.findSpecificSettingValue.resolves(
        relevantTest1TenantSetting,
      );

      const actualSetting = await service.getSingleSetting(
        test1Setting.id,
        undefined,
        AppConfigSettingSource.tenant,
      );

      expect(actualSetting).to.deep.equal(relevantTest1TenantSetting);
    });

    it('returns the most specific setting value if type isnt specified', async () => {
      configStoreStub.findMostRelevantSettingForUser.resolves(
        relevantTest1UserSetting,
      );

      const actualSetting = await service.getSingleSetting(test1Setting.id);

      expect(actualSetting).to.deep.equal(relevantTest1UserSetting);
    });
  });

  describe('getAllSettings', async () => {
    it('returns all settings when allStoredValues is set to true', async () => {
      configStoreStub.findAllUserSettings.resolves([test1UserSetting]);
      configStoreStub.findAllTenantSettings.resolves([
        test1TenantSetting,
        test2TenantSetting,
      ]);
      configStoreStub.findAllSettings.resolves([test1Setting, test2Setting]);

      const settingsList = await service.getAllSettings(undefined, true);

      // expected to return a list of ALL setting value types
      const expected = [
        relevantTest1Setting,
        relevantTest1TenantSetting,
        relevantTest1UserSetting,
        relevantTest2Setting,
        relevantTest2TenantSetting,
      ];

      expect(settingsList.length).to.equal(expected.length);
      expect(settingsList).to.have.same.deep.members(expected);
    });

    it('returns all settings with only the most specific value when allStoredValues is set to false', async () => {
      configStoreStub.findAllUserSettings.resolves([test1UserSetting]);
      configStoreStub.findAllTenantSettings.resolves([
        test1TenantSetting,
        test2TenantSetting,
      ]);
      configStoreStub.findAllSettings.resolves([test1Setting, test2Setting]);

      const settingsList = await service.getAllSettings(undefined, false);

      // expected to return a list of ALL setting value types
      const expected = [relevantTest1UserSetting, relevantTest2TenantSetting];

      expect(settingsList.length).to.equal(expected.length);
      expect(settingsList).to.have.same.deep.members(expected);
    });

    it('only returns settings with a specific group name when specified', async () => {
      configStoreStub.findAllUserSettings.resolves([test1UserSetting]);
      configStoreStub.findAllTenantSettings.resolves([
        test1TenantSetting,
        test2TenantSetting,
      ]);
      configStoreStub.findAllSettings.resolves([test1Setting, test2Setting]);

      const groupName = 'test-group';
      await service.getAllSettings(groupName, false);

      expect(configStoreStub.findAllUserSettings.calledWith(groupName));
      expect(configStoreStub.findAllTenantSettings.calledWith(groupName));
      expect(configStoreStub.findAllSettings.calledWith(groupName));
    });
  });

  describe('updateOrCreateSetting', async () => {
    it('throws an error if a name or setting ID isnt passed', async () => {
      const badSettingUpdates = {
        dataType: 'string',
        value: 'setting value',
        levelToUpdate: AppConfigSettingSource.default,
      };

      const outcome = await expect(
        service.updateOrCreateSetting(badSettingUpdates as UpdateSettingData),
      ).to.be.rejectedWith(IctError);
      expect(outcome.statusCode).to.equal(IctError.badRequest().statusCode);
      expect(configStoreStub.findSetting.called).to.be.false;
    });

    it('throws an error when the given setting ID doesnt exist', async () => {
      const settingUpdates: UpdateSettingData = {
        id: 'someIDthatdoesntexist',
        name: 'new-setting-name',
        dataType: 'string',
        value: 'setting value',
        levelToUpdate: AppConfigSettingSource.default,
      };

      configStoreStub.findSettingById.resolves(undefined);
      configStoreStub.findSettingByName.resolves(undefined);

      // expect(newSetting).to.deep.equal(
      //   service.convertSettingValueToRelevantSetting(newlyCreatedSetting)[0]
      // );
      const outcome = await expect(
        service.updateOrCreateSetting(settingUpdates as UpdateSettingData),
      ).to.be.rejectedWith(IctError);
      expect(outcome.statusCode).to.equal(IctError.notFound().statusCode);
      expect(configStoreStub.findSettingById.called).to.be.true;
    });

    it('fails to create a new setting when the given setting ID doesnt exist, but the request doesnt have enough data to create a new one', async () => {
      const settingUpdates = {
        dataType: 'string',
        value: 'setting value',
        levelToUpdate: AppConfigSettingSource.default,
      };

      configStoreStub.findSettingById.resolves(undefined);
      configStoreStub.findSettingByName.resolves(undefined);

      const outcome = await expect(
        service.updateOrCreateSetting(settingUpdates as UpdateSettingData),
      ).to.be.rejectedWith(IctError);
      expect(outcome.statusCode).to.equal(IctError.badRequest().statusCode);
      expect(configStoreStub.findSetting.called);
    });

    it('creates a new StoredSetting if the setting ID exists, but the requested levelToUpdate doesnt exist yet', async () => {
      const settingUpdates: UpdateSettingData = {
        id: test2Setting.id,
        name: test2Setting.name,
        dataType: test2Setting.dataType,
        value: 'a user setting',
        levelToUpdate: AppConfigSettingSource.user,
      };

      const newlyCreatedUserSetting = service.createNewStoredSetting(
        test2Setting,
        settingUpdates.value,
        AppConfigSettingSource.user,
        changedBy,
      ) as UserSetting;

      configStoreStub.findSettingById.resolves(test2Setting);
      configStoreStub.findUserSetting.resolves(undefined);
      configStoreStub.saveUserSetting.resolves(newlyCreatedUserSetting);

      const newSetting = await service.updateOrCreateSetting(settingUpdates);

      expect(newSetting).to.deep.equal({
        setting: service.convertSettingValueToRelevantSetting(
          newlyCreatedUserSetting,
        )[0],
        created: false,
      });
      expect(configStoreStub.findSettingById.called).to.be.true;
      expect(configStoreStub.findUserSetting.called).to.be.true;
      expect(configStoreStub.saveUserSetting.called).to.be.true;
      expect(configStoreStub.saveSetting.called).to.be.false;
    });

    it('updates an existing setting when the setting is found in the database', async () => {
      const settingUpdates: UpdateSettingData = {
        id: test1Setting.id,
        name: test1Setting.name,
        dataType: test1Setting.dataType,
        value: 'a new default setting value for test1Setting',
        levelToUpdate: AppConfigSettingSource.default,
      };

      test1Setting.defaultValue = settingUpdates.value;

      configStoreStub.findSettingById.resolves(test1Setting);
      configStoreStub.saveSetting.resolves(test1Setting);

      const updatedSetting =
        await service.updateOrCreateSetting(settingUpdates);

      expect(updatedSetting).to.deep.equal({
        setting: service.convertSettingValueToRelevantSetting(test1Setting)[0],
        created: false,
      });
      expect(configStoreStub.findSettingById.called).to.be.true;
      expect(configStoreStub.saveSetting.called).to.be.true;
    });
  });

  describe('createNewStoredSetting', async () => {
    it('returns a TenantSetting if storedSettingType is set to tenant', async () => {
      const tenantSetting = service.createNewStoredSetting(
        test2Setting,
        'val',
        AppConfigSettingSource.tenant,
        changedBy,
      );

      expect(tenantSetting instanceof TenantSetting).to.be.true;
    });
    it('returns a UserSetting if storedSettingType is set to user', async () => {
      const userSetting = service.createNewStoredSetting(
        test2Setting,
        'val',
        AppConfigSettingSource.user,
        changedBy,
      );

      expect(userSetting instanceof UserSetting).to.be.true;
    });
  });

  describe('deleteSetting', async () => {
    it('delete count is 1 when user setting only is deleted', async () => {
      configStoreStub.deleteUserSetting.resolves(1);

      const settingId = 'someid';
      const result = await service.deleteSetting(
        settingId,
        AppConfigSettingSource.user,
      );

      expect(configStoreStub.deleteUserSetting.calledWith(settingId)).to.be
        .true;
      expect(configStoreStub.deleteSetting.called).to.be.false;
      expect(configStoreStub.deleteTenantSetting.called).to.be.false;
      expect(result.recordsDeleted).to.equal(1);
    });

    it('delete count is correct when all setting types deleted', async () => {
      configStoreStub.deleteUserSetting.resolves(2);
      configStoreStub.deleteTenantSetting.resolves(1);
      configStoreStub.deleteFacilitySetting.resolves(3);
      configStoreStub.deleteSetting.resolves(1);

      const settingId = 'someid';
      const result = await service.deleteSetting(
        settingId,
        AppConfigSettingSource.default,
      );

      expect(configStoreStub.deleteUserSetting.calledWith(settingId)).to.be
        .true;
      expect(configStoreStub.deleteSetting.calledWith(settingId)).to.be.true;
      expect(configStoreStub.deleteTenantSetting.calledWith(settingId)).to.be
        .true;
      expect(configStoreStub.deleteFacilitySetting.calledWith(settingId)).to.be
        .true;
      expect(result.recordsDeleted).to.equal(7);
    });

    it('attempts to delete all record types when no levelToDelete is passed', async () => {
      configStoreStub.deleteUserSetting.resolves(1);
      configStoreStub.deleteTenantSetting.resolves(1);
      configStoreStub.deleteFacilitySetting.resolves(1);
      configStoreStub.deleteSetting.resolves(1);

      const settingId = 'someid';
      const result = await service.deleteSetting(settingId);

      expect(configStoreStub.deleteUserSetting.calledWith(settingId)).to.be
        .true;
      expect(configStoreStub.deleteSetting.calledWith(settingId)).to.be.true;
      expect(configStoreStub.deleteTenantSetting.calledWith(settingId)).to.be
        .true;
      expect(configStoreStub.deleteFacilitySetting.calledWith(settingId)).to.be
        .true;
      expect(result.recordsDeleted).to.equal(4);
    });

    it('throws an error if a facility ID is not selected', async () => {
      const settingId = 'someid';

      // Ensure no facility ID is selected in the context
      contextService.selectedFacilityId = null;

      // Attempt to delete a facility setting without a facility ID selected
      const outcome = await expect(
        service.deleteSetting(settingId, AppConfigSettingSource.facility),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.badRequest().statusCode);
      expect(outcome.message).to.equal(
        'A facility ID must be selected to delete a facility setting.',
      );

      // Verify that no actual delete operations were attempted
      expect(configStoreStub.deleteFacilitySetting.called).to.be.false;
    });
  });
});
