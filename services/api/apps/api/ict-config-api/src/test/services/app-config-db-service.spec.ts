import sinon from 'sinon';
import {expect} from 'chai';
import {ConfigStore, ContextService} from 'ict-api-foundations';
import {AppConfigDbService} from '../../services/app-config-db-service.ts';
import {AppConfigDbStore} from '../../stores/app-config-db-store.ts';
import {CuratedTableColumnValueRenderType} from '../../defs/curated-table-column.ts';

describe('AppConfigDbService', () => {
  describe('getCuratedDataset', async () => {
    it('should return a list of raw results', async () => {
      const context = new ContextService();
      const configStore = new ConfigStore(context);
      const appConfigDbStore = new AppConfigDbStore(context, configStore);
      const appConfigDbService = new AppConfigDbService(appConfigDbStore);
      const testResults = [{test: 'test', test2: 42}];
      const testTableName = 'test_table';
      const expected = testResults;

      sinon
        .stub(appConfigDbService, 'getCuratedTableNames')
        .resolves([testTableName]);
      sinon.stub(appConfigDbStore, 'getCuratedDataset').resolves(testResults);
      const result = await appConfigDbService.getCuratedDataset(testTableName);

      expect(result).to.deep.equal(expected);
    });
  });

  describe('getCuratedDatasetRows', async () => {
    it('should return a list of CuratedTableRows', async () => {
      const context = new ContextService();
      const configStore = new ConfigStore(context);
      const appConfigDbStore = new AppConfigDbStore(context, configStore);
      const appConfigDbService = new AppConfigDbService(appConfigDbStore);
      const testTableName = 'test_table';
      const curatedRows = [
        {
          columns: [
            {
              name: 'test',
              renderType: CuratedTableColumnValueRenderType.String,
              value: 'test',
            },
            {
              name: 'test2',
              renderType: CuratedTableColumnValueRenderType.Number,
              value: 42,
            },
          ],
        },
      ];

      sinon
        .stub(appConfigDbService, 'getCuratedTableNames')
        .resolves([testTableName]);
      sinon
        .stub(appConfigDbStore, 'getCuratedDatasetRows')
        .resolves(curatedRows);
      const result =
        await appConfigDbService.getCuratedDatasetRows(testTableName);

      expect(result).to.deep.equal(curatedRows);
    });
  });

  describe('getCuratedTableNames', () => {
    it('should return a list of table names', async () => {
      const context = new ContextService();
      const configStore = new ConfigStore(context);
      const appConfigDbStore = new AppConfigDbStore(context, configStore);
      const appConfigDbService = new AppConfigDbService(appConfigDbStore);
      const expected = ['dim_table1', 'fct_table2', 'fct_table3'];
      sinon
        .stub(appConfigDbStore, 'getCuratedTableNames')
        .resolves([
          {table_id: 'dim_table1'},
          {table_id: 'fct_table2'},
          {table_id: 'fct_table3'},
        ]);

      const result = await appConfigDbService.getCuratedTableNames();

      expect(result).to.deep.equal(expected);
    });
  });
});
