import {
  Container,
  ProtectedRouteMiddleware,
  DatabaseTypes,
  PostgresDatabaseOptions,
  IctError,
  HttpStatusCodes,
} from 'ict-api-foundations';
import {EntityTypes} from 'ict-api-schema';
import {
  Controller,
  Example,
  Get,
  Put,
  Body,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Response,
  Middlewares,
} from 'tsoa';
import {MetricConfigService} from '../services/metric-config-service.ts';
import {MetricConfigSummary} from '../defs/metric-config-summary.ts';
import type {MetricConfigDetail} from '../defs/metric-config-detail.ts';
import {CustomMetricConfigurationEntity} from 'ict-api-schema';

@Route('config/process-flow')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [
      {
        type: DatabaseTypes.Postgres,
        options: {
          entityType: EntityTypes.Config,
        } as PostgresDatabaseOptions,
      },
      {
        type: DatabaseTypes.Postgres,
        options: {
          entityType: EntityTypes.ProcessFlow,
        } as PostgresDatabaseOptions,
      },
    ],
  }),
])
export class MetricConfigController extends Controller {
  static exampleResponse: MetricConfigSummary[] = [
    {
      id: '1',
      metricName: 'test-metric',
      configType: 'node',
      nodeName: 'test-node',
      factType: 'test-fact',
      enabled: true,
      active: true,
      isCustom: false,
      facilityId: 'default',
    },
  ];

  /**
   * Get metric configurations based on filter criteria.
   * Without any query params, returns all metric configs that are not soft_deleted.
   * @param {string} metricName Filter by metric name
   * @param {string} metricId Filter by metric ID
   * @param {string} factType Filter by fact type
   * @param {string} nodeName Filter by node name
   * @param {boolean} active Filter by active status
   * @param {boolean} enabled Filter by enabled status
   * @param {string} configType Filter by config type
   */
  @Example<MetricConfigSummary[]>(MetricConfigController.exampleResponse)
  @SuccessResponse(
    HttpStatusCodes.OK,
    'Returns a list of metric configurations',
  )
  @Response(
    HttpStatusCodes.NOT_FOUND,
    'No metric configurations found matching the criteria',
  )
  @Response(HttpStatusCodes.INTERNAL_SERVER_ERROR, 'Internal server error')
  @Get('metric-configs')
  @OperationId('GetMetricConfigs')
  @Tags('config')
  public async getMetricConfigs(
    @Query() metricName?: string,
    @Query() metricId?: string,
    @Query() factType?: string,
    @Query() nodeName?: string,
    @Query() active?: boolean,
    @Query() enabled?: boolean,
    @Query() configType?: string,
  ): Promise<MetricConfigSummary[]> {
    // Get the metric config service
    const service: MetricConfigService = Container.get(MetricConfigService);

    const filters = {
      metricName,
      metricId,
      factType,
      nodeName,
      active,
      enabled,
      configType,
    };

    const metricConfigs = await service.getMetricConfigSummaries(filters);

    if (!metricConfigs || metricConfigs.length === 0) {
      const filterDetails = Object.entries(filters)
        .filter(([, value]) => value !== undefined)
        .map(([key, value]) => `${key}: ${value}`)
        .join(', ');

      throw IctError.notFound(
        `No metric configurations found with filters: ${filterDetails}`,
      );
    }

    return metricConfigs;
  }

  static examplePutResponse: CustomMetricConfigurationEntity = {
    id: '1',
    metricConfigName: 'test-metric',
    configType: 'node',
    views: ['facility', 'multishuttle'],
    matchConditions: {eventType: 'test-event'},
    factType: 'test-fact',
    sourceSystem: 'diq',
    displayName: 'Test Metric',
    description: 'Example metric configuration for testing',
    enabled: true,
    active: true,
    facilityId: 'facility-1', // Set by facility middleware from ict-facility-id header
    nodeName: 'test-node',
    metricType: 'stockTime',
    timeWindow: '60m_set',
    aggregation: 'sum',
    redisOperation: 'event_set',
    graphOperation: 'area_node',
    metricUnits: '/hr',
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
  };

  /**
   * Updates or creates a metric configuration.
   * If a configuration with the same metric name and facility already exists, it will be updated.
   * Otherwise, a new configuration will be created.
   * The facility ID is determined from the ict-facility-id header.
   * @param {MetricConfig} requestBody The metric configuration data
   */
  @Example<Partial<CustomMetricConfigurationEntity>>(
    MetricConfigController.examplePutResponse,
  )
  @Response(HttpStatusCodes.OK, 'Metric configuration updated successfully')
  @Response(
    HttpStatusCodes.CREATED,
    'Metric configuration created successfully',
  )
  @Response(HttpStatusCodes.BAD_REQUEST, 'Invalid metric configuration data')
  @Put('metric-config')
  @OperationId('PutConfigProcessFlowMetricConfig')
  @Tags('config')
  public async putMetricConfig(
    @Body() requestBody: MetricConfigDetail,
  ): Promise<CustomMetricConfigurationEntity> {
    // Get the metric config service
    const service: MetricConfigService = Container.get(MetricConfigService);

    const result = await service.updateOrCreateMetricConfig(requestBody);

    if (result.isNew) {
      this.setStatus(HttpStatusCodes.CREATED);
    } else {
      this.setStatus(HttpStatusCodes.OK);
    }

    return result.config;
  }
}
