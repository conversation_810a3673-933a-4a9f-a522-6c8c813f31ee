import {Container, WinstonLogger, AppConfig} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {AppConfigService} from '../services/app-config-service.ts';

@Route('config')
export class AppConfigController extends Controller {
  static exampleResponse: AppConfig = {
    api: {
      baseUrl: 'https://run-ict-d-api.ict.dematic.dev/',
    },
    site: {
      timezone: 'America/New_York',
    },
  };

  /**
   * @param {string} config
   * @example config "my-special-config"
   */
  @Example<AppConfig>(AppConfigController.exampleResponse)
  @SuccessResponse('200')
  @Get('/app-config')
  @OperationId('GetConfigAppConfig')
  @Tags('config')
  public async getAppConfig(@Query() config?: string): Promise<AppConfig> {
    const logger = Container.get(WinstonLogger);

    // Get the enterprise search service
    const service: AppConfigService = Container.get(AppConfigService);

    const configFile = config ? `${config}.json` : 'app-config.json';

    const appConfig: string = await service.getAppConfigData(config);
    logger.info('Loaded config file', {configFile});
    return JSON.parse(appConfig) as AppConfig;
  }
}
