import {
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ProtectedRouteMiddleware,
  SecurityRoles,
  TsoaSecurityAllRoles,
} from 'ict-api-foundations';
import {
  Controller,
  Get,
  Route,
  SuccessResponse,
  Middlewares,
  Example,
  OperationId,
  Tags,
  Security,
} from 'tsoa';
import {AppConfigDbService} from '../services/app-config-db-service.ts';

@Route('config')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
])
export class AppConfigCuratedTableListController extends Controller {
  static readonly exampleResponse: string[] = [
    'dim_table1',
    'fct_table2',
    'fct_table3',
  ];
  /**
   * Endpoint for getting a list of tables available in the curated dataset.
   */
  @Example<string[]>(AppConfigCuratedTableListController.exampleResponse)
  @SuccessResponse('200')
  @Security(TsoaSecurityAllRoles, [SecurityRoles.CT_ENGINEERS])
  @Get('/curated-tables/list')
  @OperationId('GetConfigCuratedTablesList')
  @Tags('config')
  public async getConfigCuratedTablesList(): Promise<string[]> {
    const service = Container.get(AppConfigDbService);
    const curatedTableList = await service.getCuratedTableNames();
    return curatedTableList ?? [];
  }
}
