import {
  Container,
  DatabaseTypes,
  HttpStatusCodes,
  IctError,
  PostgresDatabaseOptions,
  ProtectedRouteMiddleware,
  AppConfigSetting,
  AppConfigSettingSource,
  AppConfigSettingArrayOrAppConfigSetting,
  TsoaSecurityAllRoles,
  SecurityRoles,
  TsoaSecurityAnyRoles,
} from 'ict-api-foundations';
import {EntityTypes} from 'ict-api-schema';
import {
  Body,
  Controller,
  Delete,
  Example,
  Get,
  Middlewares,
  Path,
  Post,
  Put,
  Query,
  Route,
  SuccessResponse,
  Response,
  OperationId,
  Tags,
  Security,
} from 'tsoa';

import {ConfigSettingsService} from '../services/config-settings-service.ts';
import {DefaultConfigSeedingResult} from '../defs/app-configuration-setting.ts';
import type {
  DeletedRecordsResult,
  UpdateSettingData,
} from '../defs/app-configuration-setting.ts';

const postgresDbOptions: PostgresDatabaseOptions = {
  entityType: EntityTypes.Config,
};

const dbMiddleware = {
  databases: [
    {
      type: DatabaseTypes.Postgres,
      options: postgresDbOptions,
    },
  ],
};
@Middlewares(
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, dbMiddleware),
)
@Route('config')
export class ConfigSettingsController extends Controller {
  static readonly exampleRelevantSetting: AppConfigSetting = {
    id: '0e0f42b-1b58-4a5c-a785-5e6af',
    name: 'available-packages',
    group: 'app-configuration',
    dataType: 'json',
    value: {
      test: true,
    },
    source: AppConfigSettingSource.tenant,
  };

  static readonly exampleGetSettingResponse: AppConfigSetting =
    ConfigSettingsController.exampleRelevantSetting;

  static readonly exampleGetAllSettingsResponse: AppConfigSetting[] = [
    ConfigSettingsController.exampleRelevantSetting,
  ];
  /**
   * Retrieves all settings and their values or a single setting based on id and/or name
   * @param {string} settingId Id of the setting to retrieve. Optional, if this and settingName are both omitted, all settings will be returned.
   * @param {string} settingName Name of the setting to retrieve. Optional, if this and settingName are both omitted, all settings will be returned.
   * If both setting id and name are provided, only settings matching BOTH parameters will be returned.
   * @param {AppConfigSettingSource} settingType Level of setting to be returned (user, tenant, default). Optional, defaults to all levels.
   * Only used when a setting id and/or name is provided.
   * @param {boolean} allStoredValues If true, will fetch the default, tenant, and user setting values for a setting.
   * If false, only the most specific stored value is returned; for example, if there is a tenant level value and a
   * user level value applicable to the given user, then only the user level value is returned. Optional, defaults to false.
   * @param {string} group filter on settings that are a part of the specified group name. Only used when a setting id and/or name is provided.
   * @returns {Promise<AppConfigSettingArrayOrAppConfigSetting>} either an array of app config settings or a single app config setting
   * @throws IctError
   */
  @Example<AppConfigSetting[]>(
    ConfigSettingsController.exampleGetAllSettingsResponse,
  )
  @SuccessResponse('200')
  @Get('/settings')
  @OperationId('GetConfigSettings')
  @Tags('config')
  public async getConfigurationSettings(
    @Query() settingId?: string,
    @Query() settingName?: string,
    @Query() settingType?: AppConfigSettingSource,
    @Query() allStoredValues?: boolean,
    @Query() group?: string,
  ): Promise<AppConfigSettingArrayOrAppConfigSetting> {
    const service = Container.get(ConfigSettingsService);
    // get only a single setting if params are given
    if (settingId || settingName) {
      const setting = await service.getSingleSetting(
        settingId,
        settingName,
        settingType,
        allStoredValues,
      );

      if (!setting)
        throw IctError.notFound(
          'A setting matching the given parameters was not found.',
        );

      return setting;
    }

    // otherwise, get all settings
    return await service.getAllSettings(group, allStoredValues);
  }

  static readonly exampleSetDefaultConfigResponse: DefaultConfigSeedingResult =
    {
      successful: [ConfigSettingsController.exampleRelevantSetting],
      unsuccessful: [ConfigSettingsController.exampleRelevantSetting],
      existing: [ConfigSettingsController.exampleRelevantSetting],
    };

  /**
   * Inserts the default settings to seed a new tenant's database
   * @returns {Promise<DefaultConfigSeedingResult>} contract
   */
  @Example<DefaultConfigSeedingResult>(
    ConfigSettingsController.exampleSetDefaultConfigResponse,
  )
  @SuccessResponse('200')
  @Post('/insert-default-config')
  @OperationId('PostConfigDefaultConfig')
  @Tags('config')
  public async setDefaultConfigurationSettings(): Promise<DefaultConfigSeedingResult> {
    const service = Container.get(ConfigSettingsService);
    return await service.setDefaultConfigSettings();
  }

  static readonly exampleUpdateSettingResponse: AppConfigSetting =
    ConfigSettingsController.exampleRelevantSetting;
  /**
   * Updates the specified setting with the properties in settingInfo.  Creates a new setting if
   * the setting isn't found, and the required information is provided.
   * @param {UpdateSettingData} settingInfo Identifying information about the requested Setting resource, as well as requested updates.<br><br>
   * <b>id</b> - the setting Id.  ID is required in order to update an existing setting, but not to add a new one<br>
   * <b>name</b> - the name of the setting.  Required.<br>
   * <b>dataType</b> - one of: 'json', 'string', 'boolean', or 'number'<br>
   * <b>group</b> - the Setting group.  This value is updated if it is different.<br>
   * <b>value</b> - the value to update the setting with. Can be any value<br>
   * <b>levelToUpdate</b> - one of: 'default', 'tenant', 'site', or 'user'. This determines the appropriate value to change.<br>
   * <b>description</b> - optional string description of the setting.<br>
   * @returns {Promise<AppConfigSetting>} app config setting after results have been applied
   * @throws IctError
   */
  @Example<AppConfigSetting>(
    ConfigSettingsController.exampleUpdateSettingResponse,
  )
  @SuccessResponse(HttpStatusCodes.OK)
  @Security(TsoaSecurityAllRoles, [SecurityRoles.CT_CONFIGURATORS])
  @Response(HttpStatusCodes.CREATED, 'New setting created')
  @Put('/settings')
  @OperationId('PutConfigSettings')
  @Tags('config')
  public async updateSetting(
    @Body() settingInfo: UpdateSettingData,
  ): Promise<AppConfigSetting> {
    const service = Container.get(ConfigSettingsService);
    const updatedSetting = await service.updateOrCreateSetting(settingInfo);

    this.setStatus(
      updatedSetting.created ? HttpStatusCodes.CREATED : HttpStatusCodes.OK,
    );

    return updatedSetting.setting;
  }

  static readonly exampleDeleteSettingResponse: DeletedRecordsResult = {
    recordsDeleted: 3,
    message: 'Deleted tenant, user, and default setting.',
  };

  /**
   *
   * @param settingId setting ID to delete
   * @param levelToDelete specific record to delete.  If blank, all stored setting values associated with the setting ID are deleted
   * @returns {Promise<DeletedRecordsResult>} the number of rows deleted
   */
  @Example<DeletedRecordsResult>(
    ConfigSettingsController.exampleDeleteSettingResponse,
  )
  @SuccessResponse('200')
  @Delete('/settings/{settingId}')
  @Security(TsoaSecurityAllRoles, [SecurityRoles.CT_CONFIGURATORS])
  @OperationId('DeleteConfigSetting')
  @Tags('config')
  public async deleteSetting(
    @Path() settingId: string,
    @Query() levelToDelete?: AppConfigSettingSource,
  ): Promise<DeletedRecordsResult> {
    const service = Container.get(ConfigSettingsService);
    return await service.deleteSetting(settingId, levelToDelete);
  }

  static readonly exampleSettingSchemaResponse: unknown = {
    $schema: 'http://json-schema.org/draft-07/schema#',
    title: 'Generated schema for Root',
    type: 'object',
    properties: {
      suffixes: {
        type: 'array',
        items: {
          type: 'string',
        },
      },
    },
    required: ['suffixes'],
  };

  @Example<unknown>(ConfigSettingsController.exampleSettingSchemaResponse)
  @SuccessResponse('200')
  @Get('/settings/schema')
  @Security(TsoaSecurityAnyRoles, [
    SecurityRoles.CT_CONFIGURATORS,
    SecurityRoles.CT_ENGINEERS,
  ])
  @OperationId('getSettingSchema')
  @Tags('config')
  public async getSettingSchema(
    @Query() settingId?: string,
    @Query() settingName?: string,
  ): Promise<unknown> {
    const service = Container.get(ConfigSettingsService);

    return await service.getSettingSchema(settingId, settingName);
  }
}
