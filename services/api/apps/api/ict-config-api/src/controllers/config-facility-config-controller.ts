import {
  AppConfigSettingSource,
  Container,
  ContextService,
  DatabaseTypes,
  FacilityConfigSettings,
  IctError,
  PostgresDatabaseOptions,
  ProtectedRouteMiddleware,
  SecurityRoles,
  WinstonLogger,
} from 'ict-api-foundations';
import {EntityTypes} from 'ict-api-schema';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  OperationId,
  Route,
  SuccessResponse,
  Tags,
} from 'tsoa';

import {FacilityMap} from '@ict/sdk-foundations/types';
import {ConfigSettingsService} from '../services/config-settings-service.ts';

const postgresDbOptions: PostgresDatabaseOptions = {
  entityType: EntityTypes.Config,
};

const dbMiddleware = {
  databases: [
    {
      type: DatabaseTypes.Postgres,
      options: postgresDbOptions,
    },
  ],
};

@Middlewares(
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, dbMiddleware),
)
@Route('config')
export class ConfigFacilityConfigController extends Controller {
  static readonly exampleRelevantSetting: FacilityConfigSettings = [
    {
      id: '0e0f42b-1b58-4a5c-a785-5e6af',
      name: 'available-packages',
      group: 'app-configuration',
      dataType: 'json',
      value: {
        test: true,
      },
      source: AppConfigSettingSource.tenant,
    },
  ];

  static readonly exampleGetSettingResponse: FacilityConfigSettings =
    ConfigFacilityConfigController.exampleRelevantSetting;

  static readonly exampleGetAllSettingsResponse: FacilityConfigSettings =
    ConfigFacilityConfigController.exampleRelevantSetting;

  @Example<FacilityConfigSettings>(
    ConfigFacilityConfigController.exampleGetAllSettingsResponse,
  )
  @SuccessResponse('200')
  @Get('/facility-config')
  @OperationId('GetConfigFacilityConfig')
  @Tags('config')
  public async getConfigurationSettings(): Promise<FacilityConfigSettings> {
    const service = Container.get(ConfigSettingsService);
    const context = Container.get(ContextService);
    const logger = Container.get(WinstonLogger);
    const userRoles = context.userRoles || [];
    const isInternalUser =
      userRoles.includes(SecurityRoles.CT_CONFIGURATORS) ||
      userRoles.includes(SecurityRoles.CT_ENGINEERS);

    // Determine the facility ID to use for configuration retrieval
    if (!context.selectedFacilityId) {
      logger.info('no selected facility map was passed in.');

      try {
        context.selectedFacilityId = await this.determineFacilityId(
          service,
          context,
        );
      } catch (error) {
        // If we can't determine a facility ID and user is internal, return all settings
        if (isInternalUser) {
          return service.getAllSettings();
        }
        throw error;
      }
    }

    const allSettings = await service.getAllSettings();
    return allSettings;
  }

  private async determineFacilityId(
    service: ConfigSettingsService,
    context: ContextService,
  ): Promise<string> {
    // First, try to get user's selected facility ID
    let facilityId = await this.getUserSelectedFacilityId(service);

    if (facilityId) {
      return facilityId;
    }

    // If no user setting, try to get default facility from tenant maps
    facilityId = this.getDefaultFacilityId(context);

    // Get the available facilities for the user based on this organization's list of facilities and the user's facility roles
    const availableFacilities = (context.facilityMaps as FacilityMap[]).filter(
      facility => context.userRoles?.some(role => role === facility.id),
    );

    if (
      facilityId &&
      availableFacilities.some(facility => facility.id === facilityId)
    ) {
      // only return the default facility if it's available to the user
      return facilityId;
    }

    if (availableFacilities.length > 0) {
      // return the first available facility
      return availableFacilities[0].id;
    }

    // No facility could be determined or available to the user
    throw IctError.notFound('No Facility could be found!');
  }

  private getDefaultFacilityId(context: ContextService): string | undefined {
    const defaultFacility: FacilityMap | undefined = context.facilityMaps?.find(
      f => f.default === true,
    );

    return defaultFacility?.id;
  }

  private async getUserSelectedFacilityId(
    service: ConfigSettingsService,
  ): Promise<string | undefined> {
    const selectedFacilityIdSetting = await service.getSingleSetting(
      undefined,
      'selected-facility-id',
      AppConfigSettingSource.user,
      false,
    );
    let selectedFacilityId: string | undefined;
    if (selectedFacilityIdSetting) {
      selectedFacilityId = Array.isArray(selectedFacilityIdSetting)
        ? (selectedFacilityIdSetting[0].value as string)
        : (selectedFacilityIdSetting.value as string);
    }
    return selectedFacilityId;
  }
}
