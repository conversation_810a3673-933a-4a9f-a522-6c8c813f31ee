import {
  AppConfigSettingLogsData,
  DatabaseTypes,
  PostgresDatabaseOptions,
  ProtectedRouteMiddleware,
  Container,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  OperationId,
  Query,
  Route,
  SuccessResponse,
  Tags,
} from 'tsoa';
import {EntityTypes} from 'ict-api-schema';
import {ConfigSettingLogsService} from '../services/config-setting-logs-service.ts';

const postgresDbOptions: PostgresDatabaseOptions = {
  entityType: EntityTypes.Config,
};

const dbMiddleware = {
  databases: [
    {
      type: DatabaseTypes.Postgres,
      options: postgresDbOptions,
    },
  ],
};

@Middlewares(
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, dbMiddleware)
)
@Route('config')
export class ConfigSettingLogsController extends Controller {
  /**
   * Retrieves logs for a setting
   * @param setting_id Id of the setting being searched for
   * @param limit maximum number of logs that may be returned
   * (if 0, all logs will be returned)
   * @returns {Promise<AppConfigSettingLogsData>} returns array of AppConfigSettingLogs under data field
   * @throws IctError
   */
  @Example<AppConfigSettingLogsData>({
    data: [
      {
        changedBy: 'testUser',
        timestamp: new Date('2024-08-14T12:26:29.001Z'),
        source: 'user',
        newValue: 'false',
      },
      {
        changedBy: 'testUser',
        timestamp: new Date('2024-08-14T12:26:28.261Z'),
        source: 'user',
        newValue: '33',
      },
      {
        changedBy: 'testUser',
        timestamp: new Date('2024-08-14T12:26:27.557Z'),
        source: 'tenant',
        newValue: 'testStringValue',
      },
      {
        changedBy: 'testUser',
        timestamp: new Date('2024-08-14T12:26:26.933Z'),
        source: 'tenant',
        newValue: '{json data}',
      },
    ],
  })
  @SuccessResponse('200')
  @Get('/setting-logs')
  @OperationId('GetConfigSettingLogs')
  @Tags('config')
  public async getConfigurationSettingLogs(
    @Query() setting_id: string,
    @Query() limit: number = 0
  ): Promise<AppConfigSettingLogsData> {
    const service = Container.get(ConfigSettingLogsService);
    const settingLogs = await service.getSettingLogs(setting_id, limit);
    return {data: settingLogs};
  }
}
