import {BaseHealthCheck} from 'ict-api-foundations';
import {Get, Route, OperationId, Tags} from 'tsoa';

@Route('/config/healthcheck')
export class HealthCheckController {
  @Get()
  @OperationId('GetConfigBasicHealthCheck')
  @Tags('config')
  public getConfigBasicHealthCheck() {
    return BaseHealthCheck.getBasicHealthCheck();
  }

  @Get('/full')
  @OperationId('GetConfigFullHealthCheck')
  @Tags('config')
  public getConfigFullHealthCheck() {
    return BaseHealthCheck.getDeepHealthCheck(['auth0', 'bigQuery', 'redis']);
  }
}
