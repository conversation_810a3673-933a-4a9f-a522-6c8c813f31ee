import {
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ProtectedRouteMiddleware,
  SecurityRoles,
  TsoaSecurityAllRoles,
  WinstonLogger,
} from 'ict-api-foundations';
import {
  Controller,
  Get,
  Query,
  Route,
  SuccessResponse,
  Middlewares,
  Security,
  OperationId,
  Tags,
} from 'tsoa';
import {AppConfigDbService} from '../services/app-config-db-service.ts';
import {CuratedTableRow} from '../defs/curated-table-row.ts';

@Route('config/v2')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
])
export class AppConfigDbControllerV2 extends Controller {
  /**
   * Handle a request for the top 100 rows of a table.
   * @param {string} table Name of the table to get data for.
   */
  @SuccessResponse('200')
  @Security(TsoaSecurityAllRoles, [SecurityRoles.CT_ENGINEERS])
  @Get('/curated-data')
  @OperationId('GetConfigCuratedDataV2')
  @Tags('config')
  public async getConfigCuratedDataset(
    @Query() table: string
  ): Promise<CuratedTableRow[]> {
    const logger = Container.get(WinstonLogger);
    const service = Container.get(AppConfigDbService);
    const curatedData = await service.getCuratedDatasetRows(table);
    logger.info('Returning top 100 rows for the table', {table});
    return curatedData;
  }
}
