import {
  Container,
  HttpStatusCodes,
  IctError,
  ProtectedRouteMiddleware,
  AppConfigSetting,
  AppConfigSettingSource,
  SecurityRoles,
  ContextService,
  WinstonLogger,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {USER_WRITABLE_GROUP_NAME, UserWritableGroupType} from 'ict-api-schema';
import {
  Body,
  Controller,
  Middlewares,
  Put,
  Route,
  SuccessResponse,
  Response,
  OperationId,
  Tags,
  Example,
} from 'tsoa';

import {ConfigSettingsService} from '../services/config-settings-service.ts';
import type {UpdateSettingData} from '../defs/app-configuration-setting.ts';

const dbMiddleware = {
  databases: [configPostgresDatabase()],
};

@Middlewares(
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, dbMiddleware),
)
@Route('config')
export class UserWritableController extends Controller {
  private logger: WinstonLogger;

  constructor() {
    super();
    this.logger = Container.get(WinstonLogger);
  }

  static readonly exampleUpdateSettingResponse: AppConfigSetting = {
    id: '0e0f42b-1b58-4a5c-a785-5e6af',
    name: UserWritableGroupType.Favorites,
    group: USER_WRITABLE_GROUP_NAME,
    dataType: 'json',
    value: {
      favorites: ['route-1', 'route-2'],
    },
    source: AppConfigSettingSource.user,
  };

  /**
   * Updates a user writable setting. This endpoint is specifically for user-level settings.
   * Any authenticated user can update their own settings, and configurators can update any user's settings.
   * @param {UpdateSettingData & { userId?: string }} settingInfo Identifying information about the requested Setting resource, as well as requested updates.
   * The setting must be in the 'user-writable' group and will always be updated at the user level.
   * If the user is a ct_configurator, they may specify a userId to update another user's setting.
   * @returns {Promise<AppConfigSetting>} app config setting after results have been applied
   * @throws IctError
   */
  @Example<AppConfigSetting>(
    UserWritableController.exampleUpdateSettingResponse,
  )
  @SuccessResponse(HttpStatusCodes.OK)
  @Response(HttpStatusCodes.CREATED, 'New setting created')
  @Put('/user-writable')
  @OperationId('PutUserWritable')
  @Tags('config')
  public async updateUserWritable(
    @Body() settingInfo: UpdateSettingData & {userId?: string},
  ): Promise<AppConfigSetting> {
    // Get user context
    const context = Container.get(ContextService);
    const currentUserId = context.userId;
    const userRoles = context.userRoles || [];
    const isConfigurator = userRoles.includes(SecurityRoles.CT_CONFIGURATORS);

    this.logger.info('UserWritableController Debug:', {
      currentUserId,
      userRoles,
      isConfigurator,
      requestedUserId: settingInfo.userId,
      settingInfo,
      context: {
        hasUserId: !!context.userId,
        hasUserRoles: !!context.userRoles,
        rawContext: context,
      },
    });

    // Validate that the setting is in the user-writable group
    if (settingInfo.group !== USER_WRITABLE_GROUP_NAME) {
      throw IctError.badRequest(
        'Only settings in the user-writable group can be updated through this endpoint',
      );
    }

    // Determine which userId to update
    let targetUserId = currentUserId;
    if (isConfigurator && settingInfo.userId) {
      targetUserId = settingInfo.userId;
    } else if (
      !isConfigurator &&
      settingInfo.userId &&
      settingInfo.userId !== currentUserId
    ) {
      throw IctError.forbidden(
        'You can only update your own user-writable settings.',
      );
    }

    const service = Container.get(ConfigSettingsService);

    const userSettingInfo = {
      ...settingInfo,
      levelToUpdate: AppConfigSettingSource.user,
      userId: targetUserId,
    };

    // For regular users, check if setting exists first
    if (!isConfigurator) {
      // First check for user-specific setting
      const existingUserSetting = await service.getSingleSetting(
        undefined,
        userSettingInfo.name,
        AppConfigSettingSource.user,
        false,
      );

      if (!existingUserSetting) {
        // If no user setting exists, check if there's a default setting
        const defaultSetting = await service.getSingleSetting(
          undefined,
          userSettingInfo.name,
          AppConfigSettingSource.default,
          false,
        );

        if (!defaultSetting) {
          throw IctError.notFound(
            'Setting does not exist. Only updates to existing settings are allowed.',
          );
        }
      }
    }

    const updatedSetting = await service.updateOrCreateSetting(userSettingInfo);

    this.setStatus(
      updatedSetting.created ? HttpStatusCodes.CREATED : HttpStatusCodes.OK,
    );

    return updatedSetting.setting;
  }
}
