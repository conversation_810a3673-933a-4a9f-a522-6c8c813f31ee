import {
  AppConfigSetting,
  AppConfigSettingDataType,
  AppConfigSettingSource,
} from 'ict-api-foundations';

export interface UpdateSettingData {
  id?: string;
  name: string;
  group?: string | null;
  dataType: AppConfigSettingDataType;
  value: unknown;
  levelToUpdate: AppConfigSettingSource;
  description?: string | null;
}

export interface DefaultConfigSeedingResult {
  successful: AppConfigSetting[];
  unsuccessful: AppConfigSetting[];
  existing: AppConfigSetting[];
}

export interface DeletedRecordsResult {
  recordsDeleted: number;
  message?: string;
}

export interface UpdateOrCreateSettingOutcome {
  setting: AppConfigSetting;
  created: boolean;
}
