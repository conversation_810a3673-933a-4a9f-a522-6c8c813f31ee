import {Storage, Bucket} from '@google-cloud/storage';
import {
  Container,
  DiService,
  EnvironmentService,
  WinstonLogger,
} from 'ict-api-foundations';

const APP_CONFIG_STORAGE_BUCKET = 'configapidata';

@DiService()
export class AppConfigStorageBucket {
  private storage: Storage;
  private readonly bucketName: string;
  private bucket: Bucket;
  private logger: <PERSON>Logger;
  private envService: EnvironmentService;

  constructor(name?: string) {
    this.storage = new Storage();
    this.envService = Container.get(EnvironmentService);
    this.bucketName = this.getBucketName(name);
    this.bucket = this.storage.bucket(this.bucketName);
    this.logger = Container.get(WinstonLogger);
    this.logger.info(`bucket name: [${this.bucketName}]`);
  }

  public async isAvailable(): Promise<boolean> {
    this.logger.info(
      `Checking if storage bucket is available. ${this.bucketName}`
    );
    const value = await this.bucket.exists();
    this.logger.info(`bucket available = ${value}`);
    return value.toString() === 'true';
  }

  public async fileExists(file: string): Promise<boolean> {
    this.logger.info(`Checking if file is available. ${file}`);
    const value = await this.bucket.file(file).exists();
    this.logger.info(`file available = ${value}`);
    return value.toString() === 'true';
  }

  public async fileDownload(file: string) {
    return await this.bucket.file(file).download();
  }

  private getBucketName(name?: string): string {
    if (name) return name;
    return this.envService.bucket.name
      ? this.envService.bucket.name
      : APP_CONFIG_STORAGE_BUCKET;
  }
}
