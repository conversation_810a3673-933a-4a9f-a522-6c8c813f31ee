import {
  BigQueryDate,
  BigQueryDatetime,
  BigQueryInt,
  BigQueryTime,
  BigQueryTimestamp,
  Geography,
  Query,
} from '@google-cloud/bigquery';
import {
  ConfigStore,
  ContextService,
  DatabaseProvider,
  DiService,
  IctError,
} from 'ict-api-foundations';
import {Table} from '../defs/app-config-db-def.ts';
import {
  CuratedTableColumn,
  CuratedTableColumnValueRenderType,
} from '../defs/curated-table-column.ts';
import {CuratedTableRow} from '../defs/curated-table-row.ts';

@DiService()
export class AppConfigDbStore {
  constructor(
    private context: ContextService,
    private configStore: ConfigStore
  ) {}

  get dbProvider(): DatabaseProvider {
    return this.context.dbProvider;
  }

  /**
   * Retrieve the top 100 rows for the given table name.
   * @param tableName BigQuery Table Name.
   * @returns Raw row results.
   */
  async getCuratedDataset(tableName: string): Promise<unknown[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const edpTable = this.dbProvider.bigQuery.getEdpFullTablePath(tableName);

    // Filter by partition colum __raw_message_ingestion_time and select last 100 rows
    const sql = `SELECT * 
             FROM ${edpTable} 
             WHERE TIMESTAMP_TRUNC(__raw_message_ingestion_time, DAY) >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 31 DAY) 
             ORDER BY __raw_message_ingestion_time DESC 
             LIMIT 100`;

    const sqlOptions: Query = {
      query: sql,
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length) {
      throw IctError.noContent();
    }

    return rows;
  }

  /**
   * Retrieve the top 100 rows for the given table name.
   * @param tableName BigQuery Table Name.
   * @returns Raw row results converted into CuratedTableRows.
   */
  async getCuratedDatasetRows(tableName: string): Promise<CuratedTableRow[]> {
    const results = await this.getCuratedDataset(tableName);
    return AppConfigDbStore.getCuratedTableRowsFromRawResults(results);
  }

  async getCuratedTableNames(): Promise<Table[] | undefined> {
    const bigQueryDb = this.context.dbProvider.bigQuery;

    const edpTable = this.dbProvider.bigQuery.getEdpFullTablePath(
      'INFORMATION_SCHEMA.TABLES'
    );

    // return list of tables from silver and bronze datasets excluding views, for the ability to filter by common partition field __raw_message_ingestion_time
    const sql = `
            SELECT
              table_name as table_id
            FROM
              ${edpTable}
            WHERE
              table_type = 'BASE TABLE' -- Exclude views (silver_integration_tests, bronze_integration_tests)
              AND ( 
                STARTS_WITH(table_name, 'silver') OR
                STARTS_WITH(table_name, 'bronze')
              )
            ORDER BY table_name
        `;

    const sqlOptions: Query = {
      query: sql,
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length) {
      throw IctError.noContent();
    }

    return rows as Table[];
  }

  private static getCuratedTableRowsFromRawResults(
    results: unknown[]
  ): CuratedTableRow[] {
    const rows: CuratedTableRow[] = [];
    results.forEach(rawRow => {
      const columnValues: CuratedTableColumn[] = [];
      const row = rawRow as Record<string, unknown>;
      for (const key in row) {
        const columnValue = row[key];
        columnValues.push(
          AppConfigDbStore.createDatabaseColumnValue(key, columnValue)
        );
      }
      rows.push({columns: columnValues});
    });
    return rows;
  }

  private static createDatabaseColumnValue(
    name: string,
    rawValue: unknown
  ): CuratedTableColumn {
    if (rawValue === null) {
      return {
        name,
        renderType: CuratedTableColumnValueRenderType.Null,
        value: rawValue,
      };
    }
    if (
      rawValue instanceof BigQueryDate ||
      rawValue instanceof BigQueryTimestamp ||
      rawValue instanceof BigQueryDatetime ||
      rawValue instanceof BigQueryTime
    ) {
      return {
        name,
        renderType: CuratedTableColumnValueRenderType.String,
        value: rawValue.value,
      };
    }
    if (rawValue instanceof Date) {
      return {
        name,
        renderType: CuratedTableColumnValueRenderType.String,
        value: rawValue.toISOString(),
      };
    }
    if (rawValue instanceof Geography) {
      return {
        name,
        renderType: CuratedTableColumnValueRenderType.String,
        value: rawValue.value,
      };
    }
    if (rawValue instanceof BigQueryInt) {
      return {
        name,
        renderType: CuratedTableColumnValueRenderType.Number,
        value: Number(rawValue),
      };
    }
    if (typeof rawValue === 'object') {
      return {
        name,
        renderType: CuratedTableColumnValueRenderType.String,
        value: JSON.stringify(rawValue),
      };
    }
    if (typeof rawValue === 'boolean') {
      return {
        name,
        renderType: CuratedTableColumnValueRenderType.Boolean,
        value: rawValue,
      };
    }
    if (typeof rawValue === 'number') {
      return {
        name,
        renderType: CuratedTableColumnValueRenderType.Number,
        value: rawValue,
      };
    }
    return {
      name,
      renderType: CuratedTableColumnValueRenderType.String,
      value: rawValue,
    };
  }
}
