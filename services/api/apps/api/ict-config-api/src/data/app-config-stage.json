{"data": {"api": {"baseUrl": "https://stage.api.ict.dematic.dev/", "operators": {"endpoints": {"get_operators_active_series": {"path": "/operators/active/series"}, "get_operators_areas_status": {"path": "/operators/areas/status"}, "get_operators_active_areas": {"path": "/operators/active/areas"}, "get_operators_active_areas_map": {"path": "/operators/active/areas"}, "get_operators_areas": {"path": "/operators/areas"}, "get_operators_active": {"path": "/operators/active"}, "put_operators_areas": {"path": "/operators/areas"}}}, "inventory": {"endpoints": {"get_inventory_filter": {"path": "/inventory/filter"}, "get_inventory_accuracy": {"path": "/inventory/accuracy"}, "get_inventory_advices_cycle_time": {"path": "/inventory/advices/cycle-time"}, "get_inventory_advices_finished": {"path": "/inventory/advices/finished"}, "get_inventory_advices_in_progress": {"path": "/inventory/advices/in-progress"}, "get_inventory_advices_list": {"path": "/inventory/advices/list"}, "get_inventory_advices_outstanding": {"path": "/inventory/advices/outstanding"}, "post_inventory_sku_list": {"path": "/inventory/skus/list"}, "get_inventory_skus_skuid_events_list": {"path": "/inventory/skus/<skuId>/events/list"}, "get_inventory_storage_utilization": {"path": "/inventory/storage/utilization"}, "get_inventory_category_filter": {"path": "/inventory/category/list"}, "get_inventory_high_impact_skus_list": {"path": "/inventory/sku/high-impact/list"}, "get_inventory_performance_series": {"path": "/inventory/performance/series"}, "get_inventory_stock_events_list": {"path": "/inventory/stock/events/list"}, "get_inventory_forecast_data_analysis_timestamp": {"path": "/inventory/forecast/data-analysis-timestamp"}, "get_inventory_forecast_list": {"path": "/inventory/forecast/list"}}}, "equipment": {"endpoints": {"get_equipment_faults": {"path": "/equipment/faults"}, "get_equipment_faults_active": {"path": "/equipment/faults/active/list"}, "get_equipment_faults_details": {"path": "/equipment/faults/details"}, "get_equipment_faults_areas": {"path": "/equipment/faults/areas"}, "get_equipment_faults_series": {"path": "/equipment/faults/series"}, "get_equipment_faults_areas_status": {"path": "/equipment/faults/areas/status"}, "get_inventory_advice_details": {"path": "/inventory/advices/<adviceId>/details"}, "get_equipment_faults_events": {"path": "/equipment/faults/events"}, "get_equipment_summary_areas": {"path": "/equipment/summary/areas"}, "get_equipment_outbound_rate": {"path": "/equipment/outboundrate"}, "get_equipment_movements_detail": {"path": "/equipment/<equipmentId>/movements/detail"}, "get_equipment_events_aislenodeevents": {"path": "/equipment/events/aisle-node-events"}, "get_equipment_workstation_movements_detail": {"path": "/equipment/workstation/<workstationId>/movements/detail"}, "get_equipment_workstation_aisle_active_faults": {"path": "/equipment/workstation/<workstationId>/aisle/active-faults"}, "get_equipment_workstation_operator_activity": {"path": "/equipment/workstation/<workstationId>/operator/activity"}, "get_equipment_aisle_metrics_detail": {"path": "/equipment/summary/aisles/01"}, "get_equipment_workstation_line_rates_series": {"path": "/equipment/workstation/<workstationId>/line-rates/series"}, "get_equipment_workstation_starved_blocked_time_series": {"path": "/equipment/workstation/<workstationId>/starved-blocked-time/series"}, "get_equipment_summary_workstation": {"path": "/equipment/summary/workstations/01"}}}, "workstation": {"endpoints": {"get_workstation_list": {"path": "/workstation/list"}, "get_workstation_performance": {"path": "/workstation/metrics/performance"}, "get_workstation_summary": {"path": "/workstation/metrics/summary"}, "get_workstation_list_series": {"path": "/workstation/list/series"}}}, "orders": {"endpoints": {"get_orders_fulfillment_outstanding": {"path": "/orders/fulfillment-outstanding"}, "get_orders_fulfillment": {"path": "/orders/fulfillment"}, "get_orders_completion": {"path": "/orders/completion"}, "get_orders_progress": {"path": "/orders/customer/progress"}, "get_customer_orders_progress": {"path": "/orders/customer/progress"}, "get_pick_orders_progress": {"path": "/orders/progress"}, "get_orders_shipped": {"path": "/orders/customer/shipped"}, "get_customer_orders_shipped": {"path": "/orders/customer/shipped"}, "get_pick_orders_shipped": {"path": "/orders/shipped"}, "get_orders_throughput": {"path": "/orders/throughput"}, "get_orders_throughput_series": {"path": "/orders/throughput/series"}, "get_orders_throughput_areas_status": {"path": "/orders/throughput/areas/status"}, "get_orders_throughput_areas": {"path": "/orders/throughput/areas"}, "get_orders_cycletime": {"path": "/orders/pick/cycletime"}, "get_customer_orders_cycletime": {"path": "/orders/customer/cycletime"}, "get_orders_cycletime_areas_status": {"path": "/orders/cycletime/areas/status"}, "get_orders_cycletime_areas": {"path": "/orders/cycletime/areas"}, "get_orders_cycletime_series": {"path": "/orders/cycletime/series"}, "get_orders_lineprogress": {"path": "/orders/lineprogress"}, "get_orders_lineprogress_series": {"path": "/orders/lineprogress/series"}, "get_orders_lineprogress_areas": {"path": "/orders/lineprogress/areas"}, "get_orders_lineprogress_areas_status": {"path": "/orders/lineprogress/areas/status"}, "get_orders_fulfillment_rate": {"path": "/orders/performance/fulfillment"}, "get_orders_units_remaining": {"path": "/orders/remaining"}}}, "ai": {"endpoints": {"get_enterprise_search": {"path": "/ai/enterprise-search"}}}, "dataExplorer": {"endpoints": {"get_data_explorer_search": {"path": "/data-explorer/search"}, "get_data_explorer_results": {"path": "/data-explorer/results"}, "get_data_explorer_results_id": {"path": "/data-explorer/results/<resultId>"}, "get_data_explorer_suggested_recommendations": {"path": "/data-explorer/recommendations"}, "put_data_explorer_results": {"path": "/data-explorer/results/<id>"}, "delete_data_explorer_results_id": {"path": "/data-explorer/results/<resultId>"}}}, "tableauProxy": {"endpoints": {"get_tableau_proxy": {"path": "/tableau/api/3.6", "baseURL": "https://dev-ict-tableau-proxy-dz2p45qgkq-ue.a.run.app/"}}}, "processFlow": {"endpoints": {"get_areas": {"path": "/process-flow/areas"}}}}, "dataUrls": {"mockApi": "https://stage-ict-mock-data-gen-kdpydjklpq-ue.a.run.app/", "tableauMovementsAndFaultsDashboard": "https://na.insights.dematic.cloud/t/StarkIndustries/views/MovementsandFaultsOverview/MovementsandFaultsOverview", "tableauMovementsAndFaultsDetailsDashboard": "https://na.insights.dematic.cloud/t/StarkIndustries/views/MovementsandFaultsDetails/MSMovementsandFaults", "tableauMovementsDashboard": "https://na.insights.dematic.cloud/t/StarkIndustries/views/MovementsOverview/MovementsOverview", "tableauHistoricalMovementsAndFaultsDashboard": "https://na.insights.dematic.cloud/t/StarkIndustries/views/HistoricalKPIComparison/HistoricalOverview", "tableauShuttleHardwareAnalysis": "https://na.insights.dematic.cloud/t/StarkIndustries/views/ShuttleHardwareAnalysis/ShuttleHardwareAnalysis", "insightsSiteName": "StarkIndustries"}, "settings": {"logLevel": "debug"}, "site": {"timezone": "America/New_York"}, "packages": [{"name": "process-flow-visualization", "enabled": true}, {"name": "operational-alerting", "enabled": true}, {"name": "automation-visualization", "enabled": true}, {"name": "operations-visibility", "enabled": true}, {"name": "solution-analytics", "enabled": true}, {"name": "performance-analysis", "enabled": true}, {"name": "advanced-orchestration", "enabled": true}, {"name": "config-administration", "enabled": true}, {"name": "application-health", "enabled": true, "requiredRoles": ["ct_engineers"]}]}}