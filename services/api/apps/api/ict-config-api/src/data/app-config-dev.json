{"data": {"api": {"baseUrl": "https://run-ict-d-api.ict.dematic.dev/", "overrides": [{"operationId": "getEquipmentFaults", "baseUrl": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, {"operationId": "getEquipmentSummaryWorkstations", "baseUrl": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, {"operationId": "getEquipmentFaultsAreas", "baseUrl": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, {"operationId": "getEquipmentSummaryAreas", "baseUrl": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, {"operationId": "getEquipmentOutboundRate", "baseUrl": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, {"operationId": "getEquipmentFaultsActiveList", "baseUrl": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, {"operationId": "getEquipmentFaultsEvents", "baseUrl": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, {"operationId": "getEquipmentFaultsSeries", "baseUrl": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, {"operationId": "getEquipmentMovementsDetail", "baseUrl": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, {"operationId": "getEquipmentEventsAisleNodeEvents", "baseUrl": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, {"operationId": "getEquipmentWorkstationStarvedBlockedTimeSeries", "baseUrl": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, {"operationId": "getEquipmentWorkstationOperatorActivity", "baseUrl": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}], "operators": {"endpoints": {"get_operators_active_series": {"path": "/operators/active/series"}, "get_operators_areas_status": {"path": "/operators/areas/status", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_operators_active_areas": {"path": "/operators/active/areas", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_operators_active_areas_map": {"path": "/operators/active/areas", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_operators_areas": {"path": "/operators/areas"}, "get_operators_active": {"path": "/operators/active"}, "put_operators_areas": {"path": "/operators/areas"}}}, "inventory": {"endpoints": {"get_inventory_filter": {"path": "/inventory/filter", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_inventory_accuracy": {"path": "/inventory/accuracy", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_inventory_advices_cycle_time": {"path": "/inventory/advices/cycle-time"}, "get_inventory_advices_finished": {"path": "/inventory/advices/finished"}, "get_inventory_advices_in_progress": {"path": "/inventory/advices/in-progress"}, "get_inventory_advices_list": {"path": "/inventory/advices/list"}, "get_inventory_advices_outstanding": {"path": "/inventory/advices/outstanding"}, "get_inventory_advice_details": {"path": "/inventory/advices/<adviceId>/details"}, "post_inventory_sku_list": {"path": "/inventory/skus/list", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_inventory_skus_skuid_events_list": {"path": "/inventory/skus/<skuId>/events/list"}, "get_inventory_storage_utilization": {"path": "/inventory/storage/utilization", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_inventory_category_filter": {"path": "/inventory/category/list", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_inventory_high_impact_skus_list": {"path": "/inventory/sku/high-impact/list"}, "get_inventory_performance_series": {"path": "/inventory/performance/series", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_inventory_stock_events_list": {"path": "/inventory/stock/events/list", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_inventory_forecast_data_analysis_timestamp": {"path": "/inventory/forecast/data-analysis-timestamp", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_inventory_forecast_list": {"path": "/inventory/forecast/list", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}}}, "equipment": {"endpoints": {"get_equipment_faults": {"path": "/equipment/faults", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_equipment_faults_active": {"path": "/equipment/faults/active/list", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_equipment_faults_details": {"path": "/equipment/faults/details", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_equipment_faults_areas": {"path": "/equipment/faults/areas", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_equipment_faults_series": {"path": "/equipment/faults/series"}, "get_equipment_faults_areas_status": {"path": "/equipment/faults/areas/status", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_equipment_faults_events": {"path": "/equipment/faults/events", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_equipment_summary_areas": {"path": "/equipment/summary/areas", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_equipment_outbound_rate": {"path": "/equipment/outboundrate", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_equipment_movements_detail": {"path": "/equipment/<equipmentId>/movements/detail", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_equipment_events_aislenodeevents": {"path": "/equipment/events/aisle-node-events", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_equipment_workstation_movements_detail": {"path": "/equipment/workstation/<workstationId>/movements/detail"}, "get_equipment_workstation_aisle_active_faults": {"path": "/equipment/workstation/<workstationId>/aisle/active-faults"}, "get_equipment_workstation_operator_activity": {"path": "/equipment/workstation/<workstationId>/operator/activity"}, "get_equipment_aisle_metrics_detail": {"path": "/equipment/summary/aisles/01", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_equipment_workstation_line_rates_series": {"path": "/equipment/workstation/<workstationId>/line-rates/series"}, "get_equipment_workstation_starved_blocked_time_series": {"path": "/equipment/workstation/<workstationId>/starved-blocked-time/series"}, "get_equipment_summary_workstation": {"path": "/equipment/summary/workstations/01", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}}}, "orders": {"endpoints": {"get_orders_fulfillment_outstanding": {"path": "/orders/fulfillment-outstanding", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_orders_fulfillment": {"path": "/orders/fulfillment", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_orders_completion": {"path": "/orders/completion", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_orders_progress": {"path": "/orders/customer/progress"}, "get_customer_orders_progress": {"path": "/orders/customer/progress"}, "get_pick_orders_progress": {"path": "/orders/progress"}, "get_orders_shipped": {"path": "/orders/customer/shipped"}, "get_customer_orders_shipped": {"path": "/orders/customer/shipped"}, "get_pick_orders_shipped": {"path": "/orders/shipped"}, "get_orders_throughput": {"path": "/orders/throughput", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_orders_throughput_series": {"path": "/orders/throughput/series"}, "get_orders_throughput_areas_status": {"path": "/orders/throughput/areas/status", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_orders_throughput_areas": {"path": "/orders/throughput/areas", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_orders_cycletime": {"path": "/orders/pick/cycletime", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_customer_orders_cycletime": {"path": "/orders/customer/cycletime"}, "get_orders_cycletime_areas_status": {"path": "/orders/cycletime/areas/status", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app"}, "get_orders_cycletime_areas": {"path": "/orders/cycletime/areas", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_orders_cycletime_series": {"path": "/orders/cycletime/series"}, "get_orders_lineprogress": {"path": "/orders/lineprogress"}, "get_orders_lineprogress_series": {"path": "/orders/lineprogress/series", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_orders_lineprogress_areas": {"path": "/orders/lineprogress/areas", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_orders_lineprogress_areas_status": {"path": "/orders/lineprogress/areas/status", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_orders_fulfillment_rate": {"path": "/orders/performance/fulfillment", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_orders_units_remaining": {"path": "/orders/remaining", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}}}, "ai": {"endpoints": {"get_enterprise_search": {"path": "/ai/enterprise-search"}}}, "dataExplorer": {"endpoints": {"get_data_explorer_search": {"path": "/data-explorer/search"}, "get_data_explorer_results": {"path": "/data-explorer/results"}, "get_data_explorer_results_id": {"path": "/data-explorer/results/<resultId>"}, "get_data_explorer_suggested_recommendations": {"path": "/data-explorer/recommendations"}, "put_data_explorer_results": {"path": "/data-explorer/results/<id>"}, "delete_data_explorer_results_id": {"path": "/data-explorer/results/<resultId>"}}}, "workstation": {"endpoints": {"get_workstation_list": {"path": "/workstation/list", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_workstation_performance": {"path": "/workstation/metrics/performance", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_workstation_summary": {"path": "/workstation/metrics/summary", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}, "get_workstation_list_series": {"path": "/workstation/list/series", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}}}, "tableauProxy": {"endpoints": {"get_tableau_proxy": {"path": "/tableau/api/3.6"}}}, "processFlow": {"endpoints": {"get_areas": {"path": "/process-flow/areas", "baseURL": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/"}}}}, "dataUrls": {"mockApi": "https://dev-ict-mock-data-gen-dz2p45qgkq-ue.a.run.app/", "tableauMovementsAndFaultsDashboard": "https://na.insights.dematic.cloud/t/StarkIndustries/views/MovementsandFaultsOverview/MovementsandFaultsOverview", "tableauMovementsAndFaultsDetailsDashboard": "https://na.insights.dematic.cloud/t/StarkIndustries/views/MovementsandFaultsDetails/MSMovementsandFaults", "tableauMovementsDashboard": "https://na.insights.dematic.cloud/t/StarkIndustries/views/MovementsOverview/MovementsOverview", "tableauHistoricalMovementsAndFaultsDashboard": "https://na.insights.dematic.cloud/t/StarkIndustries/views/HistoricalKPIComparison/HistoricalOverview", "tableauShuttleHardwareAnalysis": "https://na.insights.dematic.cloud/t/StarkIndustries/views/ShuttleHardwareAnalysis/ShuttleHardwareAnalysis", "insightsSiteName": "StarkIndustries"}, "settings": {"logLevel": "debug"}, "site": {"timezone": "America/New_York"}, "packages": [{"name": "process-flow-visualization", "enabled": true}, {"name": "operational-alerting", "enabled": true}, {"name": "automation-visualization", "enabled": true}, {"name": "operations-visibility", "enabled": true}, {"name": "solution-analytics", "enabled": true}, {"name": "performance-analysis", "enabled": true}, {"name": "advanced-orchestration", "enabled": true}, {"name": "application-health", "enabled": true, "requiredRoles": ["ct_engineers"]}, {"name": "config-administration", "enabled": true}]}}