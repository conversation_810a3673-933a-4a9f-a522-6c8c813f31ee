{"name": "ict-config-api", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/ict-config-api/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "ict-config-api:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "ict-config-api:build:development"}, "production": {"buildTarget": "ict-config-api:build:production"}}}}}