import {DateTime} from 'luxon';
import {ContextService, DatabaseProvider, DiService} from 'ict-api-foundations';
import {DiagnosticsInfrastructureQueryResponseItem} from '../defs/diagnostics-infrastructure-list-def.ts';

@DiService()
export class DiagnosticsStore {
  constructor(private context: ContextService) {}

  get dbProvider(): DatabaseProvider {
    return this.context.dbProvider;
  }

  getDiagnosticsInfrastructureList(): Promise<
    DiagnosticsInfrastructureQueryResponseItem[]
  > {
    // TODO: Make call to database
    // TODO2: Remove skip on test
    const now = DateTime.utc();
    return Promise.resolve([
      {
        name: 'asoswcs2',
        last_update: now.minus({minutes: 1}).toJSDate().toISOString(),
        status: 'Offline',
        cpu_percentage: 16,
        memory_usage_percentage: 68.3,
        disk_usage_percentage: 38.7,
        labels: ['v6.1 A'],
      },
      {
        name: 'asoswcs2',
        last_update: now.minus({minutes: 15}).toJSDate().toISOString(),
        status: 'Online',
        cpu_percentage: 16,
        memory_usage_percentage: 68.3,
        disk_usage_percentage: 38.7,
        labels: ['v6.1 A'],
      },
      {
        name: 'asoswcs2',
        last_update: now.minus({minutes: 21}).toJSDate().toISOString(),
        status: 'Offline',
        cpu_percentage: 16,
        memory_usage_percentage: 68.3,
        disk_usage_percentage: 38.7,
        labels: ['v6.1 A'],
      },
      {
        name: 'asoswcs2',
        last_update: now.minus({minutes: 22}).toJSDate().toISOString(),
        status: 'Online',
        cpu_percentage: 16,
        memory_usage_percentage: 68.3,
        disk_usage_percentage: 38.7,
        labels: ['v6.1 A'],
      },
      {
        name: 'asoswcs2',
        last_update: now.minus({minutes: 34}).toJSDate().toISOString(),
        status: 'Online',
        cpu_percentage: 16,
        memory_usage_percentage: 68.3,
        disk_usage_percentage: 38.7,
        labels: ['v6.1 A'],
      },
      {
        name: 'asoswcs2',
        last_update: now.minus({minutes: 34}).toJSDate().toISOString(),
        status: 'Online',
        cpu_percentage: 16,
        memory_usage_percentage: 68.3,
        disk_usage_percentage: 38.7,
        labels: ['v6.1 A'],
      },
      {
        name: 'asoswcs2',
        last_update: now.minus({minutes: 34}).toJSDate().toISOString(),
        status: 'Online',
        cpu_percentage: 16,
        memory_usage_percentage: 68.3,
        disk_usage_percentage: 38.7,
        labels: ['v6.1 A'],
      },
      {
        name: 'asoswcs2',
        last_update: now.minus({minutes: 34}).toJSDate().toISOString(),
        status: 'Online',
        cpu_percentage: 16,
        memory_usage_percentage: 68.3,
        disk_usage_percentage: 38.7,
        labels: ['v6.1 A'],
      },
      {
        name: 'asoswcs2',
        last_update: now
          .minus({hours: 1, minutes: 23})
          .toJSDate()
          .toISOString(),
        status: 'Online',
        cpu_percentage: 16,
        memory_usage_percentage: 68.3,
        disk_usage_percentage: 38.7,
        labels: ['v6.1 A'],
      },
      {
        name: 'asoswcs2',
        last_update: now
          .minus({hours: 1, minutes: 23})
          .toJSDate()
          .toISOString(),
        status: 'Online',
        cpu_percentage: 16,
        memory_usage_percentage: 68.3,
        disk_usage_percentage: 38.7,
        labels: ['v6.1 A'],
      },
    ]);
  }
}
