import {DiService} from 'ict-api-foundations';
import {DiagnosticsStore} from '../stores/diagnostics-store.ts';
import {DiagnosticsInfrastructureObject} from '../defs/diagnostics-infrastructure-object.ts';

/** Service that handles applying business logic for Diagnostics to store queries. */
@DiService()
export class DiagnosticsService {
  constructor(private diagnosticsStore: DiagnosticsStore) {}

  async getDiagnosticsInfrastructureList(): Promise<
    DiagnosticsInfrastructureObject[]
  > {
    const list = await this.diagnosticsStore.getDiagnosticsInfrastructureList();
    return list.map(item => ({
      name: item.name,
      lastUpdate: item.last_update,
      status: item.status.toString(),
      cpuPercentage: item.cpu_percentage,
      memoryUsagePercentage: item.memory_usage_percentage,
      diskUsagePercentage: item.disk_usage_percentage,
      labels: item.labels,
    }));
  }
}
