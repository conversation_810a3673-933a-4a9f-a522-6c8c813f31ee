import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {DiagnosticsService} from '../../services/diagnostics-service.ts';
import {DiagnosticsInfrastructureListController} from '../../controllers/diagnostics-infrastructure-list-controller.ts';

describe('DiagnosticsDiagnosticsListController', () => {
  const url: string = '/diagnostics/infrastructure/list';
  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Diagnostics Service. */
  let diagnosticsServiceStub: sinon.SinonStubbedInstance<DiagnosticsService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Diagnostics Accuracy Controller Error scenarios', () => {
    it('should error when the service throws an error', async () => {
      diagnosticsServiceStub = sinon.createStubInstance(DiagnosticsService);
      Container.set(DiagnosticsService, diagnosticsServiceStub);
      diagnosticsServiceStub.getDiagnosticsInfrastructureList.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app).get(url).send();

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWithMatch(
        diagnosticsServiceStub.getDiagnosticsInfrastructureList
      );
    });
  });
  describe('Diagnostics Infrastructure Controller success scenarios', () => {
    beforeEach(() => {
      diagnosticsServiceStub = sinon.createStubInstance(DiagnosticsService);
      Container.set(DiagnosticsService, diagnosticsServiceStub);
      diagnosticsServiceStub.getDiagnosticsInfrastructureList.resolves(
        DiagnosticsInfrastructureListController.exampleData
      );
    });

    it('should call the service and return the data with default params', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        DiagnosticsInfrastructureListController.exampleResponse
      );

      sinon.assert.calledWithMatch(
        diagnosticsServiceStub.getDiagnosticsInfrastructureList
      );
    });

    it('should call the service and return the data with passed params', async () => {
      const response = await request(app).get(url).send();

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        DiagnosticsInfrastructureListController.exampleResponse
      );

      sinon.assert.calledWithMatch(
        diagnosticsServiceStub.getDiagnosticsInfrastructureList
      );
    });
  });
});
