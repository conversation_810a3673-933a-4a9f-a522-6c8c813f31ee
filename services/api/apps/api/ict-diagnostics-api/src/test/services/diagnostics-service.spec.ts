import {expect} from 'chai';
import sinon from 'sinon';
import {DiagnosticsStore} from '../../stores/diagnostics-store.ts';
import {DiagnosticsService} from '../../services/diagnostics-service.ts';
import {DiagnosticsInfrastructureQueryResponseItem} from '../../defs/diagnostics-infrastructure-list-def.ts';
import {DiagnosticsInfrastructureObject} from '../../defs/diagnostics-infrastructure-object.ts';

describe('DiagnosticsService', () => {
  let diagnosticsStoreStub: sinon.SinonStubbedInstance<DiagnosticsStore>;
  let diagnosticsService: DiagnosticsService;

  beforeEach(() => {
    diagnosticsStoreStub = sinon.createStubInstance(DiagnosticsStore);
    diagnosticsService = new DiagnosticsService(diagnosticsStoreStub);
  });

  describe('getDiagnosticsInfrastructureList', () => {
    let fakeStoreResponse: DiagnosticsInfrastructureQueryResponseItem[];

    beforeEach(() => {
      fakeStoreResponse = [
        {
          name: 'asoswcs2',
          last_update: '2023-01-01T00:00:00.000Z',
          status: 'Offline',
          cpu_percentage: 16,
          memory_usage_percentage: 68.3,
          disk_usage_percentage: 38.7,
          labels: ['v6.1 A'],
        },
      ];

      diagnosticsStoreStub.getDiagnosticsInfrastructureList.resolves(
        fakeStoreResponse
      );
    });

    it('should return the correct data', async () => {
      const result =
        await diagnosticsService.getDiagnosticsInfrastructureList();
      const expected: DiagnosticsInfrastructureObject[] = [
        {
          name: 'asoswcs2',
          lastUpdate: '2023-01-01T00:00:00.000Z',
          status: 'Offline',
          cpuPercentage: 16,
          memoryUsagePercentage: 68.3,
          diskUsagePercentage: 38.7,
          labels: ['v6.1 A'],
        },
      ];
      expect(result).to.deep.equal(expected);
    });
  });
});
