import {expect} from 'chai';
import {
  BigQueryDatabase,
  ContextService,
  DatabaseProvider,
} from 'ict-api-foundations';
import sinon from 'sinon';
import {BigQuery} from '@google-cloud/bigquery';
import {DiagnosticsStore} from '../../stores/diagnostics-store.ts';
import {DiagnosticsInfrastructureQueryResponseItem} from '../../defs/diagnostics-infrastructure-list-def.ts';

describe('DiagnosticsStore', () => {
  let diagnosticsStore: DiagnosticsStore;
  let queryStub: sinon.SinonStubbedInstance<BigQuery>;
  let dbProvider: DatabaseProvider;
  let contextService: ContextService;

  beforeEach(() => {
    queryStub = sinon.createStubInstance(BigQuery);
    dbProvider = new DatabaseProvider();
    const bqDatabase = new BigQueryDatabase(queryStub, 'testDataset');
    dbProvider.set(bqDatabase.getType(), bqDatabase);

    contextService = new ContextService();
    sinon.stub(contextService, 'dbProvider').get(() => dbProvider);
    diagnosticsStore = new DiagnosticsStore(contextService);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getDiagnosticsInfrastructureList', () => {
    let fakeQueryResponse: [DiagnosticsInfrastructureQueryResponseItem[]];

    beforeEach(() => {
      fakeQueryResponse = [
        [
          {
            name: 'asoswcs2',
            last_update: '2023-01-01T00:00:00.000Z',
            status: 'Offline',
            cpu_percentage: 16,
            memory_usage_percentage: 68.3,
            disk_usage_percentage: 38.7,
            labels: ['v6.1 A'],
          },
        ],
      ];
      queryStub.query.resolves(fakeQueryResponse);
    });

    // TODO Remove skip once query is implemented
    it.skip('should return a list of diagnostics infrastructure from the database', async () => {
      const result = await diagnosticsStore.getDiagnosticsInfrastructureList();
      expect(result).to.deep.equal(fakeQueryResponse);
    });
  });
});
