import {Fi<PERSON><PERSON><PERSON>, Sort<PERSON>ield} from 'ict-api-foundations';

export interface DiagnosticsInfrastructureRequestFields {
  /**
   * @isInt
   * @default 1
   */
  page?: number;
  /**
   * @isInt
   * @default 10
   */
  limit?: number;
  /**
   * @isArray
   * @default []
   */
  filterFields?: FilterField[];
  /**
   * @isArray
   * @default []
   */
  sortFields?: SortField[];
}

export interface DiagnosticsInfrastructureQueryResponseItem {
  /** Name of the infrastucture object */
  name: string;
  /** Time the data was last updated */
  last_update: string;
  /** Status of the infrastructure object */
  status: string;
  /** Percent of CPU used */
  cpu_percentage: number;
  /** Percent of memory used */
  memory_usage_percentage: number;
  /** Percent of disk space used */
  disk_usage_percentage: number;
  /** List of labels for this infrastructure object */
  labels: string[];
}
