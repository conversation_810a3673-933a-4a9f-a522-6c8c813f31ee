/**
 * DTO that describes the Diagnostics Infrastructure Object.
 */
export interface DiagnosticsInfrastructureObject {
  name: string;
  lastUpdate: string;
  status: string | DiagnosticsInfrastructureObjectStatus;
  cpuPercentage: number;
  memoryUsagePercentage: number;
  diskUsagePercentage: number;
  labels: string[];
}

/**
 * DTO that describes the Diagnostics Infrastructure Object Status.
 */
export enum DiagnosticsInfrastructureObjectStatus {
  online = 'Online',
  offline = 'Offline',
}

export type DiagnosticsInfrastructureContract =
  DiagnosticsInfrastructureObject[];
