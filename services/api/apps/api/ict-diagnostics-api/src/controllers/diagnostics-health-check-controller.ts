import {BaseHealthCheck} from 'ict-api-foundations';
import {Get, Route, OperationId, Tags} from 'tsoa';

@Route('/diagnostics/healthcheck')
export class DiagnosticsHealthCheckController {
  @Get()
  @OperationId('GetDiagnosticsBasicHealthCheck')
  @Tags('diagnostics')
  public getDiagnosticsBasicHealthCheck() {
    return BaseHealthCheck.getBasicHealthCheck();
  }

  @Get('/full')
  @OperationId('GetDiagnosticsFullHealthCheck')
  @Tags('diagnostics')
  public getDiagnosticsFullHealthCheck() {
    return BaseHealthCheck.getDeepHealthCheck(['auth0', 'bigQuery', 'redis']);
  }
}
