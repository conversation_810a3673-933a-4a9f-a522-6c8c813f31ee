import {
  CacheMiddleware,
  Container,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Middlewares,
  Get,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {DiagnosticsService} from '../services/diagnostics-service.ts';
import {
  DiagnosticsInfrastructureContract,
  DiagnosticsInfrastructureObject,
} from '../defs/diagnostics-infrastructure-object.ts';

@Route('diagnostics')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class DiagnosticsInfrastructureListController extends Controller {
  public static readonly exampleData: DiagnosticsInfrastructureObject[] = [
    {
      name: 'test name',
      lastUpdate: '2020-01-01T00:00:00.000Z',
      status: 'Offline',
      cpuPercentage: 10,
      memoryUsagePercentage: 20,
      diskUsagePercentage: 30,
      labels: ['test label 1', 'test label 2'],
    },
  ];

  public static readonly exampleResponse: DiagnosticsInfrastructureContract =
    DiagnosticsInfrastructureListController.exampleData;

  /**
   *
   * @returns {Promise<DiagnosticsInfrastructureContract>} contract
   */
  @Example<DiagnosticsInfrastructureContract>(
    DiagnosticsInfrastructureListController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/infrastructure/list')
  @OperationId('GetDiagnosticsInfrastructureList')
  @Tags('diagnostics')
  public async getDiagnosticsInfrastructureList(): Promise<DiagnosticsInfrastructureContract> {
    const diagnosticsService = Container.get(DiagnosticsService);
    return await diagnosticsService.getDiagnosticsInfrastructureList();
  }
}
