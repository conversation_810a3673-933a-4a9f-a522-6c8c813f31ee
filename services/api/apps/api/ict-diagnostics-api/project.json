{"name": "ict-diagnostics-api", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/ict-diagnostics-api/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "ict-diagnostics-api:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "ict-diagnostics-api:build:development"}, "production": {"buildTarget": "ict-diagnostics-api:build:production"}}}}}