# ICT Diagnostics API

## Overview

The ICT Diagnostics API provides diagnostic capabilities for the Control Tower platform. This service enables system administrators and operators to monitor infrastructure health, diagnose issues, and gather system information.

## Purpose

This API serves as the logic layer for handling business logic for diagnostics within Control Tower, providing comprehensive system monitoring and diagnostic capabilities.

## Key Features

- **Infrastructure Monitoring**: Monitor the health and status of various infrastructure components
- **System Diagnostics**: Perform diagnostic checks on system components
- **Health Reporting**: Provide detailed health reports for system components
- **Infrastructure List**: Retrieve and manage infrastructure component listings

## API Endpoints

### Health Check

- `GET /health/basic` - Basic service health check

### Infrastructure Management

- `GET /infrastructure/list` - Retrieve list of infrastructure components
- `GET /infrastructure/{id}/status` - Get status of specific infrastructure component

### Diagnostics

- `POST /diagnostics/run` - Run diagnostic checks
- `GET /diagnostics/reports` - Retrieve diagnostic reports

## Architecture

The service follows the standard Control Tower API architecture:

```bash
src/
├── controllers/     # TSOA controllers with route definitions
│   ├── diagnostics-health-check-controller.ts
│   └── diagnostics-infrastructure-list-controller.ts
├── services/        # Business logic services
├── stores/          # Data access layer
├── defs/           # TypeScript type definitions
├── test/           # Unit tests
└── index.ts        # Service entry point
```

## Dependencies

- **Database**: PostgreSQL for storing diagnostic data and infrastructure information
- **Redis**: Caching for diagnostic results and infrastructure status
- **Monitoring Systems**: Integration with various monitoring and alerting systems

## Environment Variables

- `AUTH_AUDIENCE`: Authentication audience for JWT validation
- `AUTH_DOMAIN`: Auth0 domain for authentication
- `DATABASE_URL`: PostgreSQL connection string
- `REDIS_URL`: Redis connection string

## Development

### Prerequisites

- Node.js >= 22.0.0
- Yarn package manager
- PostgreSQL database
- Redis instance

### Local Development

```bash
# Install dependencies
yarn install

# Start development server
yarn watch

# Run tests
yarn test

# Build for production
yarn build
```

### Testing

```bash
# Run all tests
yarn test

# Run tests in watch mode
yarn test:watch

# Run linting
yarn lint
```

## Deployment

The service is containerized using Docker and can be deployed to Google Cloud Run or other container platforms.

### Docker Build

```bash
docker build -t ict-diagnostics-api .
```

### Environment Configuration

Ensure all required environment variables are configured for the deployment environment.

## Monitoring

- **Health Checks**: Available at `/health/basic`
- **Metrics**: Service metrics exported for monitoring
- **Logging**: Structured JSON logging with correlation IDs

## Use Cases

### System Administration

- Monitor overall system health
- Identify performance bottlenecks
- Track infrastructure changes

### Operations Support

- Quick diagnostic checks
- Infrastructure status reporting
- System troubleshooting

### Maintenance Planning

- Proactive issue detection
- Capacity planning insights
- Performance trend analysis

## Related Services

- **ict-admin-api**: Administrative functions and user management
- **ict-config-api**: Configuration management for diagnostic settings
- **ict-equipment-api**: Equipment monitoring and fault management
