{"entryFile": "src/index.ts", "noImplicitAdditionalProperties": "throw-on-extras", "controllerPathGlobs": ["src/controllers/*.ts"], "spec": {"outputDirectory": "docs", "specVersion": 3, "yaml": true, "specFileBaseName": "openapi", "title": "Tableau Management API", "spec": {"servers": [{"url": "http://localhost:8080/", "description": "Local development"}, {"url": "https://dev.bi.ict.dematic.dev/management", "description": "Dev development"}]}, "securityDefinitions": {"jwt": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}, "auth0": {"type": "oauth2", "authorizationUrl": "https://dev-zmogq9vd.us.auth0.com/authorize", "flow": "implicit"}}}, "routes": {"routesDir": "build", "esm": true}}