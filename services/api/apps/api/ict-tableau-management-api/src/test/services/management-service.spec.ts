import {expect, use} from 'chai';
import chaiAsPromised from 'chai-as-promised';
import sinon from 'sinon';
import {
  ContextService,
  EnvironmentService,
  IctError,
} from 'ict-api-foundations';
import axios, {AxiosRequestConfig} from 'axios';
import {ManagementService} from '../../services/management-service.ts';
import {AuthTicket} from '../../defs/auth-ticket.ts';

function getFormDataEntries(formData: FormData): Record<string, string> {
  const entries: Record<string, string> = {};
  formData.forEach((value, key) => {
    entries[key] = value as string;
  });
  return entries;
}

describe('ManagementService', () => {
  use(chaiAsPromised);
  const testUserId = 'testUserId';
  const testTableauSite = 'testTableauSite';
  const testTableauNodeIp = 'testTableauNodeIp';
  const testTableauTrustedTicket = 'testTableauTrustedTicket';
  const requestConfig: AxiosRequestConfig = {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  };

  let contextService: ContextService;
  let envService: EnvironmentService;
  let managementService: ManagementService;
  let axiosPostStub: sinon.SinonStub;

  beforeEach(() => {
    contextService = new ContextService();
    sinon.stub(contextService, 'userId').get(() => testUserId);

    envService = new EnvironmentService();
    sinon.stub(envService, 'tableau').get(() => {
      return {
        nodeIp: testTableauNodeIp,
      };
    });

    managementService = new ManagementService(contextService, envService);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('retrieveTrustedTicketAsync', () => {
    beforeEach(() => {
      sinon.stub(contextService, 'organization').get(() => {
        return {
          metadata: {},
        };
      });
    });

    it('should return a valid DTO when given a valid Tableau Trusted auth ticket -- default tableau site', async () => {
      axiosPostStub = sinon.stub(axios, 'post');
      axiosPostStub.resolves({data: testTableauTrustedTicket});

      const expectedResult: AuthTicket = {
        userId: testUserId,
        ticket: testTableauTrustedTicket,
      };
      const expectedFormData = new FormData();
      expectedFormData.append('username', testUserId);

      const result = await managementService.retrieveTrustedTicketAsync();

      expect(result).to.deep.equal(expectedResult);
      expect(axiosPostStub.calledOnce).to.be.true;
      expect(axiosPostStub.firstCall.args[0]).to.equal(
        `http://${testTableauNodeIp}/trusted`,
      );
      expect(getFormDataEntries(axiosPostStub.firstCall.args[1])).to.deep.equal(
        getFormDataEntries(expectedFormData),
      );
      expect(axiosPostStub.firstCall.args[2]).to.deep.equal(requestConfig);
    });

    it('should return a valid DTO when given a valid Tableau Trusted auth ticket -- test tableau site', async () => {
      axiosPostStub = sinon.stub(axios, 'post');
      axiosPostStub.resolves({data: testTableauTrustedTicket});

      sinon.stub(contextService, 'organization').get(() => {
        return {
          metadata: {
            tableauSite: testTableauSite,
          },
        };
      });

      const expectedResult: AuthTicket = {
        userId: testUserId,
        ticket: testTableauTrustedTicket,
      };
      const expectedFormData = new FormData();
      expectedFormData.append('username', testUserId);
      expectedFormData.append('target_site', testTableauSite);

      const result = await managementService.retrieveTrustedTicketAsync();

      expect(result).to.deep.equal(expectedResult);
      expect(axiosPostStub.calledOnce).to.be.true;
      expect(axiosPostStub.firstCall.args[0]).to.equal(
        `http://${testTableauNodeIp}/trusted`,
      );
      expect(getFormDataEntries(axiosPostStub.firstCall.args[1])).to.deep.equal(
        getFormDataEntries(expectedFormData),
      );
      expect(axiosPostStub.firstCall.args[2]).to.deep.equal(requestConfig);
    });

    it('should throw 401 error when Tableau Trusted auth ticket is invalid', async () => {
      axiosPostStub = sinon.stub(axios, 'post');
      axiosPostStub.resolves({data: '-1'});

      const outcome = await expect(
        managementService.retrieveTrustedTicketAsync(),
      ).to.be.rejectedWith(IctError);
      expect(outcome.statusCode).to.equal(401);
      expect(outcome.message).to.equal('Failed to retrieve trusted ticket.');
    });

    it('should throw error when axios post fails', async () => {
      axiosPostStub = sinon.stub(axios, 'post');
      axiosPostStub.rejects(new Error('axios post failed'));

      const outcome = await expect(
        managementService.retrieveTrustedTicketAsync(),
      ).to.be.rejectedWith(Error);
      expect(outcome.message).to.equal('axios post failed');
    });
  });
});
