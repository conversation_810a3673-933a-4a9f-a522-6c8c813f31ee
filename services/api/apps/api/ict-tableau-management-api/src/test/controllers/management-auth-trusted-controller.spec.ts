import sinon from 'sinon';
import request from 'supertest';
import {expect} from 'chai';
import {Container, appSetup} from 'ict-api-foundations';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {ManagementService} from '../../services/management-service.ts';

describe('ManagementAuthTrustedController', () => {
  const url: string = '/management/auth/trusted';
  const app = appSetup(RegisterRoutes);

  describe('ManagementAuthTrustedController Error scenarios', () => {
    it('should error when the service throws an error', async () => {
      const managementServiceStub = sinon.createStubInstance(ManagementService);
      Container.set(ManagementService, managementServiceStub);
      managementServiceStub.retrieveTrustedTicketAsync.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app).get(url);

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
    });
  });

  describe('ManagementAuthTrustedController success scenarios', () => {
    beforeEach(() => {
      const managementServiceStub = sinon.createStubInstance(ManagementService);
      Container.set(ManagementService, managementServiceStub);
      managementServiceStub.retrieveTrustedTicketAsync.resolves({
        userId: 'testUserId',
        ticket: 'testTableauTrustedTicket',
      });
    });

    it('should call the service and return the data', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal({
        userId: 'testUserId',
        ticket: 'testTableauTrustedTicket',
      });
    });
  });
});
