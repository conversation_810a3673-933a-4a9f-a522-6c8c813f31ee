import {
  ContextService,
  DiService,
  EnvironmentService,
  IctError,
} from 'ict-api-foundations';
import axios from 'axios';
import {AuthTicket} from '../defs/auth-ticket.ts';

@DiService()
export class ManagementService {
  constructor(
    private contextService: ContextService,
    private envService: EnvironmentService
  ) {}

  public async retrieveTrustedTicketAsync(): Promise<AuthTicket> {
    const trustedURL = `http://${this.envService.tableau.nodeIp}/trusted`;
    const formData = new FormData();
    formData.append('username', this.contextService.userId);
    const tableauSite = this.contextService.organization.metadata.tableauSite;
    if (tableauSite) {
      formData.append('target_site', tableauSite);
    }

    const response = await axios.post<string>(trustedURL, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    if (response.data && !response.data.trim().startsWith('-1')) {
      return {
        userId: this.contextService.userId,
        ticket: response.data,
      };
    }
    throw IctError.unauthorized('Failed to retrieve trusted ticket.');
  }
}
