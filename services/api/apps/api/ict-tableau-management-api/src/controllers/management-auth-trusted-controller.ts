import {Container} from 'ict-api-foundations';
import {Controller, Route, SuccessResponse, Get, OperationId, Tags} from 'tsoa';
import {ManagementService} from '../services/management-service.ts';
import {AuthTicket} from '../defs/auth-ticket.ts';

@Route('management')
export class ManagementAuthTrustedController extends Controller {
  @SuccessResponse('200')
  @Get('/auth/trusted')
  @OperationId('GetTrustedTicket')
  @Tags('tableau-management')
  public async getTrustedTicket(): Promise<AuthTicket> {
    const managementService = Container.get(ManagementService);
    return await managementService.retrieveTrustedTicketAsync();
  }
}
