import express from 'express';
import 'reflect-metadata';
import {
  ApiMiddleware,
  AuthMiddleware,
  Container,
  ContextMiddleware,
  CorsMiddleware,
  RequestLoggerMiddleware,
  SecurityMiddleware,
  WinstonLogger,
} from 'ict-api-foundations';

import {RegisterRoutes} from '../build/routes.ts';

const app = express();

const requestMiddleware = Container.get(RequestLoggerMiddleware);
const authMiddlware = Container.get(AuthMiddleware);
const corsMiddleware = Container.get(CorsMiddleware);
const securityMiddleware = Container.get(SecurityMiddleware);
const contextMiddleware = Container.get(ContextMiddleware);

// setup middlewares
app.use(
  express.urlencoded({
    extended: true,
  }),
);
app.use(express.json());

app.use(securityMiddleware.use);
app.use(corsMiddleware.use);
app.use(authMiddlware.use);
app.use(contextMiddleware.use);
app.use(requestMiddleware.use);

RegisterRoutes(app);

ApiMiddleware.applyErrorMiddlewares(app);
const logger = Container.get(WinstonLogger);

const port = 8080;
app.listen(port, () =>
  logger.info(`Tableau Management API listening on local port ${port}.`),
);
