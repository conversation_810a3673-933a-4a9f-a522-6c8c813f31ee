{"name": "ict-tableau-management-api", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/ict-tableau-management-api/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "ict-tableau-management-api:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "ict-tableau-management-api:build:development"}, "production": {"buildTarget": "ict-tableau-management-api:build:production"}}}}}