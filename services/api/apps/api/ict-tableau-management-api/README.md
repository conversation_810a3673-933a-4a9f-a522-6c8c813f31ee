# ICT Tableau Management API

## Overview

The ICT Tableau Management API provides management capabilities for Tableau integration within the Control Tower platform. This service handles authentication, configuration, and management of Tableau resources.

## Purpose

This API serves as the logic layer for handling business logic for Tableau management within Control Tower, providing secure and efficient integration with Tableau services.

## Key Features

- **Tableau Authentication**: Secure authentication with Tableau services
- **Trusted Authentication**: Support for trusted authentication flows
- **Resource Management**: Manage Tableau resources and configurations
- **Integration Support**: Provide integration points for other services

## API Endpoints

### Health Check

- `GET /health/basic` - Basic service health check

### Authentication

- `POST /management/auth/trusted` - Trusted authentication endpoint
- `GET /management/auth/status` - Authentication status check

### Resource Management

- `GET /management/resources` - List available Tableau resources
- `POST /management/resources` - Create new Tableau resource
- `PUT /management/resources/{id}` - Update Tableau resource
- `DELETE /management/resources/{id}` - Delete Tableau resource

## Architecture

The service follows the standard Control Tower API architecture:

```bash
src/
├── controllers/     # TSOA controllers with route definitions
│   └── management-auth-trusted-controller.ts
├── services/        # Business logic services
├── stores/          # Data access layer
├── defs/           # TypeScript type definitions
├── test/           # Unit tests
└── index.ts        # Service entry point
```

## Dependencies

- **Database**: PostgreSQL for storing Tableau configurations
- **Redis**: Caching for authentication tokens and configurations
- **Tableau Server**: External Tableau server integration
- **Auth0**: Authentication provider integration

## Environment Variables

- `AUTH_AUDIENCE`: Authentication audience for JWT validation
- `AUTH_DOMAIN`: Auth0 domain for authentication
- `TABLEAU_SERVER_URL`: Tableau server URL
- `TABLEAU_SITE_ID`: Tableau site identifier
- `TABLEAU_USERNAME`: Tableau service account username
- `TABLEAU_PASSWORD`: Tableau service account password

## Development

### Prerequisites

- Node.js >= 22.0.0
- Yarn package manager
- PostgreSQL database
- Redis instance
- Tableau server access

### Local Development

```bash
# Install dependencies
yarn install

# Start development server
yarn watch

# Run tests
yarn test

# Build for production
yarn build
```

### Testing

```bash
# Run all tests
yarn test

# Run tests in watch mode
yarn test:watch

# Run linting
yarn lint
```

## Deployment

The service is containerized using Docker and can be deployed to Google Cloud Run or other container platforms.

### Docker Build

```bash
docker build -t ict-tableau-management-api .
```

### Environment Configuration

Ensure all required environment variables are configured for the deployment environment.

## Monitoring

- **Health Checks**: Available at `/health/basic`
- **Metrics**: Service metrics exported for monitoring
- **Logging**: Structured JSON logging with correlation IDs

## Use Cases

### Business Intelligence

- Tableau dashboard integration
- Data visualization support
- Report generation and distribution

### Analytics Integration

- Real-time data feeds to Tableau
- Automated report generation
- Data pipeline integration

### User Management

- Tableau user provisioning
- Access control management
- License management

## Security

### Authentication

- JWT-based authentication
- Trusted authentication with Tableau
- Secure credential management

### Authorization

- Role-based access control
- Resource-level permissions
- Audit logging

## Related Services

- **ict-tableau-proxy**: Tableau proxy services for data access
- **ict-dataexplorer-api**: Data exploration and analytics
- **ict-admin-api**: Administrative functions and user management
- **ict-config-api**: Configuration management for Tableau settings

## Integration Patterns

### Trusted Authentication

The service supports trusted authentication flows for secure integration with Tableau services.

### Resource Management

Provides APIs for managing Tableau resources including workbooks, data sources, and users.

### Configuration Management

Supports dynamic configuration updates for Tableau integration settings.
