import sinon from 'sinon';
import request from 'supertest';
import {Container, IctError, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
import {OrderService} from '../../services/order-service.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {OrdersPickCycleCountController} from '../../controllers/orders-pick-cycle-count-controller.ts';

describe('OrdersPickCycleCountController', () => {
  const url: string = '/orders/pick/cycle-count';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Order Service. */
  let orderServiceStub: sinon.SinonStubbedInstance<OrderService>;

  const start_date = new Date('2023-01-01T00:00:00.000Z');
  const end_date = new Date('2023-01-02T00:00:00.000Z');

  afterEach(() => {
    sinon.restore();
  });

  describe('Orders Pick Cycle Count Controller Error scenarios', () => {
    it('should validate that start_date and end_date params are passed', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details.start_date.message).to.equal(
        "'start_date' is required"
      );
      expect(response.body.details.end_date.message).to.equal(
        "'end_date' is required"
      );
    });

    it('should validate that start_date and end_date params are valid ISO8601 dates', async () => {
      const response = await request(app).get(url).query({
        start_date: '20-01-01',
        end_date: '01/01/2020 00:00:00',
      });

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details.start_date.message).to.equal(
        'invalid ISO 8601 datetime format, i.e. YYYY-MM-DDTHH:mm:ss'
      );
      expect(response.body.details.end_date.message).to.equal(
        'invalid ISO 8601 datetime format, i.e. YYYY-MM-DDTHH:mm:ss'
      );
    });

    it('should validate that start_date comes before end_date', async () => {
      const response = await request(app).get(url).query({
        start_date: '2023-01-02T00:00:00',
        end_date: '2023-01-01T00:00:00',
      });

      expect(response.status).to.equal(400);
      expect(response.body.status).to.equal(400);
      expect(response.body.title).to.equal('Bad Request');
    });

    it('should error when the service throws an error', async () => {
      orderServiceStub = sinon.createStubInstance(OrderService);
      Container.set(OrderService, orderServiceStub);
      orderServiceStub.getOrdersPickCycleCount.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app).get(url).query({
        start_date,
        end_date,
      });

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWith(
        orderServiceStub.getOrdersPickCycleCount,
        start_date,
        end_date
      );
    });

    it('should throw a No Content IctError if no data was found', async () => {
      orderServiceStub = sinon.createStubInstance(OrderService);
      Container.set(OrderService, orderServiceStub);
      orderServiceStub.getOrdersPickCycleCount.resolves(undefined);

      const response = await request(app).get(url).query({
        start_date,
        end_date,
      });

      expect(response.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('Orders Pick Cycle Count Controller success scenarios', () => {
    beforeEach(() => {
      orderServiceStub = sinon.createStubInstance(OrderService);
      Container.set(OrderService, orderServiceStub);
      orderServiceStub.getOrdersPickCycleCount.resolves(
        OrdersPickCycleCountController.exampleData
      );
    });

    it('should call the service and return the data', async () => {
      const response = await request(app).get(url).query({
        start_date,
        end_date,
      });

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        OrdersPickCycleCountController.exampleResponse
      );

      sinon.assert.calledWith(
        orderServiceStub.getOrdersPickCycleCount,
        start_date,
        end_date
      );
    });
  });
});
