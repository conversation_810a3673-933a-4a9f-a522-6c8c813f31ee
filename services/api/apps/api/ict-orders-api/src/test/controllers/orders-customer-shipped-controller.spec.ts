import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
import {OrderService} from '../../services/order-service.ts';

import {RegisterRoutes} from '../../../build/routes.ts';
import {OrdersShippedController} from '../../controllers/orders-customer-shipped-controller.ts';

describe('OrdersShippedController', () => {
  const url: string = '/orders/customer/shipped';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Equipment Service. */
  let orderServiceStub: sinon.SinonStubbedInstance<OrderService>;

  const start_date = '2023-01-01T00:00:00.000Z';
  const end_date = '2023-01-02T00:00:00.000Z';

  afterEach(() => {
    sinon.restore();
  });

  describe('Orders Shipped Controller Error scenarios', () => {
    it('should validate that start_date and end_date params are passed', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details.start_date.message).to.equal(
        "'start_date' is required",
      );
      expect(response.body.details.end_date.message).to.equal(
        "'end_date' is required",
      );
    });

    it('should validate that start_date and end_date params are valid ISO8601 dates', async () => {
      const response = await request(app).get(url).query({
        start_date: '20-01-01',
        end_date: '01/01/2020 00:00:00',
      });

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details.start_date.message).to.equal(
        'invalid ISO 8601 datetime format, i.e. YYYY-MM-DDTHH:mm:ss',
      );
      expect(response.body.details.end_date.message).to.equal(
        'invalid ISO 8601 datetime format, i.e. YYYY-MM-DDTHH:mm:ss',
      );
    });

    it('should validate that start_date comes before end_date', async () => {
      const response = await request(app).get(url).query({
        start_date: '2023-01-02T00:00:00',
        end_date: '2023-01-01T00:00:00',
      });

      expect(response.status).to.equal(400);
      expect(response.body.status).to.equal(400);
      expect(response.body.title).to.equal('Bad Request');
    });

    it('should error when the service throws an error', async () => {
      orderServiceStub = sinon.createStubInstance(OrderService);
      Container.set(OrderService, orderServiceStub);
      orderServiceStub.getOrdersShipped.rejects(
        new Error('Something bad happened'),
      );

      const response = await request(app).get(url).query({
        start_date,
        end_date,
      });

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWith(
        orderServiceStub.getOrdersShipped,
        start_date,
        end_date,
      );
    });
  });
  describe('Order Projected Fulfillment Controller success scenarios', () => {
    beforeEach(() => {
      orderServiceStub = sinon.createStubInstance(OrderService);
      Container.set(OrderService, orderServiceStub);
      orderServiceStub.getOrdersShipped.resolves(
        OrdersShippedController.exampleData,
      );
    });

    it('should call the service and return the data', async () => {
      const response = await request(app).get(url).query({
        start_date,
        end_date,
      });

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        OrdersShippedController.exampleResponse,
      );

      sinon.assert.calledWith(
        orderServiceStub.getOrdersShipped,
        start_date,
        end_date,
      );
    });
  });
});
