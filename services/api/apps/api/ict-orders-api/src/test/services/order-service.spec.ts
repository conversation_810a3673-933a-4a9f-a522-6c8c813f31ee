import {expect} from 'chai';
import sinon from 'sinon';
import {BigQueryTimestamp} from '@google-cloud/bigquery';
import {
  ChartSeriesData,
  WMSCustomerOrderDetailStatus,
} from 'ict-api-foundations';
import {OrderService} from '../../services/order-service.ts';
import {OrderStore} from '../../stores/order-store.ts';
import {
  CustomerOrderProgress,
  OrderProgress,
} from '../../defs/order-progress-def.ts';
import {OrderShipped} from '../../defs/order-lines-shipped-def.ts';
import {OrderCycleTime} from '../../defs/order-cycle-time-def.ts';
import {OrderThroughputData} from '../../defs/order-throughput-def.ts';
import {EstimatedCompletionQueryResult} from '../../defs/estimated-completion-def.ts';
import {
  OrderCycleTimeChartData,
  OrderCycleTimeChartQueryResponse,
} from '../../defs/order-cycle-time-chart-def.ts';
import {
  OrderLineProgressChartQueryData,
  OrderLineProgressSeriesData,
} from '../../defs/order-line-progress-chart-def.ts';
import {UnitsRemainingQueryResponse} from '../../defs/order-remaining-def.ts';
import {OrdersOutstandingData} from '../../defs/orders-outstanding-def.ts';
import {
  OrderPerformanceFulfillmentData,
  OrderPerformanceFulfillmentQueryResponse,
} from '../../defs/order-performance-fulfillment-def.ts';
import {
  OrderProgressSeriesData,
  OrderProgressSeriesQueryData,
} from '../../defs/order-progress-chart-def.ts';
import {OrderLineProgress} from '../../defs/order-line-progress-def.ts';
import {CustomerOrderThroughputData} from '../../defs/customer-order-throughput-def.ts';
import {CustomerOrderEstimatedCompletion} from '../../defs/order-estimated-completion-def.ts';
import {
  CustomerOrderLineThroughputSeriesData,
  CustomerOrderLineThroughputSeriesQueryResponse,
} from '../../defs/order-customer-line-throughput-series-def.ts';
import {
  FacilityOrderLineThroughputSeriesResponse,
  FacilityOrderLineThroughputSeriesQueryResponse,
} from '../../defs/facility-order-line-throughput-series-def.ts';
import {OrderLineThroughputData} from '../../defs/order-line-throughput-def.ts';
import {OrderThroughputChartQueryData} from '../../defs/order-throughput-chart-def.ts';
import {OrderCustomerLineThroughputData} from '../../defs/order-customer-line-throughput-def.ts';
import {OrderPickLineThroughputSeriesQueryData} from '../../defs/order-line-throughput-series-def.ts';
import {OrdersPickCycleCountData} from '../../defs/orders-pick-cycle-count-def.ts';
import {OrdersShipped} from '../../defs/order-shipped-def.ts';
import {ProjectedOrderFulfillment} from '../../defs/orders-projected-fulfillment-def.ts';

describe('OrderService', () => {
  // Order Store stub, intercepts calls made by the order service
  let orderStoreStub: sinon.SinonStubbedInstance<OrderStore>;

  // Order service to use in tests
  let orderService: OrderService;

  beforeEach(() => {
    // Setup order store stub
    orderStoreStub = sinon.createStubInstance(OrderStore);
    orderService = new OrderService(orderStoreStub);
  });

  describe('getOrderProgress', () => {
    it('should get total and current orders', async () => {
      const startDate = '2021-11-30T00:00:01';
      const endDate = '2021-11-30T23:59:59';
      const mockQueryResponse: OrderProgress = {
        orderProgressPercentage: 24,
      };
      orderStoreStub.getOrderProgress.resolves(mockQueryResponse);

      const result = await orderService.getOrderProgress(startDate, endDate);

      expect(result).to.deep.equal(mockQueryResponse);
    });
  });

  describe('getOrdersFacilityProgress', () => {
    it('should get total and current orders', async () => {
      const startDate = '2021-11-30T00:00:01';
      const endDate = '2021-11-30T23:59:59';
      const mockQueryResponse: OrderProgress = {
        orderProgressPercentage: 24,
      };
      orderStoreStub.getOrdersFacilityProgress.resolves(mockQueryResponse);

      const result = await orderService.getOrdersFacilityProgress(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockQueryResponse);
    });
  });

  describe('getCustomerOrderLineThroughputSeries', () => {
    let startDate: Date;
    let endDate: Date;

    beforeEach(() => {
      startDate = new Date('2021-11-30T00:00:01');
      endDate = new Date('2021-11-30T23:59:59');
    });

    it('should get order estimated completion for the timeframe', async () => {
      const mockStoreResponse: CustomerOrderLineThroughputSeriesQueryResponse[] =
        [
          {
            event_interval: new BigQueryTimestamp('2021-11-30T00:00:00.000Z'),
            completed_order_lines_hourly_rate: 15,
          },
          {
            event_interval: new BigQueryTimestamp('2021-11-30T00:15:00.000Z'),
            completed_order_lines_hourly_rate: 15,
          },
        ];
      orderStoreStub.getCustomerOrderLineThroughputSeries.resolves(
        mockStoreResponse,
      );

      const result = await orderService.getCustomerOrderLineThroughputSeries(
        startDate,
        endDate,
      );

      const expected: CustomerOrderLineThroughputSeriesData = {
        throughput: [
          {
            name: '2021-11-30T00:00:00.000Z',
            value: 15,
          },
          {
            name: '2021-11-30T00:15:00.000Z',
            value: 15,
          },
        ],
      };
      expect(result).to.deep.equal(expected);
    });
  });

  describe('getFacilityOrderLineThroughputSeries', () => {
    let startDate: Date;
    let endDate: Date;

    beforeEach(() => {
      startDate = new Date('2021-11-30T00:00:01');
      endDate = new Date('2021-11-30T23:59:59');
    });

    it('should get facility order line throughput series for the timeframe', async () => {
      const mockStoreResponse: FacilityOrderLineThroughputSeriesQueryResponse[] =
        [
          {
            event_interval: new BigQueryTimestamp('2021-11-30T00:00:00.000Z'),
            hourly_throughput_rate: 15,
          },
          {
            event_interval: new BigQueryTimestamp('2021-11-30T00:15:00.000Z'),
            hourly_throughput_rate: 15,
          },
        ];
      orderStoreStub.getFacilityOrderLineThroughputSeries.resolves(
        mockStoreResponse,
      );

      const result = await orderService.getFacilityOrderLineThroughputSeries(
        startDate,
        endDate,
      );

      const expected: FacilityOrderLineThroughputSeriesResponse = {
        throughput: [
          {
            name: '2021-11-30T00:00:00.000Z',
            value: 15,
          },
          {
            name: '2021-11-30T00:15:00.000Z',
            value: 15,
          },
        ],
      };
      expect(result).to.deep.equal(expected);
    });
  });

  describe('getOrderThroughputChartData', () => {
    let startDate: Date;
    let endDate: Date;

    beforeEach(() => {
      startDate = new Date('2021-10-20');
      endDate = new Date('2021-10-21');
    });

    it('should get order throughput chart data', async () => {
      const mockStoreResponse: OrderThroughputChartQueryData[] = [
        {
          completed_orders: 15,
          event_hour: new BigQueryTimestamp('2021-10-20'),
        },
        {
          completed_orders: 15,
          event_hour: new BigQueryTimestamp('2021-10-21'),
        },
      ];
      orderStoreStub.getOrderThroughputChartData.resolves(mockStoreResponse);

      const result = await orderService.getOrderThroughputChartData(
        startDate.toISOString(),
        endDate.toISOString(),
      );

      const expected = [
        {
          name: '2021-10-20T00:00:00.000Z',
          value: 15,
        },
        {
          name: '2021-10-21T00:00:00.000Z',
          value: 15,
        },
      ];

      expect(result).to.deep.equal(expected);
    });
  });

  describe('getCustomerOrderProgress', () => {
    it('should get total and current orders', async () => {
      const startDate = '2021-11-30T00:00:01';
      const endDate = '2021-11-30T23:59:59';
      const mockQueryResponse: CustomerOrderProgress = {
        orderProgressPercentage: 24,
      };
      orderStoreStub.getCustomerOrderProgress.resolves(mockQueryResponse);

      const result = await orderService.getCustomerOrderProgress(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockQueryResponse);
    });
  });

  describe('getOrdersEstimatedCompletion', () => {
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2021-11-27T00:23:50';
      endDate = '2021-11-28T18:22:53.';
    });

    it('should get estimated completion time', async () => {
      sinon.useFakeTimers(new Date('2023-04-22T10:00:00.000Z').getTime());

      const mockStoreResponse: EstimatedCompletionQueryResult = {
        estimatedHoursRemaining: 5,
        nonCompletedCount: 5,
        ratePerHour: 10,
      };

      orderStoreStub.getOrdersEstimatedCompletion.resolves(mockStoreResponse);

      const result = await orderService.getOrdersEstimatedCompletion(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal({
        completionTime: '2023-04-22T15:00:00.000Z',
      });
    });
  });

  describe('getOrderCycleTime', () => {
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2021-11-30T00:00:01';
      endDate = '2021-11-30T23:59:59';
    });

    it('should get order cycle time for the timeframe', async () => {
      const mockStoreResponse: OrderCycleTime = {
        orderCycleTimeMinutes: 209,
      };
      orderStoreStub.getOrderCycleTime.resolves(mockStoreResponse);

      const result = await orderService.getOrderCycleTime(startDate, endDate);

      expect(result).to.deep.equal(mockStoreResponse);
    });
  });

  describe('getCustomerOrderCycleTime', () => {
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2021-11-30T00:00:01';
      endDate = '2021-11-30T23:59:59';
    });

    it('should get order cycle time for the timeframe', async () => {
      const mockStoreResponse: OrderCycleTime = {
        orderCycleTimeMinutes: 209,
      };
      orderStoreStub.getCustomerOrderCycleTime.resolves(mockStoreResponse);

      const result = await orderService.getCustomerOrderCycleTime(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockStoreResponse);
    });
  });

  describe('getFacilityOrderCycleTime', () => {
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2025-07-17T00:00:01';
      endDate = '2025-07-17T23:59:59';
    });

    it('should get order cycle time for the timeframe', async () => {
      const mockStoreResponse: OrderCycleTime = {
        orderCycleTimeMinutes: 209,
      };
      orderStoreStub.getFacilityOrderCycleTime.resolves(mockStoreResponse);

      const result = await orderService.getFacilityOrderCycleTime(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockStoreResponse);
    });
  });

  describe('getCustomerEstimatedCompletion', () => {
    let startDate: Date;
    let endDate: Date;

    beforeEach(() => {
      startDate = new Date('2021-11-30T00:00:01');
      endDate = new Date('2021-11-30T23:59:59');
    });

    it('should get order estimated completion for the timeframe', async () => {
      const mockStoreResponse: CustomerOrderEstimatedCompletion = 209;
      orderStoreStub.getCustomerOrderEstimatedCompletion.resolves(
        mockStoreResponse,
      );

      const result = await orderService.getCustomerOrdersEstimatedCompletion(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal({
        estimatedCompletionMinutes: mockStoreResponse,
      });
    });
  });

  describe('getFacilityOrdersEstimatedCompletion', () => {
    let startDate: Date;
    let endDate: Date;

    beforeEach(() => {
      startDate = new Date('2021-11-30T00:00:01');
      endDate = new Date('2021-11-30T23:59:59');
    });

    it('should get order estimated completion for the timeframe', async () => {
      const mockStoreResponse: CustomerOrderEstimatedCompletion = 209;
      orderStoreStub.getFacilityOrdersEstimatedCompletion.resolves(
        mockStoreResponse,
      );

      const result = await orderService.getFacilityOrdersEstimatedCompletion(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal({
        estimatedCompletionMinutes: mockStoreResponse,
      });
    });
  });

  describe('getOrderShipped', () => {
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2021-11-27T10:26:54.813Z';
      endDate = '2021-11-28T10:26:54.813Z';
    });

    it('should get orders shipped for the timeframe', async () => {
      const mockStoreResponse: OrderShipped = {
        total: 1500,
        current: 345,
        past: 300,
      };
      orderStoreStub.getPickOrdersShipped.resolves(mockStoreResponse);

      const result = await orderService.getPickOrdersShipped(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockStoreResponse);
    });
  });

  describe('getOrdersShipped', () => {
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2025-06-03T00:00:00.000Z';
      endDate = '2025-06-03T23:59:00.000Z';
    });

    it('should get orders shipped for the timeframe', async () => {
      const mockStoreResponse: OrdersShipped = {
        total: 1500,
        shipped: 40,
      };
      orderStoreStub.getOrdersFacilityShipped.resolves(mockStoreResponse);

      const result = await orderService.getOrdersFacilityShipped(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockStoreResponse);
    });
  });

  describe('getOrderCycleTimeChartData', () => {
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2023-03-03T00:00:01';
      endDate = '2023-03-03T23:59:59';
    });

    const mockOrderCycleTimeChartStoreResponse: OrderCycleTimeChartQueryResponse[] =
      [
        {
          event_hour: new BigQueryTimestamp('2023-03-03T00:00:00'),
          average_cycletime_seconds: 203,
        },
        {
          event_hour: new BigQueryTimestamp('2023-03-03T01:00:00'),
          average_cycletime_seconds: 199,
        },
      ];

    it('should return the correct data', async () => {
      orderStoreStub.getOrderCycleTimeChartData.resolves(
        mockOrderCycleTimeChartStoreResponse,
      );

      const result = await orderService.getOrderCycleTimeChartData(
        startDate,
        endDate,
      );
      const expected: OrderCycleTimeChartData = {
        orderCycleTimeChart: mockOrderCycleTimeChartStoreResponse.map(
          datum => ({
            name: datum.event_hour.value,
            value: datum.average_cycletime_seconds,
          }),
        ),
      };
      expect(result).to.deep.equal(expected);
    });
  });

  describe('getOrderThroughputRateAsync', () => {
    const mockOrderThroughputResponse: OrderThroughputData = {
      throughputRateLinesPerHour: 45,
    };
    it('should get order throughput rate data', async () => {
      const startDate = '2023-07-01T00:00:01';
      const endDate = '2023-07-10T23:59:59';

      orderStoreStub.getOrderThroughputRate.resolves(
        mockOrderThroughputResponse,
      );

      const result = await orderService.getOrderThroughputRateAsync(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockOrderThroughputResponse);
    });
  });

  describe('geCustomerOrderThroughputRate', () => {
    const mockOrderThroughputResponse: CustomerOrderThroughputData = {
      throughputRateOrdersPerHour: 45,
    };

    it('should get order throughput rate data', async () => {
      const startDate = '2023-07-01T00:00:01';
      const endDate = '2023-07-10T23:59:59';

      orderStoreStub.getCustomerOrderThroughputRate.resolves(
        mockOrderThroughputResponse,
      );

      const result = await orderService.getCustomerOrderThroughputRate(
        new Date(startDate),
        new Date(endDate),
      );

      expect(result).to.deep.equal(mockOrderThroughputResponse);
    });

    it('should use the current datetime when endDate is in the future', async () => {
      const startDate = '2023-07-01T00:00:01';
      const currentDate = new Date();
      const endDate = new Date().setDate(currentDate.getDate() + 1);

      orderStoreStub.getCustomerOrderThroughputRate.resolves(
        mockOrderThroughputResponse,
      );

      const result = await orderService.getCustomerOrderThroughputRate(
        new Date(startDate),
        new Date(endDate),
      );

      expect(result).to.deep.equal(mockOrderThroughputResponse);
      expect(
        orderStoreStub.getCustomerOrderCycleTime.calledWith(
          startDate,
          currentDate.toISOString(),
        ),
      );
    });
  });

  describe('getFacilityOrderThroughputRate', () => {
    const mockOrderThroughputResponse: CustomerOrderThroughputData = {
      throughputRateOrdersPerHour: 45,
    };

    it('should get order throughput rate data', async () => {
      const startDate = '2023-07-01T00:00:01';
      const endDate = '2023-07-10T23:59:59';

      orderStoreStub.getFacilityOrderThroughputRate.resolves(
        mockOrderThroughputResponse,
      );

      const result = await orderService.getFacilityOrderThroughputRate(
        new Date(startDate),
        new Date(endDate),
      );

      expect(result).to.deep.equal(mockOrderThroughputResponse);
    });

    it('should use the current datetime when endDate is in the future', async () => {
      const startDate = '2023-07-01T00:00:01';
      const currentDate = new Date();
      const endDate = new Date().setDate(currentDate.getDate() + 1);

      orderStoreStub.getFacilityOrderThroughputRate.resolves(
        mockOrderThroughputResponse,
      );

      const result = await orderService.getFacilityOrderThroughputRate(
        new Date(startDate),
        new Date(endDate),
      );

      expect(result).to.deep.equal(mockOrderThroughputResponse);
      expect(
        orderStoreStub.getFacilityOrderThroughputRate.calledWith(
          startDate,
          currentDate.toISOString(),
        ),
      );
    });
  });

  describe('getOrderLineProgressChartData', () => {
    let startDate: string;
    let endDate: string;
    let mockOrderLineProgressChartResponse: OrderLineProgressChartQueryData[];

    beforeEach(() => {
      startDate = '2023-05-24T04:00:00.000Z';
      endDate = '2023-05-24T06:00:00.000Z';
      mockOrderLineProgressChartResponse = [
        {
          hour: '2023-05-24T04:00:00.000Z',
          quantityPicked: 4427,
          runningTotal: 148287,
          quantityTarget: 157023,
        },
        {
          hour: '2023-05-24T05:00:00.000Z',
          quantityPicked: 5282,
          runningTotal: 153569,
          quantityTarget: 157023,
        },
      ];
    });

    it('should return the correct data', async () => {
      orderStoreStub.getOrderLineProgressChartData.resolves(
        mockOrderLineProgressChartResponse,
      );

      const result = await orderService.getOrderLineProgressChartData(
        startDate,
        endDate,
      );

      const expected: OrderLineProgressSeriesData = {
        progress: mockOrderLineProgressChartResponse.map(datum => ({
          name: datum.hour,
          value: datum.runningTotal,
        })),
        trend: [
          {
            name: '2023-05-24T05:00:00.000Z',
            value: 153569,
          },
          {
            name: '2023-05-24T06:00:00.000Z',
            value: 230353,
          },
        ],
      };
      expect(result).to.deep.equal(expected);
    });
  });

  describe('getOrderLineProgressChartDataAsync', () => {
    let startDate: string;
    let endDate: string;
    let fakeStoreResponse: UnitsRemainingQueryResponse;

    beforeEach(() => {
      startDate = '2023-05-24T00:00:00.000Z';
      endDate = '2023-05-25T00:00:00.000Z';
      fakeStoreResponse = {
        unitsRemaining: 1337,
      };
      orderStoreStub.getUnitsRemaining.resolves(fakeStoreResponse);
    });

    it('should return the correct data', async () => {
      const result = await orderService.getUnitsRemaining(startDate, endDate);

      const expected: UnitsRemainingQueryResponse = fakeStoreResponse;
      expect(result).to.deep.equal(expected);
    });
  });

  describe('getOrderPerformanceFulfillment', () => {
    let startDate: string;
    let endDate: string;
    let departmentFilter: string;
    let mockStoreResponse: OrderPerformanceFulfillmentQueryResponse;

    beforeEach(() => {
      startDate = '2023-05-24T00:00:00.000Z';
      endDate = '2023-05-25T00:00:00.000Z';
      departmentFilter = 'All';
      mockStoreResponse = {
        orderFulfillment: 24,
      };
      orderStoreStub.getOrderPerformanceFulfillment.resolves(mockStoreResponse);
    });

    it('should return the correct data', async () => {
      const result = await orderService.getOrderPerformanceFulfillment(
        startDate,
        endDate,
        departmentFilter,
      );

      const expected: OrderPerformanceFulfillmentData = {
        orderFulfillment: 24,
      };
      expect(result).to.deep.equal(expected);
    });
  });

  describe('getOrdersFulfillmentOutstanding', () => {
    let startDate: string;
    let endDate: string;
    let fakeStoreResponse: OrdersOutstandingData;

    beforeEach(() => {
      startDate = '2023-05-24T00:00:00.000Z';
      endDate = '2023-05-25T00:00:00.000Z';
      fakeStoreResponse = {
        incompletedTotal: 28,
      };
      orderStoreStub.getOrderFulfillmentOutstanding.resolves(fakeStoreResponse);
    });

    it('should return the correct data', async () => {
      const result = await orderService.getOrdersFulfillmentOutstanding(
        startDate,
        endDate,
      );

      const expected: OrdersOutstandingData = fakeStoreResponse;
      expect(result).to.deep.equal(expected);
    });
  });

  describe('getOrdersFacilityOutstanding', () => {
    let startDate: string;
    let endDate: string;
    let fakeStoreResponse: OrdersOutstandingData;

    beforeEach(() => {
      startDate = '2023-05-24T00:00:00.000Z';
      endDate = '2023-05-25T00:00:00.000Z';
      fakeStoreResponse = {
        incompletedTotal: 28,
      };
      orderStoreStub.getOrderFacilityOutstanding.resolves(fakeStoreResponse);
    });

    it('should return the correct data', async () => {
      const result = await orderService.getOrdersFacilityOutstanding(
        startDate,
        endDate,
      );

      const expected: OrdersOutstandingData = fakeStoreResponse;
      expect(result).to.deep.equal(expected);
    });
  });

  describe('getOrderLineThroughput', () => {
    let startDate: string;
    let endDate: string;
    let fakeStoreResponse: OrderLineThroughputData;

    beforeEach(() => {
      startDate = '2023-05-24T00:00:00.000Z';
      endDate = '2023-05-25T00:00:00.000Z';
      fakeStoreResponse = {
        totalCompletedOrderLines: 1000,
      };
      orderStoreStub.getOrderLineThroughput.resolves(fakeStoreResponse);
    });

    it('should return the correct data', async () => {
      const result = await orderService.getOrderLineThroughput(
        startDate,
        endDate,
      );

      const expected: OrderLineThroughputData = fakeStoreResponse;
      expect(result).to.deep.equal(expected);
    });
  });

  describe('getOrderProgressSeries', () => {
    let startDate: Date;
    let endDate: Date;
    let fakeStoreResponse: OrderProgressSeriesQueryData[];

    beforeEach(() => {
      startDate = new Date('2023-05-24T00:00:00.000Z');
      endDate = new Date('2023-05-25T00:00:00.000Z');
      fakeStoreResponse = [
        {
          hour: '2023-05-24T04:00:00.000Z',
          runningCompletedTotal: 42,
          totalOrders: 2107,
        },

        {
          hour: '2023-05-24T05:00:00.000Z',
          runningCompletedTotal: 84,
          totalOrders: 4214,
        },
      ];
      orderStoreStub.getOrderProgressSeries.resolves(fakeStoreResponse);
    });

    it('should return the correct data', async () => {
      const result = await orderService.getOrderProgressSeries(
        startDate,
        endDate,
      );

      const expected: OrderProgressSeriesData = {
        progress: [
          {
            name: '2023-05-24T04:00:00.000Z',
            value: (42 / 2107) * 100,
          },
          {
            name: '2023-05-24T05:00:00.000Z',
            value: (84 / 4214) * 100,
          },
        ],
      };
      expect(result).to.deep.equal(expected);
    });
  });

  describe('getCustomerOrderProgressSeries', () => {
    let startDate: Date;
    let endDate: Date;
    let fakeStoreResponse: OrderProgressSeriesQueryData[];

    beforeEach(() => {
      startDate = new Date('2023-05-24T00:00:00.000Z');
      endDate = new Date('2023-05-25T00:00:00.000Z');
      fakeStoreResponse = [
        {
          hour: '2023-05-24T04:00:00.000Z',
          runningCompletedTotal: 42,
          totalOrders: 2107,
        },

        {
          hour: '2023-05-24T05:00:00.000Z',
          runningCompletedTotal: 84,
          totalOrders: 4214,
        },
      ];
      orderStoreStub.getCustomerOrderProgressSeries.resolves(fakeStoreResponse);
    });

    it('should return the correct data', async () => {
      const result = await orderService.getCustomerOrderProgressSeries(
        startDate,
        endDate,
      );

      const expected: OrderProgressSeriesData = {
        progress: [
          {
            name: '2023-05-24T04:00:00.000Z',
            value: (42 / 2107) * 100,
          },
          {
            name: '2023-05-24T05:00:00.000Z',
            value: (84 / 4214) * 100,
          },
        ],
      };
      expect(result).to.deep.equal(expected);
    });
  });

  describe('getCustomerOrderLineProgressSeries', () => {
    let startDate: Date;
    let endDate: Date;
    let fakeStoreResponse: OrderProgressSeriesQueryData[];

    beforeEach(() => {
      startDate = new Date('2023-05-24T00:00:00.000Z');
      endDate = new Date('2023-05-25T00:00:00.000Z');
      fakeStoreResponse = [
        {
          hour: '2023-05-24T04:00:00.000Z',
          runningCompletedTotal: 42,
          totalOrders: 2107,
        },

        {
          hour: '2023-05-24T05:00:00.000Z',
          runningCompletedTotal: 84,
          totalOrders: 4214,
        },
      ];
      orderStoreStub.getCustomerOrderLineProgressSeries.resolves(
        fakeStoreResponse,
      );
    });

    it('should return the correct data', async () => {
      const result = await orderService.getCustomerOrderLineProgressSeries(
        startDate,
        endDate,
      );

      const expected: OrderProgressSeriesData = {
        progress: [
          {
            name: '2023-05-24T04:00:00.000Z',
            value: (42 / 2107) * 100,
          },
          {
            name: '2023-05-24T05:00:00.000Z',
            value: (84 / 4214) * 100,
          },
        ],
      };
      expect(result).to.deep.equal(expected);
    });
  });

  describe('getFacilityOrderLineProgressSeries', () => {
    let startDate: Date;
    let endDate: Date;
    let fakeStoreResponse: OrderProgressSeriesQueryData[];

    beforeEach(() => {
      startDate = new Date('2025-07-16T00:00:00.000Z');
      endDate = new Date('2025-07-17T00:00:00.000Z');
      fakeStoreResponse = [
        {
          hour: '2025-07-16T04:00:00.000Z',
          runningCompletedTotal: 42,
          totalOrders: 2107,
        },

        {
          hour: '2025-07-16T05:00:00.000Z',
          runningCompletedTotal: 84,
          totalOrders: 4214,
        },
      ];
      orderStoreStub.getFacilityOrderLineProgressSeries.resolves(
        fakeStoreResponse,
      );
    });

    it('should return the correct data', async () => {
      const result = await orderService.getFacilityOrderLineProgressSeries(
        startDate,
        endDate,
      );

      const expected: OrderProgressSeriesData = {
        progress: [
          {
            name: '2025-07-16T04:00:00.000Z',
            value: (42 / 2107) * 100,
          },
          {
            name: '2025-07-16T05:00:00.000Z',
            value: (84 / 4214) * 100,
          },
        ],
      };
      expect(result).to.deep.equal(expected);
    });
  });

  describe('getCustomerOrderLineProgress', () => {
    it('should get total and current orders', async () => {
      const startDate = '2021-11-30T00:00:01';
      const endDate = '2021-11-30T23:59:59';
      const status = WMSCustomerOrderDetailStatus.Shipped;
      const mockStoreResponse: OrderLineProgress = {
        lineProgressPercent: 24,
      };
      orderStoreStub.getCustomerOrderLineProgress.resolves(mockStoreResponse);

      const result = await orderService.getCustomerOrderLineProgress(
        startDate,
        endDate,
        status,
      );

      expect(result).to.deep.equal(mockStoreResponse);
    });
  });

  describe('getFacilityOrderLineProgress', () => {
    it('should get total and current orders', async () => {
      const startDate = '2025-07-17T00:00:01';
      const endDate = '2025-07-17T23:59:59';
      const mockStoreResponse: OrderLineProgress = {
        lineProgressPercent: 24,
      };
      orderStoreStub.getFacilityOrderLineProgress.resolves(mockStoreResponse);

      const result = await orderService.getFacilityOrderLineProgress(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockStoreResponse);
    });
  });

  describe('getOrderCustomerLineThroughput', () => {
    let startDate: string;
    let endDate: string;
    let fakeStoreResponse: OrderCustomerLineThroughputData;

    beforeEach(() => {
      startDate = '2023-05-24T00:00:00.000Z';
      endDate = '2023-05-25T00:00:00.000Z';
      fakeStoreResponse = {
        throughputRateOrderLinesPerHour: 1000,
      };
      orderStoreStub.getOrderCustomerLineThroughput.resolves(fakeStoreResponse);
    });

    it('should return the correct data', async () => {
      const result = await orderService.getOrderCustomerLineThroughput(
        startDate,
        endDate,
      );

      const expected: OrderCustomerLineThroughputData = fakeStoreResponse;
      expect(result).to.deep.equal(expected);
    });
  });

  describe('getOrderLineThroughputSeries', () => {
    const startDate = '2021-11-30';
    const endDate = '2021-11-31';

    it('should get order line throughput series data for the timeframe', async () => {
      const mockStoreResponse: OrderPickLineThroughputSeriesQueryData[] = [
        {
          event_interval: new BigQueryTimestamp('2021-11-30'),
          hourly_throughput_rate: 15,
        },
        {
          event_interval: new BigQueryTimestamp('2021-11-31'),
          hourly_throughput_rate: 15,
        },
      ];
      orderStoreStub.getOrderPickLineThroughputSeries.resolves(
        mockStoreResponse,
      );

      const result = await orderService.getOrderPickLineThroughputSeries(
        startDate,
        endDate,
      );

      const expected: ChartSeriesData[] = [
        {
          name: '2021-11-30T00:00:00.000Z',
          value: 15,
        },
        {
          name: '2021-12-01T00:00:00.000Z',
          value: 15,
        },
      ];
      expect(result).to.deep.equal(expected);
    });
  });

  describe('getOrdersPickCycleCount', () => {
    const startDate = new Date('2021-11-30');
    const endDate = new Date('2021-11-31');

    it('should get pick order cycle time data for the timeframe', async () => {
      const mockStoreResponse: OrdersPickCycleCountData = {
        cycleCount: 200,
      };

      orderStoreStub.getOrdersPickCycleCount.resolves(mockStoreResponse);

      const result = await orderService.getOrdersPickCycleCount(
        startDate,
        endDate,
      );

      const expected: OrdersPickCycleCountData = {
        cycleCount: 200,
      };

      expect(result).to.deep.equal(expected);
    });
  });

  describe('getProjectedCustomerOrderFulfillment', () => {
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2021-11-27T10:26:54.813Z';
      endDate = '2021-11-28T10:26:54.813Z';
    });

    it('should get projected order fulfillment for the timeframe', async () => {
      const mockStoreResponse: ProjectedOrderFulfillment = {
        projectedOrderFulfillmentPercentage: 24,
      };
      orderStoreStub.getProjectedCustomerOrderFulfillment.resolves(
        mockStoreResponse,
      );

      const result =
        await orderService.getProjectedCustomerOrderFulfillmentPercentage(
          startDate,
          endDate,
        );

      expect(result).to.deep.equal(mockStoreResponse);
    });
  });

  describe('getProjectedFacilityOrderFulfillment', () => {
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2021-11-27T10:26:54.813Z';
      endDate = '2021-11-28T10:26:54.813Z';
    });

    it('should get projected facility order fulfillment for the timeframe', async () => {
      const mockStoreResponse: ProjectedOrderFulfillment = {
        projectedOrderFulfillmentPercentage: 24,
      };
      orderStoreStub.getProjectedFacilityOrderFulfillment.resolves(
        mockStoreResponse,
      );

      const result =
        await orderService.getProjectedFacilityOrderFulfillmentPercentage(
          startDate,
          endDate,
        );

      expect(result).to.deep.equal(mockStoreResponse);
    });
  });
});
