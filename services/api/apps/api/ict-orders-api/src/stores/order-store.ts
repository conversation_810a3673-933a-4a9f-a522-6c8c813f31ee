import {Query} from '@google-cloud/bigquery';
import {
  ConfigStore,
  ContextService,
  DatabaseProvider,
  DiService,
  IctError,
  WinstonLogger,
  WMSCustomerOrderDetailStatus,
  WMSCustomerOrderStatus,
  WMSCustomerOrderState,
  PickTaskEventCode,
} from 'ict-api-foundations';
import {EstimatedCompletionQueryResult} from '../defs/estimated-completion-def.ts';
import {CycletimeFacilityArea} from '../defs/order-areas-cycle-time-def.ts';
import {LineProgressFacilityArea} from '../defs/order-areas-lineprogress-def.ts';
import {ThroughputFacilityArea} from '../defs/order-areas-throughput-def.ts';
import {OrderCycleTimeChartQueryResponse} from '../defs/order-cycle-time-chart-def.ts';
import {
  OrderCycleTime,
  OrderCycleTimeSetting,
} from '../defs/order-cycle-time-def.ts';
import {OrderLineProgressChartQueryData} from '../defs/order-line-progress-chart-def.ts';
import {OrderLineProgress} from '../defs/order-line-progress-def.ts';
import {OrderPerformanceFulfillmentQueryResponse} from '../defs/order-performance-fulfillment-def.ts';
import {
  CustomerOrderProgress,
  OrderProgress,
} from '../defs/order-progress-def.ts';
import {UnitsRemainingQueryResponse} from '../defs/order-remaining-def.ts';
import {OrderShipped} from '../defs/order-lines-shipped-def.ts';
import {OrdersShipped} from '../defs/order-shipped-def.ts';
import {
  OrderCustomerThroughputChartQueryData,
  OrderThroughputChartQueryData,
} from '../defs/order-throughput-chart-def.ts';
import {OrderThroughputData} from '../defs/order-throughput-def.ts';
import {OrdersOutstandingData} from '../defs/orders-outstanding-def.ts';
import {ProjectedOrderFulfillment} from '../defs/orders-projected-fulfillment-def.ts';

import {OrderProgressSeriesQueryData} from '../defs/order-progress-chart-def.ts';
import {CustomerOrderThroughputData} from '../defs/customer-order-throughput-def.ts';
import {
  CustomerOrderEstimatedCompletion,
  FacilityOrderEventCodes,
} from '../defs/order-estimated-completion-def.ts';
import {CustomerOrderLineThroughputSeriesQueryResponse} from '../defs/order-customer-line-throughput-series-def.ts';
import {OrderLineThroughputData} from '../defs/order-line-throughput-def.ts';
import {PickOrderArea} from '../defs/pick-order-def.ts';
import {OrderPickLineThroughputSeriesQueryData} from '../defs/order-line-throughput-series-def.ts';
import {FacilityOrderLineThroughputSeriesQueryResponse} from '../defs/facility-order-line-throughput-series-def.ts';
import {OrderCustomerLineThroughputData} from '../defs/order-customer-line-throughput-def.ts';
import {OrdersPickCycleCountData} from '../defs/orders-pick-cycle-count-def.ts';

@DiService()
export class OrderStore {
  constructor(
    private context: ContextService,
    private logger: WinstonLogger,
    private configStore: ConfigStore,
  ) {}

  get dbProvider(): DatabaseProvider {
    return this.context.dbProvider;
  }

  async getOrderProgress(
    startDate: string,
    endDate: string,
  ): Promise<OrderProgress> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const eventCompleteCodes = await this.getOrderCompletedCodes();

    // Query EDP gold tables. This will eventually be the only route to take once all tenants are migrated.
    const goldPickTask = bigQueryDb.getEdpFullTablePath('gold_pick_task');

    const sql = `
        WITH
        orderCounts AS (
            SELECT
                COUNT(DISTINCT pick_task_code) AS totalCount,
                COUNT(DISTINCT IF(UPPER(event_code) IN UNNEST(@eventCompleteCodes), pick_task_code, NULL)) AS completedCount
            FROM \`${goldPickTask}\`
            WHERE event_timestamp_utc > @startDate
            AND event_timestamp_utc < @endDate
        )
        SELECT IFNULL((completedCount / NULLIF(totalCount, 0)) * 100, 100) AS orderProgressPercentage
        FROM orderCounts;
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
        eventCompleteCodes,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length || !rows[0].orderProgressPercentage) {
      throw IctError.noContent();
    }

    return {
      orderProgressPercentage: rows[0].orderProgressPercentage,
    } as OrderProgress;
  }

  async getOrdersFacilityProgress(
    startDate: string,
    endDate: string,
  ): Promise<CustomerOrderProgress> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const goldFacilityOrderTable = this.dbProvider.bigQuery.getEdpFullTablePath(
      'gold_facility_order',
    );

    const sql = `
        SELECT
          IFNULL(SAFE_DIVIDE(
            COUNT(DISTINCT IF(UPPER(event_code)='COMPLETE',facility_order_code, NULL)),
            COUNT(DISTINCT facility_order_code)
          ) * 100, 0) AS orderProgressPercentage
        FROM ${goldFacilityOrderTable}
        WHERE
          event_timestamp_utc > @startDate
          AND event_timestamp_utc < @endDate;
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length || !rows[0].orderProgressPercentage) {
      throw IctError.noContent();
    }

    // percentage is guaranteed to not be null by query
    return {
      orderProgressPercentage: rows[0].orderProgressPercentage,
    } as CustomerOrderProgress;
  }

  async getCustomerOrderProgress(
    startDate: string,
    endDate: string,
  ): Promise<CustomerOrderProgress> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const wmsCustomerOrderTable = this.dbProvider.bigQuery.getEdpFullTablePath(
      'gold_wms_customer_order',
    );

    const sql = `
      WITH 
        timeFiltered AS (
          SELECT
            DISTINCT order_code,
            sts_code,
          FROM ${wmsCustomerOrderTable}
          WHERE edit_date_timestamp_utc BETWEEN TIMESTAMP(@startDate) AND TIMESTAMP(@endDate)
        ),
        shippedOrders AS (
          SELECT
            COUNT(order_code) as totalShipped,
          FROM timeFiltered
          WHERE sts_code = '${WMSCustomerOrderStatus.Shipped}'
        ),
        totalOrders AS (
          SELECT
            COUNT(DISTINCT order_code) AS distinctOrders,
          FROM timeFiltered
        )
        SELECT
          IFNULL(SAFE_DIVIDE((SELECT totalShipped FROM shippedOrders), (SELECT distinctOrders FROM totalOrders)) * 100, 0) as orderProgressPercentage;
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length || !rows[0].orderProgressPercentage) {
      throw IctError.noContent();
    }

    // percentage is guaranteed to not be null by query
    return {
      orderProgressPercentage: rows[0].orderProgressPercentage,
    } as CustomerOrderProgress;
  }

  async getOrderProgressSeries(startDate: Date, endDate: Date) {
    const bigQueryDb = this.dbProvider.bigQuery;

    const eventCompleteCodes = await this.getOrderCompletedCodes();

    const pickTaskTable =
      this.dbProvider.bigQuery.getEdpFullTablePath('gold_pick_task');

    const sql = `
        WITH allEvents AS (
          SELECT
            pick_task_code,
            event_code,
            event_timestamp_utc
          FROM ${pickTaskTable}
          WHERE 
            event_timestamp_utc BETWEEN @startDate AND @endDate
          GROUP BY
            pick_task_code,
            event_code,
            event_timestamp_utc
        ),
        firstEvents AS (
          SELECT agg.allEvents.*
          FROM 
          (
            SELECT
              pick_task_code,
              event_code,
              ARRAY_AGG(STRUCT(allEvents) ORDER BY event_timestamp_utc)[SAFE_OFFSET(0)] agg 
            FROM allEvents allEvents
            GROUP BY
              pick_task_code,
              event_code
          )
        ),
        completedOrders AS (
          SELECT agg.firstEvents.*
          FROM 
          (
            SELECT
              pick_task_code,
              event_code,
            ARRAY_AGG(STRUCT(firstEvents) ORDER BY event_timestamp_utc)[SAFE_OFFSET(0)] agg 
            FROM firstEvents firstEvents 
            WHERE UPPER(event_code) IN UNNEST(@eventCompleteCodes)
            GROUP BY
              pick_task_code,
              event_code
          )
        ),
        completedOrdersByHour AS (
          SELECT
            TIMESTAMP_TRUNC(event_timestamp_utc, HOUR) AS hour,
            COUNT(DISTINCT(pick_task_code)) as completeOrders
          FROM completedOrders
          GROUP BY hour
        ),
        allOrders AS (
          SELECT pick_task_code as total
          FROM allEvents
          GROUP BY pick_task_code
        )
        SELECT 
          hour,
          SUM(completeOrders) OVER (ORDER BY hour) as running_completed_total,
          (SELECT COUNT(*) FROM allOrders) as total_orders,
        FROM completedOrdersByHour
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
        eventCompleteCodes,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length) {
      throw IctError.noContent();
    }

    // Map rows to OrderLineProgressChartData objects.
    const ordeProgressChartProcessedData: OrderProgressSeriesQueryData[] =
      rows.map(row => ({
        hour: row.hour.value,
        runningCompletedTotal: row.running_completed_total,
        totalOrders: row.total_orders,
      }));

    return ordeProgressChartProcessedData;
  }

  async getCustomerOrderProgressSeries(
    startDate: Date,
    endDate: Date,
    status?: WMSCustomerOrderStatus,
  ) {
    const bigQueryDb = this.dbProvider.bigQuery;
    let orderProgressStatus = status;
    if (
      status !== WMSCustomerOrderStatus.Shipped &&
      status !== WMSCustomerOrderStatus.Picked
    ) {
      orderProgressStatus = undefined;
    }

    const wmsCustomerOrderTable = this.dbProvider.bigQuery.getEdpFullTablePath(
      'gold_wms_customer_order',
    );

    const sql = `
      WITH earliestEvents AS (
        SELECT
          order_code,
          state_code,
          sts_code,
          edit_date_timestamp_utc,
          ROW_NUMBER() OVER (PARTITION BY order_code, state_code ORDER BY edit_date_timestamp_utc) AS row_num
        FROM ${wmsCustomerOrderTable}
        WHERE 
          edit_date_timestamp_utc BETWEEN @startDate AND @endDate
      ),
      filteredOrders AS (
        SELECT
          order_code,
          state_code,
          sts_code,
          edit_date_timestamp_utc
        FROM earliestEvents
        WHERE row_num = 1
          AND ${orderProgressStatus ? `sts_code = '${orderProgressStatus}'` : `state_code = '${WMSCustomerOrderState.Closed}'`}
      ),
      completedOrdersByHour AS (
        SELECT
          TIMESTAMP_TRUNC(edit_date_timestamp_utc, HOUR) AS hour,
          COUNT(DISTINCT order_code) AS completeOrders
        FROM filteredOrders
        GROUP BY hour
      ),
      allOrders AS (
        SELECT DISTINCT order_code
        FROM earliestEvents
      )
      SELECT 
        hour,
        SUM(completeOrders) OVER (ORDER BY hour) AS running_completed_total,
        (SELECT COUNT(*) FROM allOrders) AS total_orders
      FROM completedOrdersByHour;
    `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length) {
      throw IctError.noContent();
    }

    // Map rows to OrderLineProgressChartData objects.
    const ordeProgressChartProcessedData: OrderProgressSeriesQueryData[] =
      rows.map(row => ({
        hour: row.hour.value,
        runningCompletedTotal: row.running_completed_total,
        totalOrders: row.total_orders,
      }));

    return ordeProgressChartProcessedData;
  }

  async getOrderLineProgress(
    startDate: string,
    endDate: string,
  ): Promise<OrderLineProgress> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const pickTable = this.dbProvider.bigQuery.getEdpFullTablePath('gold_pick');

    const sql = `
        WITH
        totalLineItems AS (
          SELECT
            pick_task_line_code,
            task_line_complete_ind
          FROM ${pickTable}
          WHERE event_timestamp_utc BETWEEN @startDate AND @endDate
        ),
        totalLineItemsCount AS (
          SELECT
            COUNT(DISTINCT(pick_task_line_code)) AS totalCount
          FROM totalLineItems
        ),
        completedLineItemsCount AS (
          SELECT
            COUNT(DISTINCT(pick_task_line_code)) AS completedCount
          FROM totalLineItems
          WHERE task_line_complete_ind = 1
        )
        SELECT
          SAFE_DIVIDE(completedCount, totalCount) * 100 AS lineProgressPercent
        FROM totalLineItemsCount, completedLineItemsCount
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length || !rows[0].lineProgressPercent) {
      throw IctError.noContent();
    }

    const lineProgressData: OrderLineProgress = {
      lineProgressPercent: rows[0].lineProgressPercent,
    };

    return lineProgressData;
  }

  async getCustomerOrderLineProgress(
    startDate: string,
    endDate: string,
    status?: WMSCustomerOrderDetailStatus,
  ): Promise<OrderLineProgress> {
    const bigQueryDb = this.dbProvider.bigQuery;

    let shippingAreaFlag = 0;

    if (status) {
      shippingAreaFlag =
        status === WMSCustomerOrderDetailStatus.Shipped ? 1 : 0;
    }

    const edpCustomerOrderDetailTable =
      this.dbProvider.bigQuery.getEdpFullTablePath(
        'gold_wms_customer_order_detail',
      );

    const sql = `
        WITH completedOrders AS (
          SELECT 
            count(distinct order_id_line) as totalComplete
          FROM ${edpCustomerOrderDetailTable}
          WHERE edit_date_timestamp_utc > @startDate 
            AND edit_date_timestamp_utc < @endDate
            AND sts = '${status ?? WMSCustomerOrderDetailStatus.Shipped}'
        )
        SELECT 
          IFNULL((SELECT totalComplete FROM completedOrders LIMIT 1) / NULLIF(COUNT(distinct order_id_line), 0) * 100, 0) as lineProgressPercent,
        FROM ${edpCustomerOrderDetailTable}
        -- Do not include order details that have not made it to Picking yet in the total when getting progress for Shipping area
        WHERE edit_date_timestamp_utc > @startDate 
          AND edit_date_timestamp_utc < @endDate
          AND (
            (@shippingAreaFlag = 1 AND sts != '${WMSCustomerOrderDetailStatus.Open}')
            OR (@shippingAreaFlag = 0)
          )
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
        shippingAreaFlag,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length || !rows[0].lineProgressPercent) {
      throw IctError.noContent();
    }

    const percentage = rows[0].lineProgressPercent;
    const lineProgressData: OrderLineProgress = {
      lineProgressPercent: percentage,
    };

    return lineProgressData;
  }

  async getFacilityOrderLineProgress(
    startDate: string,
    endDate: string,
  ): Promise<OrderLineProgress> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const facilityOrderLineTable = this.dbProvider.bigQuery.getEdpFullTablePath(
      'gold_facility_order_line',
    );

    const sql = `
        -- Get all unique facility order lines with their event codes
        WITH recentOrderLineData AS (
          SELECT DISTINCT
            CONCAT(facility_order_code, '-', facility_order_line_code) as distinctOrderLine,
            event_code
          FROM ${facilityOrderLineTable}
          WHERE event_timestamp_utc > @startDate
            AND event_timestamp_utc < @endDate
        ),
        -- Get only the completed order lines
        completedOrderLine AS (
          SELECT DISTINCT distinctOrderLine as complete
          FROM recentOrderLineData
          WHERE UPPER(event_code) = 'COMPLETE'
        ),
        -- Get all order lines (for total count)
        totalOrderLines AS (
          SELECT DISTINCT distinctOrderLine as total
          FROM recentOrderLineData
        )
        -- Calculate the percentage of completed lines
        SELECT
          IFNULL(
            SAFE_DIVIDE(
              (SELECT COUNT(*) FROM completedOrderLine),
              (SELECT COUNT(*) FROM totalOrderLines)
            ) * 100,
            0
          ) AS lineProgressPercent
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length || !rows[0].lineProgressPercent) {
      throw IctError.noContent();
    }

    const percentage = rows[0].lineProgressPercent;
    const lineProgressData: OrderLineProgress = {
      lineProgressPercent: percentage,
    };

    return lineProgressData;
  }

  async getOrdersEstimatedCompletion(
    startDate: string,
    endDate: string,
  ): Promise<EstimatedCompletionQueryResult> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const eventCompleteCodes = await this.getOrderCompletedCodes();

    const pickTaskTable =
      this.dbProvider.bigQuery.getEdpFullTablePath('gold_pick_task');

    const sql = `
      WITH timeFiltered AS (
        SELECT
          *,
          TIMESTAMP_DIFF(CURRENT_TIMESTAMP(), event_timestamp_utc, SECOND) AS timeDiffSeconds
        FROM \`${pickTaskTable}\`
        WHERE event_timestamp_utc BETWEEN @startDate AND @endDate
      ),
      ordersCount AS (
        SELECT
          COUNT(DISTINCT pick_task_code) AS totalCount,
          COUNT(DISTINCT IF(UPPER(event_code) IN UNNEST(@eventCompleteCodes), pick_task_code, NULL)) AS completedCount,
          MAX(timeDiffSeconds) AS maxTimeDiff
        FROM timeFiltered
      )
      SELECT
        totalCount - completedCount AS nonCompletedCount,
        IFNULL(completedCount / maxTimeDiff * 3600, 0) AS ratePerHour,
        IF(IFNULL(completedCount / maxTimeDiff * 3600, 0) > 0, 
          (totalCount - completedCount) / (completedCount / maxTimeDiff * 3600), 
          NULL) AS estimatedHoursRemaining
      FROM ordersCount;
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
        eventCompleteCodes,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);
    const result: EstimatedCompletionQueryResult = rows?.[0];
    this.logger.info('getOrdersEstimatedCompletion results', result);
    if (!result || result.ratePerHour === 0) {
      throw IctError.noContent();
    }

    return result;
  }

  async getPickOrdersShipped(
    startDate: string,
    endDate: string,
  ): Promise<OrderShipped> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const eventCompleteCodes = await this.getOrderCompletedCodes();

    const pickTaskTable =
      this.dbProvider.bigQuery.getEdpFullTablePath('gold_pick_task');

    const sql = `
      DECLARE recentStartDate TIMESTAMP;
      DECLARE endDate TIMESTAMP;

      DECLARE previousStartDate TIMESTAMP;
      DECLARE tempPreviousEndDate TIMESTAMP;
      DECLARE previousEndDate TIMESTAMP;

      SET recentStartDate = TIMESTAMP_TRUNC(CAST(@startDate AS TIMESTAMP), DAY);
      SET previousStartDate = timestamp_sub(TIMESTAMP_TRUNC(CAST(@startDate AS TIMESTAMP), DAY), INTERVAL 1 DAY);
      SET tempPreviousEndDate = timestamp_sub(CAST(@endDate AS TIMESTAMP), INTERVAL 1 DAY);
      SET previousEndDate = CAST(CONCAT(CAST(CAST(tempPreviousEndDate AS DATE) AS STRING), 'T', EXTRACT(HOUR FROM tempPreviousEndDate),':', EXTRACT(MINUTE FROM tempPreviousEndDate), ':', EXTRACT(SECOND FROM tempPreviousEndDate)) as TIMESTAMP);

      WITH timeCurrent AS (
          SELECT *
          FROM ${pickTaskTable}
          WHERE event_timestamp_utc BETWEEN recentStartDate AND @endDate
      ),
      timePast AS (
          SELECT *
          FROM ${pickTaskTable}
          WHERE event_timestamp_utc BETWEEN previousStartDate AND previousEndDate
      )
      SELECT
          (SELECT COUNT(DISTINCT pick_task_code) 
          FROM timeCurrent 
          WHERE UPPER(event_code) IN UNNEST(@eventCompleteCodes)) AS \`current\`,
          
          (SELECT COUNT(DISTINCT pick_task_code) 
          FROM timePast 
          WHERE UPPER(event_code) IN UNNEST(@eventCompleteCodes)) AS \`past\`,
          
          (SELECT COUNT(DISTINCT pick_task_code) 
          FROM timeCurrent) AS total;
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
        eventCompleteCodes,
      },
    };
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length || (!rows[0].total && !rows[0].current && !rows[0].past)) {
      throw IctError.noContent();
    }

    return rows[0] as OrderShipped;
  }

  async getOrdersFacilityShipped(
    startDate: string,
    endDate: string,
  ): Promise<OrdersShipped> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const goldFacilityOrder = this.dbProvider.bigQuery.getEdpFullTablePath(
      'gold_facility_order',
    );

    const sql = `
        SELECT 
          COUNT(DISTINCT facility_order_code) as total, 
          COUNT(DISTINCT IF(UPPER(event_code)='COMPLETE', facility_order_code, NULL)) AS shipped,
        FROM ${goldFacilityOrder}
        WHERE event_timestamp_utc > @startDate 
          AND event_timestamp_utc < @endDate
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length || (!rows[0].total && !rows[0].shipped && !rows[0].past)) {
      throw IctError.noContent();
    }

    return rows[0] as OrdersShipped;
  }

  async getOrdersShipped(
    startDate: string,
    endDate: string,
  ): Promise<OrdersShipped> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const goldWmsCustomerOrder = this.dbProvider.bigQuery.getEdpFullTablePath(
      'gold_wms_customer_order',
    );

    const sql = `
        SELECT 
          COUNT(distinct order_code) as total,
          COUNTIF(sts_code='${WMSCustomerOrderStatus.Shipped}') as shipped
        FROM ${goldWmsCustomerOrder}
        WHERE edit_date_timestamp_utc > @startDate 
          AND edit_date_timestamp_utc < @endDate
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length || (!rows[0].total && !rows[0].shipped)) {
      throw IctError.noContent();
    }

    return rows[0] as OrdersShipped;
  }

  async getOrderCycleTime(
    startDate: string,
    endDate: string,
  ): Promise<OrderCycleTime> {
    // get the setting that defines the state range for an order cycle
    const settingValue = await this.configStore
      .findMostRelevantSettingForUser(
        this.context.userId,
        undefined,
        'cycle-time-state-range',
      )
      .then(setting => {
        return setting?.value as OrderCycleTimeSetting;
      });

    const startStates = settingValue?.startStates.map(state => {
      return state?.toUpperCase();
    });
    const endStates = settingValue?.endStates.map(state => {
      return state?.toUpperCase();
    });

    if (!settingValue || !startStates || !endStates) {
      throw IctError.internalServerError(
        'Config for cycle time range settings not found',
      );
    }
    this.logger.info(
      `Order cycle time range settings found. Start states: ${startStates}  End states: ${endStates}`,
    );

    // get the BigQuery DB and make the query
    const bigQueryDb = this.dbProvider.bigQuery;

    const pickTaskTable =
      this.dbProvider.bigQuery.getEdpFullTablePath('gold_pick_task');

    const sql = `
        WITH
        timeFiltered AS (
          SELECT *
          FROM ${pickTaskTable}
          WHERE event_timestamp_utc BETWEEN @startDate AND @endDate
        ),
        startTimes AS (
          SELECT
            pick_task_code,
            MIN(event_timestamp_utc) AS event_timestamp_utc,
          FROM timeFiltered
          WHERE UPPER(event_code) IN UNNEST(@startStates)
          GROUP BY pick_task_code
        ),
        endTimes AS (
          SELECT
            pick_task_code,
            MAX(event_timestamp_utc) AS event_timestamp_utc,
          FROM timeFiltered
          WHERE UPPER(event_code) IN UNNEST(@endStates)
          GROUP BY pick_task_code
        ),
        startEndTimes AS (
          SELECT
            st.pick_task_code,
            st.event_timestamp_utc AS startTime,
            et.event_timestamp_utc AS endTime,
          FROM startTimes st
          JOIN endTimes et
            ON st.pick_task_code = et.pick_task_code
        )
        SELECT ROUND(
        AVG(
          CASE WHEN TIMESTAMP_DIFF(
            TIMESTAMP(endTime),
            TIMESTAMP(startTime),
            MINUTE
          ) < 0 THEN 0 ELSE TIMESTAMP_DIFF(
            TIMESTAMP(endTime),
            TIMESTAMP(startTime),
            MINUTE
            )
            END
          )
        ) AS orderCycleTimeMinutes
        FROM startEndTimes;
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
        startStates,
        endStates,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length || !rows[0].orderCycleTimeMinutes) {
      throw IctError.noContent();
    }

    return rows[0] as OrderCycleTime;
  }

  async getFacilityOrdersEstimatedCompletion(
    startDate: Date,
    endDate: Date,
  ): Promise<CustomerOrderEstimatedCompletion> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const goldFacilityOrderTable = this.dbProvider.bigQuery.getEdpFullTablePath(
      'gold_facility_order',
    );

    const sql = `
        WITH timeFiltered AS (
          SELECT
            facility_order_code,
            event_code,
            TIMESTAMP_DIFF(CURRENT_TIMESTAMP(), event_timestamp_utc, SECOND) AS timeDiffSeconds,
          FROM ${goldFacilityOrderTable}
          WHERE event_timestamp_utc BETWEEN @startDate AND @endDate
        ),
        totalOrdersCount AS (
          SELECT
            COUNT(DISTINCT(facility_order_code)) AS totalCount,
          FROM timeFiltered
        ),
        shippedOrdersCount AS (
          SELECT
            COUNT(DISTINCT(facility_order_code)) AS shippedCount,
          FROM timeFiltered tf
          WHERE tf.event_code = '${FacilityOrderEventCodes.Complete}'
        ),
        nonShippedOrdersCount AS (
          SELECT 
            totalCount - shippedCount AS nonShippedCount,
          FROM totalOrdersCount, shippedOrdersCount
        ),
        currentRate AS (
          SELECT
            -- Use MAX(timeDiffSeconds) which results in calculating the rate of orders from the first order we saw until now
            IFNULL((SELECT shippedCount FROM shippedOrdersCount) / MAX(timeDiffSeconds) * 60, 0) AS ratePerMinute,
          FROM timeFiltered
        )
        SELECT
          IF(ratePerMinute > 0, nonShippedCount / ratePerMinute, NULL) AS estimatedMinutesRemaining,
        FROM nonShippedOrdersCount, currentRate;
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows?.length || !rows?.[0]?.estimatedMinutesRemaining) {
      throw IctError.noContent();
    }

    return rows[0].estimatedMinutesRemaining;
  }

  async getCustomerOrderEstimatedCompletion(
    startDate: Date,
    endDate: Date,
  ): Promise<CustomerOrderEstimatedCompletion> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const goldWmsCustomerOrderTable =
      this.dbProvider.bigQuery.getEdpFullTablePath('gold_wms_customer_order');

    const sql = `
        WITH timeFiltered AS (
          SELECT
            order_code,
            sts_code,
            TIMESTAMP_DIFF(CURRENT_TIMESTAMP(), edit_date_timestamp_utc, SECOND) AS timeDiffSeconds,
          FROM ${goldWmsCustomerOrderTable}
          WHERE edit_date_timestamp_utc BETWEEN @startDate AND @endDate
        ),
        totalOrdersCount AS (
          SELECT
            COUNT(DISTINCT(order_code)) AS totalCount,
          FROM timeFiltered
        ),
        shippedOrdersCount AS (
          SELECT
            COUNT(DISTINCT(order_code)) AS shippedCount,
          FROM timeFiltered tf
          WHERE tf.sts_code = '${WMSCustomerOrderStatus.Shipped}'
        ),
        nonShippedOrdersCount AS (
          SELECT 
            totalCount - shippedCount AS nonShippedCount,
          FROM totalOrdersCount, shippedOrdersCount
        ),
        currentRate AS (
          SELECT
            -- Use MAX(timeDiffSeconds) which results in calculating the rate of orders from the first order we saw until now
            IFNULL((SELECT shippedCount FROM shippedOrdersCount) / MAX(timeDiffSeconds) * 60, 0) AS ratePerMinute,
          FROM timeFiltered
        )
        SELECT
          IF(ratePerMinute > 0, nonShippedCount / ratePerMinute, NULL) AS estimatedMinutesRemaining,
        FROM nonShippedOrdersCount, currentRate;
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows?.length || !rows?.[0]?.estimatedMinutesRemaining) {
      throw IctError.noContent();
    }

    this.logger.info(
      `${this.getCustomerOrderEstimatedCompletion.name} results: ${rows[0].estimatedMinutesRemaining}`,
    );

    return rows[0].estimatedMinutesRemaining;
  }

  async getCustomerOrderCycleTime(
    startDate: string,
    endDate: string,
    status?: WMSCustomerOrderStatus,
  ): Promise<OrderCycleTime> {
    const bigQueryDb = this.dbProvider.bigQuery;

    let startStatus: WMSCustomerOrderStatus;
    if (!status) {
      startStatus = WMSCustomerOrderStatus.Open;
    } else if (status === WMSCustomerOrderStatus.Picked) {
      startStatus = WMSCustomerOrderStatus.Open;
    } else {
      startStatus = WMSCustomerOrderStatus.Picked;
    }

    const customerOrderTable = this.dbProvider.bigQuery.getEdpFullTablePath(
      'gold_wms_customer_order',
    );

    const sql = `
      WITH ordersCompletedToday AS (
        SELECT 
          order_code, 
          MAX(edit_date_timestamp_utc) AS completeTime
        FROM ${customerOrderTable}
        WHERE edit_date_timestamp_utc BETWEEN @startDate AND @endDate
        GROUP BY order_code
      ),
      orderStartTime AS (
        SELECT 
          o.order_code, 
          MIN(o.edit_date_timestamp_utc) AS startTime
        FROM ${customerOrderTable} o 
          INNER JOIN ordersCompletedToday s
            ON o.order_code = s.order_code
        WHERE o.edit_date_timestamp_utc > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 5 DAY)
          AND o.sts_code = '${startStatus}'
        GROUP BY order_code
      ),
      cycleTimes AS (
        SELECT 
          o.order_code, 
          s.completeTime, 
          o.startTime,
          CASE
            WHEN TIMESTAMP_DIFF(TIMESTAMP(s.completeTime), TIMESTAMP(o.startTime), MINUTE) < 0
              THEN 0
              ELSE TIMESTAMP_DIFF(TIMESTAMP(s.completeTime), TIMESTAMP(o.startTime),MINUTE)
          END AS cycleTimeMinutes
        FROM orderStartTime o 
        INNER JOIN ordersCompletedToday s
          ON o.order_code = s.order_code
      )
      SELECT
        AVG(cycleTimeMinutes) AS orderCycleTimeMinutes
      FROM cycleTimes
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length || !rows[0].orderCycleTimeMinutes) {
      throw IctError.noContent();
    }

    return rows[0] as OrderCycleTime;
  }

  async getFacilityOrderCycleTime(
    startDate: string,
    endDate: string,
  ): Promise<OrderCycleTime> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const facilityOrderTable = this.dbProvider.bigQuery.getEdpFullTablePath(
      'gold_facility_order',
    );

    const sql = `
      SELECT
        SAFE_DIVIDE(AVG(cycleTimeMS), 60000) as orderCycleTimeMinutes
      FROM (
        SELECT
          -- use milliseconds to get cycle time to avoid inaccuracies from truncating with minutes
          TIMESTAMP_DIFF(completeTime, startTime, MILLISECOND) as cycleTimeMS
        FROM (
          SELECT
            facility_order_code,
            MAX(CASE WHEN UPPER(event_code) = 'COMPLETE' THEN event_timestamp_utc ELSE NULL END) AS completeTime,
            MIN(CASE WHEN UPPER(event_code) = 'RELEASED' THEN event_timestamp_utc ELSE NULL END) AS startTime
          FROM
            ${facilityOrderTable}
          WHERE
            event_timestamp_utc BETWEEN @startDate AND @endDate
          GROUP BY
            facility_order_code
        ) AS subquery
        WHERE
          startTime IS NOT NULL
          AND completeTime IS NOT NULL
          AND startTime < completeTime
        )
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length || !rows[0].orderCycleTimeMinutes) {
      throw IctError.noContent();
    }

    return rows[0] as OrderCycleTime;
  }

  async getCustomerOrderLineThroughputSeries(
    startDate: Date,
    endDate: Date,
  ): Promise<CustomerOrderLineThroughputSeriesQueryResponse[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const goldWmsCustomerOrderDetail =
      this.dbProvider.bigQuery.getEdpFullTablePath(
        'gold_wms_customer_order_detail',
      );

    const sql = `
      WITH filtered AS (
        SELECT
          order_id_line,
          MAX(edit_date_timestamp_utc) AS record_timestamp,
        FROM \`${goldWmsCustomerOrderDetail}\`
        WHERE edit_date_timestamp_utc BETWEEN TIMESTAMP(@startDate) AND TIMESTAMP(@endDate)
          AND sts = '${WMSCustomerOrderDetailStatus.Picked}'
        GROUP BY order_id_line
      )
      SELECT
        -- Splits into 15 minute intervals: https://stackoverflow.com/a/55191793
        TIMESTAMP_SECONDS(15 * 60 * DIV(UNIX_SECONDS(f.record_timestamp), 15 * 60)) AS event_interval,
        COUNT(f.order_id_line) * 4 AS completed_order_lines_hourly_rate,
      FROM filtered f
      GROUP BY
        event_interval
      ORDER BY
        event_interval
    `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);
    return rows as CustomerOrderLineThroughputSeriesQueryResponse[];
  }

  async getAreasCycleTime(
    startDate: string,
    endDate: string,
  ): Promise<CycletimeFacilityArea[]> {
    const bigQueryDb = this.dbProvider.bigQuery;
    const tableName =
      this.dbProvider.bigQuery.getFullTablePath('fct_pick_order');

    const sql = `
    WITH
    timeFiltered AS (
      SELECT pick_order_uuid AS pickOrderPk, pick_order_event AS event, record_timestamp AS eventTime,
      'PICKING' AS areaId, operator_uuid
      FROM \`${tableName}\`
      WHERE record_timestamp > @startDate
      AND record_timestamp < @endDate
    ),
    startEndTimes AS (
      SELECT
        areaId,
        pickOrderPk,
        operator_uuid,
        MAX(IF(event = "RELEASED", CAST(eventTime AS TIMESTAMP), NULL)) as startTime,
        MAX(IF(UPPER(event) IN ('COMPLETE', 'COMPLETED', 'PICK_COMPLETE', 'FINISHED'), CAST(eventTime AS TIMESTAMP), NULL)) as endTime,
        TIMESTAMP_DIFF(
          MAX(IF(UPPER(event) IN ('COMPLETE', 'COMPLETED', 'PICK_COMPLETE', 'FINISHED'), CAST(eventTime AS TIMESTAMP), NULL)),
          MAX(IF(event = "RELEASED", CAST(eventTime AS TIMESTAMP), NULL)),
          SECOND
        ) AS cycleTime
      FROM timeFiltered
      GROUP BY areaId, pickOrderPk, operator_uuid
      HAVING startTime IS NOT NULL AND endTime IS NOT NULL
    ),
    lineCount as (
      SELECT COUNT(pickOrderPk) AS linesPerOrder, timeFiltered.pickOrderPk FROM timeFiltered WHERE event = 'RELEASED' GROUP BY pickOrderPk
    ),
    areaOrders AS (
      SELECT
        areaId,
        TIMESTAMP_SECONDS(CAST(UNIX_SECONDS(MAX(endTime)) / linesPerOrder AS INT64)) as completion_time,
        ROUND(AVG(cycleTime) / linesPerOrder) AS averageCycleTime,
        MIN(cycleTime) / linesPerOrder AS minimumCycleTime,
        MAX(cycleTime) / linesPerOrder AS maximumCycleTime
      FROM startEndTimes JOIN lineCount ON startEndTimes.pickOrderPk = lineCount.pickOrderPk
      GROUP BY areaId, linesPerOrder
    )
SELECT
      startEndTimes.areaId,
      FORMAT_TIMESTAMP('%Y-%m-%dT%H:%M:%E3S', completion_time) AS completionTime,
      ROUND(120 - AVG(cycleTime)) AS time_difference,
      averageCycleTime,
      minimumCycleTime,
      maximumCycleTime,
      startEndTimes.areaId AS areaName
    FROM startEndTimes
JOIN areaOrders
    ON startEndTimes.areaId = areaOrders.areaId
    GROUP BY startEndTimes.areaId, completionTime, averageCycleTime, minimumCycleTime, maximumCycleTime
    `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length) {
      throw IctError.noContent();
    }

    // Map rows to AreaCycleTime objects.
    const areas: CycletimeFacilityArea[] = rows.map(row => ({
      id: row.areaId,
      name: row.areaName,
      alertStatus: {
        status: row.status,
        identifier: row.identifier,
      },
      operators: {
        activeOperators: row.activeOperators,
        lowRecOperators: row.lowRecOperators,
        highRecOperators: row.highRecOperators,
      },
      cycleTime: {
        averageCycleTimeSeconds: row.averageCycleTime,
        fastestCycleTimeSeconds: row.minimumCycleTime,
        slowestCycleTimeSeconds: row.maximumCycleTime,
        completionTime: row.completionTime,
        // TODO: highRecCycleTime and targetCycleTime
        slowestRecCycleTimeSeconds: 130000,
        targetCycleTimeSeconds: 2000,
      },
    }));

    // Return an object matching the AreaCycleTimeResponse interface.
    return areas;
  }

  async getAreasThroughput(
    startDate: string,
    endDate: string,
    area?: string,
  ): Promise<ThroughputFacilityArea[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const tableName =
      this.dbProvider.bigQuery.getFullTablePath('fct_pick_order');
    // build the SQL query to retrieve the order throughput rates by area for the given date range
    // TO DO: Operator data will reflect entire date range to be queried (using same date parameters)from same dataset as order throughput
    let sql = `
      SELECT COUNT(DISTINCT pick_order_uuid) as throughputRate,
      CASE
        WHEN pick_order_event = 'REQUESTING' THEN 'Pending'
        WHEN pick_order_event = 'NOT_RELEASED' THEN 'Pending'
        WHEN pick_order_event = 'RELEASED' THEN 'Picking'
        WHEN pick_order_event = 'ACTIVE' THEN 'Picking'
        WHEN pick_order_event = 'COMPLETE' THEN 'Shipping'
        WHEN pick_order_event = 'FINISHED' THEN 'Shipping'
        WHEN pick_order_event = 'CONSOLIDATION' THEN 'Shipping'
      END AS areaName,
      COUNT(DISTINCT operator_uuid) as activeOperators,
      '' AS areaId,
      '' AS lowRecOperators,
      '' AS highRecOperators,
      '' AS status,
      '' AS identifier,
      '' AS message,
      FROM \`${tableName}\`
  WHERE record_timestamp > @startDate AND record_timestamp < @endDate
  GROUP BY areaName        `;

    if (area) {
      sql = sql.concat(' WHERE areaName = @area');
    }

    sql = sql.concat(';');

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
        area,
      },
    };

    // Execute the query and return the results
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length) {
      throw IctError.noContent();
    }

    // TODO: alert status should be configured as a result of operator information and config data in a db
    // Map rows to AreaThroughput objects.
    const areas: ThroughputFacilityArea[] = rows.map(row => ({
      id: row.areaName,
      name: row.areaName,
      operators: {
        activeOperators: row.activeOperators,
        lowRecOperators: row.lowRecOperators,
        highRecOperators: row.highRecOperators,
      },
      alertStatus: {
        status: row.status,
        identifier: row.identifier,
        message: row.message,
      },
      throughput: {
        throughputRate: row.throughputRate,
        maxThroughputCapacity:
          Math.ceil((row.throughputRate * 1.5) / 100) * 100,
        maxThroughputTarget: this.calculateThroughputTarget(
          row.status,
          row.throughputRate,
          'max',
        ),
        minThroughputTarget: this.calculateThroughputTarget(
          row.status,
          row.throughputRate,
          'min',
        ),
      },
    }));

    // Return an object matching the AreaThroughputResponse interface.
    return areas;
  }

  /**
   * Helper method to calculate throughput targets based on status
   * @param status The status of the area
   * @param throughputRate The current throughput rate
   * @param targetType Whether to calculate 'max' or 'min' target
   * @returns The calculated throughput target
   */
  private calculateThroughputTarget(
    status: string,
    throughputRate: number,
    targetType: 'max' | 'min',
  ): number {
    if (status === 'critical') {
      return (
        Math.ceil((throughputRate * (targetType === 'max' ? 1.4 : 1.2)) / 10) *
        10
      );
    }

    if (status === 'caution') {
      return (
        Math.ceil((throughputRate * (targetType === 'max' ? 1.3 : 1.1)) / 10) *
        10
      );
    }

    return (
      Math.ceil((throughputRate * (targetType === 'max' ? 1.1 : 0.9)) / 10) * 10
    );
  }

  async getOrderThroughputRate(
    startDate: string,
    endDate: string,
    area?: PickOrderArea,
  ): Promise<OrderThroughputData> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const eventCompleteCodes = await this.getOrderCompletedCodes();

    const goldPickTask =
      this.dbProvider.bigQuery.getEdpFullTablePath('gold_pick_task');

    const sql = `
        SELECT
          SAFE_DIVIDE(COUNT(DISTINCT pick_task_code), TIMESTAMP_DIFF(@endDate, @startDate, HOUR)) as throughputRateLinesPerHour
        FROM \`${goldPickTask}\`
        WHERE (UPPER(event_code) IN UNNEST(@eventCompleteCodes))
            AND event_timestamp_utc BETWEEN TIMESTAMP(@startDate) AND TIMESTAMP(@endDate)
            ${area ? `AND area_code = '${area}'` : ''}
      `;

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
        eventCompleteCodes,
      },
    };

    // Execute the query and return the results
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length || !rows[0].throughputRateLinesPerHour) {
      throw IctError.noContent();
    }

    return rows[0] as OrderThroughputData;
  }

  async getOrderCustomerLineThroughput(
    startDate: string,
    endDate: string,
    status?: WMSCustomerOrderDetailStatus,
  ): Promise<OrderCustomerLineThroughputData> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const goldWmsCustomerOrderDetail =
      this.dbProvider.bigQuery.getEdpFullTablePath(
        'gold_wms_customer_order_detail',
      );

    const sql = `
      SELECT 
        SAFE_DIVIDE(COUNT(DISTINCT order_id_line), TIMESTAMP_DIFF(@endDate, @startDate, HOUR)) as throughputRateOrderLinesPerHour
        FROM ${goldWmsCustomerOrderDetail}
        WHERE edit_date_timestamp_utc > @startDate 
          AND edit_date_timestamp_utc < @endDate
          AND sts='${status ?? WMSCustomerOrderDetailStatus.Shipped}'
    `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length || !rows[0].throughputRateOrderLinesPerHour) {
      throw IctError.noContent();
    }

    return rows[0] as OrderCustomerLineThroughputData;
  }

  async getOrderCustomerThroughputChartData(
    startDate: string,
    endDate: string,
  ): Promise<OrderCustomerThroughputChartQueryData[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const wmsCusterOrderTable = this.dbProvider.bigQuery.getEdpFullTablePath(
      'gold_wms_customer_order',
    );

    const sql = `
        WITH timeBuckets AS (
          SELECT
            TIMESTAMP_BUCKET(edit_date_timestamp_utc, INTERVAL 15 MINUTE) as event_interval,
            COUNT(DISTINCT(order_code)) * 4 as completed_orders
          FROM \`${wmsCusterOrderTable}\`
          WHERE edit_date_timestamp_utc BETWEEN @startDate AND @endDate
            AND sts_code = '${WMSCustomerOrderStatus.Shipped}'
          GROUP BY event_interval
        )
        SELECT
          event_interval,
          IFNULL(completed_orders, 0) as completed_orders
        FROM GAP_FILL(
          TABLE timeBuckets,
          ts_column => 'event_interval',
          bucket_width => INTERVAL 15 MINUTE
        )
        ORDER BY event_interval;
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    // throw no content if every data point has a rate of 0
    if (
      !rows.length ||
      !rows.some(element => element.hourly_throughput_rate !== 0)
    ) {
      throw IctError.noContent();
    }

    return rows as OrderCustomerThroughputChartQueryData[];
  }

  async getOrderPickLineThroughputSeries(
    startDate: string,
    endDate: string,
  ): Promise<OrderPickLineThroughputSeriesQueryData[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const pickTable = this.dbProvider.bigQuery.getEdpFullTablePath('gold_pick');

    const sql = `
        WITH completedPickOrderLines AS (
          SELECT
            event_timestamp_utc,
            pick_task_line_code,
            work_type_code
          FROM \`${pickTable}\` lineFct

          WHERE event_timestamp_utc BETWEEN TIMESTAMP(@startDate) AND TIMESTAMP(@endDate)
            AND picked_qty > 0
            AND CONTAINS_SUBSTR(work_type_code, "PICK")
        ),
        timeBuckets AS (
          SELECT
            TIMESTAMP_BUCKET(event_timestamp_utc, INTERVAL 15 MINUTE) as event_interval,
            COUNT(DISTINCT(pick_task_line_code)) * 4 as hourly_throughput_rate
          FROM completedPickOrderLines
          GROUP BY event_interval
        )
        SELECT event_interval, IFNULL(hourly_throughput_rate, 0) as hourly_throughput_rate
        FROM GAP_FILL(
          TABLE timeBuckets,
          ts_column => 'event_interval',
          bucket_width => INTERVAL 15 MINUTE
        )
        ORDER BY event_interval;
    `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    // throw no content if every data point has a rate of 0
    if (
      !rows.length ||
      !rows.some(element => element.hourly_throughput_rate !== 0)
    ) {
      throw IctError.noContent();
    }

    return rows as OrderPickLineThroughputSeriesQueryData[];
  }

  async getFacilityOrderLineThroughputSeries(
    startDate: string,
    endDate: string,
  ): Promise<FacilityOrderLineThroughputSeriesQueryResponse[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const facilityOrderLineTable = this.dbProvider.bigQuery.getEdpFullTablePath(
      'gold_facility_order_line',
    );

    const sql = `
      WITH filtered AS (
        SELECT
          facility_order_line_code,
          MAX(event_timestamp_utc) AS record_timestamp,
        FROM \`${facilityOrderLineTable}\`
        WHERE event_timestamp_utc BETWEEN TIMESTAMP(@startDate) AND TIMESTAMP(@endDate)
          AND UPPER(event_code) = 'COMPLETE'
        GROUP BY facility_order_line_code
      )
      SELECT
        -- Splits into 15 minute intervals: https://stackoverflow.com/a/55191793
        TIMESTAMP_SECONDS(15 * 60 * DIV(UNIX_SECONDS(f.record_timestamp), 15 * 60)) AS event_interval,
        COUNT(f.facility_order_line_code) * 4 AS hourly_throughput_rate,
      FROM filtered f
      GROUP BY
        event_interval
      ORDER BY
        event_interval
    `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);
    return rows as FacilityOrderLineThroughputSeriesQueryResponse[];
  }

  async getOrderLineThroughput(
    startDate: string,
    endDate: string,
  ): Promise<OrderLineThroughputData> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const goldPick = this.dbProvider.bigQuery.getEdpFullTablePath('gold_pick');

    const sql = `
        SELECT
          SAFE_DIVIDE(COUNT(DISTINCT pick_task_line_code), TIMESTAMP_DIFF(@endDate, @startDate, HOUR)) as totalCompletedOrderLines
        FROM \`${goldPick}\`
        WHERE task_line_complete_ind = 1
          AND event_timestamp_utc BETWEEN TIMESTAMP(@startDate) AND TIMESTAMP(@endDate)
        `;

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    // Execute the query and return the results
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length || !rows[0].totalCompletedOrderLines) {
      throw IctError.noContent();
    }

    return rows[0];
  }
  /**
   * Calculates the number of orders that were put through the given area per hour between the given start and end date times.
   * If status is not passed, the calculation is done with the number of orders shipped.
   */
  async getCustomerOrderThroughputRate(
    startDate: string,
    endDate: string,
    status?: WMSCustomerOrderStatus,
  ): Promise<CustomerOrderThroughputData> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const goldCutsomerOrder = this.dbProvider.bigQuery.getEdpFullTablePath(
      'gold_wms_customer_order',
    );

    const sql = `
        SELECT 
          SAFE_DIVIDE(COUNT(DISTINCT order_code), TIMESTAMP_DIFF(@endDate, @startDate, HOUR)) as throughputRateOrdersPerHour
        FROM \`${goldCutsomerOrder}\`
        WHERE edit_date_timestamp_utc > @startDate 
          AND edit_date_timestamp_utc < @endDate
          AND sts_code='${status ?? WMSCustomerOrderStatus.Shipped}'
      `;

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    // Execute the query and return the results
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length || !rows[0].throughputRateOrdersPerHour) {
      throw IctError.noContent();
    }

    const orderThroughputData: CustomerOrderThroughputData = {
      throughputRateOrdersPerHour: rows[0].throughputRateOrdersPerHour,
    };

    return orderThroughputData;
  }

  async getFacilityOrderThroughputRate(
    startDate: string,
    endDate: string,
  ): Promise<CustomerOrderThroughputData> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const goldFacilityOrder = this.dbProvider.bigQuery.getEdpFullTablePath(
      'gold_facility_order',
    );

    const sql = `
        SELECT 
          SAFE_DIVIDE(COUNT(DISTINCT facility_order_code), TIMESTAMP_DIFF(@endDate, @startDate, HOUR)) as throughputRateOrdersPerHour
        FROM \`${goldFacilityOrder}\`
        WHERE event_timestamp_utc > @startDate 
          AND event_timestamp_utc < @endDate
          AND UPPER(event_code)='COMPLETE'
      `;

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    // Execute the query and return the results
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length || !rows[0].throughputRateOrdersPerHour) {
      throw IctError.noContent();
    }

    const orderThroughputData: CustomerOrderThroughputData = {
      throughputRateOrdersPerHour: rows[0].throughputRateOrdersPerHour,
    };

    return orderThroughputData;
  }

  async getOrderThroughputChartData(
    startDate: string,
    endDate: string,
  ): Promise<OrderThroughputChartQueryData[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const eventCompleteCodes = await this.getOrderCompletedCodes();

    const goldPickTask =
      this.dbProvider.bigQuery.getEdpFullTablePath('gold_pick_task');

    const sql = `
          WITH completedOrders AS (
            SELECT
              event_timestamp_utc,
              pick_task_code
            FROM \`${goldPickTask}\`
            WHERE event_timestamp_utc BETWEEN TIMESTAMP(@startDate) AND TIMESTAMP(@endDate)
              AND UPPER(event_code) IN UNNEST(@eventCompleteCodes)
          ),
          timeBuckets AS (
            SELECT
              TIMESTAMP_BUCKET(event_timestamp_utc, INTERVAL 15 MINUTE) as event_hour,
              COUNT(DISTINCT(pick_task_code)) * 4 as completed_orders
            FROM completedOrders
            GROUP BY event_hour
          )
          SELECT event_hour, IFNULL(completed_orders, 0) as completed_orders
          FROM GAP_FILL(
            TABLE timeBuckets,
            ts_column => 'event_hour',
            bucket_width => INTERVAL 15 MINUTE
          )
          ORDER BY event_hour;
          `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
        eventCompleteCodes,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    // throw no content if every data point has a rate of 0
    if (
      !rows.length ||
      !rows.some(element => element.hourly_throughput_rate !== 0)
    ) {
      throw IctError.noContent();
    }

    return rows as OrderThroughputChartQueryData[];
  }

  async getOrderCycleTimeChartData(startDate: string, endDate: string) {
    const bigQueryDb = this.dbProvider.bigQuery;

    const pickOrderTaskTable =
      this.dbProvider.bigQuery.getEdpFullTablePath('gold_pick_task');

    const sql = `
        WITH timeFiltered AS (
          SELECT *
          FROM \`${pickOrderTaskTable}\`
          WHERE event_timestamp_utc BETWEEN @startDate AND @endDate
        ),
        startTimes AS (
          SELECT
            pick_task_code,
            event_timestamp_utc
          FROM timeFiltered
          WHERE UPPER(event_code) = 'ACTIVE'
        ),
        endTimes AS (
          SELECT
            pick_task_code,
            event_timestamp_utc
          FROM timeFiltered
          WHERE UPPER(event_code) IN ('COMPLETED', 'PICK_COMPLETE')
        ),
        startEndTimes AS (
          SELECT
            st.pick_task_code,
            st.event_timestamp_utc AS startTime,
            et.event_timestamp_utc AS endTime
          FROM startTimes st
          JOIN endTimes et
            ON st.pick_task_code = et.pick_task_code
        ),
        cycleTimes AS (
          SELECT
            TIMESTAMP_TRUNC(startTime, HOUR) AS event_hour,
            ROUND(
              TIMESTAMP_DIFF(
                TIMESTAMP(endTime),
                TIMESTAMP(startTime),
                MINUTE
              )
            ) AS orderCycleTimeMinutes
          FROM startEndTimes
        )
        SELECT
          event_hour,
          AVG(cycleTimes.orderCycleTimeMinutes) * 60 AS average_cycletime_seconds
        FROM cycleTimes
        GROUP BY event_hour
        ORDER BY event_hour;
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length) {
      throw IctError.noContent();
    }

    return rows as OrderCycleTimeChartQueryResponse[];
  }

  async getOrderLineProgressAreas(
    startDate: string,
    endDate: string,
  ): Promise<LineProgressFacilityArea[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const sql = `
      -- CTE to calculate progress based on order area.
      -- This uses a modulo operation on the pickOrderPk to distribute orders into four distinct areas.
      WITH progressCalculation AS (
          SELECT
          areaId,
          SUM(quantityPicked) AS currentCount,
          SUM(quantityTarget) AS target,
          pickOrderPk
          FROM
              -- Source table for order area progress.
              \`${bigQueryDb.getFullTablePath('demo01_area_assigned_orders')}\`
          WHERE
              -- Filter based on the given date range.
              TIMESTAMP(eventTime) BETWEEN TIMESTAMP(@startDate) AND TIMESTAMP(@endDate)
          GROUP BY
              -- Group by the calculated area and orderPk for aggregation.
              areaId, pickOrderPk
      )

      -- Main query to get the order progress details for each area.
      SELECT
          progressCalculation.areaId AS areaId,
          -- Fetch the human-readable name of the area from dimensions table.
          areaDimension.name AS areaName,
          -- Calculate the order line progress as a percentage.
          ROUND(100*SAFE_DIVIDE(SUM(progressCalculation.currentCount), SUM(progressCalculation.target)), 1) AS orderLineProgress,
          -- Aggregate counts for the current progress and target.
          SUM(progressCalculation.currentCount) AS orderLinesCompleted,
          SUM(progressCalculation.target) AS totalOrderLines,
          -- Fetch status and operators details for each area.
          areaStatus.active_operators as activeOperators,
          areaStatus.low_rec_operators as lowRecOperators,
          areaStatus.high_rec_operators as highRecOperators,
          areaStatus.identifier as identifier,
          areaStatus.status as status,
          areaStatus.message as message,
          areaStatus.max_operators as maxOperators
      FROM
          -- Start with the progress calculations from the CTE.
          progressCalculation
      JOIN
          -- Join with the area status table to get status details for each area.
          \`${bigQueryDb.getFullTablePath(
            'demo01_internal_area_status_v2',
          )}\` as areaStatus
      ON
          -- Join condition based on areaId.
          progressCalculation.areaId = areaStatus.areaId
      JOIN
          -- Join with the dimensions table to get the area name.
          \`${bigQueryDb.getFullTablePath(
            'demo01_internal_dimensions_area',
          )}\` as areaDimension
      ON
          -- Join condition based on areaId.
          progressCalculation.areaId = areaDimension.primaryKey
      GROUP BY
          -- Group by area details and status fields to aggregate progress.
          progressCalculation.areaId,
          areaDimension.name,
          areaStatus.active_operators,
          areaStatus.low_rec_operators,
          areaStatus.high_rec_operators,
          areaStatus.identifier,
          areaStatus.status,
          areaStatus.message,
          areaStatus.max_operators
      ORDER BY
          -- Order the results by areaId.
          progressCalculation.areaId
      `;

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length) {
      throw IctError.noContent();
    }

    // TODO: Configure bounds of getStatus parameter
    const areaData: LineProgressFacilityArea[] = rows.map(row => ({
      id: row.areaId,
      name: row.areaName,
      alertStatus: {
        status: row.status,
        identifier: row.identifier,
      },
      operators: {
        activeOperators: row.activeOperators,
        lowRecOperators: row.lowRecOperators,
        highRecOperators: row.highRecOperators,
      },
      orderLines: {
        orderLineProgress: row.orderLineProgress,
        orderLinesCompleted: row.orderLinesCompleted,
        totalOrderLines: row.totalOrderLines,
      },
    }));

    return areaData;
  }

  async getCustomerOrderLineProgressSeries(
    startDate: Date,
    endDate: Date,
    status?: WMSCustomerOrderDetailStatus,
  ) {
    const bigQueryDb = this.dbProvider.bigQuery;
    let orderProgressStatus = status;
    if (
      status !== WMSCustomerOrderDetailStatus.Shipped &&
      status !== WMSCustomerOrderDetailStatus.Picked
    ) {
      orderProgressStatus = undefined;
    }

    const wmsCustomerOrderDetail = this.dbProvider.bigQuery.getEdpFullTablePath(
      'gold_wms_customer_order_detail',
    );

    const sql = `
        WITH allEvents AS (
          SELECT
            order_id_line,
            state,
            sts,
            edit_date_timestamp_utc
          FROM \`${wmsCustomerOrderDetail}\`
          WHERE edit_date_timestamp_utc BETWEEN @startDate AND @endDate
          GROUP BY
            order_id_line,
            state,
            sts,
            edit_date_timestamp_utc
        ),
        firstEvents AS (
          SELECT agg.allEvents.*
          FROM 
          (
            SELECT
              order_id_line,
              state,
              sts,
              ARRAY_AGG(STRUCT(allEvents) ORDER BY edit_date_timestamp_utc)[SAFE_OFFSET(0)] AS agg 
            FROM allEvents
            GROUP BY
              order_id_line,
              state,
              sts
          )
        ),
        completedOrders AS (
          SELECT agg.firstEvents.*
          FROM 
          (
            SELECT
              order_id_line,
              state,
              sts,
              ARRAY_AGG(STRUCT(firstEvents) ORDER BY edit_date_timestamp_utc)[SAFE_OFFSET(0)] AS agg 
            FROM firstEvents
            WHERE sts = '${orderProgressStatus || WMSCustomerOrderStatus.Shipped}'
            GROUP BY
              order_id_line,
              state,
              sts
          )
        ),
        completedOrdersByHour AS (
          SELECT
            TIMESTAMP_TRUNC(edit_date_timestamp_utc, HOUR) AS hour,
            COUNT(DISTINCT(order_id_line)) AS completeOrders
          FROM completedOrders
          GROUP BY hour
        ),
        allOrders AS (
          SELECT order_id_line AS total
          FROM allEvents
          GROUP BY order_id_line
        )
        SELECT 
          hour,
          SUM(completeOrders) OVER (ORDER BY hour) AS running_completed_total,
          (SELECT COUNT(*) FROM allOrders) AS total_orders
        FROM completedOrdersByHour
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length) {
      throw IctError.noContent();
    }

    const ordeProgressChartProcessedData: OrderProgressSeriesQueryData[] =
      rows.map(row => ({
        hour: row.hour.value,
        runningCompletedTotal: row.running_completed_total,
        totalOrders: row.total_orders,
      }));

    return ordeProgressChartProcessedData;
  }

  async getFacilityOrderLineProgressSeries(startDate: Date, endDate: Date) {
    const bigQueryDb = this.dbProvider.bigQuery;

    const facilityOrderLine = this.dbProvider.bigQuery.getEdpFullTablePath(
      'gold_facility_order_line',
    );

    const sql = `
        WITH recentOrderLineData AS (
          SELECT DISTINCT
            CONCAT(facility_order_code, '-', facility_order_line_code) as distinctOrderLine,
            event_code,
            event_timestamp_utc
          FROM ${facilityOrderLine}
          WHERE event_timestamp_utc > @startDate
            AND event_timestamp_utc < @endDate
        ),
        -- Get only the completed order lines with their completion timestamps
        completedOrderLine AS (
          SELECT DISTINCT 
            distinctOrderLine as complete,
            MIN(event_timestamp_utc) as completedTime
          FROM recentOrderLineData
          WHERE UPPER(event_code) = 'COMPLETE'
          GROUP BY distinctOrderLine
        ),
        -- Get all order lines (for total count)
        totalOrderLines AS (
          SELECT DISTINCT distinctOrderLine as total
          FROM recentOrderLineData
        ),
        -- Group completed orders by hour
        completedOrdersByHour AS (
          SELECT
            TIMESTAMP_TRUNC(completedTime, HOUR) AS hour,
            COUNT(DISTINCT(complete)) AS completeOrders
          FROM completedOrderLine
          GROUP BY hour
        )
        -- Calculate running totals and return hourly data
        SELECT 
          hour,
          SUM(completeOrders) OVER (ORDER BY hour) AS running_completed_total,
          (SELECT COUNT(*) FROM totalOrderLines) AS total_orders
        FROM completedOrdersByHour
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length) {
      throw IctError.noContent();
    }

    const ordeProgressChartProcessedData: OrderProgressSeriesQueryData[] =
      rows.map(row => ({
        hour: row.hour.value,
        runningCompletedTotal: row.running_completed_total,
        totalOrders: row.total_orders,
      }));

    return ordeProgressChartProcessedData;
  }

  async getOrderLineProgressChartData(
    startDate: string,
    endDate: string,
  ): Promise<OrderLineProgressChartQueryData[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const goldPick = this.dbProvider.bigQuery.getEdpFullTablePath('gold_pick');

    const sql = `
        WITH totalTargetByHour AS (
          SELECT
            SUM(requested_qty) as total_quantityTarget
            FROM \`${goldPick}\`
          WHERE
            event_timestamp_utc >= @startDate AND
            event_timestamp_utc <= @endDate
        ),
        hourlyPicks AS (
          SELECT
            TIMESTAMP_TRUNC(event_timestamp_utc, HOUR) AS hour,
            SUM(picked_qty) as total_quantityPicked
          FROM \`${goldPick}\`
          WHERE
            event_timestamp_utc >= @startDate AND
            event_timestamp_utc <= @endDate
          GROUP BY hour
        )
        SELECT
          hourlyPicks.hour,
          COALESCE(totalTargetByHour.total_quantityTarget,hourlyPicks.total_quantityPicked) AS total_quantityTarget,
          hourlyPicks.total_quantityPicked,
          SUM(hourlyPicks.total_quantityPicked)OVER (ORDER BY hourlyPicks.hour) as running_total
        FROM hourlyPicks
        CROSS JOIN totalTargetByHour
        ORDER BY hourlyPicks.hour;
      `;

    const sqlOptions = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length) {
      throw IctError.noContent();
    }

    // Map rows to OrderLineProgressChartData objects.
    const orderLineProgressChartProcessedData: OrderLineProgressChartQueryData[] =
      rows.map(row => ({
        hour: row.hour.value,
        quantityPicked: row.total_quantityPicked,
        runningTotal: row.running_total,
        quantityTarget: row.total_quantityTarget,
      }));

    return orderLineProgressChartProcessedData;
  }

  async getProjectedCustomerOrderFulfillment(
    startDate: string,
    endDate: string,
  ): Promise<ProjectedOrderFulfillment> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const goldPickTask = bigQueryDb.getEdpFullTablePath('gold_pick_task');

    // Projected Order Fulfillment = (Total distinct orders - Total Orders in Created status) / Total Distinct Orders
    const sql = `
          WITH orders_with_status AS (
            SELECT
              pick_task_code,
              event_code,
              ROW_NUMBER() OVER (PARTITION BY pick_task_code ORDER BY event_timestamp_utc DESC) AS rn
            FROM
              ${goldPickTask}
            WHERE event_timestamp_utc BETWEEN @startDate AND @endDate 
          ), status_counts AS (
            SELECT 
              COUNT(DISTINCT pick_task_code) AS total_orders,
              COUNTIF(UPPER(event_code) = '${PickTaskEventCode.Created.toUpperCase()}') AS orders_in_created
            FROM orders_with_status
            WHERE rn=1
          )
          SELECT 
            COALESCE(SAFE_DIVIDE((total_orders - orders_in_created), total_orders) * 100, 0) AS projectedOrderFulfillmentPercentage
          FROM status_counts;
      `;

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    // execute the query and return the results
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);
    if (!rows.length || !rows[0].projectedOrderFulfillmentPercentage) {
      throw IctError.noContent();
    }

    const projectedOrderFulfillmentPercentageData: ProjectedOrderFulfillment = {
      projectedOrderFulfillmentPercentage:
        rows[0].projectedOrderFulfillmentPercentage,
    };

    return projectedOrderFulfillmentPercentageData;
  }

  async getProjectedFacilityOrderFulfillment(
    startDate: string,
    endDate: string,
  ): Promise<ProjectedOrderFulfillment> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const goldFacilityOrderLine = bigQueryDb.getEdpFullTablePath(
      'gold_facility_order_line',
    );
    const goldInventorySnapshot = bigQueryDb.getEdpFullTablePath(
      'gold_inventory_snapshot',
    );

    // Projected Order Fulfillment = percent of orders with sufficient facility inventory to fully satisfy requested order lines
    // % of orders where requested_quantity >= available_quantity
    const sql = `
      WITH skus_requested AS (
        SELECT
          sku_requested as sku_code,
          SUM(COALESCE(quantity_requested, 0)) as quantity_requested,
        FROM ${goldFacilityOrderLine}
        WHERE
          event_timestamp_utc BETWEEN @startDate AND @endDate 
          AND UPPER(event_code) = "RECEIVED"
        GROUP BY sku_requested
      ),
      inventory_timestamp AS (
        SELECT MAX(snapshot_timestamp_utc) AS latest_snapshot
        FROM ${goldInventorySnapshot}
        WHERE snapshot_timestamp_utc >= TIMESTAMP_SUB(CURRENT_TIMESTAMP, INTERVAL 2 DAY)
      ),
      -- get inventory quantity of each sku from the latest snapshot
      inventory_quantities AS (
        SELECT
          sku_code,
          SUM(COALESCE(total_inventory_qty, 0)) as quantity_available
        FROM ${goldInventorySnapshot}
        WHERE 
          snapshot_timestamp_utc > TIMESTAMP_SUB(CURRENT_TIMESTAMP, INTERVAL 2 DAY)
          AND snapshot_timestamp_utc = (SELECT latest_snapshot FROM inventory_timestamp)
          AND sku_code IN ( SELECT sku_code FROM skus_requested )
        GROUP BY sku_code
      ),
      fulfillment_by_sku AS (
        SELECT
          r.sku_code,
          r.quantity_requested,
          i.quantity_available,
          -- max fulfillment percentage of 100
          COALESCE(LEAST(SAFE_DIVIDE(i.quantity_available, r.quantity_requested) * 100, 100), 0) as fulfillment_percentage,
        FROM skus_requested r
        LEFT JOIN inventory_quantities i
          ON r.sku_code = i.sku_code
      ),
      final_fulfillment AS (
        SELECT
          AVG(fulfillment_percentage) as projectedOrderFulfillmentPercentage
        FROM fulfillment_by_sku
      )

      SELECT * from final_fulfillment
    `;

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    // execute the query and return the results
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);
    if (!rows.length || !rows[0].projectedOrderFulfillmentPercentage) {
      throw IctError.noContent();
    }

    const projectedOrderFulfillmentPercentageData: ProjectedOrderFulfillment = {
      projectedOrderFulfillmentPercentage:
        rows[0].projectedOrderFulfillmentPercentage,
    };

    return projectedOrderFulfillmentPercentageData;
  }

  async getOrderPerformanceFulfillment(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    startDate: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    endDate: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    departmentFilter: string,
  ): Promise<OrderPerformanceFulfillmentQueryResponse> {
    // TODO: Implement call to DB to get fulfillment data: https://jira.dematic.com/browse/ICT-2175
    // TODO2: Remove skip on test once query is written
    return Promise.resolve({
      orderFulfillment: 45,
    });
  }

  async getUnitsRemaining(
    startDate: string,
    endDate: string,
  ): Promise<UnitsRemainingQueryResponse> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const eventCompleteCodes = await this.getOrderCompletedCodes();

    const pickOrderTask =
      this.dbProvider.bigQuery.getEdpFullTablePath('gold_pick_task');

    const sql = `
        WITH timeFiltered AS (
          SELECT
            pick_task_code,
            event_code,
            -- Used for getting the most recent pick task event
            ROW_NUMBER() OVER (PARTITION BY pick_task_code ORDER BY event_timestamp_utc DESC) AS recency,
          FROM \`${pickOrderTask}\`
          WHERE event_timestamp_utc BETWEEN @startDate AND @endDate
        ),
        mostRecentPicks AS (
          SELECT
            pick_task_code,
            event_code,
          FROM timeFiltered
          WHERE recency = 1
        ),
        unfinishedPicks AS (
          SELECT *
          FROM mostRecentPicks
          WHERE UPPER(event_code) NOT IN UNNEST(@eventCompleteCodes)
        )
        SELECT COUNT(*) AS unitsRemaining FROM unfinishedPicks;
      `;

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
        eventCompleteCodes,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length || !rows[0].unitsRemaining) {
      throw IctError.noContent();
    }

    return rows[0] as UnitsRemainingQueryResponse;
  }

  async getOrderFulfillmentOutstanding(
    startDate: string,
    endDate: string,
  ): Promise<OrdersOutstandingData> {
    try {
      const bigQueryDb = this.dbProvider.bigQuery;

      const platinumOrderCountTable =
        this.dbProvider.bigQuery.getEdpFullTablePath(
          'platinum_order_count_snapshots',
        );

      const sql = `
          SELECT 
            incompleted_orders 
          FROM \`${platinumOrderCountTable}\`
          WHERE etl_create_timestamp_utc BETWEEN TIMESTAMP(@startDate) AND TIMESTAMP(@endDate)
          ORDER BY etl_create_timestamp_utc DESC
          LIMIT 1;
        `;

      const sqlOptions: Query = {
        query: sql,
        params: {
          startDate,
          endDate,
        },
      };

      const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);
      if (!rows.length) {
        throw IctError.noContent();
      }
      const result: OrdersOutstandingData = {
        incompletedTotal: rows[0].incompleted_orders,
      };
      return result;
    } catch (error) {
      if (error instanceof IctError) {
        // don't rethrow if it's already an IctError
        throw error;
      }
      throw IctError.internalServerError(
        'Failed to retrieve orders outstanding data',
        error,
      );
    }
  }

  async getOrderFacilityOutstanding(
    startDate: string,
    endDate: string,
  ): Promise<OrdersOutstandingData> {
    try {
      const bigQueryDb = this.dbProvider.bigQuery;

      const goldFacilityOrderTable =
        this.dbProvider.bigQuery.getEdpFullTablePath('gold_facility_order');

      const sql = `
        WITH latest_events AS (
          SELECT
            tenant_id,
            facility_id,
            source_system,
            facility_order_code,
            event_code,
            ROW_NUMBER() OVER (PARTITION BY facility_order_code ORDER BY event_timestamp_utc DESC) as rn
          FROM \`${goldFacilityOrderTable}\`
          WHERE event_timestamp_utc BETWEEN TIMESTAMP(@startDate) AND TIMESTAMP(@endDate)
        ),
        status_summary AS (
          SELECT
            tenant_id,
            facility_id,
            source_system,
            facility_order_code,
            CASE
              WHEN UPPER(event_code) IN ('COMPLETE') THEN 'Completed'
              ELSE 'Incompleted'
            END AS order_status
          FROM latest_events
          WHERE rn = 1
        )
        SELECT
          COUNTIF(order_status = 'Incompleted') AS incompleted_orders
        FROM status_summary
      `;

      const sqlOptions: Query = {
        query: sql,
        params: {
          startDate,
          endDate,
        },
      };

      const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);
      if (!rows.length) {
        throw IctError.noContent();
      }
      const result: OrdersOutstandingData = {
        incompletedTotal: rows[0].incompleted_orders,
      };
      return result;
    } catch (error) {
      if (error instanceof IctError) {
        // don't rethrow if it's already an IctError
        throw error;
      }
      throw IctError.internalServerError(
        'Failed to retrieve facility orders outstanding data',
        error,
      );
    }
  }

  async getOrdersPickCycleCount(
    startDate: Date,
    endDate: Date,
  ): Promise<OrdersPickCycleCountData> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const goldPick = bigQueryDb.getEdpFullTablePath('gold_pick');

    const sql = `
        SELECT 
          COUNT(*) as cycleCount
        FROM \`${goldPick}\`
        WHERE event_timestamp_utc BETWEEN @startDate AND @endDate
          AND UPPER(work_type_code) IN ('FREECYCLECOUNT', 'CYCLECOUNT', 'INSPECTION')
      `;

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    return rows[0] as OrdersPickCycleCountData;
  }

  /**
   * Utility to retrieve the app config setting for the tenant/user to determine which event codes shall be
   * used to determine if an order is completed or not.
   * NOTE - if the config setting doesn't exist or doesn't have proper data, then a default list is returned
   * @returns an array of strings representing the codes that signify that an order is complete.
   */
  public async getOrderCompletedCodes(): Promise<string[]> {
    const defaultReturnValue = [
      PickTaskEventCode.Completed,
      PickTaskEventCode.PickComplete,
      'COMPLETE', // this should go away once it's handled in EDP canonicalization
    ].map(c => c.toUpperCase());

    const orderCompletedCodesSetting =
      await this.configStore.findMostRelevantSettingForUser(
        this.context.userId,
        undefined,
        'order-complete-event-codes',
      );

    if (orderCompletedCodesSetting?.value) {
      const orderCompletedCodes = (
        orderCompletedCodesSetting.value as {codes: string[]}
      ).codes;

      if (orderCompletedCodes && orderCompletedCodes.length > 0)
        return orderCompletedCodes.map(c => c.toUpperCase());
    }

    return defaultReturnValue;
  }
}
