import {HealthStatus} from 'ict-api-foundations';

export function getOrderProgressStatus(orderProgressPercentage: number) {
  if (orderProgressPercentage < 0.35) {
    return HealthStatus.Warning;
  }
  if (orderProgressPercentage < 0.5) {
    return HealthStatus.Critical;
  }
  if (orderProgressPercentage < 0.65) {
    return HealthStatus.Caution;
  }
  return HealthStatus.Ok;
}

export function getOrderLineProgressStatus(orderProgressPercentage: number) {
  if (orderProgressPercentage < 0.35) {
    return HealthStatus.Warning;
  }
  if (orderProgressPercentage < 0.5) {
    return HealthStatus.Critical;
  }
  if (orderProgressPercentage < 0.65) {
    return HealthStatus.Caution;
  }
  return HealthStatus.Ok;
}

export function getAreasCycleTimeStatus(timeDifference: number) {
  if (timeDifference > 60) {
    return HealthStatus.Critical;
  }
  if (timeDifference > 0) {
    return HealthStatus.Caution;
  }
  return HealthStatus.Ok;
}

export function getOrderThroughputStatus(throughputRateLinesPerHour: number) {
  if (throughputRateLinesPerHour > 1200) {
    return HealthStatus.Ok;
  }
  if (throughputRateLinesPerHour > 900) {
    return HealthStatus.Caution;
  }
  if (throughputRateLinesPerHour > 600) {
    return HealthStatus.Warning;
  }
  return HealthStatus.Critical;
}

export function getOrderLineProgressAreasStatus(
  orderLineProgress: number
): HealthStatus {
  // Return type should be HealthStatus
  if (orderLineProgress >= 80) {
    return HealthStatus.Ok;
  }
  if (orderLineProgress >= 50) {
    return HealthStatus.Caution;
  }
  return HealthStatus.Critical;
}

export function getProjectedOrderFulfillmentStatus(
  projectedOrderFulfillmentPercentage: number
) {
  // TODO: Define bounds of status return
  // This is a placeholder implementation
  if (projectedOrderFulfillmentPercentage > 80) return HealthStatus.Ok;
  if (projectedOrderFulfillmentPercentage > 70) return HealthStatus.Caution;
  return HealthStatus.Critical;
}

// determines the status based on the time difference
export function getEstimatedCompletionOrderStatus(
  timeDifferenceInSeconds: number
) {
  const timeDifferenceInMinutes = Math.abs(timeDifferenceInSeconds / 60);
  if (timeDifferenceInMinutes < 0) {
    return 'ok';
  }
  if (timeDifferenceInMinutes <= 60) {
    // less than or equal to 1 hour late
    return 'caution';
  }
  if (timeDifferenceInMinutes <= 120) {
    // less than or equal to 2 hours late
    return 'warning';
  }
  return 'critical';
}

export function getOrderShippedChange(current: number, past: number) {
  return ((current - past) / past) * 100;
}

export function getOrderShippedStatus(change: number) {
  if (change < -50) {
    return 'critical';
  }
  if (change < 0) {
    return 'caution';
  }
  return 'ok';
}
