import {BigQueryTimestamp} from '@google-cloud/bigquery';
import {
  ApiResponse,
  BaseChartConfig,
  ChartSeriesData,
} from 'ict-api-foundations';

export interface OrderCycleTimeChartQueryResponse {
  event_hour: BigQueryTimestamp;
  average_cycletime_seconds: number;
}

export type OrderCycleTimeChartData = {
  orderCycleTimeChart: ChartSeriesData[];
};

export type OrderCycleTimeChartApiResponse = ApiResponse<
  OrderCycleTimeChartData,
  BaseChartConfig
>;
