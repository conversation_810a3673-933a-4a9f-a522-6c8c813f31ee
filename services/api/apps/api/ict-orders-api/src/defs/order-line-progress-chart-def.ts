import {
  ApiResponse,
  BaseChartConfig,
  ChartSeriesData,
} from 'ict-api-foundations';

// DTO that describes the order line progress chart data returned by the query.
export interface OrderLineProgressChartQueryData {
  // Timestamp at the top of the hour for the chart data.
  hour: string;
  // Number of quantity picked for th.e hour.
  quantityPicked: number;
  // Current running total of quantity picked incrementing by the hour.
  runningTotal: number;
  // Target quantity needed to be picked for the day.
  quantityTarget: number;
}

export type OrderLineProgressSeriesData = {
  progress: ChartSeriesData[];
  trend: ChartSeriesData[];
};

// Describes the contract for the data returned by the api.
export type OrderLineProgressChartApiResponse = ApiResponse<
  OrderLineProgressSeriesData,
  BaseChartConfig
>;
