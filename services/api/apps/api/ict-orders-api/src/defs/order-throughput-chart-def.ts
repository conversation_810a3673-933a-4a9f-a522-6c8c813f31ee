import {BigQueryTimestamp} from '@google-cloud/bigquery';
import {
  ApiResponse,
  BaseChartConfig,
  ChartSeriesData,
} from 'ict-api-foundations';

/** DTO that describes the order throughput chart data returned by the query. */
export interface OrderThroughputChartQueryData {
  /** Time the event happened, to the hour. */
  event_hour: BigQueryTimestamp;
  /** Number of completed orderlines. */
  completed_orders: number;
}
export interface OrderCustomerThroughputChartQueryData {
  /** Time the event happened, to the hour. */
  event_interval: BigQueryTimestamp;
  /** Number of completed orderlines. */
  completed_orders: number;
}

export type OrderThroughputChartSeriesResponseData = {
  throughput: ChartSeriesData[];
};

/** Describes the contract for the data returned by the api. */
export type OrderThroughputChartApiResponse = ApiResponse<
  OrderThroughputChartSeriesResponseData,
  BaseChartConfig
>;
