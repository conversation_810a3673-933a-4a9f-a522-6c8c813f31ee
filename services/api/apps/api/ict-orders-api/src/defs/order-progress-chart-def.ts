import {ChartSeriesData} from 'ict-api-foundations';

// DTO that describes the order progress chart data returned by the query.
export interface OrderProgressSeriesQueryData {
  // Timestamp at the top of the hour for the chart data.
  hour: string;
  // quantity of total orders this timeframe.
  totalOrders: number;
  // Running quantity of complete orders up to this hour.
  runningCompletedTotal: number;
}

export type OrderCustomerLineProgressSeriesData = {
  progress: ChartSeriesData[];
};

export type OrderProgressSeriesData = {
  progress: ChartSeriesData[];
};
