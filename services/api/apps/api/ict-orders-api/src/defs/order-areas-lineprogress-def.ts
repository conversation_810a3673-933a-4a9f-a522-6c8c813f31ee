import {FacilityArea} from 'ict-api-foundations';

/**
 * DTO that defines the relevant details for the current orderline progress per area.
 */
export interface OrderAreasLineProgress {
  orderLineProgress: number;
  orderLinesCompleted: number;
  totalOrderLines: number;
}

// Extend the FacilityArea to inject the orderline progress object

export interface LineProgressFacilityArea extends FacilityArea {
  /**
   * Orderline progress area data
   */
  orderLines: OrderAreasLineProgress;
}

export interface OrderLineProgressAreasData {
  areas: LineProgressFacilityArea[];
}
