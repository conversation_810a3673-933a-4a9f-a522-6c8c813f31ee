import {FacilityArea, HealthStatus} from 'ict-api-foundations';

export interface OrderCycleTimeArea {
  averageCycleTimeSeconds: number;
  slowestCycleTimeSeconds: number;
  fastestCycleTimeSeconds: number;
  targetCycleTimeSeconds: number;
  slowestRecCycleTimeSeconds: number;
  completionTime: string;
  status?: HealthStatus;
}

export interface CycletimeFacilityArea extends FacilityArea {
  /**
   * Order cycle time area data
   */
  cycleTime: OrderCycleTimeArea;
}

export interface CycleTimeAreasConfig {
  targetTime: string;
}

export interface AreaCycleTimeResponse {
  data: {
    areas: CycletimeFacilityArea[];
  };
  config: CycleTimeAreasConfig;
}
