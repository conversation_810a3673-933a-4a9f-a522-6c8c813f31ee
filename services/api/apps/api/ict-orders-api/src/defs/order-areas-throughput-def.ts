import {FacilityArea, HealthStatus} from 'ict-api-foundations';

/**
 * DTO that describes the order throughput data by area.
 */

export interface ThroughputAreaData {
  throughputRate: number;
  maxThroughputCapacity?: number;
  minThroughputTarget?: number;
  maxThroughputTarget?: number;
  status?: HealthStatus;
}
export interface ThroughputFacilityArea extends FacilityArea {
  /**
   * Order throughput area data
   */
  throughput: ThroughputAreaData;
}

export interface AreaThroughputResponse {
  data: {
    areas: ThroughputFacilityArea[];
  };
}
