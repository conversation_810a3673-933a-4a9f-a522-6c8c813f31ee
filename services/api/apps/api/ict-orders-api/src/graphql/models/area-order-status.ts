// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {Field, ObjectType} from 'type-graphql';

@ObjectType()
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export class AreaOrderStatus {
  @Field(() => String)
  name = '';

  @Field(() => Number)
  total = 0;

  @Field(() => Number)
  current = 0;

  @Field(() => Number)
  operators = 0;

  @Field(() => Number, {name: 'low_limit'})
  lowLimit = 0;

  @Field(() => Number, {name: 'high_limit'})
  highLimit = 0;

  @Field(() => String, {name: 'range_status'})
  rangeStatus = '';

  @Field(() => String)
  status = '';

  @Field(() => String)
  message = '';
}
