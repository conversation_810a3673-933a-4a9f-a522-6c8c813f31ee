// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {Ctx, Arg, Query, Resolver} from 'type-graphql';
import {GraphQLContext} from 'ict-api-foundations';
import {AreaOrderStatus} from '../models/area-order-status.ts';

@Resolver()
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export class OrderResolver {
  @Query(() => [AreaOrderStatus])
  areaOrderStatuses_poc(
    @Ctx() context: GraphQLContext,
    @Arg('names', () => [String], {nullable: true}) names?: string[]
  ): AreaOrderStatus[] {
    return [
      {
        name: 'area1',
        total: 100,
        current: 50,
        operators: 10,
        lowLimit: 10,
        highLimit: 90,
        rangeStatus: 'warning',
        status: 'bad',
        message: `User id: ${context.user.id} | User dataset: ${context.user.dataset}`,
      },
      {
        name: 'area2',
        total: 100,
        current: 50,
        operators: 10,
        lowLimit: 10,
        highLimit: 90,
        rangeStatus: 'normal',
        status: 'warning',
        message: `User id: ${context.user.id} | User dataset: ${context.user.dataset}`,
      },
      {
        name: 'area3',
        total: 100,
        current: 50,
        operators: 10,
        lowLimit: 10,
        highLimit: 90,
        rangeStatus: 'bad',
        status: 'normal',
        message: `User id: ${context.user.id} | User dataset: ${context.user.dataset}`,
      },
      {
        name: 'area4',
        total: 100,
        current: 50,
        operators: 10,
        lowLimit: 10,
        highLimit: 90,
        rangeStatus: 'warning',
        status: 'normal',
        message: `User id: ${context.user.id} | User dataset: ${context.user.dataset}`,
      },
    ].filter(areaOrderStatus => {
      if (!names) {
        return true;
      }
      return names.includes(areaOrderStatus.name);
    });
  }
}
