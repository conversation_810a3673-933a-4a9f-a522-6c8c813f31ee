import {
  ProtectedRouteMiddleware,
  ChartSeriesData,
  configPostgresDatabase,
  Container,
  startEndDateValidation,
  BaseChartConfig,
  CacheMiddleware,
  DatabaseTypes,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {OrderThroughputChartApiResponse} from '../defs/order-throughput-chart-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OrderThroughputChartController extends Controller {
  public static readonly exampleData: ChartSeriesData[] = [
    {
      name: '01',
      value: 20,
    },
    {
      name: '02',
      value: 30,
    },
  ];

  public static readonly exampleConfig: BaseChartConfig = {
    yHighBand: 30 * 0.8,
    yLowBand: 30 * 0.6,
  };

  public static readonly exampleResponse: OrderThroughputChartApiResponse = {
    throughput: OrderThroughputChartController.exampleData,
    metadata: OrderThroughputChartController.exampleConfig,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {OrderThroughputChartApiResponse} contract
   */
  @Example<OrderThroughputChartApiResponse>(
    OrderThroughputChartController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/throughput/series')
  @OperationId('GetOrdersThroughputSeries')
  @Tags('orders')
  public async getFaultsAreas(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<OrderThroughputChartApiResponse> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    const chartData = await orderService.getOrderThroughputChartData(
      startDate.toISOString(),
      endDate.toISOString()
    );

    const maxValue = chartData
      .map(c => c.value)
      .reduce((max, cur) => (cur > max ? cur : max), 0);
    const contract: OrderThroughputChartApiResponse = {
      throughput: chartData,

      metadata: {
        // TODO: Use values from a config db
        yHighBand: Math.floor(maxValue * 0.8),
        yLowBand: Math.floor(maxValue * 0.6),
      },
    };

    return contract;
  }
}
