import {
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ProtectedRouteMiddleware,
  startEndDateValidation,
  ChartSeriesData,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {FacilityOrderLineThroughputSeriesResponse} from '../defs/facility-order-line-throughput-series-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
])
export class FacilityOrderLineThroughputSeriesController extends Controller {
  public static readonly exampleData: ChartSeriesData[] = [
    {
      name: '01',
      value: 20,
    },
    {
      name: '02',
      value: 30,
    },
  ];

  public static readonly exampleResponse: FacilityOrderLineThroughputSeriesResponse =
    {
      throughput: FacilityOrderLineThroughputSeriesController.exampleData,
    };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {FacilityOrderLineThroughputSeriesResponse} contract
   */
  @Example<FacilityOrderLineThroughputSeriesResponse>(
    FacilityOrderLineThroughputSeriesController.exampleResponse,
  )
  @Get('/facility/line/throughput/series')
  @OperationId('GetOrdersFacilityLineThroughputSeries')
  @Tags('orders')
  public async getFacilityOrderLineThroughputSeries(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
  ): Promise<FacilityOrderLineThroughputSeriesResponse> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the service
    const orderService = Container.get(OrderService);

    return await orderService.getFacilityOrderLineThroughputSeries(
      startDate,
      endDate,
    );
  }
}
