import {
  ApiResponse,
  ProtectedRouteMiddleware,
  Container,
  IctError,
  startEndDateValidation,
  CacheMiddleware,
  DatabaseTypes,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {
  OrderLineProgress,
  OrderLineProgressConfig,
} from '../defs/order-line-progress-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OrderLineProgressController extends Controller {
  public static readonly exampleData: OrderLineProgress = {
    lineProgressPercent: 98.37804793582056,
  };

  public static readonly exampleConfig: OrderLineProgressConfig = {
    lowRange: 60,
    highRange: 75,
    max: 100,
  };

  public static readonly exampleResponse: ApiResponse<
    OrderLineProgress,
    OrderLineProgressConfig
  > = {
    lineProgressPercent: 98.37804793582056,
    metadata: OrderLineProgressController.exampleConfig,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @param {string} area
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {ApiResponse<OrderLineProgress, OrderLineProgressConfig>} contract
   */
  @Example<ApiResponse<OrderLineProgress, OrderLineProgressConfig>>(
    OrderLineProgressController.exampleResponse,
  )
  @SuccessResponse('200')
  @Get('/lineprogress')
  @OperationId('GetOrdersLineProgress')
  @Tags('orders')
  public async getFaultsAreas(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
  ): Promise<ApiResponse<OrderLineProgress, OrderLineProgressConfig>> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    const orderLineProgress: OrderLineProgress =
      await orderService.getOrderLineProgress(
        startDate.toISOString(),
        endDate.toISOString(),
      );

    if (orderLineProgress) {
      const contract: ApiResponse<OrderLineProgress, OrderLineProgressConfig> =
        {
          ...orderLineProgress,
          metadata: OrderLineProgressController.exampleConfig,
        };
      return contract;
    }
    throw IctError.internalServerError(
      'Unable to retrieve order line progress data.',
    );
  }
}
