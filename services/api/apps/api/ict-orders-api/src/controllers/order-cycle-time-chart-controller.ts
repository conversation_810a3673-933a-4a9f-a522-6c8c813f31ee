import {
  ProtectedRouteMiddleware,
  Container,
  startEndDateValidation,
  BaseChartConfig,
  CacheMiddleware,
  DatabaseTypes,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {
  OrderCycleTimeChartApiResponse,
  OrderCycleTimeChartData,
} from '../defs/order-cycle-time-chart-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OrderCycleTimeChartController extends Controller {
  private static readonly min: number = 23;
  private static readonly max: number = 34;

  public static readonly exampleData: OrderCycleTimeChartData = {
    orderCycleTimeChart: [
      {
        name: '00',
        value: OrderCycleTimeChartController.min,
      },
      {
        name: '01',
        value: OrderCycleTimeChartController.max,
      },
    ],
  };

  public static readonly exampleConfig: BaseChartConfig = {
    yMax: Math.floor(OrderCycleTimeChartController.max * 1.05),
    yMin: Math.floor(0),
    yHighBand: Math.floor(OrderCycleTimeChartController.max * 0.8),
    yLowBand: Math.floor(OrderCycleTimeChartController.max * 0.6),
  };

  public static readonly exampleResponse: OrderCycleTimeChartApiResponse = {
    orderCycleTimeChart: [
      {
        name: '00',
        value: OrderCycleTimeChartController.min,
      },
      {
        name: '01',
        value: OrderCycleTimeChartController.max,
      },
    ],
    metadata: OrderCycleTimeChartController.exampleConfig,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @param {string} area
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {OrderCycleTimeChartApiResponse} contract
   */
  @Example<OrderCycleTimeChartApiResponse>(
    OrderCycleTimeChartController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/cycletime/series')
  @OperationId('GetOrdersCycleTimeSeries')
  @Tags('orders')
  public async getFaultsAreas(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<OrderCycleTimeChartApiResponse> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    const chartData = await orderService.getOrderCycleTimeChartData(
      startDate.toISOString(),
      endDate.toISOString()
    );

    const maxValue = chartData.orderCycleTimeChart
      .map(cd => cd.value)
      .reduce((max: number, cur: number) => Math.max(max, cur), 0);
    const contract: OrderCycleTimeChartApiResponse = {
      ...chartData,
      metadata: {
        // Set yMax to +5% of highest value in chart, keeps everything in view
        yMax: Math.floor(maxValue * 1.05),
        yMin: 0,
        // TODO: Get these values from a config database
        yHighBand: Math.floor(maxValue * 0.8),
        yLowBand: Math.floor(maxValue * 0.6),
      },
    };

    return contract;
  }
}
