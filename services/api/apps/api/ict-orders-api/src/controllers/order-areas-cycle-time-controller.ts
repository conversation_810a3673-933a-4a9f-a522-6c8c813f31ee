import {
  ApiResponse,
  CacheMiddleware,
  Container,
  FacilityArea,
  HealthStatus,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {
  CycleTimeAreasConfig,
  CycletimeFacilityArea,
} from '../defs/order-areas-cycle-time-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OrderAreasCycleTimeController extends Controller {
  public static readonly exampleData: CycletimeFacilityArea[] = [
    {
      id: 'testArea3',
      name: 'picking',
      operators: {
        activeOperators: 3,
        lowRecOperators: 14,
        highRecOperators: 20,
      },
      cycleTime: {
        averageCycleTimeSeconds: 20,
        fastestCycleTimeSeconds: 10,
        slowestCycleTimeSeconds: 40,
        completionTime: '2021-11-28T06:34:35.551Z',
        targetCycleTimeSeconds: 2000,
        slowestRecCycleTimeSeconds: 130000,
        status: HealthStatus.Ok,
      },
      alertStatus: {
        status: HealthStatus.Ok,
      },
    },
    {
      id: 'testArea1',
      name: 'shipping',
      operators: {
        activeOperators: 3,
        lowRecOperators: 14,
        highRecOperators: 20,
      },
      cycleTime: {
        averageCycleTimeSeconds: 30,
        fastestCycleTimeSeconds: 10,
        slowestCycleTimeSeconds: 40,
        completionTime: '2021-11-28T06:35:07.278Z',
        targetCycleTimeSeconds: 2000,
        slowestRecCycleTimeSeconds: 130000,
        status: HealthStatus.Ok,
      },
      alertStatus: {
        status: HealthStatus.Ok,
      },
    },
  ];

  public static readonly exampleConfig: CycleTimeAreasConfig = {
    targetTime: 'T2:00:00',
  };

  public static readonly exampleResponse: ApiResponse<
    {areas: CycletimeFacilityArea[]},
    CycleTimeAreasConfig
  > = {
    areas: OrderAreasCycleTimeController.exampleData,
    metadata: OrderAreasCycleTimeController.exampleConfig,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {ApiResponse<{areas: FacilityArea[]}, CycleTimeAreasConfig>} contract
   */
  @Example<ApiResponse<{areas: FacilityArea[]}, CycleTimeAreasConfig>>(
    OrderAreasCycleTimeController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/cycletime/areas')
  @OperationId('GetOrdersCycleTime')
  @Tags('orders')
  public async getFaultsAreas(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<ApiResponse<{areas: FacilityArea[]}, CycleTimeAreasConfig>> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    const areas = await orderService.getAreasCycleTime(
      startDate.toISOString(),
      endDate.toISOString()
    );

    // Generate your configuration data.
    const config: CycleTimeAreasConfig = {
      targetTime: 'T2:00:00',
    };

    // Return an ApiResponse with the AreaCycleTimeResponse structure
    return {
      areas,
      metadata: config,
    };
  }
}
