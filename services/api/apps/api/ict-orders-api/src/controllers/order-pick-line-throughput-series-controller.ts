import {
  CacheMiddleware,
  ProtectedRouteMiddleware,
  ChartSeriesData,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {OrderPickLineThroughputSeriesData} from '../defs/order-line-throughput-series-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OrderPickLineThroughputSeriesController extends Controller {
  public static readonly exampleData: ChartSeriesData[] = [
    {
      name: '01',
      value: 20,
    },
    {
      name: '02',
      value: 30,
    },
  ];

  public static readonly exampleResponse: OrderPickLineThroughputSeriesData = {
    throughput: OrderPickLineThroughputSeriesController.exampleData,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {OrderPickLineThroughputSeriesData} contract
   */
  @Example<OrderPickLineThroughputSeriesData>(
    OrderPickLineThroughputSeriesController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/pick/line/throughput/series')
  @OperationId('GetOrdersPickLineThroughputSeries')
  @Tags('orders')
  public async getOrderPickLineThroughputSeries(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<OrderPickLineThroughputSeriesData> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the service
    const orderService = Container.get(OrderService);

    const seriesData = await orderService.getOrderPickLineThroughputSeries(
      startDate.toISOString(),
      endDate.toISOString()
    );

    return {
      throughput: seriesData,
    };
  }
}
