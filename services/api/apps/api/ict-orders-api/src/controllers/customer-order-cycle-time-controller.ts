import {
  ProtectedRouteMiddleware,
  Container,
  startEndDateValidation,
  CacheMiddleware,
  WMSCustomerOrderArea,
  WMSCustomerOrderStatus,
  DatabaseTypes,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {OrderCycleTime} from '../defs/order-cycle-time-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class CustomerOrderCycleTimeController extends Controller {
  private static readonly exampleOrderCycleTimeMinutes = 208;

  public static readonly exampleData: OrderCycleTime = {
    orderCycleTimeMinutes:
      CustomerOrderCycleTimeController.exampleOrderCycleTimeMinutes,
  };

  public static readonly exampleResponse: OrderCycleTime = {
    orderCycleTimeMinutes:
      CustomerOrderCycleTimeController.exampleOrderCycleTimeMinutes,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {OrderCycleTime} contract
   */
  @Example<OrderCycleTime>(CustomerOrderCycleTimeController.exampleResponse)
  @SuccessResponse('200')
  @Get('/customer/cycletime')
  @OperationId('GetOrdersCustomerCycleTime')
  @Tags('orders')
  public async getCustomerOrderCycleTime(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
    @Query('area')
    area?: WMSCustomerOrderArea
  ): Promise<OrderCycleTime> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    let status: WMSCustomerOrderStatus | undefined;
    if (area === WMSCustomerOrderArea.Shipping) {
      status = WMSCustomerOrderStatus.Shipped;
    } else if (area === WMSCustomerOrderArea.Picking) {
      status = WMSCustomerOrderStatus.Picked;
    }

    const orderCycleTime: OrderCycleTime =
      await orderService.getCustomerOrderCycleTime(
        startDate.toISOString(),
        endDate.toISOString(),
        status
      );

    return {
      orderCycleTimeMinutes: orderCycleTime.orderCycleTimeMinutes,
    };
  }
}
