import {
  Container,
  ProtectedRouteMiddleware,
  startEndDateValidation,
  WMSCustomerOrderArea,
  WMSCustomerOrderStatus,
  DatabaseTypes,
  configPostgresDatabase,
  CacheMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  OperationId,
  Query,
  Route,
  SuccessResponse,
  Tags,
} from 'tsoa';
import {
  OrderCustomerLineProgressSeriesData,
  OrderProgressSeriesData,
} from '../defs/order-progress-chart-def.ts';
import {OrderService} from '../services/order-service.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class CustomerOrderProgressSeriesController extends Controller {
  static readonly exampleData: OrderCustomerLineProgressSeriesData = {
    progress: [
      {
        name: '2024-04-01T00:00:00Z',
        value: 82.3,
      },
      {
        name: '2024-04-01T01:00:00Z',
        value: 82.1,
      },
      {
        name: '2024-04-01T02:00:00Z',
        value: 81.5,
      },
      {
        name: '2024-04-01T03:00:00Z',
        value: 83.7,
      },
    ],
  };

  static readonly exampleResponse: OrderProgressSeriesData =
    CustomerOrderProgressSeriesController.exampleData;

  /**
   * Gets a series of order progress percentage, averaging by each hour incrementing.
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {OrderProgressSeriesData} contract
   */
  @Example<OrderProgressSeriesData>(
    CustomerOrderProgressSeriesController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/customer/progress/series')
  @OperationId('GetOrdersCustomerProgressSeries')
  @Tags('orders')
  public async getCustomerOrderProgressSeries(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
    @Query('area') area?: WMSCustomerOrderArea
  ): Promise<OrderProgressSeriesData> {
    startEndDateValidation(startDate, endDate);

    let status: WMSCustomerOrderStatus | undefined;
    if (area === WMSCustomerOrderArea.Shipping) {
      status = WMSCustomerOrderStatus.Shipped;
    } else if (area === WMSCustomerOrderArea.Picking) {
      status = WMSCustomerOrderStatus.Picked;
    }

    const orderService = Container.get(OrderService);

    return await orderService.getCustomerOrderProgressSeries(
      startDate,
      endDate,
      status
    );
  }
}
