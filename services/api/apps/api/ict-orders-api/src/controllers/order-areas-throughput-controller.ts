import {
  CacheMiddleware,
  Container,
  HealthStatus,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {ThroughputFacilityArea} from '../defs/order-areas-throughput-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OrderAreasThroughputController extends Controller {
  public static readonly exampleData: ThroughputFacilityArea[] = [
    {
      id: 'testArea4',
      name: 'Shipping',
      operators: {
        activeOperators: 30,
        lowRecOperators: 25,
        highRecOperators: 31,
      },
      alertStatus: {
        status: HealthStatus.Critical,
      },
      throughput: {
        throughputRate: 2082,
        maxThroughputCapacity: 1100,
        minThroughputTarget: 800,
        maxThroughputTarget: 1000,
      },
    },
    {
      id: 'testArea3',
      name: 'Packing',
      operators: {
        activeOperators: 28,
        lowRecOperators: 19,
        highRecOperators: 35,
      },
      alertStatus: {
        status: HealthStatus.Ok,
      },
      throughput: {
        throughputRate: 1907,
        maxThroughputCapacity: 1100,
        minThroughputTarget: 800,
        maxThroughputTarget: 1000,
      },
    },
    {
      id: 'testArea1',
      name: 'Picking',
      operators: {
        activeOperators: 14,
        lowRecOperators: 19,
        highRecOperators: 37,
      },
      alertStatus: {
        status: HealthStatus.Ok,
      },
      throughput: {
        throughputRate: 1778,
        maxThroughputCapacity: 1100,
        minThroughputTarget: 800,
        maxThroughputTarget: 1000,
      },
    },
    {
      id: 'testArea2',
      name: 'Put Wall',
      operators: {
        activeOperators: 54,
        lowRecOperators: 32,
        highRecOperators: 34,
      },
      alertStatus: {
        status: HealthStatus.Caution,
      },
      throughput: {
        throughputRate: 954,
        maxThroughputCapacity: 1100,
        minThroughputTarget: 800,
        maxThroughputTarget: 1000,
      },
    },
  ];

  public static readonly exampleResponse: {
    areas: ThroughputFacilityArea[];
  } = {
    areas: OrderAreasThroughputController.exampleData,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @param {string} area
   * @isDateTime start_date
   * @isDateTime end_date
   * @isString area
   * @returns {{areas: ThroughputFacilityArea[]}} contract
   */
  @Example<{areas: ThroughputFacilityArea[]}>(
    OrderAreasThroughputController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/throughput/areas')
  @OperationId('GetOrdersThroughputAreas')
  @Tags('orders')
  public async getFaultsAreas(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
    @Query('area') area?: string
  ): Promise<{areas: ThroughputFacilityArea[]}> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    // Generate your configuration data.
    return {
      areas: await orderService.getAreasThroughput(
        startDate.toISOString(),
        endDate.toISOString(),
        area
      ),
    };
  }
}
