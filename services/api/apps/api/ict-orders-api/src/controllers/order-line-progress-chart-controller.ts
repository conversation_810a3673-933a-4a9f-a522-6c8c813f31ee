import {
  ProtectedRouteMiddleware,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  startEndDateValidation,
  BaseChartConfig,
  CacheMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {
  OrderLineProgressChartApiResponse,
  OrderLineProgressSeriesData,
} from '../defs/order-line-progress-chart-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OrderLineProgressChartController extends Controller {
  public static readonly exampleData: OrderLineProgressSeriesData = {
    progress: [
      {name: '04', value: 782},
      {name: '05', value: 1535},
    ],
    trend: [
      {name: '05', value: 1535},
      {name: '06', value: 2302},
      {name: '07', value: 3069},
    ],
  };

  public static readonly exampleConfig: BaseChartConfig = {
    yMin: 0,
    yMax: 3375,
  };

  public static readonly exampleResponse: OrderLineProgressChartApiResponse = {
    progress: [
      {name: '04', value: 782},
      {name: '05', value: 1535},
    ],
    trend: [
      {name: '05', value: 1535},
      {name: '06', value: 2302},
      {name: '07', value: 3069},
    ],
    metadata: OrderLineProgressChartController.exampleConfig,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @param {string} area
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {OrderLineProgressChartApiResponse} contract
   */
  @Example<OrderLineProgressChartApiResponse>(
    OrderLineProgressChartController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/lineprogress/series')
  @OperationId('GetOrdersLineProgressSeries')
  @Tags('orders')
  public async getFaultsAreas(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<OrderLineProgressChartApiResponse> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    const chartData = await orderService.getOrderLineProgressChartData(
      startDate.toISOString(),
      endDate.toISOString()
    );

    const maximum = (max: number, cur: number) => (max > cur ? max : cur);
    const maxSeriesValue = chartData.progress
      .map(c => c.value)
      .reduce(maximum, 0);
    const maxTrendValue = chartData.trend.map(c => c.value).reduce(maximum, 0);
    const contract: OrderLineProgressChartApiResponse = {
      ...chartData,
      metadata: {
        yMin: 0,
        yMax: Math.floor(Math.max(maxSeriesValue, maxTrendValue) * 1.1),
      },
    };

    return contract;
  }
}
