import {
  ProtectedRouteMiddleware,
  Container,
  startEndDateValidation,
  CacheMiddleware,
  DatabaseTypes,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {UnitsRemainingData} from '../defs/order-remaining-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OrderRemainingController extends Controller {
  public static readonly exampleData: UnitsRemainingData = {
    unitsRemaining: 1337,
  };

  public static readonly exampleResponse: UnitsRemainingData =
    OrderRemainingController.exampleData;

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {UnitsRemainingData} contract
   */
  @Example<UnitsRemainingData>(OrderRemainingController.exampleResponse)
  @SuccessResponse('200')
  @Get('/remaining')
  @OperationId('GetOrdersRemaining')
  @Tags('orders')
  public async getFaultsAreas(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<UnitsRemainingData> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    return await orderService.getUnitsRemaining(
      startDate.toISOString(),
      endDate.toISOString()
    );
  }
}
