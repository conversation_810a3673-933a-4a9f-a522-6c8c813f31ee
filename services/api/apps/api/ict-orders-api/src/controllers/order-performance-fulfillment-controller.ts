import {
  ProtectedRouteMiddleware,
  Container,
  startEndDateValidation,
  CacheMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {OrderPerformanceFulfillmentData} from '../defs/order-performance-fulfillment-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OrderPerformanceFulfillmentController extends Controller {
  public static readonly exampleData: {
    orderFulfillment: number;
  } = {
    orderFulfillment: 34,
  };

  public static readonly exampleResponse: {orderFulfillment: number} =
    OrderPerformanceFulfillmentController.exampleData;

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @param {string} area
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {orderFulfillment: number} contract
   */
  @Example<{orderFulfillment: number}>(
    OrderPerformanceFulfillmentController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/performance/fulfillment')
  @OperationId('GetOrdersPerformanceFulfillment')
  @Tags('orders')
  public async getFaultsAreas(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
    @Query('department_filter') departmentFilter: string
  ): Promise<OrderPerformanceFulfillmentData> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    return await orderService.getOrderPerformanceFulfillment(
      startDate.toISOString(),
      endDate.toISOString(),
      departmentFilter
    );
  }
}
