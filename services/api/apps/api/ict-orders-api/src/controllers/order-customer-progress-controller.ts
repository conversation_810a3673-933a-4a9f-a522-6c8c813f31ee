import {
  ApiResponse,
  ProtectedRouteMiddleware,
  Container,
  startEndDateValidation,
  CacheMiddleware,
  DatabaseTypes,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {
  OrderProgress,
  OrderProgressConfig,
} from '../defs/order-progress-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OrderProgressController extends Controller {
  static readonly exampleData: OrderProgress = {
    orderProgressPercentage: 35,
  };

  static readonly exampleConfig: OrderProgressConfig = {
    seg1: 40,
    seg2: 50,
    seg3: 60,
  };

  static readonly exampleResponse: ApiResponse<
    OrderProgress,
    OrderProgressConfig
  > = {
    orderProgressPercentage: 35,
    metadata: OrderProgressController.exampleConfig,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @param {string} area
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {ApiResponse<OrderProgress, OrderProgressConfig>} contract
   */
  @Example<ApiResponse<OrderProgress, OrderProgressConfig>>(
    OrderProgressController.exampleResponse,
  )
  @SuccessResponse('200')
  @Get('/progress')
  @OperationId('GetOrdersProgress')
  @Tags('orders')
  public async getFaultsAreas(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
  ): Promise<ApiResponse<OrderProgress, OrderProgressConfig>> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    // retrieve order progress percentage from the service
    const orderProgress = await orderService.getOrderProgress(
      startDate.toISOString(),
      endDate.toISOString(),
    );

    const contract: ApiResponse<OrderProgress, OrderProgressConfig> = {
      ...orderProgress,
      metadata: OrderProgressController.exampleConfig,
    };
    return contract;
  }
}
