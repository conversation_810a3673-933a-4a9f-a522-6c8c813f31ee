import {
  ApiResponse,
  CacheMiddleware,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  IctError,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {OrdersOutstandingData} from '../defs/orders-outstanding-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OrderFulfillmentOutstandingController extends Controller {
  public static readonly exampleData: OrdersOutstandingData = {
    incompletedTotal: 28,
  };

  public static readonly exampleConfig = {};

  public static readonly exampleResponse: ApiResponse<
    OrdersOutstandingData,
    {}
  > = {
    incompletedTotal: 28,
    metadata: OrderFulfillmentOutstandingController.exampleConfig,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @param {string} area
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {ApiResponse<OrderOutstanding, OrderOutstandingConfig>} contract
   */
  @Example<ApiResponse<OrdersOutstandingData, {}>>(
    OrderFulfillmentOutstandingController.exampleResponse,
  )
  @SuccessResponse('200')
  @Get('/fulfillment-outstanding')
  @OperationId('GetOrdersFulfillmentOutstanding')
  @Tags('orders')
  public async getOrdersFulfillmentOutstanding(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
  ): Promise<ApiResponse<OrdersOutstandingData, {}>> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    const ordersOutstanding =
      await orderService.getOrdersFulfillmentOutstanding(
        startDate.toISOString(),
        endDate.toISOString(),
      );

    if (ordersOutstanding) {
      const contract: ApiResponse<OrdersOutstandingData, {}> = {
        ...ordersOutstanding,
        metadata: OrderFulfillmentOutstandingController.exampleConfig,
      };
      return contract;
    }
    throw IctError.internalServerError(
      'Unable to retrieve order line progress data.',
    );
  }
}
