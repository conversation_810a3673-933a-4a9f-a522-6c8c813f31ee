import {
  ProtectedRouteMiddleware,
  Container,
  startEndDateValidation,
  DatabaseTypes,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {OrderLineProgress} from '../defs/order-line-progress-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
])
export class FacilityOrderLineProgressController extends Controller {
  public static readonly exampleData: OrderLineProgress = {
    lineProgressPercent: 98.37804793582056,
  };

  public static readonly exampleResponse: OrderLineProgress =
    FacilityOrderLineProgressController.exampleData;

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @param {string} areas
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {OrderLineProgress} contract
   */
  @Example<OrderLineProgress>(
    FacilityOrderLineProgressController.exampleResponse,
  )
  @SuccessResponse('200')
  @Get('facility/line/progress')
  @OperationId('GetOrdersFacilityLineProgress')
  @Tags('orders')
  public async getFacilityOrderLineProgress(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
  ): Promise<OrderLineProgress> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    const orderLineProgress: OrderLineProgress =
      await orderService.getFacilityOrderLineProgress(
        startDate.toISOString(),
        endDate.toISOString(),
      );

    return orderLineProgress;
  }
}
