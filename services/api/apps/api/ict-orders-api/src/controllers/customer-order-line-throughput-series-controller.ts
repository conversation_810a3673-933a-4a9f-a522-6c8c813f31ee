import {
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Route,
  Middlewares,
  Controller,
  Query,
  Get,
  OperationId,
  Tags,
  Example,
} from 'tsoa';
import {CustomerOrderLineThroughputSeriesData} from '../defs/order-customer-line-throughput-series-def.ts';
import {OrderService} from '../services/order-service.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
])
export class OrderCustomerLineThroughputSeriesController extends Controller {
  public static readonly exmapleData: CustomerOrderLineThroughputSeriesData = {
    throughput: [
      {
        name: '2024-04-22T07:00:00.000Z',
        value: 24,
      },
      {
        name: '2024-04-22T07:15:00.000Z',
        value: 62,
      },
      {
        name: '2024-04-22T07:30:00.000Z',
        value: 57,
      },
    ],
  };

  public static readonly exampleResponse: CustomerOrderLineThroughputSeriesData =
    OrderCustomerLineThroughputSeriesController.exmapleData;

  @Example<CustomerOrderLineThroughputSeriesData>(
    OrderCustomerLineThroughputSeriesController.exampleResponse
  )
  @Get('/customer/line/throughput/series')
  @OperationId('GetOrdersCustomerLineThroughputSeries')
  @Tags('orders')
  public async getCustomerOrderLineThroughputSeries(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<CustomerOrderLineThroughputSeriesData> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    return await orderService.getCustomerOrderLineThroughputSeries(
      startDate,
      endDate
    );
  }
}
