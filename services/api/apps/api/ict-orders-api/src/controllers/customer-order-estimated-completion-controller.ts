import {
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  OperationId,
  Query,
  Route,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {CustomerOrderEstimatedCompletionData} from '../defs/order-estimated-completion-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
])
export class CustomerOrderEstimatedCompletionController extends Controller {
  public static readonly exampleResponse: CustomerOrderEstimatedCompletionData =
    {
      estimatedCompletionMinutes: 242,
    };

  /**
   *
   * @param {Date} startDate
   * @param {Date} endDate
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {CustomerOrderEstimatedCompletionData} contract
   */
  @Example(CustomerOrderEstimatedCompletionController.exampleResponse)
  @Get('/customer/completion')
  @OperationId('GetOrdersCustomerEstimatedCompletion')
  @Tags('orders')
  public async getCustomerOrderEstimatedCompletion(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<CustomerOrderEstimatedCompletionData> {
    startEndDateValidation(startDate, endDate);
    const orderService = Container.get(OrderService);

    return await orderService.getCustomerOrdersEstimatedCompletion(
      startDate,
      endDate
    );
  }
}
