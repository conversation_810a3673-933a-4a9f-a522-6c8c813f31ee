import {
  ProtectedRouteMiddleware,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  startEndDateValidation,
  WMSCustomerAreaToDetailStatusMap,
  WMSCustomerOrderArea,
  WMSCustomerOrderDetailStatus,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {OrderCustomerLineThroughputData} from '../defs/order-customer-line-throughput-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
])
export class OrderCustomerLineThroughputController extends Controller {
  public static readonly exampleData: OrderCustomerLineThroughputData = {
    throughputRateOrderLinesPerHour: 100,
  };

  public static readonly exampleResponse: OrderCustomerLineThroughputData =
    OrderCustomerLineThroughputController.exampleData;

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @param {string} areas
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {OrderCustomerLineThroughput} contract
   */
  @Example<OrderCustomerLineThroughputData>(
    OrderCustomerLineThroughputController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('customer/line/throughput')
  @OperationId('GetOrdersCustomerLineThroughput')
  @Tags('orders')
  public async getCustomerOrderLineThroughput(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
    @Query('area') area?: WMSCustomerOrderArea
  ): Promise<OrderCustomerLineThroughputData> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    let status: WMSCustomerOrderDetailStatus | undefined;
    if (area) status = WMSCustomerAreaToDetailStatusMap.get(area);

    return await orderService.getOrderCustomerLineThroughput(
      startDate.toISOString(),
      endDate.toISOString(),
      status
    );
  }
}
