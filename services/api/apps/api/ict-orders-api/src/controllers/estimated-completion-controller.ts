import {
  ApiResponse,
  CacheMiddleware,
  Container,
  ProtectedRouteMiddleware,
  startEndDateValidation,
  DatabaseTypes,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {
  EstimatedOrderCompletionConfig,
  EstimatedOrderCompletionTimes,
} from '../defs/estimated-completion-def.ts';
import {OrderService} from '../services/order-service.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class EstimatedCompletionController extends Controller {
  private static readonly exampleCompletionTime = '2023-07-30T20:38:05.000Z';

  public static readonly exampleData: EstimatedOrderCompletionTimes = {
    completionTime: EstimatedCompletionController.exampleCompletionTime,
  };

  public static readonly exampleDataFormatted: EstimatedOrderCompletionTimes = {
    completionTime: EstimatedCompletionController.exampleCompletionTime,
  };

  public static readonly exampleConfig: EstimatedOrderCompletionConfig = {
    targetTime: '16:00',
  };

  public static readonly exampleResponse: ApiResponse<
    EstimatedOrderCompletionTimes,
    EstimatedOrderCompletionConfig
  > = {
    completionTime: EstimatedCompletionController.exampleCompletionTime,
    metadata: EstimatedCompletionController.exampleConfig,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date defaults to the current datetime if empty
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {ApiResponse<EstimatedOrderCompletionTimes, EstimatedOrderCompletionConfig>} contract
   * @throws {IctError} error
   */
  @Example<
    ApiResponse<EstimatedOrderCompletionTimes, EstimatedOrderCompletionConfig>
  >(EstimatedCompletionController.exampleResponse)
  @SuccessResponse('200')
  @Get('/completion')
  @OperationId('GetOrdersCompletion')
  @Tags('orders')
  public async getOrdersCompletion(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate?: Date
  ): Promise<
    ApiResponse<EstimatedOrderCompletionTimes, EstimatedOrderCompletionConfig>
  > {
    const newEndDate = endDate ?? new Date();
    startEndDateValidation(startDate, newEndDate);
    const orderService = Container.get(OrderService);

    const orderCompletionTimes =
      await orderService.getOrdersEstimatedCompletion(
        startDate.toISOString(),
        newEndDate.toISOString()
      );

    return {
      ...orderCompletionTimes,
      metadata: EstimatedCompletionController.exampleConfig,
    };
  }
}
