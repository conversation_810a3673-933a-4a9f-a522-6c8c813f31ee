import {
  ProtectedRouteMiddleware,
  Container,
  startEndDateValidation,
  CacheMiddleware,
  configPostgresDatabase,
  DatabaseTypes,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {CustomerOrderProgress} from '../defs/order-progress-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class CustomerOrderProgressController extends Controller {
  static readonly exampleData: CustomerOrderProgress = {
    orderProgressPercentage: 35,
  };

  static readonly exampleResponse: CustomerOrderProgress =
    CustomerOrderProgressController.exampleData;

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @param {string} area
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {CustomerOrderProgress} contract
   */
  @Example<CustomerOrderProgress>(
    CustomerOrderProgressController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/customer/progress')
  @OperationId('GetOrdersCustomerProgress')
  @Tags('orders')
  public async getCustomerOrderProgress(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<CustomerOrderProgress> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    // retrieve order progress percentage from the service
    return await orderService.getCustomerOrderProgress(
      startDate.toISOString(),
      endDate.toISOString()
    );
  }
}
