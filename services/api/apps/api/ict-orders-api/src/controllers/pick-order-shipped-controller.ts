import {
  ApiResponse,
  ProtectedRouteMiddleware,
  Container,
  startEndDateValidation,
  CacheMiddleware,
  DatabaseTypes,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {OrderShippedData} from '../defs/order-lines-shipped-def.ts';
import {getOrderShippedChange} from '../utils/status-formatters.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class PickOrderShippedController extends Controller {
  public static readonly exampleData: OrderShippedData = {
    total: 1000,
    current: 225,
    past: 300,
    change: -25,
  };

  public static readonly exampleConfig = {};

  public static readonly exampleResponse: ApiResponse<OrderShippedData, {}> = {
    total: 1000,
    current: 225,
    past: 300,
    change: -25,
    metadata: PickOrderShippedController.exampleConfig,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {ApiResponse<OrderShippedData, {}>} contract
   */
  @Example<ApiResponse<OrderShippedData, {}>>(
    PickOrderShippedController.exampleResponse
  )
  @SuccessResponse('200')
  // @Get('/pick/shipped')
  @Get('/shipped')
  @OperationId('GetPickOrdersShipped')
  @Tags('orders')
  public async getPickOrdersShipped(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<ApiResponse<OrderShippedData, {}>> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    const orderShipped = await orderService.getPickOrdersShipped(
      startDate.toISOString(),
      endDate.toISOString()
    );

    const change =
      orderShipped.current > 0 && orderShipped.past > 0
        ? getOrderShippedChange(orderShipped.current, orderShipped.past)
        : 0;

    const contract: ApiResponse<OrderShippedData, {}> = {
      // Total planned orders at the given time
      total: orderShipped.total,
      // Number of orders in a shipped status at the given time
      current: orderShipped.current,
      // Number of orders in a shipped status at this point in time on the previous production day
      past: orderShipped.past,
      change,
      metadata: PickOrderShippedController.exampleConfig,
    };

    return contract;
  }
}
