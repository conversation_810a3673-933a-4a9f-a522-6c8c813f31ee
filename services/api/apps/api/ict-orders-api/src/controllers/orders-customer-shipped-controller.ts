import {
  ApiResponse,
  ProtectedRouteMiddleware,
  Container,
  startEndDateValidation,
  CacheMiddleware,
  DatabaseTypes,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {OrdersShipped} from '../defs/order-shipped-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OrdersShippedController extends Controller {
  public static readonly exampleData: OrdersShipped = {
    total: 1000,
    shipped: 225,
  };

  public static readonly exampleConfig = {};

  public static readonly exampleResponse: ApiResponse<OrdersShipped, {}> = {
    total: 1000,
    shipped: 225,
    metadata: OrdersShippedController.exampleConfig,
  };

  /**
   * Queries the wms_customer_order table to calculate the number of orders that have been shipped out of
   * the total number of orders open between start_date and end_date
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {ApiResponse<OrdersShipped>} contract
   */
  @Example<ApiResponse<OrdersShipped, {}>>(
    OrdersShippedController.exampleResponse,
  )
  @SuccessResponse('200')
  @Get('/customer/shipped')
  @OperationId('GetOrdersCustomerShipped')
  @Tags('orders')
  public async getOrdersShipped(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
  ): Promise<ApiResponse<OrdersShipped, {}>> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    const ordersShipped = await orderService.getOrdersShipped(
      startDate.toISOString(),
      endDate.toISOString(),
    );

    const contract: ApiResponse<OrdersShipped, {}> = {
      // Total planned orders at the given time
      total: ordersShipped.total,
      // Number of orders in a shipped status at the given time
      shipped: ordersShipped.shipped,
      metadata: OrdersShippedController.exampleConfig,
    };

    return contract;
  }
}
