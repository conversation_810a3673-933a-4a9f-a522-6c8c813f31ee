import {
  ProtectedRouteMiddleware,
  Container,
  HealthStatus,
  startEndDateValidation,
  CacheMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {
  LineProgressFacilityArea,
  OrderLineProgressAreasData,
} from '../defs/order-areas-lineprogress-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OrderAreasLineprogressController extends Controller {
  public static readonly exampleData: LineProgressFacilityArea[] = [
    {
      id: 'testArea1',
      name: 'Picking',
      alertStatus: {
        status: HealthStatus.Ok,
      },
      operators: {
        activeOperators: 13,
        lowRecOperators: 19,
        highRecOperators: 37,
      },
      orderLines: {
        orderLineProgress: 99.5,
        orderLinesCompleted: 426,
        totalOrderLines: 428,
      },
    },
    {
      id: 'testArea2',
      name: 'Put Wall',
      alertStatus: {
        status: HealthStatus.Caution,
      },
      operators: {
        activeOperators: 55,
        lowRecOperators: 32,
        highRecOperators: 34,
      },
      orderLines: {
        orderLineProgress: 100,
        orderLinesCompleted: 221,
        totalOrderLines: 221,
      },
    },
    {
      id: 'testArea3',
      name: 'Packing',
      alertStatus: {
        status: HealthStatus.Ok,
      },
      operators: {
        activeOperators: 28,
        lowRecOperators: 19,
        highRecOperators: 35,
      },
      orderLines: {
        orderLineProgress: 100,
        orderLinesCompleted: 764,
        totalOrderLines: 764,
      },
    },
    {
      id: 'testArea4',
      name: 'Shipping',
      alertStatus: {
        status: HealthStatus.Critical,
      },
      operators: {
        activeOperators: 30,
        lowRecOperators: 25,
        highRecOperators: 31,
      },
      orderLines: {
        orderLineProgress: 99.5,
        orderLinesCompleted: 386,
        totalOrderLines: 388,
      },
    },
  ];

  public static readonly exampleResponse: OrderLineProgressAreasData = {
    areas: OrderAreasLineprogressController.exampleData,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {OrderLineProgressAreasData} contract
   */
  @Example<OrderLineProgressAreasData>(
    OrderAreasLineprogressController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/lineprogress/areas')
  @OperationId('GetOrdersLineProgressAreas')
  @Tags('orders')
  public async getFaultsAreas(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<OrderLineProgressAreasData> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    return {
      areas: await orderService.getOrderLineProgressAreas(
        startDate.toISOString(),
        endDate.toISOString()
      ),
    };
  }
}
