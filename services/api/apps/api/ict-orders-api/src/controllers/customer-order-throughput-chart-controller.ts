import {
  ProtectedRouteMiddleware,
  ChartSeriesData,
  Container,
  startEndDateValidation,
  DatabaseTypes,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
])
export class OrderCustomerThroughputChartController extends Controller {
  public static readonly exampleData: ChartSeriesData[] = [
    {
      name: '01',
      value: 20,
    },
    {
      name: '02',
      value: 30,
    },
  ];

  public static readonly exampleResponse: {throughput: ChartSeriesData[]} = {
    throughput: OrderCustomerThroughputChartController.exampleData,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {ChartSeriesData[]} contract
   */
  @Example<{throughput: ChartSeriesData[]}>(
    OrderCustomerThroughputChartController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/customer/throughput/series')
  @OperationId('GetOrdersCustomerThroughputSeries')
  @Tags('orders')
  public async getFaultsAreas(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<{throughput: ChartSeriesData[]}> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    return {
      throughput: await orderService.getOrderCustomerThroughputChartData(
        startDate.toISOString(),
        endDate.toISOString()
      ),
    };
  }
}
