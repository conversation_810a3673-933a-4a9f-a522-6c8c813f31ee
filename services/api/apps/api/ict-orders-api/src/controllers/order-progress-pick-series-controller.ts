import {
  CacheMiddleware,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  OperationId,
  Query,
  Route,
  SuccessResponse,
  Tags,
} from 'tsoa';
import {OrderProgressSeriesData} from '../defs/order-progress-chart-def.ts';
import {OrderService} from '../services/order-service.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OrderProgressSeriesController extends Controller {
  static readonly exampleData: OrderProgressSeriesData = {
    progress: [
      {
        name: '2024-04-01T00:00:00Z',
        value: 82.3,
      },
      {
        name: '2024-04-01T01:00:00Z',
        value: 82.1,
      },
      {
        name: '2024-04-01T02:00:00Z',
        value: 81.5,
      },
      {
        name: '2024-04-01T03:00:00Z',
        value: 83.7,
      },
    ],
  };

  static readonly exampleResponse: OrderProgressSeriesData =
    OrderProgressSeriesController.exampleData;

  /**
   * Gets a series of order progress percentage, averaging by each hour incrementing.
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {OrderProgressSeriesData} contract
   */
  @Example<OrderProgressSeriesData>(
    OrderProgressSeriesController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/pick/progress/series')
  @OperationId('GetOrdersProgressSeries')
  @Tags('orders')
  public async getOrderProgressSeries(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<OrderProgressSeriesData> {
    startEndDateValidation(startDate, endDate);

    const orderService = Container.get(OrderService);

    return await orderService.getOrderProgressSeries(startDate, endDate);
  }
}
