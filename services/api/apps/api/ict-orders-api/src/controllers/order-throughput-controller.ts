import {
  ApiResponse,
  ProtectedRouteMiddleware,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  IctError,
  startEndDateValidation,
  CacheMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {
  OrderThroughputConfig,
  OrderThroughputData,
} from '../defs/order-throughput-def.ts';
import {PickOrderArea} from '../defs/pick-order-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OrderThroughputController extends Controller {
  public static readonly exampleData: OrderThroughputData = {
    throughputRateLinesPerHour: 100,
  };

  public static readonly exampleConfig: OrderThroughputConfig = {
    lowRange: 600,
    max: 1600,
  };

  public static readonly exampleResponse: ApiResponse<
    OrderThroughputData,
    OrderThroughputConfig
  > = {
    throughputRateLinesPerHour: 100,
    metadata: OrderThroughputController.exampleConfig,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {ApiResponse<OrderThroughputData, OrderThroughputConfig>} contract
   */
  @Example<ApiResponse<OrderThroughputData, OrderThroughputConfig>>(
    OrderThroughputController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/throughput')
  @OperationId('GetOrdersThroughput')
  @Tags('orders')
  public async getFaultsAreas(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
    @Query('area') area?: PickOrderArea
  ): Promise<ApiResponse<OrderThroughputData, OrderThroughputConfig>> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    const orderThroughputLinesProcessData =
      await orderService.getOrderThroughputRateAsync(
        startDate.toISOString(),
        endDate.toISOString(),
        area
      );
    if (orderThroughputLinesProcessData) {
      // TODO will need a config database to get customizable values
      const config: OrderThroughputConfig = {
        max: 1600,
        lowRange: 600,
      };

      return {
        ...orderThroughputLinesProcessData,
        metadata: config,
      };
    }
    throw IctError.internalServerError(
      'Unable to retrieve order throughput rate data'
    );
  }
}
