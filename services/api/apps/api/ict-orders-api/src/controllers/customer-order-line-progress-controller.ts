import {
  ProtectedRouteMiddleware,
  Container,
  IctError,
  startEndDateValidation,
  WMSCustomerOrderArea,
  WMSCustomerOrderDetailStatus,
  DatabaseTypes,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {OrderLineProgress} from '../defs/order-line-progress-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
])
export class OrderCustomerLineProgressController extends Controller {
  public static readonly exampleData: OrderLineProgress = {
    lineProgressPercent: 98.37804793582056,
  };

  public static readonly exampleResponse: OrderLineProgress =
    OrderCustomerLineProgressController.exampleData;

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @param {string} areas
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {OrderLineProgress} contract
   */
  @Example<OrderLineProgress>(
    OrderCustomerLineProgressController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('customer/line/progress')
  @OperationId('GetOrdersCustomerLineProgress')
  @Tags('orders')
  public async getCustomerOrderLineProgress(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
    @Query('area') area?: WMSCustomerOrderArea
  ): Promise<OrderLineProgress> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    let status: WMSCustomerOrderDetailStatus | undefined;
    if (area === WMSCustomerOrderArea.Shipping) {
      status = WMSCustomerOrderDetailStatus.Shipped;
    } else if (area === WMSCustomerOrderArea.Picking) {
      status = WMSCustomerOrderDetailStatus.Picked;
    }

    const orderLineProgress: OrderLineProgress =
      await orderService.getCustomerOrderLineProgress(
        startDate.toISOString(),
        endDate.toISOString(),
        status
      );

    if (!orderLineProgress) {
      throw IctError.internalServerError(
        'Unable to retrieve customer order line progress data.'
      );
    }
    return orderLineProgress;
  }
}
