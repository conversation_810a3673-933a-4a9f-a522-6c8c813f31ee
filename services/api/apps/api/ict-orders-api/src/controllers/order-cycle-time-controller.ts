import {
  ApiResponse,
  ProtectedRouteMiddleware,
  Container,
  startEndDateValidation,
  DatabaseTypes,
  CacheMiddleware,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {
  OrderCycleTime,
  OrderCycleTimeConfig,
  OrderCycleTimeData,
} from '../defs/order-cycle-time-def.ts';

const dbMiddleware = {
  databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
};

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, dbMiddleware),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OrderCycleTimeController extends Controller {
  private static readonly exampleOrderCycleTimeMinutes = 208;

  public static readonly exampleData: OrderCycleTime = {
    orderCycleTimeMinutes:
      OrderCycleTimeController.exampleOrderCycleTimeMinutes,
  };

  public static readonly exampleConfig: OrderCycleTimeConfig = {
    max: 200,
    highRange: 109,
  };

  public static readonly exampleResponse: ApiResponse<
    OrderCycleTimeData,
    OrderCycleTimeConfig
  > = {
    orderCycleTimeMinutes:
      OrderCycleTimeController.exampleOrderCycleTimeMinutes,
    status: 'caution',
    metadata: OrderCycleTimeController.exampleConfig,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @param {string} area
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {ApiResponse<OrderCycleTimeData, OrderCycleTimeConfig>} contract
   */
  @Example<ApiResponse<OrderCycleTimeData, OrderCycleTimeConfig>>(
    OrderCycleTimeController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/pick/cycletime')
  @OperationId('GetOrdersPickCycleTime')
  @Tags('orders')
  public async getFaultsAreas(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<ApiResponse<OrderCycleTimeData, OrderCycleTimeConfig>> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    const orderCycleTime: OrderCycleTime = await orderService.getOrderCycleTime(
      startDate.toISOString(),
      endDate.toISOString()
    );

    const contract: ApiResponse<OrderCycleTimeData, OrderCycleTimeConfig> = {
      orderCycleTimeMinutes: orderCycleTime.orderCycleTimeMinutes,
      // TODO: Calculate status based on values and range
      status: 'caution',

      // TODO: Pull this data from a config database
      metadata: {
        max: 200,
        highRange: 109,
      },
    };

    return contract;
  }
}
