import {
  CacheMiddleware,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ProtectedRouteMiddleware,
  startEndDateValidation,
  WMSCustomerAreaToStatusMap,
  WMSCustomerOrderArea,
  WMSCustomerOrderStatus,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {CustomerOrderThroughputData} from '../defs/customer-order-throughput-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class CustomerOrderThroughputController extends Controller {
  public static readonly exampleData: CustomerOrderThroughputData = {
    throughputRateOrdersPerHour: 100,
  };

  public static readonly exampleResponse: CustomerOrderThroughputData =
    CustomerOrderThroughputController.exampleData;

  /**
   * Calculates the number of orders per hour that were shipped in the given time frame.
   * If an area param is used, then the matching status is used in the calculation instead of 'shipped'.
   * If the end_date is in the future, then the current datetime is used instead.
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {CustomerOrderThroughputData} contract
   */
  @Example<CustomerOrderThroughputData>(
    CustomerOrderThroughputController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/customer/throughput')
  @OperationId('GetOrdersCustomerThroughput')
  @Tags('orders')
  public async getCustomerThroughputRate(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
    @Query('area') area?: WMSCustomerOrderArea
  ): Promise<CustomerOrderThroughputData> {
    startEndDateValidation(startDate, endDate);
    const orderService = Container.get(OrderService);

    let status: WMSCustomerOrderStatus | undefined;
    if (area) status = WMSCustomerAreaToStatusMap.get(area);

    return await orderService.getCustomerOrderThroughputRate(
      startDate,
      endDate,
      status
    );
  }
}
