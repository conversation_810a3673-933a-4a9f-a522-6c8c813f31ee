import {BaseHealthCheck} from 'ict-api-foundations';
import {Get, Route, OperationId, Tags} from 'tsoa';

@Route('/orders/healthcheck')
export class OrdersHealthCheckController {
  @Get()
  @OperationId('GetOrdersBasicHealthCheck')
  @Tags('orders')
  public getOrdersBasicHealthCheck() {
    return BaseHealthCheck.getBasicHealthCheck();
  }

  @Get('/full')
  @OperationId('GetOrdersFullHealthCheck')
  @Tags('orders')
  public getOrdersFullHealthCheck() {
    return BaseHealthCheck.getDeepHealthCheck(['auth0', 'bigQuery', 'redis']);
  }
}
