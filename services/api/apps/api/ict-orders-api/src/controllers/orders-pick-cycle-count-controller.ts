import {
  ProtectedRouteMiddleware,
  Container,
  startEndDateValidation,
  CacheMiddleware,
  IctError,
  DatabaseTypes,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {OrdersPickCycleCountData} from '../defs/orders-pick-cycle-count-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OrdersPickCycleCountController extends Controller {
  private static readonly exampleOrdersPickCycleCount = 208;

  public static readonly exampleData: OrdersPickCycleCountData = {
    cycleCount: OrdersPickCycleCountController.exampleOrdersPickCycleCount,
  };

  public static readonly exampleResponse: OrdersPickCycleCountData =
    OrdersPickCycleCountController.exampleData;

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {OrdersPickCycleCountData} contract
   */
  @Example<OrdersPickCycleCountData>(
    OrdersPickCycleCountController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/pick/cycle-count')
  @OperationId('GetOrdersPickCycleCount')
  @Tags('orders')
  public async getOrdersPickCycleCount(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<OrdersPickCycleCountData> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the orders service
    const ordersService = Container.get(OrderService);

    const ordersPickCycleCount = await ordersService.getOrdersPickCycleCount(
      startDate,
      endDate
    );

    if (!ordersPickCycleCount || !ordersPickCycleCount.cycleCount) {
      throw IctError.noContent();
    }

    return ordersPickCycleCount;
  }
}
