import {
  ProtectedRouteMiddleware,
  Container,
  startEndDateValidation,
  CacheMiddleware,
  DatabaseTypes,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {ProjectedOrderFulfillment} from '../defs/orders-projected-fulfillment-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class CustomerOrderProjectedFulfillmentController extends Controller {
  public static readonly exampleData: ProjectedOrderFulfillment = {
    projectedOrderFulfillmentPercentage: 67.5,
  };

  public static readonly exampleResponse: ProjectedOrderFulfillment = {
    projectedOrderFulfillmentPercentage: 67.5,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @param {string} area
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {ApiResponse<ProjectedOrderFulfillment, ProjectedOrderFulfillmentConfig>} contract
   */
  @Example<ProjectedOrderFulfillment>(
    CustomerOrderProjectedFulfillmentController.exampleResponse,
  )
  @SuccessResponse('200')
  @Get('/customer/fulfillment')
  @OperationId('GetCustomerOrdersFulfillment')
  @Tags('orders')
  public async getCustomerOrderProjectedFulfillment(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
  ): Promise<ProjectedOrderFulfillment> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    // retrieve projected order fulfillment percentage from the service
    const projectedOrderProjectedFulfillment =
      await orderService.getProjectedCustomerOrderFulfillmentPercentage(
        startDate.toISOString(),
        endDate.toISOString(),
      );

    return projectedOrderProjectedFulfillment;
  }
}
