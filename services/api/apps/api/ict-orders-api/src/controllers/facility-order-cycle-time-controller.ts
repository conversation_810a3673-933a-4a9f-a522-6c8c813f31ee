import {
  ProtectedRouteMiddleware,
  Container,
  startEndDateValidation,
  CacheMiddleware,
  DatabaseTypes,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {OrderCycleTime} from '../defs/order-cycle-time-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class FacilityOrderCycleTimeController extends Controller {
  private static readonly exampleOrderCycleTimeMinutes = 208;

  public static readonly exampleData: OrderCycleTime = {
    orderCycleTimeMinutes:
      FacilityOrderCycleTimeController.exampleOrderCycleTimeMinutes,
  };

  public static readonly exampleResponse: OrderCycleTime = {
    orderCycleTimeMinutes:
      FacilityOrderCycleTimeController.exampleOrderCycleTimeMinutes,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {OrderCycleTime} contract
   */
  @Example<OrderCycleTime>(FacilityOrderCycleTimeController.exampleResponse)
  @SuccessResponse('200')
  @Get('/facility/cycletime')
  @OperationId('GetOrdersFacilityCycleTime')
  @Tags('orders')
  public async getFacilityOrderCycleTime(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
  ): Promise<OrderCycleTime> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    const orderCycleTime: OrderCycleTime =
      await orderService.getFacilityOrderCycleTime(
        startDate.toISOString(),
        endDate.toISOString(),
      );

    return {
      orderCycleTimeMinutes: orderCycleTime.orderCycleTimeMinutes,
    };
  }
}
