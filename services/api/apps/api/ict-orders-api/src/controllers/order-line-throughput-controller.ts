import {
  ProtectedRouteMiddleware,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  IctError,
  startEndDateValidation,
  CacheMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {OrderLineThroughputData} from '../defs/order-line-throughput-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OrderLineThroughputController extends Controller {
  public static readonly exampleData: OrderLineThroughputData = {
    totalCompletedOrderLines: 100,
  };

  public static readonly exampleResponse: OrderLineThroughputData =
    OrderLineThroughputController.exampleData;

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {OrderLineThroughputData} contract
   */
  @Example<OrderLineThroughputData>(
    OrderLineThroughputController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/line/throughput')
  @OperationId('GetOrdersLineThroughput')
  @Tags('orders')
  public async getFaultsAreas(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<OrderLineThroughputData> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    const totalOrderLineThroughput = await orderService.getOrderLineThroughput(
      startDate.toISOString(),
      endDate.toISOString()
    );
    if (!totalOrderLineThroughput) {
      throw IctError.internalServerError(
        'Unable to retrieve order line throughput data'
      );
    }
    return totalOrderLineThroughput;
  }
}
