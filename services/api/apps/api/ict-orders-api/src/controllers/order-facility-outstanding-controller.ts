import {
  ApiResponse,
  CacheMiddleware,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  IctError,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OrderService} from '../services/order-service.ts';
import {OrdersOutstandingData} from '../defs/orders-outstanding-def.ts';

@Route('orders')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OrderFacilityOutstandingController extends Controller {
  public static readonly exampleData: OrdersOutstandingData = {
    incompletedTotal: 28,
  };

  public static readonly exampleConfig = {};

  public static readonly exampleResponse: ApiResponse<
    OrdersOutstandingData,
    {}
  > = {
    incompletedTotal: 28,
    metadata: OrderFacilityOutstandingController.exampleConfig,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @param {string} area
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {ApiResponse<OrderOutstanding, OrderOutstandingConfig>} contract
   */
  @Example<ApiResponse<OrdersOutstandingData, {}>>(
    OrderFacilityOutstandingController.exampleResponse,
  )
  @SuccessResponse('200')
  @Get('/facility/outstanding')
  @OperationId('GetOrdersFacilityOutstanding')
  @Tags('orders')
  public async getOrdersFacilityOutstanding(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
  ): Promise<ApiResponse<OrdersOutstandingData, {}>> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const orderService = Container.get(OrderService);

    const ordersOutstanding = await orderService.getOrdersFacilityOutstanding(
      startDate.toISOString(),
      endDate.toISOString(),
    );

    if (ordersOutstanding) {
      const contract: ApiResponse<OrdersOutstandingData, {}> = {
        ...ordersOutstanding,
        metadata: OrderFacilityOutstandingController.exampleConfig,
      };
      return contract;
    }
    throw IctError.internalServerError(
      'Unable to retrieve facility orders outstanding data.',
    );
  }
}
