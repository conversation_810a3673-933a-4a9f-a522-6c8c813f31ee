{"name": "ict-orders-api", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/ict-orders-api/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "ict-orders-api:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "ict-orders-api:build:development"}, "production": {"buildTarget": "ict-orders-api:build:production"}}}}}