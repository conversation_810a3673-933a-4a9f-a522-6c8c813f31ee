{"name": "ict-orders-api", "version": "1.0.0", "description": "API logic layer for handling business logic for orders within Control Tower.", "author": "Dematic", "license": "ISC", "type": "module", "main": "src/index.ts", "engines": {"node": ">=22.0.0"}, "scripts": {"test": "yarn run -T tsoa routes && AUTH_AUDIENCE='http://localhost:8080/' AUTH_DOMAIN='test-domain.auth0.com' yarn run -T mocha ./src/test/**/*.spec.ts", "test:watch": "tsoa routes && mocha ./src/test/**/*.spec.ts --watch", "start": "node --import ../register-tsnode.mjs --enable-source-maps ./src/index.ts", "start:otel": "node --require ../ict-instrumentation/build/instrumentation.cjs --import ../register-tsnode.mjs ./src/index.ts", "build": "yarn run -T tsoa spec-and-routes", "spec": "yarn run -T tsoa spec", "lint": "yarn run -T gts lint", "clean": "yarn run -T gts clean", "compile": "tsc", "fix": "yarn run -T gts fix", "posttest": "yarn lint", "debug": "nodemon --exec \"tsoa routes && node --inspect=0.0.0.0:9229 --import ../register-tsnode.mjs ./src/index.ts\"", "watch": "yarn run -T tsoa routes && node --watch --no-warnings=ExperimentalWarning --import ../register-tsnode.mjs ./src/index.ts | yarn run -T pino-pretty"}}