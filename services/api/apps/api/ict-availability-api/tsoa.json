{"entryFile": "src/index.ts", "noImplicitAdditionalProperties": "throw-on-extras", "controllerPathGlobs": ["src/controllers/*.ts"], "spec": {"outputDirectory": "docs", "specVersion": 3, "yaml": true, "specFileBaseName": "openapi", "title": "System Availability API", "spec": {"servers": [{"url": "http://localhost:8080/", "description": "Local development"}, {"url": "https://run-ict-d-api.ict.dematic.dev", "description": "Dev development"}, {"url": "https://stage.api.ict.dematic.dev", "description": "Staging"}, {"url": "https://api.ict.dematic.cloud", "description": "Production"}]}, "securityDefinitions": {"auth0": {"type": "oauth2", "authorizationUrl": "https://dev-zmogq9vd.us.auth0.com/authorize?audience=https://dev.api.ict.dematic.dev", "flow": "implicit"}}}, "routes": {"authenticationModule": "../../../libs/api/ict-api-foundations/src/middleware/tsoa-security-middleware.ts", "routesDir": "build", "esm": true}}