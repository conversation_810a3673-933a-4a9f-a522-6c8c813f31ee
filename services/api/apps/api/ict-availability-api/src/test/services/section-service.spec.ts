import sinon from 'sinon';
import {expect} from 'chai';
import {
  Container,
  ContextService,
  WinstonLogger,
  IctError,
} from 'ict-api-foundations';
import {
  EDPAvailabilityService,
  EDPSectionsResponse,
} from '../../services/edp-availability-service.ts';
import {
  SectionService,
  AvailableSectionsQueryParams,
} from '../../services/section-service.ts';
import {GetAvailableSectionsResponse} from '../../defs/section-def.ts';
import {ContextUtils} from '../../utils/context-utils.js';

describe('SectionService', () => {
  let service: SectionService;
  let edpAvailabilityServiceStub: sinon.SinonStubbedInstance<EDPAvailabilityService>;
  let loggerStub: sinon.SinonStubbedInstance<WinstonLogger>;
  let contextServiceStub: sinon.SinonStubbedInstance<ContextService>;
  let getTenantIdStub: sinon.SinonStub;
  let getFacilityIdStub: sinon.SinonStub;

  const mockTenantId = 'test-tenant';
  const mockFacilityId = 'test-facility';

  beforeEach(() => {
    edpAvailabilityServiceStub = sinon.createStubInstance(
      EDPAvailabilityService,
    );
    loggerStub = sinon.createStubInstance(WinstonLogger);
    contextServiceStub = sinon.createStubInstance(ContextService);

    contextServiceStub.datasetId = `${mockTenantId}_${mockFacilityId}`;

    getTenantIdStub = sinon
      .stub(ContextUtils, 'getTenantId')
      .returns(mockTenantId);
    getFacilityIdStub = sinon
      .stub(ContextUtils, 'getFacilityId')
      .returns(mockFacilityId);

    sinon.stub(Container, 'get').callsFake(token => {
      if (token === EDPAvailabilityService) return edpAvailabilityServiceStub;
      if (token === WinstonLogger) return loggerStub;
      if (token === ContextService) return contextServiceStub;
      throw new Error(`Unexpected token: ${token}`);
    });

    service = new SectionService(
      edpAvailabilityServiceStub,
      loggerStub,
      contextServiceStub,
    );
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getAvailableSections', () => {
    const mockQueryParams: AvailableSectionsQueryParams = {
      limit: 10,
      offset: 0,
      searchTerm: 'test',
    };

    const mockEdpResponse = {
      data: [
        {
          name: '01L08RI1',
          pk: 378,
        },
        {
          name: '01L09RI1',
          pk: 459,
        },
      ],
      metadata: {
        page: 1,
        limit: 10,
        total: 2,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false,
      },
    };

    const mockAppResponse: GetAvailableSectionsResponse = {
      data: [
        {
          name: '01L08RI1',
          pk: 378,
        },
        {
          name: '01L09RI1',
          pk: 459,
        },
      ],
      metadata: {
        page: 1,
        limit: 10,
        totalResults: 2,
        totalPages: 1,
      },
    };

    it('should successfully retrieve available sections', async () => {
      edpAvailabilityServiceStub.getAvailableSections.resolves(
        mockEdpResponse as unknown as EDPSectionsResponse,
      );

      const result = await service.getAvailableSections(mockQueryParams);

      expect(result).to.deep.equal(mockAppResponse);
      expect(
        edpAvailabilityServiceStub.getAvailableSections.calledOnceWith(
          mockTenantId,
          mockFacilityId,
          sinon.match.any,
        ),
      ).to.be.true;
    });

    it('should handle errors from the EDP service', async () => {
      const error = new Error('EDP service error');
      edpAvailabilityServiceStub.getAvailableSections.rejects(error);

      try {
        await service.getAvailableSections(mockQueryParams);
        expect.fail('Should have thrown an error');
      } catch (err) {
        expect(err).to.equal(error);
      }
    });

    it('should throw an error if tenant ID is not found', async () => {
      getTenantIdStub.throws(new Error('Tenant ID not found in context'));

      try {
        await service.getAvailableSections(mockQueryParams);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect((error as Error).message).to.equal(
          'Tenant ID not found in context',
        );
      }
    });

    it('should throw an error if facility ID is not found', async () => {
      getFacilityIdStub.throws(new Error('Facility ID not found in context'));

      try {
        await service.getAvailableSections(mockQueryParams);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect((error as Error).message).to.equal(
          'Facility ID not found in context',
        );
      }
    });
  });
});
