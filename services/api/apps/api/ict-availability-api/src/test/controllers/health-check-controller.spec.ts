import express from 'express';
import {ApiMiddleware, testHealthCheck} from 'ict-api-foundations';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';

// Setup
const app = express();
app.use(express.urlencoded({extended: true}));
app.use(express.json());
RegisterRoutes(app);
ApiMiddleware.applyErrorMiddlewares(app);

// Run common health check tests
testHealthCheck(app, '/availability');
