import sinon from 'sinon';
import {expect} from 'chai';
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON>ogger} from 'ict-api-foundations';
import {SectionController} from '../../controllers/section-controller.ts';
import {SectionService} from '../../services/section-service.js';
import {GetAvailableSectionsResponse} from '../../defs/section-def.js';

describe('SectionController', () => {
  let controller: SectionController;
  let sectionServiceStub: sinon.SinonStubbedInstance<SectionService>;
  let loggerStub: sinon.SinonStubbedInstance<WinstonLogger>;

  beforeEach(() => {
    sectionServiceStub = sinon.createStubInstance(SectionService);
    loggerStub = sinon.createStubInstance(WinstonLogger);

    sinon.stub(Container, 'get').callsFake(token => {
      if (token === SectionService) return sectionServiceStub;
      if (token === WinstonLogger) return loggerStub;
      throw new Error(`Unexpected token: ${token}`);
    });

    controller = new SectionController();
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getAvailableSectionNames', () => {
    it('should return available section names from the service', async () => {
      const mockResponse: GetAvailableSectionsResponse = {
        data: [
          {
            name: '01AISLE',
            pk: 171,
          },
          {
            name: '01EL01',
            pk: 159,
          },
        ],
        metadata: {
          page: 1,
          limit: 10,
          totalResults: 2,
          totalPages: 1,
        },
      };

      sectionServiceStub.getAvailableSections.resolves(mockResponse);

      const result = await controller.getAvailableSections(10, 10, 'test');

      expect(result).to.deep.equal(mockResponse);
      expect(
        sectionServiceStub.getAvailableSections.calledOnceWith({
          limit: 10,
          offset: 10,
          searchTerm: 'test',
        }),
      ).to.be.true;
    });

    it('should handle errors from the service', async () => {
      const error = new Error('Service error');
      sectionServiceStub.getAvailableSections.rejects(error);

      try {
        await controller.getAvailableSections(0, 10, 'test');
        expect.fail('Should have thrown an error');
      } catch (err) {
        expect(err).to.equal(error);
      }
    });
  });
});
