import {
  DiSer<PERSON>,
  WinstonLogger,
  ContextService,
  type BaseFilterType,
  type SortField,
} from 'ict-api-foundations';
import {
  EDPAvailabilityService,
  EDPAlarmQueryParams,
  EDPAlarm,
} from './edp-availability-service.ts';
import {Alarm, PostAlarmsListResponse} from '../defs/alarm-def.ts';
import {AlarmCreateDto} from '../defs/alarm-create-dto.ts';
import {ContextUtils} from '../utils/context-utils.js';
import {AlarmUpdateDto} from '../defs/alarm-update-dto.ts';

// Standard pagination format - matching the controller inline interface
export type AlarmQueryParams = {
  filters?: BaseFilterType;
  sortFields?: SortField[];
  groupByFields?: string[];
  limit?: number;
  offset?: number;
  start_date: Date;
  end_date: Date;
  searchString?: string;
};

// wrapper service that implements business logic for alarms data retrieval - wraps the EDP Availability Service

@DiService()
export class AlarmsService {
  constructor(
    private edpAvailabilityService: EDPAvailabilityService,
    private logger: WinstonLogger,
    private context: ContextService,
  ) {}

  public async getAlarms(
    queryParams: AlarmQueryParams,
  ): Promise<PostAlarmsListResponse> {
    const tenantId = ContextUtils.getTenantId(this.context, this.logger);
    const facilityId = ContextUtils.getFacilityId(this.context, this.logger);

    this.logger.info('Getting alarms', {
      tenantId,
      facilityId,
      queryParams,
    });

    try {
      // transform standard request to EDP API format
      const edpParams = this.transformRequestToEDPParams(queryParams);

      const edpResponse = await this.edpAvailabilityService.getAlarms(
        tenantId,
        facilityId,
        edpParams,
      );

      this.logger.info('EDP API Response received:', {
        dataLength: edpResponse.data?.length ?? 0,
        metadata: edpResponse.metadata,
        metadataKeys: Object.keys(edpResponse.metadata || {}),
      });

      // transform response to standard app format
      const alarms = this.transformEDPAlarmsToAppFormat(edpResponse.data);

      return {
        data: alarms,
        metadata: {
          page: edpResponse.metadata?.page ?? 1,
          limit: queryParams.limit || 50,
          totalResults: edpResponse.metadata?.total ?? alarms.length,
          totalPages:
            edpResponse.metadata?.totalPages ??
            Math.ceil(
              (edpResponse.metadata?.total ?? alarms.length) /
                (queryParams.limit || 50),
            ),
        },
      };
    } catch (error) {
      this.logger.error('Failed to get alarms', {error, queryParams});
      throw error;
    }
  }

  public async getAlarmById(alarmId: string): Promise<Alarm> {
    const tenantId = ContextUtils.getTenantId(this.context, this.logger);
    const facilityId = ContextUtils.getFacilityId(this.context, this.logger);

    this.logger.info('Getting alarm by ID', {
      tenantId,
      facilityId,
      alarmId,
    });

    try {
      const edpAlarm = await this.edpAvailabilityService.getAlarmById(
        tenantId,
        facilityId,
        alarmId,
      );

      // Transform single EDP alarm to app format
      return this.transformEDPAlarmToAppFormat(edpAlarm);
    } catch (error) {
      this.logger.error('Failed to get alarm by ID', {error, alarmId});
      throw error;
    }
  }

  public async createAlarm(alarm: AlarmCreateDto): Promise<void> {
    const tenantId = ContextUtils.getTenantId(this.context, this.logger);
    const facilityId = ContextUtils.getFacilityId(this.context, this.logger);

    this.logger.info('Creating alarm', {
      tenantId,
      facilityId,
      alarm,
    });

    try {
      await this.edpAvailabilityService.createAlarm(
        tenantId,
        facilityId,
        alarm,
      );
    } catch (error) {
      this.logger.error('Failed to create alarm', {error, alarm});
      throw error;
    }
  }

  public async updateAlarm(alarm: AlarmUpdateDto): Promise<void> {
    const tenantId = ContextUtils.getTenantId(this.context, this.logger);
    const facilityId = ContextUtils.getFacilityId(this.context, this.logger);

    this.logger.info('Updating alarm', {
      tenantId,
      facilityId,

      alarm,
    });

    try {
      await this.edpAvailabilityService.updateAlarm(
        tenantId,
        facilityId,

        alarm,
      );
    } catch (error) {
      this.logger.error('Failed to update alarm', {error, id: alarm.id, alarm});
      throw error;
    }
  }

  // transform standard pagination request to edp api params
  private transformRequestToEDPParams(
    queryParams: AlarmQueryParams,
  ): EDPAlarmQueryParams {
    const params: EDPAlarmQueryParams = {};

    // Convert offset to page for EDP API
    if (queryParams.offset !== undefined && queryParams.limit) {
      params.page = Math.floor(queryParams.offset / queryParams.limit) + 1;
    } else {
      params.page = 1;
    }

    // Standard pagination fields
    if (queryParams.limit !== undefined) params.limit = queryParams.limit;
    if (queryParams.searchString) params.searchTerm = queryParams.searchString;

    // Date range fields (required)
    if (queryParams.start_date) {
      params.startTimestamp = queryParams.start_date.toISOString();
    }
    if (queryParams.end_date) {
      params.endTimestamp = queryParams.end_date.toISOString();
    }

    // Handle standard sortFields format
    if (queryParams.sortFields?.length) {
      const firstSort = queryParams.sortFields[0];

      // Map standard columnName to EDP API field names
      const sortMapping: Record<string, string> = {
        area: 'sectionArea',
        section: 'sectionName',
        equipment: 'equipmentName',
        faultId: 'faultId',
        description: 'faultDescription',
        tag: 'faultTag',
        faultTag: 'faultTag',
        severity: 'severity',
        startTime: 'faultStartTimeUtc',
        endTime: 'faultEndTimeUtc',
        faultDuration: 'faultDuration',
        splitStartTime: 'splitStartTimeUtc',
        splitEndTime: 'splitEndTimeUtc',
        splitDuration: 'splitDuration',
        origStartTime: 'origStartTimeUtc',
        origEndTime: 'origEndTimeUtc',
        origDuration: 'origDuration',
        refDuration: 'refDuration',
        status: 'removalStatus',
        reason: 'removalReason',
        updatedStartTime: 'updatedStartTimeUtc',
        updatedEndTime: 'updatedEndTimeUtc',
        updatedDuration: 'updatedDuration',
        duration: 'duration',
        comments: 'comments',
      };

      const mappedSort =
        sortMapping[firstSort.columnName] || firstSort.columnName;
      params.sortBy = mappedSort as NonNullable<EDPAlarmQueryParams['sortBy']>;
      params.orderBy = firstSort.isDescending ? 'DESC' : 'ASC';
    }

    if (queryParams.filters) {
      // assuming filters might have these properties:
      this.extractFiltersFromStandardFormat(queryParams.filters, params);
    }

    return params;
  }

  private extractFiltersFromStandardFormat(
    filters: BaseFilterType,
    params: EDPAlarmQueryParams,
  ): void {
    if (typeof filters === 'object' && filters !== null) {
      const filterObj = filters as unknown as Record<string, unknown>;

      if (Array.isArray(filterObj.areas) && filterObj.areas.length > 0) {
        params.sectionAreas = JSON.stringify(filterObj.areas);
      }
      if (Array.isArray(filterObj.sections) && filterObj.sections.length > 0) {
        params.sectionNames = JSON.stringify(filterObj.sections);
      }
      if (
        Array.isArray(filterObj.equipment) &&
        filterObj.equipment.length > 0
      ) {
        params.equipmentNames = JSON.stringify(filterObj.equipment);
      }
      if (Array.isArray(filterObj.statuses) && filterObj.statuses.length > 0) {
        params.removalStatuses = JSON.stringify(filterObj.statuses);
      }
      if (Array.isArray(filterObj.faultIds) && filterObj.faultIds.length > 0) {
        params.faultIds = JSON.stringify(filterObj.faultIds);
      }
      if (Array.isArray(filterObj.reasons) && filterObj.reasons.length > 0) {
        params.removalReasons = JSON.stringify(filterObj.reasons);
      }
    }
  }

  // Transform multiple EDP alarms to app format
  private transformEDPAlarmsToAppFormat(edpAlarms: EDPAlarm[]): Alarm[] {
    return edpAlarms.map(edpAlarm =>
      this.transformEDPAlarmToAppFormat(edpAlarm),
    );
  }

  // Transform single EDP alarm to app format
  private transformEDPAlarmToAppFormat(edpAlarm: EDPAlarm): Alarm {
    return {
      id: edpAlarm.id || '',
      faultId: edpAlarm.faultId || '',
      title: edpAlarm.faultDescription || '',
      description: edpAlarm.faultDescription || '',
      tag: edpAlarm.faultTag || '',
      severity: edpAlarm.severity || undefined,
      location: {
        area: edpAlarm.sectionArea || '',
        section: edpAlarm.sectionName || '',
        equipment: edpAlarm.equipmentName || '',
      },
      timing: {
        // Reference times (REF)
        startTime: edpAlarm.faultStartTimeUtc
          ? new Date(edpAlarm.faultStartTimeUtc)
          : new Date(),
        endTime: edpAlarm.faultEndTimeUtc
          ? new Date(edpAlarm.faultEndTimeUtc)
          : undefined,
        duration: edpAlarm.faultDuration
          ? edpAlarm.faultDuration.toString()
          : '0',
        updatedStartTime: edpAlarm.updatedStartTimeUtc
          ? new Date(edpAlarm.updatedStartTimeUtc)
          : undefined,
        updatedEndTime: edpAlarm.updatedEndTimeUtc
          ? new Date(edpAlarm.updatedEndTimeUtc)
          : undefined,
        updatedDuration:
          edpAlarm.updatedDuration !== null &&
          edpAlarm.updatedDuration !== undefined
            ? edpAlarm.updatedDuration.toString()
            : undefined,
        // Split times (SPLIT)
        splitStartTime: edpAlarm.splitStartTimeUtc
          ? new Date(edpAlarm.splitStartTimeUtc)
          : undefined,
        splitEndTime: edpAlarm.splitEndTimeUtc
          ? new Date(edpAlarm.splitEndTimeUtc)
          : undefined,
        splitDuration: edpAlarm.splitDuration
          ? edpAlarm.splitDuration.toString()
          : undefined,
        // Original times (ORIG)
        origStartTime: edpAlarm.origStartTimeUtc
          ? new Date(edpAlarm.origStartTimeUtc)
          : undefined,
        origEndTime: edpAlarm.origEndTimeUtc
          ? new Date(edpAlarm.origEndTimeUtc)
          : undefined,
        origDuration: edpAlarm.origDuration
          ? edpAlarm.origDuration.toString()
          : undefined,
      },
      status: edpAlarm.removalStatus || '',
      reason: edpAlarm.removalReason || '',
      comments: edpAlarm.comments || '',
    };
  }
}
