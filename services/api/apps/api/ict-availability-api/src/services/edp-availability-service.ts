import {
  <PERSON><PERSON><PERSON><PERSON>,
  WinstonLogger,
  EnvironmentService,
  ContextService,
  IctError,
  Container,
} from 'ict-api-foundations';
import {GoogleAuth} from 'google-auth-library';
import axios, {AxiosError, AxiosResponse} from 'axios';
import {AlarmCreateDto} from '../defs/alarm-create-dto.ts';
import {AlarmUpdateDto} from '../defs/alarm-update-dto.ts';

// todo move to a defs file
export interface EDPAlarmQueryParams {
  page?: number;
  limit?: number;
  sortBy?:
    | 'sectionArea'
    | 'sectionName'
    | 'equipmentName'
    | 'faultId'
    | 'faultDescription'
    | 'faultTag'
    | 'severity'
    | 'faultStartTimeUtc'
    | 'faultEndTimeUtc'
    | 'faultDuration'
    | 'splitStartTimeUtc'
    | 'splitEndTimeUtc'
    | 'splitDuration'
    | 'origStartTimeUtc'
    | 'origEndTimeUtc'
    | 'origDuration'
    | 'refDuration'
    | 'removalStatus'
    | 'removalReason'
    | 'updatedStartTimeUtc'
    | 'updatedEndTimeUtc'
    | 'updatedDuration'
    | 'duration'
    | 'comments';
  orderBy?: 'ASC' | 'DESC';
  startTimestamp?: string;
  endTimestamp?: string;
  sectionAreas?: string;
  sectionNames?: string;
  equipmentNames?: string;
  faultIds?: string;
  removalStatuses?: string;
  removalReasons?: string;
  searchTerm?: string;
}

export interface EDPEquipmentNamesQueryParams {
  page?: number;
  limit?: number;
  searchTerm?: string;
}

export interface EDPSectionsQueryParams {
  page?: number;
  limit?: number;
  searchTerm?: string;
}

export interface EDPAlarm {
  id: string;
  sectionArea: string;
  sectionName: string;
  equipmentName: string;
  faultId: string;
  faultDescription: string;
  faultTag: string;
  severity?: string;
  faultStartTimeUtc: string;
  faultEndTimeUtc: string;
  faultDuration: number;
  splitStartTimeUtc: string;
  splitEndTimeUtc: string;
  splitDuration: number;
  origStartTimeUtc: string;
  origEndTimeUtc: string;
  origDuration: number;
  removalStatus: string;
  removalReason: string;
  updatedStartTimeUtc: string;
  updatedEndTimeUtc: string;
  updatedDuration: number;
  comments?: string;
}

export interface EDPSingleAlarmResponse {
  statusCode: number;
  success: boolean;
  message: string;
  data: EDPAlarm;
}

export interface EDPAlarmsResponse {
  data: EDPAlarm[];
  metadata: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface EDPEquipmentNamesResponse {
  data: string[];
  metadata: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface EDPSectionsResponse {
  data: {name: string; pk: number}[];
  metadata: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

// service that wraps calls to the edp availability api
// handles Google Cloud Identity Token authentication with caching.

@DiService()
export class EDPAvailabilityService {
  private cachedToken: string | null = null;
  private tokenExpiry = 0;
  private readonly baseUrl: string;
  private readonly projectId: string;
  private logger: WinstonLogger;
  private envService: EnvironmentService;
  private context: ContextService;

  constructor() {
    this.logger = Container.get(WinstonLogger);
    this.envService = Container.get(EnvironmentService);
    this.context = Container.get(ContextService);

    this.baseUrl =
      this.envService.systemAvailability.url ||
      'http://host.docker.internal:3000';

    this.projectId =
      this.envService.systemAvailability.projectId || 'edp-d-us-east2-etl';

    this.logger.info('EDP Service initialized', {
      baseUrl: this.baseUrl,
      projectId: this.projectId,
    });
  }

  // get list of alarms with filtering options (idk what filters are available yet)

  public async getAlarms(
    tenantBid: string,
    facilityBid: string,
    queryParams: EDPAlarmQueryParams = {},
  ): Promise<EDPAlarmsResponse> {
    this.logger.info('Getting alarms from EDP API', {
      tenantBid,
      facilityBid,
      queryParams,
    });

    try {
      const token = await this.getAuthToken();
      const headers = this.buildRequestHeaders(tenantBid, facilityBid, token);

      const response: AxiosResponse<EDPAlarmsResponse> = await axios.get(
        `${this.baseUrl}/alarms`,
        {
          headers,
          params: queryParams,
        },
      );

      this.logger.info('Successfully retrieved alarms from EDP API', {
        tenantBid,
        facilityBid,
        totalAlarms: response.data.metadata.total,
        page: response.data.metadata.page,
      });

      return response.data;
    } catch (error: unknown) {
      this.logger.error('Failed to retrieve alarms from EDP API', {
        error,
        tenantBid,
        facilityBid,
        queryParams,
      });

      if (error instanceof AxiosError) {
        if (error.response?.status === 401) {
          // Token might be expired, clear cache for next attempt
          this.cachedToken = null;
          this.tokenExpiry = 0;
          throw IctError.unauthorized('EDP API authentication failed');
        }

        if (error.response?.status === 400) {
          throw IctError.badRequest('Invalid request parameters for EDP API');
        }

        if (error.response?.status === 404) {
          throw IctError.notFound('EDP API endpoint not found');
        }
      }

      throw IctError.internalServerError(
        'Failed to retrieve alarms from EDP API',
        this.formatError(error),
      );
    }
  }

  // get alarm by id
  public async getAlarmById(
    tenantBid: string,
    facilityBid: string,
    alarmId: string,
  ): Promise<EDPAlarm> {
    this.logger.info('Getting alarm by ID from EDP API', {
      tenantBid,
      facilityBid,
      alarmId,
    });

    try {
      const token = await this.getAuthToken();
      const headers = this.buildRequestHeaders(tenantBid, facilityBid, token);

      const response: AxiosResponse<EDPSingleAlarmResponse> = await axios.get(
        `${this.baseUrl}/alarms/${alarmId}`,
        {
          headers,
        },
      );

      this.logger.info('Successfully retrieved alarm by ID from EDP API', {
        tenantBid,
        facilityBid,
        alarmId,
        faultId: response.data.data.faultId,
      });

      return response.data.data;
    } catch (error: unknown) {
      this.logger.error('Failed to retrieve alarm by ID from EDP API', {
        error,
        tenantBid,
        facilityBid,
        alarmId,
      });

      if (error instanceof AxiosError) {
        if (error.response?.status === 401) {
          // Token might be expired, clear cache for next attempt
          this.cachedToken = null;
          this.tokenExpiry = 0;
          throw IctError.unauthorized('EDP API authentication failed');
        }

        if (error.response?.status === 404) {
          throw IctError.notFound(
            `Alarm with ID ${alarmId} not found in EDP system`,
          );
        }
      }

      throw IctError.internalServerError(
        'Failed to retrieve alarm from EDP API',
        this.formatError(error),
      );
    }
  }

  public async createAlarm(
    tenantBid: string,
    facilityBid: string,
    alarm: AlarmCreateDto,
  ): Promise<void> {
    this.logger.info('Creating alarm in EDP API', {
      tenantBid,
      facilityBid,
      alarm,
    });

    try {
      const token = await this.getAuthToken();
      const headers = this.buildRequestHeaders(tenantBid, facilityBid, token);

      await axios.post(`${this.baseUrl}/alarms/`, alarm, {
        headers,
      });

      this.logger.info('Successfully created alarm in EDP API', {
        tenantBid,
        facilityBid,
        alarm,
      });
    } catch (error: unknown) {
      this.logger.error('Failed to create alarm in EDP API', {
        error,
        tenantBid,
        facilityBid,
        alarm,
      });

      if (error instanceof AxiosError) {
        if (error.response?.status === 401) {
          // Token might be expired, clear cache for next attempt
          this.cachedToken = null;
          this.tokenExpiry = 0;
          throw IctError.unauthorized('EDP API authentication failed');
        }

        if (error.response?.status === 400) {
          throw IctError.badRequest(`Invalid alarm data provided`);
        }
      }

      throw IctError.internalServerError(
        'Failed to create alarm in EDP API',
        this.formatError(error),
      );
    }
  }

  public async updateAlarm(
    tenantBid: string,
    facilityBid: string,
    alarm: AlarmUpdateDto,
  ): Promise<void> {
    this.logger.info('Updating alarm in EDP API', {
      tenantBid,
      facilityBid,
      alarmId: alarm.id,
      alarm,
    });

    try {
      const token = await this.getAuthToken();
      const headers = this.buildRequestHeaders(tenantBid, facilityBid, token);

      // Convert Date objects to ISO strings for external API
      const alarmPayload = {
        ...alarm,
        startDateLocal: alarm.startDateLocal.toISOString(),
        endDateLocal: alarm.endDateLocal.toISOString(),
      };

      await axios.patch(`${this.baseUrl}/alarms`, alarmPayload, {
        headers,
      });

      this.logger.info('Successfully updated alarm in EDP API', {
        tenantBid,
        facilityBid,
        alarmId: alarm.id,
        alarm,
      });
    } catch (error: unknown) {
      this.logger.error('Failed to update alarm in EDP API', {
        error,
        tenantBid,
        facilityBid,
        alarmId: alarm.id,
        alarm,
      });

      if (error instanceof AxiosError) {
        if (error.response?.status === 401) {
          // Token might be expired, clear cache for next attempt
          this.cachedToken = null;
          this.tokenExpiry = 0;
          throw IctError.unauthorized('EDP API authentication failed');
        }

        if (error.response?.status === 400) {
          throw IctError.badRequest(`Invalid alarm data provided`);
        }

        if (error.response?.status === 404) {
          throw IctError.notFound(
            `Alarm with ID ${alarm.id} not found in EDP system`,
          );
        }
      }

      throw IctError.internalServerError(
        'Failed to update alarm in EDP API',
        this.formatError(error),
      );
    }
  }

  public async getAvailableEquipmentNames(
    tenantBid: string,
    facilityBid: string,
    queryParams: EDPEquipmentNamesQueryParams = {},
  ): Promise<EDPEquipmentNamesResponse> {
    this.logger.info('Getting available equipment names from EDP API', {
      tenantBid,
      facilityBid,
      queryParams,
    });

    try {
      const token = await this.getAuthToken();
      const headers = this.buildRequestHeaders(tenantBid, facilityBid, token);

      const response: AxiosResponse<EDPEquipmentNamesResponse> =
        await axios.get(`${this.baseUrl}/equipment/names/list`, {
          headers,
          params: queryParams,
        });

      this.logger.info(
        'Successfully retrieved available equipment names from EDP API',
        {
          tenantBid,
          facilityBid,
          totalItems: response.data.metadata.total,
          page: response.data.metadata.page,
        },
      );

      return response.data;
    } catch (error: unknown) {
      this.logger.error(
        'Failed to retrieve available equipment names from EDP API',
        {
          error,
          tenantBid,
          facilityBid,
          queryParams,
        },
      );

      if (error instanceof AxiosError) {
        if (error.response?.status === 401) {
          // Token might be expired, clear cache for next attempt
          this.cachedToken = null;
          this.tokenExpiry = 0;
          throw IctError.unauthorized('EDP API authentication failed');
        }

        if (error.response?.status === 400) {
          throw IctError.badRequest('Invalid request parameters for EDP API');
        }

        if (error.response?.status === 404) {
          throw IctError.notFound('EDP API endpoint not found');
        }
      }

      throw IctError.internalServerError(
        'Failed to retrieve available equipment names from EDP API',
        this.formatError(error),
      );
    }
  }

  public async getAvailableSections(
    tenantBid: string,
    facilityBid: string,
    queryParams: EDPSectionsQueryParams = {},
  ): Promise<EDPSectionsResponse> {
    this.logger.info('Getting available sections from EDP API', {
      tenantBid,
      facilityBid,
      queryParams,
    });

    try {
      const token = await this.getAuthToken();
      const headers = this.buildRequestHeaders(tenantBid, facilityBid, token);

      const response: AxiosResponse<EDPSectionsResponse> = await axios.get(
        `${this.baseUrl}/sections/list`,
        {
          headers,
          params: queryParams,
        },
      );

      this.logger.info(
        'Successfully retrieved available sections from EDP API',
        {
          tenantBid,
          facilityBid,
          totalItems: response.data.metadata.total,
          page: response.data.metadata.page,
        },
      );

      return response.data;
    } catch (error: unknown) {
      this.logger.error('Failed to retrieve available sections from EDP API', {
        error,
        tenantBid,
        facilityBid,
        queryParams,
      });

      if (error instanceof AxiosError) {
        if (error.response?.status === 401) {
          // Token might be expired, clear cache for next attempt
          this.cachedToken = null;
          this.tokenExpiry = 0;
          throw IctError.unauthorized('EDP API authentication failed');
        }

        if (error.response?.status === 400) {
          throw IctError.badRequest('Invalid request parameters for EDP API');
        }

        if (error.response?.status === 404) {
          throw IctError.notFound('EDP API endpoint not found');
        }
      }

      throw IctError.internalServerError(
        'Failed to retrieve available sections from EDP API',
        this.formatError(error),
      );
    }
  }

  // health check endpoint (no authentication required)

  public async healthCheck(): Promise<string> {
    try {
      const response: AxiosResponse<string> = await axios.get(
        `${this.baseUrl}/health`,
      );

      this.logger.info('EDP API health check successful');
      return response.data;
    } catch (error: unknown) {
      this.logger.error('EDP API health check failed', {
        error: this.formatError(error),
      });
      throw IctError.internalServerError(
        'EDP API health check failed',
        this.formatError(error),
      );
    }
  }

  // get and cache Google Cloud Identity Token
  // based on the pattern from AIService

  private async getAuthToken(): Promise<string> {
    const now = Date.now();

    // Return cached token if still valid (with 5 minute buffer)
    if (this.cachedToken && now < this.tokenExpiry - 300000) {
      this.logger.debug('Using cached EDP API token');
      return this.cachedToken;
    }

    this.logger.info('Fetching new Google Cloud Identity Token for EDP API');

    try {
      const auth = new GoogleAuth();
      const nodeEnv = process.env.ENVIRONMENT;

      // For local development, use LOCAL_ID_TOKEN if available
      if (nodeEnv === 'local') {
        const localToken = process.env.LOCAL_ID_TOKEN;
        if (localToken) {
          this.logger.info('Using local development token for EDP API');
          this.cachedToken = localToken;
          this.tokenExpiry = now + 3600000; // 1 hour
          return localToken;
        }
      }

      // Get ID token for the EDP API audience
      const client = await auth.getIdTokenClient(this.baseUrl);
      const token = await client.idTokenProvider.fetchIdToken(this.baseUrl);

      if (!token) {
        throw new Error('Failed to obtain Google Cloud Identity Token');
      }

      // Cache the token (typical ID tokens expire in 1 hour)
      this.cachedToken = token;
      this.tokenExpiry = now + 3600000; // 1 hour

      this.logger.info('Successfully obtained and cached EDP API token');
      return token;
    } catch (error) {
      this.logger.error(
        'Failed to obtain Google Cloud Identity Token for EDP API',
        {error},
      );
      throw IctError.internalServerError(
        'Failed to authenticate with EDP API',
        error,
      );
    }
  }

  // build request headers for EDP API calls

  private buildRequestHeaders(
    tenantBid: string,
    facilityBid: string,
    token: string,
  ): Record<string, string> {
    return {
      Authorization: `Bearer ${token}`,
      'edp-tenant-bid': tenantBid,
      'edp-facility-bid': facilityBid,
      'Content-Type': 'application/json',
    };
  }

  /**
   * Helper to safely convert unknown error to string
   */
  private formatError(error: unknown): string {
    if (error instanceof Error) {
      return error.message;
    }
    return String(error);
  }

  // clear cached token (useful for testing or forced refresh)

  public clearTokenCache(): void {
    this.logger.info('Clearing EDP API token cache');
    this.cachedToken = null;
    this.tokenExpiry = 0;
  }
}
