import {ContextService, IctError, <PERSON><PERSON>ogger} from 'ict-api-foundations';

export class ContextUtils {
  public static getTenantId(
    context: ContextService,
    logger: WinstonLog<PERSON>,
  ): string {
    const tenantId = context.datasetId.split('_')[0];
    if (!tenantId) {
      logger.error('Tenant ID not found in context', {
        context,
      });
      throw IctError.badRequest('Tenant ID not found in context');
    }
    return tenantId;
  }

  public static getFacilityId(
    context: ContextService,
    logger: WinstonLogger,
  ): string {
    const facilityId = context.datasetId.split('_')[1];
    if (!facilityId) {
      logger.error('Facility ID not found in context', {
        context,
      });
      throw IctError.badRequest('Facility ID not found in context');
    }
    return facilityId;
  }
}
