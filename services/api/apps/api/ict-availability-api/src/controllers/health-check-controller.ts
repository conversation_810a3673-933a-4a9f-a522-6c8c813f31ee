import {BaseHealthCheck} from 'ict-api-foundations';
import {Get, Route, OperationId, Tags} from 'tsoa';

@Route('/availability/healthcheck')
export class HealthCheckController {
  @Get()
  @OperationId('GetAdminBasicHealthCheck')
  @Tags('availability')
  public getAdminBasicHealthCheck() {
    return BaseHealthCheck.getBasicHealthCheck();
  }

  @Get('/full')
  @OperationId('GetAvailabilityFullHealthCheck')
  @Tags('availability')
  public getAvailabilityFullHealthCheck() {
    return BaseHealthCheck.getDeepHealthCheck(['auth0']);
  }
}
