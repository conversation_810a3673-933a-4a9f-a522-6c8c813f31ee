import {
  Container,
  ProtectedRouteMiddleware,
  WinstonLogger,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Get,
  Middlewares,
  OperationId,
  Route,
  SuccessResponse,
  Tags,
  Query,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.js';
import {GetAvailableEquipmentNamesResponse} from '../defs/equipment-def.js';

@Route('availability')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [configPostgresDatabase()],
  }),
])
export class EquipmentController extends Controller {
  /**
   * Get available equipment names with filters using offset-based pagination
   *
   * @param {number} offset - Record offset for pagination (0-based)
   * @param {number} limit - Maximum number of records to return
   * @param {string} searchTerm - Optional search term to filter equipment names
   * @returns {Promise<GetAvailableEquipmentNamesResponse>} Paginated response with available equipment names
   * @throws IctError
   */
  @SuccessResponse('200', 'Successfully retrieved available equipment names')
  @Get('/equipment/names/list')
  @OperationId('GetAvailableEquipmentNames')
  @Tags('availability')
  public async getAvailableEquipmentNames(
    @Query() offset?: number,
    @Query() limit?: number,
    @Query() searchTerm?: string,
  ): Promise<GetAvailableEquipmentNamesResponse> {
    const logger = Container.get(WinstonLogger);
    const equipmentService = Container.get(EquipmentService);

    logger.info('Getting available equipment names list', {
      offset,
      limit,
      searchTerm,
    });

    return await equipmentService.getAvailableEquipmentNames({
      offset,
      limit,
      searchTerm,
    });
  }
}
