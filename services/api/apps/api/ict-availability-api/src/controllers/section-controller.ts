import {
  Container,
  ProtectedRouteMiddleware,
  WinstonLogger,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Get,
  Middlewares,
  OperationId,
  Route,
  SuccessResponse,
  Tags,
  Query,
} from 'tsoa';
import {SectionService} from '../services/section-service.js';
import {GetAvailableSectionsResponse} from '../defs/section-def.js';

@Route('availability')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [configPostgresDatabase()],
  }),
])
export class SectionController extends Controller {
  /**
   * Get available sections with filters using offset-based pagination
   *
   * @param {number} offset - Record offset for pagination (0-based)
   * @param {number} limit - Maximum number of records to return
   * @param {string} searchTerm - Optional search term to filter sections
   * @returns {Promise<GetAvailableSectionsResponse>} Paginated response with available sections
   * @throws IctError
   */
  @SuccessResponse('200', 'Successfully retrieved available sections')
  @Get('/sections/list')
  @OperationId('GetAvailableSections')
  @Tags('availability')
  public async getAvailableSections(
    @Query() offset?: number,
    @Query() limit?: number,
    @Query() searchTerm?: string,
  ): Promise<GetAvailableSectionsResponse> {
    const logger = Container.get(WinstonLogger);
    const sectionService = Container.get(SectionService);

    logger.info('Getting available sections list', {
      offset,
      limit,
      searchTerm,
    });

    return await sectionService.getAvailableSections({
      offset,
      limit,
      searchTerm,
    });
  }
}
