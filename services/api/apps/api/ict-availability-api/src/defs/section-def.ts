import {ApiResponseArray} from '@ict/sdk-foundations/types';

// Pagination metadata for sections
export interface SectionsPaginationInfo {
  page: number;
  limit: number;
  totalResults: number;
  totalPages: number;
}

// Standard API response type for available sections
export type GetAvailableSectionsResponse = ApiResponseArray<
  {
    name: string;
    pk: number;
  }[],
  SectionsPaginationInfo
>;
