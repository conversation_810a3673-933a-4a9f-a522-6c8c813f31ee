{"name": "ict-availability-api", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/ict-availability-api/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "ict-availability-api:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "ict-availability-api:build:development"}, "production": {"buildTarget": "ict-availability-api:build:production"}}}}}