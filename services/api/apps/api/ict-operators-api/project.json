{"name": "ict-operators-api", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/ict-operators-api/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "ict-operators-api:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "ict-operators-api:build:development"}, "production": {"buildTarget": "ict-operators-api:build:production"}}}}}