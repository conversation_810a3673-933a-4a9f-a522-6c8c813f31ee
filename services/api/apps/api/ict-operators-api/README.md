# Operators API Cloud Function

This handles all API calls involving Operators within Control Tower.

---

## Setting up for Local Development

If you are working on any part of the monorepo, you will need to bootstrap to ensure that all of the packages are synced and linked together with yarn as a first step, as this API package has dependencies to other packages within the monorepo, such as the Foundations package.

To do this:

- Ensure your terminal's current directory is set to the root "/control-tower-api/" folder.
- Run the yarn command.
  - `yarn run setup`

You WILL run into compilation errors when compiling the Typescript for this API package if you do not perform this process, as the compiler won't know where to find the locally linked packages that are dependencies.

## Run on a Local Machine

### Requirements

In order to run this on a local dev machine, you must have the following installed:

- gcloud CLI (<https://cloud.google.com/sdk/gcloud>)

If you want to run the Docker container (preferred):

- Docker Engine (<https://docs.docker.com/engine/install/>)
- If on Mac or Windows, you must use Docker Desktop which requires a license for commercial use.
- If on Windows, you can use just Docker Engine and circumvent the Docker Desktop requirement by installing Ubuntu on WSL (<https://ubuntu.com/tutorials/install-ubuntu-on-wsl2-on-windows-11-with-gui-support#1-overview>) and then installing the Docker Engine within Ubuntu.

If you want to run directly in Node:

- Node.js (<https://nodejs.dev/en/learn/how-to-install-nodejs/>)
- yarn (`npm install -g yarn`)

### Docker Container (preferred)

Running the Docker container locally is the preferred way of running and debugging the Node app locally as this is how it will run when deployed to the cloud.

- If have not run a gcloud command before, you must initialize the SDK environment. (<https://cloud.google.com/sdk/gcloud/reference/init>)
- Ensure you have the Default Application Credentials for the right GCP project.
  - Switch to the GCP project that has the necessary resources for the API.
    - `gcloud config set project $MY_PROJECT_ID`
  - Acquire the Default Application Credentials for that project.
    - `gcloud auth application-default login`
- Build the API.
  - Ensure you are in the root folder of the repo.
  - `yarn run setup`
- Build the Docker container.
  - Ensure you are in the root folder of the repo. This is because the Dockerfile needs to copy the API Foundations files.
  - `docker build -t operators-api -f ./cloudrun/operators-api/Dockerfile .`
- After the Docker container finishes building, run the Docker container with the following parameters:
  - `docker run -d -v "$HOME/.config/gcloud/application_default_credentials.json":/gcp/creds.json:ro --env GOOGLE_APPLICATION_CREDENTIALS=/gcp/creds.json --env ENV=DEV -p {port}:8080 -p 9229:9229 operators-api`
  - {port} is any available port on the host machine.
  - If running in a Mac or Windows environment, the Default Application Credentials path on the host machine may be different than the above example.
- The API should be now reachable on the localhost at the specified port.
  - Ex: <http://localhost:8080/> if {port} = 8080
- To stop the Docker container, find the running container's name and stop it.
  - `docker container ls`
  - `docker stop {container_id}`

### Running in Node

- If have not run a gcloud command before, you must initialize the SDK environment. (<https://cloud.google.com/sdk/gcloud/reference/init>)
- Ensure you have the Default Application Credentials for the right GCP project.
  - Switch to the GCP project that has the necessary resources for the API.
    - `gcloud config set project $MY_PROJECT_ID`
  - Acquire the Default Application Credentials for that project.
    - `gcloud auth application-default login`
- Install the Node packages.
  - `yarn`
- Run the Node application.
  - `yarn start`
- The API should be now reachable on the localhost at port 8080.
- Ex: <http://localhost:8080/>

## Debug on a Local Machine

This guide shows how to be able to debug and step through code running in a local Docker container with Visual Studio Code (<https://code.visualstudio.com/>).

### Debugging the Docker Container

- Ensure the 'Docker' extension is installed in Visual Studio Code.
- Ensure the root directory of the open workspace in Visual Studio Code is /operators-api/, otherwise VS Code won't pick up the debugging configurations and the debugger will fail to map the source files.
- Run the Docker container locally following the above instructions.
- Open the 'Run and Debug' menu by clicking the icon on the left sidebar.
- Ensure "Docker: Attach to Node" is selected in the dropdown and click the green arrow.
- You should now see the output of the container in the Debug Console and should be able to pause and set breakpoints in the code running within the Docker container.

### Debugging the Node App Directly

- Ensure the root directory of the open workspace in Visual Studio Code is /operators-api/, otherwise the VS Code won't pick up the debugging configurations.
- Ensure you have built your latest code.
  - `yarn compile`
- Open the 'Run and Debug' menu by clicking the icon on the left sidebar.
- Ensure "Launch Node" is selected in the dropdown and click the green arrow.
- You should now see the output of the launched Node app in the Debug Console and should be able to pause and set breakpoints in the code running within the Node application within the Typescript files.

## ESLINT run command

Implemented ESLINT to check for conflicts between ESLINT and prettier. In order to run the command use `yarn run lint`. The command console will alert if any packages cause conflict.
