import {
  ConfigStore,
  ContextService,
  DatabaseProvider,
  DiService,
  IctError,
} from 'ict-api-foundations';
import {Query} from '@google-cloud/bigquery';
import {AreaStatus} from '../defs/reassign-operator-def.ts';
import {FacilityMap} from '../entities/facility-map.ts';
import {OperatorsChartSeriesQueryResponse} from '../defs/operator-chart-def.ts';
import {OperatorAreasQueryResponse} from '../defs/operator-areas-def.ts';
import {OperatorCount} from '../defs/operator-count-def.ts';

@DiService()
export class OperatorStore {
  constructor(
    private context: ContextService,
    private configStore: ConfigStore
  ) {}

  get dbProvider(): DatabaseProvider {
    return this.context.dbProvider;
  }

  async getOperatorAreas(): Promise<OperatorAreasQueryResponse[]> {
    const bigQueryDb = this.dbProvider.bigQuery;
    const dimensionsAreaTable = bigQueryDb.getFullTablePath(
      'demo01_internal_dimensions_area'
    );
    const areaStatusTable = bigQueryDb.getFullTablePath(
      'demo01_internal_area_status_v2'
    );

    const sql = `
      SELECT areaDimensions.name,
        areaDimensions.id,
        areaStatus.active_operators,
        areaStatus.low_rec_operators,
        areaStatus.high_rec_operators,
        areaStatus.max_operators,
        areaStatus.pick_rate,
        areaStatus.queued_orders,
        areaStatus.status,
        areaStatus.identifier,
        areaStatus.message,
      FROM \`${dimensionsAreaTable}\` AS areaDimensions
      JOIN \`${areaStatusTable}\` AS areaStatus
      ON areaDimensions.id = areaStatus.areaId
    `;

    const sqlOptions = {
      query: sql,
    };

    const [areaRows] = await bigQueryDb.executeMonitoredJob(sqlOptions);
    return areaRows as OperatorAreasQueryResponse[];
  }

  async getFacilityMapAsync(mapId: string): Promise<FacilityMap | undefined> {
    const bigQueryDb = this.dbProvider.bigQuery;

    // query the database for a map with a matching mapId
    const sql = `
            SELECT * FROM \`${bigQueryDb.getFullTablePath(
              'demo01_internal_area_map'
            )}\`
            WHERE id = @mapId
            LIMIT 1
        `;
    const sqlOptions: Query = {
      query: sql,
      params: {
        mapId,
      },
    };

    const [mapRows] = await bigQueryDb.executeMonitoredJob(sqlOptions);
    if (!mapRows || !mapRows.length) {
      return undefined;
    }

    return mapRows[0] as FacilityMap;
  }

  /**
   * Gets the status of the area ids.
   * @param areaIds list of area ids to get the status of
   * @returns list of status of all found areas
   */
  async getAreaStatus(areaIds: string[]): Promise<AreaStatus[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const sql = `
      SELECT * FROM \`${bigQueryDb.getFullTablePath(
        'demo01_internal_area_status'
      )}\`
      WHERE areaId IN UNNEST(@areaIds);
    `;
    const sqlOptions: Query = {
      query: sql,
      params: {
        areaIds,
      },
    };
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);
    return rows as AreaStatus[];
  }

  async updatePositionStatus(
    areaIds: string[],
    areaTotalMapping: {areaId: string; activeOperators: number}[]
  ): Promise<void> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const updateAllSql = `
      UPDATE \`${bigQueryDb.getFullTablePath(
        'demo01_internal_area_status'
      )}\` old_status
      SET active_operators = new_status.activeOperators
      FROM (
        SELECT areaId, activeOperators
        FROM UNNEST(@areaTotalMapping)
      ) new_status
      WHERE old_status.areaId IN UNNEST(@allAreaIds)
      AND old_status.areaId = new_status.areaId;
    `;

    const updateSqlOptions: Query = {
      query: updateAllSql,
      params: {
        allAreaIds: areaIds,
        areaTotalMapping,
      },
    };
    await bigQueryDb.executeMonitoredJob(updateSqlOptions);
  }

  /**
   * Retrieve the total number of operators for a specified date range.
   * @param startDate Start date for the date range.
   * @param endDate End date for the date range.
   * @returns Array of OperatorCounts objects for the specified date range
   */
  async getOperatorCountsAsync(
    startDate: string,
    endDate: string
  ): Promise<OperatorCount> {
    try {
      const bigQueryDb = this.dbProvider.bigQuery;

      const platinumOperatorsOnline =
        this.dbProvider.bigQuery.getEdpFullTablePath(
          'platinum_operators_online'
        );

      const sql = `
          SELECT 
            online_operators AS activeOperators
          FROM
            ${platinumOperatorsOnline}
          WHERE
            etl_create_timestamp_utc <= @endDate
          ORDER BY 
            etl_create_timestamp_utc DESC
          LIMIT 1;`;

      const sqlOptions: Query = {
        query: sql,
        params: {
          startDate,
          endDate,
        },
      };

      // execute the query and return the results
      const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

      // default to 0 if rows is empty
      return {
        activeOperators: (rows[0]?.activeOperators ?? 0) as number,
      };
    } catch (error) {
      throw IctError.internalServerError(
        'Failed to retrieve total operator count data',
        error
      );
    }
  }

  async getOperatorChartData(
    startDate: string,
    endDate: string
  ): Promise<OperatorsChartSeriesQueryResponse[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const platinumOperatorsOnline =
      this.dbProvider.bigQuery.getEdpFullTablePath('platinum_operators_online');

    const sql = `
        SELECT 
          online_operators,
          etl_create_timestamp_utc as record_timestamp,
        FROM
          \`${platinumOperatorsOnline}\`
          WHERE etl_create_timestamp_utc BETWEEN @startDate AND @endDate
        ORDER BY etl_create_timestamp_utc ASC
      `;

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length) {
      throw IctError.noContent();
    }

    return rows as OperatorsChartSeriesQueryResponse[];
  }
}
