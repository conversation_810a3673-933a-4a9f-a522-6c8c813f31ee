import {expect} from 'chai';
import sinon from 'sinon';
import {HealthStatus} from 'ict-api-foundations';
import {OperatorStore} from '../../stores/operator-store.ts';
import {OperatorService} from '../../services/operator-service.ts';
import {AreaStatus, FromArea} from '../../defs/reassign-operator-def.ts';
import {
  OperatorAreasQueryResponse,
  OperatorFacilityArea,
} from '../../defs/operator-areas-def.ts';

describe('OperatorService', () => {
  let operatorStoreStub: sinon.SinonStubbedInstance<OperatorStore>;
  let operatorService: OperatorService;

  beforeEach(() => {
    operatorStoreStub = sinon.createStubInstance(OperatorStore);
    operatorService = new OperatorService(operatorStoreStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getOperatorAreas', () => {
    let fakeStoreResponse: OperatorAreasQueryResponse[];

    beforeEach(() => {
      fakeStoreResponse = [
        {
          name: 'test area',
          id: 'test id',
          active_operators: 2,
          low_rec_operators: 1,
          high_rec_operators: 4,
          max_operators: 5,
          pick_rate: 8,
          queued_orders: 6,
          status: HealthStatus.Ok,
          identifier: undefined,
          message: undefined,
        },
      ];
      operatorStoreStub.getOperatorAreas.resolves(fakeStoreResponse);
    });

    it('should return the correct data format', async () => {
      const resp = await operatorService.getActiveAreas();
      const expected: OperatorFacilityArea[] = fakeStoreResponse.map(fsr => ({
        id: fsr.id,
        name: fsr.name,
        alertStatus: {
          status: fsr.status,
          identifier: fsr.identifier,
          message: fsr.message,
        },
        operators: {
          activeOperators: fsr.active_operators,
          lowRecOperators: fsr.low_rec_operators,
          highRecOperators: fsr.high_rec_operators,
          maxOperators: fsr.max_operators,
          pickRate: fsr.pick_rate,
          queuedOrders: fsr.queued_orders,
        },
      }));
      expect(resp).to.deep.equal(expected);
    });
  });

  // TODO: Test that the PUT request returns correct Promise
  describe('putReassignOperator', () => {
    it('should update the operator counts correctly', async () => {
      // Create mock data for the test
      const currentAreas: AreaStatus[] = [
        {
          areaId: 'testArea3',
          active_operators: 24,
          low_rec_operators: 32,
          high_rec_operators: 34,
          status: 'testStatus',
          alert: 'Workstation 5 supervisor requested assistance!',
          max_operators: 100,
          queued_orders: 24,
          pick_rate: 42.784,
        },
        {
          areaId: 'testArea4',
          active_operators: 60,
          low_rec_operators: 25,
          high_rec_operators: 31,
          status: 'testStatus',
          alert: 'Not enough active operators in area!',
          max_operators: 100,
          queued_orders: 28,
          pick_rate: 22.729,
        },
        {
          areaId: 'testArea5',
          active_operators: 40,
          low_rec_operators: 25,
          high_rec_operators: 31,
          status: 'testStatus',
          alert: 'Not enough active operators in area!',
          max_operators: 100,
          queued_orders: 28,
          pick_rate: 22.729,
        },
      ];
      const fromAreas: FromArea[] = [
        {area_id: 'testArea3', number_to_move: 3},
        {area_id: 'testArea3', number_to_move: 4},
      ];
      const toAreaId = 'testArea4';
      try {
        // Call the function and assert that no errors are thrown
        await operatorService.putReassignOperator(
          currentAreas,
          fromAreas,
          toAreaId
        );
      } catch (error) {
        // This code should not be reached
        console.error(error);
        expect.fail('No errors should be thrown');
      }
    });
  });
});
