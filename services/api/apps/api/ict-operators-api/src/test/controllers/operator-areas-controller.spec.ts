import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {OperatorService} from '../../services/operator-service.ts';
import {OperatorsActiveAreasController} from '../../controllers/operators-active-areas-controller.ts';

describe('OperatorAreasController', () => {
  const url: string = '/operators/active/areas';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Operator Service. */
  let operatorServiceStub: sinon.SinonStubbedInstance<OperatorService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Operator Areas Controller Error scenarios', () => {
    it('should error when the service throws an error', async () => {
      operatorServiceStub = sinon.createStubInstance(OperatorService);
      Container.set(OperatorService, operatorServiceStub);
      operatorServiceStub.getActiveAreas.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app).get(url);

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledOnce(operatorServiceStub.getActiveAreas);
    });
  });
  describe('Operator Areas Controller success scenarios', () => {
    beforeEach(() => {
      operatorServiceStub = sinon.createStubInstance(OperatorService);
      Container.set(OperatorService, operatorServiceStub);
      operatorServiceStub.getActiveAreas.resolves(
        OperatorsActiveAreasController.exampleData
      );
    });

    it('should call "OperatorServiceStub.getAverageOperatorAreas" and return the data', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        OperatorsActiveAreasController.exampleResponse
      );

      sinon.assert.calledOnce(operatorServiceStub.getActiveAreas);
    });
  });
});
