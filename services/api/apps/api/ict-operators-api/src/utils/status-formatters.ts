import {HealthStatus} from 'ict-api-foundations';

export function getOperatorStatus(
  activeOperators: number,
  low: number,
  high: number,
  max: number
): HealthStatus {
  // TODO: Implement logic here to determine the status based on active operators and the thresholds.
  if (activeOperators < low) return HealthStatus.Critical;
  if (activeOperators <= high) return HealthStatus.Ok;
  if (activeOperators <= max) return HealthStatus.Warning;
  return HealthStatus.Caution;
}
