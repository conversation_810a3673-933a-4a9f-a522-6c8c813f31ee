import {
  CacheMiddleware,
  Container,
  HealthStatus,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OperatorService} from '../services/operator-service.ts';
import {OperatorFacilityArea} from '../defs/operator-areas-def.ts';

@Route('operators')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OperatorsActiveAreasController extends Controller {
  public static readonly exampleData: OperatorFacilityArea[] = [
    {
      id: '1',
      name: 'test area',
      alertStatus: {
        status: HealthStatus.Ok,
      },
      operators: {
        activeOperators: 2,
        lowRecOperators: 1,
        highRecOperators: 4,
        maxOperators: 5,
        pickRate: 20,
        queuedOrders: 8,
      },
    },
  ];

  public static readonly exampleResponse: OperatorFacilityArea[] =
    OperatorsActiveAreasController.exampleData;

  /**
   *
   * @returns {Promise<OperatorFacilityArea[]>} contract
   */
  @Example<OperatorFacilityArea[]>(
    OperatorsActiveAreasController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/active/areas')
  @OperationId('GetOperatorsActiveAreas')
  @Tags('operators')
  public async getOperatorAreas(): Promise<OperatorFacilityArea[]> {
    // get the operator service
    const operatorService = Container.get(OperatorService);

    // attempt to load the area status data from the db
    return await operatorService.getActiveAreas();
  }
}
