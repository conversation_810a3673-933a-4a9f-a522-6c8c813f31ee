import {BaseHealthCheck} from 'ict-api-foundations';
import {Get, Route, OperationId, Tags} from 'tsoa';

@Route('/operators/healthcheck')
export class OperatorsHealthCheckController {
  @Get()
  @OperationId('GetOperatorsBasicHealthCheck')
  @Tags('operators')
  public getOperatorsBasicHealthCheck() {
    return BaseHealthCheck.getBasicHealthCheck();
  }

  @Get('/full')
  @OperationId('GetOperatorsFullHealthCheck')
  @Tags('operators')
  public getOperatorsFullHealthCheck() {
    return BaseHealthCheck.getDeepHealthCheck(['auth0', 'bigQuery', 'redis']);
  }
}
