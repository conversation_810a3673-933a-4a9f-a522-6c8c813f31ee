import {
  CacheMiddleware,
  BaseChartConfig,
  Container,
  ProtectedRouteMiddleware,
  startEndDateValidation,
  DatabaseTypes,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OperatorService} from '../services/operator-service.ts';
import {
  OperatorsChartSeriesApiResponse,
  OperatorsChartSeriesData,
} from '../defs/operator-chart-def.ts';

@Route('operators')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OperatorsActiveSeriesController extends Controller {
  public static readonly exampleData: OperatorsChartSeriesData = {
    operators: [
      {name: '2021-11-01T00:00:00.0000Z', value: 5},
      {name: '2021-11-01T01:00:00.0000Z', value: 10},
      {name: '2021-11-01T02:00:00.0000Z', value: 11},
      {name: '2021-11-01T03:00:00.0000Z', value: 10},
    ],
  };

  public static readonly exampleConfig: BaseChartConfig = {
    yMin: 0,
    yMax: 12,
    yHighBand: 8,
    yLowBand: 6,
  };

  public static readonly exampleResponse: OperatorsChartSeriesApiResponse = {
    operators: [
      {name: '2021-11-01T00:00:00.0000Z', value: 5},
      {name: '2021-11-01T01:00:00.0000Z', value: 10},
      {name: '2021-11-01T02:00:00.0000Z', value: 11},
      {name: '2021-11-01T03:00:00.0000Z', value: 10},
    ],
    metadata: OperatorsActiveSeriesController.exampleConfig,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {Promise<OperatorsChartSeriesApiResponse>} contract
   */
  @Example<OperatorsChartSeriesApiResponse>(
    OperatorsActiveSeriesController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/active/series')
  @OperationId('GetOperatorsActiveSeries')
  @Tags('operators')
  public async getOperatorChart(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<OperatorsChartSeriesApiResponse> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // get the operator service
    const operatorService = Container.get(OperatorService);

    const chartData = await operatorService.getActiveSeries(
      startDate.toISOString(),
      endDate.toISOString()
    );

    const maxValue = chartData.operators
      .map(o => o.value)
      .reduce((max, cur) => (max > cur ? max : cur), 0);
    const contract: OperatorsChartSeriesApiResponse = {
      ...chartData,
      metadata: {
        yMin: 0,
        yMax: Math.floor(maxValue * 1.1),
        // TODO: Get values from config db
        yHighBand: Math.floor(maxValue * 0.8),
        yLowBand: Math.floor(maxValue * 0.6),
      },
    };

    return contract;
  }
}
