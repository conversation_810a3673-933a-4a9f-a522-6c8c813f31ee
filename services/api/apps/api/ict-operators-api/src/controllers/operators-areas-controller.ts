import {
  CacheMiddleware,
  Container,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Body,
  Controller,
  FieldErrors,
  Middlewares,
  Put,
  Route,
  SuccessResponse,
  ValidateError,
  OperationId,
  Tags,
} from 'tsoa';
import {OperatorService} from '../services/operator-service.ts';
import {AreaStatus, FromArea} from '../defs/reassign-operator-def.ts';
import {OperatorStore} from '../stores/operator-store.ts';

// TODO: Move this out to separate def.
interface RequestBody {
  from_areas: FromArea[];
  to_area_id: string;
}

// TODO: We should move and rename this so that it doesn't conflict with the built in type.
interface Error {
  reason: string;
  message: string;
}

@Route('operators')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OperatorsAreasController extends Controller {
  /**
   * Validates the request data against current data.
   * @param currentAreas operator areas as they currently exist
   * @param fromAreas Areas to reassign operators from
   * @param toArea Area to reassign operators to
   * @returns list of errors found while validating
   */
  private static validateRequestData = (
    currentAreas: AreaStatus[],
    fromAreas: FromArea[],
    toArea: string
  ): Error[] => {
    const errors: Error[] = [];
    const allAreaIds = [toArea, ...fromAreas.map(fa => fa.area_id)];

    errors.push(
      ...OperatorsAreasController.validateAreasExist(currentAreas, allAreaIds)
    );
    if (errors.length > 0) return errors;

    errors.push(
      ...OperatorsAreasController.validateOperatorCounts(
        currentAreas,
        fromAreas
      )
    );
    if (errors.length > 0) return errors;

    return errors;
  };

  /**
   * Checks if the requested areas have enough active operators to complete the request.
   * @param currentAreas operator areas as they currently exist
   * @param fromAreas areas to move operators from
   * @returns list of errors found while validating
   */
  private static validateOperatorCounts = (
    currentAreas: AreaStatus[],
    fromAreas: FromArea[]
  ): Error[] => {
    const errors: Error[] = [];
    fromAreas.forEach(fa => {
      // By ReassignOperatorsController point we know that the area exists so we can assert non-null value from "find";
      const curFromArea = currentAreas.find(ca => ca.areaId === fa.area_id)!;
      if (curFromArea.active_operators < fa.number_to_move) {
        errors.push({
          reason: 'requestBody.from_areas',
          message: `There aren't enough active operators in the ${fa.area_id} area to move the specified amount.`,
        });
      }
    });

    return errors;
  };

  /**
   * Checks that requested areas exist.
   * @param currentAreas operator areas as they currently exist
   * @param areaIds list of all area ids to validate
   * @returns list of errors found while validating
   */
  private static validateAreasExist = (
    currentAreas: AreaStatus[],
    areaIds: string[]
  ): Error[] => {
    const errors: Error[] = [];
    const currentAreaIds = currentAreas.map(ca => ca.areaId);
    const nonExisitingAreas = areaIds.filter(
      areaId => currentAreaIds.indexOf(areaId) === -1
    );
    if (nonExisitingAreas.length > 0) {
      errors.push(
        ...nonExisitingAreas.map(areaId => ({
          reason: 'requestBody.to_area_id',
          message: `'${areaId}' does not exist`,
        }))
      );
    }
    return errors;
  };

  /**
   * Gets the current operator status for the areas
   * @param operatorStore store to access the database
   * @param areaIds list of area ids to get their status
   * @returns list of operator areas status
   */
  private static getOperatorAreaStatus = async (
    areaIds: string[]
  ): Promise<AreaStatus[]> => {
    // TODO: This should be moved to the service.  The controller probably shouldn't access the store directly.
    const operatorStore = Container.get(OperatorStore);
    const operatorAreaStatus = await operatorStore.getAreaStatus(areaIds);
    return Promise.resolve(operatorAreaStatus);
  };

  @SuccessResponse('200')
  @Put('/areas')
  @OperationId('PutOperatorsAreas')
  @Tags('operators')
  public async reassignOperators(
    @Body() requestBody: RequestBody
  ): Promise<void> {
    // get the operator service
    const operatorService = Container.get(OperatorService);

    const allAreaIds = [
      requestBody.to_area_id,
      ...requestBody.from_areas.map(fa => fa.area_id),
    ];
    const currentAreas =
      await OperatorsAreasController.getOperatorAreaStatus(allAreaIds);
    const validationErrors = OperatorsAreasController.validateRequestData(
      currentAreas,
      requestBody.from_areas,
      requestBody.to_area_id
    );
    if (validationErrors.length > 0) {
      const fieldErrors: FieldErrors = {};
      validationErrors.forEach(
        // eslint-disable-next-line no-return-assign
        error => (fieldErrors[error.reason] = {message: error.message})
      );
      throw new ValidateError(fieldErrors, 'You Lose!');
    }

    return await operatorService.putReassignOperator(
      currentAreas,
      requestBody.from_areas,
      requestBody.to_area_id
    );
  }
}
