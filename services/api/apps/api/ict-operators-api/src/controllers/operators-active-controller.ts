import {
  ApiResponse,
  CacheMiddleware,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  HealthStatus,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {OperatorService} from '../services/operator-service.ts';
import {
  OperatorCount,
  OperatorCountData,
  OperatorCountsConfig,
} from '../defs/operator-count-def.ts';
import {getOperatorStatus} from '../utils/status-formatters.ts';

@Route('operators')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class OperatorsActiveController extends Controller {
  public static readonly exampleData: OperatorCount = {
    activeOperators: 1,
  };

  public static readonly exampleOperatorCountData: OperatorCountData = {
    operatorCount: 1,
    alertStatus: {
      status: HealthStatus.Critical,
    },
  };

  public static readonly exampleConfig: OperatorCountsConfig = {
    startDateTime: '2023-01-01T00:00:00.000Z',
    endDateTime: '2023-01-02T00:00:00.000Z',
    lowRecOperators: 150,
    highRecOperators: 250,
    maxOperators: 300,
  };

  public static readonly exampleResponse: ApiResponse<
    OperatorCountData,
    OperatorCountsConfig
  > = {
    operatorCount: 1,
    alertStatus: {
      status: HealthStatus.Critical,
    },
    metadata: OperatorsActiveController.exampleConfig,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {Promise<ApiResponse<OperatorCountData, OperatorCountsConfig>>} contract
   */
  @Example<ApiResponse<OperatorCountData, OperatorCountsConfig>>(
    OperatorsActiveController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/active')
  @OperationId('GetOperatorsActive')
  @Tags('operators')
  public async getOperator(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<ApiResponse<OperatorCountData, OperatorCountsConfig>> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // get the operator service
    const operatorService = Container.get(OperatorService);

    // TODO: connect to config db
    const lowRecOperators = 150;
    const highRecOperators = 250;
    const maxOperators = 300;

    // retrieve the total number of operators from the database for the specified date range
    const operatorCountResult = await operatorService.getActiveOperators(
      startDate.toISOString(),
      endDate.toISOString()
    );

    // Assign operator count result to active operators. If there is no value, store code assigns default of 0
    const activeOperators = operatorCountResult.activeOperators;

    // construct the data contract with the operator counts
    return {
      operatorCount: activeOperators,
      alertStatus: {
        status: getOperatorStatus(
          activeOperators,
          lowRecOperators,
          highRecOperators,
          maxOperators
        ),
      },
      metadata: {
        startDateTime: startDate.toISOString(),
        endDateTime: endDate.toISOString(),
        lowRecOperators,
        highRecOperators,
        maxOperators,
      },
    };
  }
}
