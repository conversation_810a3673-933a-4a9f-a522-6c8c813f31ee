import {DiService} from 'ict-api-foundations';
import {OperatorStore} from '../stores/operator-store.ts';
import {AreaStatus, FromArea} from '../defs/reassign-operator-def.ts';
import {OperatorsChartSeriesData} from '../defs/operator-chart-def.ts';
import {OperatorFacilityArea} from '../defs/operator-areas-def.ts';

/**
 * Service that handles applying business logic for Operators to store queries.
 */
@DiService()
export class OperatorService {
  constructor(private operatorStore: OperatorStore) {}

  async getActiveAreas(): Promise<OperatorFacilityArea[]> {
    const areaRows = await this.operatorStore.getOperatorAreas();
    const areas: OperatorFacilityArea[] = areaRows.map(area => ({
      id: area.id,
      name: area.name,
      alertStatus: {
        status: area.status,
        identifier: area.identifier,
        message: area.message,
      },
      operators: {
        activeOperators: area.active_operators,
        lowRecOperators: area.low_rec_operators,
        highRecOperators: area.high_rec_operators,
        maxOperators: area.max_operators,
        pickRate: area.pick_rate,
        queuedOrders: area.queued_orders,
      },
    }));
    return areas;
  }

  /**
   * Attempt to reassign operators
   * @param currentAreas operator areas as they currently exist
   * @param fromAreas areas to move operators from
   * @toAreaId id of area to move operators from fromAreas
   * @returns void if succesfull
   */
  async putReassignOperator(
    currentAreas: AreaStatus[],
    fromAreas: FromArea[],
    toAreaId: string
  ): Promise<void> {
    // Create mappings between areaId to their new active_operator counts
    const toAreaNewTotal = {
      areaId: toAreaId,
      activeOperators:
        // Get toAreas current active operators and add all the from area operators that are being moved
        currentAreas.find(cr => cr.areaId === toAreaId)!.active_operators +
        fromAreas
          .map(fa => fa.number_to_move)
          .reduce((total, current) => total + current),
    };
    const fromAreasNewTotals = fromAreas.map(fa => ({
      areaId: fa.area_id,
      activeOperators:
        // Subtract amount of operators being moved from the current active operators for the area
        currentAreas.find(cr => cr.areaId === fa.area_id)!.active_operators -
        fa.number_to_move,
    }));
    const areaTotalMapping: {areaId: string; activeOperators: number}[] = [
      toAreaNewTotal,
      ...fromAreasNewTotals,
    ];

    const allAreaIds = [toAreaId, ...fromAreas.map(fa => fa.area_id)];

    await this.operatorStore.updatePositionStatus(allAreaIds, areaTotalMapping);
  }

  async getActiveSeries(
    startDate: string,
    endDate: string
  ): Promise<OperatorsChartSeriesData> {
    const activeOperatorsData = await this.operatorStore.getOperatorChartData(
      startDate,
      endDate
    );

    return {
      operators: activeOperatorsData.map(ao => ({
        name: ao.record_timestamp.value,
        value: ao.online_operators,
      })),
    };
  }

  async getActiveOperators(startDate: string, endDate: string) {
    return this.operatorStore.getOperatorCountsAsync(startDate, endDate);
  }
}
