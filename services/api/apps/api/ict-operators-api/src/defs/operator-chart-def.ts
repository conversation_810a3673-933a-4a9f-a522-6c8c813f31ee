import {BigQueryTimestamp} from '@google-cloud/bigquery';
import {
  ApiResponse,
  BaseChartConfig,
  ChartSeriesData,
} from 'ict-api-foundations';

export interface OperatorsChartSeriesQueryResponse {
  record_timestamp: BigQueryTimestamp;
  online_operators: number;
}

export type OperatorsChartSeriesData = {
  operators: ChartSeriesData[];
};

export type OperatorsChartSeriesApiResponse = ApiResponse<
  OperatorsChartSeriesData,
  BaseChartConfig
>;
