import {
  AlertStatus,
  FacilityArea,
  HealthStatusValue,
} from 'ict-api-foundations';

export interface OperatorAreasQueryResponse {
  name: string;
  id: string;
  active_operators: number;
  low_rec_operators: number;
  high_rec_operators: number;
  max_operators: number;
  pick_rate: number;
  queued_orders: number;
  status: HealthStatusValue;
  identifier?: string;
  message?: string;
}

interface OperatorAlertStatus extends Omit<AlertStatus, 'status' | 'message'> {
  // Override 'status'
  status: HealthStatusValue;
  // Override 'message'
  message?: string;
}

export interface OperatorFacilityArea
  extends Omit<FacilityArea, 'alertStatus'> {
  // Override 'alertStatus'
  alertStatus: OperatorAlertStatus;
}
