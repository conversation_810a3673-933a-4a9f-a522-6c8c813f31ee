import {AlertStatus} from 'ict-api-foundations';

export interface OperatorCount {
  activeOperators: number;
}
/**
 * DTO of the necessary data to display total number of operators at a given time.
 */
export interface OperatorCountData {
  operatorCount: number;
  alertStatus: AlertStatus;
}

export interface OperatorCountsConfig {
  startDateTime: string;
  endDateTime: string;
  lowRecOperators: number;
  highRecOperators: number;
  maxOperators: number;
}
