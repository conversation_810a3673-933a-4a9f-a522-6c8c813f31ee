/**
 * DTO that defines the relevant details of a clickable area within a Facility Map.
 */
export interface FacilityArea {
  active_operators: number;
  message?: string;
  high_rec_operators: number;
  id: string;
  identifier: string;
  low_rec_operators: number;
  max_operators: number;
  name: string;
  pick_rate: number;
  status: string;
}

/**
 * DTO of the necessary data to display a Facility Map.
 */
export interface FacilityMapStatusDefinition {
  /**
   * List of areas and their IDs to be able to interact with the visual map.
   */
  areas: FacilityArea[];

  /**
   * URL to an SVG of a facility map to show as a background.
   */
  map_image_url: string;

  /**
   * Title of the map.
   */
  title: string;
}
