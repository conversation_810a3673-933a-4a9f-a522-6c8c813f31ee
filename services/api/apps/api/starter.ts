import * as fs from 'fs';
import * as path from 'path';
import * as child from 'child_process';

/* eslint-disable no-console */

function exec(command: string, args: string[], workingDir: string = '/') {
  console.log('starting process using command line: ', command, args);
  console.log('in working directory: ', workingDir);
  const childProcess = child.spawn(command, args, {
    cwd: workingDir,
  });
  childProcess.stdout.on('data', (data) => {
    console.log(data.toString());
  });

  childProcess.stderr.on('data', (data) => {
    console.error(data.toString());
  });

  childProcess.on('error', (err) => {
    console.error(`failed to start subprocess - ${command} ${args}`, err);
  });

  childProcess.on('close', (code) => {
    console.log(`${command} - process exited with code ${code}`);
  });
}

function run_script(scriptName) {
  console.log('running script: ', scriptName);
  const packageJson = path.resolve('./package.json');
  if (!fs.existsSync(packageJson)) {
    console.error('specified package.json file does not exist: ', packageJson);
    throw new Error('unable to find package.json file');
  }
  const packageBase = path.dirname(packageJson);
  console.log('using package.json file: ', packageJson);

  const packageJsonObj = JSON.parse(fs.readFileSync(packageJson, 'utf8'));
  const scripts = packageJsonObj.scripts;

  if (!(scriptName in scripts)) {
    console.error('script to run not found: ', scriptName);
    console.error('available: ', Object.keys(scripts).join(', '));
    throw new Error(`script ${scriptName} not found.`);
  }
  const script = scripts[scriptName];
  const scriptPieces = script.split(' ');
  const baseScript = scriptPieces[0];
  const scriptArgs = scriptPieces.slice(1);
  exec(baseScript, scriptArgs, packageBase);
}

function startSqlProxy() {
  exec('/usr/app/cloud-sql-proxy', [
    '--private-ip',
    '--port',
    '5432',
    process.env.CLOUD_SQL_CONN_NAME,
  ]);
}

startSqlProxy();
if (process.env.ENABLE_OTEL) {
  run_script('start:otel');
} else {
  run_script('start');
}
