import 'reflect-metadata';
import * as sinon from 'sinon';
import request from 'supertest';
import {ApiMiddleware, Container} from 'ict-api-foundations';
import {expect} from 'chai';
import {HttpStatusCode} from 'axios';
import express from 'express';
import {EnterpriseSearchResponse} from '../../defs/enterprise-search-response.ts';
import {EnterpriseSearchService} from '../../services/enterprise-search-service.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.js';

describe('EnterpriseSearchController', () => {
  const app = express();

  // Setup app for supertest
  app.use(
    express.urlencoded({
      extended: true,
    })
  );
  app.use(express.json());
  RegisterRoutes(app);
  ApiMiddleware.applyErrorMiddlewares(app);

  let mockData: EnterpriseSearchResponse;

  afterEach(() => {
    sinon.restore();
  });

  describe('ai route', () => {
    let enterpriseSearchServiceStub: sinon.SinonStubbedInstance<EnterpriseSearchService>;

    beforeEach(() => {
      enterpriseSearchServiceStub = sinon.createStubInstance(
        EnterpriseSearchService
      );
      Container.set(EnterpriseSearchService, enterpriseSearchServiceStub);

      mockData = {
        document: {
          derivedStructData: {
            extractive_answers: [
              {
                content:
                  '• Any time shoe caps are broken off due to a product jam condition. As required Remove the divert switch to inspect.',
                pageNumber: '100',
              },
            ],
            link: 'gs://nlp-test-documents/sorter/3FA7C4BF-C88D-4319-C8E3A1C1CE4C647F.pdf',
          },
          id: 'b19cd3570e7492dea637d8694671a7a9',
          name: 'projects/503659053462/locations/global/collections/default_collection/dataStores/test-search_1689888123067/branches/0/documents/b19cd3570e7492dea637d8694671a7a9',
        },
        id: 'b19cd3570e7492dea637d8694671a7a9',
      };
    });

    it('should validate that a searchText param is passed', async () => {
      const response = await request(app).get('/ai/enterprise-search');

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details.searchText.message).to.equal(
        "'searchText' is required"
      );
    });

    it('should validate that searchText param is not empty', async () => {
      const response = await request(app)
        .get('/ai/enterprise-search')
        .query({searchText: ''});

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details.searchText.message).to.equal('minLength 3');
    });

    it('should call "EnterpriseSearchService.getEnterpriseSearchResponse"', async () => {
      const searchText = 'Why am I here?';

      enterpriseSearchServiceStub.getEnterpriseSearchResponse.resolves(
        mockData
      );

      const response = await request(app)
        .get('/ai/enterprise-search')
        .query({searchText});

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(mockData);

      sinon.assert.calledWith(
        enterpriseSearchServiceStub.getEnterpriseSearchResponse,
        searchText
      );
    });

    it('should error when the service throws an error', async () => {
      const searchText = 'Why am I here?';
      const errorMessage = 'Something bad happened';
      enterpriseSearchServiceStub.getEnterpriseSearchResponse.rejects(
        new Error(errorMessage)
      );
      const response = await request(app)
        .get('/ai/enterprise-search')
        .query({searchText});

      expect(response.status).to.equal(HttpStatusCode.InternalServerError);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.title).to.equal('Internal Server Error');
    });
  });
});
