import {expect, use} from 'chai';
import chaiAsPromised from 'chai-as-promised';
import sinon from 'sinon';
import {
  EnvironmentService,
  GCPSecretManager,
  IctError,
} from 'ict-api-foundations';
import axios from 'axios';
import {EnterpriseSearchResponse} from '../../defs/enterprise-search-response.ts';
import {EnterpriseSearchService} from '../../services/enterprise-search-service.js';

describe('EnterpriseSearchService', () => {
  let enterpriseSearchService: EnterpriseSearchService;
  let secretsManagerStub: sinon.SinonStub;
  let axiosStub: sinon.SinonStub;
  let mockEnvService = EnvironmentService.prototype;
  use(chaiAsPromised);

  afterEach(() => {
    sinon.restore();
  });

  beforeEach(() => {
    mockEnvService = new EnvironmentService();
    sinon.stub(mockEnvService, 'enterpriseSearch').returns({
      url: 'https://enterprise-search-url.com',
      secret: 'enterprise-search-secret',
    });
  });

  describe('getEnterpriseSearchResponse', () => {
    it('should post searchText to the enterprise search api', async () => {
      enterpriseSearchService = new EnterpriseSearchService(mockEnvService);
      const searchText = 'Why am I here?';
      const mockData: EnterpriseSearchResponse = {
        document: {
          derivedStructData: {
            extractive_answers: [
              {
                content:
                  '• Any time shoe caps are broken off due to a product jam condition. As required Remove the divert switch to inspect.',
                pageNumber: '100',
              },
            ],
            link: 'gs://nlp-test-documents/sorter/3FA7C4BF-C88D-4319-C8E3A1C1CE4C647F.pdf',
          },
          id: 'b19cd3570e7492dea637d8694671a7a9',
          name: 'projects/503659053462/locations/global/collections/default_collection/dataStores/test-search_1689888123067/branches/0/documents/b19cd3570e7492dea637d8694671a7a9',
        },
        id: 'b19cd3570e7492dea637d8694671a7a9',
      };

      secretsManagerStub = sinon
        .stub(GCPSecretManager.prototype, 'get')
        .resolves('123');
      axiosStub = sinon.stub(axios, 'post').resolves({data: mockData});

      const result =
        await enterpriseSearchService.getEnterpriseSearchResponse(searchText);

      sinon.assert.calledOnce(secretsManagerStub);
      sinon.assert.calledOnce(axiosStub);

      expect(result).to.deep.equal(mockData);
    });

    it('should throw an error when the axios post throws an error', async () => {
      enterpriseSearchService = new EnterpriseSearchService(mockEnvService);
      const searchText = 'Why am I here?';

      secretsManagerStub = sinon
        .stub(GCPSecretManager.prototype, 'get')
        .resolves('123');
      axiosStub = sinon
        .stub(axios, 'post')
        .rejects(new Error('Something bad happened'));

      const outcome = await expect(
        enterpriseSearchService.getEnterpriseSearchResponse(searchText),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(
        IctError.internalServerError().statusCode,
      );

      sinon.assert.calledOnce(secretsManagerStub);
    });

    it('should throw Ict no content error when axios post returns no content', async () => {
      enterpriseSearchService = new EnterpriseSearchService(mockEnvService);
      const searchText = 'Why am I here?';

      secretsManagerStub = sinon
        .stub(GCPSecretManager.prototype, 'get')
        .resolves('123');
      axiosStub = sinon.stub(axios, 'post').resolves({status: 204, data: {}});

      const outcome = await expect(
        enterpriseSearchService.getEnterpriseSearchResponse(searchText),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);

      sinon.assert.calledOnce(secretsManagerStub);
    });

    it('should throw an error when secrets manager fails', async () => {
      enterpriseSearchService = new EnterpriseSearchService(mockEnvService);
      const searchText = 'Why am I here?';

      secretsManagerStub = sinon
        .stub(GCPSecretManager.prototype, 'get')
        .rejects(new Error('Something bad happened'));

      axiosStub = sinon.stub(axios, 'post').resolves({});

      expect(
        enterpriseSearchService.getEnterpriseSearchResponse(searchText),
      ).to.be.rejectedWith(Error);

      sinon.assert.notCalled(axiosStub);
    });
  });
});
