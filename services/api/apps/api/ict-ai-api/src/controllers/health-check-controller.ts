import {BaseHealthCheck} from 'ict-api-foundations';
import {Get, Route, OperationId, Tags} from 'tsoa';

@Route('/ai/healthcheck')
export class HealthCheckController {
  @Get()
  @OperationId('GetAiBasicHealthCheck')
  @Tags('ai')
  public getAiBasicHealthCheck() {
    return BaseHealthCheck.getBasicHealthCheck();
  }

  @Get('/full')
  @OperationId('GetAiFullHealthCheck')
  @Tags('ai')
  public getAiFullHealthCheck() {
    return BaseHealthCheck.getDeepHealthCheck(['auth0', 'bigQuery', 'redis']);
  }
}
