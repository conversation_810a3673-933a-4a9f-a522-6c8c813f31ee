import {Container} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {EnterpriseSearchResponse} from '../defs/enterprise-search-response.js';
import {EnterpriseSearchService} from '../services/enterprise-search-service.ts';

@Route('ai')
export class EnterpriseSearchController extends Controller {
  /**
   * @param {string} searchText
   * @isString searchText 'searchText' is required
   * @minLength searchText 3
   * @example searchText "What is a pin fault?"
   */
  @Example<EnterpriseSearchResponse>({
    document: {
      derivedStructData: {
        extractive_answers: [
          {
            content:
              '• Any time shoe caps are broken off due to a product jam condition. As required Remove the divert switch to inspect.',
            pageNumber: '100',
          },
        ],
        link: 'gs://nlp-test-documents/sorter/3FA7C4BF-C88D-4319-C8E3A1C1CE4C647F.pdf',
      },
      id: 'b19cd3570e7492dea637d8694671a7a9',
      name: 'projects/503659053462/locations/global/collections/default_collection/dataStores/test-search_1689888123067/branches/0/documents/b19cd3570e7492dea637d8694671a7a9',
    },
    id: 'b19cd3570e7492dea637d8694671a7a9',
  })
  @SuccessResponse('200')
  @Get('/enterprise-search')
  @OperationId('GetAiEnterpriseSearch')
  @Tags('ai')
  public async getEnterpriseSearchResult(
    @Query()
    searchText: string
  ): Promise<EnterpriseSearchResponse> {
    const enterpriseSearchService: EnterpriseSearchService = Container.get(
      EnterpriseSearchService
    );

    return enterpriseSearchService.getEnterpriseSearchResponse(searchText);
  }
}
