import {
  DiService,
  EnvironmentService,
  GCPSecretManager,
  IctError,
} from 'ict-api-foundations';
import https, {Agent} from 'https';
import axios, {AxiosResponse} from 'axios';
import {EnterpriseSearchResponse} from '../defs/enterprise-search-response.ts';

/**
 * Service that handles proxying calls to enterprise search api.
 */
@DiService()
export class EnterpriseSearchService {
  constructor(private envService: EnvironmentService) {}

  private genAiApiKey: string | undefined;

  /**
   * Asynchrnously fetch and set the genai api key.
   */
  async setGenAiApiKey() {
    if (this.genAiApiKey) return;
    const gcpSecrets = new GCPSecretManager();
    this.genAiApiKey = await gcpSecrets.get({
      name: this.envService.enterpriseSearch.secretsName,
    }); // TODO: This needs to be variable based on environment.
  }

  /**
   * Attempt to query enterprise search (gen ai) api.
   * @param stateDate text query string
   * @returns EnterpriseSearchResponse with document details.
   */
  async getEnterpriseSearchResponse(
    searchText: string
  ): Promise<EnterpriseSearchResponse> {
    if (!this.envService.enterpriseSearch.url) {
      throw IctError.internalServerError(
        'No enterprise search url specified for this environment'
      );
    }
    await this.setGenAiApiKey(); // TODO: Prefer asynch builder pattern?
    if (!this.genAiApiKey) {
      throw IctError.internalServerError(
        'Failed to load a valid api key from secrets manager'
      );
    }
    const agent: Agent = new https.Agent({
      rejectUnauthorized: false,
    });

    let response: AxiosResponse;
    try {
      // TODO: This will need to be variable based on environment in the future.
      response = await axios.post(
        this.envService.enterpriseSearch.url,
        {
          text: searchText,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': this.genAiApiKey,
          },
          httpsAgent: agent,
        }
      );
    } catch (error) {
      throw IctError.internalServerError(
        'Enterprise Search API failed.  Please try again.  If the problem persists, contact support.'
      );
    }

    if (response.status === 204) {
      throw IctError.noContent(
        'Enterprise Search API returned no content.  Please try again.  If the problem persists, contact support.'
      );
    }

    const searchResponse: EnterpriseSearchResponse =
      response.data as EnterpriseSearchResponse;

    return searchResponse;
  }
}
