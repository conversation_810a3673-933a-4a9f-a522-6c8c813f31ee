{"name": "ict-ai-api", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/ict-ai-api/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "ict-ai-api:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "ict-ai-api:build:development"}, "production": {"buildTarget": "ict-ai-api:build:production"}}}}}