{"entryFile": "src/index.ts", "noImplicitAdditionalProperties": "throw-on-extras", "controllerPathGlobs": ["src/controllers/*.ts"], "spec": {"outputDirectory": "build", "specVersion": 3, "yaml": true, "specFileBaseName": "openapi", "title": "Test", "securityDefinitions": {"jwt": {"type": "https", "scheme": "bearer", "bearerFormat": "JWT"}}, "spec": {"servers": [{"url": "http://localhost:8080/", "description": "Local development"}, {"url": "https://run-ict-d-api.ict.dematic.dev", "description": "Dev development"}, {"url": "https://stage.api.ict.dematic.dev", "description": "Staging"}, {"url": "https://api.ict.dematic.cloud", "description": "Production"}]}}, "routes": {"routesDir": "build", "esm": true}}