import {
  CacheMiddleware,
  Container,
  ApiResponse,
  HealthStatus,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.ts';
import {
  EquipmentFaultsConfigDefinition,
  EquipmentFaultsDefinition,
} from '../defs/equipment-faults-def.ts';

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class EquipmentFaultsController extends Controller {
  static exampleData: EquipmentFaultsDefinition = {
    faults: 50,
    status: 'caution',
  };

  static exampleConfig: EquipmentFaultsConfigDefinition = {
    highRange: 5,
    max: 50,
  };

  static exampleResponse: ApiResponse<
    EquipmentFaultsDefinition,
    EquipmentFaultsConfigDefinition
  > = {
    faults: 50,
    status: 'caution',
    metadata: EquipmentFaultsController.exampleConfig,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   */
  @Example<
    ApiResponse<EquipmentFaultsDefinition, EquipmentFaultsConfigDefinition>
  >(EquipmentFaultsController.exampleResponse)
  @SuccessResponse('200')
  @Get('/faults')
  @OperationId('GetEquipmentFaults')
  @Tags('equipment')
  public async getFaults(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<
    ApiResponse<EquipmentFaultsDefinition, EquipmentFaultsConfigDefinition>
  > {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const equipmentService = Container.get(EquipmentService);

    // Retrieve equipment faults data from the store
    const equipmentFaultsData = await equipmentService.getEquipmentFaultsAsync(
      startDate.toISOString(),
      endDate.toISOString()
    );

    return {
      ...equipmentFaultsData,
      status: EquipmentFaultsController.getHealthstatus(
        equipmentFaultsData.faults
      ),
      metadata: EquipmentFaultsController.exampleConfig,
    };
  }

  // TODO: Define the boundaries of fault status parameters
  // This is a placeholder implementation
  private static getHealthstatus(faults: number): HealthStatus {
    if (faults < 3) {
      return HealthStatus.Ok;
    }
    return HealthStatus.Caution;
  }
}
