import {
  BaseFilterType,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Post,
  Body,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.ts';
import type {
  AvailableLevels,
  FaultsLevelListRequest,
} from '../defs/equipment-faults-level-list-def.ts';

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
])
export class EquipmentFaultsLevelListController extends Controller {
  /**
   * Example data for the fault level list
   */
  static readonly exampleData: string[] = ['Level 1', 'Level 2', 'Level 3'];

  static readonly exampleResponse: AvailableLevels = {
    availableLevels: EquipmentFaultsLevelListController.exampleData,
  };

  @Example<AvailableLevels>(EquipmentFaultsLevelListController.exampleResponse)
  @SuccessResponse('200')
  @Post('/faults/level/list')
  @OperationId('PostEquipmentFaultsLevelList')
  @Tags('equipment')
  public async getEquipmentFaultsLevelList(
    @Body() params: FaultsLevelListRequest
  ): Promise<AvailableLevels> {
    // Validate the start and end dates

    // TODO: remove condition check when parameters become required
    if (params.start_date && params.end_date) {
      startEndDateValidation(params.start_date, params.end_date);
    }

    // Get the equipment service
    const equipmentService = Container.get(EquipmentService);

    // Retrieve faults level list data from the store
    const equipmentLevelListData: string[] =
      await equipmentService.getEquipmentFaultsLevelList({
        startDate: params.start_date,
        endDate: params.end_date,
        filters: params.filters as BaseFilterType,
      });

    return {
      availableLevels: equipmentLevelListData,
    };
  }
}
