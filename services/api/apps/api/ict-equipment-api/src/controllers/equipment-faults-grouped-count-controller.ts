import {
  BaseFilterType,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Post,
  Body,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.ts';
import {FaultCountGroupedSeriesData} from '../defs/equipment-fault-counts-grouped-def.ts';
import type {FaultCountGroupedByRequest} from '../defs/equipment-fault-counts-grouped-def.ts';

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
])
export class EquipmentFaultsGroupedCountController extends Controller {
  static exampleData: FaultCountGroupedSeriesData = {
    faultCounts: [
      {
        name: '01',
        value: 50,
      },
      {
        name: '02',
        value: 42,
      },
      {
        name: '03',
        value: 66,
      },
    ],
  };

  static exampleResponse: FaultCountGroupedSeriesData =
    EquipmentFaultsGroupedCountController.exampleData;

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {Promise<FaultCountGroupedSeriesData>} contract
   */
  @Example<FaultCountGroupedSeriesData>(
    EquipmentFaultsGroupedCountController.exampleResponse
  )
  @SuccessResponse('200')
  @Post('/faults/grouped/count/series')
  @OperationId('getFaultGroupedByCounts')
  @Tags('equipment')
  public async getFaultGroupedByCounts(
    @Body() params: FaultCountGroupedByRequest
  ): Promise<FaultCountGroupedSeriesData> {
    // Validate the start and end dates
    startEndDateValidation(params.startDate, params.endDate);
    // Get the equipment service
    const equipmentService = Container.get(EquipmentService);

    // Retrieve equipment faults data from the store
    return await equipmentService.getFaultsCountGroupedAsync({
      startDate: params.startDate,
      endDate: params.endDate,
      groupByColumn: params.groupByColumn,
      isAscendingOrder: params.isAscendingOrder,
      filters: params.filters as BaseFilterType,
    });
  }
}
