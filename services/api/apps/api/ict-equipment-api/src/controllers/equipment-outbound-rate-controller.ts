import {
  CacheMiddleware,
  Container,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.ts';
import {
  EquipmentOutboundRate,
  EquipmentOutboundRateApiResponse,
  EquipmentOutboundRateConfig,
} from '../defs/equipment-outbound-rate-def.ts';

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class EquipmentOuboundRateController extends Controller {
  static readonly exampleData: EquipmentOutboundRate = {
    outboundDivertsHour: 600,
    status: 'ok',
  };

  static readonly exampleConfig: EquipmentOutboundRateConfig = {
    max: 1000,
    rangeColor: 'royalblue',
  };

  static readonly exampleResponse: EquipmentOutboundRateApiResponse = {
    outboundDivertsHour: 600,
    status: 'ok',
    metadata: EquipmentOuboundRateController.exampleConfig,
  };

  @Example({
    outboundDivertsHour: 600,
    status: 'ok',
    metadata: {
      max: 1000,
      rangeColor: 'royalblue',
    },
  })
  @SuccessResponse('200')
  @Get('/outbound-rate')
  @OperationId('GetEquipmentOutboundRate')
  @Tags('equipment')
  public async getOutboundRate(): Promise<EquipmentOutboundRateApiResponse> {
    const equipmentService = Container.get(EquipmentService);

    const activity: EquipmentOutboundRate =
      await equipmentService.getOutboundRate();

    return {
      ...activity,
      metadata: EquipmentOuboundRateController.exampleConfig,
    };
  }
}
