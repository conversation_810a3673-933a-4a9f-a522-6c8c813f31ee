import {
  BaseFilterType,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Post,
  Body,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.ts';
import type {
  AvailableAisles,
  FaultsAisleListRequest,
} from '../defs/equipment-faults-aisle-list-def.ts';

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
])
export class EquipmentFaultsAisleListController extends Controller {
  /**
   * Example data for the fault Aisle list
   */
  static readonly exampleData: string[] = ['Aisle 1', 'Aisle 2', 'Aisle 3'];

  static readonly exampleResponse: AvailableAisles = {
    availableAisles: EquipmentFaultsAisleListController.exampleData,
  };

  @Example<AvailableAisles>(EquipmentFaultsAisleListController.exampleResponse)
  @SuccessResponse('200')
  @Post('/faults/aisle/list')
  @OperationId('PostEquipmentFaultsAisleList')
  @Tags('equipment')
  public async getEquipmentFaultsAisleList(
    @Body() params: FaultsAisleListRequest
  ): Promise<AvailableAisles> {
    // Validate the start and end dates
    if (params.start_date && params.end_date) {
      startEndDateValidation(params.start_date, params.end_date);
    }

    // Get the equipment service
    const equipmentService = Container.get(EquipmentService);

    // Retrieve faults Aisle list data from the store
    const equipmentAisleListData: string[] =
      await equipmentService.getEquipmentFaultsAisleList({
        startDate: params.start_date,
        endDate: params.end_date,
        filters: params.filters as BaseFilterType,
      });

    const contract: AvailableAisles = {
      availableAisles: equipmentAisleListData,
    };
    return contract;
  }
}
