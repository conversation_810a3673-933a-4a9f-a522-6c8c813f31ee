import {
  CacheMiddleware,
  Container,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Path,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.ts';
import {EquipmentWorkstationOperatorActivityData} from '../defs/equipment-workstation-operator-activity-def.ts';

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class EquipmentWorkstationOperatorActivityController extends Controller {
  /**
   * Example data for the active faults table
   */
  static readonly exampleData: EquipmentWorkstationOperatorActivityData[] = [
    {
      operatorId: 'TestOp1',
      startTime: '2020-01-01T00:00:00.000Z',
      endTime: '2020-01-01T01:00:00.000Z',
      totalTimeDuration: 3600,
      idleTime: 60,
      starvedTime: 70,
      blockedTime: 80,
      lineRatePerHour: 75,
      weightedLineRatePerHour: 90,
    },
    {
      operatorId: 'TestOp2',
      startTime: '2020-01-01T01:00:00.000Z',
      endTime: '2020-01-01T03:00:00.000Z',
      totalTimeDuration: 7200,
      idleTime: 120,
      starvedTime: 140,
      blockedTime: 160,
      lineRatePerHour: 80,
      weightedLineRatePerHour: 100,
    },
  ];

  static readonly exampleResponse: EquipmentWorkstationOperatorActivityData[] =
    EquipmentWorkstationOperatorActivityController.exampleData;

  @Example<EquipmentWorkstationOperatorActivityData[]>(
    EquipmentWorkstationOperatorActivityController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/workstation/{workstationId}/operator/activity')
  @OperationId('GetEquipmentWorkstationOperatorActivity')
  @Tags('equipment')
  public async getWorkstationOperatorActivity(
    @Path() workstationId: string
  ): Promise<EquipmentWorkstationOperatorActivityData[]> {
    const equipmentService = Container.get(EquipmentService);

    return await equipmentService.getWorkstationOperatorActivity(workstationId);
  }
}
