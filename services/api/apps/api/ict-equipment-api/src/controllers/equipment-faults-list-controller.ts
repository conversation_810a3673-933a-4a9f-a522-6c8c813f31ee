import {
  Container,
  ProtectedRouteMiddleware,
  type BaseFilterType,
  type PaginatedRequest,
  startEndDateValidation,
  IctError,
  DatabaseTypes,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Body,
  Controller,
  Example,
  Middlewares,
  Post,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.ts';
import {
  EquipmentFaultsListItem,
  PostEquipmentFaultsListResponse,
} from '../defs/equipment-faults-list-def.ts';

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
])
export class EquipmentFaultsListController extends Controller {
  public static readonly exampleData: EquipmentFaultsListItem[] = [
    {
      timestamp: '2024-08-19T12:00:00.000Z',
      durationMinutes: 1.2,
      status: '430 - Lift Drive Faulted',
      aisle: '01',
      level: '02',
      device: 'MSAI01ER02LO00',
      deviceType: 'Lift',
    },
    {
      timestamp: '2024-08-19T12:00:00.000Z',
      durationMinutes: 1.2,
      status: 'Offline',
      aisle: '02',
      level: '01',
      device: 'MSAI02ER01LO00',
      deviceType: 'Lift',
    },
    {
      timestamp: '2024-08-19T12:00:00.000Z',
      durationMinutes: 1.2,
      status: 'Offline',
      aisle: '01',
      level: '03',
      device: 'MSAI01LV03SH10',
      deviceType: 'Shuttle',
    },
  ];
  public static readonly exampleConfig = {
    limit: 10,
    page: 1,
    totalResults: 3,
  };
  public static readonly exampleResponse: PostEquipmentFaultsListResponse = {
    data: EquipmentFaultsListController.exampleData,
    metadata: EquipmentFaultsListController.exampleConfig,
  };

  /**
   *
   * @returns {Promise<PostEquipmentFaultsListResponse>} response
   * @throws IctError
   */
  @Example<PostEquipmentFaultsListResponse>(
    EquipmentFaultsListController.exampleResponse
  )
  @SuccessResponse('200')
  @Post('/faults/list')
  @OperationId('PostEquipmentFaultsList')
  @Tags('equipment')
  public async getEquipmentFaultsList(
    @Body()
    paginatedRequest: PaginatedRequest
  ): Promise<PostEquipmentFaultsListResponse> {
    // Validate the start and end dates
    startEndDateValidation(
      paginatedRequest.start_date,
      paginatedRequest.end_date
    );

    const equipmentService = Container.get(EquipmentService);
    const {
      start_date: startDate,
      end_date: endDate,
      filters,
      sortFields,
      page,
      limit,
    } = paginatedRequest;

    const faultsList = await equipmentService.getEquipmentFaultsList({
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      reqFilter: filters as BaseFilterType,
      sortFields,
      page,
      limit,
    });

    if (!faultsList.data.length) {
      throw IctError.noContent();
    }

    return faultsList;
  }
}
