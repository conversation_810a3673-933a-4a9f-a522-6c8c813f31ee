import {
  ApiResponse,
  CacheMiddleware,
  Container,
  HealthStatus,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Path,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.ts';
import {
  EquipmentSummaryWorkstationDefinition,
  EquipmentSummaryWorkstationConfigDefinition,
} from '../defs/equipment-summary-workstations-def.ts';

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class EquipmentSummaryWorkstationsController extends Controller {
  static readonly exampleData = {
    status: HealthStatus.Ok,
    operatorId: 1534,
    iatDonorTotes: 0.2,
    linesPerHour: 110,
    activeTime: 'PT8H30M',
    idleTime: 'PT1H15M',
    starvedTime: 'PT15M',
  };

  static readonly exampleResponse: ApiResponse<
    EquipmentSummaryWorkstationDefinition,
    EquipmentSummaryWorkstationConfigDefinition
  > = {
    status: HealthStatus.Ok,
    operatorId: 1534,
    iatDonorTotes: 0.2,
    linesPerHour: 110,
    activeTime: 'PT8H30M',
    idleTime: 'PT1H15M',
    starvedTime: 'PT15M',
    metadata: {
      metrics: [
        {
          id: 'operatorId',
          label: 'Operator ID',
          type: 'number',
          format: 'integer',
          display: true,
        },
        {
          id: 'iatDonorTotes',
          label: 'IAT Donor Totes',
          type: 'number',
          format: 'percent',
          display: true,
        },
        {
          id: 'linesPerHour',
          label: 'Lines Per Hour',
          type: 'string',
          format: 'duration',
          display: true,
        },
        {
          id: 'activeTime',
          label: 'Active Time',
          type: 'string',
          format: 'duration',
          display: true,
        },
        {
          id: 'idleTime',
          label: 'Idle Time',
          type: 'string',
          format: 'duration',
          display: true,
        },
        {
          id: 'starvedTime',
          label: 'Starved Time',
          type: 'string',
          format: 'duration',
          display: true,
        },
      ],
    },
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   */
  @Example<
    ApiResponse<
      EquipmentSummaryWorkstationDefinition,
      EquipmentSummaryWorkstationConfigDefinition
    >
  >(EquipmentSummaryWorkstationsController.exampleResponse)
  @SuccessResponse('200')
  @Get('/summary/workstations/{workstationId}')
  @OperationId('GetEquipmentSummaryWorkstations')
  @Tags('equipment')
  public async getSummaryWorkstations(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
    @Path() workstationId: string
  ): Promise<
    ApiResponse<
      EquipmentSummaryWorkstationDefinition,
      EquipmentSummaryWorkstationConfigDefinition
    >
  > {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const equipmentService = Container.get(EquipmentService);

    const workstationSummaryData = await equipmentService.getWorkstationSummary(
      startDate.toISOString(),
      endDate.toISOString(),
      workstationId
    );

    const config: EquipmentSummaryWorkstationConfigDefinition = {
      metrics: [
        {
          id: 'operatorId',
          label: 'Operator ID',
          type: 'number',
          format: 'integer',
          display: true,
        },
        {
          id: 'iatDonorTotes',
          label: 'IAT Donor Totes',
          type: 'number',
          format: 'percent',
          display: true,
        },
        {
          id: 'linesPerHour',
          label: 'Lines Per Hour',
          type: 'string',
          format: 'duration',
          display: true,
        },
        {
          id: 'activeTime',
          label: 'Active Time',
          type: 'string',
          format: 'duration',
          display: true,
        },
        {
          id: 'idleTime',
          label: 'Idle Time',
          type: 'string',
          format: 'duration',
          display: true,
        },
        {
          id: 'starvedTime',
          label: 'Starved Time',
          type: 'string',
          format: 'duration',
          display: true,
        },
      ],
    };

    return {
      ...workstationSummaryData,
      metadata: config,
    };
  }
}
