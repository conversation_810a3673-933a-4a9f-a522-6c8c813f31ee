import {
  ApiResponse,
  CacheMiddleware,
  Container,
  HealthStatus,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Path,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.ts';
import {
  EquipmentSummaryAisleConfigDefinition,
  EquipmentSummaryAisleDefinition,
} from '../defs/equipment-summary-aisles-def.ts';

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class EquipmentSummaryAislesController extends Controller {
  static readonly exampleData: EquipmentSummaryAisleDefinition = {
    status: HealthStatus.Ok,
    totalMovements: 40811,
    storageUtilization: 0.98,
    storedTotesPerHour: 120,
    inventoryTotes: 1800,
    retrievedTotesPerHour: 300,
    orderTotes: 2000,
  } as EquipmentSummaryAisleDefinition;

  static readonly exampleConfig: EquipmentSummaryAisleConfigDefinition = {
    metrics: [
      {
        id: 'totalMovements',
        label: 'Total Movements',
        type: 'number',
        format: 'integer',
        display: true,
      },
      {
        id: 'storageUtilization',
        label: 'Storage Utilization',
        type: 'number',
        format: 'percent',
        display: true,
      },
      {
        id: 'storedTotesPerHour',
        label: 'Stored Totes Per Hour',
        type: 'number',
        format: 'integer',
        display: true,
      },
      {
        id: 'inventoryTotes',
        label: 'Inventory Totes',
        type: 'number',
        format: 'integer',
        display: true,
      },
      {
        id: 'retrievedTotesPerHour',
        label: 'Retrieved Totes Per Hour',
        type: 'number',
        format: 'integer',
        display: true,
      },
      {
        id: 'orderTotes',
        label: 'Order Totes',
        type: 'number',
        format: 'integer',
        display: true,
      },
    ],
  };

  static readonly exampleResponse: ApiResponse<
    EquipmentSummaryAisleDefinition,
    EquipmentSummaryAisleConfigDefinition
  > = {
    status: HealthStatus.Ok,
    totalMovements: 40811,
    storageUtilization: 0.98,
    storedTotesPerHour: 120,
    inventoryTotes: 1800,
    retrievedTotesPerHour: 300,
    orderTotes: 2000,
    metadata: EquipmentSummaryAislesController.exampleConfig,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   */
  @Example<
    ApiResponse<
      EquipmentSummaryAisleDefinition,
      EquipmentSummaryAisleConfigDefinition
    >
  >(EquipmentSummaryAislesController.exampleResponse)
  @SuccessResponse('200')
  @Get('/summary/aisles/{aisleId}')
  @OperationId('GetEquipmentSummaryAisle')
  @Tags('equipment')
  public async getSummaryAisles(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
    @Path('aisleId') aisleId: string
  ): Promise<
    ApiResponse<
      EquipmentSummaryAisleDefinition,
      EquipmentSummaryAisleConfigDefinition
    >
  > {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);

    const equipmentService = Container.get(EquipmentService);

    const aisleSummaryData = await equipmentService.getAisleSummary(
      aisleId,
      startDate.toISOString(),
      endDate.toISOString()
    );

    return {
      ...aisleSummaryData,
      metadata: EquipmentSummaryAislesController.exampleConfig,
    };
  }
}
