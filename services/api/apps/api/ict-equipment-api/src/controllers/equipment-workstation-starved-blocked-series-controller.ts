import {
  CacheMiddleware,
  Container,
  ProtectedRouteMiddleware,
  getSeriesMaxValue,
} from 'ict-api-foundations';
import {
  Controller,
  Get,
  Route,
  SuccessResponse,
  Path,
  Example,
  Middlewares,
  OperationId,
  Tags,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.ts';
import {
  EquipmentWorkstationStarvedBlockedTimeSeriesApiResponse,
  EquipmentWorkstationStarvedBlockedTimeSeriesConfig,
  EquipmentWorkstationStarvedBlockedTimeSeriesData,
} from '../defs/equipment-workstation-starved-blocked-time-series-def.ts';

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class EquipmentWorkstationStarvedBlockedSeriesController extends Controller {
  static readonly exampleData: EquipmentWorkstationStarvedBlockedTimeSeriesData =
    {
      starvedTime: [
        {
          name: '2022-01-01T08:00:00.000Z',
          value: 16,
        },
        {
          name: '2022-01-01T09:00:00.000Z',
          value: 17,
        },
      ],
      blockedTime: [
        {
          name: '2022-01-01T08:00:00.000Z',
          value: 11,
        },
        {
          name: '2022-01-01T09:00:00.000Z',
          value: 12,
        },
      ],
    };

  static readonly exampleConfig: EquipmentWorkstationStarvedBlockedTimeSeriesConfig =
    {
      yMin: 0,
      yMax: 19,
      xAxisValueFormatType: 'time',
      yAxisValueFormatType: 'percentage',
      starvedTime: {
        type: 'line',
        color: '#DD5F94',
        icon: 'emptyDiamond',
      },
      blockedTime: {
        type: 'line',
        color: '#AC862C',
        icon: 'emptySquare',
      },
    };

  static readonly exampleResponse: EquipmentWorkstationStarvedBlockedTimeSeriesApiResponse =
    {
      starvedTime: [
        {
          name: '2022-01-01T08:00:00.000Z',
          value: 16,
        },
        {
          name: '2022-01-01T09:00:00.000Z',
          value: 17,
        },
      ],
      blockedTime: [
        {
          name: '2022-01-01T08:00:00.000Z',
          value: 11,
        },
        {
          name: '2022-01-01T09:00:00.000Z',
          value: 12,
        },
      ],
      metadata:
        EquipmentWorkstationStarvedBlockedSeriesController.exampleConfig,
    };

  @Example(EquipmentWorkstationStarvedBlockedSeriesController.exampleResponse)
  @SuccessResponse('200')
  @Get('/workstation/{workstationId}/starved-blocked-time/series')
  @OperationId('GetEquipmentWorkstationStarvedBlockedTimeSeries')
  @Tags('equipment')
  public async getWorkstationStarvedBlockedSeries(
    @Path() workstationId: string
  ): Promise<EquipmentWorkstationStarvedBlockedTimeSeriesApiResponse> {
    const equipmentService = Container.get(EquipmentService);
    const seriesData =
      await equipmentService.getEquipmentWorkstationStarvedBlockedSeries(
        workstationId
      );

    const maxSeriesValue = Math.max(
      getSeriesMaxValue(seriesData.starvedTime),
      getSeriesMaxValue(seriesData.blockedTime)
    );

    return {
      ...seriesData,
      metadata: {
        // TODO: Use data from a config db
        ...EquipmentWorkstationStarvedBlockedSeriesController.exampleConfig,
        // Set yMax to + 10% the maxSeriesValue, up to 100.
        yMax: Math.min(Math.ceil(maxSeriesValue * 1.1), 100),
      },
    };
  }
}
