import {
  CacheMiddleware,
  Container,
  ProtectedRouteMiddleware,
  startEndDateValidation,
  DatabaseTypes,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.ts';
import {
  EquipmentFaultsSeriesContractData,
  EquipmentFaultsSeriesData,
  EquipmentFaultsSeriesQueryData,
  EquipmentThroughputSeriesData,
} from '../defs/equipment-faults-series-def.ts';

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class EquipmentFaultsSeriesController extends Controller {
  static readonly exampleFaultsSeriesData: EquipmentFaultsSeriesQueryData[] = [
    {
      hourOfTheDay: '2023-04-10T10:00:00.000Z',
      faults: 2,
      throughput: 224,
    },
    {
      hourOfTheDay: '2023-04-10T11:00:00.000Z',
      faults: 5,
      throughput: 106,
    },
  ];

  static readonly exampleFaultsData: EquipmentFaultsSeriesData[] = [
    {
      name: '2023-04-10T10:00:00.000Z',
      value: 2,
    },
    {
      name: '2023-04-10T11:00:00.000Z',
      value: 5,
    },
  ];

  static exampleThroughputData: EquipmentThroughputSeriesData[] = [
    {
      name: '2023-04-10T10:00:00.000Z',
      value: 224,
    },
    {
      name: '2023-04-10T11:00:00.000Z',
      value: 106,
    },
  ];

  static exampleResponse: EquipmentFaultsSeriesContractData = {
    faultsOrderData: EquipmentFaultsSeriesController.exampleFaultsData,
    orderData: EquipmentFaultsSeriesController.exampleThroughputData,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   */
  @Example(EquipmentFaultsSeriesController.exampleResponse)
  @SuccessResponse('200')
  @Get('/faults/series')
  @OperationId('GetEquipmentFaultsSeries')
  @Tags('equipment')
  public async getFaultsSeries(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<EquipmentFaultsSeriesContractData> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const equipmentService = Container.get(EquipmentService);

    // Retrieve equipment faults data from the store
    const equipmentFaultsData = await equipmentService.getEquipmentFaultsSeries(
      startDate.toISOString(),
      endDate.toISOString()
    );

    const contract: EquipmentFaultsSeriesContractData = {
      faultsOrderData: equipmentFaultsData.map(row => ({
        name: row.hourOfTheDay,
        value: row.faults,
      })),
      orderData: equipmentFaultsData.map(row => ({
        name: row.hourOfTheDay,
        value: row.throughput,
      })),
    };

    return contract;
  }
}
