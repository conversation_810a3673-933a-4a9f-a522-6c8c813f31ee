import {
  BaseFilterType,
  Container,
  configPostgresDatabase,
  DatabaseTypes,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Post,
  Body,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.ts';
import {
  FaultsMovementsSeriesData,
  type FaultsMovementsSeriesRequest,
} from '../defs/equipment-faults-movements-series-def.ts';

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
])
export class EquipmentFaultsMovementsSeriesController extends Controller {
  /**
   * Example data for the movement count series and movements per fault series
   */
  static readonly exampleData: FaultsMovementsSeriesData = {
    movementCounts: [
      {
        name: '01',
        value: 50,
      },
      {
        name: '02',
        value: 42,
      },
      {
        name: '03',
        value: 66,
      },
    ],
    movementsPerFault: [
      {
        name: '01',
        value: 10,
      },
      {
        name: '02',
        value: 20,
      },
      {
        name: '03',
        value: 30,
      },
    ],
  };

  static readonly exampleResponse: FaultsMovementsSeriesData =
    EquipmentFaultsMovementsSeriesController.exampleData;

  @Example<FaultsMovementsSeriesData>(
    EquipmentFaultsMovementsSeriesController.exampleResponse
  )
  @SuccessResponse('200')
  @Post('/faults/movements/series')
  @OperationId('PostEquipmentFaultsMovementsSeries')
  @Tags('equipment')
  public async getEquipmentFaultsMovementsSeries(
    @Body() params: FaultsMovementsSeriesRequest
  ): Promise<FaultsMovementsSeriesData> {
    // Validate the start and end dates
    startEndDateValidation(params.start_date, params.end_date);

    // Get the equipment service
    const equipmentService = Container.get(EquipmentService);

    // Retrieve movement count series data and movements per fault series data from the store
    return await equipmentService.getEquipmentFaultsMovementsSeries({
      startDate: params.start_date,
      endDate: params.end_date,
      filters: params.filters as BaseFilterType,
    });
  }
}
