import {
  BaseFilterType,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Post,
  Body,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.ts';
import type {FaultsStatusListRequest} from '../defs/equipment-faults-status-list-def.ts';

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
])
export class EquipmentFaultsStatusListController extends Controller {
  /**
   * Example data for the fault status list
   */
  static readonly exampleData: string[] = ['Status 1', 'Status 2', 'Status 3'];

  static readonly exampleResponse: {availableStatuses: string[]} = {
    availableStatuses: EquipmentFaultsStatusListController.exampleData,
  };

  @Example<{
    availableStatuses: string[];
  }>(EquipmentFaultsStatusListController.exampleResponse)
  @SuccessResponse('200')
  @Post('/faults/status/list')
  @OperationId('PostEquipmentFaultsStatusList')
  @Tags('equipment')
  public async getEquipmentFaultsStatusList(
    @Body() params: FaultsStatusListRequest
  ): Promise<{availableStatuses: string[]}> {
    // Validate the start and end dates
    if (params.start_date && params.end_date) {
      startEndDateValidation(params.start_date, params.end_date);
    }

    // Get the equipment service
    const equipmentService = Container.get(EquipmentService);

    // Retrieve faults Status list data from the store
    const equipmentStatusListData: string[] =
      await equipmentService.getEquipmentFaultsStatusList({
        startDate: params.start_date,
        endDate: params.end_date,
        filters: params.filters as BaseFilterType,
      });

    const contract: {availableStatuses: string[]} = {
      availableStatuses: equipmentStatusListData,
    };
    return contract;
  }
}
