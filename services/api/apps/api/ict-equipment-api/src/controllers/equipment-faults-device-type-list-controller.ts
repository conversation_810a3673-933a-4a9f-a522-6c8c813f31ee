import {
  BaseFilterType,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Post,
  Body,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.ts';
import type {FaultsDeviceTypeListRequest} from '../defs/equipment-faults-device-type-def.ts';

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
])
export class EquipmentFaultsDeviceTypeListController extends Controller {
  /**
   * Example data for the fault device type list
   */
  static readonly exampleData: string[] = ['Type 1', 'Type 2', 'Type 3'];

  static readonly exampleResponse: {
    availableDeviceTypes: string[];
  } = {
    availableDeviceTypes: EquipmentFaultsDeviceTypeListController.exampleData,
  };

  @Example<{
    availableDeviceTypes: string[];
  }>(EquipmentFaultsDeviceTypeListController.exampleResponse)
  @SuccessResponse('200')
  @Post('/faults/device-type/list')
  @OperationId('PostEquipmentFaultsDeviceTypeList')
  @Tags('equipment')
  public async getEquipmentFaultsDeviceTypeList(
    @Body() params: FaultsDeviceTypeListRequest
  ): Promise<{availableDeviceTypes: string[]}> {
    // Validate the start and end dates

    // TODO: remove condition check when parameters become required
    if (params.start_date && params.end_date) {
      startEndDateValidation(params.start_date, params.end_date);
    }

    // Get the equipment service
    const equipmentService = Container.get(EquipmentService);

    // Retrieve faults device type list data from the store
    const equipmentDeviceTypeListData: string[] =
      await equipmentService.getEquipmentFaultsDeviceTypeList({
        startDate: params.start_date,
        endDate: params.end_date,
        filters: params.filters as BaseFilterType,
      });

    return {
      availableDeviceTypes: equipmentDeviceTypeListData,
    };
  }
}
