import {
  CacheMiddleware,
  Container,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {
  EquipmentActiveFaults,
  EquipmentActiveFaultsDefinition,
} from '../defs/equipment-faults-active-def.ts';
import {EquipmentService} from '../services/equipment-service.ts';

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class EquipmentFaultsActiveController extends Controller {
  /**
   * Example data for the active faults table
   */
  static readonly exampleData: EquipmentActiveFaultsDefinition[] = [
    {
      duration: new Date('2023-05-25T12:43:00.000Z').getTime(),
      area: 'Shipping',
      deviceReason: 'Code 123456',
    },
    {
      duration: new Date('2023-05-25T12:43:00.000Z').getTime(),
      area: 'Shipping',
      deviceReason: 'Code 343241324',
    },
    {
      duration: new Date('2023-05-25T12:43:00.000Z').getTime(),
      area: 'Packing',
      deviceReason: 'Code 765475674',
    },
  ];

  static readonly exampleResponse: EquipmentActiveFaults = {
    activeFaultsTable: EquipmentFaultsActiveController.exampleData,
  };

  @Example<EquipmentActiveFaults>(
    EquipmentFaultsActiveController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/faults/active/list')
  @OperationId('GetEquipmentFaultsActiveList')
  @Tags('equipment')
  public async getEquipmentFaultsActive(): Promise<EquipmentActiveFaults> {
    // Get the equipment service
    const equipmentService = Container.get(EquipmentService);

    // Retrieve equipment faults data from the store
    return {activeFaultsTable: await equipmentService.getFaultsActiveEvents()};
  }
}
