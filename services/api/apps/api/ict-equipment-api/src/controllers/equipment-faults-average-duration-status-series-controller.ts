import {
  BaseFilterType,
  Container,
  ProtectedRouteMiddleware,
  startEndDateValidation,
  DatabaseTypes,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Post,
  Body,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.ts';
import type {
  FaultAvgDurationByStatusRequest,
  FaultAvgDurationSeriesData,
} from '../defs/equipment-fault-counts-grouped-def.ts';

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
])
export class EquipmentFaultsAvgDurationByStatusController extends Controller {
  static exampleData: FaultAvgDurationSeriesData = {
    avgDuration: [
      {
        name: 'Reason 1',
        value: 33.4,
      },
      {
        name: 'Reason 2',
        value: 233.7,
      },
      {
        name: 'Reason 3',
        value: 199.2,
      },
    ],
  };

  static exampleResponse: FaultAvgDurationSeriesData =
    EquipmentFaultsAvgDurationByStatusController.exampleData;

  /**
   * Endpoint to get the average duration of faults by status for a given date range.
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {Promise<FaultAvgDurationSeriesData>} contract
   */
  @Example<FaultAvgDurationSeriesData>(
    EquipmentFaultsAvgDurationByStatusController.exampleResponse
  )
  @SuccessResponse('200')
  @Post('/faults/average/duration/status/series')
  @OperationId('getFaultAvgDurationByStatus')
  @Tags('equipment')
  public async getFaultAvgDurationByStatus(
    @Body() params: FaultAvgDurationByStatusRequest
  ): Promise<FaultAvgDurationSeriesData> {
    // Validate the start and end dates
    startEndDateValidation(params.startDate, params.endDate);
    // Get the equipment service
    const equipmentService = Container.get(EquipmentService);

    // Retrieve equipment faults data from the store
    return await equipmentService.getFaultsAvgDurationByStatusAsync({
      startDate: params.startDate,
      endDate: params.endDate,
      isAscendingOrder: params.isAscendingOrder,
      filters: params.filters as BaseFilterType,
    });
  }
}
