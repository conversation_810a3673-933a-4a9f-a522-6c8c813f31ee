import {
  ApiResponse,
  CacheMiddleware,
  Container,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.ts';
import {
  FaultsAreasConfig,
  FaultsAreasResponse,
  FaultsFacilityArea,
} from '../defs/fault-areas-def.ts';

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class EquipmentFaultsAreasController extends Controller {
  static readonly exampleData: FaultsFacilityArea[] = [
    {
      id: 'f746a38e-8e7f-4863-8ddd-5df5e192c760',
      name: 'Picking',
      alertStatus: {
        status: 'ok',
        identifier: '',
      },
      operators: {
        activeOperators: 1,
        lowRecOperators: 0,
        highRecOperators: 2,
      },
      faults: {
        totalFaults: 35,
        maxFaultsAllowed: 5,
        downtimeMinutes: 104.96945,
        status: 'caution',
      },
    },
    {
      id: '03dc76c5-e348-43ca-9b2a-9f3dfbe2079a',
      name: 'Packing',
      alertStatus: {
        status: 'ok',
        identifier: '',
      },
      operators: {
        activeOperators: 1,
        lowRecOperators: 0,
        highRecOperators: 2,
      },
      faults: {
        totalFaults: 4,
        maxFaultsAllowed: 5,
        downtimeMinutes: 0.0020666666666666667,
        status: 'caution',
      },
    },
  ];

  static readonly exampleConfig: FaultsAreasConfig = {
    min: 0,
    max: 5,
  };

  static readonly exampleResponse: ApiResponse<
    FaultsAreasResponse,
    FaultsAreasConfig
  > = {
    areas: EquipmentFaultsAreasController.exampleData,
    metadata: EquipmentFaultsAreasController.exampleConfig,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   */
  @Example<ApiResponse<FaultsAreasResponse, FaultsAreasConfig>>(
    EquipmentFaultsAreasController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/faults/area')
  @OperationId('GetEquipmentFaultsArea')
  @Tags('equipment')
  public async getFaultsAreas(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<ApiResponse<FaultsAreasResponse, FaultsAreasConfig>> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const equipmentService = Container.get(EquipmentService);

    // Retrieve fault areas data from the store
    const faultAreasData = await equipmentService.getFaultsAreasAsync(
      startDate.toISOString(),
      endDate.toISOString()
    );

    // TODO: pull this data from a config database, hardcoded for now
    const config: FaultsAreasConfig = {
      min: 0,
      max: 5,
    };

    return {
      areas: faultAreasData,
      metadata: config,
    };
  }
}
