import {
  CacheMiddleware,
  Container,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Path,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.ts';
import {EquipmentWorkstationActiveFaultsData} from '../defs/equipment-workstation-active-faults-def.ts';

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class EquipmentWorkstationActiveFaultsController extends Controller {
  static readonly exampleData: EquipmentWorkstationActiveFaultsData[] = [
    {
      description: 'Maintenance Gate Level 1',
      startTime: '2023-11-23T14:30:00.000Z',
      duration: 720,
      faultId: 'F1234',
    },
  ];

  static readonly exampleResponse: EquipmentWorkstationActiveFaultsData[] =
    EquipmentWorkstationActiveFaultsController.exampleData;

  @Example<EquipmentWorkstationActiveFaultsData[]>(
    EquipmentWorkstationActiveFaultsController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/workstation/{workstationId}/aisle/active-faults')
  @OperationId('GetEquipmentWorkstationAisleActiveFaults')
  @Tags('equipment')
  public async getWorkstationActiveFaults(
    @Path() workstationId: string
  ): Promise<EquipmentWorkstationActiveFaultsData[]> {
    // Get the equipment service
    const equipmentService = Container.get(EquipmentService);

    return await equipmentService.getEquipmentWorkstationActiveFaults(
      workstationId
    );
  }
}
