import {
  CacheMiddleware,
  Container,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Path,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.ts';
import {EquipmentWorkstationAisleMovementData} from '../defs/equipment-workstation-aisle-movement-chart-def.ts';

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class EquipmentWorkstationAisleMovementChartController extends Controller {
  static readonly exampleData: EquipmentWorkstationAisleMovementData = {
    retrieval: 17548,
    storage: 10517,
    positioning: 5688,
    iat: 5282,
    shuffle: 1435,
    bypass: 341,
  };

  static readonly exampleResponse: EquipmentWorkstationAisleMovementData =
    EquipmentWorkstationAisleMovementChartController.exampleData;

  @Example<EquipmentWorkstationAisleMovementData>(
    EquipmentWorkstationAisleMovementChartController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/workstation/{workstationId}/movements/detail')
  @OperationId('GetEquipmentWorkstationMovementsDetail')
  @Tags('equipment')
  public async getWorkstationAisleMovementChart(
    @Path() workstationId: string
  ): Promise<EquipmentWorkstationAisleMovementData> {
    // Get the equipment service
    const equipmentService = Container.get(EquipmentService);

    return await equipmentService.getEquipmentWorkstationAisleMovementData(
      workstationId
    );
  }
}
