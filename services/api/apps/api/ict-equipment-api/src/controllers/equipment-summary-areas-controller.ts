import {
  AlertStatus,
  CacheMiddleware,
  Container,
  HealthStatus,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.ts';
import {EquipmentFlowRateDefinition} from '../defs/equipment-flow-rate-def.ts';
import {EquipmentFaultsDowntimeQueryResponse} from '../defs/equipment-faults-downtime-def.ts';
import {MovementQualityQueryResponse} from '../defs/equipment-movement-quality-def.ts';
import {
  EquipmentSummaryAreaDefinition,
  EquipmentSummaryAreaContract,
} from '../defs/equipment-summary-areas-def.ts';
import {EquipmentFaultsMovementsQueryResponse} from '../defs/equipment-faults-movements-def.ts';

interface AggregatedMovementsPerFault {
  area: string;
  movementsPerFault: number;
}

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class EquipmentSummaryAreasController extends Controller {
  static readonly exampleMovementsPerFaultData: EquipmentFaultsMovementsQueryResponse[] =
    [
      {
        movementCount: 100,
        faultCount: 2,
        area: 'Area1',
        eventTime: '2023-04-10T01:26:54.813Z',
      },
      {
        movementCount: 5,
        faultCount: 0,
        area: 'Area1',
        eventTime: '2023-04-10T01:26:54.813Z',
      },
      {
        movementCount: 10,
        faultCount: 1,
        area: 'Area2',
        eventTime: '2023-04-10T01:26:54.813Z',
      },
      {
        movementCount: 100,
        faultCount: 1,
        area: 'Area2',
        eventTime: '2023-04-10T01:26:54.813Z',
      },
    ];

  static readonly exampleTotalDowntimeData: EquipmentFaultsDowntimeQueryResponse[] =
    [
      {
        area: 'Area1',
        downtimeMS: 480000,
      },
      {
        area: 'Area2',
        downtimeMS: 120000,
      },
    ];

  static readonly exampleFlowRateData: EquipmentFlowRateDefinition[] = [
    {
      area: 'Area1',
      avgHourlyFlowRate: 500,
      rangeOfHours: 5,
    },
    {
      area: 'Area2',
      avgHourlyFlowRate: 100,
      rangeOfHours: 2,
    },
  ];

  static readonly exampleMovementQualityData: MovementQualityQueryResponse[] = [
    {
      area: 'Area1',
      movementQualityPercentage: 99.5,
    },
    {
      area: 'Area2',
      movementQualityPercentage: 89,
    },
  ];

  static readonly exampleResponse: EquipmentSummaryAreaContract = {
    areas: [
      {
        id: 'Area1',
        downTimeMinutes: 8,
        movementsPerFault: 52.5,
        name: 'Area1',
        qualityPercentage: 99.5,
        outboundRatePerHour: 500,
        status: HealthStatus.Ok,
        alerts: [
          {
            status: HealthStatus.Critical,
            identifier: '',
          },
          {
            status: HealthStatus.Caution,
            identifier: 'Workstation 5',
          },
          {
            status: HealthStatus.Warning,
            identifier: 'Workstation 2',
          },
          {
            status: HealthStatus.Ok,
            identifier: 'Workstation 1',
          },
        ],
      },
      {
        id: 'Area2',
        downTimeMinutes: 2,
        movementsPerFault: 55,
        name: 'Area2',
        qualityPercentage: 89,
        outboundRatePerHour: 100,
        status: HealthStatus.Ok,
        alerts: [
          {
            status: HealthStatus.Critical,
            identifier: '',
          },
          {
            status: HealthStatus.Caution,
            identifier: 'Workstation 5',
          },
          {
            status: HealthStatus.Warning,
            identifier: 'Workstation 2',
          },
          {
            status: HealthStatus.Ok,
            identifier: 'Workstation 1',
          },
        ],
      },
    ],
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   */
  @Example<EquipmentSummaryAreaContract>(
    EquipmentSummaryAreasController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/summary/areas')
  @OperationId('GetEquipmentSummaryAreas')
  @Tags('equipment')
  public async getSummaryAreas(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<EquipmentSummaryAreaContract> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // Get the equipment service
    const equipmentService = Container.get(EquipmentService);

    // Query for the four KPIs for this endpoint
    const movementsPerFaultRequest =
      EquipmentSummaryAreasController.getAggregatedMovementsPerFault(
        equipmentService,
        startDate.toISOString(),
        endDate.toISOString()
      );

    const totalDowntimeRequest = equipmentService.getTotalDowntimeByArea(
      startDate.toISOString(),
      endDate.toISOString()
    );

    const movementQualityRequest = equipmentService.getMovementQualityByArea(
      startDate.toISOString(),
      endDate.toISOString()
    );

    const flowRateRequest = equipmentService.getEquipmentFlowRateByArea(
      startDate.toISOString(),
      endDate.toISOString()
    );

    // Run each of the four queries in parallel
    const promises: [
      Promise<AggregatedMovementsPerFault[]>,
      Promise<EquipmentFaultsDowntimeQueryResponse[]>,
      Promise<MovementQualityQueryResponse[]>,
      Promise<EquipmentFlowRateDefinition[]>,
    ] = [
      movementsPerFaultRequest,
      totalDowntimeRequest,
      movementQualityRequest,
      flowRateRequest,
    ];

    const [movementsPerFault, totalDowntime, movementQuality, flowRate] =
      await Promise.all(promises);

    // TODO: return a 204 no content response if all KPIs are empty

    // Map the four KPI queries to EquipmentSummaryAreaDefinition
    const equipmentAreaResults =
      EquipmentSummaryAreasController.mapToEquipmentSummaryArea(
        movementsPerFault,
        totalDowntime,
        movementQuality,
        flowRate
      );

    return {
      areas: equipmentAreaResults,
    };
  }

  /**
   * Maps the four KPI array inputs into an EquipmentSummaryAreaDefinition
   * @param movementsPerFault
   * @param totalDowntime
   * @param movementQuality
   * @param flowRate
   * @returns EquipmentSummaryAreaDefinition[]
   */
  static mapToEquipmentSummaryArea(
    movementsPerFault: AggregatedMovementsPerFault[],
    totalDowntime: EquipmentFaultsDowntimeQueryResponse[],
    movementQuality: MovementQualityQueryResponse[],
    flowRate: EquipmentFlowRateDefinition[]
  ): EquipmentSummaryAreaDefinition[] {
    const results: EquipmentSummaryAreaDefinition[] = [];

    movementsPerFault.forEach(areaMovementsPerFault => {
      const area = areaMovementsPerFault.area;
      const areaTotalDowntime = totalDowntime.find(td => td.area === area);
      const areaMovementQuality = movementQuality.find(mq => mq.area === area);
      const areaFlowRate = flowRate.find(fr => fr.area === area);

      const currentMovementsPerFault = areaMovementsPerFault.movementsPerFault;
      const currentDownTimeMinutes =
        (areaTotalDowntime?.downtimeMS || 0) / 60000;
      const currentQualityPercentage =
        areaMovementQuality?.movementQualityPercentage;

      results.push({
        id: area,
        downTimeMinutes: currentDownTimeMinutes,
        movementsPerFault: currentMovementsPerFault,
        name: area,
        qualityPercentage: currentQualityPercentage || 0,
        outboundRatePerHour: areaFlowRate?.avgHourlyFlowRate || 0,
        status: EquipmentSummaryAreasController.getMockStatus(area),
        alerts: EquipmentSummaryAreasController.getMockAlerts(),
      });
    });

    return results;
  }

  /**
   * TODO: This is a temporary function to return mock status for an area
   */
  static getMockStatus(area: string): HealthStatus {
    switch (area) {
      case 'PACKING':
        return HealthStatus.Critical;
      case 'PICKING':
        return HealthStatus.Caution;
      case 'SHIPPING':
        return HealthStatus.Warning;
      case 'STORING':
        return HealthStatus.Ok;
      default:
        return HealthStatus.Ok;
    }
  }

  /**
   * TODO: This is a temporary function to return mock alerts for an area
   */
  static getMockAlerts(): AlertStatus[] {
    return [
      {
        status: HealthStatus.Critical,
        identifier: '',
      },
      {
        status: HealthStatus.Caution,
        identifier: 'Workstation 5',
      },
      {
        status: HealthStatus.Warning,
        identifier: 'Workstation 2',
      },
      {
        status: HealthStatus.Ok,
        identifier: 'Workstation 1',
      },
    ];
  }

  /**
   * Calls the getMovementsPerFaultByArea query and reduces the 15m bucketed
   * results into a single "movementsPerFault" for each area
   * @param equipmentService
   * @param startDate
   * @param endDate
   * @returns AggregatedMovementsPerFault[]
   */
  static async getAggregatedMovementsPerFault(
    equipmentService: EquipmentService,
    startDate: string,
    endDate: string
  ): Promise<AggregatedMovementsPerFault[]> {
    const areaMap: {
      [areaName: string]: {totalMovements: number; totalFaults: number};
    } = {};

    const movementsPerFaultByArea =
      await equipmentService.getMovementsPerFaultByArea(startDate, endDate);

    // Sum up the total movements and total faults for each area
    movementsPerFaultByArea.forEach(movement => {
      if (areaMap[movement.area]) {
        const currentArea = areaMap[movement.area];
        currentArea.totalFaults += movement.faultCount;
        currentArea.totalMovements += movement.movementCount;
      } else {
        areaMap[movement.area] = {
          totalFaults: movement.faultCount,
          totalMovements: movement.movementCount,
        };
      }
    });

    // For each area, calculate the movementsPerFault and return
    const results: AggregatedMovementsPerFault[] = [];
    for (const [key, value] of Object.entries(areaMap)) {
      results.push({
        area: key,
        movementsPerFault: value.totalMovements / value.totalFaults,
      });
    }

    return results;
  }
}
