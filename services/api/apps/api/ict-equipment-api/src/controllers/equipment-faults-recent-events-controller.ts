import {
  CacheMiddleware,
  Container,
  ProtectedRouteMiddleware,
  DatabaseTypes,
  configPostgresDatabase,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.ts';
import {
  FaultEvent,
  FaultEventContract,
} from '../defs/equipment-faults-event-def.ts';

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class EquipmentFaultsRecentEventsController extends Controller {
  static readonly exampleData: FaultEvent[] = [
    {
      time: '2024-01-04T18:40:29.371Z',
      area: '03dc76c5-e348-43ca-9b2a-9f3dfbe2079a MS072145160111',
      description: ' ',
    },
    {
      time: '2024-01-04T18:33:40.098Z',
      area: '03dc76c5-e348-43ca-9b2a-9f3dfbe2079a MS052065060111',
      description: ' ',
    },
    {
      time: '2024-01-04T18:31:31.433Z',
      area: 'f746a38e-8e7f-4863-8ddd-5df5e192c760 CCTA01TRNS',
      description: ' ',
    },
    {
      time: '2024-01-04T18:29:04.873Z',
      area: 'f746a38e-8e7f-4863-8ddd-5df5e192c760 CCTA01TRNS',
      description: ' ',
    },
  ];

  static readonly exampleResponse: FaultEventContract =
    EquipmentFaultsRecentEventsController.exampleData;

  @Example<FaultEventContract>(
    EquipmentFaultsRecentEventsController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/faults/events')
  @OperationId('GetEquipmentFaultsEvents')
  @Tags('equipment')
  public async getFaultsRecentEvents(): Promise<FaultEventContract> {
    // Get the equipment service
    const equipmentService = Container.get(EquipmentService);

    return await equipmentService.getFaultsRecentEvents();
  }
}
