import {
  ApiResponseArray,
  BaseFilterType,
  Container,
  ProtectedRouteMiddleware,
  SortField,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Body,
  Controller,
  Example,
  Middlewares,
  Post,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {BigQueryTimestamp} from '@google-cloud/bigquery';
import {EquipmentService} from '../services/equipment-service.ts';
import {
  RecentEventQueryConfig,
  RecentEventQueryResponse,
  RecentEventQueryServiceParams,
} from '../defs/equipment-recent-event-def.ts';

@Route('equipment')
@Middlewares([ProtectedRouteMiddleware.apiProtectedDataRoute()])
export class EquipmentRecentEventsController extends Controller {
  static readonly exampleRequest = {
    filters: {
      type: 'single',
      comparison: 'NotLike',
      name: 'event_time',
      value: '123',
    },
    start_date: '2024-01-01T00:00:00.000Z',
    end_date: '2024-01-02T00:00:00.000Z',
  };

  static readonly exampleMinRequest = {
    start_date: '2024-01-01T00:00:00.000Z',
    end_date: '2024-01-02T00:00:00.000Z',
  };

  static readonly exampleData: RecentEventQueryResponse[] = [
    {
      time: new BigQueryTimestamp('2024-01-01T00:00:00.000Z'),
      tenant: 'Dematic Software',
      facility: 'Site 1',
      description: 'Description',
      eventType: 'event_type',
      equipmentId: '123',
      eventStartTime: new BigQueryTimestamp('2024-01-01T00:00:00.000Z'),
      totalTime: 123,
      faultCode: 'E123',
    },
  ];

  static readonly exampleConfig: RecentEventQueryConfig = {
    page: 1,
    limit: 100,
    totalResults: 25,
  };

  static readonly exampleResponse: ApiResponseArray<
    RecentEventQueryResponse[],
    RecentEventQueryConfig
  > = {
    data: EquipmentRecentEventsController.exampleData,
    metadata: EquipmentRecentEventsController.exampleConfig,
  };

  /**
   * @example filters {
   *  "type": "single",
   *  "comparison": "NotLike",
   *  "name": "<string>",
   *  "value": "<string>>"
   * }
   * @example sortFields [
   *  {
   *    "columnName": "event_time",
   *    "isDescending": true
   *  }
   * ]
   * @example page 1
   * @example limit 100
   */
  @Example(EquipmentRecentEventsController.exampleResponse)
  @SuccessResponse('200')
  @Post('/events/list')
  @OperationId('PostEquipmentEventsList')
  @Tags('equipment')
  public async getRecentEvents(
    @Body()
    paginatedRequest: {
      filters?: BaseFilterType;
      sortFields?: SortField[];
      page?: number;
      limit?: number;
      start_date: Date;
      end_date: Date;
    }
  ): Promise<
    ApiResponseArray<RecentEventQueryResponse[], RecentEventQueryConfig>
  > {
    // Validate the start and end dates
    startEndDateValidation(
      paginatedRequest.start_date,
      paginatedRequest.end_date
    );
    // Get the equipment service
    const equipmentService = Container.get(EquipmentService);

    const filters = paginatedRequest.filters;
    let sortFields = paginatedRequest.sortFields;
    let pageNum = paginatedRequest.page;
    let limitNum = paginatedRequest.limit;

    const startDate = paginatedRequest.start_date;
    let endDate = paginatedRequest.end_date;

    if (paginatedRequest.start_date && !paginatedRequest.end_date) {
      endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + 1);
    }

    if (!pageNum) {
      // default page value
      pageNum = 1;
    }

    if (!limitNum) {
      // default limit value
      limitNum = 100;
    }

    if (!sortFields) {
      // default to no sorting
      sortFields = [];
    }

    const recentEventQueryServiceParams: RecentEventQueryServiceParams = {
      startDate,
      endDate,
      limit: limitNum,
      page: pageNum,
      sortField: sortFields,
      reqFilter: filters,
    };

    const recentEvents = await equipmentService.getRecentEvents(
      recentEventQueryServiceParams
    );

    return recentEvents;
  }
}
