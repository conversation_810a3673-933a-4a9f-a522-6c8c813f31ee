import {BaseHealthCheck} from 'ict-api-foundations';
import {Get, Route, OperationId, Tags} from 'tsoa';

@Route('/equipment/healthcheck')
export class EquipmentHealthCheckController {
  @Get()
  @OperationId('GetEquipmentBasicHealthCheck')
  @Tags('equipment')
  public getEquipmentBasicHealthCheck() {
    return BaseHealthCheck.getBasicHealthCheck();
  }

  @Get('/full')
  @OperationId('GetEquipmentFullHealthCheck')
  @Tags('equipment')
  public getEquipmentFullHealthCheck() {
    return BaseHealthCheck.getDeepHealthCheck(['auth0', 'bigQuery', 'redis']);
  }
}
