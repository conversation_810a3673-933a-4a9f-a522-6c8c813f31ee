import {
  CacheMiddleware,
  Container,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Path,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {EquipmentService} from '../services/equipment-service.ts';
import {EquipmentWorkstationLineRatesSeriesData} from '../defs/equipment-workstation-line-rates-series-def.ts';

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class EquipmentWorkstationLineRatesSeriesController extends Controller {
  /**
   * Example data for the active faults table
   */
  static readonly exampleData: EquipmentWorkstationLineRatesSeriesData = {
    lineRates: [
      {
        name: '2023-01-01T00:00:00.000Z',
        value: 100,
      },
      {
        name: '2023-01-01T01:00:00.000Z',
        value: 120,
      },
      {
        name: '2023-01-01T02:00:00.000Z',
        value: 15,
      },
      {
        name: '2023-01-01T03:00:00.000Z',
        value: 70,
      },
    ],
  };

  static readonly exampleResponse: EquipmentWorkstationLineRatesSeriesData =
    EquipmentWorkstationLineRatesSeriesController.exampleData;

  @Example<EquipmentWorkstationLineRatesSeriesData>(
    EquipmentWorkstationLineRatesSeriesController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/workstation/{workstationId}/line-rates/series')
  @OperationId('GetEquipmentWorkstationLineRatesSeries')
  @Tags('equipment')
  public async getWorkstationLineRatesSeries(
    @Path() workstationId: string
  ): Promise<EquipmentWorkstationLineRatesSeriesData> {
    const equipmentService = Container.get(EquipmentService);

    return await equipmentService.getEquipmentWorkstationLineRatesSeries(
      workstationId
    );
  }
}
