import {
  Body,
  Controller,
  Example,
  Middlewares,
  Post,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {
  BaseFilterType,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import type {
  EquipmentFaultsDeviceIdRequest,
  EquipmentFaultsDeviceIdServiceParams,
} from '../defs/equipment-faults-deviceids-list-def.ts';
import {EquipmentService} from '../services/equipment-service.ts';

@Route('equipment')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
])
export class EquipmentFaultsDeviceIdsListController extends Controller {
  static readonly exampleRequest = {
    start_date: '2024-01-01T00:00:00.000Z',
    end_date: '2024-01-02T00:00:00.000Z',
  };

  static readonly exampleData: string[] = [
    'equipment1',
    'equipment2',
    'equipment3',
  ];

  static readonly exampleResponse =
    EquipmentFaultsDeviceIdsListController.exampleData;

  @Example(EquipmentFaultsDeviceIdsListController.exampleResponse)
  @SuccessResponse('200')
  @Post('/faults/device-id/list')
  @OperationId('PostEquipmentFaultsDeviceIdsList')
  @Tags('equipment')
  public async getEquipmentFaultsDeviceIdList(
    @Body()
    requestBody: EquipmentFaultsDeviceIdRequest
  ): Promise<string[]> {
    // Validate the start and end dates
    if (requestBody.start_date && requestBody.end_date) {
      startEndDateValidation(requestBody.start_date, requestBody.end_date);
    }
    // Get the equipment service
    const equipmentService = Container.get(EquipmentService);

    const equipmentFaultsDeviceIdServiceParams: EquipmentFaultsDeviceIdServiceParams =
      {
        startDate: requestBody.start_date,
        endDate: requestBody.end_date,
        filters: requestBody.filters as BaseFilterType,
      };

    return await equipmentService.getEquiupmentFaultsDeviceIdsList(
      equipmentFaultsDeviceIdServiceParams
    );
  }
}
