import {
  ApiResponseArray,
  BaseFilterType,
  BigQueryGenerator,
  ChartSeriesData,
  DiService,
  PaginatedResults,
  SortField,
  SQLBaseFieldConversionResult,
  TreeFieldFilterObject,
} from 'ict-api-foundations';
import {EquipmentActiveFaultsDefinition} from '../defs/equipment-faults-active-def.ts';
import {EquipmentFaultsQueryDefinition} from '../defs/equipment-faults-def.ts';
import {EquipmentFaultsDowntimeQueryResponse} from '../defs/equipment-faults-downtime-def.ts';
import {FaultEvent} from '../defs/equipment-faults-event-def.ts';
import {EquipmentFaultsMovementsQueryResponse} from '../defs/equipment-faults-movements-def.ts';
import {EquipmentFaultsSeriesQueryData} from '../defs/equipment-faults-series-def.ts';
import {EquipmentFlowRateDefinition} from '../defs/equipment-flow-rate-def.ts';
import {
  RecentEventQueryConfig,
  RecentEventQueryResponse,
  RecentEventQueryServiceParams,
  RecentEventQueryStoreParams,
} from '../defs/equipment-recent-event-def.ts';
import {MovementQualityQueryResponse} from '../defs/equipment-movement-quality-def.ts';
import {EquipmentSummaryAisleDefinition} from '../defs/equipment-summary-aisles-def.ts';
import {EquipmentSummaryWorkstationDefinition} from '../defs/equipment-summary-workstations-def.ts';
import {EquipmentWorkstationOperatorActivityData} from '../defs/equipment-workstation-operator-activity-def.ts';
import {FaultsFacilityArea} from '../defs/fault-areas-def.ts';
import {EquipmentStore} from '../stores/equipment-store.ts';
import {EquipmentWorkstationLineRatesSeriesData} from '../defs/equipment-workstation-line-rates-series-def.ts';
import {
  EquipmentOutboundRate,
  EquipmentOutboundRateQueryResponse,
} from '../defs/equipment-outbound-rate-def.ts';
import {EquipmentWorkstationStarvedBlockedTimeSeriesData} from '../defs/equipment-workstation-starved-blocked-time-series-def.ts';
import {EquipmentWorkstationAisleMovementData} from '../defs/equipment-workstation-aisle-movement-chart-def.ts';
import {EquipmentWorkstationActiveFaultsData} from '../defs/equipment-workstation-active-faults-def.ts';
import {
  FaultAvgDurationByStatusQueryServiceParams,
  FaultAvgDurationSeriesData,
  FaultCountGroupedByServiceParams,
  FaultCountGroupedSeriesData,
} from '../defs/equipment-fault-counts-grouped-def.ts';
import {FaultsLevelListServiceParams} from '../defs/equipment-faults-level-list-def.ts';
import {FaultsAisleListServiceParams} from '../defs/equipment-faults-aisle-list-def.ts';
import {EquipmentFaultsDeviceIdServiceParams} from '../defs/equipment-faults-deviceids-list-def.ts';
import {
  FaultsMovementsSeriesData,
  FaultsMovementsSeriesServiceParams,
} from '../defs/equipment-faults-movements-series-def.ts';
import {FaultsDeviceTypeListServiceParams} from '../defs/equipment-faults-device-type-def.ts';
import {FaultsStatusListServiceParams} from '../defs/equipment-faults-status-list-def.ts';
import {
  EquipmentFaultsListServiceParams,
  PostEquipmentFaultsListResponse,
} from '../defs/equipment-faults-list-def.ts';

/**
 * Service that handles applying business logic for Equipment to store queries.
 */
@DiService()
export class EquipmentService {
  constructor(private equipmentStore: EquipmentStore) {}

  /**
   * Attempt to query the database for equipment faults data.
   * @param startDate start of the date range.
   * @param endDate end of the date range.
   * @returns Data Contract that contains the EquipmentFaultsDefinition with equipment faults data.
   */
  async getEquipmentFaultsAsync(
    startDate: string,
    endDate: string,
  ): Promise<EquipmentFaultsQueryDefinition> {
    const faultsData = await this.equipmentStore.getFaultsCountAsync(
      startDate,
      endDate,
    );

    return faultsData;
  }

  /**
   * Queries the db for the equipment faults series data
   * @param startDate start of the date range.
   * @param endDate end of the date range.
   * @returns EquipmentFaultsSeriesQueryData[]
   */
  async getEquipmentFaultsSeries(
    startDate: string,
    endDate: string,
  ): Promise<EquipmentFaultsSeriesQueryData[]> {
    return await this.equipmentStore.getFaultsToThroughputSeries(
      startDate,
      endDate,
    );
  }

  /**
   * Queries the db for movements per fault. Data is bucketed in 15m intervals
   * @param startDate start of the date range.
   * @param endDate end of the date range.
   * @returns EquipmentFaultsMovementsQueryResponse[]
   */
  async getMovementsPerFaultByArea(
    startDate: string,
    endDate: string,
  ): Promise<EquipmentFaultsMovementsQueryResponse[]> {
    const movementsPerFault =
      await this.equipmentStore.getFaultAndEquipmentMovementsAsync(
        startDate,
        endDate,
      );
    if (!movementsPerFault) {
      return [];
    }

    return movementsPerFault;
  }

  /**
   * Queries the db for total downtime grouped by area
   * @param startDate start of the date range.
   * @param endDate end of the date range.
   * @returns EquipmentFaultsDowntimeQueryResponse[]
   */
  async getTotalDowntimeByArea(
    startDate: string,
    endDate: string,
  ): Promise<EquipmentFaultsDowntimeQueryResponse[]> {
    const totalDowntimes = await this.equipmentStore.getTotalDowntimes(
      startDate,
      endDate,
    );
    if (!totalDowntimes) {
      return [];
    }

    return totalDowntimes;
  }

  /**
   * Queries the db for movement quality grouped by area
   * @param startDate start of the date range.
   * @param endDate end of the date range.
   * @returns MovementQualityQueryResponse[]
   */
  async getMovementQualityByArea(
    startDate: string,
    endDate: string,
  ): Promise<MovementQualityQueryResponse[]> {
    const movementQuality = await this.equipmentStore.getMovementQuality(
      startDate,
      endDate,
    );
    if (!movementQuality) {
      return [];
    }

    return movementQuality;
  }

  /*
   * Queries the db for summary information on an Aisle
   * @param startDate start of the date range.
   * @param endDate end of the date range.
   * @returns EquipmentSummaryAisleDefinition
   */
  async getAisleSummary(
    aisleId: string,
    startDate: string,
    endDate: string,
  ): Promise<EquipmentSummaryAisleDefinition> {
    return this.equipmentStore.getAisleSummary(aisleId, startDate, endDate);
  }

  /**
   * Queries the db for summary information on an Workstation
   * @param startDate start of the date range.
   * @param endDate end of the date range.
   * @returns EquipmentSummaryWorkstationDefinition
   */
  async getWorkstationSummary(
    startDate: string,
    endDate: string,
    workstationId: string,
  ): Promise<EquipmentSummaryWorkstationDefinition> {
    return this.equipmentStore.getWorkstationSummary(
      startDate,
      endDate,
      workstationId,
    );
  }

  /**
   * Queries the db for equipment flow rate grouped by area
   * @param startDate start of the date range.
   * @param endDate end of the date range.
   * @returns EquipmentFlowRateDefinition[]
   */
  async getEquipmentFlowRateByArea(
    startDate: string,
    endDate: string,
  ): Promise<EquipmentFlowRateDefinition[]> {
    const flowRate = await this.equipmentStore.getEquipmentFlowRateAverage(
      startDate,
      endDate,
    );
    if (!flowRate) {
      return [];
    }

    return flowRate;
  }

  /**
   * Attempt to query the database for fault areas data.
   * @param startDate start of the date range.
   * @param endDate end of the date range.
   * @returns array of fault area data
   */
  async getFaultsAreasAsync(
    startDate: string,
    endDate: string,
  ): Promise<FaultsFacilityArea[]> {
    const faultAreasData = await this.equipmentStore.getFaultsAreasAsync(
      startDate,
      endDate,
    );

    return faultAreasData;
  }

  async getFaultsRecentEvents(): Promise<FaultEvent[]> {
    return (await this.equipmentStore.getFaultsRecentEvents()).map(
      faultEvent => ({
        time: faultEvent._TimeUTC.value,
        area: faultEvent.Area,
        description: `${faultEvent.Message}${
          faultEvent.FLT_DESC ? `; ${faultEvent.FLT_DESC}` : ''
        }`,
      }),
    );
  }

  async getFaultsActiveEvents(): Promise<EquipmentActiveFaultsDefinition[]> {
    return this.equipmentStore.getEquipmentActiveFaultsData();
  }

  async getFaultsCountGroupedAsync(
    params: FaultCountGroupedByServiceParams,
  ): Promise<FaultCountGroupedSeriesData> {
    const seriesData: ChartSeriesData[] = (
      await this.equipmentStore.getFaultsCountGroupedAsync({
        startDate: params.startDate,
        endDate: params.endDate,
        groupByColumn: params.groupByColumn,
        isAscendingOrder: params.isAscendingOrder,
        filters: params.filters,
      })
    ).map(ele => ({
      name: ele.groupByColumn,
      value: ele.count,
    }));

    return {
      faultCounts: seriesData,
    };
  }
  async getEquiupmentFaultsDeviceIdsList(
    params: EquipmentFaultsDeviceIdServiceParams,
  ): Promise<string[]> {
    return (
      await this.equipmentStore.getEquipmentFaultsFilterTypeList({
        startDate: params.startDate,
        endDate: params.endDate,
        filters: params.filters,
        filterColumnName: 'device_code',
      })
    ).map(ele => ele.faultFilter);
  }

  async getFaultsAvgDurationByStatusAsync(
    params: FaultAvgDurationByStatusQueryServiceParams,
  ): Promise<FaultAvgDurationSeriesData> {
    const seriesData: ChartSeriesData[] = (
      await this.equipmentStore.getFaultsAvgDurationByStatusAsync({
        startDate: params.startDate,
        endDate: params.endDate,
        isAscendingOrder: params.isAscendingOrder,
        filters: params.filters,
      })
    ).map(ele => ({
      name: ele.status,
      value: ele.avgDuration,
    }));

    return {
      avgDuration: seriesData,
    };
  }

  async getRecentEvents(
    recentEventQueryParams: RecentEventQueryServiceParams,
  ): Promise<
    ApiResponseArray<RecentEventQueryResponse[], RecentEventQueryConfig>
  > {
    const updatedQueryParams: RecentEventQueryStoreParams = {
      startDate: recentEventQueryParams.startDate,
      endDate: recentEventQueryParams.endDate,
      limit: recentEventQueryParams.limit,
      page: recentEventQueryParams.page,
      sortField: recentEventQueryParams.sortField,
      filters: recentEventQueryParams.reqFilter,
    };
    const recentEvent: PaginatedResults<RecentEventQueryResponse[]> =
      await this.equipmentStore.getRecentEvents(updatedQueryParams);

    const recentEventContract: ApiResponseArray<
      RecentEventQueryResponse[],
      RecentEventQueryConfig
    > = {
      data: recentEvent.list,
      metadata: {
        page: recentEventQueryParams.page,
        limit: recentEventQueryParams.limit,
        totalResults: recentEvent.totalResults,
      },
    };
    return recentEventContract;
  }

  async getWorkstationOperatorActivity(
    workstationId: string,
  ): Promise<EquipmentWorkstationOperatorActivityData[]> {
    const activityData =
      await this.equipmentStore.getWorkstationOperatorActivity(workstationId);

    return activityData.map(ad => {
      // If operator is still active at the workstation use current time as "end time" for duration calculations
      const endTime = ad.end_time?.value ?? new Date().toISOString();
      return {
        operatorId: ad.operator_id,
        startTime: ad.start_time.value,
        // Use null to represent that the operator is still active at the workstation
        endTime: ad.end_time?.value ?? null,
        // totalTimeDuration is the difference in seconds between ad.start_time.value and ad.end_time.value
        totalTimeDuration: Math.floor(
          (new Date(endTime).getTime() -
            new Date(ad.start_time.value).getTime()) /
            1000,
        ),
        idleTime: ad.idle_time,
        starvedTime: ad.starved_time,
        blockedTime: ad.blocked_time,
        lineRatePerHour: ad.line_rates,
        weightedLineRatePerHour: ad.weighted_line_rates,
      };
    });
  }

  async getEquipmentWorkstationActiveFaults(
    locationId: string,
  ): Promise<EquipmentWorkstationActiveFaultsData[]> {
    const activeFaults =
      await this.equipmentStore.getEquipmentWorkstationActiveFaults(locationId);
    return activeFaults.map(af => ({
      description: af.description,
      startTime: af.start_time.value,
      duration: af.duration,
      faultId: af.fault_id,
    }));
  }

  async getEquipmentWorkstationLineRatesSeries(
    workstationId: string,
  ): Promise<EquipmentWorkstationLineRatesSeriesData> {
    const chartData =
      await this.equipmentStore.getEquipmentWorkstationLineRatesSeries(
        workstationId,
      );
    return {
      lineRates: chartData.map(cd => ({
        name: cd.date_time.value,
        value: cd.line_rate,
      })),
    };
  }

  getFaultValue(faultCode: boolean): string {
    // return active fault value for boolean mapping
    const faultValue = faultCode ? '1' : '0';
    return faultValue;
  }

  async getOutboundRate(): Promise<EquipmentOutboundRate> {
    const queryResponse: EquipmentOutboundRateQueryResponse =
      await this.equipmentStore.getOutboundRate();

    return {
      outboundDivertsHour: queryResponse.outboundDivertsHour,
      status: 'ok',
    };
  }

  async getEquipmentWorkstationStarvedBlockedSeries(
    workstationId: string,
  ): Promise<EquipmentWorkstationStarvedBlockedTimeSeriesData> {
    const chartData =
      await this.equipmentStore.getEquipmentWorkstationStarvedBlockedSeries(
        workstationId,
      );
    return {
      starvedTime: chartData.map(cd => ({
        name: cd.date_time.value,
        value: cd.starved_time_percentage,
      })),
      blockedTime: chartData.map(cd => ({
        name: cd.date_time.value,
        value: cd.blocked_time_percentage,
      })),
    };
  }

  /**
   * Get aisle movement data for a workstation
   * @param workstationId Id of the workstation
   * @returns Aisle movement data for the workstation
   */
  async getEquipmentWorkstationAisleMovementData(
    /** Id of the workstation */
    workstationId: string,
  ): Promise<EquipmentWorkstationAisleMovementData> {
    return await this.equipmentStore.getEquipmentWorkstationAisleMovementData(
      workstationId,
    );
  }

  async getEquipmentFaultsLevelList(
    params: FaultsLevelListServiceParams,
  ): Promise<string[]> {
    return (
      await this.equipmentStore.getEquipmentFaultsFilterTypeList({
        startDate: params.startDate,
        endDate: params.endDate,
        filters: params.filters,
        filterColumnName: 'level',
      })
    ).map(ele => ele.faultFilter);
  }

  async getEquipmentFaultsDeviceTypeList(
    params: FaultsDeviceTypeListServiceParams,
  ): Promise<string[]> {
    return (
      await this.equipmentStore.getEquipmentFaultsFilterTypeList({
        startDate: params.startDate,
        endDate: params.endDate,
        filters: params.filters,
        filterColumnName: 'device_functional_type',
      })
    ).map(ele => ele.faultFilter);
  }

  async getEquipmentFaultsAisleList(
    params: FaultsAisleListServiceParams,
  ): Promise<string[]> {
    return (
      await this.equipmentStore.getEquipmentFaultsFilterTypeList({
        startDate: params.startDate,
        endDate: params.endDate,
        filters: params.filters,
        filterColumnName: 'aisle',
      })
    ).map(ele => ele.faultFilter);
  }

  async getEquipmentFaultsMovementsSeries(
    params: FaultsMovementsSeriesServiceParams,
  ): Promise<FaultsMovementsSeriesData> {
    let faultFilters: SQLBaseFieldConversionResult | undefined;
    let movementFilters: SQLBaseFieldConversionResult | undefined;
    if (params.filters) {
      const bqQueryGenerator = new BigQueryGenerator();
      faultFilters = bqQueryGenerator.baseFieldConversion(params.filters);

      // movement data cannot be filtered by reason_name
      const filtersWithoutStatus = BigQueryGenerator.removeFilter(
        params.filters as TreeFieldFilterObject,
        'reason_name',
      ) as BaseFilterType;

      movementFilters = filtersWithoutStatus
        ? bqQueryGenerator.baseFieldConversion(filtersWithoutStatus)
        : undefined;
    }

    const chartData =
      await this.equipmentStore.getEquipmentFaultsMovementsSeries({
        startDate: params.startDate,
        endDate: params.endDate,
        faultFilters: faultFilters?.filter,
        movementFilters: movementFilters?.filter,
        filterParams: faultFilters?.filterParams,
      });

    return {
      movementCounts: chartData.map(row => ({
        name: row.eventInterval.value,
        value: row.movementCount,
      })),
      movementsPerFault: chartData.map(row => ({
        name: row.eventInterval.value,
        value: row.movementsPerFault,
      })),
    };
  }

  async getEquipmentFaultsStatusList(
    params: FaultsStatusListServiceParams,
  ): Promise<string[]> {
    return (
      await this.equipmentStore.getEquipmentFaultsFilterTypeList({
        startDate: params.startDate,
        endDate: params.endDate,
        filters: params.filters,
        filterColumnName: 'reason_name',
      })
    ).map(ele => ele.faultFilter);
  }

  async getEquipmentFaultsList(
    params: EquipmentFaultsListServiceParams,
  ): Promise<PostEquipmentFaultsListResponse> {
    const sortFields: SortField[] = params.sortFields ?? [];
    const limit: number = params.limit ?? 25;
    const page: number = params.page ?? 1;

    const results = await this.equipmentStore.getEquipmentFaultsList({
      startDate: params.startDate,
      endDate: params.endDate,
      filters: params.reqFilter,
      sortFields,
      limit,
      page,
    });

    const faultList: PostEquipmentFaultsListResponse = {
      data: results.list.map(ele => ({
        timestamp: ele.timestamp,
        durationMinutes: ele.durationMinutes,
        status: ele.reason_name,
        aisle: ele.aisle,
        level: ele.level,
        device: ele.device_code,
        deviceType: ele.device_functional_type,
      })),
      metadata: {
        page,
        limit,
        totalResults: results.totalResults,
      },
    };

    return faultList;
  }
}
