import {BigQueryDate} from '@google-cloud/bigquery';

/** Definition of the query response for workstation active faults */
export interface EquipmentWorkstationActiveFaultsQueryResponse {
  /** Description of the fault */
  description: string;
  /** Start time of the fault */
  start_time: BigQueryDate;
  /** How long the fault has been active, in seconds */
  duration: number;
  /** Fault ID */
  fault_id: string;
}

/** Definiton of the data for the data contract for workstation active faults */
export interface EquipmentWorkstationActiveFaultsData {
  /** Description of the fault */
  description: string;
  /** Start time of the fault */
  startTime: string;
  /** How long the fault has been active, in seconds */
  duration: number;
  /** Fault ID */
  faultId: string;
}
