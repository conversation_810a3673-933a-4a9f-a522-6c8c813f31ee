import {
  BaseFilterType,
  ChartSeriesData,
  FilterObjectParameters,
} from 'ict-api-foundations';

export type FaultCountGroupedByQueryStoreParams = {
  startDate: Date;
  endDate: Date;
  groupByColumn:
    | 'aisle'
    | 'level'
    | 'device_functional_type'
    | 'device_code'
    | 'reason_name';
  isAscendingOrder?: boolean;
  filters?: BaseFilterType;
  filterParams?: FilterObjectParameters;
};

export type FaultAvgDurationByStatusQueryStoreParams = {
  startDate: Date;
  endDate: Date;
  isAscendingOrder?: boolean;
  filters?: BaseFilterType;
  filterParams?: FilterObjectParameters;
};

export type FaultCountGroupedByRequest = {
  startDate: Date;
  endDate: Date;
  groupByColumn:
    | 'aisle'
    | 'level'
    | 'device_functional_type'
    | 'device_code'
    | 'reason_name';
  isAscendingOrder?: boolean;
  filters?: unknown;
};

export type FaultAvgDurationByStatusRequest = {
  startDate: Date;
  endDate: Date;
  isAscendingOrder?: boolean;
  filters?: unknown;
};

export interface FaultCountGroupedByServiceParams {
  startDate: Date;
  endDate: Date;
  groupByColumn:
    | 'aisle'
    | 'level'
    | 'device_functional_type'
    | 'device_code'
    | 'reason_name';
  isAscendingOrder?: boolean;
  filters?: BaseFilterType;
}

export type FaultAvgDurationByStatusQueryServiceParams = {
  startDate: Date;
  endDate: Date;
  isAscendingOrder?: boolean;
  filters?: BaseFilterType;
};

export interface FaultCountGroupedQueryStoreResult {
  groupByColumn: string;
  count: number;
}

export interface FaultAvgDurationByStatusQueryStoreResult {
  status: string;
  avgDuration: number;
}

export interface FaultCountGroupedSeriesData {
  faultCounts: ChartSeriesData[];
}

export interface FaultAvgDurationSeriesData {
  avgDuration: ChartSeriesData[];
}
