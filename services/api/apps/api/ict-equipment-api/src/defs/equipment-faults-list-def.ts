import {
  ApiResponseArray,
  BaseFilterType,
  FilterObjectParameters,
  SortField,
} from 'ict-api-foundations';

export interface EquipmentFaultsListServiceParams {
  startDate: string;
  endDate: string;
  reqFilter?: BaseFilterType;
  sortFields?: SortField[];
  limit?: number;
  page?: number;
}

export interface EquipmentFaultsListStoreParams {
  startDate: string;
  endDate: string;
  filters?: BaseFilterType;
  filterParams?: FilterObjectParameters;
  sortFields: SortField[];
  limit: number;
  page: number;
}

// TODO: This type should be removed and the query should return EquipmentFaultsListItem[] instead.
// We need to use this type temporarily so the column names match what the UI sends for filters.
export interface EquipmentFaultsListQueryResponse {
  timestamp: string;
  durationMinutes: number;
  reason_name: string;
  aisle: string;
  level: string;
  device_code: string;
  device_functional_type: string;
}

export interface EquipmentFaultsListItem {
  timestamp: string;
  durationMinutes: number;
  status: string;
  aisle: string;
  level: string;
  device: string;
  deviceType: string;
}

export type PostEquipmentFaultsListResponse = ApiResponseArray<
  EquipmentFaultsListItem[],
  EquipmentFaultsPaginationInfo
>;

export interface EquipmentFaultsPaginationInfo {
  page: number;
  limit: number;
  totalResults?: number;
}
