import {BigQueryDate} from '@google-cloud/bigquery';

/** Expected response columns from the database query */
export interface EquipmentWorkstationOperatorActivityQueryResponse {
  /** Id of the operator */
  operator_id: string;
  /** Time the operator started working */
  start_time: BigQueryDate;
  /** Time the operator stopped working. If shift hasn't ended use null. */
  end_time: BigQueryDate | null;
  /** Time the operator was idle, in seconds */
  idle_time: number;
  /** Time the workstation was starved for picks, in seconds */
  starved_time: number;
  /** Time the workstation was blocked for picks, in seconds */
  blocked_time: number;
  /** The workstations line rates, per hour rate */
  line_rates: number;
  /** The workstations weighted line rates, per hour weighted rate */
  weighted_line_rates: number;
}

/** Expected return data object format for the response */
export interface EquipmentWorkstationOperatorActivityData {
  /** Id of the operator */
  operatorId: string;
  /** Time the operator started working at the workstation */
  startTime: string;
  /** Time the operator stopped working at the workstation, null if operator is still active */
  endTime: string | null;
  /** How long the operator was/currently has been at the workstation, in seconds */
  totalTimeDuration: number;
  /** Time the workstation was idle, in seconds */
  idleTime: number;
  /** Time the workstation was starved for picks, in seconds */
  starvedTime: number;
  /** Time the workstation was blocked for picks, in seconds */
  blockedTime: number;
  /** The workstations per hour line rates */
  lineRatePerHour: number;
  /** The workstations per hour weighted line rates */
  weightedLineRatePerHour: number;
}
