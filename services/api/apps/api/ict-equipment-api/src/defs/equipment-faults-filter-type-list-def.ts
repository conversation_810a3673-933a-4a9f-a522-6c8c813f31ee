import {BaseFilterType, FilterObjectParameters} from 'ict-api-foundations';

/**
 * Parameters to retrieve the available filter options for the given filterColumnName when the given filters are applied
 */
export type FaultsFilterTypeListStoreParams = {
  startDate?: Date;
  endDate?: Date;
  filters?: BaseFilterType;
  filterParams?: FilterObjectParameters;
  filterColumnName:
    | 'device_functional_type'
    | 'device_code'
    | 'reason_name'
    | 'aisle'
    | 'level';
};

export type FaultsFilterTypeListStoreResult = {
  faultFilter: string;
  totalResults: number;
};
