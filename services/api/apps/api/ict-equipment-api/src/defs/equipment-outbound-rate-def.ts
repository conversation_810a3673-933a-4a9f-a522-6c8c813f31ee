import {ApiResponse} from 'ict-api-foundations';

/** Expected response columns from the database query */
export interface EquipmentOutboundRateQueryResponse {
  outboundDivertsHour: number;
}

/** Data contract for the response from the api  */
export type EquipmentOutboundRateApiResponse = ApiResponse<
  EquipmentOutboundRate,
  EquipmentOutboundRateConfig
>;

export interface EquipmentOutboundRate {
  outboundDivertsHour: number;
  status: string;
}

export interface EquipmentOutboundRateConfig {
  max: number;
  rangeColor: string;
}
