import {FacilityArea, HealthStatus, AlertStatus} from 'ict-api-foundations';

/**
 * DTO that describes the equipment summary area data.
 */
export interface EquipmentSummaryAreaDefinition extends FacilityArea {
  status: HealthStatus;
  movementsPerFault: number;
  outboundRatePerHour: number;
  downTimeMinutes: number;
  qualityPercentage: number;
  alerts?: AlertStatus[];
}

/**
 * DTO that describes the areas summary data.
 */
export interface EquipmentSummaryAreaContract {
  areas: EquipmentSummaryAreaDefinition[];
}
