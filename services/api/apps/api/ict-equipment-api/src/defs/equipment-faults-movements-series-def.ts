import {BigQueryTimestamp} from '@google-cloud/bigquery';
import {
  BaseFilterType,
  SQLFilterField,
  SQLFilterFieldTree,
  ChartSeriesData,
  FilterObjectParameters,
} from 'ict-api-foundations';

export interface FaultsMovementsSeriesRequest {
  start_date: Date;
  end_date: Date;
  filters?: unknown;
}

export interface FaultsMovementsSeriesServiceParams {
  startDate?: Date;
  endDate?: Date;
  filters?: BaseFilterType;
}

export interface FaultsMovementsSeriesData {
  movementCounts: ChartSeriesData[];
  movementsPerFault: ChartSeriesData[];
}

export interface FaultsMovementsSeriesStoreParams {
  startDate?: Date;
  endDate?: Date;
  faultFilters?: SQLFilterField | SQLFilterFieldTree;
  movementFilters?: SQLFilterField | SQLFilterFieldTree;
  filterParams?: FilterObjectParameters;
}

export interface FaultsMovementsSeriesStoreResult {
  eventInterval: BigQueryTimestamp;
  movementCount: number;
  movementsPerFault: number;
}
