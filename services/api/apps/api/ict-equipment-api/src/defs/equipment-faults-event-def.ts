import {BigQueryTimestamp} from '@google-cloud/bigquery';

/** DTO for a Fault Event */
export interface FaultEvent {
  /** Time the event happened. UTC, ISO 8601. */
  time: string;
  /** Area that the fault occured in */
  area: string;
  /** Desciption of the fault event */
  description: string;
}

export interface FaultEventQueryResponse {
  _TimeUTC: BigQueryTimestamp;
  Area: string;
  FLT_DESC: string;
  Message: string;
}

export type FaultEventContract = FaultEvent[];
