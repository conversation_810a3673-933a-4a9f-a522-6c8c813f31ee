/** DTO that describes the equipment faults series data returned by the query. */
export interface EquipmentFaultsSeriesQueryData {
  /** Time the event happened, to the hour. */
  hourOfTheDay: string;
  /** Number of faults. */
  faults: number;
  /** Rate of picks. */
  throughput: number;
}

/** Describes the equipment faults series data returned by the service. */
export interface EquipmentFaultsSeriesData {
  /** Hour of the day that the throughput is measured. Eg: "16". */
  name: string;
  /** Number of faults for that hour. */
  value: number;
}

/** Describes the throughput rates series data returned by the service. */
export interface EquipmentThroughputSeriesData {
  /** Hour of the day that the throughput is measured. Eg: "16". */
  name: string;
  /** Throughput for that hour. */
  value: number;
}

/** Describes the contract for the data returned by the api. */
export interface EquipmentFaultsSeriesContractData {
  faultsOrderData: EquipmentFaultsSeriesData[];
  orderData: EquipmentThroughputSeriesData[];
}
