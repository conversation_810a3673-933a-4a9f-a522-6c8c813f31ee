import {BigQueryTimestamp} from '@google-cloud/bigquery';
import {
  BaseFilterType,
  FilterObjectParameters,
  SortField,
} from 'ict-api-foundations';

/** DTO for a Fault Event */
export interface RecentEvent {
  /** Time the event happened. UTC, ISO 8601. */
  time: string;
  tenant: string;
  facility: string;
  description: string;
  eventType: string;
  equipmentId: string;
  eventStartTime: string;
  totalTime: number;
  faultCode: string;
}

export interface RecentEventQueryResponse {
  time: BigQueryTimestamp;
  tenant: string;
  facility: string;
  description: string;
  eventType: string;
  equipmentId: string;
  eventStartTime: BigQueryTimestamp;
  totalTime: number;
  faultCode: string;
}

export interface RecentEventQueryConfig {
  page: number;
  limit: number;
  totalResults: number;
}

export interface RecentEventQueryServiceParams {
  startDate: Date;
  endDate: Date;
  limit: number;
  page: number;
  sortField: SortField[];
  reqFilter?: BaseFilterType;
  activeFault?: string;
}

export interface RecentEventQueryStoreParams {
  startDate: Date;
  endDate: Date;
  limit: number;
  page: number;
  sortField: SortField[];
  filters?: BaseFilterType;
  filterParams?: FilterObjectParameters;
}
