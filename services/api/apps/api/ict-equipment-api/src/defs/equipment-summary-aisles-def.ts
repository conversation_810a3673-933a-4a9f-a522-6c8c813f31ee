import {HealthStatus} from 'ict-api-foundations';

export interface EquipmentSummaryAisleDefinition {
  status: HealthStatus;
  totalMovements: number;
  storageUtilization: number;
  storedTotesPerHour: number;
  inventoryTotes: number;
  retrievedTotesPerHour: number;
  orderTotes: number;
}

export interface EquipmentSummaryAisleConfigDefinition {
  metrics: AisleMetricConfiguration[];
}

export interface AisleMetricConfiguration {
  id: string;
  label: string;
  type: string;
  format: string;
  display: boolean;
}
