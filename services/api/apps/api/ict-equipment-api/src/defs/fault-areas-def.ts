import {FacilityArea} from 'ict-api-foundations';

export interface FaultsArea {
  totalFaults: number;
  maxFaultsAllowed: number;
  downtimeMinutes: number;
  status: string;
}

/**
 * DTO that describes the fault areas data.
 */
export interface FaultsFacilityArea extends FacilityArea {
  faults: FaultsArea;
}

/**
 * DTO that describes the fault areas config data.
 */
export interface FaultsAreasConfig {
  min: number;
  max: number;
}

/** Type that describes the return structure of the data contract response. */
export interface FaultsAreasResponse {
  areas: FaultsFacilityArea[];
}
