import {HealthStatus} from 'ict-api-foundations';

export interface EquipmentSummaryWorkstationDefinition {
  status: HealthStatus;
  operatorId: number;
  iatDonorTotes: number;
  linesPerHour: number;
  activeTime: string;
  idleTime: string;
  starvedTime: string;
}

export interface EquipmentSummaryWorkstationConfigDefinition {
  metrics: WorkstationMetricConfiguration[];
}

export interface WorkstationMetricConfiguration {
  id: string;
  label: string;
  type: string;
  format: string;
  display: boolean;
}
