/**
 * DTO that describes the equipment faults data.
 */
export interface EquipmentFaultsDefinition {
  faults: number;
  status: string;
}

/**
 * DTO that describes the equipment faults query data.
 */
export interface EquipmentFaultsQueryDefinition {
  faults: number;
}

/**
 * DTO that describes the equipment faults config data.
 */
export interface EquipmentFaultsConfigDefinition {
  lowRange?: number;
  highRange?: number;
  max: number;
}
