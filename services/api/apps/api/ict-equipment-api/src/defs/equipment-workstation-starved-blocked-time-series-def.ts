import {BigQueryDate} from '@google-cloud/bigquery';
import {
  ApiResponse,
  BaseChartConfig,
  ChartSeriesData,
  SeriesConfig,
} from 'ict-api-foundations';

export interface EquipmentWorkstationStarvedBlockedTimeQueryResponse {
  /** Datetime of the recorded values. */
  date_time: BigQueryDate;
  /** Percentage of time the workstation was starved. */
  starved_time_percentage: number;
  /** Percentage of time the workstation was blocked. */
  blocked_time_percentage: number;
}

export type EquipmentWorkstationStarvedBlockedTimeSeriesData = {
  starvedTime: ChartSeriesData[];
  blockedTime: ChartSeriesData[];
};

export type EquipmentWorkstationStarvedBlockedTimeSeriesConfig =
  BaseChartConfig & {
    starvedTime: SeriesConfig;
    blockedTime: SeriesConfig;
  };

export type EquipmentWorkstationStarvedBlockedTimeSeriesApiResponse =
  ApiResponse<
    EquipmentWorkstationStarvedBlockedTimeSeriesData,
    EquipmentWorkstationStarvedBlockedTimeSeriesConfig
  >;
