import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {EquipmentService} from '../../services/equipment-service.ts';
import {EquipmentFaultsMovementsSeriesController} from '../../controllers/equipment-faults-movements-series-controller.ts';
import {FaultsMovementsSeriesRequest} from '../../defs/equipment-faults-movements-series-def.ts';

describe('EquipmentFaultsMovementsSeriesController', () => {
  const url: string = '/equipment/faults/movements/series';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Equipment Service. */
  let equipmentServiceStub: sinon.SinonStubbedInstance<EquipmentService>;

  const start_date = '2023-01-01T00:00:00.000Z';
  const end_date = '2023-01-02T00:00:00.000Z';
  const exampleRequest: FaultsMovementsSeriesRequest = {
    start_date: new Date(start_date),
    end_date: new Date(end_date),
  };

  afterEach(() => {
    sinon.restore();
  });

  describe('Equipment Faults Movements Series Controller Error scenarios', () => {
    // TODO: remove skip when start and end date become required parameters
    it.skip('should validate that start_date and end_date params are passed', async () => {
      const response = await request(app).post(url);

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      console.log(response.body);
      expect(response.body.details['params.start_date'].message).to.equal(
        "'start_date' is required"
      );
      expect(response.body.details['params.end_date'].message).to.equal(
        "'end_date' is required"
      );
    });

    it('should validate that start_date and end_date params are valid ISO8601 dates', async () => {
      const response = await request(app).post(url).send({
        start_date: '20-01-01',
        end_date: '01/01/2020 00:00:00',
      });

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details['params.start_date'].message).to.equal(
        'invalid ISO 8601 datetime format, i.e. YYYY-MM-DDTHH:mm:ss'
      );
      expect(response.body.details['params.end_date'].message).to.equal(
        'invalid ISO 8601 datetime format, i.e. YYYY-MM-DDTHH:mm:ss'
      );
    });

    it('should validate that start_date comes before end_date', async () => {
      const response = await request(app).post(url).send({
        start_date: '2023-01-02T00:00:00Z',
        end_date: '2023-01-01T00:00:00Z',
      });

      expect(response.status).to.equal(400);
      expect(response.body.status).to.equal(400);
      expect(response.body.title).to.equal('Bad Request');
    });

    it('should error when the service throws an error', async () => {
      equipmentServiceStub = sinon.createStubInstance(EquipmentService);
      Container.set(EquipmentService, equipmentServiceStub);
      equipmentServiceStub.getEquipmentFaultsMovementsSeries.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app).post(url).send(exampleRequest);

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWithMatch(
        equipmentServiceStub.getEquipmentFaultsMovementsSeries,
        {
          startDate: exampleRequest.start_date,
          endDate: exampleRequest.end_date,
        }
      );
    });
  });

  describe('Equipment Faults Movements Series Controller success scenarios', () => {
    beforeEach(() => {
      equipmentServiceStub = sinon.createStubInstance(EquipmentService);
      Container.set(EquipmentService, equipmentServiceStub);
      equipmentServiceStub.getEquipmentFaultsMovementsSeries.resolves(
        EquipmentFaultsMovementsSeriesController.exampleData
      );
    });

    it('should call "EquipmentService.getEquipmentFaultsMovementsSeries" and return the data', async () => {
      const response = await request(app).post(url).send(exampleRequest);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        EquipmentFaultsMovementsSeriesController.exampleResponse
      );

      sinon.assert.calledWithMatch(
        equipmentServiceStub.getEquipmentFaultsMovementsSeries,
        {
          startDate: exampleRequest.start_date,
          endDate: exampleRequest.end_date,
        }
      );
    });
  });
});
