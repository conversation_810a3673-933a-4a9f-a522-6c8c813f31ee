import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
import {EquipmentService} from '../../services/equipment-service.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {EquipmentSummaryAislesController} from '../../controllers/equipment-summary-aisles-controller.ts';

describe('EquipmentSummaryAislesController', () => {
  const url: string = '/equipment/summary/aisles/1';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Equipment Service. */
  let equipmentServiceStub: sinon.SinonStubbedInstance<EquipmentService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Equipment Summary Aisles Controller Error scenarios', () => {
    it('should validate that start_date and end_date params are passed', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details.start_date.message).to.equal(
        "'start_date' is required"
      );
      expect(response.body.details.end_date.message).to.equal(
        "'end_date' is required"
      );
    });

    it('should validate that start_date and end_date params are valid ISO8601 dates', async () => {
      const response = await request(app).get(url).query({
        start_date: '20-01-01',
        end_date: '01/01/2020 00:00:00',
      });

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details.start_date.message).to.equal(
        'invalid ISO 8601 datetime format, i.e. YYYY-MM-DDTHH:mm:ss'
      );
      expect(response.body.details.end_date.message).to.equal(
        'invalid ISO 8601 datetime format, i.e. YYYY-MM-DDTHH:mm:ss'
      );
    });

    it('should validate that start_date comes before end_date', async () => {
      const response = await request(app).get(url).query({
        start_date: '2023-01-02T00:00:00',
        end_date: '2023-01-01T00:00:00',
      });

      expect(response.status).to.equal(400);
      expect(response.body.status).to.equal(400);
      expect(response.body.title).to.equal('Bad Request');
    });

    it('should error when the service throws an error', async () => {
      equipmentServiceStub = sinon.createStubInstance(EquipmentService);
      Container.set(EquipmentService, equipmentServiceStub);
      equipmentServiceStub.getAisleSummary.rejects(
        new Error('Something bad happened')
      );

      const start_date = '2023-01-01T00:00:00.000Z';
      const end_date = '2023-01-02T00:00:00.000Z';
      const aisleId = '1';

      const response = await request(app).get(url).query({
        start_date,
        end_date,
        aisleId,
      });

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWith(
        equipmentServiceStub.getAisleSummary,
        aisleId,
        start_date,
        end_date
      );
    });
  });
  describe('Equipment Summary Aisles Controller success scenarios', () => {
    beforeEach(() => {
      equipmentServiceStub = sinon.createStubInstance(EquipmentService);
      Container.set(EquipmentService, equipmentServiceStub);
      equipmentServiceStub.getAisleSummary.resolves(
        EquipmentSummaryAislesController.exampleData
      );
    });

    it('should call "EquipmentService.getSummaryAisles" and return the data', async () => {
      const start_date = '2023-01-01T00:00:00.000Z';
      const end_date = '2023-01-02T00:00:00.000Z';
      const aisleId = '1';
      const response = await request(app).get(url).query({
        start_date,
        end_date,
        aisleId,
      });

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        EquipmentSummaryAislesController.exampleResponse
      );

      sinon.assert.calledWith(
        equipmentServiceStub.getAisleSummary,
        aisleId,
        start_date,
        end_date
      );
    });
  });
});
