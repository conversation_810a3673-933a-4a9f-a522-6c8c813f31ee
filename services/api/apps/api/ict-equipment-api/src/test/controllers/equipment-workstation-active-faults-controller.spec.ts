import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
import {EquipmentService} from '../../services/equipment-service.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {EquipmentWorkstationActiveFaultsController} from '../../controllers/equipment-workstation-active-faults-controller.ts';

describe('EquipmentWorkstationActiveFaultsController', () => {
  const workstationId: string = 'WS1';
  const url: string = `/equipment/workstation/${workstationId}/aisle/active-faults`;

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Equipment Service. */
  let equipmentServiceStub: sinon.SinonStubbedInstance<EquipmentService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Equipment Workstation Active Faults Controller Error scenarios', () => {
    it('should error when the service throws an error', async () => {
      equipmentServiceStub = sinon.createStubInstance(EquipmentService);
      Container.set(EquipmentService, equipmentServiceStub);
      equipmentServiceStub.getEquipmentWorkstationActiveFaults.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app).get(url);

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWith(
        equipmentServiceStub.getEquipmentWorkstationActiveFaults,
        workstationId
      );
    });
  });

  describe('Equipment Workstation Active Faults Controller success scenarios', () => {
    beforeEach(() => {
      equipmentServiceStub = sinon.createStubInstance(EquipmentService);
      Container.set(EquipmentService, equipmentServiceStub);
      equipmentServiceStub.getEquipmentWorkstationActiveFaults.resolves(
        EquipmentWorkstationActiveFaultsController.exampleData
      );
    });

    it('should call "EquipmentService.getWorkstationActiveFaultsEvents" and return the data', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        EquipmentWorkstationActiveFaultsController.exampleResponse
      );

      sinon.assert.calledWith(
        equipmentServiceStub.getEquipmentWorkstationActiveFaults,
        workstationId
      );
    });
  });
});
