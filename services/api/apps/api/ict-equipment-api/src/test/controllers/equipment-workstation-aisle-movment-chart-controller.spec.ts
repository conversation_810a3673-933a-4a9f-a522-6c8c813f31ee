import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
import {EquipmentService} from '../../services/equipment-service.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {EquipmentWorkstationAisleMovementChartController} from '../../controllers/equipment-workstation-aisle-movement-chart-controller.ts';

describe('EquipmentWorkstationAisleMovementChartController', () => {
  const workstationId = 'WS1';
  const url: string = `/equipment/workstation/${workstationId}/movements/detail`;

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Equipment Service. */
  let equipmentServiceStub: sinon.SinonStubbedInstance<EquipmentService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Equipment Workstation Aisle Movement Chart Controller Error scenarios', () => {
    it('should error when the service throws an error', async () => {
      equipmentServiceStub = sinon.createStubInstance(EquipmentService);
      Container.set(EquipmentService, equipmentServiceStub);
      equipmentServiceStub.getEquipmentWorkstationAisleMovementData.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app).get(url);
      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWith(
        equipmentServiceStub.getEquipmentWorkstationAisleMovementData,
        workstationId
      );
    });
  });

  describe('Equipment Workstation Aisle Movement Chart Controller success scenarios', () => {
    beforeEach(() => {
      equipmentServiceStub = sinon.createStubInstance(EquipmentService);
      Container.set(EquipmentService, equipmentServiceStub);
      equipmentServiceStub.getEquipmentWorkstationAisleMovementData.resolves(
        EquipmentWorkstationAisleMovementChartController.exampleData
      );
    });

    it('should call "EquipmentService.getWorkstationAisleMovementChartEvents" and return the data', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        EquipmentWorkstationAisleMovementChartController.exampleResponse
      );

      sinon.assert.calledWith(
        equipmentServiceStub.getEquipmentWorkstationAisleMovementData,
        workstationId
      );
    });
  });
});
