import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
import {EquipmentService} from '../../services/equipment-service.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {EquipmentWorkstationLineRatesSeriesController} from '../../controllers/equipment-workstation-line-rates-series-controller.ts';

describe('EquipmentWorkstationLineRatesSeriesController', () => {
  const workstationId: string = 'WS1';
  const url: string = `/equipment/workstation/${workstationId}/line-rates/series`;

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Equipment Service. */
  let equipmentServiceStub: sinon.SinonStubbedInstance<EquipmentService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Equipment Workstation Line Rates Series Controller Error scenarios', () => {
    it('should error when the service throws an error', async () => {
      equipmentServiceStub = sinon.createStubInstance(EquipmentService);
      Container.set(EquipmentService, equipmentServiceStub);
      equipmentServiceStub.getEquipmentWorkstationLineRatesSeries.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app).get(url);
      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWith(
        equipmentServiceStub.getEquipmentWorkstationLineRatesSeries,
        workstationId
      );
    });
  });

  describe('Equipment Workstation Line Rates Series Controller success scenarios', () => {
    beforeEach(() => {
      equipmentServiceStub = sinon.createStubInstance(EquipmentService);
      Container.set(EquipmentService, equipmentServiceStub);
      equipmentServiceStub.getEquipmentWorkstationLineRatesSeries.resolves(
        EquipmentWorkstationLineRatesSeriesController.exampleData
      );
    });

    it('should call "EquipmentService.getWorkstationLineRatesSeriesEvents" and return the data', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        EquipmentWorkstationLineRatesSeriesController.exampleResponse
      );

      sinon.assert.calledWith(
        equipmentServiceStub.getEquipmentWorkstationLineRatesSeries,
        workstationId
      );
    });
  });
});
