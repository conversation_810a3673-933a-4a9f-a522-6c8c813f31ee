import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
import {EquipmentService} from '../../services/equipment-service.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {EquipmentRecentEventsController} from '../../controllers/equipment-recent-events-controller.ts';

describe('EquipmentRecentEventsController', () => {
  const url: string = '/equipment/events/list';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Equipment Service. */
  let equipmentServiceStub: sinon.SinonStubbedInstance<EquipmentService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Equipment Recent Events Controller Error scenarios', () => {
    it('should validate that start_date and end_date params are passed', async () => {
      const response = await request(app).post(url).send({});

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(
        response.body.details['paginatedRequest.start_date'].message
      ).to.equal("'start_date' is required");
      expect(
        response.body.details['paginatedRequest.end_date'].message
      ).to.equal("'end_date' is required");
    });

    it('should validate that start_date and end_date params are valid ISO8601 dates', async () => {
      const mockRequest = {
        start_date: '20-01-01',
        end_date: '01/01/2020 00:00:00',
      };

      const response = await request(app).post(url).send(mockRequest);

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(
        response.body.details['paginatedRequest.start_date'].message
      ).to.equal('invalid ISO 8601 datetime format, i.e. YYYY-MM-DDTHH:mm:ss');
      expect(
        response.body.details['paginatedRequest.end_date'].message
      ).to.equal('invalid ISO 8601 datetime format, i.e. YYYY-MM-DDTHH:mm:ss');
    });

    it('should validate that start_date comes before end_date', async () => {
      const mockRequest = {
        start_date: '2023-01-02T00:00:00.000Z',
        end_date: '2023-01-01T00:00:00.000Z',
      };

      const response = await request(app).post(url).send(mockRequest);

      expect(response.status).to.equal(400);
      expect(response.body.status).to.equal(400);
      expect(response.body.title).to.equal('Bad Request');
    });

    it('should error when the service throws an error', async () => {
      equipmentServiceStub = sinon.createStubInstance(EquipmentService);
      Container.set(EquipmentService, equipmentServiceStub);
      equipmentServiceStub.getRecentEvents.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app)
        .post(url)
        .send(EquipmentRecentEventsController.exampleMinRequest);

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      // TODO: Figure out how to fuzzy match on the moment variables.
      sinon.assert.calledWithMatch(equipmentServiceStub.getRecentEvents, {
        limit: 100,
        page: 1,
        sortField: [],
        reqFilter: undefined,
      });
    });
  });
  describe('Equipment Recent Events Controller success scenarios', () => {
    beforeEach(() => {
      equipmentServiceStub = sinon.createStubInstance(EquipmentService);
      Container.set(EquipmentService, equipmentServiceStub);
      equipmentServiceStub.getRecentEvents.resolves(
        EquipmentRecentEventsController.exampleResponse
      );
    });

    it('should call "EquipmentService.getRecentEvents" and return the data', async () => {
      const response = await request(app)
        .post(url)
        .send(EquipmentRecentEventsController.exampleMinRequest);

      console.log(response.status);
      console.log(response.body);
      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        EquipmentRecentEventsController.exampleResponse
      );

      // TODO: Figure out how to fuzzy match on the moment variables.
      sinon.assert.calledWithMatch(equipmentServiceStub.getRecentEvents, {
        limit: 100,
        page: 1,
        sortField: [],
        reqFilter: undefined,
      });
    });
  });
});
