import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
import {EquipmentService} from '../../services/equipment-service.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {EquipmentWorkstationStarvedBlockedSeriesController} from '../../controllers/equipment-workstation-starved-blocked-series-controller.ts';

describe('EquipmentWorkstationStarvedBlockedSeriesController', () => {
  const workstationId = 'WS1';
  const url: string = `/equipment/workstation/${workstationId}/starved-blocked-time/series`;

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Equipment Service. */
  let equipmentServiceStub: sinon.SinonStubbedInstance<EquipmentService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Equipment Workstation Starved Blocked Series Controller Error scenarios', () => {
    it('should error when the service throws an error', async () => {
      equipmentServiceStub = sinon.createStubInstance(EquipmentService);
      Container.set(EquipmentService, equipmentServiceStub);
      equipmentServiceStub.getEquipmentWorkstationStarvedBlockedSeries.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app).get(url);
      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWith(
        equipmentServiceStub.getEquipmentWorkstationStarvedBlockedSeries,
        workstationId
      );
    });
  });

  describe('Equipment Workstation Starved Blocked Series Controller success scenarios', () => {
    beforeEach(() => {
      equipmentServiceStub = sinon.createStubInstance(EquipmentService);
      Container.set(EquipmentService, equipmentServiceStub);
      equipmentServiceStub.getEquipmentWorkstationStarvedBlockedSeries.resolves(
        EquipmentWorkstationStarvedBlockedSeriesController.exampleData
      );
    });

    it('should call "EquipmentService.getEquipmentWorkstationStarvedBlockedSeries" and return the data', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        EquipmentWorkstationStarvedBlockedSeriesController.exampleResponse
      );

      sinon.assert.calledWith(
        equipmentServiceStub.getEquipmentWorkstationStarvedBlockedSeries,
        workstationId
      );
    });
  });
});
