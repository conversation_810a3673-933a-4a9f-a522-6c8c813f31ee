import sinon from 'sinon';
import request from 'supertest';
import {Container, IctError, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {EquipmentService} from '../../services/equipment-service.ts';
import {EquipmentFaultsListController} from '../../controllers/equipment-faults-list-controller.ts';

describe('EquipmentFaultsListController', () => {
  const url: string = '/equipment/faults/list';

  const app = appSetup(RegisterRoutes);

  const start_date = '2023-01-01T00:00:00.000Z';
  const end_date = '2023-01-02T00:00:00.000Z';
  const exampleRequest = {
    start_date,
    end_date,
    page: EquipmentFaultsListController.exampleConfig.page,
    limit: EquipmentFaultsListController.exampleConfig.limit,
  };

  /** Stub instance for the Equipment Service. */
  let equipmentServiceStub: sinon.SinonStubbedInstance<EquipmentService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Equipment Faults List Controller Error scenarios', () => {
    it('should validate that start_date and end_date params are passed', async () => {
      const response = await request(app).post(url);

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      console.log(response.body);
      expect(
        response.body.details['paginatedRequest.start_date'].message
      ).to.equal("'start_date' is required");
      expect(
        response.body.details['paginatedRequest.end_date'].message
      ).to.equal("'end_date' is required");
    });

    it('should validate that start_date and end_date params are valid ISO8601 dates', async () => {
      const response = await request(app).post(url).send({
        start_date: '20-01-01',
        end_date: '01/01/2020 00:00:00',
      });

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(
        response.body.details['paginatedRequest.start_date'].message
      ).to.equal('invalid ISO 8601 datetime format, i.e. YYYY-MM-DDTHH:mm:ss');
      expect(
        response.body.details['paginatedRequest.end_date'].message
      ).to.equal('invalid ISO 8601 datetime format, i.e. YYYY-MM-DDTHH:mm:ss');
    });

    it('should validate that start_date comes before end_date', async () => {
      const response = await request(app).post(url).send({
        start_date: '2023-01-02T00:00:00Z',
        end_date: '2023-01-01T00:00:00Z',
      });

      expect(response.status).to.equal(400);
      expect(response.body.status).to.equal(400);
      expect(response.body.title).to.equal('Bad Request');
    });

    it('should validate that page and limit params are numbers', async () => {
      const response = await request(app).post(url).send({
        start_date,
        end_date,
        page: 'one',
        limit: 'two',
      });

      expect(response.status).to.equal(422);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details['paginatedRequest.page'].message).to.equal(
        'invalid integer number'
      );
      expect(response.body.details['paginatedRequest.limit'].message).to.equal(
        'invalid integer number'
      );
    });

    it('should validate minimum values for page and limit params', async () => {
      const response = await request(app).post(url).send({
        start_date,
        end_date,
        page: -1,
        limit: 0,
      });

      expect(response.status).to.equal(422);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details['paginatedRequest.page'].message).to.equal(
        'min 0'
      );
      expect(response.body.details['paginatedRequest.limit'].message).to.equal(
        'min 1'
      );
    });

    it('should validate maximum value for limit param', async () => {
      const response = await request(app).post(url).send({
        start_date,
        end_date,
        page: 1,
        limit: 1001,
      });

      expect(response.status).to.equal(422);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details['paginatedRequest.limit'].message).to.equal(
        'max 1000'
      );
    });

    it('should error when the service throws an error', async () => {
      equipmentServiceStub = sinon.createStubInstance(EquipmentService);
      Container.set(EquipmentService, equipmentServiceStub);
      equipmentServiceStub.getEquipmentFaultsList.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app).post(url).send(exampleRequest);

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWithMatch(
        equipmentServiceStub.getEquipmentFaultsList,
        {
          page: EquipmentFaultsListController.exampleConfig.page,
          limit: EquipmentFaultsListController.exampleConfig.limit,
        }
      );
    });
  });

  describe('Equipment Faults List Controller success scenarios', () => {
    beforeEach(() => {
      equipmentServiceStub = sinon.createStubInstance(EquipmentService);
      Container.set(EquipmentService, equipmentServiceStub);
      equipmentServiceStub.getEquipmentFaultsList.reset();
    });

    it('should call "EquipmentService.getEquipmentFaultsList" and return the data', async () => {
      equipmentServiceStub.getEquipmentFaultsList.resolves({
        data: EquipmentFaultsListController.exampleData,
        metadata: EquipmentFaultsListController.exampleConfig,
      });
      const response = await request(app).post(url).send(exampleRequest);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        EquipmentFaultsListController.exampleResponse
      );

      sinon.assert.calledWithMatch(
        equipmentServiceStub.getEquipmentFaultsList,
        {
          page: EquipmentFaultsListController.exampleConfig.page,
          limit: EquipmentFaultsListController.exampleConfig.limit,
        }
      );
    });

    it('should throw a No Content IctError if no data was found', async () => {
      equipmentServiceStub.getEquipmentFaultsList.resolves({
        data: [],
        metadata: EquipmentFaultsListController.exampleConfig,
      });

      const response = await request(app).post(url).send(exampleRequest);
      expect(response.status).to.equal(IctError.noContent().statusCode);
    });
  });
});
