import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
import {EquipmentService} from '../../services/equipment-service.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {EquipmentFaultsRecentEventsController} from '../../controllers/equipment-faults-recent-events-controller.ts';

describe('EquipmentFaultsRecentEventsController', () => {
  const url: string = '/equipment/faults/events';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Equipment Service. */
  let equipmentServiceStub: sinon.SinonStubbedInstance<EquipmentService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Equipment Faults Recent Events Controller Error scenarios', () => {
    it('should error when the service throws an error', async () => {
      equipmentServiceStub = sinon.createStubInstance(EquipmentService);
      Container.set(EquipmentService, equipmentServiceStub);
      equipmentServiceStub.getFaultsRecentEvents.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app).get(url);

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledOnce(equipmentServiceStub.getFaultsRecentEvents);
    });
  });

  describe('Equipment Faults Recent Events Controller success scenarios', () => {
    beforeEach(() => {
      equipmentServiceStub = sinon.createStubInstance(EquipmentService);
      Container.set(EquipmentService, equipmentServiceStub);
      equipmentServiceStub.getFaultsRecentEvents.resolves(
        EquipmentFaultsRecentEventsController.exampleData
      );
    });

    it('should call "EquipmentService.getFaultsRecentEvents" and return the data', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        EquipmentFaultsRecentEventsController.exampleResponse
      );

      sinon.assert.calledOnce(equipmentServiceStub.getFaultsRecentEvents);
    });
  });
});
