import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
import {EquipmentService} from '../../services/equipment-service.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {EquipmentFaultsDeviceIdsListController} from '../../controllers/equipment-faults-devicesids-list-controller.ts';

describe('EquipmentFaultsDeviceIdsListController', () => {
  const url: string = '/equipment/faults/device-id/list';
  const exampleMinRequest = {
    start_date: '2024-01-01T00:00:00.000Z',
    end_date: '2024-01-02T00:00:00.000Z',
  };
  /** The app to be tested. */
  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Equipment Service. */
  let equipmentServiceStub: sinon.SinonStubbedInstance<EquipmentService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Equipment Faults deviceids list Controller Error scenarios', () => {
    it.skip('should validate that start_date and end_date params are passed', async () => {
      const response = await request(app).post(url).send({});

      expect(response.status).to.equal(200);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details['requestBody.start_date'].message).to.equal(
        "'start_date' is required"
      );
      expect(response.body.details['requestBody.end_date'].message).to.equal(
        "'end_date' is required"
      );
    });

    it('should validate that start_date and end_date params are valid ISO8601 dates', async () => {
      const mockRequest = {
        start_date: '20-01-01',
        end_date: '01/01/2020 00:00:00',
      };

      const response = await request(app).post(url).send(mockRequest);

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details['requestBody.start_date'].message).to.equal(
        'invalid ISO 8601 datetime format, i.e. YYYY-MM-DDTHH:mm:ss'
      );
      expect(response.body.details['requestBody.end_date'].message).to.equal(
        'invalid ISO 8601 datetime format, i.e. YYYY-MM-DDTHH:mm:ss'
      );
    });

    it('should validate that start_date comes before end_date', async () => {
      const mockRequest = {
        start_date: '2023-01-02T00:00:00.000Z',
        end_date: '2023-01-01T00:00:00.000Z',
      };

      const response = await request(app).post(url).send(mockRequest);

      expect(response.status).to.equal(400);
      expect(response.body.status).to.equal(400);
      expect(response.body.title).to.equal('Bad Request');
    });

    it('should error when the service throws an error', async () => {
      equipmentServiceStub = sinon.createStubInstance(EquipmentService);
      Container.set(EquipmentService, equipmentServiceStub);
      equipmentServiceStub.getEquiupmentFaultsDeviceIdsList.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app).post(url).send(exampleMinRequest);

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      // TODO: Figure out how to fuzzy match on the moment variables.
      sinon.assert.calledWithMatch(
        equipmentServiceStub.getEquiupmentFaultsDeviceIdsList,
        {
          startDate: new Date('2024-01-01T00:00:00.000Z'),
          endDate: new Date('2024-01-02T00:00:00.000Z'),
        }
      );
    });
  });

  describe('Equipment Faults deviceIds list Controller success scenarios', () => {
    beforeEach(() => {
      equipmentServiceStub = sinon.createStubInstance(EquipmentService);
      Container.set(EquipmentService, equipmentServiceStub);
      const mockResponse = ['equipment1', 'equipment2', 'equipment3'];
      equipmentServiceStub.getEquiupmentFaultsDeviceIdsList.resolves(
        mockResponse
      );
    });

    it('should call "EquipmentService.getEquiupmentFaultsDeviceIdsList" and return the data', async () => {
      const response = await request(app).post(url).send(exampleMinRequest);
      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        EquipmentFaultsDeviceIdsListController.exampleResponse
      );

      sinon.assert.calledOnce(
        equipmentServiceStub.getEquiupmentFaultsDeviceIdsList
      );
    });
  });
});
