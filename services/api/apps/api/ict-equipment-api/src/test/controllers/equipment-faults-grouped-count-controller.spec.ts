import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {FaultCountGroupedByRequest} from '../../defs/equipment-fault-counts-grouped-def.ts';
import {EquipmentService} from '../../services/equipment-service.ts';
import {EquipmentFaultsGroupedCountController} from '../../controllers/equipment-faults-grouped-count-controller.ts';

describe('EquipmentFaultsGroupedCountController', () => {
  const url: string = '/equipment/faults/grouped/count/series';

  const app = appSetup(RegisterRoutes);

  const start_date = '2023-01-01T00:00:00.000Z';
  const end_date = '2023-01-02T00:00:00.000Z';
  const exampleRequest: FaultCountGroupedByRequest = {
    startDate: new Date(start_date),
    endDate: new Date(end_date),
    groupByColumn: 'aisle',
  };

  /** Stub instance for the Inventory Service. */
  let equipmentServiceStub: sinon.SinonStubbedInstance<EquipmentService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Equipment Faults Grouped Count Controller Error scenarios', () => {
    it('should error when the service throws an error', async () => {
      equipmentServiceStub = sinon.createStubInstance(EquipmentService);
      Container.set(EquipmentService, equipmentServiceStub);
      equipmentServiceStub.getFaultsCountGroupedAsync.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app).post(url).send(exampleRequest);

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWithMatch(
        equipmentServiceStub.getFaultsCountGroupedAsync,
        {
          startDate: exampleRequest.startDate,
          endDate: exampleRequest.endDate,
          groupByColumn: exampleRequest.groupByColumn,
        }
      );
    });
  });
  describe('Equipment Faults Grouped Count Controller success scenarios', () => {
    beforeEach(() => {
      equipmentServiceStub = sinon.createStubInstance(EquipmentService);
      Container.set(EquipmentService, equipmentServiceStub);
      equipmentServiceStub.getFaultsCountGroupedAsync.resolves(
        EquipmentFaultsGroupedCountController.exampleResponse
      );
    });

    it('should call "EquipmentService.getFaultsCountGroupedAsync" and return the data', async () => {
      const response = await request(app).post(url).send(exampleRequest);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        EquipmentFaultsGroupedCountController.exampleData
      );

      sinon.assert.calledWithMatch(
        equipmentServiceStub.getFaultsCountGroupedAsync,
        {
          startDate: exampleRequest.startDate,
          endDate: exampleRequest.endDate,
          groupByColumn: exampleRequest.groupByColumn,
        }
      );
    });
  });
});
