import {
  BigQuery,
  BigQueryDate,
  BigQueryTimestamp,
} from '@google-cloud/bigquery';
import * as chai from 'chai';
import chaiAsPromised from 'chai-as-promised';
import {
  AppConfigSetting,
  BigQueryDatabase,
  ConfigStore,
  ContextService,
  DatabaseProvider,
  HealthStatus,
  IctError,
  PaginatedResults,
} from 'ict-api-foundations';
import sinon, {SinonStub} from 'sinon';
import {DefaultConfigSettings} from 'ict-api-schema';
import {EquipmentFaultsQueryDefinition} from '../../defs/equipment-faults-def.ts';
import {EquipmentFaultsDowntimeQueryResponse} from '../../defs/equipment-faults-downtime-def.ts';
import {FaultEventQueryResponse} from '../../defs/equipment-faults-event-def.ts';
import {EquipmentFaultsMovementsQueryResponse} from '../../defs/equipment-faults-movements-def.ts';
import {EquipmentFaultsSeriesQueryData} from '../../defs/equipment-faults-series-def.ts';
import {MovementQualityQueryResponse} from '../../defs/equipment-movement-quality-def.ts';
import {FaultsFacilityArea} from '../../defs/fault-areas-def.ts';
import {EquipmentStore} from '../../stores/equipment-store.ts';
import {EquipmentActiveFaultsDefinition} from '../../defs/equipment-faults-active-def.ts';
import {EquipmentWorkstationOperatorActivityQueryResponse} from '../../defs/equipment-workstation-operator-activity-def.ts';
import {EquipmentWorkstationLineRatesSeriesQueryResponse} from '../../defs/equipment-workstation-line-rates-series-def.ts';
import {EquipmentWorkstationStarvedBlockedTimeQueryResponse} from '../../defs/equipment-workstation-starved-blocked-time-series-def.ts';
import {EquipmentWorkstationAisleMovementDataQueryResponse} from '../../defs/equipment-workstation-aisle-movement-chart-def.ts';
import {EquipmentWorkstationActiveFaultsQueryResponse} from '../../defs/equipment-workstation-active-faults-def.ts';
import {
  FaultsFilterTypeListStoreParams,
  FaultsFilterTypeListStoreResult,
} from '../../defs/equipment-faults-filter-type-list-def.ts';
import {
  FaultsMovementsSeriesStoreParams,
  FaultsMovementsSeriesStoreResult,
} from '../../defs/equipment-faults-movements-series-def.ts';
import {
  FaultAvgDurationByStatusQueryStoreResult,
  FaultCountGroupedQueryStoreResult,
} from '../../defs/equipment-fault-counts-grouped-def.ts';
import {
  EquipmentFaultsListQueryResponse,
  EquipmentFaultsListStoreParams,
} from '../../defs/equipment-faults-list-def.ts';

const expect = chai.expect;
chai.use(chaiAsPromised);

describe('EquipmentStore', () => {
  let equipmentStore: EquipmentStore;
  let queryStub: sinon.SinonStubbedInstance<BigQuery>;
  let dbProvider: DatabaseProvider;
  let contextService: ContextService;
  let configStore: ConfigStore;
  let executeMonitoredJobStub: SinonStub;

  beforeEach(() => {
    // Create a BigQuery client
    queryStub = sinon.createStubInstance(BigQuery);
    dbProvider = new DatabaseProvider();
    const bqDatabase = new BigQueryDatabase(queryStub, 'testDataset');
    dbProvider.set(bqDatabase.getType(), bqDatabase);

    contextService = new ContextService();
    sinon.stub(contextService, 'dbProvider').get(() => dbProvider);
    executeMonitoredJobStub = sinon.stub(
      dbProvider.bigQuery,
      'executeMonitoredJob',
    );
    configStore = new ConfigStore(contextService);
    equipmentStore = new EquipmentStore(contextService, configStore);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getEquipmentFaultsData', () => {
    const startDate = '2022-01-10';
    const endDate = '2022-01-11';

    it('should retrieve equipment faults data from the database', async () => {
      // this should be what is returned by big query
      const expectedQueryResponse = [
        {
          faultCount: 165,
        },
      ];

      const expectedResponse: EquipmentFaultsQueryDefinition = {faults: 165};

      // Mock the query response
      executeMonitoredJobStub.resolves([expectedQueryResponse]);
      // Call the function
      const result = await equipmentStore.getFaultsCountAsync(
        startDate,
        endDate,
      );
      // Assertions
      expect(executeMonitoredJobStub.calledOnce).to.be.true;
      expect(result).to.deep.equal(expectedResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        equipmentStore.getFaultsCountAsync(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getFaultsAreasAsync', () => {
    const startDate = '2022-01-10';
    const endDate = '2022-01-11';

    it('should retrieve equipment faults data from the database', async () => {
      // this should be what is returned by BigQuery
      const expectedQueryResponse = [
        {
          id: 'testArea1',
          areaFaultCount: 20,
          activeOperators: 44,
          lowRecOperators: 32,
          highRecOperators: 34,
          areaStatus: 'ok',
          identifier: '',
          message: '',
          faultDurationMS: 0,
          areaName: 'Picking',
        },
        {
          id: 'testArea2',
          areaFaultCount: 8,
          activeOperators: 24,
          lowRecOperators: 19,
          highRecOperators: 37,
          areaStatus: 'caution',
          identifier: 'Workstation 2',
          message: 'Attention Required',
          faultDurationMS: 787156,
          areaName: 'Put Wall',
        },
        {
          id: 'testArea3',
          areaFaultCount: 0,
          activeOperators: 28,
          lowRecOperators: 19,
          highRecOperators: 35,
          areaStatus: 'critical',
          identifier: 'Workstation 4',
          message: 'Action Suggested - Reassign Operators',
          faultDurationMS: 583026,
          areaName: 'Packing',
        },
        {
          id: 'testArea4',
          areaFaultCount: 0,
          activeOperators: 30,
          lowRecOperators: 25,
          highRecOperators: 31,
          areaStatus: 'caution',
          identifier: 'Workstation 1',
          message: 'Assistance Requested',
          faultDurationMS: 1568518,
          areaName: 'Shipping',
        },
      ];

      const expectedResponse: FaultsFacilityArea[] = [
        {
          id: 'testArea1',
          name: 'Picking',
          alertStatus: {
            status: HealthStatus.Ok,
            identifier: '',
          },
          operators: {
            activeOperators: 44,
            lowRecOperators: 32,
            highRecOperators: 34,
          },
          faults: {
            totalFaults: 20,
            maxFaultsAllowed: 5,
            downtimeMinutes: 0,
            status: 'caution',
          },
        },
        {
          id: 'testArea2',
          name: 'Put Wall',
          alertStatus: {
            status: HealthStatus.Caution,
            identifier: 'Workstation 2',
          },
          operators: {
            activeOperators: 24,
            lowRecOperators: 19,
            highRecOperators: 37,
          },
          faults: {
            totalFaults: 8,
            maxFaultsAllowed: 5,
            downtimeMinutes: 13.119266666666666,
            status: 'caution',
          },
        },
        {
          id: 'testArea3',
          name: 'Packing',
          alertStatus: {
            status: HealthStatus.Critical,
            identifier: 'Workstation 4',
          },
          operators: {
            activeOperators: 28,
            lowRecOperators: 19,
            highRecOperators: 35,
          },
          faults: {
            totalFaults: 0,
            maxFaultsAllowed: 5,
            downtimeMinutes: 9.7171,
            status: 'caution',
          },
        },
        {
          id: 'testArea4',
          name: 'Shipping',
          alertStatus: {
            status: HealthStatus.Caution,
            identifier: 'Workstation 1',
          },
          operators: {
            activeOperators: 30,
            lowRecOperators: 25,
            highRecOperators: 31,
          },
          faults: {
            totalFaults: 0,
            maxFaultsAllowed: 5,
            downtimeMinutes: 26.141966666666665,
            status: 'caution',
          },
        },
      ];

      // Mock the query response
      executeMonitoredJobStub.resolves([expectedQueryResponse]);
      // Call the function
      const result = await equipmentStore.getFaultsAreasAsync(
        startDate,
        endDate,
      );
      // Assertions
      expect(executeMonitoredJobStub.calledOnce).to.be.true;
      expect(result).to.deep.equal(expectedResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        equipmentStore.getFaultsAreasAsync(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getEquipmentFaultsSeriesData', () => {
    let mockStoreResponse: EquipmentFaultsSeriesQueryData[];
    const startDate = '2023-04-10T01:26:54.813Z';
    const endDate = '2023-04-11T01:26:54.813Z';
    // Simulate the expected response from BigQuery
    const mockQueryResponse = [
      {
        hourOfTheDay: {value: '2023-04-10T10:00:00.000Z'},
        faults: 2,
        throughput: 224,
      },
      {
        hourOfTheDay: {value: '2023-04-10T11:00:00.000Z'},
        faults: 5,
        throughput: 106,
      },
    ];

    beforeEach(() => {
      // Simulate the expected response from the call to equipmentStore
      mockStoreResponse = [
        {
          hourOfTheDay: '2023-04-10T10:00:00.000Z',
          faults: 2,
          throughput: 224,
        },
        {
          hourOfTheDay: '2023-04-10T11:00:00.000Z',
          faults: 5,
          throughput: 106,
        },
      ];
    });

    it('should get equipment faults series data', async () => {
      executeMonitoredJobStub.resolves([mockQueryResponse]);
      const result = await equipmentStore.getFaultsToThroughputSeries(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockStoreResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        equipmentStore.getFaultsToThroughputSeries(startDate, endDate),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getFaultsCountGroupedAsync', () => {
    let mockResult: FaultCountGroupedQueryStoreResult[];
    let startDate: Date;
    let endDate: Date;

    beforeEach(() => {
      startDate = new Date('2022-01-10');
      endDate = new Date('2022-01-11');
      mockResult = [
        {
          groupByColumn: '01',
          count: 5,
        },
        {
          groupByColumn: '02',
          count: 8,
        },
        {
          groupByColumn: '03',
          count: 13,
        },
        {
          groupByColumn: '04',
          count: 20,
        },
        {
          groupByColumn: '05',
          count: 25,
        },
      ];
      executeMonitoredJobStub.resolves([mockResult]);
    });

    it('should get equipment faults grouped counts', async () => {
      const result = await equipmentStore.getFaultsCountGroupedAsync({
        startDate,
        endDate,
        groupByColumn: 'aisle',
      });

      expect(result).to.deep.equal(mockResult);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        equipmentStore.getFaultsCountGroupedAsync({
          startDate,
          endDate,
          groupByColumn: 'aisle',
        }),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getFaultsAvgDurationByStatusAsync', () => {
    let mockResult: FaultAvgDurationByStatusQueryStoreResult[];
    let startDate: Date;
    let endDate: Date;
    beforeEach(() => {
      startDate = new Date('2022-01-10');
      endDate = new Date('2022-01-11');
      mockResult = [
        {
          status: 'Reason 01',
          avgDuration: 532.2,
        },
        {
          status: 'Reason 02',
          avgDuration: 814.9,
        },
        {
          status: 'Reason 03',
          avgDuration: 134.4,
        },
        {
          status: 'Reason 04',
          avgDuration: 205.6,
        },
        {
          status: 'Reason 05',
          avgDuration: 253.8,
        },
      ];
      executeMonitoredJobStub.resolves([mockResult]);
    });

    it('should get average duration for equipment faults grouped by status', async () => {
      const result = await equipmentStore.getFaultsAvgDurationByStatusAsync({
        startDate,
        endDate,
      });

      expect(result).to.deep.equal(mockResult);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        equipmentStore.getFaultsAvgDurationByStatusAsync({
          startDate,
          endDate,
        }),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getFaultsRecentEvents', async () => {
    const fakeQueryResponse = [
      {
        _TimeUTC: new BigQueryTimestamp('2021-10-23'),
        Area: 'Test Area',
        FLT_DESC: 'Fault Description',
        Message: 'Fault Message',
      },
    ];

    it('should retrieve recent equipment fault events from the database', async () => {
      executeMonitoredJobStub.resolves([fakeQueryResponse]);

      const result = await equipmentStore.getFaultsRecentEvents();
      const expected: FaultEventQueryResponse[] = [
        {
          _TimeUTC: new BigQueryTimestamp('2021-10-23'),
          Area: 'Test Area',
          FLT_DESC: 'Fault Description',
          Message: 'Fault Message',
        },
      ];
      expect(executeMonitoredJobStub.callCount).equal(1);
      expect(result).deep.equal(expected);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        equipmentStore.getFaultsRecentEvents(),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getFaultAndEquipmentMovements', () => {
    let mockQueryResponse: EquipmentFaultsMovementsQueryResponse[];
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      mockQueryResponse = [
        {
          movementCount: 325,
          faultCount: 2,
          area: 'PACKING',
          eventTime: '2022-01-10 10:30:00+00',
        },
        {
          movementCount: 132,
          faultCount: 0,
          area: 'PICKING',
          eventTime: '2022-01-10 10:30:00+00',
        },
        {
          movementCount: 427,
          faultCount: 5,
          area: 'SHIPPING',
          eventTime: '2022-01-10 10:30:00+00',
        },
        {
          movementCount: 378,
          faultCount: 1,
          area: 'STORAGE',
          eventTime: '2022-01-10 10:30:00+00',
        },
      ];
      startDate = '2022-01-10 10:30:00+00';
      endDate = '2022-01-10 10:45:00+00';
    });

    it('should get equipment movement and faults data', async () => {
      executeMonitoredJobStub.resolves([mockQueryResponse]);
      const result = await equipmentStore.getFaultAndEquipmentMovementsAsync(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockQueryResponse);
    });
  });

  describe('getTotalDowntime', () => {
    let mockQueryResponse: EquipmentFaultsDowntimeQueryResponse[];
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2022-01-10 10:30:00+00';
      endDate = '2022-01-10 10:45:00+00';

      mockQueryResponse = [
        {
          downtimeMS: 444096,
          area: 'PACKING',
        },
        {
          downtimeMS: 2085732,
          area: 'PICKING',
        },
        {
          downtimeMS: 314006,
          area: 'PUT WALL',
        },
        {
          downtimeMS: 94866,
          area: 'SHIPPING',
        },
      ];
    });

    it('should get total downtime data grouped by area', async () => {
      executeMonitoredJobStub.resolves([mockQueryResponse]);
      const result = await equipmentStore.getTotalDowntimes(startDate, endDate);

      expect(result).to.deep.equal(mockQueryResponse);
    });
  });

  describe('getMovementQuality', () => {
    let mockQueryResponse: MovementQualityQueryResponse[];
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2022-01-10 10:30:00+00';
      endDate = '2022-01-10 10:45:00+00';

      mockQueryResponse = [
        {
          movementQualityPercentage: 70,
          area: 'PACKING',
        },
        {
          movementQualityPercentage: 50,
          area: 'SHIPPING',
        },
        {
          movementQualityPercentage: 25,
          area: 'STORAGE',
        },
        {
          movementQualityPercentage: 10,
          area: 'PICKING',
        },
      ];
    });

    it('should get movement quality data grouped by area', async () => {
      executeMonitoredJobStub.resolves([mockQueryResponse]);
      const result = await equipmentStore.getMovementQuality(
        startDate,
        endDate,
      );

      expect(result).to.deep.equal(mockQueryResponse);
    });
  });

  describe('getEquipmentFlowRates', async () => {
    const startDate = '2022-01-10 10:30:00+00';
    const endDate = '2022-01-10 10:45:00+00';
    const fakeQueryResponse = [
      {
        avgHourlyFlowRate: 892.3,
        area: 'PACKING',
        rangeOfHours: 10,
      },
      {
        avgHourlyFlowRate: 131.3,
        area: 'SHIPPING',
        rangeOfHours: '10',
      },
      {
        avgHourlyFlowRate: 1421.8,
        area: 'PICKING',
        rangeOfHours: 10,
      },
      {
        avgHourlyFlowRate: 361.9,
        area: 'STORAGE',
        rangeOfHours: 10,
      },
    ];

    it('should retrieve avg hourly flowrates by area from the database', async () => {
      executeMonitoredJobStub.resolves([fakeQueryResponse]);

      const result = await equipmentStore.getEquipmentFlowRateAverage(
        startDate,
        endDate,
      );
      expect(executeMonitoredJobStub.callCount).equal(1);
      expect(result).deep.equal(fakeQueryResponse);
    });
  });

  describe('getEquipmentActiveFaultsData', async () => {
    it('should retrieve active equipment faults from the database', async () => {
      // This is what is returned from BigQuery
      const fakeQueryResponse = [
        {
          fault_duration_seconds: 11,
          reason_name: 'broken pipe',
          area: 'Hyrule Subsection 1',
        },
        {
          fault_duration_seconds: 12,
          reason_name: 'broken wrench',
          area: 'Hyrule Subsection 2',
        },
        {
          fault_duration_seconds: 13,
          reason_name: 'broken axe',
          area: 'Hyrule Subsection 3',
        },
      ];

      const expected: EquipmentActiveFaultsDefinition[] = [
        {
          area: 'Hyrule Subsection 1',
          deviceReason: 'broken pipe',
          duration: 11,
        },
        {
          area: 'Hyrule Subsection 2',
          deviceReason: 'broken wrench',
          duration: 12,
        },
        {
          area: 'Hyrule Subsection 3',
          deviceReason: 'broken axe',
          duration: 13,
        },
      ];

      // Mock query response
      executeMonitoredJobStub.resolves([fakeQueryResponse]);
      // Call function
      const result = await equipmentStore.getEquipmentActiveFaultsData();
      // Assertions
      expect(executeMonitoredJobStub.calledOnce).to.be.true;
      expect(result).to.deep.equal(expected);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        equipmentStore.getEquipmentActiveFaultsData(),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getWorkstationOperatorActivity', () => {
    let fakeQueryResponse: [
      EquipmentWorkstationOperatorActivityQueryResponse[],
    ];

    beforeEach(() => {
      fakeQueryResponse = [
        [
          {
            operator_id: '001531',
            start_time: new BigQueryDate('2023-04-22T07:46:00.000Z'),
            end_time: new BigQueryDate('2023-04-22T10:00:00.000Z'),
            idle_time: 900,
            starved_time: 900,
            blocked_time: 1500,
            line_rates: 75,
            weighted_line_rates: 100,
          },
          {
            operator_id: '001532',
            start_time: new BigQueryDate('2023-04-22T10:15:00.000Z'),
            end_time: null,
            idle_time: 900,
            starved_time: 900,
            blocked_time: 1500,
            line_rates: 75,
            weighted_line_rates: 100,
          },
        ],
      ];
      executeMonitoredJobStub.resolves(fakeQueryResponse);
    });

    // TODO Remove skip once query is implemented
    it.skip('should retrieve workstation operator activity data from the database', async () => {
      const result =
        await equipmentStore.getWorkstationOperatorActivity('WS01');
      expect(result).to.deep.equal(fakeQueryResponse[0]);
    });
  });

  describe('getEquipmentWorkstationLineRatesSeries', () => {
    let fakeQueryResponse: [EquipmentWorkstationLineRatesSeriesQueryResponse[]];

    beforeEach(() => {
      fakeQueryResponse = [
        [
          {
            date_time: new BigQueryDate('2022-01-10T10:00:00.000Z'),
            line_rate: 100,
          },
          {
            date_time: new BigQueryDate('2022-01-10T11:00:00.000Z'),
            line_rate: 114,
          },
        ],
      ];
      executeMonitoredJobStub.resolves(fakeQueryResponse);
    });

    // TODO Remove skip once query is implemented
    it.skip('should retrieve line rates series from the database', async () => {
      const result =
        await equipmentStore.getEquipmentWorkstationLineRatesSeries('WS01');
      expect(result).to.deep.equal(fakeQueryResponse);
    });
  });

  describe('getEquipmentWorkstationStarvedBlockedSeries', () => {
    let fakeQueryResponse: [
      EquipmentWorkstationStarvedBlockedTimeQueryResponse[],
    ];

    beforeEach(() => {
      fakeQueryResponse = [
        [
          {
            date_time: new BigQueryDate('2022-01-01T08:00:00.000Z'),
            starved_time_percentage: 16,
            blocked_time_percentage: 11,
          },
          {
            date_time: new BigQueryDate('2022-01-01T09:00:00.000Z'),
            starved_time_percentage: 17,
            blocked_time_percentage: 12,
          },
        ],
      ];
    });

    // TODO Remove skip once query is implemented
    it.skip('should retrieve starved and blocked times from the db', async () => {
      const result =
        await equipmentStore.getEquipmentWorkstationStarvedBlockedSeries(
          'WS01',
        );
      expect(result).to.deep.equal(fakeQueryResponse);
    });
  });

  describe('getEquipmentWorkstationAisleMovementData', () => {
    let fakeQueryResponse: EquipmentWorkstationAisleMovementDataQueryResponse;

    beforeEach(() => {
      fakeQueryResponse = {
        retrieval: 17548,
        storage: 10517,
        positioning: 5688,
        iat: 5282,
        shuffle: 1435,
        bypass: 341,
      };
      executeMonitoredJobStub.resolves([fakeQueryResponse]);
    });

    // TODO Remove skip once query is implemented
    it.skip('should retrieve aisle movement data from the db', () => {
      const result =
        equipmentStore.getEquipmentWorkstationAisleMovementData('WS01');
      expect(result).to.deep.equal(fakeQueryResponse);
    });
  });

  describe('getEquipmentWorkstationActiveFaults', () => {
    let fakeQueryResponse: EquipmentWorkstationActiveFaultsQueryResponse[];

    beforeEach(() => {
      fakeQueryResponse = [
        {
          description: 'Maintenance Gate Level 1',
          start_time: new BigQueryDate('2023-11-23T14:30:00.000Z'),
          duration: 720,
          fault_id: 'F1234',
        },
      ];
      executeMonitoredJobStub.resolves(fakeQueryResponse);
    });

    it('should retrieve workstation active faults from the db', async () => {
      const result =
        await equipmentStore.getEquipmentWorkstationActiveFaults('WS01');
      expect(result).to.deep.equal(fakeQueryResponse);
    });
  });

  describe('getEquipmentFaultsFilterTypeList', () => {
    let fakeQueryResponse: FaultsFilterTypeListStoreResult[];
    const startDate = new Date('2022-01-10');
    const endDate = new Date('2022-01-11');

    const params: FaultsFilterTypeListStoreParams = {
      startDate,
      endDate,
      filterColumnName: 'level',
    };

    beforeEach(() => {
      fakeQueryResponse = [
        {
          faultFilter: 'Aisle 01',
          totalResults: 300,
        },
        {
          faultFilter: 'Aisle 02',
          totalResults: 300,
        },
      ];
      executeMonitoredJobStub.resolves([fakeQueryResponse]);
    });

    it('should retrieve fault filter type list data from the db', async () => {
      const result =
        await equipmentStore.getEquipmentFaultsFilterTypeList(params);
      expect(result).to.deep.equal(fakeQueryResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        equipmentStore.getEquipmentFaultsFilterTypeList(params),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getEquipmentFaultsMovementsSeries', () => {
    let fakeQueryResponse: FaultsMovementsSeriesStoreResult[];
    const startDate = new Date('2022-01-10');
    const endDate = new Date('2022-01-11');

    const params: FaultsMovementsSeriesStoreParams = {
      startDate,
      endDate,
    };
    let settingStub: sinon.SinonStub;
    const configSetting: AppConfigSetting = {
      id: 'test',
      name: 'test',
      group: null,
      dataType: 'string',
      value: 'America/New_York',
    };

    beforeEach(() => {
      fakeQueryResponse = [
        {
          movementCount: 200,
          movementsPerFault: 100,
          eventInterval: new BigQueryTimestamp('2021-10-23'),
        },
        {
          movementCount: 100,
          movementsPerFault: 200,
          eventInterval: new BigQueryTimestamp('2021-10-24'),
        },
      ];
      executeMonitoredJobStub.resolves([fakeQueryResponse]);
      settingStub = sinon
        .stub(configStore, 'findMostRelevantSettingForUser')
        .resolves(configSetting);
    });

    afterEach(() => {
      settingStub.restore();
    });

    it('should retrieve fault filter type list data from the db', async () => {
      const result =
        await equipmentStore.getEquipmentFaultsMovementsSeries(params);
      expect(result).to.deep.equal(fakeQueryResponse);
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should get site time zone setting from config', async () => {
      await equipmentStore.getEquipmentFaultsMovementsSeries(params);

      expect(settingStub.calledOnce).to.be.true;
    });

    it('should throw an error when the setting value cannot be found', async () => {
      settingStub.resolves(undefined);

      const result = await expect(
        equipmentStore.getEquipmentFaultsMovementsSeries(params),
      ).to.be.rejectedWith(IctError);
      expect(result.statusCode).to.equal(
        IctError.internalServerError().statusCode,
      );
    });

    it('should ensure there is a default config setting defined', async () => {
      const settingNames: string[] = [];
      DefaultConfigSettings.forEach(setting => {
        settingNames.push(setting.name);
      });
      expect(DefaultConfigSettings).to.be.an('array');
      expect(settingNames).to.contain('site-time-zone');
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        equipmentStore.getEquipmentFaultsMovementsSeries(params),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getEquipmentFaultsList', () => {
    const mockQueryResponse: EquipmentFaultsListQueryResponse[] = [
      {
        timestamp: '2024-08-19T12:00:00.000Z',
        durationMinutes: 1.2,
        reason_name: '430 - Lift Drive Faulted',
        aisle: '01',
        level: '02',
        device_code: 'MSAI01ER02LO00',
        device_functional_type: 'Lift',
      },
      {
        timestamp: '2024-08-19T12:00:00.000Z',
        durationMinutes: 1.2,
        reason_name: 'Offline',
        aisle: '02',
        level: '01',
        device_code: 'MSAI02ER01LO00',
        device_functional_type: 'Lift',
      },
    ];
    const mockStoreResponse: PaginatedResults<
      EquipmentFaultsListQueryResponse[]
    > = {
      list: mockQueryResponse,
      totalResults: 0,
    };
    const mockParams: EquipmentFaultsListStoreParams = {
      startDate: '2024-10-22',
      endDate: '2024-10-23',
      sortFields: [],
      limit: 25,
      page: 1,
    };

    beforeEach(() => {
      executeMonitoredJobStub.resolves([mockQueryResponse]);
    });

    it('should retrieve fault list data from the db', async () => {
      const result = await equipmentStore.getEquipmentFaultsList(mockParams);
      expect(result).to.deep.equal(mockStoreResponse);
    });
  });
});
