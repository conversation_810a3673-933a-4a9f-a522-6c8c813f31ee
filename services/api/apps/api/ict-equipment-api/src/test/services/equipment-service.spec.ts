import {BigQueryDate, BigQueryTimestamp} from '@google-cloud/bigquery';
import {expect} from 'chai';
import {HealthStatus, PaginatedResults} from 'ict-api-foundations';
import sinon from 'sinon';
import {DateTime} from 'luxon';
import {EquipmentActiveFaultsDefinition} from '../../defs/equipment-faults-active-def.ts';
import {EquipmentFaultsQueryDefinition} from '../../defs/equipment-faults-def.ts';
import {EquipmentFaultsDowntimeQueryResponse} from '../../defs/equipment-faults-downtime-def.ts';
import {
  FaultEvent,
  FaultEventQueryResponse,
} from '../../defs/equipment-faults-event-def.ts';
import {EquipmentFaultsMovementsQueryResponse} from '../../defs/equipment-faults-movements-def.ts';
import {EquipmentFaultsSeriesQueryData} from '../../defs/equipment-faults-series-def.ts';
import {EquipmentFlowRateDefinition} from '../../defs/equipment-flow-rate-def.ts';
import {MovementQualityQueryResponse} from '../../defs/equipment-movement-quality-def.ts';
import {EquipmentOutboundRateQueryResponse} from '../../defs/equipment-outbound-rate-def.ts';
import {
  EquipmentWorkstationActiveFaultsData,
  EquipmentWorkstationActiveFaultsQueryResponse,
} from '../../defs/equipment-workstation-active-faults-def.ts';
import {
  EquipmentWorkstationAisleMovementData,
  EquipmentWorkstationAisleMovementDataQueryResponse,
} from '../../defs/equipment-workstation-aisle-movement-chart-def.ts';
import {
  EquipmentWorkstationLineRatesSeriesData,
  EquipmentWorkstationLineRatesSeriesQueryResponse,
} from '../../defs/equipment-workstation-line-rates-series-def.ts';
import {
  EquipmentWorkstationOperatorActivityData,
  EquipmentWorkstationOperatorActivityQueryResponse,
} from '../../defs/equipment-workstation-operator-activity-def.ts';
import {
  EquipmentWorkstationStarvedBlockedTimeQueryResponse,
  EquipmentWorkstationStarvedBlockedTimeSeriesData,
} from '../../defs/equipment-workstation-starved-blocked-time-series-def.ts';
import {EquipmentService} from '../../services/equipment-service.ts';
import {EquipmentStore} from '../../stores/equipment-store.ts';
import {
  FaultAvgDurationByStatusQueryStoreResult,
  FaultAvgDurationSeriesData,
  FaultCountGroupedQueryStoreResult,
  FaultCountGroupedSeriesData,
} from '../../defs/equipment-fault-counts-grouped-def.ts';
import {FaultsFilterTypeListStoreResult} from '../../defs/equipment-faults-filter-type-list-def.ts';
import {
  FaultsMovementsSeriesData,
  FaultsMovementsSeriesStoreResult,
} from '../../defs/equipment-faults-movements-series-def.ts';
import {
  EquipmentFaultsListQueryResponse,
  EquipmentFaultsListStoreParams,
  PostEquipmentFaultsListResponse,
} from '../../defs/equipment-faults-list-def.ts';

describe('EquipmentService', () => {
  // Equipment Store stub, intercepts calls made by the equipment service
  let equipmentStoreStub: sinon.SinonStubbedInstance<EquipmentStore>;

  // Equipment service to use in tests
  let equipmentService: EquipmentService;

  beforeEach(() => {
    equipmentStoreStub = sinon.createStubInstance(EquipmentStore);
    equipmentService = new EquipmentService(equipmentStoreStub);
  });

  describe('getFaultsAsync', () => {
    it('should call getFaultsAsync method of EquipmentStore', async () => {
      const startDate = '2021-11-30T00:00:01';
      const endDate = '2021-11-30T23:59:59';
      const mockResponse: EquipmentFaultsQueryDefinition = {
        faults: 50,
      };

      equipmentStoreStub.getFaultsCountAsync.resolves(mockResponse);

      const result = await equipmentService.getEquipmentFaultsAsync(
        startDate,
        endDate
      );

      expect(result).to.deep.equal(mockResponse);
    });
  });

  describe('getEquipmentFaultsSeriesData', () => {
    let startDate: string;
    let endDate: string;

    beforeEach(() => {
      startDate = '2023-04-10T01:26:54.813Z';
      endDate = '2023-04-10T01:26:54.813Z';
    });

    const mockEquipmentFaultsSeriesStoreResponse: EquipmentFaultsSeriesQueryData[] =
      [
        {
          hourOfTheDay: '2023-04-10T10:00:00.000Z',
          faults: 2,
          throughput: 224,
        },
        {
          hourOfTheDay: '2023-04-10T11:00:00.000Z',
          faults: 5,
          throughput: 106,
        },
      ];

    it('should return the correct data', async () => {
      equipmentStoreStub.getFaultsToThroughputSeries.resolves(
        mockEquipmentFaultsSeriesStoreResponse
      );

      const result = await equipmentService.getEquipmentFaultsSeries(
        startDate,
        endDate
      );
      const expected: EquipmentFaultsSeriesQueryData[] =
        mockEquipmentFaultsSeriesStoreResponse.map(row => ({
          hourOfTheDay: row.hourOfTheDay,
          faults: row.faults,
          throughput: row.throughput,
        }));
      expect(result).to.deep.equal(expected);
    });
  });

  describe('getFaultsRecentEvents', () => {
    const mockRecentFaultEvents: FaultEventQueryResponse[] = [
      {
        _TimeUTC: new BigQueryTimestamp('2021-10-23'),
        Area: 'Test Area',
        FLT_DESC: 'Fault Description',
        Message: 'Fault Message',
      },
    ];

    it('should return the correct data', async () => {
      equipmentStoreStub.getFaultsRecentEvents.resolves(mockRecentFaultEvents);

      const result = await equipmentService.getFaultsRecentEvents();
      const expected: FaultEvent[] = [
        {
          time: '2021-10-23T00:00:00.000Z',
          area: 'Test Area',
          description: 'Fault Message; Fault Description',
        },
      ];
      expect(result).deep.equal(expected);
    });
  });

  describe('getEquipmentFaultsDeviceIdList', () => {
    const mockQuery = {
      startDate: new Date('2023-12-01'),
      endDate: new Date('2024-12-31'),
    };

    const mockStoreResponse: FaultsFilterTypeListStoreResult[] = [
      {
        faultFilter: 'id1',
        totalResults: 300,
      },
      {
        faultFilter: 'id2',
        totalResults: 300,
      },
    ];

    it('should return the correct data', async () => {
      equipmentStoreStub.getEquipmentFaultsFilterTypeList.resolves(
        mockStoreResponse
      );

      const result =
        await equipmentService.getEquiupmentFaultsDeviceIdsList(mockQuery);
      const expected: string[] = ['id1', 'id2'];
      expect(result).deep.equal(expected);
    });
  });

  describe('getMovementsPerFaultByArea', () => {
    const mockResults: EquipmentFaultsMovementsQueryResponse[] = [
      {
        eventTime: new Date('2014-04-05').toISOString(),
        area: 'Test Area',
        faultCount: 25,
        movementCount: 500,
      },
    ];

    it('should return happy path data from the db', async () => {
      const startDate = '2021-11-30T00:00:01';
      const endDate = '2021-11-30T23:59:59';
      equipmentStoreStub.getFaultAndEquipmentMovementsAsync.resolves(
        mockResults
      );

      const result = await equipmentService.getMovementsPerFaultByArea(
        startDate,
        endDate
      );

      const expected: EquipmentFaultsMovementsQueryResponse[] = [
        {
          area: 'Test Area',
          eventTime: '2014-04-05T00:00:00.000Z',
          faultCount: 25,
          movementCount: 500,
        },
      ];
      expect(result).deep.equal(expected);
    });

    it('should return empty array if no results from db', async () => {
      const startDate = '2021-11-30T00:00:01';
      const endDate = '2021-11-30T23:59:59';
      equipmentStoreStub.getFaultAndEquipmentMovementsAsync.resolves(undefined);

      const result = await equipmentService.getMovementsPerFaultByArea(
        startDate,
        endDate
      );
      expect(result).deep.equal([]);
    });
  });

  describe('getTotalDowntimeByArea', () => {
    const mockResults: EquipmentFaultsDowntimeQueryResponse[] = [
      {
        area: 'Test Area',
        downtimeMS: 4500,
      },
    ];

    it('should return happy path data from the db', async () => {
      const startDate = '2021-11-30T00:00:01';
      const endDate = '2021-11-30T23:59:59';
      equipmentStoreStub.getTotalDowntimes.resolves(mockResults);

      const result = await equipmentService.getTotalDowntimeByArea(
        startDate,
        endDate
      );

      const expected: EquipmentFaultsDowntimeQueryResponse[] = [
        {
          area: 'Test Area',
          downtimeMS: 4500,
        },
      ];
      expect(result).deep.equal(expected);
    });

    it('should return empty array if no results from db', async () => {
      const startDate = '2021-11-30T00:00:01';
      const endDate = '2021-11-30T23:59:59';
      equipmentStoreStub.getTotalDowntimes.resolves(undefined);

      const result = await equipmentService.getTotalDowntimeByArea(
        startDate,
        endDate
      );
      expect(result).deep.equal([]);
    });
  });

  describe('getMovementQualityByArea', () => {
    const mockResults: MovementQualityQueryResponse[] = [
      {
        area: 'Test Area',
        movementQualityPercentage: 50,
      },
    ];

    it('should return happy path data from the db', async () => {
      const startDate = '2021-11-30T00:00:01';
      const endDate = '2021-11-30T23:59:59';
      equipmentStoreStub.getMovementQuality.resolves(mockResults);

      const result = await equipmentService.getMovementQualityByArea(
        startDate,
        endDate
      );

      const expected: MovementQualityQueryResponse[] = [
        {
          area: 'Test Area',
          movementQualityPercentage: 50,
        },
      ];
      expect(result).deep.equal(expected);
    });

    it('should return empty array if no results from db', async () => {
      const startDate = '2021-11-30T00:00:01';
      const endDate = '2021-11-30T23:59:59';
      equipmentStoreStub.getMovementQuality.resolves(undefined);

      const result = await equipmentService.getMovementQualityByArea(
        startDate,
        endDate
      );
      expect(result).deep.equal([]);
    });
  });

  describe('getEquipmentFlowRateByArea', () => {
    const mockResults: EquipmentFlowRateDefinition[] = [
      {
        area: 'Test Area',
        avgHourlyFlowRate: 15,
        rangeOfHours: 2,
      },
    ];

    it('should return happy path data from the db', async () => {
      const startDate = '2021-11-30T00:00:01';
      const endDate = '2021-11-30T23:59:59';
      equipmentStoreStub.getEquipmentFlowRateAverage.resolves(mockResults);

      const result = await equipmentService.getEquipmentFlowRateByArea(
        startDate,
        endDate
      );

      const expected: EquipmentFlowRateDefinition[] = [
        {
          area: 'Test Area',
          avgHourlyFlowRate: 15,
          rangeOfHours: 2,
        },
      ];
      expect(result).deep.equal(expected);
    });

    it('should return empty array if no results from db', async () => {
      const startDate = '2021-11-30T00:00:01';
      const endDate = '2021-11-30T23:59:59';
      equipmentStoreStub.getEquipmentFlowRateAverage.resolves(undefined);

      const result = await equipmentService.getEquipmentFlowRateByArea(
        startDate,
        endDate
      );
      expect(result).deep.equal([]);
    });
  });

  describe('getFaultsActiveEvents', () => {
    const mockResults: EquipmentActiveFaultsDefinition[] = [
      {
        area: 'Hyrule Test Area',
        duration: 19,
        deviceReason: 'A reason',
      },
    ];

    it('should return happy path data from the db', async () => {
      equipmentStoreStub.getEquipmentActiveFaultsData.resolves(mockResults);

      const result = await equipmentService.getFaultsActiveEvents();

      expect(result).deep.equal(mockResults);
    });

    it('should return empty array if no results from db', async () => {
      equipmentStoreStub.getEquipmentActiveFaultsData.resolves(undefined);

      const result = await equipmentService.getFaultsActiveEvents();
      expect(result).deep.equal(undefined);
    });
  });

  describe('getFaultsCountGroupedAsync', () => {
    const serviceResults: FaultCountGroupedSeriesData = {
      faultCounts: [
        {
          name: '01',
          value: 50,
        },
        {
          name: '02',
          value: 52,
        },
        {
          name: '03',
          value: 66,
        },
      ],
    };

    const mockResults: FaultCountGroupedQueryStoreResult[] = [
      {
        groupByColumn: '01',
        count: 50,
      },
      {
        groupByColumn: '02',
        count: 52,
      },
      {
        groupByColumn: '03',
        count: 66,
      },
    ];

    it('should return data from the db successfully', async () => {
      equipmentStoreStub.getFaultsCountGroupedAsync.resolves(mockResults);

      const result = await equipmentService.getFaultsCountGroupedAsync({
        startDate: new Date('2021-11-29T00:00:01'),
        endDate: new Date('2021-11-30T23:59:59'),
        groupByColumn: 'aisle',
      });

      expect(result).to.deep.equal(serviceResults);
    });
  });

  describe('getFaultsAvgDurationByStatusAsync', () => {
    const serviceResults: FaultAvgDurationSeriesData = {
      avgDuration: [
        {
          name: 'Reason 01',
          value: 532.2,
        },
        {
          name: 'Reason 02',
          value: 814.9,
        },
        {
          name: 'Reason 03',
          value: 134.4,
        },
        {
          name: 'Reason 04',
          value: 205.6,
        },
        {
          name: 'Reason 05',
          value: 253.8,
        },
      ],
    };

    const mockResults: FaultAvgDurationByStatusQueryStoreResult[] = [
      {
        status: 'Reason 01',
        avgDuration: 532.2,
      },
      {
        status: 'Reason 02',
        avgDuration: 814.9,
      },
      {
        status: 'Reason 03',
        avgDuration: 134.4,
      },
      {
        status: 'Reason 04',
        avgDuration: 205.6,
      },
      {
        status: 'Reason 05',
        avgDuration: 253.8,
      },
    ];

    it('should return data from the db successfully', async () => {
      equipmentStoreStub.getFaultsAvgDurationByStatusAsync.resolves(
        mockResults
      );

      const result = await equipmentService.getFaultsAvgDurationByStatusAsync({
        startDate: new Date('2021-11-29T00:00:01'),
        endDate: new Date('2021-11-30T23:59:59'),
      });

      expect(result).to.deep.equal(serviceResults);
    });
  });

  describe('getAisleSummary', () => {
    it('should return happy path data from the db', async () => {
      const startDate = '2021-11-30T00:00:01';
      const endDate = '2021-11-30T23:59:59';
      const mockSummaryData = {
        status: HealthStatus.Ok,
        totalMovements: 40811,
        storageUtilization: 0.98,
        storedTotesPerHour: 120,
        inventoryTotes: 1800,
        retrievedTotesPerHour: 300,
        orderTotes: 2000,
      };
      equipmentStoreStub.getAisleSummary.resolves(mockSummaryData);

      const result = await equipmentService.getAisleSummary(
        '1',
        startDate,
        endDate
      );
      expect(equipmentStoreStub.getAisleSummary.calledOnce).to.be.true;
      expect(result).deep.equal(mockSummaryData);
    });
  });

  describe('getWorkstationSummary', () => {
    it('should return happy path data from the db', async () => {
      const startDate = '2021-11-30T00:00:01';
      const endDate = '2021-11-30T23:59:59';
      const workstationId = 'test';
      const mockSummaryData = {
        status: HealthStatus.Ok,
        operatorId: 1534,
        iatDonorTotes: 0.2,
        linesPerHour: 110,
        activeTime: 'PT8H30M',
        idleTime: 'PT1H15M',
        starvedTime: 'PT15M',
      };
      equipmentStoreStub.getWorkstationSummary.resolves(mockSummaryData);

      const result = await equipmentService.getWorkstationSummary(
        startDate,
        endDate,
        workstationId
      );
      expect(equipmentStoreStub.getWorkstationSummary.calledOnce).to.be.true;
      expect(result).deep.equal(mockSummaryData);
    });
  });

  describe('getWorkstationOperatorActivity', () => {
    let workstationId: string;
    let fakeStoreResponse: EquipmentWorkstationOperatorActivityQueryResponse[];

    beforeEach(() => {
      workstationId = 'TestWS1';
      fakeStoreResponse = [
        {
          operator_id: '001531',
          start_time: new BigQueryDate('2023-04-22T07:46:00.000Z'),
          end_time: new BigQueryDate('2023-04-22T10:00:00.000Z'),
          idle_time: 900,
          starved_time: 900,
          blocked_time: 1500,
          line_rates: 75,
          weighted_line_rates: 100,
        },
        {
          operator_id: '001532',
          start_time: new BigQueryDate('2023-04-22T10:15:00.000Z'),
          end_time: null,
          idle_time: 900,
          starved_time: 900,
          blocked_time: 1500,
          line_rates: 75,
          weighted_line_rates: 100,
        },
      ];
      equipmentStoreStub.getWorkstationOperatorActivity.resolves(
        fakeStoreResponse
      );
    });

    it('should return the correct data', async () => {
      // Set the time to 2023-04-22T10:00:00.000Z to avoid flaky test timing issue
      sinon.useFakeTimers(new Date('2023-04-22T10:00:00.000Z').getTime());

      const result =
        await equipmentService.getWorkstationOperatorActivity(workstationId);

      const expected: EquipmentWorkstationOperatorActivityData[] = [
        {
          operatorId: '001531',
          startTime: '2023-04-22T07:46:00.000Z',
          endTime: '2023-04-22T10:00:00.000Z',
          totalTimeDuration: DateTime.fromISO('2023-04-22T10:00:00.000Z').diff(
            DateTime.fromISO('2023-04-22T07:46:00.000Z'),
            'seconds'
          ).seconds,
          idleTime: 900,
          starvedTime: 900,
          blockedTime: 1500,
          lineRatePerHour: 75,
          weightedLineRatePerHour: 100,
        },
        {
          operatorId: '001532',
          startTime: '2023-04-22T10:15:00.000Z',
          endTime: null,
          totalTimeDuration: Math.floor(
            DateTime.now().diff(
              DateTime.fromISO('2023-04-22T10:15:00.000Z'),
              'seconds'
            ).seconds
          ),
          idleTime: 900,
          starvedTime: 900,
          blockedTime: 1500,
          lineRatePerHour: 75,
          weightedLineRatePerHour: 100,
        },
      ];
      expect(result).to.deep.equal(expected);
    });
  });

  describe('getOutboundRate', () => {
    it('should return happy path by calling Equipment Store', async () => {
      const mockStoreResponse: EquipmentOutboundRateQueryResponse = {
        outboundDivertsHour: 800,
      };
      equipmentStoreStub.getOutboundRate.resolves(mockStoreResponse);

      const result = await equipmentService.getOutboundRate();
      expect(equipmentStoreStub.getOutboundRate.calledOnce).to.be.true;
      expect(result).deep.equal({
        ...mockStoreResponse,
        status: 'ok',
      });
    });
  });

  describe('getEquipmentWorkstationLineRatesSeries', () => {
    const mockQueryResponse: EquipmentWorkstationLineRatesSeriesQueryResponse[] =
      [
        {
          date_time: new BigQueryDate('2021-10-23T00:00:00.000Z'),
          line_rate: 10,
        },
        {
          date_time: new BigQueryDate('2021-10-23T01:00:00.000Z'),
          line_rate: 20,
        },
      ];

    it('should return the correct data', async () => {
      equipmentStoreStub.getEquipmentWorkstationLineRatesSeries.resolves(
        mockQueryResponse
      );

      const result =
        await equipmentService.getEquipmentWorkstationLineRatesSeries('WS01');
      const expected: EquipmentWorkstationLineRatesSeriesData = {
        lineRates: mockQueryResponse.map(r => ({
          name: r.date_time.value,
          value: r.line_rate,
        })),
      };
      expect(result).deep.equal(expected);
    });
  });

  describe('getEquipmentWorkstationStarvedBlockedSeries', () => {
    const mockQueryResponse: EquipmentWorkstationStarvedBlockedTimeQueryResponse[] =
      [
        {
          date_time: new BigQueryDate('2023-11-28T00:00:00.000Z'),
          starved_time_percentage: 17,
          blocked_time_percentage: 11,
        },
        {
          date_time: new BigQueryDate('2023-11-28T01:00:00.000Z'),
          starved_time_percentage: 16,
          blocked_time_percentage: 12,
        },
      ];
    it('should return the correct data', async () => {
      equipmentStoreStub.getEquipmentWorkstationStarvedBlockedSeries.resolves(
        mockQueryResponse
      );

      const result =
        await equipmentService.getEquipmentWorkstationStarvedBlockedSeries(
          'WS01'
        );
      const expected: EquipmentWorkstationStarvedBlockedTimeSeriesData = {
        starvedTime: [
          {name: '2023-11-28T00:00:00.000Z', value: 17},
          {name: '2023-11-28T01:00:00.000Z', value: 16},
        ],
        blockedTime: [
          {name: '2023-11-28T00:00:00.000Z', value: 11},
          {name: '2023-11-28T01:00:00.000Z', value: 12},
        ],
      };
      expect(result).deep.equal(expected);
    });
  });

  describe('getEquipmentWorkstationAisleMovementData', () => {
    const mockQueryResponse: EquipmentWorkstationAisleMovementDataQueryResponse =
      {
        retrieval: 17548,
        storage: 10517,
        positioning: 5688,
        iat: 5282,
        shuffle: 1435,
        bypass: 341,
      };
    it('should return the correct data', async () => {
      equipmentStoreStub.getEquipmentWorkstationAisleMovementData.resolves(
        mockQueryResponse
      );
      const result =
        await equipmentService.getEquipmentWorkstationAisleMovementData('WS01');
      const expected: EquipmentWorkstationAisleMovementData = {
        retrieval: 17548,
        storage: 10517,
        positioning: 5688,
        iat: 5282,
        shuffle: 1435,
        bypass: 341,
      };
      expect(result).deep.equal(expected);
    });
  });

  describe('getEquipmentWorkstationActiveFaults', () => {
    const mockQueryResponse: EquipmentWorkstationActiveFaultsQueryResponse[] = [
      {
        description: 'Maintenance Gate Level 1',
        start_time: new BigQueryDate('2023-11-23T14:30:00.000Z'),
        duration: 720,
        fault_id: 'F1234',
      },
    ];
    it('should return the correct data', async () => {
      equipmentStoreStub.getEquipmentWorkstationActiveFaults.resolves(
        mockQueryResponse
      );

      const result =
        await equipmentService.getEquipmentWorkstationActiveFaults('WS01');
      const expected: EquipmentWorkstationActiveFaultsData[] = [
        {
          description: 'Maintenance Gate Level 1',
          startTime: new BigQueryDate('2023-11-23T14:30:00.000Z').value,
          duration: 720,
          faultId: 'F1234',
        },
      ];
      console.log('expected: ', expected);
      console.log('result: ', result);
      expect(result).deep.equal(expected);
    });
  });

  describe('getEquipmentFaultsLevelList', () => {
    const mockStoreResponse: FaultsFilterTypeListStoreResult[] = [
      {
        faultFilter: 'Level 01',
        totalResults: 300,
      },
      {
        faultFilter: 'Level 02',
        totalResults: 300,
      },
    ];

    it('should return the correct data', async () => {
      equipmentStoreStub.getEquipmentFaultsFilterTypeList.resolves(
        mockStoreResponse
      );

      const result = await equipmentService.getEquipmentFaultsLevelList({
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-02'),
      });

      const expected: string[] = ['Level 01', 'Level 02'];
      expect(equipmentStoreStub.getEquipmentFaultsFilterTypeList.calledOnce).to
        .be.true;
      expect(result).deep.equal(expected);
    });
  });

  describe('getEquipmentFaultsAisleList', () => {
    const mockStoreResponse: FaultsFilterTypeListStoreResult[] = [
      {
        faultFilter: 'Aisle 01',
        totalResults: 300,
      },
      {
        faultFilter: 'Aisle 02',
        totalResults: 300,
      },
    ];

    it('should return the correct data', async () => {
      equipmentStoreStub.getEquipmentFaultsFilterTypeList.resolves(
        mockStoreResponse
      );

      const result = await equipmentService.getEquipmentFaultsAisleList({
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-02'),
      });

      const expected: string[] = ['Aisle 01', 'Aisle 02'];
      expect(equipmentStoreStub.getEquipmentFaultsFilterTypeList.calledOnce).to
        .be.true;
      expect(result).deep.equal(expected);
    });
  });

  describe('getEquipmentFaultsDeviceTypeList', () => {
    const mockStoreResponse: FaultsFilterTypeListStoreResult[] = [
      {
        faultFilter: 'type 1',
        totalResults: 300,
      },
      {
        faultFilter: 'type 2',
        totalResults: 300,
      },
    ];

    it('should return the correct data', async () => {
      equipmentStoreStub.getEquipmentFaultsFilterTypeList.resolves(
        mockStoreResponse
      );

      const result = await equipmentService.getEquipmentFaultsDeviceTypeList({
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-02'),
      });

      const expected: string[] = ['type 1', 'type 2'];
      expect(equipmentStoreStub.getEquipmentFaultsFilterTypeList.calledOnce).to
        .be.true;
      expect(result).deep.equal(expected);
    });
  });

  describe('getEquipmentFaultsMovementsList', () => {
    const mockStoreResponse: FaultsMovementsSeriesStoreResult[] = [
      {
        movementCount: 200,
        movementsPerFault: 100,
        eventInterval: new BigQueryTimestamp('2021-10-23'),
      },
      {
        movementCount: 100,
        movementsPerFault: 200,
        eventInterval: new BigQueryTimestamp('2021-10-24'),
      },
    ];

    it('should return the correct data', async () => {
      equipmentStoreStub.getEquipmentFaultsMovementsSeries.resolves(
        mockStoreResponse
      );

      const result = await equipmentService.getEquipmentFaultsMovementsSeries({
        startDate: new Date('2024-10-22'),
        endDate: new Date('2024-10-25'),
      });

      const expected: FaultsMovementsSeriesData = {
        movementCounts: [
          {name: '2021-10-23T00:00:00.000Z', value: 200},
          {name: '2021-10-24T00:00:00.000Z', value: 100},
        ],
        movementsPerFault: [
          {name: '2021-10-23T00:00:00.000Z', value: 100},
          {name: '2021-10-24T00:00:00.000Z', value: 200},
        ],
      };

      expect(equipmentStoreStub.getEquipmentFaultsMovementsSeries.calledOnce).to
        .be.true;
      expect(result).deep.equal(expected);
    });
  });

  describe('getEquipmentFaultsStatusList', () => {
    const mockStoreResponse: FaultsFilterTypeListStoreResult[] = [
      {
        faultFilter: 'Status 01',
        totalResults: 300,
      },
      {
        faultFilter: 'Status 02',
        totalResults: 300,
      },
    ];

    it('should return the correct data', async () => {
      equipmentStoreStub.getEquipmentFaultsFilterTypeList.resolves(
        mockStoreResponse
      );

      const result = await equipmentService.getEquipmentFaultsStatusList({
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-02'),
      });

      const expected: string[] = ['Status 01', 'Status 02'];
      expect(equipmentStoreStub.getEquipmentFaultsFilterTypeList.calledOnce).to
        .be.true;
      expect(result).deep.equal(expected);
    });
  });

  describe('getEquipmentFaultsList', () => {
    const mockParams: EquipmentFaultsListStoreParams = {
      startDate: '2024-10-22',
      endDate: '2024-10-23',
      sortFields: [],
      limit: 25,
      page: 1,
    };
    const mockStoreResponse: PaginatedResults<
      EquipmentFaultsListQueryResponse[]
    > = {
      list: [
        {
          timestamp: '2024-08-19T12:00:00.000Z',
          durationMinutes: 1.2,
          reason_name: '430 - Lift Drive Faulted',
          aisle: '01',
          level: '02',
          device_code: 'MSAI01ER02LO00',
          device_functional_type: 'Lift',
        },
        {
          timestamp: '2024-08-19T12:00:00.000Z',
          durationMinutes: 1.2,
          reason_name: 'Offline',
          aisle: '02',
          level: '01',
          device_code: 'MSAI02ER01LO00',
          device_functional_type: 'Lift',
        },
      ],
      totalResults: 2,
    };

    it('should return the correct data', async () => {
      equipmentStoreStub.getEquipmentFaultsList.resolves(mockStoreResponse);

      const result = await equipmentService.getEquipmentFaultsList(mockParams);

      const expected: PostEquipmentFaultsListResponse = {
        data: [
          {
            timestamp: '2024-08-19T12:00:00.000Z',
            durationMinutes: 1.2,
            status: '430 - Lift Drive Faulted',
            aisle: '01',
            level: '02',
            device: 'MSAI01ER02LO00',
            deviceType: 'Lift',
          },
          {
            timestamp: '2024-08-19T12:00:00.000Z',
            durationMinutes: 1.2,
            status: 'Offline',
            aisle: '02',
            level: '01',
            device: 'MSAI02ER01LO00',
            deviceType: 'Lift',
          },
        ],
        metadata: {
          page: mockParams.page,
          limit: mockParams.limit,
          totalResults: mockStoreResponse.totalResults,
        },
      };

      expect(equipmentStoreStub.getEquipmentFaultsList.calledOnce).to.be.true;
      expect(result).deep.equal(expected);
    });
  });
});
