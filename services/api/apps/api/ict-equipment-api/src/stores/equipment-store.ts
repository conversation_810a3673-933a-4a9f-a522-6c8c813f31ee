import {BigQueryDate, Query} from '@google-cloud/bigquery';
import {
  BigQueryGenerator,
  ContextService,
  DatabaseProvider,
  DiService,
  HealthStatus,
  PaginatedResults,
  IctError,
  SortField,
  ConfigStore,
  SQLSelectColumn,
  addStartEndDateParamValues,
  SortingSelectQueryParams,
} from 'ict-api-foundations';
import {DateTime} from 'luxon';
import {EquipmentActiveFaultsDefinition} from '../defs/equipment-faults-active-def.ts';
import {EquipmentFaultsQueryDefinition} from '../defs/equipment-faults-def.ts';
import {EquipmentFaultsDowntimeQueryResponse} from '../defs/equipment-faults-downtime-def.ts';
import {FaultEventQueryResponse} from '../defs/equipment-faults-event-def.ts';
import {EquipmentFaultsMovementsQueryResponse} from '../defs/equipment-faults-movements-def.ts';
import {EquipmentFaultsSeriesQueryData} from '../defs/equipment-faults-series-def.ts';
import {EquipmentFlowRateDefinition} from '../defs/equipment-flow-rate-def.ts';
import {
  RecentEventQueryResponse,
  RecentEventQueryStoreParams,
} from '../defs/equipment-recent-event-def.js';
import {MovementQualityQueryResponse} from '../defs/equipment-movement-quality-def.ts';
import {EquipmentOutboundRateQueryResponse} from '../defs/equipment-outbound-rate-def.ts';
import {EquipmentSummaryAisleDefinition} from '../defs/equipment-summary-aisles-def.ts';
import {EquipmentSummaryWorkstationDefinition} from '../defs/equipment-summary-workstations-def.ts';
import {EquipmentWorkstationLineRatesSeriesQueryResponse} from '../defs/equipment-workstation-line-rates-series-def.ts';
import {EquipmentWorkstationOperatorActivityQueryResponse} from '../defs/equipment-workstation-operator-activity-def.ts';
import {EquipmentWorkstationStarvedBlockedTimeQueryResponse} from '../defs/equipment-workstation-starved-blocked-time-series-def.ts';
import {FaultsFacilityArea} from '../defs/fault-areas-def.ts';
import {EquipmentWorkstationAisleMovementDataQueryResponse} from '../defs/equipment-workstation-aisle-movement-chart-def.ts';
import {EquipmentWorkstationActiveFaultsQueryResponse} from '../defs/equipment-workstation-active-faults-def.ts';
import {
  FaultCountGroupedByQueryStoreParams,
  FaultCountGroupedQueryStoreResult,
  FaultAvgDurationByStatusQueryStoreResult,
  FaultAvgDurationByStatusQueryStoreParams,
} from '../defs/equipment-fault-counts-grouped-def.ts';
import {
  FaultsFilterTypeListStoreParams,
  FaultsFilterTypeListStoreResult,
} from '../defs/equipment-faults-filter-type-list-def.ts';
import {
  FaultsMovementsSeriesStoreParams,
  FaultsMovementsSeriesStoreResult,
} from '../defs/equipment-faults-movements-series-def.ts';
import {
  EquipmentFaultsListQueryResponse,
  EquipmentFaultsListStoreParams,
} from '../defs/equipment-faults-list-def.ts';
import {SQLQueryGenerator} from '../../../../../libs/api/ict-api-foundations/src/helpers/db/sql-query-generator.ts';

@DiService()
export class EquipmentStore {
  constructor(
    private context: ContextService,
    private configStore: ConfigStore,
  ) {}

  get dbProvider(): DatabaseProvider {
    return this.context.dbProvider;
  }

  /**
   * Retrieve the total number of faults for a specified date range.
   * @param startDate Start date for the date range.
   * @param endDate End date for the date range.
   * @returns Number of faults in EquipmentFaultsDefinition format.
   */
  async getFaultsCountAsync(
    startDate: string,
    endDate: string,
  ): Promise<EquipmentFaultsQueryDefinition> {
    // build the SQL query to retrieve the fault count from a BigQuery view
    // query the database for the faults in the specified date range
    const bigQueryDb = this.dbProvider.bigQuery;

    const faultsTableName =
      this.dbProvider.bigQuery.getFullTablePath('fct_fault');

    const sql = `
    SELECT
      COUNT(*) as faultCount
    FROM
      \`${faultsTableName}\`
    WHERE
      FORMAT_TIMESTAMP('%Y-%m-%d', record_timestamp) >= @startDate
      AND FORMAT_TIMESTAMP('%Y-%m-%d', record_timestamp) <= @endDate
    `;

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    // execute the query and return the results
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    const result = rows?.[0];
    if (!result || !result.faultCount) {
      throw IctError.noContent();
    }

    const faultsData: EquipmentFaultsQueryDefinition = {
      faults: result.faultCount,
    };

    return faultsData;
  }

  async getFaultsCountGroupedAsync(
    params: FaultCountGroupedByQueryStoreParams,
  ): Promise<FaultCountGroupedQueryStoreResult[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const faultTable = this.dbProvider.bigQuery.getEdpFullTablePath(
      'gold_fault_nok_interval',
    );

    const cte = `
        WITH faults AS (
          SELECT
            ${this.getEdpFaultFilterColumns()}
          FROM
            \`${faultTable}\`
          WHERE
            fault_end_timestamp_utc BETWEEN @startDate AND @endDate
            AND REGEXP_CONTAINS(equipment_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
        )
      `;

    const allowedColumnNames = [
      'device_functional_type',
      'device_code',
      'reason_name',
      'aisle',
      'level',
      'COUNT(*)',
      'count',
      'groupByColumn',
    ];

    const groupByValue = new SQLSelectColumn(
      params.groupByColumn,
      'groupByColumn',
    );

    const countSelect = new SQLSelectColumn('COUNT(*)', 'count');

    const sortFields: SortField[] = [
      {
        columnName: 'count',
        isDescending: !params.isAscendingOrder,
      },
    ];

    const sql: SortingSelectQueryParams = {
      selectFromTable: 'faults',
      allowedColumnNames,
      cteSubquery: cte,
      groupByColumns: [params.groupByColumn],
      sortFields,
      selectColumns: [groupByValue, countSelect],
    };

    params.filterParams = addStartEndDateParamValues(
      params.startDate,
      params.endDate,
      params.filterParams,
    );

    const bqQueryGenerator = new BigQueryGenerator();
    const rows = await bqQueryGenerator.executeSelectQuery(
      bigQueryDb,
      sql,
      params.filters,
      params.filterParams,
    );

    // execute the query and return the results
    if (!rows || !rows.length) {
      throw IctError.noContent();
    }

    return rows as FaultCountGroupedQueryStoreResult[];
  }

  async getFaultsAvgDurationByStatusAsync(
    params: FaultAvgDurationByStatusQueryStoreParams,
  ): Promise<FaultAvgDurationByStatusQueryStoreResult[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const goldFaultNokInterval = this.dbProvider.bigQuery.getEdpFullTablePath(
      'gold_fault_nok_interval',
    );

    const cte = `
        WITH faults AS (
          SELECT
            fct_fault.fault_duration_milliseconds / 1000 AS fault_duration_seconds,
            ${this.getEdpFaultFilterColumns()}
          FROM
            ${goldFaultNokInterval} fct_fault
          WHERE
            fault_end_timestamp_utc BETWEEN @startDate AND @endDate
            AND REGEXP_CONTAINS(fct_fault.equipment_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
        )
      `;

    const sortFields: SortField[] = [
      {
        columnName: 'avgDuration',
        isDescending: !params.isAscendingOrder,
      },
    ];

    const allowedColumnNames = [
      'device_functional_type',
      'device_code',
      'reason_name',
      'aisle',
      'level',
      'avgDuration',
      'COALESCE(AVG(fault_duration_seconds), 0)',
      'status',
      "COALESCE(reason_name, 'N/A')",
    ];

    const groupByValue = new SQLSelectColumn(
      "COALESCE(reason_name, 'N/A')",
      'status',
    );

    const avgSelect = new SQLSelectColumn(
      'COALESCE(AVG(fault_duration_seconds), 0)',
      'avgDuration',
    );

    const sql: SortingSelectQueryParams = {
      selectFromTable: 'faults',
      allowedColumnNames,
      cteSubquery: cte,
      groupByColumns: ['status'],
      sortFields,
      selectColumns: [groupByValue, avgSelect],
    };

    params.filterParams = addStartEndDateParamValues(
      params.startDate,
      params.endDate,
      params.filterParams,
    );

    // execute the query and return the results
    const bqQueryGenerator = new BigQueryGenerator();
    const rows = await bqQueryGenerator.executeSelectQuery(
      bigQueryDb,
      sql,
      params.filters,
      params.filterParams,
    );
    if (!rows || !rows.length) {
      throw IctError.noContent();
    }

    return rows as FaultAvgDurationByStatusQueryStoreResult[];
  }

  /**
   * Retrieve the total number of faults per hour vs orderline throughput per hour for a specified date range.
   * @param startDate Start date for the date range.
   * @param endDate End date for the date range.
   * @returns Number of faults and completed orderlines in EquipmentFaultsChartQueryData format.
   */
  async getFaultsToThroughputSeries(
    startDate: string,
    endDate: string,
  ): Promise<EquipmentFaultsSeriesQueryData[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const goldNokInterval = this.dbProvider.bigQuery.getEdpFullTablePath(
      'gold_fault_nok_interval',
    );

    const goldPick = this.dbProvider.bigQuery.getEdpFullTablePath('gold_pick');

    const sql = `
        WITH
          timeFilteredFaults AS (
            SELECT COUNT (*) as faults, TIMESTAMP_TRUNC(fault_end_timestamp_utc, HOUR) AS hourOfTheDay
            FROM \`${goldNokInterval}\`
            WHERE fault_end_timestamp_utc >= @startDate
              AND fault_end_timestamp_utc <= @endDate
            GROUP BY hourOfTheDay ORDER BY hourOfTheDay),
          timeFilteredOrderLines AS (
            SELECT COUNT(pick_task_code) as throughput, TIMESTAMP_TRUNC(event_timestamp_utc, HOUR) AS hourOfTheDay
            FROM \`${goldPick}\`
            WHERE event_timestamp_utc >= @startDate
            AND event_timestamp_utc <= @endDate
            AND task_line_complete_ind = 1
            GROUP BY hourOfTheDay ORDER BY hourOfTheDay),
          allHours AS (
            SELECT timeFilteredFaults.hourOfTheDay FROM timeFilteredFaults
            UNION DISTINCT SELECT timeFilteredOrderLines.hourOfTheDay
            FROM timeFilteredOrderLines GROUP BY hourOfTheDay ORDER BY hourOfTheDay)
        SELECT allHours.hourOfTheDay, IFNULL(faults, 0) as faults, IFNULL(throughput, 0) as throughput
          FROM allHours LEFT JOIN timeFilteredFaults ON allHours.hourOfTheDay = timeFilteredFaults.hourOfTheDay
          LEFT JOIN timeFilteredOrderLines ON allHours.hourOfTheDay = timeFilteredOrderLines.hourOfTheDay
          ORDER BY allHours.hourOfTheDay;
      `;

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    // execute the query and return the results
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length) {
      throw IctError.noContent();
    }

    const seriesData: EquipmentFaultsSeriesQueryData[] = rows.map(row => ({
      hourOfTheDay: row.hourOfTheDay.value,
      faults: row.faults,
      throughput: row.throughput,
    }));

    return seriesData as EquipmentFaultsSeriesQueryData[];
  }

  /**
   * Retrieve data for fault areas.
   * @param startDate Start date for the date range.
   * @param endDate End date for the date range.
   * @returns Array of fault area objects.
   */
  async getFaultsAreasAsync(
    startDate: string,
    endDate: string,
  ): Promise<FaultsFacilityArea[]> {
    // build the SQL query to retrieve the fault areas from vizEvent
    // query the database for the fault areas in the specified date range
    const bigQueryDb = this.dbProvider.bigQuery;

    // with the data we currently have in BigQuery, there is not an effective way to get the area from the location, group, or other means
    // Defaulting to picking in the interim

    // get the fault count, operators, and downtime for each area (Only one available area for now)
    const sql = `
      SELECT 
        IFNULL(count(*), 0) as areaFaultCount,
        'picking' as areaId,
        work_area_uuid as id,
        COUNT(DISTINCT operator_uuid) as activeOperators,
        0 as lowRecOperators,
        COUNT(DISTINCT operator_uuid) + 1 as highRecOperators,
        "ok" as areaStatus,
        "" as identifier,
        "" as message,
        SUM(fault_duration_seconds) as faultDurationMS,
        'Picking' as areaName
      FROM \`${bigQueryDb.getFullTablePath('fct_fault')}\`
        WHERE FORMAT_TIMESTAMP('%Y-%m-%d', record_timestamp) >= @startDate
          AND FORMAT_TIMESTAMP('%Y-%m-%d', record_timestamp) <= @endDate
        GROUP BY work_area_uuid
    `;

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    // execute the query and return the results
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length) {
      throw IctError.noContent();
    }

    // TODO: maxFaultsAllowed and fault status should be configured as a result of the queried data and config data in a db, hardcoded for now
    const faultAreasData: FaultsFacilityArea[] = rows.map(
      (row: {
        id: string;
        areaName: string;
        areaStatus: HealthStatus;
        identifier: string;
        message: string;
        activeOperators: number;
        lowRecOperators: number;
        highRecOperators: number;
        areaFaultCount: number;
        faultDurationMS: number;
      }) => ({
        id: row.id,
        name: row.areaName,
        alertStatus: {
          status: row.areaStatus,
          identifier: row.identifier,
        },
        operators: {
          activeOperators: row.activeOperators,
          lowRecOperators: row.lowRecOperators,
          highRecOperators: row.highRecOperators,
        },
        faults: {
          totalFaults: row.areaFaultCount,
          maxFaultsAllowed: 5,
          downtimeMinutes: row.faultDurationMS / 60000, // convert milliseconds to minutes
          status: 'caution',
        },
      }),
    );

    return faultAreasData;
  }

  async getFaultsRecentEvents(): Promise<FaultEventQueryResponse[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const goldFaultNokInterval = this.dbProvider.bigQuery.getEdpFullTablePath(
      'gold_fault_nok_interval',
    );

    const sql = `
        SELECT fault_end_timestamp_utc AS _TimeUTC, 
          IFNULL(status_category_code, '') AS FLT_DESC, 
          CONCAT(IFNULL(SAFE_CAST(status_code AS STRING), ''), ' ', IFNULL(status_category_code, '')) AS Message, 
          CONCAT(IFNULL(area_code, ''), ' ', IFNULL(location_code, '')) AS Area,
        FROM  \`${goldFaultNokInterval}\` ff
        WHERE fault_end_timestamp_utc > TIMESTAMP_SUB(CURRENT_TIMESTAMP, INTERVAL 30 DAY) 
        ORDER BY _TimeUTC DESC
        LIMIT 100
      `;

    const sqlOptions: Query = {
      query: sql,
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length) {
      throw IctError.noContent();
    }

    return rows as FaultEventQueryResponse[];
  }

  async getRecentEvents(
    recentEventQueryParams: RecentEventQueryStoreParams,
  ): Promise<PaginatedResults<RecentEventQueryResponse[]>> {
    const bigQueryDb = this.dbProvider.bigQuery;
    const fct_fault = bigQueryDb.getFullTablePath('fct_fault');
    const dim_reason = bigQueryDb.getFullTablePath('dim_reason');
    const dim_device = bigQueryDb.getFullTablePath('dim_device');
    const templateSQL = `
      WITH  RecentEvents AS (SELECT
        record_timestamp as \`time\`,
        \`fct_fault\`.tenant as tenant, 
        \`fct_fault\`.facility as facility,
        \`dim_reason\`.reason_name as description, 
        'event' as eventType,
        \`dim_device\`.device_code as equipmentId,
        fault_start_date_time as eventStartTime, 
        fault_duration_seconds as totalTime,
        \`dim_reason\`.reason_code as fault_code 
      FROM
      \`${fct_fault}\` as fct_fault 
      INNER JOIN \`${dim_reason}\` as dim_reason
      ON fct_fault.fault_tag_reason_uuid = dim_reason.reason_uuid
      INNER JOIN \`${dim_device}\` as dim_device
      ON dim_device.device_uuid = fct_fault.device_uuid
      WHERE record_timestamp BETWEEN @startDate AND @endDate
      )`;

    // these are the only allowed column names that can be sorted/filtered by through input (untrusted) data
    const allowedColumnNames = [
      'time',
      'tenant',
      'facility',
      'description',
      'eventType',
      'equipmentId',
      'eventStartTime',
      'totalTime',
      'faultCode',
    ];

    const sql: SortingSelectQueryParams = {
      selectFromTable: 'RecentEvents',
      // eslint-disable-next-line object-shorthand
      allowedColumnNames: allowedColumnNames,
      cteSubquery: templateSQL,
      sortFields: recentEventQueryParams.sortField,
      page: recentEventQueryParams.page,
      limit: recentEventQueryParams.limit,
    };

    const startDate = recentEventQueryParams.startDate;
    const endDate = recentEventQueryParams.endDate;

    recentEventQueryParams.filterParams = addStartEndDateParamValues(
      startDate,
      endDate,
      recentEventQueryParams.filterParams,
    );

    const bqQueryGenerator = new BigQueryGenerator();

    const results: PaginatedResults<RecentEventQueryResponse[]> =
      await SQLQueryGenerator.paginatedQuery<RecentEventQueryResponse>(
        bqQueryGenerator,
        bigQueryDb,
        sql,
        recentEventQueryParams.filters,
        recentEventQueryParams.filterParams,
      );

    if (!results.totalResults) {
      throw IctError.noContent();
    }

    return results;
  }

  /**
   * Retrieve counts for faults and equipment movements  by area and 15 minute intervals
   * to be used for counts by area tiles or page faults graph.
   * @param startDate Start date for the date range.
   * @param endDate End date for the date range.
   * @returns Counts for faults and movements per 15 minute interval by area.
   */
  async getFaultAndEquipmentMovementsAsync(startDate: string, endDate: string) {
    // build the SQL query to retrieve the fault and movement counts from a BigQuery view
    // query the database for the faults in the specified date range in 15 minute intervals
    const bigQueryDb = this.dbProvider.bigQuery;
    const faultsViewName = this.dbProvider.bigQuery.getFullTablePath(
      'equipment_areas_downtimes',
    );

    const movementsViewName = this.dbProvider.bigQuery.getFullTablePath(
      'demo01_movementCount',
    );

    // get faults from the equipment areas downtimes
    // joined with the movement counts
    //  by areas in 15 minute intervals
    const sql = `
    WITH faults as (
      SELECT COUNT(*) as faultCount,
      CAST((TIMESTAMP_SECONDS(900 * DIV(UNIX_SECONDS(eventTime) + 450, 900))) AS STRING) as event_time,
      CASE
        WHEN MOD(faultDurationMS, 4) = 0 THEN 'PICKING'
        WHEN MOD(faultDurationMS, 4) = 1 THEN 'PACKING'
        WHEN MOD(faultDurationMS, 4) = 2 THEN 'SHIPPING'
        ELSE 'STORAGE'
      END AS area
      FROM \`${faultsViewName}\`
      GROUP BY event_time, area)
      SELECT IFNULL(movementCount, 0) as movementCount,
      IFNULL(faultCount, 0) as faultCount, mc.area, eventTime
      FROM \`${movementsViewName}\` as mc
      FULL JOIN faults ON mc.area = faults.area AND mc.eventTime = faults.event_time
      WHERE eventTime >= @startDate and eventTime <= @endDate
      ORDER BY eventTime ASC, area ASC
    `;

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    // execute the query and return the results
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    return rows as EquipmentFaultsMovementsQueryResponse[];
  }

  /**
   * Retrieve total downtime  by area
   * @param startDate Start date for the date range.
   * @param endDate End date for the date range.
   * @returns Total downtime  by area.
   */
  async getTotalDowntimes(startDate: string, endDate: string) {
    // build the SQL query to retrieve the fault areas from the downtimes view
    // query the database for the downtimes in the specified date range
    const bigQueryDb = this.dbProvider.bigQuery;

    // with the data we currently have in BigQuery, there is not an effective way to get the area from the location, group, or other means
    // methods of getting the area here are mocked (using mod operator)
    const sql = `
    SELECT sum(faultDurationMS) as downtimeMS,
    CASE
      WHEN MOD(faultDurationMS, 4) = 0 THEN 'PICKING'
      WHEN MOD(faultDurationMS, 4) = 1 THEN 'STORAGE'
      WHEN MOD(faultDurationMS, 4) = 2 THEN 'PACKING'
      ELSE 'SHIPPING'
    END as area
    FROM \`${bigQueryDb.getFullTablePath('equipment_areas_downtimes')}\`
    WHERE FORMAT_TIMESTAMP('%Y-%m-%d', eventTime) >= @startDate
      AND FORMAT_TIMESTAMP('%Y-%m-%d', eventTime) <= @endDate
    GROUP BY area`;

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    // execute the query and return the results
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    return rows as EquipmentFaultsDowntimeQueryResponse[];
  }

  /**
   * Retrieve percentage of movements that were completed without error  by area
   * @param startDate Start date for the date range.
   * @param endDate End date for the date range.
   * @returns Array of movement quality objects.
   */
  async getMovementQuality(
    startDate: string,
    endDate: string,
  ): Promise<MovementQualityQueryResponse[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const movementFaultsViewName = this.dbProvider.bigQuery.getFullTablePath(
      'demo01_movementFaultsCount',
    );

    const sql = `
    SELECT 100 - ((faultCount / movementCount) * 100) AS movementQualityPercentage, area
    FROM \`${movementFaultsViewName}\``;

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    return rows as MovementQualityQueryResponse[];
  }

  /**
   * Return avg flow rate based upon sum movments divided by number of hours within range.
   * @param startDate Start date for the date range.
   * @param endDate End date for the date range.
   * @returns Array of avg movements per hour by area.
   */
  async getEquipmentFlowRateAverage(
    startDate: string,
    endDate: string,
  ): Promise<EquipmentFlowRateDefinition[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const equipmentMovementsViewName =
      this.dbProvider.bigQuery.getFullTablePath('demo01_movementCount');

    const sql = `
    DECLARE rangeOfHours INT64;
    SET rangeOfHours = TIMESTAMP_DIFF(@endDate, @startDate, HOUR);

    SELECT ROUND(SUM(movementCount)/rangeOfHours, 2) as avgHourlyFlowRate, area, rangeOfHours
    FROM \`${equipmentMovementsViewName}\`
    WHERE eventTime >= @startDate AND eventTime <= @endDate GROUP BY area
    `;

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    return rows as EquipmentFlowRateDefinition[];
  }

  async getEquipmentActiveFaultsData(): Promise<
    EquipmentActiveFaultsDefinition[]
  > {
    const bigQueryDb = this.dbProvider.bigQuery;
    const tableName = bigQueryDb.getFullTablePath('fct_fault');
    const dimTableName = bigQueryDb.getFullTablePath('dim_reason');

    // Query the database for active faults data
    const sql = `
      SELECT

      record_timestamp,
      fault_duration_seconds,
      reasons.reason_name,

        CASE
          WHEN MOD(fault_duration_seconds, 4) = 0 THEN 'PICKING'
          WHEN MOD(fault_duration_seconds, 4) = 1 THEN 'PACKING'
          WHEN MOD(fault_duration_seconds, 4) = 2 THEN 'SHIPPING'
          ELSE 'STORAGE'
        END AS area



        FROM ${tableName} faults
          INNER JOIN
            ${dimTableName} reasons ON faults.fault_tag_reason_uuid = reasons.reason_uuid
          ORDER BY
            record_timestamp DESC
        LIMIT 100
      `;

    const sqlOptions: Query = {
      query: sql,
    };

    // Execute the query and retrieve the results
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length) {
      throw IctError.noContent();
    }

    const activeFaultAreasData: EquipmentActiveFaultsDefinition[] = rows.map(
      (row: {
        fault_duration_seconds: number;
        reason_name: string;
        area: string;
      }) => ({
        duration: row.fault_duration_seconds,
        deviceReason: row.reason_name,
        area: row.area,
      }),
    );

    return activeFaultAreasData;
  }

  /**
   * Retrieve summary information for a Workstation
   * @param startDate Start date for the date range.
   * @param endDate End date for the date range.
   * @returns Workstation summary information
   */

  async getWorkstationSummary(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    startDate: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    endDate: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    workstationId: string,
  ): Promise<EquipmentSummaryWorkstationDefinition> {
    // TODO: implement call to DB to get summary information for a Workstation
    // TODO: Include 204 no content response when no data is available
    return {
      status: HealthStatus.Ok,
      operatorId: 1534,
      iatDonorTotes: 0.2,
      linesPerHour: 110,
      activeTime: 'PT8H30M',
      idleTime: 'PT1H15M',
      starvedTime: 'PT15M',
    };
  }

  /**
   * Retrieve summary information for an Aisle
   * @param startDate Start date for the date range.
   * @param endDate End date for the date range.
   * @returns Aisle summary information
   */
  async getAisleSummary(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    aisleId: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    startDate: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    endDate: string,
  ): Promise<EquipmentSummaryAisleDefinition> {
    // TODO: implement call to DB to get summary information for an Aisle
    // TODO: Include 204 no content response when no data is available
    return {
      status: HealthStatus.Ok,
      totalMovements: 40811,
      storageUtilization: 0.98,
      storedTotesPerHour: 120,
      inventoryTotes: 1800,
      retrievedTotesPerHour: 300,
      orderTotes: 2000,
    };
  }

  /**
   * Gets the operator activity for the workstation for the last 24 hours.
   * @param workstationId the id of the workstation
   * @returns operator activity for  the workstation for the past 24 hours
   */
  async getWorkstationOperatorActivity(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    workstationId: string,
  ): Promise<EquipmentWorkstationOperatorActivityQueryResponse[]> {
    // TODO: Create query to get data from DB
    // TODO2: Remove skip on test
    // TODO: Include 204 no content response when no data is available
    return Promise.resolve([
      {
        operator_id: '001531',
        start_time: new BigQueryDate('2023-04-22T07:46:00.000Z'),
        end_time: new BigQueryDate('2023-04-22T10:00:00.000Z'),
        idle_time: 900,
        starved_time: 900,
        blocked_time: 1500,
        line_rates: 75,
        weighted_line_rates: 100,
      },
      {
        operator_id: '001532',
        start_time: new BigQueryDate('2023-04-22T10:15:00.000Z'),
        end_time: new BigQueryDate('2023-04-22T12:00:00.000Z'),
        idle_time: 900,
        starved_time: 900,
        blocked_time: 1500,
        line_rates: 75,
        weighted_line_rates: 100,
      },
      {
        operator_id: '001533',
        start_time: new BigQueryDate('2023-04-22T12:30:00.000Z'),
        end_time: new BigQueryDate('2023-04-22T15:00:00.000Z'),
        idle_time: 900,
        starved_time: 900,
        blocked_time: 1500,
        line_rates: 75,
        weighted_line_rates: 100,
      },
      {
        operator_id: '001534',
        start_time: new BigQueryDate('2023-04-22T15:30:00.000Z'),
        end_time: null,
        idle_time: 900,
        starved_time: 900,
        blocked_time: 1500,
        line_rates: 75,
        weighted_line_rates: 100,
      },
    ]);
  }

  /**
   * Retrieve the outbound rate metric
   */
  async getOutboundRate(): Promise<EquipmentOutboundRateQueryResponse> {
    // TODO: Call BigQuery to retrieve data
    // TODO: Include 204 no content response when no data is available
    return {
      outboundDivertsHour: 600,
    };
  }

  /**
   * Gets the last 24 hours of historical line rates for a workstation as a series.
   * @param workstationId workstation identifier
   * @returns Last 24 hours of historical line rates for the workstation as a series
   */
  async getEquipmentWorkstationLineRatesSeries(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    workstationId: string,
  ): Promise<EquipmentWorkstationLineRatesSeriesQueryResponse[]> {
    // TODO: Create query to get data from DB
    // TODO2: Remove skip on test
    // TODO: Include 204 no content response when no data is available
    return Promise.resolve([
      {
        date_time: new BigQueryDate('2023-01-08T08:00:00.000Z'),
        line_rate: 72,
      },
      {
        date_time: new BigQueryDate('2023-01-08T09:00:00.000Z'),
        line_rate: 69,
      },
      {
        date_time: new BigQueryDate('2023-01-08T10:00:00.000Z'),
        line_rate: 74,
      },
      {
        date_time: new BigQueryDate('2023-01-08T11:00:00.000Z'),
        line_rate: 78,
      },
      {
        date_time: new BigQueryDate('2023-01-08T12:00:00.000Z'),
        line_rate: 80,
      },
      {
        date_time: new BigQueryDate('2023-01-08T13:00:00.000Z'),
        line_rate: 85,
      },
      {
        date_time: new BigQueryDate('2023-01-08T14:00:00.000Z'),
        line_rate: 94,
      },
      {
        date_time: new BigQueryDate('2023-01-08T15:00:00.000Z'),
        line_rate: 98,
      },
      {
        date_time: new BigQueryDate('2023-01-08T16:00:00.000Z'),
        line_rate: 99,
      },
      {
        date_time: new BigQueryDate('2023-01-08T17:00:00.000Z'),
        line_rate: 103,
      },
    ]);
  }

  /**
   * Gets series data for starved and blocked times for a workstation.
   * @param workstationId Workstation identifier
   */
  async getEquipmentWorkstationStarvedBlockedSeries(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    workstationId: string,
  ): Promise<EquipmentWorkstationStarvedBlockedTimeQueryResponse[]> {
    // TODO: Implement db query
    // TODO2: Remove skip on test
    // TODO: Include 204 no content response when no data is available
    return Promise.resolve([
      {
        date_time: new BigQueryDate('2022-01-01T08:00:00.000Z'),
        starved_time_percentage: 16,
        blocked_time_percentage: 11,
      },
      {
        date_time: new BigQueryDate('2022-01-01T09:00:00.000Z'),
        starved_time_percentage: 17,
        blocked_time_percentage: 12,
      },
      {
        date_time: new BigQueryDate('2022-01-01T10:00:00.000Z'),
        starved_time_percentage: 15,
        blocked_time_percentage: 10,
      },
      {
        date_time: new BigQueryDate('2022-01-01T11:00:00.000Z'),
        starved_time_percentage: 16,
        blocked_time_percentage: 11,
      },
      {
        date_time: new BigQueryDate('2022-01-01T12:00:00.000Z'),
        starved_time_percentage: 17,
        blocked_time_percentage: 12,
      },
      {
        date_time: new BigQueryDate('2022-01-01T13:00:00.000Z'),
        starved_time_percentage: 18,
        blocked_time_percentage: 13,
      },
      {
        date_time: new BigQueryDate('2022-01-01T14:00:00.000Z'),
        starved_time_percentage: 16,
        blocked_time_percentage: 11,
      },
      {
        date_time: new BigQueryDate('2022-01-01T15:00:00.000Z'),
        starved_time_percentage: 15,
        blocked_time_percentage: 10,
      },
      {
        date_time: new BigQueryDate('2022-01-01T16:00:00.000Z'),
        starved_time_percentage: 13,
        blocked_time_percentage: 8,
      },
      {
        date_time: new BigQueryDate('2022-01-01T17:00:00.000Z'),
        starved_time_percentage: 14,
        blocked_time_percentage: 9,
      },
    ]);
  }

  /**
   * Get aisle movement data for a workstation.
   * @param workstationId Id of the workstation
   * @returns Aisle movement data for the workstation
   */
  async getEquipmentWorkstationAisleMovementData(
    /** Id of the workstation */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    workstationId: string,
  ): Promise<EquipmentWorkstationAisleMovementDataQueryResponse> {
    // TODO: Implement db query
    // TODO2: Remove skip on test
    // TODO: Include 204 no content response when no data is available
    return Promise.resolve({
      retrieval: 17548,
      storage: 10517,
      positioning: 5688,
      iat: 5282,
      shuffle: 1435,
      bypass: 341,
    });
  }

  async getEquipmentWorkstationActiveFaults(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    locationId: string,
  ): Promise<EquipmentWorkstationActiveFaultsQueryResponse[]> {
    // TODO: Query the db
    // TODO: Include 204 no content response when no data is available
    return Promise.resolve([
      {
        description: 'Maintenance Gate Level 1',
        start_time: new BigQueryDate('2023-11-23T14:30:00.000Z'),
        duration: 720,
        fault_id: 'F1234',
      },
    ]);
  }

  /**
   * Single store function to get list of available fault filters for the filterColumnName given in the parameters
   */
  async getEquipmentFaultsFilterTypeList(
    params: FaultsFilterTypeListStoreParams,
  ): Promise<FaultsFilterTypeListStoreResult[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const endDate = params.endDate ?? new Date(); // if no end date, use the current datetime
    const startDate =
      params.startDate ??
      DateTime.fromJSDate(endDate).minus({days: 30}).toJSDate(); // if no start date, use 30 days before the endDate

    const goldFaultNokInterval = this.dbProvider.bigQuery.getEdpFullTablePath(
      'gold_fault_nok_interval',
    );

    const cte = `
        WITH faults AS (
          SELECT
            ${this.getEdpFaultFilterColumns()}
          FROM
            ${goldFaultNokInterval} gold_fault_nok_interval
          WHERE
            fault_end_timestamp_utc BETWEEN @startDate AND @endDate
            AND REGEXP_CONTAINS(gold_fault_nok_interval.equipment_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
        )
      `;

    const allowedColumnNames = [
      'device_functional_type',
      'device_code',
      'reason_name',
      'aisle',
      'level',
      'faultFilter',
    ];

    const distinctColumnNames = [params.filterColumnName];

    const sortFields: SortField[] = [
      {
        columnName: 'faultFilter',
        isDescending: false,
      },
    ];

    const sql: SortingSelectQueryParams = {
      selectFromTable: 'faults',
      allowedColumnNames,
      cteSubquery: cte,
      sortFields,
      selectColumns: [
        new SQLSelectColumn(params.filterColumnName, 'faultFilter'),
      ],
      distinctColumns: distinctColumnNames,
    };

    params.filterParams = addStartEndDateParamValues(
      startDate,
      endDate,
      params.filterParams,
    );

    // execute the query and return the results
    const bqQueryGenerator = new BigQueryGenerator();
    const rows = await bqQueryGenerator.executeSelectQuery(
      bigQueryDb,
      sql,
      params.filters,
      params.filterParams,
    );
    if (!rows || !rows.length) {
      throw IctError.noContent();
    }

    return rows as FaultsFilterTypeListStoreResult[];
  }

  /**
   *
   * @param params Filters
   * @returns Movement count series data and movements per fault series data
   */
  async getEquipmentFaultsMovementsSeries(
    params: FaultsMovementsSeriesStoreParams,
  ): Promise<FaultsMovementsSeriesStoreResult[]> {
    const siteTimeZoneSettingValue = await this.configStore
      .findMostRelevantSettingForUser(
        this.context.userId,
        undefined,
        'site-time-zone',
      )
      .then(setting => {
        return setting?.value;
      });

    if (!siteTimeZoneSettingValue) {
      throw IctError.internalServerError(
        'Config for site time zone setting not found',
      );
    }

    const bigQueryDb = this.dbProvider.bigQuery;

    const allowedColumnNames = [
      'eventInterval',
      'device_functional_type',
      'device_code',
      'reason_name',
      'aisle',
      'level',
      'movementCount',
      'movementsPerFault',
    ];

    // filters are manually applied to the cte so we can filter the data before aggregation and grouping into time buckets
    // separate filters are used for faults and movements, since movements can't be filtered by reason_name
    let faultsWhereClause = '';
    let movementsWhereClause = '';
    if (params.faultFilters) {
      SQLQueryGenerator.validateFilterColumnNames(
        params.faultFilters,
        allowedColumnNames,
      );
      faultsWhereClause = `WHERE ${params.faultFilters.buildWhereContents()}`;
    }
    if (params.movementFilters) {
      SQLQueryGenerator.validateFilterColumnNames(
        params.movementFilters,
        allowedColumnNames,
      );
      movementsWhereClause = `WHERE ${params.movementFilters.buildWhereContents()}`;
    }

    const goldNokInterval = this.dbProvider.bigQuery.getEdpFullTablePath(
      'gold_fault_nok_interval',
    );
    const goldMultishuttleMovement =
      this.dbProvider.bigQuery.getEdpFullTablePath(
        'gold_movement_multishuttle',
      );

    const cte = `
        WITH 
          -- create buckets for each day in the date range to cover edge dates and gaps
          emptyTimeBuckets AS (
            SELECT * 
            FROM UNNEST(
              GENERATE_TIMESTAMP_ARRAY(
                TIMESTAMP(DATE(@startDate, @siteTimezone), @siteTimezone),
                TIMESTAMP(DATE(@endDate, @siteTimezone), @siteTimezone),
                INTERVAL 1 DAY)
            ) as timeBucket
        ),
        faults AS (
          SELECT
            fault_end_timestamp_utc AS record_timestamp,
            ${this.getEdpFaultFilterColumns()}
          FROM
            ${goldNokInterval} fct_fault
          WHERE
            fault_end_timestamp_utc BETWEEN @startDate AND @endDate
            AND REGEXP_CONTAINS(equipment_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
        ),
        filteredFaults AS (
          SELECT * FROM faults
          ${faultsWhereClause}
        ),
        movements AS (
          SELECT
            movement_end_timestamp_utc AS record_timestamp,
            IFNULL(equipment_type, 'N/A') as device_functional_type,
            IFNULL(equipment_code, 'N/A') as device_code,
            IFNULL(SUBSTRING(equipment_code, 5, 2), 'N/A') AS aisle,
            IFNULL(SUBSTRING(equipment_code, 9, 2), 'N/A') AS level
          FROM
              ${goldMultishuttleMovement} fct_movement
          WHERE
            movement_end_timestamp_utc BETWEEN @startDate AND @endDate
            AND REGEXP_CONTAINS(equipment_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
        ),
        filteredMovements AS (
          SELECT * FROM movements
          ${movementsWhereClause}
        ),
        movementBuckets AS (
          SELECT
            TIMESTAMP(DATE(record_timestamp, @siteTimezone), @siteTimezone) as eventInterval,
            COUNT(*) as movementCount
          FROM filteredMovements
          GROUP BY eventInterval
        ),
        -- join the data with the empty time buckets to fill edges and gaps
        filledMovementBuckets AS (
          SELECT
            timeBucket,
            IFNULL(movementCount, 0) as movementCount
          FROM movementBuckets
          RIGHT JOIN emptyTimeBuckets on movementBuckets.eventInterval = emptyTimeBuckets.timeBucket
        ),
        faultBuckets AS (
          SELECT
            TIMESTAMP(DATE(record_timestamp, @siteTimezone), @siteTimezone) as eventInterval,
            COUNT(*) as faultCount
          FROM filteredFaults
          GROUP BY eventInterval
        ),
        -- join the data with the empty time buckets to fill edges and gaps
        filledFaultBuckets AS (
          SELECT
            timeBucket,
            IFNULL(faultCount, 0) as faultCount
          FROM faultBuckets
          RIGHT JOIN emptyTimeBuckets on faultBuckets.eventInterval = emptyTimeBuckets.timeBucket
        ),
        movementsPerFaultBuckets AS (
          SELECT
            fmb.timeBucket as eventInterval,
            movementCount,
            IFNULL(SAFE_DIVIDE(movementCount, faultCount), 0) as movementsPerFault
          FROM filledMovementBuckets fmb
          JOIN filledFaultBuckets ffb ON ffb.timeBucket = fmb.timeBucket
    )`;

    const sortFields: SortField[] = [
      {
        columnName: 'eventInterval',
        isDescending: false,
      },
    ];

    const sql: SortingSelectQueryParams = {
      selectFromTable: 'movementsPerFaultBuckets',
      allowedColumnNames,
      cteSubquery: cte,
      sortFields,
      selectColumns: [
        new SQLSelectColumn('eventInterval'),
        new SQLSelectColumn('movementCount'),
        new SQLSelectColumn('movementsPerFault'),
      ],
    };

    params.filterParams = addStartEndDateParamValues(
      params.startDate ?? new Date(),
      params.endDate ?? new Date(),
      params.filterParams,
    );
    params.filterParams.siteTimezone = siteTimeZoneSettingValue;

    // execute the query and return the results
    const bqQueryGenerator = new BigQueryGenerator();
    const rows = await bqQueryGenerator.executeSelectQuery(
      bigQueryDb,
      sql,
      undefined,
      params.filterParams,
    );
    if (!rows || !rows.length) {
      throw IctError.noContent();
    }

    return rows as FaultsMovementsSeriesStoreResult[];
  }

  /**
   * Retrieve the total number of inventory sku for a specified date range.
   * @param startDate Start date for the date range.
   * @param endDate End date for the date range.
   * @returns Array of Inventory objects for the specified date range
   */
  async getEquipmentFaultsList(
    params: EquipmentFaultsListStoreParams,
  ): Promise<PaginatedResults<EquipmentFaultsListQueryResponse[]>> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const goldFaultNokInterval = this.dbProvider.bigQuery.getEdpFullTablePath(
      'gold_fault_nok_interval',
    );

    const cte = `
      WITH faults AS (
        SELECT
          FORMAT_TIMESTAMP('%Y-%m-%dT%H:%M:%SZ', gold_fault_nok_interval.fault_end_timestamp_utc) AS timestamp, -- use UTC or local?
          SAFE_DIVIDE(gold_fault_nok_interval.fault_duration_milliseconds, 60 * 1000) AS durationMinutes,
          ${this.getEdpFaultFilterColumns()}
         FROM
          ${goldFaultNokInterval} gold_fault_nok_interval
        WHERE
          fault_end_timestamp_utc BETWEEN @startDate AND @endDate
          AND REGEXP_CONTAINS(gold_fault_nok_interval.equipment_code, r'MSAI[0-9][0-9]..[0-9][0-9]')
        )
      `;

    // these are the only allowed column names that can be sorted/filtered by through input (untrusted) data
    const allowedColumnNames = [
      'timestamp',
      'durationMinutes',
      'device_functional_type',
      'device_code',
      'reason_name',
      'aisle',
      'level',
    ];

    const sql: SortingSelectQueryParams = {
      selectFromTable: 'faults',
      allowedColumnNames,
      cteSubquery: cte,
      sortFields: params.sortFields,
      page: params.page,
      limit: params.limit,
    };

    params.filterParams = addStartEndDateParamValues(
      params.startDate,
      params.endDate,
      params.filterParams,
    );

    const bqQueryGenerator = new BigQueryGenerator();
    const faultsList: PaginatedResults<EquipmentFaultsListQueryResponse[]> =
      await SQLQueryGenerator.paginatedQuery<EquipmentFaultsListQueryResponse>(
        bqQueryGenerator,
        bigQueryDb,
        sql,
        params.filters,
        params.filterParams,
      );

    return faultsList;
  }

  /**
   * These columns are selected for every picking buffer endpoint, and the exact formatting and naming scheme must be the same across all of these endpoints
   *  in order for the page level filtering to work properly.
   * Storing the columns that are selected here so that changes can be made in one place.
   */
  getFaultFilterColumns() {
    return `IFNULL(dim_device.device_functional_type, 'N/A') as device_functional_type,
        IFNULL(dim_device.device_code, 'N/A') as device_code,
        IFNULL(fault_code, 'N/A') as reason_name,
        IFNULL(SUBSTRING(dim_device.device_code, 5, 2), 'N/A') AS aisle,
        IFNULL(SUBSTRING(dim_device.device_code, 9, 2), 'N/A') AS level`;
  }

  /**
   * These columns are selected for every picking buffer endpoint, and the exact formatting and naming scheme must be the same across all of these endpoints
   * in order for the page level filtering to work properly.
   * Storing the columns that are selected here so that changes can be made in one place.
   */
  getEdpFaultFilterColumns() {
    return `IFNULL(functional_area_type, 'N/A') AS device_functional_type,
        IFNULL(equipment_code, 'N/A') AS device_code,
        IFNULL(status_category_code, 'N/A') AS reason_name,
        IFNULL(SUBSTRING(equipment_code, 5, 2), 'N/A') AS aisle,
        IFNULL(SUBSTRING(equipment_code, 9, 2), 'N/A') AS level`;
  }
}
