{"name": "ict-equipment-api", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/ict-equipment-api/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "ict-equipment-api:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "ict-equipment-api:build:development"}, "production": {"buildTarget": "ict-equipment-api:build:production"}}}}}