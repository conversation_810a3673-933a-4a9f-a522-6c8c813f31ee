# ICT Simulation API

## Overview

The ICT Simulation API provides simulation capabilities for the Control Tower platform. This service enables users to create, manage, and execute simulations for testing and analysis purposes.

## Purpose

This API serves as the logic layer for handling business logic for the Simulation within Control Tower, providing comprehensive simulation management and execution capabilities.

## Key Features

- **Simulation Management**: Create, update, and manage simulation configurations
- **Job Execution**: Execute simulation jobs with various parameters
- **Output Processing**: Process and retrieve simulation results
- **Performance Analysis**: Analyze simulation performance and results
- **Batch Processing**: Support for batch simulation operations

## API Endpoints

### Health Check

- `GET /health/basic` - Basic service health check

### Simulation Jobs

- `POST /simulation/jobs` - Create new simulation job
- `GET /simulation/jobs` - List simulation jobs
- `GET /simulation/jobs/{id}` - Get specific simulation job
- `PUT /simulation/jobs/{id}` - Update simulation job
- `DELETE /simulation/jobs/{id}` - Delete simulation job

### Simulation Output

- `GET /simulation/output/{jobId}` - Get simulation output
- `POST /simulation/output/process` - Process simulation output
- `GET /simulation/output/analytics` - Get output analytics

## Architecture

The service follows the standard Control Tower API architecture:

```bash
src/
├── controllers/     # TSOA controllers with route definitions
│   ├── simulation-health-check-controller.ts
│   ├── simulation-jobs-controller.ts
│   └── simulation-output-controller.ts
├── services/        # Business logic services
├── stores/          # Data access layer
├── defs/           # TypeScript type definitions
├── test/           # Unit tests
└── index.ts        # Service entry point
```

## Dependencies

- **Database**: PostgreSQL for storing simulation configurations and results
- **Redis**: Caching for simulation jobs and results
- **File Storage**: Storage for simulation output files
- **Message Queue**: For asynchronous job processing

## Environment Variables

- `AUTH_AUDIENCE`: Authentication audience for JWT validation
- `AUTH_DOMAIN`: Auth0 domain for authentication
- `DATABASE_URL`: PostgreSQL connection string
- `REDIS_URL`: Redis connection string
- `STORAGE_BUCKET`: File storage bucket for simulation outputs

## Development

### Prerequisites

- Node.js >= 22.0.0
- Yarn package manager
- PostgreSQL database
- Redis instance
- File storage access

### Local Development

```bash
# Install dependencies
yarn install

# Start development server
yarn watch

# Run tests
yarn test

# Build for production
yarn build
```

### Testing

```bash
# Run all tests
yarn test

# Run tests in watch mode
yarn test:watch

# Run linting
yarn lint
```

## Deployment

The service is containerized using Docker and can be deployed to Google Cloud Run or other container platforms.

### Docker Build

```bash
docker build -t ict-simulation-api .
```

### Environment Configuration

Ensure all required environment variables are configured for the deployment environment.

## Monitoring

- **Health Checks**: Available at `/health/basic`
- **Metrics**: Service metrics exported for monitoring
- **Logging**: Structured JSON logging with correlation IDs

## Use Cases

### Testing and Validation

- System behavior testing
- Performance validation
- Scenario analysis
- Risk assessment

### Training and Education

- Operator training simulations
- Process optimization
- Decision support

### Research and Development

- Algorithm testing
- Model validation
- Performance benchmarking

## Simulation Types

### Process Simulations

- Order processing workflows
- Inventory management scenarios
- Equipment utilization analysis

### Performance Simulations

- Throughput analysis
- Capacity planning
- Bottleneck identification

### Scenario Simulations

- What-if analysis
- Risk assessment
- Optimization scenarios

## Related Services

- **ict-orders-api**: Order data for simulation scenarios
- **ict-inventory-api**: Inventory data for simulation models
- **ict-equipment-api**: Equipment data for simulation parameters
- **ict-operators-api**: Operator data for simulation scenarios
- **ict-workstation-api**: Workstation data for simulation models
