export interface SimulationJob {
  jobId: string;
  events: Event[];
  tags: string[];
  lastUpdate: string;
  tasks: Tasks;
  createTime: string;
  userId: string;
  taskCount: number;
  state: string;
  outputReady: boolean;
  attributes: Attributes;
  timeTaken: number | null;
  uploadId: string;
}

interface Event {
  eventType: string;
  state: string;
  timestamp: string;
  taskIndex: number | null;
  jobId: string;
  randomSeeds?: number[];
  tags?: string[];
  computeResources?: ComputeResources;
  extraArgs?: ExtraArgs | null;
  taskCount?: number;
  userId?: string;
  uploadId?: string;
  runEnv?: string | null;
  runnerBackend?: string;
  unifiedRunnerVersion?: null;
  taskExitCode?: number;
}

interface ComputeResources {
  maxDuration: string | null;
  preemptible: boolean;
  instanceType: string | null;
  memoryPerTaskMb: number | null;
  cpuPerTask: number | null;
}

interface ExtraArgs {
  simulationTime?: string;
  variant: string;
  runWithDocker: boolean;
  solution: string;
  type: string;
  version: string;
}

interface Tasks {
  [taskIndex: string]: Task;
}

interface Task {
  taskIndex: number;
  lastUpdate: string;
  state: string;
  timeTaken: number | null;
  visState: string;
  exitCode: number;
}

interface Attributes {
  randomSeeds: number[];
  uploadId: string;
  runnerBackend: string;
}
