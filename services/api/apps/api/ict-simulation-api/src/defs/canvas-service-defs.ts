export interface CanvasJob {
  job_id: string;
  events: Event[];
  tags: string[];
  last_update: string;
  tasks: Tasks;
  create_time: string;
  user_id: string;
  task_count: number;
  state: string;
  output_ready: boolean;
  attributes: Attributes;
  time_taken: number | null;
  upload_id: string;
}

interface Event {
  event_type: string;
  state: string;
  timestamp: string;
  task_index: number | null;
  job_id: string;
  random_seeds?: number[];
  tags?: string[];
  compute_resources?: ComputeResources;
  extra_args?: ExtraArgs | null;
  task_count?: number;
  user_id?: string;
  upload_id?: string;
  run_env?: string | null;
  runner_backend?: string;
  unified_runner_version?: null;
  task_exit_code?: number;
}

interface ComputeResources {
  max_duration: string | null;
  preemptible: boolean;
  instance_type: string | null;
  memory_per_task_mb: number | null;
  cpu_per_task: number | null;
}

interface ExtraArgs {
  simulation_time?: string;
  variant: string;
  run_with_docker: boolean;
  solution: string;
  type: string;
  version: string;
}

interface Tasks {
  [taskIndex: string]: Task;
}

interface Task {
  task_index: number;
  last_update: string;
  state: string;
  time_taken: number | null;
  vis_state: string;
  exit_code: number;
}

interface Attributes {
  random_seeds: number[];
  upload_id: string;
  runner_backend: string;
}
