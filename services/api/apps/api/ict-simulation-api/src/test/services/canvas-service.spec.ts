import sinon from 'sinon';
import axios from 'axios';
import {expect} from 'chai';
import {Readable} from 'stream';
import {
  Container,
  ContextService,
  Environment,
  GCPSecretManager,
  IctError,
  WinstonLogger,
} from 'ict-api-foundations';
import {CanvasService} from '../../services/canvas-service.ts';
import {CanvasJob} from '../../defs/canvas-service-defs.ts';

describe('CanvasService', () => {
  let secretManagerStub: sinon.SinonStubbedInstance<GCPSecretManager>;
  let axiosGetStub: sinon.SinonStub;
  let contextService: ContextService;
  let loggerService: WinstonLogger;
  let canvasService: CanvasService;

  beforeEach(() => {
    contextService = new ContextService();
    sinon.stub(contextService, 'datasetId').get(() => 'test-dataset');
    loggerService = new WinstonLogger(contextService);

    canvasService = new CanvasService(loggerService);
    axiosGetStub = sinon.stub(axios, 'get');
    process.env.ENVIRONMENT = Environment.local;
    process.env.SIMULATION_API_URL = 'fake_url';
    process.env.SIMULATION_API_KEY = 'fake_key';
    secretManagerStub = sinon.createStubInstance(GCPSecretManager);
    secretManagerStub.get.resolves('fake_secret');
    Container.set(GCPSecretManager, secretManagerStub);
  });

  afterEach(() => {
    delete process.env.SIMULATION_API_URL;
    delete process.env.SIMULATION_API_KEY;
    delete process.env.ENVIRONMENT;
    sinon.restore();
  });

  describe('getJobs', () => {
    const mockJobs: CanvasJob[] = [
      {
        job_id: '1',
        events: [],
        tags: [],
        last_update: '2025-01-01T00:00:00.000Z',
        tasks: {},
        create_time: '2025-01-01T00:00:00.000Z',
        user_id: 'test user id',
        task_count: 0,
        state: 'test state',
        output_ready: false,
        attributes: {
          random_seeds: [],
          upload_id: 'test attribute upload id',
          runner_backend: 'test runner',
        },
        time_taken: null,
        upload_id: 'test upload id',
      },
    ];
    it('should get jobs from the canvas API', async () => {
      axiosGetStub.resolves({
        status: 200,
        data: {
          jobs: mockJobs,
        },
      });
      const results = await canvasService.getJobs();
      expect(results).to.deep.equal(mockJobs);
    });

    it('should return an empty array for any errors', async () => {
      axiosGetStub.resolves({status: 500});
      const results = await canvasService.getJobs();
      expect(results).to.deep.equal([]);
    });
  });
  describe('getOutput', () => {
    const jobId = 'job123';
    const taskIndex = 0;
    const fileName = 'output.csv';

    it('should return a readable stream for sim-events file type', async () => {
      const mockFileLinkUrl = `fake_url/jobs/${jobId}/${taskIndex}/files-link/${fileName}`;
      const mockActualFileUrl = 'http://example.com/actual-sim-event-file.zip';
      const mockStreamData = 'sim event data';
      const mockStream = new Readable({
        read() {
          this.push(mockStreamData);
          this.push(null);
        },
      });

      axiosGetStub
        .withArgs(mockFileLinkUrl, {
          headers: {'X-API-Key': 'fake_key'},
        })
        .resolves({status: 200, data: {url: mockActualFileUrl}});

      axiosGetStub
        .withArgs(mockActualFileUrl, {responseType: 'stream'})
        .resolves({status: 200, data: mockStream});

      const result = await canvasService.getOutput(
        jobId,
        taskIndex,
        fileName,
        'sim-events',
      );

      expect(result).to.equal(mockStream);
      sinon.assert.calledWith(axiosGetStub, mockFileLinkUrl, {
        headers: {'X-API-Key': 'fake_key'},
      });
      sinon.assert.calledWith(axiosGetStub, mockActualFileUrl, {
        responseType: 'stream',
      });
    });

    it('should parse and return CSV data for summary file type', async () => {
      const effectiveFileName = `stats/${fileName}`;
      const mockFileLinkUrl = `fake_url/jobs/${jobId}/${taskIndex}/files-link/${effectiveFileName}`;
      const mockActualFileUrl = 'http://example.com/actual-summary-file.csv';
      const csvData = 'header\\nvalue\\n';
      const expectedParsedCsv = [{header: 'value'}];

      axiosGetStub
        .withArgs(mockFileLinkUrl, {
          headers: {'X-API-Key': 'fake_key'},
        })
        .resolves({status: 200, data: {url: mockActualFileUrl}});

      axiosGetStub
        .withArgs(mockActualFileUrl, {responseType: 'text'})
        .resolves({status: 200, data: csvData});

      const result = await canvasService.getOutput(
        jobId,
        taskIndex,
        fileName,
        'summary',
      );

      expect(result).to.deep.equal(expectedParsedCsv);
      sinon.assert.calledWith(axiosGetStub, mockFileLinkUrl, {
        headers: {'X-API-Key': 'fake_key'},
      });
      sinon.assert.calledWith(axiosGetStub, mockActualFileUrl, {
        responseType: 'text',
      });
    });

    it('should throw InternalServerError for non-200 status from API when fetching file content', async () => {
      const mockFileLinkUrl = `fake_url/jobs/${jobId}/${taskIndex}/files-link/${fileName}`;
      const mockActualFileUrl = 'http://example.com/actual-file-to-fail.dat';

      axiosGetStub
        .withArgs(mockFileLinkUrl, {
          headers: {'X-API-Key': 'fake_key'},
        })
        .resolves({status: 200, data: {url: mockActualFileUrl}});

      axiosGetStub
        .withArgs(mockActualFileUrl, {responseType: 'stream'})
        .resolves({status: 500, data: null});

      try {
        await canvasService.getOutput(jobId, taskIndex, fileName, 'sim-events');
        expect.fail('Expected getOutput to throw but it did not.');
      } catch (error: unknown) {
        expect(error).to.be.instanceOf(IctError);
        if (error instanceof IctError) {
          expect(error.statusCode).to.equal(500);
          expect(error.message).to.contain(
            `Failed to download file ${fileName} from ${mockActualFileUrl} (Status: 500)`,
          );
        } else {
          expect.fail(`Caught error was not an instance of IctError: ${error}`);
        }
      }
    });

    it('should throw InternalServerError if fetching file URL fails', async () => {
      const mockFileLinkUrl = `fake_url/jobs/${jobId}/${taskIndex}/files-link/${fileName}`;

      axiosGetStub
        .withArgs(mockFileLinkUrl, {
          headers: {'X-API-Key': 'fake_key'},
        })
        .resolves({status: 503, data: {message: 'Service Unavailable'}});

      try {
        await canvasService.getOutput(jobId, taskIndex, fileName, 'sim-events');
        expect.fail('Expected getOutput to throw but it did not.');
      } catch (error: unknown) {
        expect(error).to.be.instanceOf(IctError);
        if (error instanceof IctError) {
          expect(error.statusCode).to.equal(500);
          expect(error.message).to.contain(
            `Failed to retrieve a valid file URL for ${fileName}`,
          );
        } else {
          expect.fail(`Caught error was not an instance of IctError: ${error}`);
        }
      }
    });

    it('should throw BadRequest for unsupported file type', async () => {
      const mockFileLinkUrl = `fake_url/jobs/${jobId}/${taskIndex}/files-link/${fileName}`;
      const mockActualFileUrl =
        'http://example.com/actual-file-for-unsupported.dat';

      axiosGetStub
        .withArgs(mockFileLinkUrl, {
          headers: {'X-API-Key': 'fake_key'},
        })
        .resolves({status: 200, data: {url: mockActualFileUrl}});

      try {
        await canvasService.getOutput(
          jobId,
          taskIndex,
          fileName,
          'unsupported-type',
        );
        expect.fail('Expected getOutput to throw but it did not.');
      } catch (error: unknown) {
        expect(error).to.be.instanceOf(IctError);
        if (error instanceof IctError) {
          expect(error.statusCode).to.equal(400);
          expect(error.message).to.contain(
            "Unsupported file_type 'unsupported-type'",
          );
        } else {
          expect.fail(`Caught error was not an instance of IctError: ${error}`);
        }
      }
      sinon.assert.neverCalledWith(
        axiosGetStub,
        mockActualFileUrl,
        sinon.match.any,
      );
    });
  });
});
