import sinon from 'sinon';
import {expect} from 'chai';
import {Container, ContextService, WinstonLogger} from 'ict-api-foundations';
import {SimulationService} from '../../services/simulation-service.ts';
import {CanvasService} from '../../services/canvas-service.ts';
import {CanvasJob} from '../../defs/canvas-service-defs.ts';
import {canvasJobToSimulationJob} from '../../utils/utils.ts';

describe(SimulationService.name, () => {
  let canvasService: sinon.SinonStubbedInstance<CanvasService>;
  let contextService: ContextService;
  let loggerService: WinstonLogger;
  let simulationService: SimulationService;

  beforeEach(() => {
    contextService = new ContextService();
    loggerService = new WinstonLogger(contextService);
    Container.set(ContextService, contextService);
    Container.set(<PERSON>Log<PERSON>, loggerService);
    canvasService = sinon.createStubInstance(CanvasService);
    simulationService = new SimulationService(canvasService, loggerService);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getJobs', () => {
    let mockCanvasJobs: CanvasJob[];

    beforeEach(() => {
      mockCanvasJobs = [
        {
          job_id: '1',
          events: [],
          tags: [],
          last_update: '2025-01-01T00:00:00.000Z',
          tasks: {},
          create_time: '2025-01-01T00:00:00.000Z',
          user_id: 'test user id',
          task_count: 0,
          state: 'test state',
          output_ready: false,
          attributes: {
            random_seeds: [],
            upload_id: 'test attribute upload id',
            runner_backend: 'test runner',
          },
          time_taken: null,
          upload_id: 'test upload id',
        },
      ];
    });

    it('should return a list of jobs', async () => {
      canvasService.getJobs.resolves(mockCanvasJobs);
      const expected = mockCanvasJobs.map(canvasJobToSimulationJob);
      const jobs = await simulationService.getJobs();
      expect(jobs).to.deep.equal(expected);
    });
  });

  describe('getOutput', () => {
    const jobId = 'test-job-id';
    const taskIndex = 0;
    const fileName = 'output.json';
    const fileType = 'application/json';
    let mockOutput: unknown[];

    beforeEach(() => {
      mockOutput = [{data: 'test output data'}];
    });

    it('should call canvasService.getOutput with the correct arguments', async () => {
      canvasService.getOutput.resolves(mockOutput);

      const output = await simulationService.getOutput(
        jobId,
        taskIndex,
        fileName,
        fileType
      );

      expect(
        canvasService.getOutput.calledOnceWithExactly(
          jobId,
          taskIndex,
          fileName,
          fileType
        )
      ).to.be.true;
      expect(output).to.deep.equal(mockOutput);
    });

    it('should handle null task_index', async () => {
      canvasService.getOutput.resolves(mockOutput);

      const output = await simulationService.getOutput(
        jobId,
        null,
        fileName,
        fileType
      );

      expect(
        canvasService.getOutput.calledOnceWithExactly(
          jobId,
          null,
          fileName,
          fileType
        )
      ).to.be.true;
      expect(output).to.deep.equal(mockOutput);
    });

    it('should handle undefined file_type', async () => {
      canvasService.getOutput.resolves(mockOutput);

      const output = await simulationService.getOutput(
        jobId,
        taskIndex,
        fileName,
        undefined
      );

      expect(
        canvasService.getOutput.calledOnceWithExactly(
          jobId,
          taskIndex,
          fileName,
          undefined
        )
      ).to.be.true;
      expect(output).to.deep.equal(mockOutput);
    });

    it('should throw an error if canvasService.getOutput throws an error', async () => {
      const errorMessage = 'Failed to get output';
      canvasService.getOutput.rejects(new Error(errorMessage));

      try {
        await simulationService.getOutput(jobId, taskIndex, fileName, fileType);
        expect.fail('Expected getOutput to throw an error, but it did not.');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        const typedError = error as Error;
        expect(typedError.message).to.equal(errorMessage);
        expect(
          canvasService.getOutput.calledOnceWithExactly(
            jobId,
            taskIndex,
            fileName,
            fileType
          )
        ).to.be.true;
      }
    });
  });
});
