import sinon from 'sinon';
import {expect} from 'chai';
import request from 'supertest';
import {Readable} from 'stream';
import {appSetup, Container} from 'ict-api-foundations';
import {SimulationOutputController} from '../../controllers/simulation-output-controller.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {SimulationService} from '../../services/simulation-service.ts';

describe(SimulationOutputController.name, () => {
  const app = appSetup(RegisterRoutes);

  let simulationServiceStub: sinon.SinonStubbedInstance<SimulationService>;

  const jobId = 'test-job-123';
  const taskIndex = 0;
  const fileName = 'output.zip';
  const summaryFileName = 'summary.csv';

  beforeEach(() => {
    simulationServiceStub = sinon.createStubInstance(SimulationService);

    Container.set(SimulationService, simulationServiceStub);
  });

  afterEach(() => {
    sinon.restore();
    Container.reset();
  });

  describe('GET /simulation/job/{jobId}/{taskIndex}/{fileName}/{fileType}/output', () => {
    it('should return 200 with zip stream for fileType "sim-events"', async () => {
      const fileType = 'sim-events';
      const mockStream = new Readable({
        read() {
          this.push('zip data');
          this.push(null);
        },
      });
      simulationServiceStub.getOutput.resolves(mockStream);

      const response = await request(app).get(
        `/simulation/job/${jobId}/${taskIndex}/${fileName}/${fileType}/output`
      );

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.equal('application/zip');
      expect(response.headers['content-disposition']).to.equal(
        `attachment; filename="${fileName}"`
      );
      expect(response.text).to.equal('zip data');
      sinon.assert.calledOnceWithExactly(
        simulationServiceStub.getOutput,
        jobId,
        taskIndex,
        fileName,
        fileType
      );
    });

    it('should return 200 with JSON data for fileType "summary"', async () => {
      const fileType = 'summary';
      const mockSummaryData = [{event: 'start'}, {event: 'end'}];
      simulationServiceStub.getOutput.resolves(mockSummaryData);

      const response = await request(app).get(
        `/simulation/job/${jobId}/${taskIndex}/${summaryFileName}/${fileType}/output`
      );

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.include('application/json');
      expect(response.body).to.deep.equal(mockSummaryData);
      sinon.assert.calledOnceWithExactly(
        simulationServiceStub.getOutput,
        jobId,
        taskIndex,
        summaryFileName,
        fileType
      );
    });

    it('should return 200 with JSON data for fileType "summary" and taskIndex 0', async () => {
      const fileType = 'summary';
      const mockSummaryData = [{event: 'start-0'}, {event: 'end-0'}];
      simulationServiceStub.getOutput
        .withArgs(jobId, 0, summaryFileName, fileType)
        .resolves(mockSummaryData);

      const response = await request(app).get(
        `/simulation/job/${jobId}/0/${summaryFileName}/${fileType}/output`
      );

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.include('application/json');
      expect(response.body).to.deep.equal(mockSummaryData);
      sinon.assert.calledOnceWithExactly(
        simulationServiceStub.getOutput,
        jobId,
        0,
        summaryFileName,
        fileType
      );
    });

    it('should return 500 if fileType is "sim-events" but service returns non-stream', async () => {
      const fileType = 'sim-events';
      const mockInvalidData = [{event: 'wrong_type'}];
      simulationServiceStub.getOutput.resolves(mockInvalidData);

      const response = await request(app).get(
        `/simulation/job/${jobId}/${taskIndex}/${fileName}/${fileType}/output`
      );

      expect(response.status).to.equal(500);
      expect(response.text).to.contain(
        `Server encountered an unexpected output format for file type '${fileType}'.`
      );
      sinon.assert.calledOnceWithExactly(
        simulationServiceStub.getOutput,
        jobId,
        taskIndex,
        fileName,
        fileType
      );
    });

    it('should return 500 if fileType is "summary" but service returns stream', async () => {
      const fileType = 'summary';
      const mockStream = new Readable({
        read() {
          this.push('unexpected stream data');
          this.push(null);
        },
      });
      simulationServiceStub.getOutput.resolves(mockStream);

      const response = await request(app).get(
        `/simulation/job/${jobId}/${taskIndex}/${summaryFileName}/${fileType}/output`
      );

      expect(response.status).to.equal(500);
      expect(response.text).to.contain(
        `Server encountered an unexpected output format for file type '${fileType}'.`
      );
      sinon.assert.calledOnceWithExactly(
        simulationServiceStub.getOutput,
        jobId,
        taskIndex,
        summaryFileName,
        fileType
      );
    });

    it('should return 500 for unknown fileType', async () => {
      const fileType = 'unknown-type';
      const mockData: unknown[] = [];
      simulationServiceStub.getOutput.resolves(mockData);

      const response = await request(app).get(
        `/simulation/job/${jobId}/${taskIndex}/${fileName}/${fileType}/output`
      );

      expect(response.status).to.equal(500);
      expect(response.text).to.contain(
        `Server encountered an unexpected output format for file type '${fileType}'.`
      );
      sinon.assert.calledOnceWithExactly(
        simulationServiceStub.getOutput,
        jobId,
        taskIndex,
        fileName,
        fileType
      );
    });

    it('should return 500 if simulationService.getOutput throws an error', async () => {
      const fileType = 'summary';
      const errorMessage = 'Service failed';
      simulationServiceStub.getOutput.rejects(new Error(errorMessage));

      const response = await request(app).get(
        `/simulation/job/${jobId}/${taskIndex}/${summaryFileName}/${fileType}/output`
      );

      expect(response.status).to.equal(500);
      expect(response.text).to.contain(errorMessage);
      sinon.assert.calledOnceWithExactly(
        simulationServiceStub.getOutput,
        jobId,
        taskIndex,
        summaryFileName,
        fileType
      );
    });
  });
});
