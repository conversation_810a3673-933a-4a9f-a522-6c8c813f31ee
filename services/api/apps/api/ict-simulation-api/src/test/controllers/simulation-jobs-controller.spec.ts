import sinon from 'sinon';
import {expect} from 'chai';
import request from 'supertest';
import {appSetup, Container} from 'ict-api-foundations';
import {SimulationJobsController} from '../../controllers/simulation-jobs-controller.ts';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {SimulationService} from '../../services/simulation-service.ts';
import {SimulationJob} from '../../defs/simulation-service-defs.ts';

describe(SimulationJobsController.name, () => {
  const app = appSetup(RegisterRoutes);

  let simulationServiceStub: sinon.SinonStubbedInstance<SimulationService>;

  beforeEach(() => {
    simulationServiceStub = sinon.createStubInstance(SimulationService);
    Container.set(SimulationService, simulationServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getSimulationJobs', () => {
    let getJobsMock: SimulationJob[];

    beforeEach(() => {
      getJobsMock = [
        {
          jobId: '1',
          events: [],
          tags: [],
          lastUpdate: '2025-01-01T00:00:00.000Z',
          tasks: {},
          createTime: '2025-01-01T00:00:00.000Z',
          userId: 'test user',
          taskCount: 0,
          state: 'test state',
          outputReady: false,
          attributes: {
            randomSeeds: [],
            uploadId: 'test attribute upload id',
            runnerBackend: 'test runner',
          },
          timeTaken: null,
          uploadId: 'test upload id',
        },
      ];
      simulationServiceStub.getJobs.resolves(getJobsMock);
    });

    it('should return the correct data', async () => {
      const response = await request(app).get('/simulation/jobs/list');
      expect(response.status).to.equal(200);
      expect(response.body.data).to.deep.equal(getJobsMock);
    });
  });
});
