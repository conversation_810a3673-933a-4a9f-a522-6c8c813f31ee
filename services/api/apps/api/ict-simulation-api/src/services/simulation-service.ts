import {Di<PERSON><PERSON><PERSON>, <PERSON><PERSON>ogger} from 'ict-api-foundations';
import {Readable} from 'stream';
import {SimulationJob} from '../defs/simulation-service-defs';
import {CanvasService} from './canvas-service.ts';
import {canvasJobToSimulationJob} from '../utils/utils.ts';

@DiService()
export class SimulationService {
  private logger: WinstonLogger;
  private canvasService: CanvasService;

  constructor(canvasService: CanvasService, logger: WinstonLogger) {
    this.canvasService = canvasService;
    this.logger = logger;
  }

  async getJobs(): Promise<SimulationJob[]> {
    this.logger.info('Fetching simulation jobs from Canvas service');
    const canvasJobs = await this.canvasService.getJobs();

    const simulationJobs: SimulationJob[] = canvasJobs.map(
      canvasJobToSimulationJob,
    );

    this.logger.info(
      `Successfully retrieved ${simulationJobs.length} simulation jobs`,
    );
    return simulationJobs;
  }

  async getOutput(
    job_id: string,
    task_index: number | null,
    file_name: string,
    file_type: string | undefined,
  ): Promise<Readable | unknown[]> {
    this.logger.info(
      `Fetching output for job: ${job_id}, task: ${task_index}, file: ${file_name}, type: ${file_type}`,
    );
    const outputData = await this.canvasService.getOutput(
      job_id,
      task_index,
      file_name,
      file_type,
    );
    this.logger.info(`Successfully retrieved output data for job: ${job_id}`);
    return outputData;
  }
}
