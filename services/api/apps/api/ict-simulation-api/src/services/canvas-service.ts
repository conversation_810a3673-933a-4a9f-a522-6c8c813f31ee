import {
  Container,
  DiService,
  Environment,
  GCPSecretManager,
  IctError,
  WinstonLogger,
} from 'ict-api-foundations';
import axios from 'axios';
import {Readable} from 'stream';
import csv from 'csv-parser';
import {CanvasJob} from '../defs/canvas-service-defs.ts';

@DiService()
export class CanvasService {
  private logger: <PERSON>Logger;

  constructor(logger: WinstonLogger) {
    this.logger = logger;
    this.logger.info('Canvas Service Initialized');
  }

  async getJobs(): Promise<CanvasJob[]> {
    this.logger.info('Starting getJobs request');

    // const env = process.env.ENVIRONMENT;
    const canvasApiKey = await this.getCanvasSecrets();
    const simulationApiUrl = process.env.SIMULATION_API_URL;
    const getJobsUrl = `${simulationApiUrl}/jobs`;

    this.logger.info('Making request to Canvas API', {
      url: getJobsUrl,
      limit: 50,
    });

    try {
      const response = await axios.get<{jobs: CanvasJob[]}>(getJobsUrl, {
        params: {
          limit: 50,
          // tag: [tenantId, env],
        },
        // make tag[] param serialize as "&tag=tenantId&tag=env" instead of "&tag[]=tenantId&tag[]=env"
        paramsSerializer: {
          indexes: null,
        },
        headers: {
          'X-API-Key': canvasApiKey,
        },
      });

      this.logger.info('Received response from Canvas API', {
        status: response.status,
        jobCount: response.data?.jobs?.length || 0,
      });

      if (response.status !== 200) {
        this.logger.info('Bad response from canvas', response.data);
        return [];
      }

      this.logger.info('Successfully retrieved jobs from Canvas', {
        totalJobs: response.data.jobs.length,
      });

      return response.data.jobs;
    } catch (error) {
      this.logger.error('Error getting jobs from canvas', {error});
      if (error instanceof IctError) throw error;

      throw IctError.internalServerError('Failed to retrieve jobs', error);
    }
  }

  async getOutput(
    job_id: string,
    task_index: number | null,
    file_name: string,
    file_type: string | undefined,
  ): Promise<Readable | unknown[]> {
    this.logger.info('Starting getOutput request', {
      job_id,
      task_index,
      file_name,
      file_type,
    });

    const canvasApiKey = await this.getCanvasSecrets();

    let effectiveFileName = file_name;
    if (file_type === 'summary') {
      effectiveFileName = `stats/${file_name}`;
    }

    this.logger.info('Determined effective file name', {
      original_file_name: file_name,
      effective_file_name: effectiveFileName,
      file_type,
    });

    const simulationApiUrl = process.env.SIMULATION_API_URL;
    let actualFileUrl = '';

    try {
      const fileEndpoint = `${simulationApiUrl}/jobs/${job_id}/${task_index}/files-link/${effectiveFileName}`;

      this.logger.info('Making request to get file URL', {
        fileEndpoint,
        job_id,
        task_index,
        effectiveFileName,
      });

      const fileUrlResponse = await axios.get<{url: string}>(fileEndpoint, {
        headers: {'X-API-Key': canvasApiKey},
      });

      this.logger.info(
        `File response status for ${effectiveFileName}: ${fileUrlResponse.status}`,
      );

      actualFileUrl = fileUrlResponse.data.url;

      this.logger.info('Successfully retrieved file URL', {
        effectiveFileName,
        actualFileUrl,
        status: fileUrlResponse.status,
      });

      if (!actualFileUrl) {
        this.logger.error(
          `No file URL received for ${effectiveFileName} from ${fileEndpoint}. Response data: ${JSON.stringify(
            fileUrlResponse.data,
          )}`,
        );
        throw IctError.internalServerError(
          `Failed to retrieve a valid file URL for ${effectiveFileName}.`,
        );
      }

      if (file_type === 'sim-events') {
        this.logger.info(
          `Processing sim-events file type - preparing to fetch zip stream from: ${actualFileUrl}`,
        );

        const fileDataResponse = await axios.get<Readable>(actualFileUrl, {
          responseType: 'stream',
        });

        if (fileDataResponse.status !== 200) {
          this.logger.error(
            `Bad response status (${fileDataResponse.status}) getting ${effectiveFileName} (stream) from ${actualFileUrl}`,
            {status: fileDataResponse.status},
          );
          throw IctError.internalServerError(
            `Failed to download file ${effectiveFileName} from ${actualFileUrl} (Status: ${fileDataResponse.status})`,
          );
        }
        this.logger.info(
          `Successfully initiated stream for ${effectiveFileName} from ${actualFileUrl}`,
        );
        return fileDataResponse.data;
      }

      if (file_type === 'summary') {
        this.logger.info(
          `Processing summary file type - fetching CSV text from: ${actualFileUrl}`,
        );

        const fileDataResponse = await axios.get<string>(actualFileUrl, {
          responseType: 'text',
        });

        if (fileDataResponse.status !== 200) {
          this.logger.error(
            `Bad response status (${fileDataResponse.status}) getting ${effectiveFileName} (text) from ${actualFileUrl}`,
            {status: fileDataResponse.status},
          );
          throw IctError.internalServerError(
            `Failed to download file ${effectiveFileName} (text) from ${actualFileUrl} (Status: ${fileDataResponse.status})`,
          );
        }

        this.logger.info('Successfully downloaded CSV text file', {
          effectiveFileName,
          actualFileUrl,
          contentLength: fileDataResponse.data.length,
        });

        let csvString = fileDataResponse.data;
        if (csvString.startsWith('"') && csvString.endsWith('"')) {
          csvString = csvString.substring(1, csvString.length - 1);
          this.logger.info('Removed surrounding quotes from CSV content');
        }
        csvString = csvString.replace(/\\n/g, '\n');

        this.logger.info('Starting CSV parsing', {
          effectiveFileName,
          processedContentLength: csvString.length,
        });

        return new Promise((resolve, reject) => {
          const results: Record<string, string>[] = [];
          const csvStream = Readable.from(csvString);
          const csvParser = csv();
          let rejected = false;

          csvParser.on('error', (parseError: Error) => {
            if (rejected) return;
            rejected = true;
            this.logger.error(
              `Error parsing CSV stream for ${effectiveFileName} from processed string`,
              {error: parseError},
            );
            reject(
              IctError.internalServerError(
                `Failed to parse CSV file ${effectiveFileName} from processed string`,
                parseError,
              ),
            );
          });

          csvStream.on('error', (sourceError: Error) => {
            if (rejected) return;
            rejected = true;
            this.logger.error(
              `Error reading source stream for ${effectiveFileName} (from processed string)`,
              {error: sourceError},
            );
            reject(
              IctError.internalServerError(
                `Failed to read source stream for ${effectiveFileName} (from processed string)`,
                sourceError,
              ),
            );
          });

          csvStream
            .pipe(csvParser)
            .on('data', (data: Record<string, string>) => results.push(data))
            .on('end', () => {
              if (!rejected) {
                this.logger.info(
                  `Successfully parsed CSV for ${effectiveFileName}. Records: ${results.length}`,
                );
                resolve(results);
              }
            });
        });
      }

      this.logger.error(
        `Unknown or unsupported file_type '${file_type}' requested for ${effectiveFileName}.`,
      );
      throw IctError.badRequest(
        `Unsupported file_type '${file_type}'. Must be 'sim-events' or 'summary'.`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing request for ${effectiveFileName} (from URL: ${actualFileUrl}), job ${job_id}`,
        {
          error,
        },
      );
      if (error instanceof IctError) throw error;
      throw IctError.internalServerError(
        `Failed to retrieve or process ${effectiveFileName} from ${actualFileUrl}`,
        error,
      );
    }
  }

  private async getCanvasSecrets() {
    let canvasApiKey: string;
    const env = process.env.ENVIRONMENT;
    const simulationApiKeyName = process.env.SIMULATION_API_KEY;

    if (!simulationApiKeyName) {
      this.logger.error('SIMULATION_API_KEY environment variable is not set');
      throw IctError.internalServerError(
        'SIMULATION_API_KEY environment variable is required but not configured',
      );
    }

    if (!env || env !== Environment.local) {
      // use secret manager here for non-dev environments
      const secretManager = Container.get(GCPSecretManager);
      if (!secretManager) {
        this.logger.error(
          'Secret manager not available for non-local environment',
        );
        throw IctError.internalServerError(
          'A secret manager is required for accessing Canvas secrets.',
        );
      }

      this.logger.info(`Retrieving secret: ${simulationApiKeyName}`);
      try {
        canvasApiKey = await secretManager.get({
          name: simulationApiKeyName,
        });
      } catch (error) {
        this.logger.error(`Failed to retrieve secret ${simulationApiKeyName}`, {
          error,
        });
        throw IctError.internalServerError(
          `Failed to retrieve Canvas API key from secret manager: ${simulationApiKeyName}`,
          error,
        );
      }
    } else {
      canvasApiKey = simulationApiKeyName;
    }

    if (!canvasApiKey) {
      this.logger.error('Canvas API key is empty after retrieval');
      throw IctError.internalServerError(
        'Canvas API key is empty - check secret configuration',
      );
    }

    return canvasApiKey;
  }
}
