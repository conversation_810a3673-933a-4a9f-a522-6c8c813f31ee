import {
  Api<PERSON><PERSON>ponseArray,
  Container,
  ProtectedRouteMiddleware,
  WinstonLogger,
  IctError,
} from 'ict-api-foundations';
import {
  Controller,
  Get,
  Middlewares,
  OperationId,
  Route,
  SuccessResponse,
  Tags,
} from 'tsoa';
import {SimulationJob} from '../defs/simulation-service-defs.ts';
import {SimulationService} from '../services/simulation-service.ts';

@Route('simulation')
@Middlewares([ProtectedRouteMiddleware.apiProtectedDataRoute()])
export class SimulationJobsController extends Controller {
  @SuccessResponse('200')
  @Get('/jobs/list')
  @OperationId('GetSimulationJobs')
  @Tags('simulation')
  public async getJobs(): Promise<ApiResponseArray<SimulationJob[], {}>> {
    const logger = Container.get(WinstonLogger);
    const simulationService = Container.get(SimulationService);

    logger.info('Request to get simulation jobs list');

    try {
      const jobs = await simulationService.getJobs();

      logger.info('Successfully retrieved simulation jobs', {
        jobCount: jobs.length,
      });

      return {
        data: jobs,
        metadata: {},
      };
    } catch (error) {
      logger.error('Failed to retrieve simulation jobs', {
        error,
      });

      if (error instanceof IctError) {
        throw error;
      }

      throw IctError.internalServerError(
        'Unable to retrieve simulation jobs',
        error,
      );
    }
  }
}
