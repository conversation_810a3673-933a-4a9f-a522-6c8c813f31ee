import {Readable} from 'stream';
import {
  Container,
  ProtectedRouteMiddleware,
  WinstonLogger,
  IctError,
} from 'ict-api-foundations';
import {
  Controller,
  Get,
  Middlewares,
  OperationId,
  Path,
  Response,
  Route,
  Tags,
} from 'tsoa';
import {SimulationService} from '../services/simulation-service.ts';

@Route('simulation')
@Middlewares([ProtectedRouteMiddleware.apiProtectedDataRoute()])
export class SimulationOutputController extends Controller {
  @Response<Readable>(
    200,
    'Success - Returns simulation output as a file stream (e.g., application/zip)',
  )
  @Response<unknown[]>(
    200,
    'Success - Returns simulation summary as JSON (application/json)',
  )
  @Get('/job/{jobId}/{taskIndex}/{fileName}/{fileType}/output')
  @OperationId('GetSimulationOutputByJobId')
  @Tags('simulation')
  public async getOutputByJobId(
    @Path() jobId: string,
    @Path() taskIndex: number | null,
    @Path() fileName: string,
    @Path() fileType: string,
  ): Promise<Readable | unknown[]> {
    const simulationService = Container.get(SimulationService);
    const logger = Container.get(WinstonLogger);

    logger.info(
      `Requesting simulation output for jobId: ${jobId}, taskIndex: ${taskIndex}, fileName: ${fileName}, fileType: ${fileType}`,
    );

    const outputData = await simulationService.getOutput(
      jobId,
      taskIndex,
      fileName,
      fileType,
    );

    logger.info(
      `Successfully retrieved output data for job ${jobId}, file ${fileName}. Output type: ${
        outputData instanceof Readable ? 'Readable stream' : typeof outputData
      }`,
    );

    this.setStatus(200);

    if (fileType === 'sim-events' && outputData instanceof Readable) {
      logger.info(
        `Returning simulation events as zip file for job ${jobId}, file ${fileName}`,
      );
      this.setHeader('Content-Type', 'application/zip');
      this.setHeader(
        'Content-Disposition',
        `attachment; filename="${fileName}"`,
      );
      return outputData;
    }
    if (fileType === 'summary' && Array.isArray(outputData)) {
      logger.info(
        `Returning simulation summary as JSON for job ${jobId}, file ${fileName}`,
      );
      this.setHeader('Content-Type', 'application/json');
      return outputData;
    }
    logger.error(
      `Unexpected output type or fileType mismatch for job ${jobId}, file ${fileName}, type ${fileType}. Output type: ${
        outputData instanceof Readable ? 'Readable' : typeof outputData
      }`,
    );
    throw IctError.internalServerError(
      `Server encountered an unexpected output format for file type '${fileType}'.`,
    );
  }
}
