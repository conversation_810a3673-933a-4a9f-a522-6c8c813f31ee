import {BaseHealthCheck} from 'ict-api-foundations';
import {Get, Route, OperationId, Tags} from 'tsoa';

@Route('/simulation/healthcheck')
export class SimulationHealthCheckController {
  @Get()
  @OperationId('GetSimulationBasicHealthCheck')
  @Tags('simulation')
  public getSimulationBasicHealthCheck() {
    return BaseHealthCheck.getBasicHealthCheck();
  }

  @Get('/full')
  @OperationId('GetSimulationFullHealthCheck')
  @Tags('simulation')
  public getSimulationFullHealthCheck() {
    return BaseHealthCheck.getDeepHealthCheck(['auth0']);
  }
}
