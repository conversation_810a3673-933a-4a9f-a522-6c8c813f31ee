import {type <PERSON><PERSON>Job} from '../defs/canvas-service-defs.ts';
import {type SimulationJob} from '../defs/simulation-service-defs.ts';

export const canvasJobToSimulationJob = (
  canvasJob: CanvasJob
): SimulationJob => {
  return {
    jobId: canvasJob.job_id,
    events: canvasJob.events.map(event => ({
      eventType: event.event_type,
      state: event.state,
      timestamp: event.timestamp,
      taskIndex: event.task_index,
      jobId: event.job_id,
      computeResources: event.compute_resources
        ? {
            maxDuration: event.compute_resources.max_duration,
            preemptible: event.compute_resources.preemptible,
            instanceType: event.compute_resources.instance_type,
            memoryPerTaskMb: event.compute_resources.memory_per_task_mb,
            cpuPerTask: event.compute_resources.cpu_per_task,
          }
        : undefined,
      extraArgs: event.extra_args
        ? {
            simulationTime: event.extra_args.simulation_time,
            variant: event.extra_args.variant,
            runWithDocker: event.extra_args.run_with_docker,
            solution: event.extra_args.solution,
            type: event.extra_args.type,
            version: event.extra_args.version,
          }
        : undefined,
      taskCount: event.task_count,
      userId: event.user_id,
      randomSeeds: event.random_seeds,
      uploadId: event.upload_id,
      runEnv: event.run_env,
      runnerBackend: event.runner_backend,
      unifiedRunnerVersion: event.unified_runner_version,
      tags: event.tags,
      taskExitCode: event.task_exit_code,
    })),
    tags: canvasJob.tags,
    lastUpdate: canvasJob.last_update,
    tasks: Object.entries(canvasJob.tasks).reduce(
      (acc, [key, value]) => {
        acc[key] = {
          taskIndex: value.task_index,
          lastUpdate: value.last_update,
          state: value.state,
          timeTaken: value.time_taken,
          visState: value.vis_state,
          exitCode: value.exit_code,
        };
        return acc;
      },
      {} as NonNullable<SimulationJob['tasks']>
    ),
    createTime: canvasJob.create_time,
    userId: canvasJob.user_id,
    taskCount: canvasJob.task_count,
    state: canvasJob.state,
    outputReady: canvasJob.output_ready,
    attributes: {
      randomSeeds: canvasJob.attributes.random_seeds,
      uploadId: canvasJob.attributes.upload_id,
      runnerBackend: canvasJob.attributes.runner_backend,
    },
    timeTaken: canvasJob.time_taken,
    uploadId: canvasJob.upload_id,
  };
};
