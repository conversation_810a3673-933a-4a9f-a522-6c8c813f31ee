{"name": "ict-simulation-api", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/ict-simulation-api/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "ict-simulation-api:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "ict-simulation-api:build:development"}, "production": {"buildTarget": "ict-simulation-api:build:production"}}}}}