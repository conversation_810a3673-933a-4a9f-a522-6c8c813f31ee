{"name": "mock-data-api", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/mock-data-api/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "mock-data-api:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "mock-data-api:build:development"}, "production": {"buildTarget": "mock-data-api:build:production"}}}}}