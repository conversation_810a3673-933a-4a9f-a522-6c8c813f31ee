{"name": "mock-data-api", "version": "1.0.0", "description": "A quick and dirty cloudrun function that returns a json file from a storage bucket based on the url path", "author": "Dematic", "license": "ISC", "main": "src/index.ts", "type": "module", "engines": {"node": ">=22.0.0"}, "scripts": {"test": "AUTH_AUDIENCE='http://localhost:8080/' AUTH_DOMAIN='test-domain.auth0.com' yarn run -T mocha ./src/test/**/*.spec.ts", "test:watch": "mocha --watch", "start": "node --import ../register-tsnode.mjs --enable-source-maps ./src/index.ts", "start:otel": "node --require ../ict-instrumentation/build/instrumentation.cjs --import ../register-tsnode.mjs ./src/index.ts", "build": "npx tsc", "lint": "gts lint", "clean": "gts clean", "fix": "gts fix", "posttest": "yarn lint", "debug": "nodemon --exec \"tsoa routes && node --inspect=0.0.0.0:9229 --import ../register-tsnode.mjs ./src/index.ts\"", "watch": "yarn run -T tsoa routes && node --watch --no-warnings=ExperimentalWarning --import ../register-tsnode.mjs ./src/index.ts | yarn run -T pino-pretty"}}