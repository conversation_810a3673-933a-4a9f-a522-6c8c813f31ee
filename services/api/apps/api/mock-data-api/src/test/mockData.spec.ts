// import {expect} from 'chai';
// // eslint-disable-next-line n/no-unpublished-import
// const sinon = require('sinon');
// // eslint-disable-next-line n/no-unpublished-import
// import {Request, Response} from 'express';
// import {handler} from '../index.ts';
// import {Storage} from '@google-cloud/storage';

// describe('Handler', () => {
//   it('should retrieve file contents successfully', async () => {
//     const req = {
//       path: '/orders/shipped',
//     };
//     const res = {
//       header: {},
//       status(code: number) {
//         expect(code).to.equal(200);
//         return this;
//       },
//       set(header: string, value: string) {
//         return this;
//       },
//       send(response: string) {
//         expect(response).to.be.a('string');
//       },
//     };
//     //mock storage bucket return
//     const mockBucket = sinon.stub(Storage.prototype, 'bucket').returns({
//       file: sinon.stub().returnsThis(),
//       download: sinon.stub().returns(
//         JSON.stringify({
//           data: {
//             current: '56982',
//             total: '75000',
//             change: 23.7,
//             status: 'ok',
//             trend_status: 'trending_up',
//           },
//         })
//       ),
//     });
//     await handler(req as Request, res as unknown as Response);
//     sinon.assert.calledWith(mockBucket, 'mockapidata');
//     mockBucket.restore();
//   });

//   it('should handle errors properly', async () => {
//     const req = {
//       path: '/orders/shipped',
//     };
//     const res = {
//       headers: {},
//       status(code: number) {
//         expect(code).to.equal(500);
//         return this;
//       },
//       set(header: string, value: string) {
//         return this;
//       },
//       send(response: object) {
//         expect(response).to.be.a('object');
//       },
//     };
//     //return error to check handling
//     const mockBucket = sinon.stub(Storage.prototype, 'bucket').returns({
//       file: sinon.stub().returnsThis(),
//       download: sinon.stub().throws({message: 'fake error thrown'}),
//     });
//     await handler(req as Request, res as unknown as Response);
//     sinon.assert.calledWith(mockBucket, 'mockapidata');
//     mockBucket.restore();
//   });
// });
