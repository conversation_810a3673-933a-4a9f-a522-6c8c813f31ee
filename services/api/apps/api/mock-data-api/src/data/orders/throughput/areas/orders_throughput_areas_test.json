{"data": {"areas": [{"id": "testArea1", "name": "Picking", "alertStatus": {"status": "ok", "identifier": "Workstation 1", "message": "Everything's in order! All picking tasks are up-to-date"}, "operators": {"activeOperators": 27, "lowRecOperators": 19, "highRecOperators": 37, "pickRate": 46.795, "queuedOrders": 26}, "throughput": {"throughputRate": 1680, "maxThroughputCapacity": 2000, "minThroughputTarget": 1400, "maxThroughputTarget": 1700, "status": "ok"}}, {"id": "testArea2", "name": "Put Wall", "operators": {"activeOperators": 8, "lowRecOperators": 32, "highRecOperators": 34, "maxOperators": 100, "pickRate": 42.784, "queuedOrders": 24}, "throughput": {"throughputRate": 4550, "maxThroughputCapacity": 5000, "minThroughputTarget": 3800, "maxThroughputTarget": 4200, "status": "ok"}}, {"id": "testArea3", "name": "Packing", "alertStatus": {"status": "caution", "identifier": "Workstation 1", "message": "down"}, "operators": {"activeOperators": 18, "lowRecOperators": 19, "highRecOperators": 35, "maxOperators": 100, "pickRate": 44.121, "queuedOrders": 28}, "throughput": {"throughputRate": 1400, "maxThroughputCapacity": 3000, "minThroughputTarget": 1550, "maxThroughputTarget": 1700, "status": "caution"}}, {"id": "testArea4", "name": "Shipping", "alertStatus": {"status": "critical", "identifier": "Workstation 4", "message": "down."}, "operators": {"activeOperators": 29, "lowRecOperators": 25, "highRecOperators": 31, "maxOperators": 100, "pickRate": 22.729, "queuedOrders": 28}, "throughput": {"throughputRate": 1280, "maxThroughputCapacity": 4000, "minThroughputTarget": 2000, "maxThroughputTarget": 2900, "status": "critical"}}]}}