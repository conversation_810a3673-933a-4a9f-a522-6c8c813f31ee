{"data": {"areas": [{"id": "testArea1", "name": "Picking", "operators": {"activeOperators": 27, "lowRecOperators": 19, "highRecOperators": 37}, "cycleTime": {"averageCycleTimeSeconds": 21, "slowestCycleTimeSeconds": 35, "fastestCycleTimeSeconds": 15, "targetCycleTimeSeconds": 22, "slowestRecCycleTimeSeconds": 30, "status": "ok"}}, {"id": "testArea2", "name": "Put Wall", "alertStatus": {"status": "caution", "identifier": "Workstation 2"}, "operators": {"activeOperators": 8, "lowRecOperators": 32, "highRecOperators": 34}, "cycleTime": {"averageCycleTimeSeconds": 27, "slowestCycleTimeSeconds": 30, "fastestCycleTimeSeconds": 18, "targetCycleTimeSeconds": 23, "slowestRecCycleTimeSeconds": 26, "status": "caution"}}, {"id": "testArea3", "name": "Packing", "alertStatus": {"status": "critical", "identifier": "Workstation 4", "message": "Action Suggested - Reassign Operators"}, "operators": {"activeOperators": 18, "lowRecOperators": 19, "highRecOperators": 35}, "cycleTime": {"averageCycleTimeSeconds": 45, "slowestCycleTimeSeconds": 50, "fastestCycleTimeSeconds": 30, "targetCycleTimeSeconds": 35, "slowestRecCycleTimeSeconds": 40, "status": "critical"}}, {"id": "testArea4", "name": "Shipping", "alertStatus": {"status": "caution", "identifier": "Workstation 1", "message": "Assistance Requested"}, "operators": {"activeOperators": 29, "lowRecOperators": 25, "highRecOperators": 31}, "cycleTime": {"averageCycleTimeSeconds": 20, "slowestCycleTimeSeconds": 35, "fastestCycleTimeSeconds": 15, "targetCycleTimeSeconds": 21, "slowestRecCycleTimeSeconds": 30, "status": "ok"}}]}}