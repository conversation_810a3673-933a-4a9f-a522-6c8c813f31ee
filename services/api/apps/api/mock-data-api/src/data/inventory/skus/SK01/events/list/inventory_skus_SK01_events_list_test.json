{"data": {"events": [{"event_date": "2023-08-23T19:49:00.000Z", "event_type": "Pick", "tote_id": "D0052785", "operator_name": "<PERSON><PERSON>", "quantity_change": -3}, {"event_date": "2023-08-23T19:49:00.000Z", "event_type": "Cycle Count", "tote_id": "D0052785", "operator_name": "<PERSON><PERSON>", "quantity_change": 1}, {"event_date": "2023-08-23T19:49:00.000Z", "event_type": "Pick", "tote_id": "D0052785", "operator_name": "<PERSON><PERSON>", "quantity_change": -2}, {"event_date": "2023-08-23T19:49:00.000Z", "event_type": "Replenishment", "tote_id": "D0052785", "operator_name": "<PERSON><PERSON>", "quantity_change": 4}, {"event_date": "2023-08-23T19:49:00.000Z", "event_type": "Pick", "tote_id": "D0052785", "operator_name": "<PERSON><PERSON>", "quantity_change": -3}, {"event_date": "2023-08-23T19:49:00.000Z", "event_type": "Cycle Count", "tote_id": "D0052785", "operator_name": "<PERSON><PERSON>", "quantity_change": 1}, {"event_date": "2023-08-23T19:49:00.000Z", "event_type": "Pick", "tote_id": "D0052785", "operator_name": "<PERSON><PERSON>", "quantity_change": -2}, {"event_date": "2023-08-23T19:49:00.000Z", "event_type": "Replenishment", "tote_id": "D0052785", "operator_name": "<PERSON><PERSON>", "quantity_change": 4}, {"event_date": "2023-08-23T19:49:00.000Z", "event_type": "Pick", "tote_id": "D0052785", "operator_name": "<PERSON><PERSON>", "quantity_change": -3}, {"event_date": "2023-08-23T19:49:00.000Z", "event_type": "Cycle Count", "tote_id": "D0052785", "operator_name": "<PERSON><PERSON>", "quantity_change": 1}, {"event_date": "2023-08-23T19:49:00.000Z", "event_type": "Pick", "tote_id": "D0052785", "operator_name": "<PERSON><PERSON>", "quantity_change": -2}, {"event_date": "2023-08-23T19:49:00.000Z", "event_type": "Replenishment", "tote_id": "D0052785", "operator_name": "<PERSON><PERSON>", "quantity_change": 4}, {"event_date": "2023-08-23T19:49:00.000Z", "event_type": "Pick", "tote_id": "D0052785", "operator_name": "<PERSON><PERSON>", "quantity_change": -3}, {"event_date": "2023-08-23T19:49:00.000Z", "event_type": "Cycle Count", "tote_id": "D0052785", "operator_name": "<PERSON><PERSON>", "quantity_change": 1}]}}