{"data": [{"operatorId": "001531", "startTime": "2023-11-25T07:46:00.000Z", "endTime": "2023-11-25T10:00:00.000Z", "totalTimeDuration": "02:14", "idleTime": "00:15", "starvedTime": "00:15", "blockedTime": "00:25", "lineRatePerHour": 75, "weightedLineRatePerHour": 100}, {"operatorId": "001532", "startTime": "2023-11-25T10:15:00.000Z", "endTime": "2023-11-25T12:00:00.000Z", "totalTimeDuration": "01:45", "idleTime": "00:15", "starvedTime": "00:15", "blockedTime": "00:25", "lineRatePerHour": 75, "weightedLineRatePerHour": 100}, {"operatorId": "001533", "startTime": "2023-11-25T12:30:00.000Z", "endTime": "2023-11-25T15:00:00.000Z", "totalTimeDuration": "02:30", "idleTime": "00:15", "starvedTime": "00:15", "blockedTime": "00:25", "lineRatePerHour": 75, "weightedLineRatePerHour": 100}, {"operatorId": "001534", "startTime": "2023-11-25T15:30:00.000Z", "endTime": "2023-11-25T--:--:--.---Z", "totalTimeDuration": "01:30", "idleTime": "00:15", "starvedTime": "00:15", "blockedTime": "00:25", "lineRatePerHour": 75, "weightedLineRatePerHour": 100}]}