import {Storage} from '@google-cloud/storage';
import express from 'express';
import {
  AuthMiddleware,
  Container,
  EnvironmentService,
  HealthCheckHandler,
  SecurityMiddleware,
  WinstonLogger,
  RequestLoggerMiddleware,
  CorsMiddleware,
} from 'ict-api-foundations';
// import isPortReachable from 'is-port-reachable';

const logger = Container.get(WinstonLogger);

const envService = Container.get(EnvironmentService);
const requestMiddleware = Container.get(RequestLoggerMiddleware);
const authMiddlware = Container.get(AuthMiddleware);
const corsMiddleware = Container.get(CorsMiddleware);
const securityMiddleware = Container.get(SecurityMiddleware);

const bucketName = envService.bucket.name
  ? envService.bucket.name
  : 'mockapidata';

// Initialize storage client
const storage = new Storage();

const app = express();

app.use(securityMiddleware.use);
app.use(corsMiddleware.use);
app.use(authMiddlware.use);
app.use(requestMiddleware.use);

app.use((req, res, next) => {
  const prefix = '/mock';
  if (req.path.startsWith(prefix)) {
    req.url = req.path.substring(prefix.length);
    logger.info(`stripped ${prefix} from path -- using ${req.url}`);
  }
  next();
});

const handleMockRequest = async (
  req: express.Request,
  res: express.Response
) => {
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'GET');
  try {
    let requestPath = req.path.slice(1, req.path.length);
    if (requestPath.charAt(requestPath.length - 1) !== '/') {
      requestPath += '/';
    }
    logger.debug(`Request path is ${requestPath}`);

    // Default file name
    const fileName = `${requestPath}test.json`;

    logger.debug(`Attempting to download file: ${fileName}`);
    const contents = await storage.bucket(bucketName).file(fileName).download();

    res.status(200).send(contents.toString());
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    if (error.code === 404) {
      logger.warn(`File not found: ${req.path}`);
      res.status(404).send({success: false, error: 'File not found'});
    } else {
      logger.error('Error processing request', error);
      res.status(500).send({success: false, error: 'Internal server error'});
    }
  }
};

app.get('/healthcheck', HealthCheckHandler.getBasicHealthCheck);
app.get('/*', handleMockRequest);
app.post('/*', handleMockRequest);
app.put('/*', handleMockRequest);

const port = envService.ports.mock || 8080;
app.listen(port, () =>
  logger.info(`Mock API listening on local port ${port}.`)
);

export default app;
