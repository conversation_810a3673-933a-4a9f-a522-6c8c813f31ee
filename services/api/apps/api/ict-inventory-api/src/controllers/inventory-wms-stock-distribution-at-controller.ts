import {
  CacheMiddleware,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {InventoryService} from '../services/inventory-service.ts';
import {InventoryStockDistributionAtData} from '../defs/inventory-distribution-stat-history-def.ts';

@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [
      configPostgresDatabase(),
      {
        type: DatabaseTypes.BigQuery,
      },
    ],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class InventoryStockDistributionAtController extends Controller {
  public static readonly exampleData: InventoryStockDistributionAtData = {
    atInventory: [
      {
        name: '2020-01-01',
        value: 20,
        isValidPercentage: true,
      },
      {
        name: '2020-01-02',
        value: 17,
        isValidPercentage: true,
      },
      {
        name: '2020-01-03',
        value: 24,
        isValidPercentage: true,
      },
      {
        name: '2020-01-04',
        value: 42,
        isValidPercentage: true,
      },
    ],
  };

  public static readonly exampleResponse: InventoryStockDistributionAtData =
    InventoryStockDistributionAtController.exampleData;

  @Example<InventoryStockDistributionAtData>(
    InventoryStockDistributionAtController.exampleResponse,
  )
  @SuccessResponse('200')
  @Get('/wms/stock/distribution/at/percentage/series')
  @OperationId('GetInventoryWmsStockDistributionAtPercentageSeries')
  @Tags('inventory')
  public async getInventoryStockDistributionAt(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
  ): Promise<InventoryStockDistributionAtData> {
    startEndDateValidation(startDate, endDate);
    const inventoryService = Container.get(InventoryService);

    return await inventoryService.getAtInventoryPercentageHistoricalDataAsync(
      startDate,
      endDate,
    );
  }
}
