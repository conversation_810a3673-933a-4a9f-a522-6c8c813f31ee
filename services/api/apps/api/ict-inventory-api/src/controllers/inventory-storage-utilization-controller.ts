import {
  CacheMiddleware,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  IctError,
  PercentValidations,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Deprecated,
  Query,
} from 'tsoa';
import {InventoryService} from '../services/inventory-service.ts';
import {InventoryStorageUtilization} from '../defs/inventory-storage-utilization-def.ts';

@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [
      configPostgresDatabase(),
      {
        type: DatabaseTypes.BigQuery,
      },
    ],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class InventoryStorageUtilizationController extends Controller {
  public static readonly exampleData: InventoryStorageUtilization = {
    utilizationPercentage: 50,
  };

  public static readonly exampleResponse: InventoryStorageUtilization = {
    utilizationPercentage: 50,
  };

  /**
   *
   * @returns {Promise<ApiResponse<InventoryStorageUtilization, InventoryStorageUtilizationConfig>>} contract
   */
  @Example<InventoryStorageUtilization>(
    InventoryStorageUtilizationController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/storage/utilization')
  @OperationId('GetInventoryStorageUtilization')
  @Tags('inventory')
  public async getInventoryStorageUtilization(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    @Deprecated() @Query('start_date') startDate?: Date,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    @Deprecated() @Query('end_date') endDate?: Date
  ): Promise<InventoryStorageUtilization> {
    // get the inventory service
    const inventoryService = Container.get(InventoryService);

    // retrieve average inventory StorageUtilization from the service
    const inventoryStorageUtilization =
      await inventoryService.getInventoryStorageUtilizationAsync();

    if (
      !inventoryStorageUtilization ||
      !inventoryStorageUtilization.utilizationPercentage
    ) {
      throw IctError.noContent();
    }

    // validate percentage is between 0 and 100
    if (
      PercentValidations.isValidPercentage(
        inventoryStorageUtilization.utilizationPercentage
      )
    ) {
      return {
        ...inventoryStorageUtilization,
      };
    }
    throw IctError.internalServerError(
      `Invalid Percentage: StorageUtilization utilizationPercentage must be between 0 and 100. Got: ${inventoryStorageUtilization.utilizationPercentage}.`
    );
  }
}
