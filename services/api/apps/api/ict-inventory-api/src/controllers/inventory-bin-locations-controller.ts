import {
  Container,
  IctError,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Query,
} from 'tsoa';
import {InventoryService} from '../services/inventory-service.ts';
import {
  InventoryBinLocation,
  InventoryBinLocationsResponse,
} from '../defs/inventory-bin-locations-def.ts';

@Route('inventory')
@Middlewares([ProtectedRouteMiddleware.apiProtectedDataRoute()])
export class InventoryBinLocationsController extends Controller {
  public static readonly exampleData: InventoryBinLocation[] = [
    {
      binLocation: 'MS011113020111',
      locationSide: 'Right',
      status: 'Occupied',
      containerType: 'Inventory Tote',
      skus: [
        {sku: 'sku1', quantity: 5},
        {sku: 'sku2', quantity: 10},
      ],
    },
  ];

  static readonly exampleResponse: InventoryBinLocationsResponse = {
    data: InventoryBinLocationsController.exampleData,
  };

  /**
   * Endpoint for retrieving bin locations for a given DMS aisle and level.
   * @param {string} aisle The double digit aisle code for the bin locations to retrieve.
   * @param {string} level The double digit level code for the bin locations to retrieve.
   * @returns {Promise<InventoryBinLocationsResponse>} contract
   * @throws IctError
   */
  @Example<InventoryBinLocationsResponse>(
    InventoryBinLocationsController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/bin-locations')
  @OperationId('GetInventoryBinLocations')
  @Tags('inventory')
  public async getInventoryBinLocations(
    @Query() aisle: string,
    @Query() level: string
  ): Promise<InventoryBinLocationsResponse> {
    const inventoryService = Container.get(InventoryService);

    const binLocations = await inventoryService.getInventoryBinLocations(
      aisle,
      level
    );

    if (binLocations.length === 0) {
      throw IctError.noContent();
    }

    return {data: binLocations};
  }
}
