import {
  CacheMiddleware,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {InventoryService} from '../services/inventory-service.ts';
import {InventoryStockDistributionOverData} from '../defs/inventory-stock-distribution-over-def.ts';

@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [
      configPostgresDatabase(),
      {
        type: DatabaseTypes.BigQuery,
      },
    ],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class InventoryStockDistributionOverPercentageSeriesController extends Controller {
  public static readonly exampleData: InventoryStockDistributionOverData = {
    overInventory: [
      {
        name: '2022-01-01T08:00:00.000Z',
        value: 92,
        isValidPercentage: true,
      },
      {
        name: '2022-01-01T09:00:00.000Z',
        value: 85,
        isValidPercentage: true,
      },
      {
        name: '2022-01-01T10:00:00.000Z',
        value: 89,
        isValidPercentage: true,
      },
      {
        name: '2022-01-01T11:00:00.000Z',
        value: 95,
        isValidPercentage: true,
      },
      {
        name: '2022-01-01T12:00:00.000Z',
        value: 94,
        isValidPercentage: true,
      },
      {
        name: '2022-01-01T13:00:00.000Z',
        value: 92,
        isValidPercentage: true,
      },
      {
        name: '2022-01-01T14:00:00.000Z',
        value: 90,
        isValidPercentage: true,
      },
    ],
  };

  public static readonly exampleResponse: InventoryStockDistributionOverData =
    InventoryStockDistributionOverPercentageSeriesController.exampleData;

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {Promise<InventoryStockDistributionOverData>} contract
   */
  @Example<InventoryStockDistributionOverData>(
    InventoryStockDistributionOverPercentageSeriesController.exampleResponse,
  )
  @SuccessResponse('200')
  @Get('/wms/stock/distribution/over/percentage/series')
  @OperationId('GetInventoryWmsStockDistributionOverPercentageSeries')
  @Tags('inventory')
  public async getInventoryStockDistributionOverPercentageSeries(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
  ): Promise<InventoryStockDistributionOverData> {
    // get the inventory service
    const inventoryService = Container.get(InventoryService);

    startEndDateValidation(startDate, endDate);

    return await inventoryService.getOverInventoryPercentageHistoricalSeriesData(
      startDate,
      endDate,
    );
  }
}
