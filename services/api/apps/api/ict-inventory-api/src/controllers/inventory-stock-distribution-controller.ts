import {
  CacheMiddleware,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Path,
} from 'tsoa';
import {InventoryService} from '../services/inventory-service.ts';
import type {
  DistributionType,
  InventoryDistributionStatusResponse,
} from '../defs/inventory-distribution-status-def.ts';

@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [
      configPostgresDatabase(),
      {
        type: DatabaseTypes.BigQuery,
      },
    ],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class InventoryStockDistributionController extends Controller {
  public static readonly exampleData: InventoryDistributionStatusResponse = {
    noInventory: [
      {
        name: '2020-01-01',
        value: 20,
        isValidPercentage: true,
      },
      {
        name: '2020-01-02',
        value: 17,
        isValidPercentage: true,
      },
      {
        name: '2020-01-03',
        value: 24,
        isValidPercentage: true,
      },
      {
        name: '2020-01-04',
        value: 42,
        isValidPercentage: true,
      },
    ],
  };

  public static readonly exampleResponse: InventoryDistributionStatusResponse =
    InventoryStockDistributionController.exampleData;

  @Example<InventoryDistributionStatusResponse>(
    InventoryStockDistributionController.exampleResponse,
  )
  @SuccessResponse('200')
  @Get('/stock/distribution/{distributionType}/percentage/series')
  @OperationId('GetInventoryStockDistributionPercentageSeries')
  @Tags('inventory')
  public async getInventoryStockDistributionAt(
    @Path() distributionType: DistributionType,
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
  ): Promise<InventoryDistributionStatusResponse> {
    startEndDateValidation(startDate, endDate);
    const inventoryService = Container.get(InventoryService);

    return await inventoryService.getInventoryPercentageHistoricalDataAsync(
      startDate,
      endDate,
      distributionType,
    );
  }
}
