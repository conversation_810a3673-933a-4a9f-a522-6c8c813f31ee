import {
  ApiResponse,
  CacheMiddleware,
  Container,
  HealthStatus,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {
  InventoryAccuracy,
  InventoryAccuracyConfig,
} from '../defs/inventory-accuracy-def.ts';
import {InventoryService} from '../services/inventory-service.ts';

@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class InventoryAccuracyController extends Controller {
  public static readonly exampleData: InventoryAccuracy = {
    accuracy: 90,
    status: HealthStatus.Ok,
    areaFilter: 'All',
  };

  public static readonly exampleConfig: InventoryAccuracyConfig = {
    seg1: 80,
    seg2: 87,
    seg3: 90,
  };

  public static readonly exampleResponse: ApiResponse<
    InventoryAccuracy,
    InventoryAccuracyConfig
  > = {
    accuracy: 90,
    status: HealthStatus.Ok,
    areaFilter: 'All',
    metadata: InventoryAccuracyController.exampleConfig,
  };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @param {string} area_filter
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {Promise<ApiResponse<InventoryAccuracy, InventoryAccuracyConfig>>} contract
   * @throws IctError
   */
  @Example<ApiResponse<InventoryAccuracy, InventoryAccuracyConfig>>(
    InventoryAccuracyController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/accuracy')
  @OperationId('GetInventoryAccuracy')
  @Tags('inventory')
  public async getInventoryAccuracy(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
    @Query('area_filter') areaFilter: string = 'All'
  ): Promise<ApiResponse<InventoryAccuracy, InventoryAccuracyConfig>> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // get the inventory service
    const inventoryService = Container.get(InventoryService);

    // retrieve average inventory accuracy from the service
    const inventoryAccuracy =
      await inventoryService.getAverageInventoryAccuracy(
        startDate.toISOString(),
        endDate.toISOString(),
        areaFilter
      );

    const contract: ApiResponse<InventoryAccuracy, InventoryAccuracyConfig> = {
      ...inventoryAccuracy,
      metadata: InventoryAccuracyController.exampleConfig,
    };

    return contract;
  }
}
