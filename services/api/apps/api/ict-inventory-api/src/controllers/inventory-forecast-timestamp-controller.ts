import {Container, ProtectedRouteMiddleware} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {InventoryForecastDataAnalysisTimestamp} from '../defs/inventory-forecast-def.ts';
import {InventoryService} from '../services/inventory-service.ts';

@Route('inventory')
@Middlewares([ProtectedRouteMiddleware.apiProtectedDataRoute()])
export class InventoryForecastTimestampController extends Controller {
  public static readonly exampleData: InventoryForecastDataAnalysisTimestamp = {
    dataUpdateTimestamp: '2024-08-05T13:00:00.000Z',
    analysisPerformedTimestamp: '2024-08-05T13:15:00.000Z',
  };

  static readonly exampleResponse: InventoryForecastDataAnalysisTimestamp =
    InventoryForecastTimestampController.exampleData;

  /**
   *
   * @returns InventoryForecastDataAnalysisTimestamp contract
   * @throws IctError
   */
  @Example<InventoryForecastDataAnalysisTimestamp>(
    InventoryForecastTimestampController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/forecast/data-analysis-timestamp')
  @OperationId('GetDataAnalysisTimestampData')
  @Tags('inventory')
  public async getInventoryForecastDataAnalysisTimestamp(): Promise<InventoryForecastDataAnalysisTimestamp> {
    const inventoryService = Container.get(InventoryService);

    return await inventoryService.getInventoryForecastTimestampAsync();
  }
}
