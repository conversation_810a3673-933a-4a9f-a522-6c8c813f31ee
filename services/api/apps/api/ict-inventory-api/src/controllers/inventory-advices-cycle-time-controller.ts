import {
  CacheMiddleware,
  Container,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {InventoryService} from '../services/inventory-service.ts';
import {AdvicesCycleTimeData} from '../defs/inventory-advices-cycle-time-def.ts';

@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class InventoryAdvicesCycleTimeController extends Controller {
  public static readonly exampleData: AdvicesCycleTimeData = {cycleTime: 2};

  public static readonly exampleResponse: AdvicesCycleTimeData =
    InventoryAdvicesCycleTimeController.exampleData;

  /**
   * @returns {Promise<AdvicesCycleTimeData>} contract
   */
  @Example<AdvicesCycleTimeData>(
    InventoryAdvicesCycleTimeController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/advices/cycle-time')
  @OperationId('GetInventoryAdvicesCycleTime')
  @Tags('inventory')
  public async getInventoryAdvicesCycleTime(): Promise<AdvicesCycleTimeData> {
    // get the inventory service
    const inventoryService = Container.get(InventoryService);

    return await inventoryService.getAdvicesCycleTime();
  }
}
