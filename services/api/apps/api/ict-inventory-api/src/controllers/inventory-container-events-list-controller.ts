import {
  BaseFilterType,
  CacheMiddleware,
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  PaginatedRequestNoDates,
  ProtectedRouteMiddleware,
  ExportFormatter,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Middlewares,
  Path,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Post,
  Body,
  Produces,
} from 'tsoa';
import {Readable} from 'stream';
import {InventoryService} from '../services/inventory-service.ts';
import {PostInventoryContainerEventsListResponse} from '../defs/inventory-container-events-list-def.ts';
import {IncludedColumns} from '../defs/inventory-export-def.ts';

@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class InventoryContainerEventsListController extends Controller {
  public static readonly exampleData = [
    {
      timestamp: '2024-09-26 12:54 PM',
      event: 'FREECYCLECOUNT',
      workstationCode: 'M11-GTP-03',
      destinationContainer: '000000',
      operator: 'EUD202284',
      sku: 'TRL1_STACK',
      quantity: 0,
    },
  ];

  public static readonly exampleConfig = {
    limit: 10,
    page: 1,
    totalResults: 1,
  };

  public static readonly exampleResponse: PostInventoryContainerEventsListResponse =
    {
      data: InventoryContainerEventsListController.exampleData,
      metadata: InventoryContainerEventsListController.exampleConfig,
    };

  /**
   * Post inventory container events data with filters and sorting.
   *
   * @param containerId Container identifier.
   * @param paginatedRequest Request body with filters, sorting, pagination, and months parameter.
   * @returns Paginated response with events data.
   */
  @Example(InventoryContainerEventsListController.exampleResponse)
  @SuccessResponse('200')
  @Post('/container-events/list/{containerId}')
  @OperationId('PostInventoryContainerEventsList')
  @Tags('inventory')
  public async postInventoryContainerEventsList(
    @Body()
    paginatedRequest: PaginatedRequestNoDates & {
      months?: number;
    },
    @Path() containerId: string,
  ): Promise<PostInventoryContainerEventsListResponse> {
    const inventoryService = Container.get(InventoryService);

    const response =
      await inventoryService.getInventoryContainerEventsListAsync(
        containerId,
        paginatedRequest,
      );
    return {
      data: response,
      metadata: {
        page: paginatedRequest.page || 1,
        limit: paginatedRequest.limit || 50,
        totalResults: response.length,
      },
    };
  }

  /**
   * Creates an excel export for inventory container events.
   *
   * @param containerId Container identifier.
   * @param paginatedRequest Request body with filters, sorting, pagination, columns, and months parameter.
   * @returns Excel buffer with events data.
   */
  @SuccessResponse('200')
  @Post('/container-events/list/export/{containerId}')
  @OperationId('PostInventoryContainerEventsListExport')
  @Produces('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
  @Tags('inventory')
  public async postInventoryContainerEventsListExport(
    @Body()
    paginatedRequest: PaginatedRequestNoDates & {
      months?: number;
      columns: IncludedColumns;
    },
    @Path() containerId: string,
  ): Promise<Readable> {
    const inventoryService = Container.get(InventoryService);

    // What's a reasonable limit when we want it all but need to provide a limit
    // or repeat the existing query? Keep the filters, set the rest for export.
    const response =
      await inventoryService.getInventoryContainerEventsListAsync(containerId, {
        reqFilter: paginatedRequest.filters as BaseFilterType,
        sortFields: [],
        page: 0,
        limit: 100000,
        months: paginatedRequest.months,
      });

    const updatedReponse = ExportFormatter.mapResponseToColumns(
      paginatedRequest.columns,
      response,
    );

    const buffer: Buffer = ExportFormatter.generateExcelFile(updatedReponse);

    const today = new Date();
    const formattedDate = `${today.getMonth() + 1}-${today.getDate()}-${today.getFullYear()}`;
    const filename = `Events_${containerId}_Forward-Pick_${formattedDate}.xlsx`;

    // Set headers for Excel file download
    this.setHeader('Content-Disposition', `attachment; filename=${filename}`);
    this.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );

    // Create a readable stream from the buffer
    const stream = new Readable();
    stream.push(buffer);
    stream.push(null); // Signal the end of the stream
    return stream;
  }
}
