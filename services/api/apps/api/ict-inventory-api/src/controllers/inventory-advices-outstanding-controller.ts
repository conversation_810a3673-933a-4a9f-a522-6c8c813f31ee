import {
  CacheMiddleware,
  Container,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {InventoryService} from '../services/inventory-service.ts';
import {AdvicesOutstandingData} from '../defs/inventory-advices-outstanding-def.ts';

@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class InventoryAdvicesOutstandingController extends Controller {
  public static readonly exampleData: AdvicesOutstandingData = {
    outstandingAdvices: 2,
  };
  public static readonly exampleResponse: AdvicesOutstandingData =
    InventoryAdvicesOutstandingController.exampleData;

  /**
   * @returns {Promise<AdvicesOutstandingData>} contract
   */
  @Example<AdvicesOutstandingData>(
    InventoryAdvicesOutstandingController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/advices/outstanding')
  @OperationId('GetInventoryAdvicesOutstanding')
  @Tags('inventory')
  public async getInventoryAdvicesOutstanding(): Promise<AdvicesOutstandingData> {
    // get the inventory service
    const inventoryService = Container.get(InventoryService);

    return await inventoryService.getAdvicesOutstanding();
  }
}
