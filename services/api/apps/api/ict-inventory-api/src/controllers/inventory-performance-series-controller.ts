import {
  CacheMiddleware,
  Container,
  IctError,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {InventoryService} from '../services/inventory-service.ts';
import {
  InventoryPerformanceSeriesContract,
  InventoryPerformanceSeriesData,
} from '../defs/inventory-performance-series-def.ts';

@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class InventoryPerformanceSeriesController extends Controller {
  public static readonly exampleInventoryAccuracyData: InventoryPerformanceSeriesData =
    {
      name: 'Inventory Accuracy',
      series: [
        {
          name: '2023-10-01',
          value: 50,
        },
        {
          name: '2023-11-01',
          value: 65,
        },
      ],
    };
  public static readonly exampleOrderFulfillmentData: InventoryPerformanceSeriesData =
    {
      name: 'Order Fulfillment',
      series: [
        {
          name: '2023-10-01',
          value: 54,
        },
        {
          name: '2023-11-01',
          value: 23,
        },
      ],
    };
  public static readonly exampleStorageUtilizationData: InventoryPerformanceSeriesData =
    {
      name: 'Storage Utilization',
      series: [
        {
          name: '2023-10-01',
          value: 70,
        },
        {
          name: '2023-11-01',
          value: 87,
        },
      ],
    };
  public static readonly exampleStockOutData: InventoryPerformanceSeriesData = {
    name: 'Stock Out',
    series: [
      {
        name: '2023-10-01',
        value: 10,
      },
      {
        name: '2023-11-01',
        value: 9,
      },
    ],
  };
  public static readonly exampleData: InventoryPerformanceSeriesData[] = [
    InventoryPerformanceSeriesController.exampleInventoryAccuracyData,
    InventoryPerformanceSeriesController.exampleOrderFulfillmentData,
    InventoryPerformanceSeriesController.exampleStorageUtilizationData,
    InventoryPerformanceSeriesController.exampleStockOutData,
  ];
  public static readonly exampleResponse: InventoryPerformanceSeriesContract =
    InventoryPerformanceSeriesController.exampleData;

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @param {string} department_filter
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {Promise<InventoryHandlingUnitsTrayedContract>} contract
   */
  @Example<InventoryPerformanceSeriesContract>(
    InventoryPerformanceSeriesController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/performance/series')
  @OperationId('GetInventoryPerformanceSeries')
  @Tags('inventory')
  public async getInventoryPerformanceSeries(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
    @Query('department_filter') departmentFilter: string
  ): Promise<InventoryPerformanceSeriesContract> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);

    // Get the inventory service
    const inventoryService = Container.get(InventoryService);

    const settings = {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      departmentFilter,
    };

    const [
      inventoryAccuracy,
      orderFulfillmentRate,
      storageUtilization,
      stockOut,
    ] = await Promise.all([
      inventoryService.getInventoryAccuracyMonthlyAggregatedSeries(settings),
      inventoryService.getOrderFulfillmentRateMonthlyAggregatedSeries(settings),
      inventoryService.getStorageUtilizationMonthlyAggregatedSeries(settings),
      inventoryService.getStockOutMonthlyAggregatedSeries(settings),
    ]);

    // only return a 204 No Content response if none of the series have any data
    if (
      inventoryAccuracy.series.length === 0 &&
      orderFulfillmentRate.series.length === 0 &&
      storageUtilization.series.length === 0 &&
      stockOut.series.length === 0
    ) {
      throw IctError.noContent();
    }

    return [
      inventoryAccuracy,
      orderFulfillmentRate,
      storageUtilization,
      stockOut,
    ];
  }
}
