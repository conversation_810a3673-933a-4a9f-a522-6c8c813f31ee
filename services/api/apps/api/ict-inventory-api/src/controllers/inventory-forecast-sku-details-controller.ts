import {
  ChartSeriesData,
  Container,
  DatabaseTypes,
  PostgresDatabaseOptions,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Path,
} from 'tsoa';
import {EntityTypes} from 'ict-api-schema';
import {
  InventorySkuForecastDetails,
  InventoryForecastSkuLocationAreas,
  InventoryForecastSkuLocationByArea,
  InventoryForecastSkuLocationDetails,
  InventoryForecastSkuOrderDetails,
  InventoryForecastSkuOrders,
  InventoryKnownDemand,
} from '../defs/inventory-forecast-def.ts';
import {InventoryService} from '../services/inventory-service.ts';

const postgresDbOptions: PostgresDatabaseOptions = {
  entityType: EntityTypes.Config,
};

const dbMiddleware = {
  databases: [
    {type: DatabaseTypes.BigQuery},
    {
      type: DatabaseTypes.Postgres,
      options: postgresDbOptions,
    },
  ],
};
@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, dbMiddleware),
])
export class InventoryForecastSkuDetailsController extends Controller {
  public static readonly exampleReserveStorageAreaData: InventoryForecastSkuLocationDetails =
    {
      locationId: 'RESERVE LOC 1',
      locationType: 'Bulk',
      containerId: 'CONT ABC',
      containerCount: 1,
      quantity: 40,
      uom: 'Eaches',
      containerDimensions: '1',
      conditionCode: 'UNRESTRICTED',
      zone: 'ZONE 2',
      lastActivityDate: '2024-05-08 09:59:58',
    };

  public static readonly exampleForwardPickAreaData: InventoryForecastSkuLocationDetails =
    {
      locationId: 'FORWARD PICK 2',
      locationType: 'Fast',
      containerId: null,
      containerCount: 2,
      quantity: 600,
      uom: 'Eaches',
      skuSize: 'NS',
      containerDimensions: '24" - 49"',
      conditionCode: 'UNRESTRICTED',
      zone: 'ASRS',
      lastActivityDate: '2024-04-25 16:50:55',
    };

  public static readonly exampleReserveArea: InventoryForecastSkuLocationByArea =
    {
      area: 'Reserve Storage',
      details: [
        InventoryForecastSkuDetailsController.exampleReserveStorageAreaData,
      ],
    };

  public static readonly examplePickArea: InventoryForecastSkuLocationByArea = {
    area: 'Forward Pick',
    details: [InventoryForecastSkuDetailsController.exampleForwardPickAreaData],
  };

  static readonly exampleLocationResponse: InventoryForecastSkuLocationAreas = {
    data: [
      InventoryForecastSkuDetailsController.exampleReserveArea,
      InventoryForecastSkuDetailsController.examplePickArea,
    ],
  };

  public static readonly exampleOrderOneData: InventoryForecastSkuOrderDetails =
    {
      skuId: 'SKU123',
      orderId: 'ORDER 1',
      priority: '1',
      allocationDate: '2024-06-28 13:45:57 UTC',
      shipDate: '2024-06-28',
      orderLines: 12,
      allocatedQty: 2,
    };

  public static readonly exampleOrderTwoData: InventoryForecastSkuOrderDetails =
    {
      skuId: 'SKU123',
      orderId: 'ORDER 2',
      priority: '1',
      allocationDate: '2024-06-28 05:12:22 UTC',
      shipDate: '2024-06-28',
      orderLines: 5,
      allocatedQty: 10,
    };

  static readonly exampleOrderResponse: InventoryForecastSkuOrders = {
    skuId: 'SKU123',
    openOrderCount: 2,
    data: [
      InventoryForecastSkuDetailsController.exampleOrderOneData,
      InventoryForecastSkuDetailsController.exampleOrderTwoData,
    ],
  };

  static readonly exampleKnownDemandData: InventoryKnownDemand = {
    percentage: 45,
    quantity: 85,
  };

  static readonly examplePredictedDemandSeriesData: ChartSeriesData[] = [
    {
      name: '2024-10-08T12:05:06.615Z',
      value: 18,
    },
    {
      name: '2024-10-11T16:10:26.829Z',
      value: 0,
    },
    {
      name: '2024-10-14T03:16:45.028Z',
      value: 30,
    },
  ];

  static readonly exampleActualDemandSeriesData: ChartSeriesData[] = [
    {
      name: '2024-10-08T12:05:06.615Z',
      value: 12,
    },
    {
      name: '2024-10-11T16:10:26.829Z',
      value: 0,
    },
    {
      name: '2024-10-14T03:16:45.028Z',
      value: 26,
    },
  ];

  static readonly exampleConfidenceLowSeriesData: ChartSeriesData[] = [
    {
      name: '2024-10-08T12:05:06.615Z',
      value: 0,
    },
    {
      name: '2024-10-11T16:10:26.829Z',
      value: 8,
    },
    {
      name: '2024-10-14T03:16:45.028Z',
      value: 0,
    },
  ];

  static readonly exampleConfidenceHighSeriesData: ChartSeriesData[] = [
    {
      name: '2024-10-08T12:05:06.615Z',
      value: 27,
    },
    {
      name: '2024-10-11T16:10:26.829Z',
      value: 6,
    },
    {
      name: '2024-10-14T03:16:45.028Z',
      value: 1,
    },
  ];

  static readonly exampleForecastResponse: InventorySkuForecastDetails = {
    confidence: 10,
    knownDemand: InventoryForecastSkuDetailsController.exampleKnownDemandData,
    shortTermDaily: 180,
    shortTermDailyDays: 88,
    longTermDaily: 178,
    longTermDailyDays: 360,
    nonZeroDemand: 60,
    zeroDemandIntermittency: 3,
    predictedDemandSeries:
      InventoryForecastSkuDetailsController.examplePredictedDemandSeriesData,
    actualDemandSeries:
      InventoryForecastSkuDetailsController.exampleActualDemandSeriesData,
    confidenceLowSeries:
      InventoryForecastSkuDetailsController.exampleConfidenceLowSeriesData,
    confidenceHighSeries:
      InventoryForecastSkuDetailsController.exampleConfidenceHighSeriesData,
  };

  /**
   *
   * @returns InventoryForecastSkuLocationAreas contract
   * @throws IctError
   */
  @Example<InventoryForecastSkuLocationAreas>(
    InventoryForecastSkuDetailsController.exampleLocationResponse
  )
  @SuccessResponse('200')
  @Get('/forecast/{skuId}/locations')
  @OperationId('GetInventoryForecastSkuLocations')
  @Tags('inventory')
  public async getInventoryForecastSkuLocations(
    @Path() skuId: string
  ): Promise<InventoryForecastSkuLocationAreas> {
    const inventoryService = Container.get(InventoryService);

    const decodedSkuId = decodeURIComponent(skuId);

    return await inventoryService.getInventoryForecastSkuLocations(
      decodedSkuId
    );
  }

  /**
   *
   * @returns InventoryForecastSkuOrders contract
   * @throws IctError
   */
  @Example<InventoryForecastSkuOrders>(
    InventoryForecastSkuDetailsController.exampleOrderResponse
  )
  @SuccessResponse('200')
  @Get('/forecast/{skuId}/orders')
  @OperationId('GetInventoryForecastSkuOrders')
  @Tags('inventory')
  public async getInventoryForecastSkuOrders(
    @Path() skuId: string
  ): Promise<InventoryForecastSkuOrders> {
    const inventoryService = Container.get(InventoryService);

    const decodedSkuId = decodeURIComponent(skuId);

    return await inventoryService.getInventoryForecastSkuOrders(decodedSkuId);
  }

  /**
   *
   * @returns InventoryForecastSkuForecastDetails contract
   * @throws IctError
   */
  @Example<InventorySkuForecastDetails>(
    InventoryForecastSkuDetailsController.exampleForecastResponse
  )
  @SuccessResponse('200')
  @Get('/forecast/{skuId}')
  @OperationId('GetInventorySkuForecast')
  @Tags('inventory')
  public async getInventoryForecastSkuForecast(
    @Path() skuId: string
  ): Promise<InventorySkuForecastDetails> {
    const inventoryService = Container.get(InventoryService);

    const decodedSkuId = decodeURIComponent(skuId);

    return await inventoryService.getInventoryForecastSkuForecastDetails(
      decodedSkuId
    );
  }
}
