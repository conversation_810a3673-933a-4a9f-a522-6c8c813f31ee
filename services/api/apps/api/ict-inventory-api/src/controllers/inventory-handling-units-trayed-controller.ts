import {
  CacheMiddleware,
  Container,
  ProtectedRouteMiddleware,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Query,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {InventoryService} from '../services/inventory-service.ts';
import {
  InventoryHandlingUnitsTrayedConfig,
  InventoryHandlingUnitsTrayedContract,
  InventoryHandlingUnitsTrayedData,
} from '../defs/inventory-total-handling-units-trayed-def.ts';

@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class InventoryHandlingUnitsTrayedController extends Controller {
  public static readonly exampleData: {
    timePeriodInHours: number;
    data: InventoryHandlingUnitsTrayedData;
  } = {
    timePeriodInHours: 24,
    data: {
      handlingUnitsTrayed: 100,
    },
  };
  public static readonly exampleConfig: InventoryHandlingUnitsTrayedConfig = {
    timePeriodInHours:
      InventoryHandlingUnitsTrayedController.exampleData.timePeriodInHours,
  };
  public static readonly exampleResponse: InventoryHandlingUnitsTrayedContract =
    {
      data: InventoryHandlingUnitsTrayedController.exampleData.data,
      metadata: InventoryHandlingUnitsTrayedController.exampleConfig,
    };

  /**
   *
   * @param {Date} start_date
   * @param {Date} end_date
   * @isDateTime start_date
   * @isDateTime end_date
   * @returns {Promise<InventoryHandlingUnitsTrayedContract>} contract
   */
  @Example<InventoryHandlingUnitsTrayedContract>(
    InventoryHandlingUnitsTrayedController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/handling-units/trayed')
  @OperationId('GetInventoryHandlingUnitsTrayed')
  @Tags('inventory')
  public async getInventoryHandlingUnitsTrayed(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<InventoryHandlingUnitsTrayedContract> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    // get the inventory service
    const inventoryService = Container.get(InventoryService);

    const handlingUnitsTrayed =
      await inventoryService.getInventoryHandlingUnitsTrayedAsync(
        startDate.toISOString(),
        endDate.toISOString()
      );

    const contract: InventoryHandlingUnitsTrayedContract = {
      data: handlingUnitsTrayed.data,
      metadata: {
        timePeriodInHours: handlingUnitsTrayed.timePeriodInHours,
      },
    };
    return contract;
  }
}
