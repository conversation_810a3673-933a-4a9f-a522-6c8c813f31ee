import {
  AuthMiddleware,
  Container,
  ContextMiddleware,
  ContextService,
  HttpStatusCodes,
  IctError,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  OperationId,
  Post,
  Route,
  SuccessResponse,
  Tags,
  UploadedFile,
} from 'tsoa';
import {InventoryService} from '../services/inventory-service.ts';
import {InventoryUploadRecentActivity} from '../defs/inventory-upload-def.ts';

const authMiddleware = Container.get(AuthMiddleware);
const contextMiddleware = Container.get(ContextMiddleware);
const inventoryService = Container.get(InventoryService);

const tenant = 'superior_uniform';

@Route('inventory')
@Middlewares([
  // TODO: This was required to get Mu<PERSON> working with our middlewares. Is there a better solution?
  authMiddleware.use,
  contextMiddleware.use,
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
])
export class InventoryUploadKnownDemandController extends Controller {
  public static readonly exampleResponse: InventoryUploadRecentActivity[] = [
    {
      date: '2025-02-18T13:48:59',
      knownOrderCount: 2454,
      knownOrderLineCount: 31490,
    },
    {
      date: '2025-02-18T11:33:59',
      knownOrderCount: 2454,
      knownOrderLineCount: 31490,
    },
    {
      date: '2025-02-17T15:41:59',
      knownOrderCount: 2268,
      knownOrderLineCount: 30987,
    },
    {
      date: '2025-02-17T12:23:00',
      knownOrderCount: 2268,
      knownOrderLineCount: 30987,
    },
    {
      date: '2025-02-17T07:13:00',
      knownOrderCount: 2268,
      knownOrderLineCount: 30987,
    },
    {
      date: '2025-02-16T13:41:00',
      knownOrderCount: 2936,
      knownOrderLineCount: 33736,
    },
    {
      date: '2025-02-16T12:34:59',
      knownOrderCount: 2936,
      knownOrderLineCount: 33736,
    },
    {
      date: '2025-02-15T14:42:00',
      knownOrderCount: 1730,
      knownOrderLineCount: 16854,
    },
    {
      date: '2025-02-15T12:12:59',
      knownOrderCount: 1730,
      knownOrderLineCount: 16854,
    },
    {
      date: '2025-02-14T11:27:00',
      knownOrderCount: 2197,
      knownOrderLineCount: 23452,
    },
  ];

  /**
   * Receives two xls files and parses them into multiline JSON to forward to the EDP PubSub Topic
   * @returns Count of records processed before sending to PubSub
   * @throws IctError
   */
  @SuccessResponse(HttpStatusCodes.OK)
  @OperationId('PostInventoryUploadKnownDemand')
  @Tags('inventory')
  @Post('/upload/known-demand')
  public async postInventoryUploadKnownDemand(
    @UploadedFile() manager: Express.Multer.File,
    @UploadedFile() details: Express.Multer.File
  ) {
    // This feature should only be available to SU users.
    const recTime = Date.now(); // Unix timestamp in milliseconds, do not convert with .toString()

    const contextService = Container.get(ContextService);
    if (contextService.datasetId !== tenant) throw IctError.unauthorized();

    // Check if both files are uploaded
    if (!manager || !details) {
      throw IctError.unprocessableRequest(
        'Both manager and details files must be uploaded.'
      );
    }

    // Check if the files have the same name
    if (manager.originalname === details.originalname) {
      throw IctError.unprocessableRequest(
        'Uploaded files cannot have the same file name.'
      );
    }

    return await inventoryService.handleInventoryUploadKnownDemand(
      manager,
      details,
      recTime
    );
  }

  /**
   *
   * @returns InventoryUploadHistory[] contract
   * @throws IctError
   */
  @Example<InventoryUploadRecentActivity[]>(
    InventoryUploadKnownDemandController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/upload/recent-activity')
  @OperationId('GetInventoryUploadRecentActivity')
  @Tags('inventory')
  public async getInventoryForecastSkuOrders(): Promise<
    InventoryUploadRecentActivity[]
  > {
    return await inventoryService.getInventoryUploadRecentActivity();
  }
}
