import {
  type BaseFilterType,
  Container,
  startEndDateValidation,
  ProtectedRouteMiddleware,
  type PaginatedRequest,
  PostgresDatabaseOptions,
  DatabaseTypes,
} from 'ict-api-foundations';
import {
  Body,
  Controller,
  Example,
  Middlewares,
  Post,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {EntityTypes} from 'ict-api-schema';
import {InventoryService} from '../services/inventory-service.ts';
import {
  InventoryHighImpactSKU,
  InventoryHighImpactSKUContract,
  InventoryHighImpactSkuPaginationInfo,
} from '../defs/inventory-high-impact-sku-def.ts';

// include postgres as a usable database for config settings in query
const postgresDbOptions: PostgresDatabaseOptions = {
  entityType: EntityTypes.Config,
};

const dbOptions = {
  databases: [
    {type: DatabaseTypes.BigQuery},
    {
      type: DatabaseTypes.Postgres,
      options: postgresDbOptions,
    },
  ],
};

@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, dbOptions),
])
export class InventoryHighImpactSkuController extends Controller {
  public static readonly exampleData: InventoryHighImpactSKU[] = [
    {
      sku: '123456',
      accuracy: 0.9,
      storageArea: 'A',
      quantity: 100,
      cubeUtilization: 0.5,
      daysOnHand: 10,
    },
  ];
  public static readonly exampleConfig: InventoryHighImpactSkuPaginationInfo = {
    page: 1,
    limit: 10,
    totalResults: 1,
  };
  public static readonly exampleResponse: InventoryHighImpactSKUContract = {
    data: InventoryHighImpactSkuController.exampleData,
    metadata: InventoryHighImpactSkuController.exampleConfig,
  };

  /**
   * @returns {Promise<InventoryHighImpactSKUContract>} contract
   */
  @Example<InventoryHighImpactSKUContract>(
    InventoryHighImpactSkuController.exampleResponse
  )
  @SuccessResponse('200')
  @Post('/sku/high-impact/list')
  @OperationId('PostInventorySkuHighImpactList')
  @Tags('inventory')
  public async getInventoryHighImpactSku(
    @Body()
    paginatedRequest: PaginatedRequest
  ): Promise<InventoryHighImpactSKUContract> {
    // Validate the start and end dates
    startEndDateValidation(
      paginatedRequest.start_date,
      paginatedRequest.end_date
    );
    const inventoryService = Container.get(InventoryService);
    const {
      start_date: startDate,
      end_date: endDate,
      filters,
      sortFields,
      page,
      limit,
    } = paginatedRequest;

    const skuListContract =
      await inventoryService.getInventoryHighImpactSkuListAsync({
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        reqFilter: filters as BaseFilterType,
        sortFields,
        page,
        limit,
      });
    return skuListContract;
  }
}
