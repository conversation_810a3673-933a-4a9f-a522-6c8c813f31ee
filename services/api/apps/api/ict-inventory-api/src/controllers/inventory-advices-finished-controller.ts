import {
  CacheMiddleware,
  Container,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {InventoryService} from '../services/inventory-service.ts';
import {AdvicesFinishedData} from '../defs/inventory-finished-advices-def.ts';

@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class InventoryAdvicesFinishedController extends Controller {
  public static readonly exampleData: AdvicesFinishedData = {
    finishedAdvices: 2,
  };

  public static readonly exampleResponse: AdvicesFinishedData =
    InventoryAdvicesFinishedController.exampleData;

  /**
   * @returns {Promise<AdvicesFinishedData>} contract
   */
  @Example<AdvicesFinishedData>(
    InventoryAdvicesFinishedController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/advices/finished')
  @OperationId('GetInventoryAdvicesFinished')
  @Tags('inventory')
  public async getInventoryAdvicesFinished(): Promise<AdvicesFinishedData> {
    // get the inventory service
    const inventoryService = Container.get(InventoryService);

    return await inventoryService.getAdvicesFinished();
  }
}
