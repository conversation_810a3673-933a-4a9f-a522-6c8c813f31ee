import {
  Container,
  ProtectedRouteMiddleware,
  configPostgresDatabase,
  DatabaseTypes,
  startEndDateValidation,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Query,
  Get,
} from 'tsoa';
import {TaskTypeData} from '../defs/inventory-replenishment-details-def.ts';
import {InventoryService} from '../services/inventory-service.ts';

@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [
      configPostgresDatabase(),
      {
        type: DatabaseTypes.BigQuery,
      },
    ],
  }),
])
export class InventoryReplenishmentTaskTypeSeriesController extends Controller {
  // Example response data
  public static readonly exampleResponse: TaskTypeData = {
    demand: [
      {name: '2025-01-01', value: 15},
      {name: '2025-01-02', value: 12},
      {name: '2025-01-03', value: 18},
      {name: '2025-01-04', value: 10},
      {name: '2025-01-05', value: 22},
      {name: '2025-01-06', value: 8},
      {name: '2025-01-07', value: 14},
    ],
    topOff: [
      {name: '2025-01-01', value: 8},
      {name: '2025-01-02', value: 6},
      {name: '2025-01-03', value: 10},
      {name: '2025-01-04', value: 7},
      {name: '2025-01-05', value: 12},
      {name: '2025-01-06', value: 5},
      {name: '2025-01-07', value: 9},
    ],
    relocation: [
      {name: '2025-01-01', value: 5},
      {name: '2025-01-02', value: 3},
      {name: '2025-01-03', value: 8},
      {name: '2025-01-04', value: 4},
      {name: '2025-01-05', value: 7},
      {name: '2025-01-06', value: 2},
      {name: '2025-01-07', value: 6},
    ],
  };

  /**
   * @param {Date} start_date
   * @param {Date} end_date
   * @returns {Promise<TaskTypeData>} contract
   * @throws IctError
   */
  @Example<TaskTypeData>(
    InventoryReplenishmentTaskTypeSeriesController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/replenishment/task-type-series')
  @OperationId('GetInventoryReplenishmentTaskTypeSeries')
  @Tags('inventory')
  public async getInventoryReplenishmentTaskTypeSeries(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date
  ): Promise<TaskTypeData> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    const inventoryService = Container.get(InventoryService);

    return await inventoryService.getReplenishmentTaskTypeSeriesData(
      startDate,
      endDate
    );
  }
}
