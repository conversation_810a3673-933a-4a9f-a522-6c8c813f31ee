import {BaseHealthCheck} from 'ict-api-foundations';
import {Get, Route, OperationId, Tags} from 'tsoa';

@Route('/inventory/healthcheck')
export class InventoryHealthCheckController {
  @Get()
  @OperationId('GetInventoryBasicHealthCheck')
  @Tags('inventory')
  public getInventoryBasicHealthCheck() {
    return BaseHealthCheck.getBasicHealthCheck();
  }

  @Get('/full')
  @OperationId('GetInventoryFullHealthCheck')
  @Tags('inventory')
  public getInventoryFullHealthCheck() {
    return BaseHealthCheck.getDeepHealthCheck(['auth0', 'bigQuery', 'redis']);
  }
}
