import {
  CacheMiddleware,
  Container,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Get,
  Middlewares,
  Route,
  SuccessResponse,
  Path,
  Example,
  OperationId,
  Tags,
} from 'tsoa';
import {InventoryService} from '../services/inventory-service.ts';

import {
  AdviceDetailsData,
  AdvicesDetailsQueryResponse,
} from '../defs/inventory-advices-details-drawer-def.ts';
import {formatAdviceDetails} from '../utils/advice-details-formatter.ts';

@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class InventoryAdvicesDetailsController extends Controller {
  public static readonly exampleAdviceDetailsData: AdvicesDetailsQueryResponse[] =
    [
      {
        supplier_id: '001',
        handling_unit_id: 'HU-00001',
        handling_unit_type: 'PALLET',
        advice_line: 'ADV-00003#1',
        sku: 'SKU-00001',
        quantity: 10,
        packaging_level: 'CASE',
      },
    ];

  public static readonly exampleResponse: AdviceDetailsData = {
    supplierId: '001',
    itemsReceived: [
      {
        handlingUnit: 'HU-00001',
        handlingUnitType: 'PALLET',
        adviceLine: 'ADV-00003#1',
        sku: 'SKU-00001',
        quantity: 10,
        packagingLevel: 'CASE',
      },
    ],
  };
  /**
   *
   * @param adviceId
   * @returns {Promise<AdviceDetailsData>} advice details
   */
  @Example<AdviceDetailsData>(InventoryAdvicesDetailsController.exampleResponse)
  @SuccessResponse('200')
  @Get('/advices/{adviceId}/details')
  @OperationId('GetInventoryAdvicesDetails')
  @Tags('inventory')
  public async getAdviceDetailsController(
    @Path() adviceId: string
  ): Promise<AdviceDetailsData> {
    const inventoryService = Container.get(InventoryService);
    const decodedAdviceId = decodeURIComponent(adviceId);

    const adviceDetails =
      await inventoryService.getAdviceDetails(decodedAdviceId);

    return formatAdviceDetails(adviceDetails);
  }
}
