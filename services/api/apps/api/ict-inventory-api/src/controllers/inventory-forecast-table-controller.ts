import {
  BaseFilterType,
  Container,
  DatabaseTypes,
  type PaginatedRequestNoDates,
  PostgresDatabaseOptions,
  ProtectedRouteMiddleware,
  SortField,
  ExportFormatter,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Post,
  Body,
  Produces,
} from 'tsoa';
import {EntityTypes} from 'ict-api-schema';
import {Readable} from 'stream';
import {
  CurrentInventory,
  ForecastedInventory,
  InventoryForecastListing,
  InventoryForecastListingParams,
  InventoryForecastListingData,
  ProjectedInventory,
  InventoryForecastListingConfig,
} from '../defs/inventory-forecast-def.ts';
import {InventoryService} from '../services/inventory-service.ts';
import {IncludedColumns} from '../defs/inventory-export-def.ts';

const postgresDbOptions: PostgresDatabaseOptions = {
  entityType: EntityTypes.Config,
};

const dbMiddleware = {
  databases: [
    {type: DatabaseTypes.BigQuery},
    {
      type: DatabaseTypes.Postgres,
      options: postgresDbOptions,
    },
  ],
};
@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, dbMiddleware),
])
export class InventoryForecastTableController extends Controller {
  public static readonly exampleCurrentInventoryData: CurrentInventory = {
    reserveStorage: 100,
    forwardPick: 200,
  };
  public static readonly exampleProjectedInventoryData: ProjectedInventory = {
    pendingReplenishment: 200,
    pendingPicks: 50,
    allocatedOrders: 100,
    projectedForwardPick: 460,
  };

  public static readonly exampleForecastedInventoryData: ForecastedInventory = {
    averageReplenishment: 200,
    averageDemand: 100,
    demandTomorrow: 200,
    knownDemand: 37,
    forwardPickTomorrow: -122,
    twoDayDemand: 100,
    twoDayForwardPick: 200,
  };

  public static readonly exampleData: InventoryForecastListing = {
    sku: 'Item 1',
    current: InventoryForecastTableController.exampleCurrentInventoryData,
    projected: InventoryForecastTableController.exampleProjectedInventoryData,
    forecast: InventoryForecastTableController.exampleForecastedInventoryData,
  };

  public static readonly exampleConfig: InventoryForecastListingConfig = {
    page: 1,
    limit: 50,
    totalResults: 1,
  };

  static readonly exampleResponse: InventoryForecastListingData = {
    data: [InventoryForecastTableController.exampleData],
    metadata: InventoryForecastTableController.exampleConfig,
  };

  /**
   *
   * @returns InventoryForecastListingData
   * @throws IctError
   */
  @Example<InventoryForecastListingData>(
    InventoryForecastTableController.exampleResponse,
  )
  @SuccessResponse('200')
  @Post('/forecast/list')
  @OperationId('PostInventoryForecastList')
  @Tags('inventory')
  public async getInventoryForecastList(
    @Body()
    paginatedRequest: PaginatedRequestNoDates,
  ): Promise<InventoryForecastListingData> {
    const inventoryService = Container.get(InventoryService);

    let filters = paginatedRequest.filters;
    let sortFields = paginatedRequest.sortFields;
    let pageNum = paginatedRequest.page;
    let limitNum = paginatedRequest.limit;
    const searchString = paginatedRequest.searchString;
    // Default values
    if (pageNum === undefined) {
      pageNum = 0;
    }

    if (!limitNum) {
      limitNum = 50;
    }

    if (!sortFields) {
      sortFields = [];
    } else {
      sortFields = updateSortFields(sortFields);
    }

    if (filters) {
      filters = updateFilter(filters);
    }

    const inventoryForecastListingParams: InventoryForecastListingParams = {
      page: pageNum,
      limit: limitNum,
      sortField: sortFields,
      reqFilter: filters as BaseFilterType,
      searchString,
    };

    return await inventoryService.getInventoryForecastListingAsync(
      inventoryForecastListingParams,
    );
  }

  /**
   * Creates an excel export for inventory forecast data.
   *
   * @param paginatedRequest Request body with filters, sorting, and columns parameter.
   * @returns Excel buffer with forecast data.
   */
  @SuccessResponse('200')
  @Post('/forecast/list/export')
  @OperationId('PostInventoryForecastListExport')
  @Tags('inventory')
  @Produces('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
  public async postInventoryForecastListExport(
    @Body()
    paginatedRequest: PaginatedRequestNoDates & {
      columns: IncludedColumns;
    },
  ): Promise<Readable> {
    const inventoryService = Container.get(InventoryService);

    let filters = paginatedRequest.filters;
    if (filters) {
      filters = updateFilter(filters);
    }

    const inventoryForecastListingParams: InventoryForecastListingParams = {
      page: paginatedRequest.page ?? 0,
      limit: paginatedRequest.limit ?? 50,
      sortField: paginatedRequest.sortFields ?? [],
      reqFilter: filters as BaseFilterType,
      searchString: paginatedRequest.searchString,
    };

    const forecastListResponse =
      await inventoryService.getInventoryForecastListingAsync(
        inventoryForecastListingParams,
      );

    // Flatten the nested structure
    const flattenedData = forecastListResponse.data.map(item => {
      return {
        sku: item.sku,
        'current.reserveStorage': item.current.reserveStorage,
        'current.forwardPick': item.current.forwardPick,
        'projected.pendingReplenishment': item.projected.pendingReplenishment,
        'projected.pendingPicks': item.projected.pendingPicks,
        'projected.allocatedOrders': item.projected.allocatedOrders,
        'projected.projectedForwardPick': item.projected.projectedForwardPick,
        'forecast.averageReplenishment': item.forecast.averageReplenishment,
        'forecast.averageDemand': item.forecast.averageDemand,
        'forecast.demandTomorrow': item.forecast.demandTomorrow,
        'forecast.knownDemand': item.forecast.knownDemand,
        'forecast.forwardPickTomorrow': item.forecast.forwardPickTomorrow,
        'forecast.twoDayDemand': item.forecast.twoDayDemand,
        'forecast.twoDayForwardPick': item.forecast.twoDayForwardPick,
      };
    });

    const updatedResponse = ExportFormatter.mapResponseToColumns(
      paginatedRequest.columns,
      flattenedData,
    );

    const buffer: Buffer = ExportFormatter.generateExcelFile(updatedResponse);

    const today = new Date();
    const formattedDate = `${today.getMonth() + 1}-${today.getDate()}-${today.getFullYear()}`;
    const filename = `Forecast_Forward-Pick_${formattedDate}.xlsx`;

    // Set headers for Excel file download
    this.setHeader('Content-Disposition', `attachment; filename=${filename}`);
    this.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );

    // Create a readable stream from the buffer
    const stream = new Readable();
    stream.push(buffer);
    stream.push(null); // Signal the end of the stream
    return stream;
  }
}

function updateSortFields(sortFields: SortField[]): SortField[] {
  return sortFields.map(sortField => {
    const updatedSortField = {...sortField};
    const dotIndex = updatedSortField.columnName.indexOf('.');
    if (dotIndex !== -1) {
      const field = updatedSortField.columnName.substring(dotIndex + 1);
      updatedSortField.columnName = field;
    }
    return updatedSortField;
  });
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function updateFilter(filter: any): BaseFilterType {
  if (filter.type === 'single') {
    const singleFilter = filter;
    const dotIndex = singleFilter.name.indexOf('.');
    if (dotIndex !== -1) {
      singleFilter.name = singleFilter.name.substring(dotIndex + 1);
    }
    return singleFilter;
  }

  const treeFilter = filter;
  treeFilter.left = updateFilter(treeFilter.left);
  treeFilter.right = updateFilter(treeFilter.right);
  return treeFilter;
}
