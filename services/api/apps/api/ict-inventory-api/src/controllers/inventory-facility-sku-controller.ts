import {
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ProtectedRouteMiddleware,
  type BaseFilterType,
  type PaginatedRequestNoDates,
} from 'ict-api-foundations';
import {Readable} from 'stream';
import {
  Body,
  Controller,
  Example,
  Middlewares,
  OperationId,
  Post,
  Produces,
  Route,
  SuccessResponse,
  Tags,
} from 'tsoa';
import {ExportFormatter} from 'ict-api-foundations';
import {
  FacilityInventorySku,
  PostFacilityInventorySkusListResponse,
} from '../defs/inventory-sku-def.ts';
import {InventoryService} from '../services/inventory-service.ts';
import {IncludedColumns} from '../defs/inventory-export-def.ts';

@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [
      configPostgresDatabase(),
      {
        type: DatabaseTypes.BigQuery,
      },
    ],
  }),
])
export class InventoryFacilitySkuController extends Controller {
  public static readonly exampleData: FacilityInventorySku[] = [
    {
      sku: 'Dematic#1000000',
      description: 'Test item',
      daysOnHand: 1.3,
      status: 'under',
      averageDailyQuantity: 2.3,
      averageDailyOrders: 50.45,
      quantityAvailable: 5,
      quantityAllocated: 18,
      totalQuantity: 23,
      locations: 3,
      skuPositions: 1,
      latestActivityDateTimestamp: '2024-08-01T09:32:42Z',
    },
    {
      sku: 'Dematic#1000001',
      description: 'Test item 2',
      daysOnHand: 1.3,
      status: 'under',
      averageDailyQuantity: 2.3,
      averageDailyOrders: 50.45,
      quantityAvailable: 5,
      quantityAllocated: 18,
      totalQuantity: 23,
      locations: 1,
      skuPositions: 1,
      latestActivityDateTimestamp: '2024-08-01T09:32:42Z',
    },
    {
      sku: 'Dematic#1000002',
      description: 'Test item 3',
      daysOnHand: 1.3,
      status: 'under',
      averageDailyQuantity: 2.3,
      averageDailyOrders: 50.45,
      quantityAvailable: 5,
      quantityAllocated: 18,
      totalQuantity: 23,
      locations: 2,
      skuPositions: 1,
      latestActivityDateTimestamp: '2024-08-01T09:32:42Z',
    },
  ];
  public static readonly exampleConfig = {
    limit: 10,
    page: 1,
    totalResults: 3,
  };
  public static readonly exampleResponse: PostFacilityInventorySkusListResponse =
    {
      data: InventoryFacilitySkuController.exampleData,
      metadata: InventoryFacilitySkuController.exampleConfig,
    };

  /**
   *
   * @returns {Promise<PostFacilityInventorySkusListResponse>} contract
   * @throws IctError
   */
  @Example<PostFacilityInventorySkusListResponse>(
    InventoryFacilitySkuController.exampleResponse,
  )
  @SuccessResponse('200')
  @Post('/facility/skus/list')
  @OperationId('PostInventoryFacilitySkusList')
  @Tags('inventory')
  public async getInventoryFacilitySkus(
    @Body()
    paginatedRequest: PaginatedRequestNoDates,
  ): Promise<PostFacilityInventorySkusListResponse> {
    const inventoryService = Container.get(InventoryService);
    const {filters, sortFields, page, limit, searchString} = paginatedRequest;

    const skuListContract =
      await inventoryService.getInventoryFacilitySkuListAsync({
        reqFilter: filters as BaseFilterType,
        sortFields,
        page,
        limit,
        searchString,
      });

    return skuListContract;
  }

  /**
   *
   * @returns {Promise<Readable>} Excel file as a readable stream
   * @throws IctError
   */

  @SuccessResponse('200')
  @Post('/facility/skus/list/export')
  @OperationId('PostInventoryFacilitySkusListExport')
  @Tags('inventory')
  @Produces('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
  public async getInventoryFacilitySkusExcelFile(
    @Body()
    paginatedRequest: PaginatedRequestNoDates & {
      columns: IncludedColumns;
    },
  ): Promise<Readable> {
    const inventoryService = Container.get(InventoryService);

    const exportQuery = {
      reqFilter: paginatedRequest.filters as BaseFilterType,
      sortFields: paginatedRequest.sortFields || [],
      page: paginatedRequest.page,
      limit: paginatedRequest.limit,
      searchString: paginatedRequest.searchString,
    };

    const skuListContract =
      await inventoryService.getInventoryFacilitySkuListAsync(exportQuery);

    const updatedReponse = ExportFormatter.mapResponseToColumns(
      paginatedRequest.columns,
      skuListContract.data,
    );

    const buffer: Buffer = ExportFormatter.generateExcelFile(updatedReponse);

    const today = new Date();
    const formattedDate = `${today.getMonth() + 1}-${today.getDate()}-${today.getFullYear()}`;
    const filename = `SKUs_Forward-Pick_${formattedDate}.xlsx`;

    // Set headers for Excel file download
    this.setHeader('Content-Disposition', `attachment; filename=${filename}`);
    this.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );

    // Create a readable stream from the buffer
    const stream = new Readable();
    stream.push(buffer);
    stream.push(null); // Signal the end of the stream
    return stream;
  }
}
