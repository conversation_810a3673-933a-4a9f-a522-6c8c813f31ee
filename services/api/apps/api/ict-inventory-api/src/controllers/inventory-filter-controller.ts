import {
  CacheMiddleware,
  Container,
  IctError,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {InventoryService} from '../services/inventory-service.ts';
import {InventoryAreaFilterDefinition} from '../defs/inventory-area-filter-def.ts';

@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class InventoryFilterController extends Controller {
  public static readonly exampleData: InventoryAreaFilterDefinition = {
    areaFilterTable: [],
  };
  public static readonly exampleResponse: InventoryAreaFilterDefinition =
    InventoryFilterController.exampleData;

  /**
   * @returns {Promise<InventoryAreaFilterDefinition>} contract
   * @throws IctError
   */
  @Example<InventoryAreaFilterDefinition>(
    InventoryFilterController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/filter')
  @OperationId('GetInventoryFilter')
  @Tags('inventory')
  public async getInventoryFilter(): Promise<InventoryAreaFilterDefinition> {
    // get the inventory service
    const inventoryService = Container.get(InventoryService);

    const areaFilterData = await inventoryService.getInventoryAreaAsync();
    if (areaFilterData) {
      return {areaFilterTable: areaFilterData.areaFilterTable};
    }
    throw IctError.internalServerError(
      'Unable to retrieve inventory area filter data.'
    );
  }
}
