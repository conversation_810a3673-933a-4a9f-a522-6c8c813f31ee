import {
  CacheMiddleware,
  Container,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {InventoryService} from '../services/inventory-service.ts';
import {
  AdvicesList,
  AdvicesListData,
} from '../defs/inventory-advices-list-def.ts';

@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class InventoryAdvicesListController extends Controller {
  public static readonly exampleData: AdvicesListData[] = [];

  public static readonly exampleResponse: AdvicesList = {
    adviceList: InventoryAdvicesListController.exampleData,
  };

  /**
   * @returns {Promise<AdvicesList>} contract
   */
  @Example<AdvicesList>(InventoryAdvicesListController.exampleResponse)
  @SuccessResponse('200')
  @Get('/advices/list')
  @OperationId('GetInventoryAdvicesList')
  @Tags('inventory')
  public async getInventoryAdvicesList(): Promise<AdvicesList> {
    // get the inventory service
    const inventoryService = Container.get(InventoryService);

    return {adviceList: await inventoryService.getAdvicesList()};
  }
}
