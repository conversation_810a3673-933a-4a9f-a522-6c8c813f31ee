import {
  CacheMiddleware,
  Container,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Get,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
} from 'tsoa';
import {InventoryService} from '../services/inventory-service.ts';
import {InventoryAdvicesInProgressData} from '../defs/inventory-advices-in-progress-def.ts';

@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(),
  CacheMiddleware.use({minutes: CacheMiddleware.DEFAULT_CACHE_MINUTES}),
])
export class InventoryAdvicesInProgressController extends Controller {
  public static readonly exampleData: InventoryAdvicesInProgressData = {
    inProgressAdvices: 2,
  };

  public static readonly exampleResponse: InventoryAdvicesInProgressData =
    InventoryAdvicesInProgressController.exampleData;

  /**
   * @returns {Promise<InventoryAdvicesInProgressData>} contract
   */
  @Example<InventoryAdvicesInProgressData>(
    InventoryAdvicesInProgressController.exampleResponse
  )
  @SuccessResponse('200')
  @Get('/advices/in-progress')
  @OperationId('GetInventoryAdvicesInProgress')
  @Tags('inventory')
  public async getInventoryAdvicesInProgress(): Promise<InventoryAdvicesInProgressData> {
    // get the inventory service
    const inventoryService = Container.get(InventoryService);

    return await inventoryService.getInventoryAdvicesInProgress();
  }
}
