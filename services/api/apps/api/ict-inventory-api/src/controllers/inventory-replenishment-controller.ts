import {
  Container,
  ProtectedRouteMiddleware,
  configPostgresDatabase,
  DatabaseTypes,
  startEndDateValidation,
  ChartSeriesData,
} from 'ict-api-foundations';
import {
  Controller,
  Example,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Query,
  Get,
} from 'tsoa';
// eslint-disable-next-line import/no-cycle
import {InventoryService} from '../services/inventory-service.ts';
import {
  InventoryReplenishmentDetails,
  ShiftData,
} from '../defs/inventory-replenishment-details-def.ts';

@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [
      configPostgresDatabase(),
      {
        type: DatabaseTypes.BigQuery,
      },
    ],
  }),
])
export class InventoryReplenishmentDetailsController extends Controller {
  public static readonly dailyReplenishmentsExampleData: ChartSeriesData[] = [
    {name: '2025-01-01', value: 3},
    {name: '2025-01-02', value: 9},
    {name: '2025-01-03', value: 14},
    {name: '2025-01-04', value: 33},
    {name: '2025-01-05', value: 12},
    {name: '2025-01-06', value: 40},
    {name: '2025-01-07', value: 15},
  ];

  public static readonly dailyPendingOrdersExampleData: ChartSeriesData[] = [
    {name: '2025-01-01', value: 25},
    {name: '2025-01-02', value: 19},
    {name: '2025-01-03', value: 23},
    {name: '2025-01-04', value: 10},
    {name: '2025-01-05', value: 8},
    {name: '2025-01-06', value: 32},
    {name: '2025-01-07', value: 20},
  ];

  public static readonly dailyCycleTimesExampleData: ChartSeriesData[] = [
    {name: '2025-01-01', value: 14},
    {name: '2025-01-02', value: 22},
    {name: '2025-01-03', value: 34},
    {name: '2025-01-04', value: 13},
    {name: '2025-01-05', value: 9},
    {name: '2025-01-06', value: 4},
    {name: '2025-01-07', value: 19},
  ];

  public static readonly exampleShiftOneData: ChartSeriesData[] = [
    {name: '2025-01-01', value: 12},
    {name: '2025-01-02', value: 42},
    {name: '2025-01-03', value: 66},
    {name: '2025-01-04', value: 42},
    {name: '2025-01-05', value: 37},
    {name: '2025-01-06', value: 22},
    {name: '2025-01-07', value: 18},
  ];

  public static readonly exampleShiftTwoData: ChartSeriesData[] = [
    {name: '2025-01-01', value: 2},
    {name: '2025-01-02', value: 23},
    {name: '2025-01-03', value: 12},
    {name: '2025-01-04', value: 9},
    {name: '2025-01-05', value: 12},
    {name: '2025-01-06', value: 34},
    {name: '2025-01-07', value: 22},
  ];
  public static readonly exampleShiftThirdData: ChartSeriesData[] = [
    {name: '2025-01-01', value: 5},
    {name: '2025-01-02', value: 13},
    {name: '2025-01-03', value: 23},
    {name: '2025-01-04', value: 6},
    {name: '2025-01-05', value: 11},
    {name: '2025-01-06', value: 4},
    {name: '2025-01-07', value: 33},
  ];

  // Simple seeded random number generator
  private static seededRandom(initialSeed: number): () => number {
    let seed = initialSeed;
    return function seededRandomFunction() {
      const x = Math.sin((seed += 1)) * 10000;
      return x - Math.floor(x);
    };
  }

  private static generateDataForDateRange(
    seed: number,
    startDate: Date,
    endDate: Date,
  ): ChartSeriesData[] {
    const data: ChartSeriesData[] = [];
    const oneDayInMillis = 24 * 60 * 60 * 1000; // Milliseconds in one day
    const random = InventoryReplenishmentDetailsController.seededRandom(seed);

    for (
      let date = new Date(startDate);
      date < endDate;
      date = new Date(date.getTime() + oneDayInMillis)
    ) {
      data.push({
        name: date.toISOString().split('T')[0],
        value: Math.floor(random() * 50) + 1, // Random value between 1 and 50
      });
    }

    return data;
  }

  public static readonly exampleShiftData: ShiftData = {
    firstShift: InventoryReplenishmentDetailsController.exampleShiftOneData,
    secondShift: InventoryReplenishmentDetailsController.exampleShiftTwoData,
    thirdShift: InventoryReplenishmentDetailsController.exampleShiftThirdData,
  };

  public static readonly exampleResponse: InventoryReplenishmentDetails = {
    // averageDailyReplenishments: 17,
    // averagePendingOrders: 26,
    // averageCycleTimes: 13,
    dailyReplenishments:
      InventoryReplenishmentDetailsController.dailyReplenishmentsExampleData,
    dailyPendingOrders:
      InventoryReplenishmentDetailsController.dailyPendingOrdersExampleData,
    dailyCycleTimes:
      InventoryReplenishmentDetailsController.dailyCycleTimesExampleData,
    shiftData: [InventoryReplenishmentDetailsController.exampleShiftData],
    shiftTimes: {
      first_endTime: '15:29',
      second_endTime: '23:59',
      third_endTime: '06:59',
      first_startTime: '07:00',
      second_startTime: '15:30',
      third_startTime: '00:00',
    },
  };

  /**
   * @param {Date} start_date
   * @param {Date} end_date
   * @returns {Promise<InventoryReplenishmentDetails>} contract
   * @throws IctError
   */
  @Example<InventoryReplenishmentDetails>(
    InventoryReplenishmentDetailsController.exampleResponse,
  )
  @SuccessResponse('200')
  @Get('/replenishment/details')
  @OperationId('GetInventoryReplenishmentDetails')
  @Tags('inventory')
  public async getInventoryReplenishmentDetails(
    @Query('start_date') startDate: Date,
    @Query('end_date') endDate: Date,
  ): Promise<InventoryReplenishmentDetails> {
    // Validate the start and end dates
    startEndDateValidation(startDate, endDate);
    const inventoryService = Container.get(InventoryService);

    return await inventoryService.getInventoryReplenishmentDetails(
      startDate,
      endDate,
    );
  }
}
