import {
  Controller,
  Example,
  Get,
  Middlewares,
  Route,
  SuccessResponse,
  OperationId,
  Tags,
  Path,
} from 'tsoa';
import {
  configPostgresDatabase,
  Container,
  DatabaseTypes,
  ProtectedRouteMiddleware,
} from 'ict-api-foundations';
import {InventoryService} from '../services/inventory-service.ts';
import {
  InventoryContainerEventsKpiContract,
  InventoryContainerEventsKpiData,
} from '../defs/inventory-container-events-kpi-def.ts';

@Route('inventory')
@Middlewares([
  ProtectedRouteMiddleware.apiProtectedDataRoute(undefined, {
    databases: [{type: DatabaseTypes.BigQuery}, configPostgresDatabase()],
  }),
])
export class InventoryContainerEventsKpiController extends Controller {
  public static readonly exampleData: InventoryContainerEventsKpiData = {
    containerId: 'AN0884',
    locationId: 'LOC987',
    zone: 'Zone A',
    sku: 'TRL_4536',
    quantity: 150,
    lastActivityDate: '2024-11-03 09:15:10 AM',
    lastCycleCount: '2024-10-30 11:45:20 AM',
    dataUpdated: '2024-11-05 08:00:00 AM',
    cycleCountEventsToday: 3,
    averageDailyCycleCount: 15,
    pickEventsToday: 18,
    averageDailyPickEvents: 29,
  };

  public static readonly exampleResponse: InventoryContainerEventsKpiContract =
    {
      events: [InventoryContainerEventsKpiController.exampleData],
    };

  /**
   * Get container KPI metrics for a specific container ID.
   *
   * @param {string} containerId - ID of the container for which KPI metrics are retrieved.
   * @returns {Promise<InventoryContainerEventsKpiContract>} KPI metrics for the container.
   * @throws IctError
   */
  @Example<InventoryContainerEventsKpiContract>(
    InventoryContainerEventsKpiController.exampleResponse,
  )
  @SuccessResponse('200')
  @Get('/container-events/{containerId}')
  @OperationId('GetInventoryContainerEventsDetails')
  @Tags('inventory')
  public async getInventoryContainerEventsDetails(
    @Path() containerId: string,
  ): Promise<InventoryContainerEventsKpiContract> {
    const inventoryService = Container.get(InventoryService);

    // Fetch data for the specified container ID
    return await inventoryService.getInventoryContainerEventsKpiAsync(
      containerId,
    );
  }

  /**
   * Get container KPI metrics for a specific container ID using WMS tables.
   *
   * @param {string} containerId - ID of the container for which KPI metrics are retrieved.
   * @returns {Promise<InventoryContainerEventsKpiContract>} KPI metrics for the container.
   * @throws IctError
   */
  @Example<InventoryContainerEventsKpiContract>(
    InventoryContainerEventsKpiController.exampleResponse,
  )
  @SuccessResponse('200')
  @Get('/wms/container-events/{containerId}')
  @OperationId('GetInventoryWMSContainerEventsDetails')
  @Tags('inventory')
  public async getInventoryWMSContainerEventsDetails(
    @Path() containerId: string,
  ): Promise<InventoryContainerEventsKpiContract> {
    const inventoryService = Container.get(InventoryService);

    // Fetch data for the specified container ID using WMS tables
    return await inventoryService.getInventoryWMSContainerEventsKpiAsync(
      containerId,
    );
  }
}
