/* eslint-disable @typescript-eslint/no-explicit-any */
import {PubSub} from '@google-cloud/pubsub';
import {Container, IctError, WinstonLogger} from 'ict-api-foundations';
import {DateTime} from 'luxon';
import * as XLSX from 'xlsx';

const edpTopicId = process.env.EDP_PUBSUB_TOPIC_ID ?? 'adi-data';
const edpBigQueryDb = process.env.EDP_BIGQUERY_PROJECT_ID;
const pubsub = new PubSub({projectId: edpBigQueryDb});
const tenant = 'superioruniform';
const facility = 'superioruniform_eudoraar';
const sourceSystem = 'superioruniform_eudoraar_diq';
const logger = Container.get(WinstonLogger);

export class DataFlowManager {
  /**
   * Does the parsing work and handles some data validation/cleanup.
   * @returns The cleaned up JSONs
   * @throws IctError
   */
  static parseXlsToJson(buffer: Buffer): any[] {
    const workbook = XLSX.read(buffer, {type: 'buffer'});
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const jsonData: any[][] = XLSX.utils.sheet_to_json(worksheet, {
      header: 1,
      raw: false,
    });

    // Find the row with the most non-empty cells
    let headerRowIndex = -1;
    let maxNonEmptyCells = 0;

    for (let i = 0; i < jsonData.length; i++) {
      const row = jsonData[i];
      const nonEmptyCells = row.filter(
        cell => cell !== undefined && cell !== null && cell !== ''
      ).length;

      if (nonEmptyCells > maxNonEmptyCells) {
        maxNonEmptyCells = nonEmptyCells;
        headerRowIndex = i;
      }
    }

    if (headerRowIndex === -1) {
      throw IctError.unprocessableRequest(
        'Confirm both files have a header row.'
      );
    }

    // Convert headers to lowercase
    const headers = jsonData[headerRowIndex].map((header: string) =>
      header.trim().toLowerCase()
    );
    const rows = jsonData.slice(headerRowIndex + 1);

    return rows.map((row: any[]) => {
      const obj: any = {};
      headers.forEach((header: string, index: number) => {
        let cellValue = row[index];
        if (typeof cellValue === 'string') {
          cellValue = cellValue.trim();
        }
        // Remove leading zeros from numeric strings
        if (/^0+\d+$/.test(cellValue)) {
          cellValue = cellValue.replace(/^0+/, '');
        }
        if (
          header.toLowerCase().includes('date') ||
          header.toLowerCase().includes('dt')
        ) {
          if (typeof cellValue === 'number') {
            // Convert Excel date to valid formatted date
            const date = new Date((cellValue - 25569) * 86400 * 1000);
            cellValue = date.toISOString();
          } else if (typeof cellValue === 'string') {
            // Check if the string matches the mm/dd/yy format
            const datePattern = /\d{1,2}\/\d{1,2}\/\d{2}$/;
            if (datePattern.test(cellValue)) {
              const [month, day, year] = cellValue.split('/');
              const formattedDate = new Date(
                `20${year}-${month}-${day}`
              ).toISOString();
              cellValue = formattedDate;
            }
          }
        }
        // Coerce specific numeric fields to integers
        if (
          header.toLowerCase().includes('quantity') ||
          header.toLowerCase().includes('qty')
        ) {
          cellValue = Math.round(cellValue);
        }
        obj[header] = cellValue;
      });
      return obj;
    });
  }

  /**
   * Gets the date from the 'Manager' file to use in both facts.
   * @returns The date provided
   */
  static extractDateFromManager(buffer: Buffer): string {
    const workbook = XLSX.read(buffer, {type: 'buffer'});
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];

    // Find the cell containing "Date & Time"
    const cellAddress = Object.keys(worksheet).find(cell => {
      const cellValue = worksheet[cell]?.v;
      return typeof cellValue === 'string' && cellValue.includes('Date & Time');
    });

    if (!cellAddress) {
      throw IctError.unprocessableRequest(
        "The order manager file is missing a 'Date & Time' value."
      );
    }

    const match = cellAddress.match(/([A-Z]+)(\d+)/);
    if (!match) {
      throw IctError.unprocessableRequest(
        `Could not parse cell address: ${cellAddress}.`
      );
    }

    const col = match[1];
    const row = parseInt(match[2], 10);
    const targetCol = String.fromCharCode(col.charCodeAt(0) + 2);
    const targetCell = `${targetCol}${row}`;
    const dateCell = worksheet[targetCell];

    if (
      !dateCell ||
      !(typeof dateCell.v === 'string' || typeof dateCell.v === 'number')
    ) {
      throw IctError.unprocessableRequest(
        'Unable to determine Date & Time value.'
      );
    }

    // If dateValue is a number, convert it to a JavaScript Date object
    if (typeof dateCell.v === 'number') {
      const excelDate = new Date((dateCell.v - (25567 + 2)) * 86400 * 1000);
      const estDate = DateTime.fromISO(excelDate.toISOString().split('.')[0]); // Excel date formatting is obnoxious
      // The file upload time is EST but should be saved in the DB using CST, so minus 1 hour.
      return estDate.minus({hours: 1}).toFormat("yyyy-MM-dd'T'HH:mm:ss");
    }
    return '';
  }

  /**
   * Helper function to split JSON data into chunks of 200 kB or less
   * @returns Sections of JSON
   */
  static splitJsonData(jsonData: any[], maxSize: number): any[][] {
    const chunks = [];
    let currentChunk: any[] = [];
    let currentSize = 0;

    jsonData.forEach(item => {
      const itemString = JSON.stringify(item);
      const itemSize = Buffer.byteLength(itemString, 'utf8');

      if (currentSize + itemSize > maxSize) {
        chunks.push(currentChunk);
        currentChunk = [];
        currentSize = 0;
      }

      currentChunk.push(item);
      currentSize += itemSize;
    });

    if (currentChunk.length > 0) {
      chunks.push(currentChunk);
    }

    return chunks;
  }

  /**
   * Sends the json and logs it
   */
  static async sendToPubSub(
    message: any,
    recTimeNum: number,
    factType: string
  ): Promise<void> {
    const topicName = `projects/${edpBigQueryDb}/topics/${edpTopicId}`;
    const topic = pubsub.topic(topicName);
    logger.info(`Preparing to send messages to ${topicName}.`);

    // Serialize the message to a JSON string
    const serializedMessage = JSON.stringify(message);
    const recTime: string = recTimeNum.toString();

    const attributes = {
      type: factType,
      sourcesystem: sourceSystem,
      facility,
      tenant,
      recTime,
    };

    // Publish the message
    const dataBuffer = Buffer.from(serializedMessage);
    // Array to store message IDs
    const messageIds: string[] = [];
    try {
      const messageId = await topic.publishMessage({
        data: dataBuffer,
        attributes,
      });
      messageIds.push(messageId); // Store the message ID
    } catch (error) {
      IctError.unprocessableRequest(`Error publishing message: ${error}`);
    }
    // Log the total number of successfully sent messages
    logger.info(`Successfully sent ${messageIds.length} messages.`);
  }
}
