import {
  AdviceDetailsData,
  AdvicesDetailsQueryResponse,
} from '../defs/inventory-advices-details-drawer-def';

export const formatAdviceDetails = (
  adviceDetails: AdvicesDetailsQueryResponse[]
): AdviceDetailsData => {
  return {
    supplierId: adviceDetails[0].supplier_id,
    itemsReceived: adviceDetails.map(item => ({
      handlingUnit: item.handling_unit_id,
      handlingUnitType: item.handling_unit_type,
      adviceLine: item.advice_line,
      sku: item.sku,
      quantity: item.quantity,
      packagingLevel: item.packaging_level,
    })),
  };
};
