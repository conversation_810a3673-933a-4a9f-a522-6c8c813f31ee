import {
  PaginatedResults,
  DatabaseProvider,
  IctError,
  DiService,
  ContextService,
  ChartSeriesPercentageData,
  PercentValidations,
  ConfigStore,
  WMSCustomerOrderState,
  WMSCustomerOrderStatus,
  WMSCustomerOrderDetailStatus,
  SortingSelectQueryParams,
  BigQueryGenerator,
  addStartEndDateParamValues,
  SQLQueryGenerator,
} from 'ict-api-foundations';
import {DateTime} from 'luxon';
import {BigQueryDate, Query} from '@google-cloud/bigquery';
import {DefaultConfigSettings} from 'ict-api-schema';
import {
  FacilityInventorySku,
  WmsInventorySku,
  InventorySkuQueryStoreParams,
} from '../defs/inventory-sku-def.ts';
import {InventoryAccuracy} from '../defs/inventory-accuracy-def.ts';
import {InventoryAreaFilterDefinition} from '../defs/inventory-area-filter-def.ts';
import {
  InventoryStorageUtilization,
  StorageUtilizationAisleFilterList,
  StorageUtilizationAreaFilterList,
} from '../defs/inventory-storage-utilization-def.ts';
import {
  InventoryHighImpactSKU,
  InventoryHighImpactSKUListStoreParams,
} from '../defs/inventory-high-impact-sku-def.ts';
import {
  InventoryContainerEventsData,
  InventoryContainerEventsQueryStoreParams,
} from '../defs/inventory-container-events-list-def.ts';
import {InventoryPerformanceSeriesQueryResponse} from '../defs/inventory-performance-series-def.ts';
import {InventoryAdvicesInProgressQueryResponse} from '../defs/inventory-advices-in-progress-def.ts';
import {AdvicesOutstandingQueryResponse} from '../defs/inventory-advices-outstanding-def.ts';
import {AdvicesFinishedQueryResponse} from '../defs/inventory-finished-advices-def.ts';
import {AdvicesDetailsQueryResponse} from '../defs/inventory-advices-details-drawer-def.ts';
import {AdvicesCycleTimeQueryResponse} from '../defs/inventory-advices-cycle-time-def.ts';
import {AdvicesListQueryResponse} from '../defs/inventory-advices-list-def.ts';
import {getInventoryAccuracyStatus} from '../utils/status-formatters.ts';
import {WorkAreaCodeFilterList} from '../defs/inventory-work-area-codes-filter-list-def.ts';
import {
  InventoryForecastListingQueryResponse,
  InventoryForecastQueryParams,
  InventoryForecastSkuDetailsQueryResponse,
  InventoryForecastTimestampResponse,
  InventoryForecastZoneMapping,
  InventorySkuLocationResponse,
  InventorySkuOrdersResponse,
} from '../defs/inventory-forecast-def.ts';
import {InventoryContainerEventsKpiResponse} from '../defs/inventory-container-events-kpi-def.ts';
import {
  InventoryContainerData,
  InventoryContainerQueryStoreParams,
} from '../defs/inventory-container-def.ts';
import {
  InventoryReplenishmentQueryResponse,
  TaskTypeData,
} from '../defs/inventory-replenishment-details-def.ts';
import {InventoryUploadRecentActivityQueryResponse} from '../defs/inventory-upload-def.ts';
import {InventoryBinLocationQueryResponse} from '../defs/inventory-bin-locations-def.ts';
import {DistributionType} from '../defs/inventory-distribution-status-def.ts';

@DiService()
export class InventoryStore {
  constructor(
    private context: ContextService,
    private configStore: ConfigStore,
  ) {}

  get dbProvider(): DatabaseProvider {
    return this.context.dbProvider;
  }

  /**
   * Gets the average cycle time of finished advices over the last 24 hours.
   * @returns average advice cycle time
   */
  async getAdvicesCycleTime(): Promise<AdvicesCycleTimeQueryResponse> {
    const bigQueryDb = this.dbProvider.bigQuery;
    const dimReceivingAdviceSummaryTable = bigQueryDb.getFullTablePath(
      'dim_receiving_advice',
    );

    const sql = `
      SELECT
        IFNULL(AVG(TIMESTAMP_DIFF(finish_time, start_time, MINUTE)), 0) AS cycle_time
      FROM \`${dimReceivingAdviceSummaryTable}\`
      -- Advice has been finished in the last 24 hours
      WHERE finish_time > TIMESTAMP_SUB(CURRENT_TIMESTAMP, INTERVAL 24 HOUR)
    `;

    const sqlOptions: Query = {
      query: sql,
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length || !rows[0].cycle_time) {
      throw IctError.noContent();
    }

    return rows[0];
  }

  /**
   * Gets the count of advices finished in the last 24 hours
   * @returns count of advices finished in the last 24 hours
   */
  async getAdvicesFinished(): Promise<AdvicesFinishedQueryResponse> {
    const bigQueryDb = this.dbProvider.bigQuery;
    const dimReceivingAdviceSummaryTable = bigQueryDb.getFullTablePath(
      'dim_receiving_advice',
    );

    const sql = `
      SELECT COUNT(*) AS finished_advices
      FROM \`${dimReceivingAdviceSummaryTable}\`
      WHERE finish_time > TIMESTAMP_SUB(CURRENT_TIMESTAMP, INTERVAL 24 HOUR)
    `;

    const sqlOptions: Query = {
      query: sql,
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length || !rows[0].finished_advices) {
      throw IctError.noContent();
    }

    return rows[0];
  }

  /**
   * Gets advices and their line counts that were created in the last 30 days
   * @returns list of advices and their line counts
   */
  async getAdvicesList(): Promise<AdvicesListQueryResponse[]> {
    const bigQueryDb = this.dbProvider.bigQuery;
    const dimReceivingAdviceTable = this.dbProvider.bigQuery.getFullTablePath(
      'dim_receiving_advice',
    );
    const fctReceivingAdviceSummaryTable =
      this.dbProvider.bigQuery.getFullTablePath('fct_receiving_advice_summary');

    const sql = `
      SELECT
          dra.receiving_advice_code AS advice_id,
          MAX(IF(dra.finish_time IS NOT NULL, 'Finished', IF(dra.start_time IS NOT NULL, 'In progress', 'Created'))) AS advice_status,
          MAX(dra.receiving_advice_owner) AS owner_id,
          MAX(dra.receiving_advice_type) AS advice_type,
          MAX(dra.supplier) AS supplier_id,
          MAX(dra.create_time) AS created_time,
          MAX(dra.start_time) AS start_time,
          MAX(dra.finish_time) AS finished_time,
          MAX(fras.receiving_advice_completed_line_count) AS advice_lines,
          MAX(fras.receiving_advice_exactly_delivered_line_count) AS delivered_lines,
          MAX(fras.receiving_advice_over_delivered_line_count) AS overdelivered_lines,
          MAX(fras.receiving_advice_under_delivered_line_count) AS underdelivered_lines
      FROM \`${dimReceivingAdviceTable}\` dra
      JOIN \`${fctReceivingAdviceSummaryTable}\` fras
          ON dra.receiving_advice_code = fras.receiving_advice_code
      -- Advice has been created in the last 30 days
      WHERE dra.create_time > TIMESTAMP_SUB(CURRENT_TIMESTAMP, INTERVAL 30 DAY)
      GROUP BY dra.receiving_advice_code;
    `;

    const sqlOptions: Query = {
      query: sql,
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length) {
      throw IctError.noContent();
    }

    return rows;
  }

  /**
   * Gets the latest details for a specific advice
   * @param adviceId The advice ID to get details for
   * @returns details for a specific advice
   */
  async getAdviceDetails(
    adviceId: string,
  ): Promise<AdvicesDetailsQueryResponse[]> {
    const bigQueryDb = this.dbProvider.bigQuery;
    const dimReceivingAdviceSummaryTable = bigQueryDb.getFullTablePath(
      'dim_receiving_advice',
    );
    const fctReceivingAdviceLineTable = bigQueryDb.getFullTablePath(
      'fct_receiving_advice_line',
    );
    const dimItemTable = bigQueryDb.getFullTablePath('dim_item');
    const dimContainerTypeTable =
      bigQueryDb.getFullTablePath('dim_container_type');

    const sql = `
    SELECT
      ra.receiving_advice_code AS advice_line,
      ra.supplier AS supplier_id,
      ral.handling_unit_code AS handling_unit_id,
      dct.container_type_code AS handling_unit_type,
      ral.receiving_advice_qty AS quantity,
      ral.packaging_level_code AS packaging_level,
      COALESCE(di.item_sku, ral.sku_id) AS sku
    FROM
      \`${dimReceivingAdviceSummaryTable}\` AS ra
    JOIN
      \`${fctReceivingAdviceLineTable}\` AS ral
    ON
      ra.receiving_advice_uuid = ral.receiving_advice_uuid
    JOIN
      \`${dimContainerTypeTable}\` AS dct
    ON
      ral.container_type_uuid = dct.container_type_uuid
    JOIN
      \`${dimItemTable}\` AS di
    ON
      ral.item_uuid = di.item_uuid
    WHERE
      receiving_advice_code = @adviceId
    GROUP BY
      supplier_id,
      handling_unit_id,
      handling_unit_type,
      quantity,
      packaging_level,
      ra.active_rec_ind,
      ral.receiving_advice_line_code,
      ra.finish_time,
      sku,
      advice_line,
      create_time,
      CASE
        WHEN ra.active_rec_ind = 1 THEN 'In Progress'
        WHEN ra.active_rec_ind = 0
      AND ra.finish_time IS NULL THEN 'Created'
      ELSE
      'Finished'
    END
    ORDER BY
  create_time DESC;`;

    const sqlOptions: Query = {
      query: sql,
      params: {
        adviceId,
      },
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);
    if (!rows.length) {
      throw IctError.noContent();
    }
    return rows;
  }

  /**
   * Retrieve the total number of inventory sku for a specified date range.
   * @param startDate Start date for the date range.
   * @param endDate End date for the date range.
   * @returns Array of Inventory objects for the specified date range
   */
  async getInventoryWmsSkuListAsync(
    params: InventorySkuQueryStoreParams,
  ): Promise<PaginatedResults<WmsInventorySku[]>> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const workAreaCodesToFilterBy: string[] =
      await this.getWorkAreaCodesToFilterBy();

    // build the filter.  If none are found, don't add the filter at all
    const workAreaFilter =
      workAreaCodesToFilterBy.length > 0
        ? `zone IN (${workAreaCodesToFilterBy.map(wa => `'${wa}'`).toString()})`
        : '';

    const goldWmsInventory =
      bigQueryDb.getEdpFullTablePath('gold_wms_inventory');
    const inventoryHistoryTable = bigQueryDb.getEdpFullTablePath(
      'platinum_inventory_history_by_zone',
    );
    const goldWmsCustomerOrder = bigQueryDb.getEdpFullTablePath(
      'gold_wms_customer_order',
    );
    const goldWmsCustomerOrderDetail = bigQueryDb.getEdpFullTablePath(
      'gold_wms_customer_order_detail',
    );
    const goldPick = bigQueryDb.getEdpFullTablePath('gold_pick'); // replaces fct_order_line

    const cte = `
        WITH latest_item_order_averages AS (
          select etl_create_timestamp_utc, 
              sku,
              MAX(avg_picked_qty) as avg_picked_qty,
              MAX(avg_picked_orders) as avg_picked_orders
            FROM ${inventoryHistoryTable}
            WHERE 
              etl_create_timestamp_utc = (SELECT MAX(etl_create_timestamp_utc) FROM ${inventoryHistoryTable} )
              ${workAreaFilter ? `AND ${workAreaFilter}` : ''}
            GROUP BY 
              etl_create_timestamp_utc,
              sku
        ),
        recent_wms_inventory AS (
          SELECT *
          FROM ${goldWmsInventory}
          WHERE query_timestamp_utc > TIMESTAMP_SUB(CURRENT_TIMESTAMP, INTERVAL 2 DAY)
        ),
        latest_inventory_snapshot_timestamp AS (
          SELECT MAX(query_timestamp_utc) as latestSnapshot
          FROM recent_wms_inventory
        ),
        distinct_max_containers AS (
          SELECT 
            sku,
            MAX(max_cont_in_dms) as maxContainers
          FROM (
            SELECT DISTINCT sku, max_cont_in_dms
            FROM recent_wms_inventory
            WHERE query_timestamp_utc = (SELECT latestSnapshot FROM latest_inventory_snapshot_timestamp)
              ${workAreaFilter ? `AND ${workAreaFilter}` : ''}
              AND max_cont_in_dms IS NOT NULL
          )
          GROUP BY sku
        ),
        latest_inventory_quantities_by_location AS (
              SELECT
                fct.sku,
                fct.query_timestamp_utc,
                fct.location_id,
                SUM(fct.quantity) as inventory_qty,
                COUNT(*) as skuPositions,
                MAX(fct.last_cc_inspection_date_utc) as latestCycleCountTimestamp,
                MAX(fct.last_activity_date_utc) as latestActivityDateTimestamp
              FROM recent_wms_inventory fct
              WHERE query_timestamp_utc = (SELECT latestSnapshot FROM latest_inventory_snapshot_timestamp)
                ${workAreaFilter ? `AND ${workAreaFilter}` : ''}
              GROUP BY 
                sku, 
                query_timestamp_utc,
                location_id
        ),
        -- this cte is used to count all the picks for each sku since the last inventory snapshot
        recent_picks AS (
          SELECT item_sku as sku, 
            SUM(picked_qty) as total_qty_picked
          FROM ${goldPick} 
          WHERE event_timestamp_utc >= (SELECT latestSnapshot FROM latest_inventory_snapshot_timestamp)
            AND event_timestamp_utc > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 2 DAY) -- this is just here to satisfy the partition filter req. 
            AND UPPER(work_type_code) = 'PICK'
          GROUP BY sku
        ),
        -- this retrieves a subset of wms customer orders from the last 24 hours so the next couple of queries only execute against the subset
        recent_orders AS (
          SELECT order_code,
            state_code,
            sts_code
          FROM \`${goldWmsCustomerOrder}\`
          WHERE edit_date_timestamp_utc BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR) AND CURRENT_TIMESTAMP()
            AND short_flag != '1'
        ),
        -- finds the orders that are currently open and in an allocated state, and that haven't been in a picked or shipped status yet
        recent_allocated_orders AS (
          SELECT distinct order_code
          FROM recent_orders
          WHERE order_code NOT IN (SELECT distinct order_code FROM recent_orders WHERE sts_code='${WMSCustomerOrderStatus.Picked}' OR sts_code='${WMSCustomerOrderStatus.Shipped}')
            AND state_code = '${WMSCustomerOrderState.Allocated_A}' AND sts_code = '${WMSCustomerOrderStatus.Open}'
        ),
        -- for the orders currently in an allocated state, find the most recent fact for each line within the order
        last_record_per_order_line AS (
          SELECT ol.*, ROW_NUMBER() OVER (PARTITION BY order_key, order_line ORDER BY edit_date_timestamp_utc DESC) AS rn
          FROM \`${goldWmsCustomerOrderDetail}\` ol
          WHERE edit_date_timestamp_utc > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 2 DAY)
            AND order_key IN (SELECT distinct order_code FROM recent_allocated_orders)
        ),
        -- sum the quantity allocated for each order line in an open status and allocating state (state=2) and group by sku
        recent_allocations AS (
          SELECT CONCAT(sku, storer_id) as sku,
            SUM(qty_allocated) as qty_allocated
          FROM last_record_per_order_line
          WHERE rn=1
            AND state='${WMSCustomerOrderState.Allocating}' 
            AND sts='${WMSCustomerOrderDetailStatus.Open}'
          GROUP BY sku
        ),
        sku_list AS (
          SELECT
                liv.sku as sku,
                '' as description,
                COALESCE(ROUND(MAX(hi.avg_picked_qty), 1), 0) as averageDailyQuantity,
                COALESCE(ROUND(MAX(hi.avg_picked_orders), 1), 0) as averageDailyOrders,
                -- target_multiplicity doesnt seem to be present anywhere
                0 as targetMultiplicity,
                -- velocity_classification doesnt seem to be present anywhere
                0 as velocityClassification,
                COALESCE(MAX(ra.qty_allocated),0) as quantityAllocated,
                GREATEST(COALESCE(SUM(liv.inventory_qty), 0) - COALESCE(MAX(rp.total_qty_picked), 0), 0) as totalQuantity,
                COALESCE(COUNT(distinct liv.location_id), 0) as locations,
                COALESCE(SUM(liv.skuPositions), 0) as skuPositions,
                COALESCE(MAX(dmc.maxContainers), 0) as maxContainers,
                (COALESCE(COUNT(distinct liv.location_id), 0) - COALESCE(MAX(dmc.maxContainers), 0)) as contOverage,
                FORMAT_TIMESTAMP('%Y-%m-%dT%H:%M:%SZ', MAX(liv.query_timestamp_utc)) as latestInventorySnapshotTimestamp,
                FORMAT_TIMESTAMP('%Y-%m-%dT%H:%M:%SZ', MAX(liv.latestCycleCountTimestamp)) as latestCycleCountTimestamp,
                FORMAT_TIMESTAMP('%Y-%m-%dT%H:%M:%SZ', MAX(liv.latestActivityDateTimestamp)) as latestActivityDateTimestamp
          FROM latest_inventory_quantities_by_location liv 
            LEFT JOIN latest_item_order_averages hi ON liv.sku = hi.sku
            LEFT JOIN recent_picks rp ON liv.sku = rp.sku
            LEFT JOIN recent_allocations ra ON ra.sku = liv.sku 
            LEFT JOIN distinct_max_containers dmc ON liv.sku = dmc.sku
          GROUP BY 
            liv.sku
        ),
        daysOnHandCte AS (
          SELECT 
            sl.*,
            COALESCE(
              CASE 
                WHEN sl.averageDailyQuantity = 0 AND sl.totalQuantity > 0 THEN 999
                ELSE FLOOR(SAFE_DIVIDE(sl.totalQuantity, sl.averageDailyQuantity))
              END,
              0
            ) AS daysOnHand
          FROM sku_list sl
        ),
        FinalSkuList AS (
          SELECT sl.*,
            CASE
              WHEN COALESCE(sl.daysOnHand, 0) > 25 THEN 'over'
              WHEN 
                  COALESCE(sl.daysOnHand, 0) >= 11 
                  AND COALESCE(sl.daysOnHand, 0) <= 25 
                      THEN 'at'
              WHEN COALESCE(sl.daysOnHand, 0) > 0 
                  AND COALESCE(sl.daysOnHand, 0) < 11
                      THEN 'under'
              ELSE 'no'
            END AS status,
            GREATEST(totalQuantity-quantityAllocated, 0) as quantityAvailable
          FROM daysOnHandCte sl
        )
      `;

    // these are the only allowed column names that can be sorted/filtered by through input (untrusted) data
    const allowedColumnNames = [
      'sku',
      'quantityAvailable',
      'quantityAllocated',
      'maxContainers',
      'skuPositions',
      'contOverage',
      'daysOnHand',
      'averageDailyQuantity',
      'averageDailyOrders',
      'latestActivityDateTimestamp',
      'latestCycleCountTimestamp',
      'description',
      'targetMultiplicity',
      'velocityClassification',
      'locations',
      'status',
      'totalQuantity',
    ];

    // these are the only allowed column names that can be used for global search
    const searchableColumns = ['sku', 'description', 'status'];

    const sql: SortingSelectQueryParams = {
      selectFromTable: 'FinalSkuList',
      allowedColumnNames,
      searchableColumns,
      cteSubquery: cte,
      sortFields: params.sortFields,
      page: params.page,
      limit: params.limit,
      searchString: params.searchString,
    };

    const bqQueryGenerator = new BigQueryGenerator();
    const skuList: PaginatedResults<WmsInventorySku[]> =
      await SQLQueryGenerator.paginatedQuery<WmsInventorySku>(
        bqQueryGenerator,
        bigQueryDb,
        sql,
        params.filters,
        params.filterParams,
      );

    if (!skuList.list.length) {
      throw IctError.noContent();
    }

    return skuList;
  }

  /**
   * Retrieve the total number of inventory sku for a specified date range.
   * @param startDate Start date for the date range.
   * @param endDate End date for the date range.
   * @returns Array of Inventory objects for the specified date range
   */
  async getInventoryFacilitySkuListAsync(
    params: InventorySkuQueryStoreParams,
  ): Promise<PaginatedResults<FacilityInventorySku[]>> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const goldFacilityOrder = bigQueryDb.getEdpFullTablePath(
      'gold_facility_order',
    );
    const goldInventorySnapshot = bigQueryDb.getEdpFullTablePath(
      'gold_inventory_snapshot',
    );
    const goldPick = bigQueryDb.getEdpFullTablePath('gold_pick'); // replaces fct_order_line
    const inventoryHistoryTable = bigQueryDb.getEdpFullTablePath(
      'platinum_inventory_history',
    );
    const goldFacilityOrderLine = bigQueryDb.getEdpFullTablePath(
      'gold_facility_order_line',
    );

    const cte = `
        WITH latest_item_order_averages AS (
          SELECT 
            etl_create_timestamp_utc, 
            sku as sku_code,
            MAX(avg_picked_qty) AS avg_picked_qty,
            MAX(avg_picked_orders) AS avg_picked_orders
          FROM ${inventoryHistoryTable}
          WHERE etl_create_timestamp_utc = (
            SELECT MAX(etl_create_timestamp_utc) 
            FROM ${inventoryHistoryTable}
          )
          GROUP BY 
            etl_create_timestamp_utc,
            sku
        ),
        latest_inventory_snapshot_timestamp AS (
          SELECT MAX(snapshot_timestamp_utc) AS latestSnapshot
          FROM ${goldInventorySnapshot}
          WHERE snapshot_timestamp_utc > TIMESTAMP_SUB(CURRENT_TIMESTAMP, INTERVAL 2 DAY)
        ),
        recent_inventory AS (
          SELECT *
          FROM ${goldPick}
          WHERE event_timestamp_utc >= (SELECT latestSnapshot FROM latest_inventory_snapshot_timestamp)
            AND event_timestamp_utc >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 2 DAY) -- Added partition filter
        ),
        latest_inventory_quantities_by_location AS (
          SELECT
            fct.item_sku,
            fct.event_timestamp_utc,
            fct.location_code,
            SUM(fct.picked_qty) AS inventory_qty,
            COUNT(*) AS skuPositions,
            MAX(fct.event_timestamp_utc) AS latestActivityDateTimestamp
          FROM recent_inventory fct
          WHERE event_timestamp_utc >= (SELECT latestSnapshot FROM latest_inventory_snapshot_timestamp)
            AND event_timestamp_utc >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 2 DAY) -- Added partition filter
          GROUP BY 
            item_sku, 
            event_timestamp_utc,
            location_code
        ),
        -- This CTE is used to count all the picks for each SKU since the last inventory snapshot
        recent_picks AS (
          SELECT 
            item_sku AS sku, 
            SUM(picked_qty) AS total_qty_picked
          FROM ${goldPick}
          WHERE event_timestamp_utc >= (SELECT latestSnapshot FROM latest_inventory_snapshot_timestamp)
            AND UPPER(workflow_code) = 'PICKING'
            AND event_timestamp_utc >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY) -- Added partition filter
          GROUP BY item_sku
        ),
        -- This retrieves a subset of facility orders from the last 24 hours
        recent_orders AS (
          SELECT 
            facility_order_code, 
            event_code
          FROM ${goldFacilityOrder}
          WHERE event_timestamp_utc BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR) AND CURRENT_TIMESTAMP()
        ),
        -- Finds the orders that are currently open and in an allocated state
        recent_allocated_orders AS (
          SELECT DISTINCT facility_order_code
          FROM recent_orders
          WHERE event_code = 'INVENTORY_ALLOCATED'
        ),
        -- For the orders currently in an allocated state, find the most recent fact for each line
        last_record_per_order_line AS (
          SELECT 
            ol.*, 
            ROW_NUMBER() OVER (
              PARTITION BY facility_order_code, facility_order_line_code 
              ORDER BY event_timestamp_utc DESC
            ) AS rn
          FROM ${goldFacilityOrderLine} ol
          WHERE event_timestamp_utc > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 2 DAY)
            AND facility_order_code IN (SELECT DISTINCT facility_order_code FROM recent_allocated_orders)
        ),
        -- Sum the quantity allocated for each order line in an allocating state and group by SKU
        recent_allocations AS (
          SELECT 
            sku_requested AS sku,
            SUM(quantity_completed) AS qty_allocated
          FROM last_record_per_order_line
          WHERE event_code = 'ALLOCATED'
          GROUP BY sku
        ),
        sku_list AS (
          SELECT
            liv.item_sku AS sku,
            '' AS description,
            COALESCE(ROUND(MAX(hi.avg_picked_qty), 1), 0) AS averageDailyQuantity,
            COALESCE(ROUND(MAX(hi.avg_picked_orders), 1), 0) AS averageDailyOrders,
            COALESCE(MAX(ra.qty_allocated), 0) AS quantityAllocated,
            GREATEST(COALESCE(SUM(liv.inventory_qty), 0) - COALESCE(MAX(rp.total_qty_picked), 0), 0) AS totalQuantity,
            COALESCE(COUNT(DISTINCT liv.location_code), 0) AS locations,
            COALESCE(SUM(liv.skuPositions), 0) AS skuPositions,
            FORMAT_TIMESTAMP('%Y-%m-%dT%H:%M:%SZ', MAX(liv.event_timestamp_utc)) AS latestInventorySnapshotTimestamp,
            FORMAT_TIMESTAMP('%Y-%m-%dT%H:%M:%SZ', MAX(liv.latestActivityDateTimestamp)) AS latestActivityDateTimestamp
          FROM latest_inventory_quantities_by_location liv 
            LEFT JOIN latest_item_order_averages hi ON liv.item_sku = hi.sku_code
            LEFT JOIN recent_picks rp ON liv.item_sku = rp.sku
            LEFT JOIN recent_allocations ra ON ra.sku = liv.item_sku 
          GROUP BY liv.item_sku
        ),
        daysOnHandCte AS (
          SELECT 
            sl.*,
            COALESCE(
              CASE 
                WHEN sl.averageDailyQuantity = 0 AND sl.totalQuantity > 0 THEN 999
                ELSE FLOOR(SAFE_DIVIDE(sl.totalQuantity, sl.averageDailyQuantity))
              END,
              0
            ) AS daysOnHand
          FROM sku_list sl
        ),
        FinalSkuList AS (
          SELECT 
            doh.*,
            CASE
              WHEN COALESCE(doh.daysOnHand, 0) > 25 THEN 'over'
              WHEN COALESCE(doh.daysOnHand, 0) >= 11 
                AND COALESCE(doh.daysOnHand, 0) <= 25 THEN 'at'
              WHEN COALESCE(doh.daysOnHand, 0) > 0 
                AND COALESCE(doh.daysOnHand, 0) < 11 THEN 'under'
              ELSE 'no'
            END AS status,
            GREATEST(totalQuantity - quantityAllocated, 0) AS quantityAvailable
          FROM daysOnHandCte doh
        )
      `;

    // these are the only allowed column names that can be sorted/filtered by through input (untrusted) data
    const allowedColumnNames = [
      'sku',
      'quantityAvailable',
      'quantityAllocated',
      'skuPositions',
      'daysOnHand',
      'averageDailyQuantity',
      'averageDailyOrders',
      'latestActivityDateTimestamp',
      'description',
      'locations',
      'status',
      'totalQuantity',
    ];

    // these are the only allowed column names that can be used for global search
    const searchableColumns = ['sku', 'description', 'status'];

    const sql: SortingSelectQueryParams = {
      selectFromTable: 'FinalSkuList',
      allowedColumnNames,
      searchableColumns,
      cteSubquery: cte,
      sortFields: params.sortFields,
      page: params.page,
      limit: params.limit,
      searchString: params.searchString,
    };

    const bqQueryGenerator = new BigQueryGenerator();
    const skuList: PaginatedResults<FacilityInventorySku[]> =
      await SQLQueryGenerator.paginatedQuery<FacilityInventorySku>(
        bqQueryGenerator,
        bigQueryDb,
        sql,
        params.filters,
        params.filterParams,
      );

    if (!skuList.list.length) {
      throw IctError.noContent();
    }

    return skuList;
  }

  /**
   * Gets the total number of outstanding advices
   * @returns count of outstanding advices
   */
  async getAdvicesOutstanding(): Promise<AdvicesOutstandingQueryResponse> {
    const bigQueryDb = this.dbProvider.bigQuery;
    const dimReceivingAdviceSummaryTable = bigQueryDb.getFullTablePath(
      'dim_receiving_advice',
    );

    const sql = `
      SELECT
        COUNT(*) AS outstanding_advices
      FROM \`${dimReceivingAdviceSummaryTable}\`
      -- Advice has been created
      WHERE create_time IS NOT NULL
        -- But is not done
        AND finish_time IS NULL
        -- Up to 30 days old
        AND create_time > TIMESTAMP_SUB(CURRENT_TIMESTAMP, INTERVAL 30 DAY)
    `;

    const sqlOptions: Query = {
      query: sql,
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length || !rows[0].outstanding_advices) {
      throw IctError.noContent();
    }

    return rows[0];
  }

  /**
   * Retrieve the average inventory accuracy percentage for a specified date range.
   * @param startDate Start date for the date range.
   * @param endDate End date for the date range.
   * @returns The average inventory accuracy percentage and status or 100 if no results found
   */
  async getAverageInventoryAccuracy(
    startDate: string,
    endDate: string,
    areaFilter: string,
  ): Promise<InventoryAccuracy> {
    // Create new variable instead of modifying parameter
    const updatedAreaFilter = 'All';

    // Use updatedAreaFilter instead of areaFilter in the rest of the function
    const usingAllFilter = updatedAreaFilter === 'All' ? 1 : 0;

    const bigQueryDb = this.dbProvider.bigQuery;

    const tableName =
      this.dbProvider.bigQuery.getFullTablePath('fct_order_line');

    const sql = `
    WITH loadUnitCountingClassification AS (
      SELECT SUM(requested_qty) as expectedQuantity,
      SUM(picked_qty) AS countedQuantity,
      'Manual' AS type
      FROM \`${tableName}\`
      WHERE record_timestamp >= @startDate AND record_timestamp < @endDate
      GROUP BY item_uuid
      )
    SELECT
      AVG((countedQuantity / expectedQuantity) * 100) AS accuracy
      FROM loadUnitCountingClassification
    WHERE (@usingAllFilter = 1
      AND expectedQuantity > 0)
      OR (@usingAllFilter = 0 AND type = @areaFilter
        AND expectedQuantity > 0)
    `;

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
        areaFilter,
        usingAllFilter,
      },
    };

    // execute the query and return the results
    const [result] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!result.length || !result[0]?.accuracy) {
      throw IctError.noContent();
    }

    const accuracy = result[0].accuracy;
    // Return the result as InventoryAccuracy object
    return {
      accuracy: parseFloat(accuracy.toFixed(1)),
      status: getInventoryAccuracyStatus(accuracy),
      areaFilter: updatedAreaFilter,
    };
  }

  /**
   * Get the number of advices in progress.
   * @returns The number of advices in progress
   */
  async getInventoryAdvicesInProgress(): Promise<InventoryAdvicesInProgressQueryResponse> {
    const bigQueryDb = this.dbProvider.bigQuery;
    const dimReceivingAdviceSummaryTable = bigQueryDb.getFullTablePath(
      'dim_receiving_advice',
    );

    const sql = `
      SELECT
        COUNT(*) AS in_progress_advices
      FROM \`${dimReceivingAdviceSummaryTable}\`
      -- Advice has been started
      WHERE start_time IS NOT NULL
        -- But is not done
        AND finish_time IS NULL
    `;

    const sqlOptions: Query = {
      query: sql,
    };

    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!rows.length || !rows[0].in_progress_advices) {
      throw IctError.noContent();
    }

    return rows[0];
  }

  /**
   * Retrieve filter string from the database.
   * @returns Object of area filters
   */
  async getInventoryAreaFilterAsync(): Promise<InventoryAreaFilterDefinition> {
    const bigQueryDb = this.dbProvider.bigQuery;

    // query the database for the desired inventory area filter
    const sql = `
      SELECT area_filter
      FROM \`${bigQueryDb.getFullTablePath('demo01_inventory_area_filter')}\`
      ORDER BY sort_order`;

    const sqlOptions: Query = {
      query: sql,
    };

    // execute the query and return the results
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    // TODO: Include 204 no content response when no data is available

    const areaFilters: InventoryAreaFilterDefinition = {
      areaFilterTable: rows.map(
        (row: {area_filter: string}) => row.area_filter,
      ),
    };

    // return area_filter as a table
    return areaFilters;
  }

  /**
   * Retrieve the average inventory storage utilization for the most recent data snapshot.
   * Filters by areas and aisles defined in config settings.
   * @returns The average inventory storage utilization
   */
  async getInventoryStorageUtilization(): Promise<InventoryStorageUtilization> {
    const bigQueryDb = this.dbProvider.bigQuery;

    // filter by the aisles and areas defined in the config settings
    const storageUtilizationAreaSetting =
      await this.configStore.findMostRelevantSettingForUser(
        this.context.userId,
        undefined,
        'inventory-storage-utilization-area-filter-list',
      );

    let areasToFilterBy: string[] = [];
    if (storageUtilizationAreaSetting && storageUtilizationAreaSetting.value) {
      // Should be a simple string list such as ['ASRS', 'GTP1'], etc.
      areasToFilterBy = (
        storageUtilizationAreaSetting.value as StorageUtilizationAreaFilterList
      ).areaCodes;
    }

    const storageUtilizationAisleSetting =
      await this.configStore.findMostRelevantSettingForUser(
        this.context.userId,
        undefined,
        'inventory-storage-utilization-aisle-filter-list',
      );

    let aislesToFilterBy: string[] = [];
    if (
      storageUtilizationAisleSetting &&
      storageUtilizationAisleSetting.value
    ) {
      // Should be a simple string list such as ['ASRS', 'GTP1'], etc.
      aislesToFilterBy = (
        storageUtilizationAisleSetting.value as StorageUtilizationAisleFilterList
      ).aisleCodes;
    }

    let areaFilter = '';
    let aisleFilter = '';

    const edpFctStorageUtilization = bigQueryDb.getEdpFullTablePath(
      'gold_storage_utilization',
    );

    // build the filters.  If none are found, don't add the filters at all
    areaFilter =
      areasToFilterBy.length > 0
        ? `AND UPPER(area_code) IN (${areasToFilterBy.map(area => `UPPER('${area}')`).toString()})`
        : '';

    aisleFilter =
      aislesToFilterBy.length > 0
        ? `AND CAST(aisle_code AS STRING) IN (${aislesToFilterBy.map(aisle => `'${aisle}'`).toString()})`
        : '';

    const sql = `
          WITH filtered_data AS (
            SELECT 
              total_location_count, 
              empty_location_count, 
              area_code,
              aisle_code,
              event_timestamp_utc
            FROM ${edpFctStorageUtilization}
            WHERE event_timestamp_utc > TIMESTAMP_SUB(CURRENT_TIMESTAMP, INTERVAL 2 DAY)
          )   
          SELECT
            ROUND(AVG(SAFE_DIVIDE((total_location_count - empty_location_count), total_location_count) * 100), 1) AS average_storage_utilization_percentage
          FROM filtered_data 
          WHERE
            event_timestamp_utc = (SELECT MAX(event_timestamp_utc) FROM filtered_data)
            ${areaFilter}
            ${aisleFilter};
      `;

    const sqlOptions: Query = {
      query: sql,
    };

    // execute the query and return the results
    const [result] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    const utilization = result[0].average_storage_utilization_percentage;

    // Return the result as InventoryStorageUtilization object
    return {
      utilizationPercentage: utilization,
    };
  }

  async getInventoryStatusDistributionHistoricalSeriesData(
    startDate: Date,
    endDate: Date,
    distributionType: DistributionType,
  ): Promise<ChartSeriesPercentageData[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const sql = await this.generateItemDistributionStatusQuery(
      distributionType,
      'standard',
    );

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [distributionData] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!distributionData.length) {
      throw IctError.noContent();
    }

    const validatedRows: ChartSeriesPercentageData[] = distributionData.map(
      row => {
        return {
          name: row.name,
          value: row.value,
          isValidPercentage: PercentValidations.isValidPercentage(row.value),
        };
      },
    );

    return validatedRows;
  }

  async getOverInventoryPercentageHistoricalSeriesData(
    startDate: Date,
    endDate: Date,
  ): Promise<ChartSeriesPercentageData[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const sql = await this.generateItemDistributionStatusQuery('over', 'wms');

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [distributionOverData] =
      await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!distributionOverData.length) {
      throw IctError.noContent();
    }

    const validatedRows: ChartSeriesPercentageData[] = distributionOverData.map(
      row => {
        return {
          name: row.name,
          value: row.value,
          isValidPercentage: PercentValidations.isValidPercentage(row.value),
        };
      },
    );

    return validatedRows;
  }

  async getUnderInventoryPercentageHistoricalSeriesData(
    startDate: Date,
    endDate: Date,
  ): Promise<ChartSeriesPercentageData[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const sql = await this.generateItemDistributionStatusQuery('under', 'wms');

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [distributionUnderData] =
      await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!distributionUnderData.length) {
      throw IctError.noContent();
    }

    const validatedRows: ChartSeriesPercentageData[] =
      distributionUnderData.map(row => {
        return {
          name: row.name,
          value: row.value,
          isValidPercentage: PercentValidations.isValidPercentage(row.value),
        };
      });

    return validatedRows;
  }

  /**
   * Helper function to generate the query that returns a list of daily calculations indicating what percentage of
   * inventory was in the status of at, under, over, or no
   * @param distributionStatus under, over, at, or no.  Used to build the query with the correct parameters
   * @returns a raw sql query
   */
  public async generateItemDistributionStatusQuery(
    distributionStatus: DistributionType,
    inventoryDataType: 'wms' | 'standard',
  ): Promise<string> {
    const itemHistoryTable = this.dbProvider.bigQuery.getEdpFullTablePath(
      inventoryDataType === 'wms'
        ? 'platinum_inventory_history_by_zone'
        : 'platinum_inventory_history',
    );

    const workAreaCodesToFilterBy: string[] =
      await this.getWorkAreaCodesToFilterBy();

    let fieldCalculationQry;
    switch (distributionStatus) {
      case 'under':
        fieldCalculationQry =
          'COALESCE(SUM(CASE WHEN daysOnHand > 0 and daysOnHand < 11 THEN 1 ELSE 0 END), 0) AS inventory_status_count';
        break;
      case 'over':
        fieldCalculationQry =
          'COALESCE(SUM(CASE WHEN daysOnHand > 25 THEN 1 ELSE 0 END), 0) AS inventory_status_count';
        break;
      case 'at':
        fieldCalculationQry =
          'COALESCE(SUM(CASE WHEN daysOnHand >= 11 and daysOnHand <= 25 THEN 1 ELSE 0 END), 0) AS inventory_status_count';
        break;
      case 'no':
        fieldCalculationQry =
          'COALESCE(SUM(CASE WHEN daysOnHand <= 0 THEN 1 ELSE 0 END), 0) AS inventory_status_count';
        break;
      default:
        throw IctError.internalServerError(
          "Couldn't determine the requested distribution status category.",
        );
    }

    // build the filter.  If none are found, don't add the filter at all
    const workAreaFilter =
      workAreaCodesToFilterBy.length > 0
        ? `AND zone IN (${workAreaCodesToFilterBy.map(wa => `'${wa}'`).toString()})`
        : '';

    const sql = `
        WITH item_snapshot_by_day AS (
          select etl_create_timestamp_utc,
            sku,
            SUM(inventory_qty) as relevant_inventory_qty, 
            CASE WHEN (MAX(avg_picked_qty) = 0 AND SUM(inventory_qty) > 0) THEN 999 ELSE COALESCE(SAFE_DIVIDE(SUM(inventory_qty), MAX(avg_picked_qty)), 0) END as daysOnHand,
            MAX(avg_picked_qty) as avg_picked_qty,
            MAX(avg_picked_orders) as avg_picked_orders
          FROM ${itemHistoryTable}
          WHERE 
            etl_create_timestamp_utc BETWEEN @startDate AND @endDate
            ${workAreaFilter}
          GROUP BY 
            etl_create_timestamp_utc,
            sku
        ),
        inventory_distribution_counts AS (
            SELECT
                etl_create_timestamp_utc,
                ${fieldCalculationQry},
                COALESCE(COUNT(distinct sku), 0) AS total_inventory_count
            FROM item_snapshot_by_day
            GROUP BY etl_create_timestamp_utc
        )
        SELECT STRING(etl_create_timestamp_utc) as name,
          CASE WHEN total_inventory_count > 0 THEN inventory_status_count / total_inventory_count * 100 ELSE 0 END AS value
        FROM inventory_distribution_counts
        ORDER BY etl_create_timestamp_utc ASC`;

    return sql;
  }

  public async getWorkAreaCodesToFilterBy() {
    const inventoryWorkAreaCodeFilterSetting =
      await this.configStore.findMostRelevantSettingForUser(
        this.context.userId,
        undefined,
        'inventory-work-area-code-filter-list',
      );

    let workAreaCodesToFilterBy: string[] = [];
    if (
      inventoryWorkAreaCodeFilterSetting &&
      inventoryWorkAreaCodeFilterSetting.value
    ) {
      // Should be a simple string list such as ['ASRS', 'GTP1'], etc.
      workAreaCodesToFilterBy = (
        inventoryWorkAreaCodeFilterSetting.value as WorkAreaCodeFilterList
      ).areaCodes;
    }
    return workAreaCodesToFilterBy;
  }

  async getNoInventoryPercentageHistoricalSeriesData(
    startDate: Date,
    endDate: Date,
  ): Promise<ChartSeriesPercentageData[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const sql = await this.generateItemDistributionStatusQuery('no', 'wms');

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    const [distributionNoData] =
      await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!distributionNoData.length) {
      throw IctError.noContent();
    }

    const validatedRows: ChartSeriesPercentageData[] = distributionNoData.map(
      row => {
        return {
          name: row.name,
          value: row.value,
          isValidPercentage: PercentValidations.isValidPercentage(row.value),
        };
      },
    );

    return validatedRows;
  }

  async getInventoryContainersList(
    params: InventoryContainerQueryStoreParams,
  ): Promise<PaginatedResults<InventoryContainerData[]>> {
    const bigQueryDb = this.dbProvider.bigQuery;

    // Determine if we need to bypass config settings
    const workAreaCodesToFilterBy: string[] = params?.byPassConfigSetting
      ? [] // Skip fetching work area codes if bypassing config
      : await this.getWorkAreaCodesToFilterBy();

    // Build the zone filters from the config settings values
    // If none are found, default to adding no filters
    const workAreaFilter =
      workAreaCodesToFilterBy.length > 0
        ? `area_code IN (${workAreaCodesToFilterBy.map(wa => `'${wa}'`).toString()})`
        : '';

    const goldInventorySnapshot = bigQueryDb.getEdpFullTablePath(
      'gold_inventory_snapshot',
    );
    const goldPick = bigQueryDb.getEdpFullTablePath('gold_pick');

    const cte = `
      WITH inventory_timestamp AS (
        SELECT MAX(snapshot_timestamp_utc) AS latest_snapshot
        FROM \`${goldInventorySnapshot}\`
        WHERE snapshot_timestamp_utc >= TIMESTAMP_SUB(CURRENT_TIMESTAMP, INTERVAL 2 DAY)
      ),
      -- get the most recent pick/put, cycle count, and free cycle count events for each container
      recent_events AS (
        SELECT
          source_handling_unit_code as handling_unit_code,
          MAX(CASE WHEN UPPER(work_type_code) IN ('PICKALTR', 'PICK', 'PICKSNBX', 'SNBX', 'PICKING', 'DECANTING') THEN event_timestamp_utc END) as last_pick_time_utc,
          MAX(CASE WHEN UPPER(work_type_code) IN ('CYCLECOUNT', 'CYCLECOUNTING') THEN event_timestamp_utc END) as last_cycle_count_time_utc,
          MAX(CASE WHEN UPPER(work_type_code) IN ('FREECYCLECOUNT') THEN event_timestamp_utc END) as last_free_cycle_count_time_utc
        FROM \`${goldPick}\`
        WHERE event_timestamp_utc > TIMESTAMP_SUB(CURRENT_TIMESTAMP, INTERVAL 2 DAY)
          -- don't include null or empty string handling unit codes
          AND source_handling_unit_code <> ''
          AND UPPER(work_type_code) IN ('PICKALTR', 'PICK', 'PICKSNBX', 'SNBX', 'CYCLECOUNT', 'FREECYCLECOUNT', 'CYCLECOUNTING', 'PICKING', 'DECANTING')
        GROUP BY source_handling_unit_code
      ),
      -- get quantity picked and put for each container
      pick_and_decant_events AS (
        SELECT
          source_handling_unit_code as handling_unit_code,
          item_sku AS sku,
          SUM(CASE WHEN UPPER(work_type_code) IN ('PICKALTR', 'PICK', 'PICKSNBX', 'SNBX', 'PICKING') THEN picked_qty ELSE 0 END) AS picked_quantity_since_snapshot,
          SUM(CASE WHEN UPPER(work_type_code) IN ('DECANTING') THEN picked_qty ELSE 0 END) AS decanted_quantity_since_snapshot
        FROM \`${goldPick}\`
        WHERE event_timestamp_utc > (SELECT latest_snapshot FROM inventory_timestamp)
          AND event_timestamp_utc >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
          AND picked_qty > 0
          AND UPPER(work_type_code) IN ('PICKALTR', 'PICK', 'PICKSNBX', 'SNBX', 'PICKING', 'DECANTING')
          -- don't include null or empty string handling unit codes
          AND source_handling_unit_code <> ''
        GROUP BY source_handling_unit_code, item_sku
      ),
      summed_pick_and_decant_events AS (
        SELECT
          handling_unit_code,
          sku,
          SUM(picked_quantity_since_snapshot) as picked_quantity,
          SUM(decanted_quantity_since_snapshot) as decant_quantity,
        FROM pick_and_decant_events
        GROUP BY handling_unit_code, sku
      ),
      -- get latest inventory snapshot data
      inventory_dump AS (
        SELECT DISTINCT
          base_handling_unit_code as container_id,
          location_code as location_id,
          area_code as zone,
          sku_code as sku,
          COALESCE(total_inventory_qty, 0) as quantity,
          snapshot_timestamp_utc as last_activity_date_utc,
          snapshot_timestamp_utc as snapshot_timestamp_utc
        FROM \`${goldInventorySnapshot}\`
        WHERE snapshot_timestamp_utc > TIMESTAMP_SUB(CURRENT_TIMESTAMP, INTERVAL 2 DAY)
          AND snapshot_timestamp_utc = (SELECT latest_snapshot FROM inventory_timestamp)
          -- don't include null or empty string container ids
          AND base_handling_unit_code <> ''
          ${workAreaFilter ? `AND ${workAreaFilter}` : ''}
      ),
      -- update inventory dump with new quantities based on picks and decants
      -- also join with recent pick events to get timestamps
      container_list AS (
        SELECT
          i.container_id as container_id,
          i.location_id as location_id,
          i.zone as zone,
          i.sku as sku,
          -- subtract picks and add decants
          -- safeguard against negative quantities
          GREATEST(i.quantity - IFNULL(n.picked_quantity, 0) + IFNULL(n.decant_quantity, 0), 0) as quantity,
          -- last activity date is the most recent event
          GREATEST(
            IFNULL(i.last_activity_date_utc, TIMESTAMP('1900-01-01')),
            IFNULL(r.last_pick_time_utc, TIMESTAMP('1900-01-01')),
            IFNULL(r.last_cycle_count_time_utc, TIMESTAMP('1900-01-01')),
            IFNULL(r.last_free_cycle_count_time_utc, TIMESTAMP('1900-01-01'))
          ) as last_activity_date,
          -- last cycle count only comes from most recent cycle count event, not inventory snapshot
          r.last_cycle_count_time_utc as last_cycle_count,
          -- data updated is either the last inventory snapshot or the most recent cycle count event
          GREATEST(
            IFNULL(i.snapshot_timestamp_utc, TIMESTAMP('1900-01-01')), 
            IFNULL(r.last_cycle_count_time_utc, TIMESTAMP('1900-01-01'))
          ) as data_updated,
          -- free cycle count events can only come from pick data, not inventory
          r.last_free_cycle_count_time_utc as free_cycle_count
        FROM inventory_dump i
        LEFT JOIN summed_pick_and_decant_events n
          ON n.handling_unit_code = i.container_id
          AND n.sku = i.sku
        LEFT JOIN recent_events r
          ON i.container_id = r.handling_unit_code
      )
    `;

    const allowedColumnNames = [
      'container_id',
      'location_id',
      'zone',
      'sku',
      'quantity',
      'last_activity_date',
      'last_cycle_count',
      'data_updated',
      'free_cycle_count',
    ];

    const searchableColumns = ['container_id', 'location_id', 'zone', 'sku'];

    const sql: SortingSelectQueryParams = {
      selectFromTable: 'container_list',
      allowedColumnNames,
      cteSubquery: cte,
      sortFields: params?.sortFields,
      page: params?.page || 0,
      limit: params?.limit,
      searchableColumns,
      searchString: params.searchString,
    };

    // Execute the query and return the results
    const bqQueryGenerator = new BigQueryGenerator();
    const containerList: PaginatedResults<InventoryContainerData[]> =
      await SQLQueryGenerator.paginatedQuery<InventoryContainerData>(
        bqQueryGenerator,
        bigQueryDb,
        sql,
        params?.filters,
        params?.filterParams,
      );

    if (!containerList.list.length) {
      throw IctError.noContent();
    }

    return containerList;
  }

  async getInventoryWMSContainersList(
    params: InventoryContainerQueryStoreParams,
  ): Promise<PaginatedResults<InventoryContainerData[]>> {
    const bigQueryDb = this.dbProvider.bigQuery;

    // Determine if we need to bypass config settings
    const workAreaCodesToFilterBy: string[] = params?.byPassConfigSetting
      ? [] // Skip fetching work area codes if bypassing config
      : await this.getWorkAreaCodesToFilterBy();

    // Build the zone filters from the config settings values
    // If none are found, default to adding no filters
    const workAreaFilter =
      workAreaCodesToFilterBy.length > 0
        ? `zone IN (${workAreaCodesToFilterBy.map(wa => `'${wa}'`).toString()})`
        : '';

    const goldWMSInventory =
      bigQueryDb.getEdpFullTablePath('gold_wms_inventory');
    const goldPick = bigQueryDb.getEdpFullTablePath('gold_pick');

    const cte = `
      WITH inventory_timestamp AS (
        SELECT MAX(query_timestamp_utc) AS latest_snapshot
        FROM \`${goldWMSInventory}\`
        WHERE query_timestamp_utc >= TIMESTAMP_SUB(CURRENT_TIMESTAMP, INTERVAL 2 DAY)
      ),
      -- get the most recent pick/put, cycle count, and free cycle count events for each container
      recent_events AS (
        SELECT
          source_handling_unit_code as handling_unit_code,
          MAX(CASE WHEN UPPER(work_type_code) IN ('PICKALTR', 'PICK', 'PICKSNBX', 'SNBX', 'PICKING', 'DECANTING') THEN event_timestamp_utc END) as last_pick_time_utc,
          MAX(CASE WHEN UPPER(work_type_code) IN ('CYCLECOUNT', 'CYCLECOUNTING') THEN event_timestamp_utc END) as last_cycle_count_time_utc,
          MAX(CASE WHEN UPPER(work_type_code) IN ('FREECYCLECOUNT') THEN event_timestamp_utc END) as last_free_cycle_count_time_utc
        FROM \`${goldPick}\`
        WHERE event_timestamp_utc > TIMESTAMP_SUB(CURRENT_TIMESTAMP, INTERVAL 2 DAY)
          -- don't include null or empty string handling unit codes
          AND source_handling_unit_code <> ''
          AND UPPER(work_type_code) IN ('PICKALTR', 'PICK', 'PICKSNBX', 'SNBX', 'CYCLECOUNT', 'FREECYCLECOUNT', 'CYCLECOUNTING', 'PICKING', 'DECANTING')
        GROUP BY source_handling_unit_code
      ),
      -- get quantity picked and put for each container
      pick_and_decant_events AS (
        SELECT
          source_handling_unit_code as handling_unit_code,
          item_sku AS sku,
          SUM(CASE WHEN UPPER(work_type_code) IN ('PICKALTR', 'PICK', 'PICKSNBX', 'SNBX', 'PICKING') THEN picked_qty ELSE 0 END) AS picked_quantity_since_snapshot,
          SUM(CASE WHEN UPPER(work_type_code) IN ('DECANTING') THEN picked_qty ELSE 0 END) AS decanted_quantity_since_snapshot
        FROM \`${goldPick}\`
        WHERE event_timestamp_utc > (SELECT latest_snapshot FROM inventory_timestamp)
          AND event_timestamp_utc >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
          AND picked_qty > 0
          AND UPPER(work_type_code) IN ('PICKALTR', 'PICK', 'PICKSNBX', 'SNBX', 'PICKING', 'DECANTING')
          -- don't include null or empty string handling unit codes
          AND source_handling_unit_code <> ''
        GROUP BY source_handling_unit_code, item_sku
      ),
      summed_pick_and_decant_events AS (
        SELECT
          handling_unit_code,
          sku,
          SUM(picked_quantity_since_snapshot) as picked_quantity,
          SUM(decanted_quantity_since_snapshot) as decant_quantity,
        FROM pick_and_decant_events
        GROUP BY handling_unit_code, sku
      ),
      -- get latest inventory snapshot data
      inventory_dump AS (
        SELECT DISTINCT
          container_id,
          location_id,
          zone,
          sku,
          COALESCE(quantity, 0) as quantity,
          last_activity_date_utc,
          last_cc_inspection_date_utc,
          query_timestamp_utc as snapshot_timestamp_utc
        FROM \`${goldWMSInventory}\`
        WHERE query_timestamp_utc > TIMESTAMP_SUB(CURRENT_TIMESTAMP, INTERVAL 2 DAY)
          AND query_timestamp_utc = (SELECT latest_snapshot FROM inventory_timestamp)
          -- don't include null or empty string container ids
          AND container_id <> ''
          ${workAreaFilter ? `AND ${workAreaFilter}` : ''}
      ),
      -- update inventory dump with new quantities based on picks and decants
      -- also join with recent pick events to get timestamps
      container_list AS (
        SELECT
          i.container_id as container_id,
          i.location_id as location_id,
          i.zone as zone,
          i.sku as sku,
          -- subtract picks and add decants
          -- safeguard against negative quantities
          GREATEST(i.quantity - IFNULL(n.picked_quantity, 0) + IFNULL(n.decant_quantity, 0), 0) as quantity,
          -- last activity date is the most recent event
          GREATEST(
            IFNULL(i.last_activity_date_utc, TIMESTAMP('1900-01-01')),
            IFNULL(r.last_pick_time_utc, TIMESTAMP('1900-01-01')),
            IFNULL(r.last_cycle_count_time_utc, TIMESTAMP('1900-01-01')),
            IFNULL(r.last_free_cycle_count_time_utc, TIMESTAMP('1900-01-01'))
          ) as last_activity_date,
          -- last cycle count comes from either the inventory snapshot or the most recent cycle count event since then
          GREATEST(
            IFNULL(i.last_cc_inspection_date_utc, TIMESTAMP('1900-01-01')), 
            IFNULL(r.last_cycle_count_time_utc, TIMESTAMP('1900-01-01'))
          ) as last_cycle_count,
          -- data updated is either the last inventory snapshot or the most recent cycle count event
          GREATEST(
            IFNULL(i.snapshot_timestamp_utc, TIMESTAMP('1900-01-01')), 
            IFNULL(r.last_cycle_count_time_utc, TIMESTAMP('1900-01-01'))
          ) as data_updated,
          -- free cycle count events can only come from pick data, not inventory
          r.last_free_cycle_count_time_utc as free_cycle_count
        FROM inventory_dump i
        LEFT JOIN summed_pick_and_decant_events n
          ON n.handling_unit_code = i.container_id
          AND n.sku = i.sku
        LEFT JOIN recent_events r
          ON i.container_id = r.handling_unit_code
      )
    `;

    const allowedColumnNames = [
      'container_id',
      'location_id',
      'zone',
      'sku',
      'quantity',
      'last_activity_date',
      'last_cycle_count',
      'data_updated',
      'free_cycle_count',
    ];

    const searchableColumns = ['container_id', 'location_id', 'zone', 'sku'];

    const sql: SortingSelectQueryParams = {
      selectFromTable: 'container_list',
      allowedColumnNames,
      cteSubquery: cte,
      sortFields: params?.sortFields,
      page: params?.page || 0,
      limit: params?.limit,
      searchableColumns,
      searchString: params.searchString,
    };

    // Execute the query and return the results
    const bqQueryGenerator = new BigQueryGenerator();
    const containerList: PaginatedResults<InventoryContainerData[]> =
      await SQLQueryGenerator.paginatedQuery<InventoryContainerData>(
        bqQueryGenerator,
        bigQueryDb,
        sql,
        params?.filters,
        params?.filterParams,
      );

    if (!containerList.list.length) {
      throw IctError.noContent();
    }

    return containerList;
  }

  /**
   * Retrieve the KPI metrics for a selected container using WMS tables.
   * @param containerId Id for the container to retrieve KPIs.
   * @returns KPI metrics for the container.
   */
  async getInventoryWMSContainerEventsKpiAsync(
    containerId: string,
  ): Promise<InventoryContainerEventsKpiResponse[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const goldPick = bigQueryDb.getEdpFullTablePath('gold_pick');
    const goldWmsInventory =
      bigQueryDb.getEdpFullTablePath('gold_wms_inventory');

    const siteTimeZone =
      (
        await this.configStore.findMostRelevantSettingForUser(
          this.context.userId,
          undefined,
          'site-time-zone',
        )
      )?.value ??
      DefaultConfigSettings.find(c => c.name === 'site-time-zone')
        ?.defaultValueString;

    const sql = `
      WITH daily_events AS (
        SELECT
          source_handling_unit_code AS container_id,
          SUM(CASE WHEN TIMESTAMP_TRUNC(event_timestamp_utc, DAY, @siteTimeZone) = TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY, @siteTimeZone)
                  AND work_type_code IN ('FreeCycleCount', 'Inspection') THEN 1 ELSE 0 END) AS cycle_count_events_today,
          SUM(CASE WHEN TIMESTAMP_TRUNC(event_timestamp_utc, DAY, @siteTimeZone) = TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY, @siteTimeZone)
                  AND work_type_code = 'PICK' THEN 1 ELSE 0 END) AS pick_events_today
        FROM ${goldPick}
        WHERE TIMESTAMP_TRUNC(event_timestamp_utc, DAY, @siteTimeZone) = TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY, @siteTimeZone)
          AND source_handling_unit_code = @selected_container_id
        GROUP BY source_handling_unit_code
      ),
      seven_day_events AS (
        SELECT
          item_sku AS sku,
          AVG(CASE WHEN work_type_code IN ('FreeCycleCount', 'Inspection') THEN 1 ELSE 0 END) AS average_daily_cycle_count,
          AVG(CASE WHEN work_type_code = 'PICK' THEN 1 ELSE 0 END) AS average_daily_pick_events
        FROM ${goldPick}
        WHERE event_timestamp_utc >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
          AND source_handling_unit_code = @selected_container_id
        GROUP BY item_sku
      ),
      recent_wms_inventory AS (
        SELECT *
        FROM ${goldWmsInventory}
        WHERE query_timestamp_utc > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 2 DAY)
      ),
      latest_inventory_snapshot_timestamp AS (
        SELECT MAX(query_timestamp_utc) AS latest_snapshot
        FROM recent_wms_inventory
      ),
      latest_inventory AS (
        SELECT
          fct.container_id,
          fct.location_id,
          fct.zone,
          fct.sku,
          SUM(fct.quantity) AS total_quantity,
          MAX(fct.last_activity_date_utc) AS last_activity_date,
          MAX(fct.last_cc_inspection_date_utc) AS last_cycle_count
        FROM recent_wms_inventory fct
        WHERE fct.query_timestamp_utc = (SELECT latest_snapshot FROM latest_inventory_snapshot_timestamp)
          AND fct.container_id = @selected_container_id
        GROUP BY fct.container_id, fct.location_id, fct.zone, fct.sku
      ),
      pick_and_decant_events AS (
        SELECT
          source_handling_unit_code AS container_id,
          item_sku AS sku,
          SUM(CASE WHEN work_type_code = 'PICK' THEN picked_qty ELSE 0 END) AS picked_quantity_since_snapshot,
          SUM(CASE WHEN work_type_code = 'DECANT' THEN picked_qty ELSE 0 END) AS decanted_quantity_since_snapshot
        FROM ${goldPick}
        WHERE event_timestamp_utc > (SELECT MAX(last_activity_date) FROM latest_inventory)
          AND event_timestamp_utc >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
          AND picked_qty > 0
          AND work_type_code IN ('PICK', 'DECANT')
          AND source_handling_unit_code = @selected_container_id
        GROUP BY source_handling_unit_code, item_sku
      ),
      calculated_inventory AS (
        SELECT
          li.container_id,
          li.location_id,
          li.zone,
          li.sku,
          GREATEST(li.total_quantity - COALESCE(pde.picked_quantity_since_snapshot, 0) + COALESCE(pde.decanted_quantity_since_snapshot, 0), 0) AS quantity,
          li.last_activity_date,
          li.last_cycle_count,
          COALESCE(pde.picked_quantity_since_snapshot, 0) AS picked_quantity_since_snapshot,
          COALESCE(pde.decanted_quantity_since_snapshot, 0) AS decanted_quantity_since_snapshot
        FROM latest_inventory li
        LEFT JOIN pick_and_decant_events pde
          ON li.container_id = pde.container_id
          AND li.sku = pde.sku
      )
      SELECT
        ci.container_id,
        ci.location_id,
        ci.zone,
        ci.sku,
        ci.quantity,
        ci.last_activity_date,
        ci.last_cycle_count,
        (SELECT latest_snapshot FROM latest_inventory_snapshot_timestamp) AS data_updated,
        COALESCE(de.cycle_count_events_today, 0) AS cycle_count_events_today,
        COALESCE(de.pick_events_today, 0) AS pick_events_today,
        COALESCE(se.average_daily_cycle_count, 0) AS average_daily_cycle_count,
        COALESCE(se.average_daily_pick_events, 0) AS average_daily_pick_events,
        ci.picked_quantity_since_snapshot,
        ci.decanted_quantity_since_snapshot
      FROM calculated_inventory ci
      LEFT JOIN daily_events de ON ci.container_id = de.container_id
      LEFT JOIN seven_day_events se ON ci.sku = se.sku
      WHERE ci.container_id = @selected_container_id
      ORDER BY ci.container_id, ci.sku
    `;

    const sqlOptions: Query = {
      query: sql,
      params: {
        selected_container_id: containerId,
        siteTimeZone,
      },
    };

    const [result] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!result || !result.length) {
      throw IctError.noContent();
    }

    return result;
  }

  /**
   * Retrieve the KPI metrics for a selected container.
   * @param containerId Id for the container to retrieve KPIs.
   * @returns KPI metrics for the container.
   */
  async getInventoryContainerEventsKpiAsync(
    containerId: string,
  ): Promise<InventoryContainerEventsKpiResponse[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const goldPick = bigQueryDb.getEdpFullTablePath('gold_pick');
    const goldInventorySnapshot = bigQueryDb.getEdpFullTablePath(
      'gold_inventory_snapshot',
    );

    const siteTimeZone =
      (
        await this.configStore.findMostRelevantSettingForUser(
          this.context.userId,
          undefined,
          'site-time-zone',
        )
      )?.value ??
      DefaultConfigSettings.find(c => c.name === 'site-time-zone')
        ?.defaultValueString;

    const sql = `
      WITH daily_events AS (
        SELECT
          source_handling_unit_code AS container_id,
          SUM(CASE WHEN TIMESTAMP_TRUNC(event_timestamp_utc, DAY, @siteTimeZone) = TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY, @siteTimeZone)
                  AND UPPER(work_type_code) IN ('CYCLECOUNT', 'INSPECTION') THEN 1 ELSE 0 END) AS cycle_count_events_today,
          SUM(CASE WHEN TIMESTAMP_TRUNC(event_timestamp_utc, DAY, @siteTimeZone) = TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY, @siteTimeZone)
                  AND UPPER(work_type_code) = 'PICKING' THEN 1 ELSE 0 END) AS pick_events_today,
          -- Get the last cycle count time with max event timestamp when work type code is CYCLECOUNT or INSPECTION
          MAX(CASE WHEN UPPER(work_type_code) IN ('CYCLECOUNT', 'INSPECTION') THEN event_timestamp_utc END) AS last_cycle_count_time,
          -- Get the last activity date and just get the max event timestamp
          MAX(event_timestamp_utc) AS last_activity_time
        FROM ${goldPick}
        WHERE TIMESTAMP_TRUNC(event_timestamp_utc, DAY, @siteTimeZone) = TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY, @siteTimeZone)
        GROUP BY source_handling_unit_code
      ),
      seven_day_events AS (
        SELECT
          item_sku AS sku,
          AVG(CASE WHEN UPPER(work_type_code) IN ('CYCLECOUNT', 'INSPECTION') THEN 1 ELSE 0 END) AS average_daily_cycle_count,
          AVG(CASE WHEN UPPER(work_type_code) = 'PICKING' THEN 1 ELSE 0 END) AS average_daily_pick_events
        FROM ${goldPick}
        WHERE TIMESTAMP_TRUNC(event_timestamp_utc, DAY, @siteTimeZone) >= TIMESTAMP_TRUNC(TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY), DAY, @siteTimeZone)
        GROUP BY item_sku
      ),
      inventory_timestamp AS (
        SELECT MAX(snapshot_timestamp_utc) AS latest_snapshot
        FROM ${goldInventorySnapshot}
        WHERE inventory_unit_code = @selected_container_id
          AND snapshot_timestamp_utc >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
      ),
      latest_inventory_snapshot AS (
        SELECT 
          inventory_unit_code AS container_id,
          location_code AS location_id,
          '' AS zone, -- No zone in snapshot table
          sku_code AS sku,
          total_inventory_qty AS total_quantity,
          unallocated_inventory_qty AS unallocated_quantity,
          uom,
          snapshot_timestamp_utc AS last_activity_date,
          snapshot_timestamp_utc AS last_cycle_count,
          snapshot_timestamp_utc AS data_updated
        FROM ${goldInventorySnapshot}
        WHERE snapshot_timestamp_utc = (SELECT latest_snapshot FROM inventory_timestamp)
          AND inventory_unit_code = @selected_container_id
          AND snapshot_timestamp_utc >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
      ),
      pick_and_decant_events AS (
        SELECT
          source_handling_unit_code AS container_id,
          item_sku AS sku,
          SUM(CASE WHEN UPPER(work_type_code) = 'PICKING' THEN picked_qty ELSE 0 END) AS picked_quantity_since_snapshot,
          SUM(CASE WHEN UPPER(work_type_code) = 'DECANT' THEN picked_qty ELSE 0 END) AS decanted_quantity_since_snapshot
        FROM ${goldPick}
        WHERE event_timestamp_utc > (SELECT MAX(last_activity_date) FROM latest_inventory_snapshot)
          AND TIMESTAMP_TRUNC(event_timestamp_utc, DAY, @siteTimeZone) >= TIMESTAMP_TRUNC(TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY), DAY, @siteTimeZone)
          AND picked_qty > 0
          AND UPPER(work_type_code) IN ('PICKING', 'DECANT')
        GROUP BY source_handling_unit_code, item_sku
      ),
      calculated_inventory AS (
        SELECT
          li.container_id,
          li.location_id,
          li.zone,
          li.sku,
          GREATEST(li.total_quantity - COALESCE(pde.picked_quantity_since_snapshot, 0) + COALESCE(pde.decanted_quantity_since_snapshot, 0), 0) AS quantity,
          li.unallocated_quantity + COALESCE(pde.picked_quantity_since_snapshot, 0) AS unallocated_quantity,
          li.uom,
          li.last_activity_date,
          li.last_cycle_count,
          li.data_updated,
          COALESCE(pde.picked_quantity_since_snapshot, 0) AS picked_quantity_since_snapshot,
          COALESCE(pde.decanted_quantity_since_snapshot, 0) AS decanted_quantity_since_snapshot
        FROM latest_inventory_snapshot li
        LEFT JOIN pick_and_decant_events pde 
          ON li.container_id = pde.container_id 
          AND li.sku = pde.sku
      )
      SELECT
        ci.container_id,
        ci.location_id,
        ci.zone,
        ci.sku,
        ci.quantity,
        -- coalesce between this and de.last activity to get greatest between both
        GREATEST(ci.last_activity_date, COALESCE(de.last_activity_time, TIMESTAMP('1900-01-01'))) AS last_activity_date,
        -- coalesce between this and de.last cycle time to get greatest between both
        GREATEST(ci.last_cycle_count, COALESCE(de.last_cycle_count_time, TIMESTAMP('1900-01-01'))) AS last_cycle_count,
        -- coalesce between this and de.last activity to get greatest between both
        GREATEST(ci.data_updated, COALESCE(de.last_activity_time, TIMESTAMP('1900-01-01'))) AS data_updated,
        COALESCE(de.cycle_count_events_today, 0) AS cycle_count_events_today,
        COALESCE(de.pick_events_today, 0) AS pick_events_today,
        COALESCE(se.average_daily_cycle_count, 0) AS average_daily_cycle_count,
        COALESCE(se.average_daily_pick_events, 0) AS average_daily_pick_events,
        ci.picked_quantity_since_snapshot,
        ci.decanted_quantity_since_snapshot
      FROM calculated_inventory ci
      LEFT JOIN daily_events de ON ci.container_id = de.container_id
      LEFT JOIN seven_day_events se ON ci.sku = se.sku
      ORDER BY COALESCE(de.cycle_count_events_today, 0) DESC
      LIMIT 50
    `;

    const sqlOptions: Query = {
      query: sql,
      params: {
        selected_container_id: containerId,
        siteTimeZone,
      },
    };

    const [result] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!result || !result.length) {
      throw IctError.noContent();
    }

    return result;
  }

  async getInventoryContainerEventsList(
    containerId: string,
    params: InventoryContainerEventsQueryStoreParams,
  ): Promise<PaginatedResults<InventoryContainerEventsData[]>> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const goldPick = bigQueryDb.getEdpFullTablePath('gold_pick');

    const cte = `
        WITH containerEventsList AS (
          SELECT DISTINCT
            event_timestamp_utc as event_date,
            work_type_code as event_type,
            destination_handling_unit_code as destination_container,
            picked_qty as quantity,
            operator_name as operator_name,
            workstation_code as workstation_code,
            item_sku as sku_code
          FROM ${goldPick}
          WHERE source_handling_unit_code = @containerId
                  AND event_timestamp_utc > TIMESTAMP(DATE_SUB(CURRENT_DATE(), INTERVAL @months MONTH))
          ORDER BY event_date DESC
      )
      `;

    const allowedColumnNames = [
      'event_date',
      'event_type',
      'destination_container',
      'quantity',
      'operator_name',
      'workstation_code',
      'sku_code',
    ];

    const sql: SortingSelectQueryParams = {
      selectFromTable: 'containerEventsList',
      allowedColumnNames,
      cteSubquery: cte,
      sortFields: params.sortFields,
      page: params.page,
      limit: params.limit,
    };

    const filterParams = {
      ...params.filterParams,
      containerId,
      months: params.months ?? 6,
      counter: 0,
    };

    // Execute the query and return the paginated results.
    const bqQueryGenerator = new BigQueryGenerator();
    const containerEvents: PaginatedResults<InventoryContainerEventsData[]> =
      await SQLQueryGenerator.paginatedQuery<InventoryContainerEventsData>(
        bqQueryGenerator,
        bigQueryDb,
        sql,
        params.filters,
        filterParams,
      );

    if (!containerEvents.list.length) {
      throw IctError.noContent();
    }

    return containerEvents;
  }

  async getInventoryHighImpactSkuListAsync(
    params: InventoryHighImpactSKUListStoreParams,
  ): Promise<PaginatedResults<InventoryHighImpactSKU[]>> {
    const bigQueryDb = this.dbProvider.bigQuery;
    const orderLineFactTable = bigQueryDb.getFullTablePath('fct_order_line');
    const inventoryFactTable = bigQueryDb.getFullTablePath('fct_inventory');
    const itemDimTable = bigQueryDb.getFullTablePath('dim_item');
    const workAreaDimTable = bigQueryDb.getFullTablePath('dim_work_area');
    const hst_item = this.dbProvider.bigQuery.getFullTablePath('hst_item');

    const highImpactPercentageSetting =
      await this.configStore.findMostRelevantSettingForUser(
        this.context.userId,
        undefined,
        'inventory-high-impact-sku-percentage',
      );
    const highImpactPercentage =
      highImpactPercentageSetting && highImpactPercentageSetting.value
        ? (highImpactPercentageSetting.value as number) / 100
        : 0.1;

    const cteSQL = `
    WITH SKUCounts AS (
      SELECT
        Orders.item_uuid,
        COUNT(Orders.item_uuid) as count,
        SUM(COUNT(Orders.item_uuid)) OVER() as total
      FROM ${orderLineFactTable} Orders
      WHERE Orders.record_timestamp BETWEEN @startDate AND @endDate
      GROUP BY Orders.item_uuid
      ORDER BY COUNT(Orders.item_uuid) DESC
    ),
    HighImpactSKUs as (
      SELECT * FROM SKUCounts
      WHERE count / total >= ${highImpactPercentage}
    ),
    HighImpactOrders as (
      SELECT * FROM ${orderLineFactTable} Orders
      WHERE Orders.item_uuid in (SELECT item_uuid FROM HighImpactSKUs)
      AND Orders.record_timestamp BETWEEN @startDate AND @endDate
    ),
    ItemsShipped as (
      SELECT SUM(Orders.picked_qty) / 10 as ItemsShippedPerDay FROM ${orderLineFactTable} Orders
      WHERE Orders.record_timestamp BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 DAY) AND CURRENT_TIMESTAMP()
    ),
    InventoryTotalQty as (
      SELECT * FROM ${inventoryFactTable} Inventory
      WHERE Inventory.item_uuid IN (SELECT item_uuid FROM HighImpactSKUs)
      AND Inventory.record_timestamp BETWEEN @startDate AND @endDate
    ),
    LatestItemHistory AS (
      SELECT agg.hst_item.*
      FROM 
      (
          SELECT item_uuid, 
          ARRAY_AGG(STRUCT(hst_item) ORDER BY record_timestamp DESC)[SAFE_OFFSET(0)] agg 
          FROM \`${hst_item}\` hst_item GROUP BY item_uuid
      )
    ),
    HighImpactItems as (
      SELECT
        Item.item_sku as sku,
        COALESCE(SAFE_DIVIDE(SUM(Orders.picked_qty), SUM(inventory.inventory_qty)) * 100, 0)as accuracy,
        Areas.work_area_name as storageArea,
        SUM(Orders.picked_qty) as quantity,
        0 as cubeUtilization,
        COALESCE(ItemHist.days_on_hand, 0) as daysOnHand
      FROM HighImpactOrders Orders
      INNER JOIN ${itemDimTable} Item ON Orders.item_uuid = Item.item_uuid
      INNER JOIN ${workAreaDimTable} Areas ON Areas.work_area_uuid = Orders.work_area_uuid
      LEFT JOIN LatestItemHistory ItemHist on Item.item_uuid = ItemHist.item_uuid
      LEFT JOIN InventoryTotalQty inventory on inventory.item_uuid = Orders.item_uuid
      GROUP BY Item.item_sku, Areas.work_area_name, ItemHist.days_on_hand
      ORDER BY Item.item_sku
    )`;

    const updatedParams = {
      ...params,
      sortFields: params.sortFields.length
        ? params.sortFields
        : [{isDescending: true, columnName: 'accuracy'}],
    };

    const allowedColumnNames = [
      'sku',
      'accuracy',
      'storageArea',
      'quantity',
      'cubeUtilization',
      'daysOnHand',
    ];

    const sql: SortingSelectQueryParams = {
      selectFromTable: 'HighImpactItems',
      allowedColumnNames,
      cteSubquery: cteSQL,
      sortFields: updatedParams.sortFields,
      limit: updatedParams.limit,
      page: updatedParams.page,
    };

    const filterParams = addStartEndDateParamValues(
      updatedParams.startDate,
      updatedParams.endDate,
      params.filterParams,
    );

    // execute the query and return the results
    const bqQueryGenerator = new BigQueryGenerator();
    const skuList: PaginatedResults<InventoryHighImpactSKU[]> =
      await SQLQueryGenerator.paginatedQuery<InventoryHighImpactSKU>(
        bqQueryGenerator,
        bigQueryDb,
        sql,
        updatedParams.filters,
        filterParams,
      );

    if (!skuList.list.length) {
      throw IctError.noContent();
    }

    return skuList;
  }

  async getInventoryAccuracyMonthlyAggregatedSeries(settings: {
    startDate: string;
    endDate: string;
    departmentFilter: string;
  }): Promise<InventoryPerformanceSeriesQueryResponse[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const fctOrderLineTable = bigQueryDb.getFullTablePath('fct_order_line');

    const sql = `
      WITH monthlyPercentages AS (
        SELECT
          (SAFE_DIVIDE(SUM(picked_qty) / SUM(requested_qty), 1) * 100) AS percentage,
          EXTRACT(MONTH FROM record_timestamp) AS month,
          EXTRACT(YEAR FROM record_timestamp) AS year,
        FROM \`${fctOrderLineTable}\`
        WHERE CAST(record_timestamp AS DATE) >= DATE(@startDate) AND CAST(record_timestamp AS DATE) < DATE(@endDate)
        GROUP BY year, month
      ),
      -- This is used to fill in any months with no data
      -- Used to prevent "holes" in the data
      fill_months AS (
        SELECT
          EXTRACT(MONTH FROM fill_date) AS month,
          EXTRACT(YEAR FROM fill_date) AS year,
        FROM UNNEST(GENERATE_DATE_ARRAY(DATE(@startDate), DATE(@endDate))) fill_date
        GROUP BY year, month
      )
      SELECT
        IFNULL(mp.percentage, 0) AS percentage,
        DATE(fm.year, fm.month, 1) AS aggregated_date
      FROM monthlyPercentages mp
      FULL JOIN fill_months fm
        ON mp.year = fm.year AND mp.month = fm.month
      ORDER BY aggregated_date
    `;
    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate: settings.startDate,
        endDate: settings.endDate,
      },
    };

    const [monthlyPercentages] =
      await bigQueryDb.executeMonitoredJob(sqlOptions);
    return monthlyPercentages;
  }

  async getOrderFulfillmentRateMonthlyAggregatedSeries(settings: {
    startDate: string;
    endDate: string;
    departmentFilter: string;
  }): Promise<InventoryPerformanceSeriesQueryResponse[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const fctOrderLineTable = bigQueryDb.getFullTablePath('fct_order_line');

    const sql = `
      WITH monthlyPercentages AS (
        SELECT
          (SAFE_DIVIDE(SUM(pick_order_line_complete_ind) / COUNT(*), 1) * 100) AS percentage,
          EXTRACT(MONTH FROM record_timestamp) AS month,
          EXTRACT(YEAR FROM record_timestamp) AS year,
        FROM \`${fctOrderLineTable}\`
        WHERE CAST(record_timestamp AS DATE) >= DATE(@startDate) AND CAST(record_timestamp AS DATE) < DATE(@endDate)
        GROUP BY year, month
      ),
      -- This is used to fill in any months with no data
      -- Used to prevent "holes" in the data
      fill_months AS (
        SELECT
          EXTRACT(MONTH FROM fill_date) AS month,
          EXTRACT(YEAR FROM fill_date) AS year,
        FROM UNNEST(GENERATE_DATE_ARRAY(DATE(@startDate), DATE(@endDate))) fill_date
        GROUP BY year, month
      )
      SELECT
        IFNULL(mp.percentage, 0) AS percentage,
        DATE(fm.year, fm.month, 1) AS aggregated_date
      FROM monthlyPercentages mp
      FULL JOIN fill_months fm
        ON mp.year = fm.year AND mp.month = fm.month
      ORDER BY aggregated_date
    `;
    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate: settings.startDate,
        endDate: settings.endDate,
      },
    };

    const [monthlyPercentages] =
      await bigQueryDb.executeMonitoredJob(sqlOptions);
    return monthlyPercentages;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async getStorageUtilizationMonthlyAggregatedSeries(settings: {
    startDate: string;
    endDate: string;
    departmentFilter: string;
  }): Promise<InventoryPerformanceSeriesQueryResponse[]> {
    // Don't have any data for this yet, table: 'fct_bin_utilization'
    // https://wiki.dematic.com/display/ICT/KPI%3A+Storage+Utilization
    // TODO: Remove skip from test once query is implemented
    return Promise.resolve([
      {
        aggregated_date: new BigQueryDate('2023-10-01'),
        percentage: 99,
      },
      {
        aggregated_date: new BigQueryDate('2023-11-01'),
        percentage: 97,
      },
      {
        aggregated_date: new BigQueryDate('2023-12-01'),
        percentage: 96,
      },
      {
        aggregated_date: new BigQueryDate('2024-01-01'),
        percentage: 94,
      },
      {
        aggregated_date: new BigQueryDate('2024-02-01'),
        percentage: 92,
      },
      {
        aggregated_date: new BigQueryDate('2024-03-01'),
        percentage: 90,
      },
      {
        aggregated_date: new BigQueryDate('2024-04-01'),
        percentage: 88,
      },
      {
        aggregated_date: new BigQueryDate('2024-05-01'),
        percentage: 86,
      },
      {
        aggregated_date: new BigQueryDate('2024-06-01'),
        percentage: 84,
      },
      {
        aggregated_date: new BigQueryDate('2024-07-01'),
        percentage: 82,
      },
      {
        aggregated_date: new BigQueryDate('2024-08-01'),
        percentage: 80,
      },
      {
        aggregated_date: new BigQueryDate('2024-09-01'),
        percentage: 77,
      },
    ]);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async getStockOutMonthlyAggregatedSeries(settings: {
    startDate: string;
    endDate: string;
    departmentFilter: string;
  }): Promise<InventoryPerformanceSeriesQueryResponse[]> {
    // Don't have any data for this yet, table: 'fct_inventory'
    // TODO: Remove skip from test once query is implemented
    return Promise.resolve([
      {
        aggregated_date: new BigQueryDate('2023-10-01'),
        percentage: 99,
      },
      {
        aggregated_date: new BigQueryDate('2023-11-01'),
        percentage: 97,
      },
      {
        aggregated_date: new BigQueryDate('2023-12-01'),
        percentage: 96,
      },
      {
        aggregated_date: new BigQueryDate('2024-01-01'),
        percentage: 94,
      },
      {
        aggregated_date: new BigQueryDate('2024-02-01'),
        percentage: 92,
      },
      {
        aggregated_date: new BigQueryDate('2024-03-01'),
        percentage: 90,
      },
      {
        aggregated_date: new BigQueryDate('2024-04-01'),
        percentage: 88,
      },
      {
        aggregated_date: new BigQueryDate('2024-05-01'),
        percentage: 86,
      },
      {
        aggregated_date: new BigQueryDate('2024-06-01'),
        percentage: 84,
      },
      {
        aggregated_date: new BigQueryDate('2024-07-01'),
        percentage: 82,
      },
      {
        aggregated_date: new BigQueryDate('2024-08-01'),
        percentage: 80,
      },
      {
        aggregated_date: new BigQueryDate('2024-09-01'),
        percentage: 77,
      },
    ]);
  }

  async getAtInventoryPercentageHistoricalDataAsync(
    startDate: Date,
    endDate: Date,
  ): Promise<ChartSeriesPercentageData[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const sql = await this.generateItemDistributionStatusQuery('at', 'wms');

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };
    const [rows] = await bigQueryDb.executeMonitoredJob(sqlOptions);
    if (!rows.length) {
      throw IctError.noContent();
    }

    const validatedRows: ChartSeriesPercentageData[] = rows.map(row => {
      return {
        name: row.name,
        value: row.value,
        isValidPercentage: PercentValidations.isValidPercentage(row.value),
      };
    });

    return validatedRows;
  }

  /**
   * Retrieve the location information about a selected sku.
   * @param skuId Id for the inventory to locate.
   * @returns The inventory locations seperated by storage areas.
   */
  async getInventoryForecastDrawerLocations(
    skuId: string,
  ): Promise<InventorySkuLocationResponse[]> {
    const inventoryZones = await this.configStore
      .findMostRelevantSettingForUser(
        this.context.userId,
        undefined,
        'inventory-zone-mapping',
      )
      .then(setting => {
        return setting?.value as InventoryForecastZoneMapping;
      });

    const forwardPick = inventoryZones?.forward_pick;
    const reserveStorage = inventoryZones?.reserve_storage;

    if (
      !inventoryZones ||
      !forwardPick ||
      !reserveStorage ||
      !forwardPick.length ||
      !reserveStorage.length
    ) {
      throw IctError.internalServerError(
        'Config setting or value for inventory zone mapping not found',
      );
    }

    const forward = forwardPick
      .map(item => `zone LIKE '%${item}%'`)
      .join(' OR ');
    const reserve = reserveStorage
      .map(item => `zone LIKE '%${item}%'`)
      .join(' OR ');

    const bigQueryDb = this.dbProvider.bigQuery;

    const gold_wms_inventory =
      bigQueryDb.getEdpFullTablePath('gold_wms_inventory');

    const sql = `
      WITH latest_snapshot AS (
          SELECT MAX(query_timestamp_utc) AS latestSnapshot
          FROM \`${gold_wms_inventory}\`
          WHERE query_timestamp_utc >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)),
      skuData AS (
          SELECT 
              sku, 
              container_id, 
              location_id, 
              location_type, 
              quantity, 
              sku_size, 
              condition_code, 
              zone, 
              last_activity_date_local,
              ROW_NUMBER() OVER (PARTITION BY container_id ORDER BY last_activity_date_local DESC) AS row_num 
          FROM \`${gold_wms_inventory}\` gwi
          JOIN latest_snapshot ls ON gwi.query_timestamp_utc = ls.latestSnapshot
          WHERE gwi.query_timestamp_utc >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)
            AND sku = '${skuId}'
        ),
      groupedData AS (
          SELECT 
              location_id, 
              UPPER(location_type) as location_type, 
              container_id, 
              quantity, 
              sku_size,
              condition_code, 
              zone, 
              last_activity_date_local,
              CASE 
                  WHEN ${reserve} THEN 'reserve'
                  WHEN ${forward} THEN 'forward'
              END AS zone_group
          FROM skuData 
          WHERE row_num = 1
      ),
      containerCount AS (
          SELECT location_id, COUNT(container_id) AS container_count, SUM(quantity) AS total_quantity, quantity as container_quantity
          FROM groupedData 
          GROUP BY location_id, quantity
      ),
      reserveData AS (
          SELECT 
              g.location_id, 
              g.location_type,
              CASE WHEN MAX(c.container_count) > 1 THEN NULL ELSE MAX(g.container_id) END AS container_id, 
              MAX(c.container_count) AS container_count,
              MAX(c.total_quantity) AS quantity, 
              MAX(c.container_quantity) AS container_quantity,
              MAX(g.sku_size) AS sku_size,
              g.condition_code, 
              g.zone, 
              MAX(g.last_activity_date_local) AS last_activity_date_local
          FROM groupedData g
          JOIN containerCount c ON g.location_id = c.location_id
          WHERE g.zone_group = 'reserve'
          GROUP BY g.location_id, g.location_type, g.condition_code, g.zone
          ORDER BY g.location_id
      ),
      forwardData AS (
          SELECT 
              g.location_id, 
              g.location_type,
              CASE WHEN MAX(c.container_count) > 1 THEN NULL ELSE MAX(g.container_id) END AS container_id, 
              MAX(c.container_count) AS container_count,
              MAX(c.total_quantity) AS quantity, 
              MAX(g.sku_size) AS sku_size,
              g.condition_code, 
              g.zone, 
              MAX(g.last_activity_date_local) AS last_activity_date_local
          FROM groupedData g
          JOIN containerCount c ON g.location_id = c.location_id
          WHERE g.zone_group = 'forward'
          GROUP BY g.location_id, g.location_type, g.condition_code, g.zone
          ORDER BY g.location_id
      )

      SELECT 
          ARRAY(
              SELECT AS STRUCT * 
              FROM reserveData
              ORDER BY location_id, container_id
          ) AS reserve,
          ARRAY(
              SELECT AS STRUCT * 
              FROM forwardData
              ORDER BY location_id, container_id
          ) AS forward
      FROM groupedData
      LIMIT 1;
      `;

    const sqlOptions: Query = {
      query: sql,
    };

    // execute the query using the clients BQ project to access the EDP project and return the results
    const [result] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!result || !result.length) {
      throw IctError.noContent();
    }

    return result;
  }

  /**
   * Retrieve the information for open orders that contain a selected sku.
   * @param skuId Id for the sku associated with open orders.
   * @returns The open order information that are associated with the sku.
   */
  async getInventoryForecastDrawerOrders(
    skuId: string,
  ): Promise<InventorySkuOrdersResponse[]> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const wmsInventoryTable =
      bigQueryDb.getEdpFullTablePath('gold_wms_inventory');

    const wmsCustomerOrderTable = bigQueryDb.getEdpFullTablePath(
      'gold_wms_customer_order',
    );

    const wmsCustomerOrderDetailTable = bigQueryDb.getEdpFullTablePath(
      'gold_wms_customer_order_detail',
    );

    const pickTaskTable = bigQueryDb.getEdpFullTablePath('gold_pick_task');

    const sql = `
      WITH skuDetails AS (
      SELECT
        sku_style AS sku,
        sku_size AS size, 
        sku AS sku_code,
      FROM \`${wmsInventoryTable}\`
      WHERE sku = @skuId
        AND TIMESTAMP_TRUNC(query_timestamp_utc, DAY) > TIMESTAMP_SUB(TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY), INTERVAL 2 DAY)
      LIMIT 1
    ),
    allOrders AS (
      SELECT 
        external_code,
        order_code,
        state_code,
        sts_code, 
        priority_code,
        ship_date_timestamp_utc,
        ship_no_later_date_time_local,
        short_flag,
        __raw_message_ingestion_time,
        ROW_NUMBER() OVER (PARTITION BY order_code ORDER BY __raw_message_ingestion_time DESC, edit_date_timestamp_utc DESC) AS row_num
      FROM \`${wmsCustomerOrderTable}\`
      WHERE TIMESTAMP_TRUNC(edit_date_timestamp_utc, DAY) > TIMESTAMP_SUB(TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY), INTERVAL 2 DAY)
    ),
    recentPickTaskEvent AS (
    SELECT 
          facility_order_code,
          event_code,
          event_timestamp_utc,
          ROW_NUMBER() OVER (PARTITION BY facility_order_code ORDER BY event_timestamp_utc DESC) AS pick_task_row_num
        FROM \`${pickTaskTable}\`
        WHERE event_timestamp_utc > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 3 DAY)
    ),
    orders AS (
      SELECT * 
      FROM allOrders ao
      JOIN recentPickTaskEvent rpte
        ON ao.external_code = rpte.facility_order_code
      WHERE ao.row_num = 1 
        AND rpte.pick_task_row_num = 1
        AND ao.state_code = '3' 
        AND ao.sts_code = '0' 
        AND ao.ship_date_timestamp_utc IS NULL 
        AND ao.short_flag = '0' 
        AND UPPER(rpte.event_code) NOT IN ('COMPLETED', 'PICK_COMPLETE') 
        AND rpte.event_timestamp_utc > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 3 DAY)
      ORDER BY ao.__raw_message_ingestion_time
    ),
    orderDetails AS (
      SELECT DISTINCT 
        order_code, 
        order_id_line, 
        priority_code, 
        edit_date_time_local AS allocation, 
        ship_no_later_date_time_local AS ship_date, 
        qty_allocated, 
        sku, 
        storer_id, 
        order_line,
        state, 
        sts,
        ROW_NUMBER() OVER (PARTITION BY order_key, order_line ORDER BY od.__raw_message_ingestion_time DESC, od.edit_date_timestamp_utc DESC) AS detail_row_num
      FROM \`${wmsCustomerOrderDetailTable}\` od
      JOIN orders o ON od.order_key = o.order_code
      WHERE TIMESTAMP_TRUNC(od.edit_date_timestamp_utc, DAY) > TIMESTAMP_SUB(TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY), INTERVAL 2 DAY)
    ),
    orderOverview AS (
      SELECT 
        order_code, 
        priority_code, 
        ship_date, 
        COUNT(order_line) AS order_lines 
      FROM orderDetails 
      WHERE detail_row_num = 1 
      GROUP BY order_code, priority_code, ship_date
    ),
    skuOverview AS (
      SELECT 
        order_code, 
        sku, 
        storer_id, 
        MAX(allocation) AS allocation, 
        SUM(qty_allocated) AS qty_allocated 
      FROM orderDetails 
      WHERE detail_row_num = 1 
        AND sts = '0' 
        AND qty_allocated > 0
        AND sku = (SELECT sku FROM skuDetails) 
        AND storer_id = (SELECT size FROM skuDetails) 
      GROUP BY order_code, sku, storer_id
    )
    SELECT 
      orderOverview.order_code AS order_id, 
      orderOverview.priority_code AS priority, 
      skuOverview.allocation AS allocation_date, 
      orderOverview.ship_date, 
      orderOverview.order_lines, 
      skuOverview.qty_allocated, 
      (SELECT sku_code FROM skuDetails) AS skuId 
    FROM orderOverview 
    JOIN skuOverview 
    ON orderOverview.order_code = skuOverview.order_code 
    ORDER BY order_id;
  `;

    const sqlOptions: Query = {
      query: sql,
      params: {
        skuId,
      },
    };

    // execute the query using the clients BQ project to access the EDP project and return the results
    const [result] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!result || !result.length) {
      throw IctError.noContent();
    }

    return result;
  }

  /**
   * Retrieve the current and historical forcast information for a selected sku.
   * @param skuId Id for the sku associated with forecast data.
   * @returns Current and historical (series) data for a sku.
   */
  async getInventoryForecastSkuDetails(
    skuId: string,
  ): Promise<InventoryForecastSkuDetailsQueryResponse> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const skuFeaturesInferenceTable = bigQueryDb.getEdpFullTablePath(
      'gold_sku_features_inference',
    );

    const inventoryForecastHistoryTable = bigQueryDb.getEdpFullTablePath(
      'platinum_inventory_forecast_history',
    );

    const sql = `
      WITH forecastInferenceData AS (
        SELECT 
          ROUND(confidence_quantity_demand, 4) AS confidence_quantity,
          CASE 
            WHEN t_plus_1_quantity_demand > 0 THEN COALESCE(known_quantity_demand, 0) / t_plus_1_quantity_demand
            ELSE 0
          END AS known_demand_percentage,
          t_plus_1_quantity_demand AS known_demand_qty,
          ROUND(short_term_daily_quantity_demand, 4) AS short_term_daily_demand_quantity, 
          short_term_historical_data_span AS short_term_daily_days, 
          ROUND(long_term_daily_quantity_demand, 4) AS long_term_daily_demand_quantity, 
          long_term_historical_data_span AS long_term_daily_days, 
          ROUND(short_term_non_zero_quantity_demand * 100, 4) AS non_zero_demand_percentage, 
          ROUND(short_term_zero_quantity_demand_intermittency, 4) AS zero_demand_intermittency_days,
          ROW_NUMBER() OVER (
            PARTITION BY sku, TIMESTAMP_TRUNC(inference_datetime, DAY) 
            ORDER BY inference_datetime DESC
          ) AS row_num,
        FROM \`${skuFeaturesInferenceTable}\`
        WHERE TIMESTAMP_TRUNC(inference_datetime, DAY) >= TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY)
          AND sku = @skuId
      ),

      forecastData AS (
        SELECT * FROM forecastInferenceData WHERE row_num = 1
      ),

      seriesDates AS (
        SELECT DISTINCT future_actual_date 
        FROM \`${inventoryForecastHistoryTable}\`
        WHERE future_actual_date > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 365 DAY)
      ),

      skuData AS (
        SELECT * 
        FROM \`${inventoryForecastHistoryTable}\`
        WHERE sku = @skuId
      ),

      ungroupedSeriesData AS (
        SELECT 
          CAST(FORMAT_TIMESTAMP('%Y-%m-%dT%H:%M:%E3SZ', sd.future_actual_date) AS STRING) AS name,   
          COALESCE(s.actual_demand, 0) AS actual_demand,
          COALESCE(s.forecasted_demand, 0) AS forecasted_demand,
          COALESCE(s.demand_lower_range, 0) AS demand_lower_range,
          COALESCE(ROUND(s.demand_upper_range, 4), 0) AS demand_upper_range
        FROM seriesDates sd
        LEFT JOIN skuData s ON sd.future_actual_date = s.future_actual_date 
        ORDER BY sd.future_actual_date
      ),

      seriesGroupedData AS (
        SELECT 
          ARRAY_AGG(STRUCT(name, actual_demand AS value) ORDER BY name) AS actual_demand_series,
          ARRAY_AGG(STRUCT(name, forecasted_demand AS value) ORDER BY name) AS forecasted_demand_series,
          ARRAY_AGG(STRUCT(name, demand_lower_range AS value) ORDER BY name) AS demand_lower_range_series,
          ARRAY_AGG(STRUCT(name, demand_upper_range AS value) ORDER BY name) AS demand_upper_range_series
        FROM ungroupedSeriesData
      )

      SELECT * 
      FROM seriesGroupedData
      LEFT JOIN forecastData ON TRUE;
    `;

    const sqlOptions: Query = {
      query: sql,
      params: {
        skuId,
      },
    };

    // execute the query using the clients BQ project to access the EDP project and return the results
    const [result] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!result || !result.length) {
      throw IctError.noContent();
    }

    return result[0];
  }

  /**
   * Retrieve the timestamps for the last inference run and the last inventory snapshot.
   * @returns The two timestamps (UTC)
   */
  async getInventoryForecastTimestamps(): Promise<InventoryForecastTimestampResponse> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const wmsInventoryTable =
      bigQueryDb.getEdpFullTablePath('gold_wms_inventory');

    const skuFeaturesInferenceTable = bigQueryDb.getEdpFullTablePath(
      'gold_sku_features_inference',
    );

    const sql = `
      WITH lastRunInferenceDay AS (
        SELECT DISTINCT inference_datetime 
        FROM \`${skuFeaturesInferenceTable}\`
        -- Inference does not run every day so limit window to 5 days
        WHERE inference_datetime > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 5 DAY)
        ORDER BY inference_datetime DESC 
        LIMIT 1
      ),
      inventoryTimestamp AS (
        SELECT MAX(query_timestamp_utc) AS latestSnapshot
        FROM \`${wmsInventoryTable}\`
        WHERE query_timestamp_utc >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)
      )
      SELECT
        inference_datetime,
        (SELECT latestSnapshot FROM inventoryTimestamp) AS inventory_timestamp 
      FROM lastRunInferenceDay;
    `;

    const sqlOptions: Query = {
      query: sql,
    };

    // execute the query using the clients BQ project to access the EDP project and return the results
    const [result] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!result || !result.length) {
      throw IctError.noContent();
    }

    return result[0];
  }

  /**
   * Retrieve the list of inventory that was processsed by the AIML and the data associated with it.
   * @returns The list of skus including current inventory data and forecasted inventory.
   */
  async getInventoryForecastListing(
    params: InventoryForecastQueryParams,
  ): Promise<PaginatedResults<InventoryForecastListingQueryResponse[]>> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const inventoryZones = await this.configStore
      .findMostRelevantSettingForUser(
        this.context.userId,
        undefined,
        'inventory-zone-mapping',
      )
      .then(setting => {
        return setting?.value as InventoryForecastZoneMapping;
      });

    const forwardPick = inventoryZones?.forward_pick;
    const reserveStorage = inventoryZones?.reserve_storage;

    if (
      !inventoryZones ||
      !forwardPick ||
      !reserveStorage ||
      !forwardPick.length ||
      !reserveStorage.length
    ) {
      throw IctError.internalServerError(
        'Config setting or value for inventory zone mapping not found',
      );
    }

    const forward = forwardPick
      .map(item => `zone LIKE '%${item}%'`)
      .join(' OR ');
    const reserve = reserveStorage
      .map(item => `zone LIKE '%${item}%'`)
      .join(' OR ');

    const platinum_inventory_forecasting_list = bigQueryDb.getEdpFullTablePath(
      'platinum_inventory_forecasting_list',
    );

    const templateSQL = `
      WITH zoneQuantities AS (SELECT
      -- This value should sum the quantity of an SKU in all manual pick areas (configurable)
      CASE 
          WHEN ${reserve} THEN zone_quantity
      END AS reserve_storage,
      -- This value should sum the SKU quantity in the automated pick areas (configurable)
      CASE
          WHEN ${forward} THEN zone_quantity 
      END AS forward_pick,
      * FROM \`${platinum_inventory_forecasting_list}\`),

      inventoryForecastData AS (
        SELECT sku,

        -- This value should sum the quantity of an SKU in all manual pick areas (configurable)
        COALESCE(SUM(reserve_storage),0) AS reserveStorage, 

        -- This value should sum the SKU quantity in the automated pick areas (configurable)
        (COALESCE(SUM(forward_pick),0) - COALESCE(picks_complete, 0)) AS forwardPick,

        -- This value should sum the quantity of outstanding replenishment tasks for an item (see platinum_inventory_forecasting_list)
        MAX(pending_replenishment) AS pendingReplenishment,

        -- This value should sum the quantity of an SKU defined in WMS Order Replenishment with a 'PendPicks' task (see platinum_inventory_forecasting_list)
        MAX(pending_picks) AS pendingPicks,

        -- This value should sum the quantity of an SKU defined in order lines that have not yet been completed 
        MAX(allocated_orders) AS allocatedOrders,

        -- Total quantity of an item projected to remain in inventory. 
        -- This value should combine current inventory in Forward Pick and Pending Replenishments minus the Allocated Orders
        (COALESCE(SUM(forward_pick),0) - COALESCE(picks_complete,0) + MAX(pending_replenishment)) - (MAX(allocated_orders) + MAX(pending_picks)) AS projectedForwardPick,

        -- This value should sum the quantity of daily replenishments for a SKU each day with a completed replenishment task within the last 30 
        -- calendar days and then average that sum over the total days of replenishment activity during that 30 day period (see platinum_inventory_forecasting_list)
        MAX(average_replenishment) AS averageReplenishment,

        -- This value should sum the quantity of a SKU defined in completed order lines for each day with a completed order line 
        -- within the last 30 calendar days and then average that sum over the total days of order activity during that 30 day period (see platinum_inventory_forecasting_list)
        CAST(MAX(average_demand) AS INT64) AS averageDemand,

        -- Total order demand (by quantity) forecasted by the SKU Demand Forecast model for tomorrow, provided by the t_plus_1_quantity_demand field 
        -- Only display if the inference has been run for the current day. (see platinum_inventory_forecasting_list)
        demand_tomorrow AS demandTomorrow,

        -- The value provided from the known_quantity_demand is in units or eaches. This value is divided by the t_plus_1_quantity_demand to determine t
        -- he known demand % required for this column. Only display if the inference has been run for the current day. (see platinum_inventory_forecasting_list)
        known_demand AS knownDemand,

        -- This value should combine projected inventory from today minus the t_plus_1_quantity_demand (Demand Tomorrow) value.
        -- Only display if the inference has been run for the current day.
        CASE WHEN show_data = true 
            THEN ((COALESCE(SUM(forward_pick),0) - COALESCE(picks_complete,0) + MAX(pending_replenishment)) - 
                (MAX(allocated_orders) + MAX(pending_picks) + COALESCE(demand_tomorrow, 0))) 
            ELSE NULL 
        END AS forwardPickTomorrow,

        -- This value is provided by the t_plus_2_quantity_demand field. Only display if the inference has been run for the current day. (see platinum_inventory_forecasting_list)
        two_day_demand AS twoDayDemand,

        -- This value should combine projected inventory from today minus the t_plus_1_quantity_demand (Demand Tomorrow) 
        -- and the t_plus_2_quantity_demand (Two Day Demand) value. Only display if the inference has been run for the current day. 
        CASE WHEN show_data = true 
            THEN ((COALESCE(SUM(forward_pick),0) - COALESCE(picks_complete,0) + MAX(pending_replenishment)) - 
                (MAX(allocated_orders) + MAX(pending_picks) + COALESCE(demand_tomorrow, 0) + COALESCE(two_day_demand, 0))) 
            ELSE NULL 
        END AS twoDayForwardPick
    FROM zoneQuantities
    GROUP BY sku, picks_complete, demandTomorrow, knownDemand, twoDayDemand, show_data
    )
    `;

    // these are the only allowed column names that can be sorted/filtered by through input (untrusted) data
    const allowedColumnNames = [
      'sku',
      'reserveStorage',
      'forwardPick',
      'pendingReplenishment',
      'pendingPicks',
      'allocatedOrders',
      'projectedForwardPick',
      'averageReplenishment',
      'averageDemand',
      'demandTomorrow',
      'knownDemand',
      'forwardPickTomorrow',
      'twoDayDemand',
      'twoDayForwardPick',
    ];

    const searchableColumns = ['sku'];

    const sql: SortingSelectQueryParams = {
      selectFromTable: 'inventoryForecastData',
      allowedColumnNames,
      searchableColumns,
      searchString: params.searchString,
      cteSubquery: templateSQL,
      sortFields: params.sortField,
      page: params.page,
      limit: params.limit,
    };

    // execute the query using the clients BQ project to access the EDP project and return the results
    const bqQueryGenerator = new BigQueryGenerator();
    const results: PaginatedResults<InventoryForecastListingQueryResponse[]> =
      await SQLQueryGenerator.paginatedQuery<InventoryForecastListingQueryResponse>(
        bqQueryGenerator,
        bigQueryDb,
        sql,
        params.filters,
        params.filterParams,
      );

    if (!results.list.length) {
      throw IctError.noContent();
    }

    return results;
  }

  /**
   * Retrieve the time the last submitted report was generated by the end-user.
   * @returns A single timestamp (UTC)
   */
  async getLastUploadTime(): Promise<Date> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const silver_known_order =
      bigQueryDb.getEdpFullTablePath('silver_known_order');

    const sql = `
      SELECT created_date_time_local as date
        FROM \`${silver_known_order}\` 
        WHERE TIMESTAMP_TRUNC(__raw_message_ingestion_time, DAY) > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 14 DAY) 
        ORDER BY created_date_time_local DESC
        LIMIT 1
    `;

    const sqlOptions: Query = {
      query: sql,
    };

    // execute the query using the clients BQ project to access the EDP project and return the results
    const [result] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    // It's acceptable to not have a result for today.
    return result[0]?.date?.value;
  }

  /**
   * Retrieve historic data describing replenishment activities.
   * @returns InventoryReplenishmentQueryResponse
   */
  async getInventoryReplenishmentDetails(
    startDate: Date,
    endDate: Date,
  ): Promise<InventoryReplenishmentQueryResponse> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const timezone = await this.configStore
      .findMostRelevantSettingForUser(
        this.context.userId,
        undefined,
        'site-time-zone',
      )
      .then(setting => {
        return setting?.value?.toString();
      });

    const shiftTimes: {[key: string]: string} = await this.configStore
      .findMostRelevantSettingForUser(
        this.context.userId,
        undefined,
        'shift-start-and-end-times',
      )
      .then(setting => {
        return setting?.value as unknown as {[key: string]: string};
      });

    let shift_sql: string = '';

    const firstStart = shiftTimes.first_startTime;
    let firstEnd = shiftTimes.first_endTime;
    const secondStart = shiftTimes.second_startTime;
    let secondEnd = shiftTimes.second_endTime;
    const thirdStart = shiftTimes.third_startTime;
    let thirdEnd = shiftTimes.third_endTime;

    if (shiftTimes) {
      if (
        (firstStart && !firstEnd) ||
        (secondStart && !secondEnd) ||
        (thirdStart && !thirdEnd) ||
        (secondStart && !firstStart) ||
        (firstEnd && !firstStart) ||
        (secondEnd && !secondStart) ||
        (thirdEnd && !thirdStart)
      ) {
        throw IctError.unprocessableRequest(
          'Shift assignments are misconfigured.',
        );
      }

      if (firstStart) {
        if (firstEnd === '00:00') {
          firstEnd = '23:59';
        }

        shift_sql = `
          CASE 
            WHEN FORMAT_DATETIME('%H:%M', completion_time) BETWEEN '${this.formatTime(firstStart)}' AND '${this.formatTime(firstEnd)}' THEN 'first'`;
        if (secondStart) {
          if (secondEnd === '00:00') {
            secondEnd = '23:59';
          }
          shift_sql = shift_sql.concat(`
            WHEN FORMAT_DATETIME('%H:%M', completion_time) BETWEEN '${this.formatTime(secondStart)}' AND '${this.formatTime(secondEnd)}' THEN 'second'`);
        }
        if (thirdStart) {
          if (thirdEnd === '00:00') {
            thirdEnd = '23:59';
          }
          shift_sql = shift_sql.concat(`
          WHEN FORMAT_DATETIME('%H:%M', completion_time) BETWEEN '${this.formatTime(thirdStart)}' AND '${this.formatTime(thirdEnd)}' THEN 'third'`);
        }
        shift_sql = shift_sql.concat('END AS shifts');
      }
    }
    if (!firstStart) {
      // If there is no shift times defined, throw everything into one shift for happy query and mapping.
      shift_sql = `
      CASE WHEN FORMAT_DATETIME('%H:%M', completion_time) BETWEEN '00:00' AND '23:59' THEN 'first' END AS shifts`;
    }

    // Convert to the local timezone
    const utcStartDateTime = DateTime.fromJSDate(startDate, {zone: 'utc'});
    const utcEndDateTime = DateTime.fromJSDate(endDate, {zone: 'utc'});

    // Format the local time without the timezone offset
    const localStartDateTime = utcStartDateTime
      .setZone(timezone)
      .toFormat('yyyy-MM-dd');

    // Subtract one day from range as thee trunc will encapsulate an extra day:
    // 2025-01-22T04..-> 2025-01-22 (all records for the day) -> 2025-01-29T04.. -> 2025-01-29 (all records for the day) ->
    // = 8 days of data. Addressing it here also works better for the
    // UNNEST(GENERATE_DATE_ARRAY('${localStartDateTime}', '${localEndDateTime}')) AS date.
    // Remove the current day since we want historical averages and currently accumalating day throws that off.
    const localEndDateTime = utcEndDateTime
      .setZone(timezone)
      .minus({days: 1})
      .toFormat('yyyy-MM-dd');

    const gold_wms_order_replenishment = bigQueryDb.getEdpFullTablePath(
      'gold_wms_order_replenishment',
    );

    const gold_wms_customer_order = bigQueryDb.getEdpFullTablePath(
      'gold_wms_customer_order',
    );

    let sql = `
      WITH replenishment_selection AS (
        SELECT 
          query_timestamp_utc, 
          query_date_time_local, 
          task_type_code, 
          task_id 
        FROM \`${gold_wms_order_replenishment}\` 
        WHERE 
          query_timestamp_utc BETWEEN TIMESTAMP_SUB(TIMESTAMP_TRUNC(@startDate, DAY), INTERVAL 1 DAY) 
          AND TIMESTAMP_TRUNC(@endDate, DAY)
          AND DATETIME_TRUNC(query_date_time_local, DAY) >= DATETIME('${localStartDateTime}') 
          AND DATETIME_TRUNC(query_date_time_local, DAY) <= DATETIME('${localEndDateTime}')
      ),

      adjusted_times AS (
        SELECT 
          MAX(query_date_time_local) AS completion_time, 
          task_type_code, 
          task_id 
        FROM replenishment_selection
        GROUP BY task_type_code, task_id
      ),

      daily_activities AS (
        SELECT 
          COUNT(task_id) AS task_count, 
          task_type_code, 
          DATETIME_TRUNC(completion_time, DAY) AS day 
        FROM adjusted_times 
        GROUP BY day, task_type_code 
        ORDER BY day
      ),

      averages AS (
        SELECT 
          ROUND(COUNT(task_id) / COUNT(DISTINCT DATETIME_TRUNC(completion_time, DAY))) AS avg_count, 
          task_type_code 
        FROM adjusted_times 
        GROUP BY task_type_code
      ),

      shift_assignments AS (
        SELECT
          DATETIME_TRUNC(completion_time, DAY) AS day,
          task_id,
          FORMAT_DATETIME('%H:%M', completion_time) AS time, `;
    if (shiftTimes) {
      sql = sql.concat(shift_sql);
    }

    sql = sql.concat(
      ` FROM adjusted_times 
        WHERE task_type_code = 'PendReplenish'
      ),

      shift_break_down AS (
        SELECT 
          COUNT(task_id) AS per_shift, 
          day, 
          shifts 
        FROM shift_assignments 
        WHERE shifts IS NOT NULL 
        GROUP BY day, shifts 
        ORDER BY day, shifts
      ),

      order_info AS (
        SELECT order_code,
        edit_date_time_local,
        sts_code
        FROM \`${gold_wms_customer_order}\`
        WHERE  
          edit_date_timestamp_utc BETWEEN TIMESTAMP_SUB(TIMESTAMP_TRUNC(@startDate, DAY), INTERVAL 1 DAY) 
          AND TIMESTAMP_TRUNC(@endDate, DAY)
          AND DATETIME_TRUNC(edit_date_time_local, DAY) >= DATETIME('${localStartDateTime}') 
          AND DATETIME_TRUNC(edit_date_time_local, DAY) <= DATETIME('${localEndDateTime}')
          AND ship_date_timestamp_utc IS NULL
      ),

      ordersPickComplete AS (
        SELECT 
          order_code, 
          MAX(edit_date_time_local) AS completeTime
        FROM order_info
        WHERE sts_code = '${WMSCustomerOrderStatus.Picked}'
        GROUP BY order_code
      ),

      orderStartTime AS (
        SELECT 
          o.order_code, 
          MIN(o.edit_date_time_local) AS startTime
        FROM order_info o 
        INNER JOIN ordersPickComplete s ON o.order_code = s.order_code
        WHERE o.sts_code = '${WMSCustomerOrderStatus.Open}'
        GROUP BY o.order_code
      ),

      cycleTimes AS (
        SELECT 
          o.order_code, 
          s.completeTime, 
          o.startTime,
          GREATEST(TIMESTAMP_DIFF(TIMESTAMP(s.completeTime), TIMESTAMP(o.startTime), MINUTE), 0) AS cycleTimeMinutes
        FROM orderStartTime o 
        INNER JOIN ordersPickComplete s ON o.order_code = s.order_code
      ),

      daily_cycle_time AS (
        SELECT
          ROUND(AVG(cycleTimeMinutes)) AS orderCycleTimeMinutes,
          DATETIME_TRUNC(completeTime, DAY) AS day
        FROM cycleTimes 
        GROUP BY day
      ),

      date_range AS (
        SELECT
          date
        FROM
          UNNEST(GENERATE_DATE_ARRAY('${localStartDateTime}', '${localEndDateTime}')) AS date
      ),

      daily_pending_replenishments AS (
        SELECT task_count, day 
        FROM daily_activities 
        WHERE task_type_code = 'PendReplenish'
      ),

      daily_pending_orders AS (
        SELECT task_count, day 
        FROM daily_activities 
        WHERE task_type_code = 'PendPicks'
      ),

      first_shift AS (
        SELECT * 
        FROM shift_break_down 
        WHERE shifts = 'first'
      )`,
    );

    if (secondStart) {
      sql = sql.concat(`
      , second_shift AS (
        SELECT * 
        FROM shift_break_down 
        WHERE shifts = 'second'
        )`);
    }

    if (thirdStart) {
      sql = sql.concat(`
      , third_shift AS (
        SELECT * 
        FROM shift_break_down 
        WHERE shifts = 'third'
        )`);
    }

    sql = sql.concat(`
      SELECT 
        (SELECT avg_count FROM averages WHERE task_type_code = 'PendReplenish') AS average_replenishments,
        (SELECT avg_count FROM averages WHERE task_type_code = 'PendPicks') AS average_pending_orders,
        (SELECT ROUND(AVG(cycleTimeMinutes)) FROM cycleTimes) AS average_cycle_times,
        (SELECT ARRAY(
          SELECT AS STRUCT COALESCE(task_count, 0) AS value, date AS name 
          FROM daily_pending_replenishments 
          FULL JOIN date_range ON day = date
          ORDER BY date
        ) LIMIT 1) AS daily_replenishments,
        (SELECT ARRAY(
          SELECT AS STRUCT COALESCE(task_count, 0) AS value, date AS name 
          FROM daily_pending_orders  
          FULL JOIN date_range ON day = date
          ORDER BY date
        ) LIMIT 1) AS daily_pending_orders,
        (SELECT ARRAY(
          SELECT AS STRUCT COALESCE(orderCycleTimeMinutes, 0) AS value, date AS name
          FROM daily_cycle_time 
          FULL JOIN date_range ON day = date
          ORDER BY date
        ) LIMIT 1) AS daily_cycle_times,
        (SELECT ARRAY(
          SELECT AS STRUCT COALESCE(per_shift, 0) AS value, date AS name 
          FROM first_shift 
          FULL JOIN date_range ON day = date
          ORDER BY date
        ) LIMIT 1) AS first_shift
         `);
    if (secondStart) {
      sql = sql.concat(`
         ,(SELECT ARRAY(
          SELECT AS STRUCT COALESCE(per_shift, 0) AS value, date AS name 
          FROM second_shift 
          FULL JOIN date_range ON day = date
          ORDER BY date
        ) LIMIT 1) AS second_shift`);
    }

    if (thirdStart) {
      sql = sql.concat(`
        ,(SELECT ARRAY(
          SELECT AS STRUCT COALESCE(per_shift, 0) AS value, date AS name 
          FROM third_shift 
          FULL JOIN date_range ON day = date
          ORDER BY date
        ) LIMIT 1) AS third_shift`);
    }
    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    // execute the query using the clients BQ project to access the EDP project and return the results
    const [result] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!result.length || !result[0]) {
      throw IctError.noContent();
    }
    result[0].shift_times = shiftTimes;
    return result[0];
  }

  /**
   * Retrieve task type data for replenishment activities.
   * @param startDate Start date for the data range
   * @param endDate End date for the data range
   * @returns Object with task type data matching the ChartTimeSeriesData structure
   */
  async getInventoryReplenishmentTaskTypeData(
    startDate: Date,
    endDate: Date,
  ): Promise<TaskTypeData> {
    const bigQueryDb = this.dbProvider.bigQuery;

    const timezone = await this.configStore
      .findMostRelevantSettingForUser(
        this.context.userId,
        undefined,
        'site-time-zone',
      )
      .then(setting => {
        return setting?.value?.toString();
      });

    // Convert to the local timezone
    const utcStartDateTime = DateTime.fromJSDate(startDate, {zone: 'utc'});
    const utcEndDateTime = DateTime.fromJSDate(endDate, {zone: 'utc'});

    // Format the local time without the timezone offset
    const localStartDateTime = utcStartDateTime
      .setZone(timezone)
      .toFormat('yyyy-MM-dd');

    // Subtract one day from range for consistent data handling
    const localEndDateTime = utcEndDateTime
      .setZone(timezone)
      .minus({days: 1})
      .toFormat('yyyy-MM-dd');

    const gold_wms_order_replenishment = bigQueryDb.getEdpFullTablePath(
      'gold_wms_order_replenishment',
    );

    const sql = `
      WITH replenishment_data AS (
        SELECT 
          query_timestamp_utc, 
          query_date_time_local, 
          replen_type,
          task_id 
        FROM \`${gold_wms_order_replenishment}\` 
        WHERE 
          query_timestamp_utc BETWEEN TIMESTAMP_SUB(TIMESTAMP_TRUNC(@startDate, DAY), INTERVAL 1 DAY) 
          AND TIMESTAMP_TRUNC(@endDate, DAY)
          AND DATETIME_TRUNC(query_date_time_local, DAY) >= DATETIME('${localStartDateTime}') 
          AND DATETIME_TRUNC(query_date_time_local, DAY) <= DATETIME('${localEndDateTime}')
          AND replen_type IN (3, 4, 5)
      ),

      task_completion AS (
        SELECT 
          MAX(query_date_time_local) AS completion_time, 
          replen_type, 
          task_id 
        FROM replenishment_data
        GROUP BY replen_type, task_id
      ),

      daily_task_types AS (
        SELECT 
          COUNT(task_id) AS task_count, 
          replen_type, 
          DATETIME_TRUNC(completion_time, DAY) AS day 
        FROM task_completion 
        GROUP BY day, replen_type 
        ORDER BY day
      ),

      date_range AS (
        SELECT
          date
        FROM
          UNNEST(GENERATE_DATE_ARRAY('${localStartDateTime}', '${localEndDateTime}')) AS date
      ),

      demand_tasks AS (
        SELECT task_count, day 
        FROM daily_task_types 
        WHERE replen_type = 5  -- TASKDEMAND
      ),

      topoff_tasks AS (
        SELECT task_count, day 
        FROM daily_task_types 
        WHERE replen_type = 4  -- TASKTOPOFF
      ),

      relocation_tasks AS (
        SELECT task_count, day 
        FROM daily_task_types 
        WHERE replen_type = 3  -- TASKRELOC
      )

      SELECT ARRAY(
        SELECT AS STRUCT
          ARRAY(
            SELECT AS STRUCT 
              COALESCE(task_count, 0) AS value, 
              CAST(date AS STRING) AS name 
            FROM demand_tasks 
            FULL JOIN date_range ON day = date
            ORDER BY date
          ) AS demand,
          ARRAY(
            SELECT AS STRUCT 
              COALESCE(task_count, 0) AS value, 
              CAST(date AS STRING) AS name 
            FROM topoff_tasks 
            FULL JOIN date_range ON day = date
            ORDER BY date
          ) AS topOff,
          ARRAY(
            SELECT AS STRUCT 
              COALESCE(task_count, 0) AS value, 
              CAST(date AS STRING) AS name 
            FROM relocation_tasks 
            FULL JOIN date_range ON day = date
            ORDER BY date
          ) AS relocation
      ) AS taskTypeData
    `;

    const sqlOptions: Query = {
      query: sql,
      params: {
        startDate,
        endDate,
      },
    };

    // Execute the query
    const [result] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!result.length || !result[0]) {
      throw IctError.noContent();
    }

    return result[0];
  }

  /**
   * Retreives the known report generation timestamp and count of records for the
   * associated times in the known order and known order line tables.
   * @returns The local date time, known order record count, and known order line record count.
   */
  async getInventoryUploadRecentActivity(): Promise<
    InventoryUploadRecentActivityQueryResponse[]
  > {
    const bigQueryDb = this.dbProvider.bigQuery;

    const silver_known_order =
      bigQueryDb.getEdpFullTablePath('silver_known_order');

    const silver_known_order_line = bigQueryDb.getEdpFullTablePath(
      'silver_known_order_line',
    );

    const sql = `
        WITH known_order AS (
          SELECT DISTINCT(created_date_time_local), 
          COUNT(*) AS records 
          FROM \`${silver_known_order}\` 
          WHERE TIMESTAMP_TRUNC(__raw_message_ingestion_time, DAY) 
            >= TIMESTAMP_SUB(TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY), INTERVAL 7 DAY) 
          GROUP BY created_date_time_local),

        known_order_line AS (
          SELECT DISTINCT(created_date_time_local), 
          COUNT(*) AS records 
          FROM \`${silver_known_order_line}\` 
          WHERE TIMESTAMP_TRUNC(__raw_message_ingestion_time, DAY) 
            >= TIMESTAMP_SUB(TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY), INTERVAL 7 DAY) 
          GROUP BY created_date_time_local)

        -- Limit to current day and previous 6 days based on "local" CST
        SELECT 
          ko.created_date_time_local, 
          ko.records AS known_order_count, 
          kol.records AS known_order_line_count 
          FROM known_order_line kol JOIN known_order ko 
            ON kol.created_date_time_local = ko.created_date_time_local 
          WHERE ko.created_date_time_local 
            > DATETIME_SUB(DATETIME_TRUNC(CURRENT_DATETIME('America/Chicago'), DAY), INTERVAL 6 DAY) 
          ORDER BY created_date_time_local DESC 
          LIMIT 10;
    `;

    const sqlOptions: Query = {
      query: sql,
    };

    // execute the query using the clients BQ project to access the EDP project and return the results
    const [result] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    if (!result || !result.length) {
      throw IctError.noContent();
    }
    return result;
  }

  private formatTime(time: string): string {
    const [hours, minutes] = time.split(':');
    const formattedHours = hours.padStart(2, '0');
    return `${formattedHours}:${minutes}`;
  }

  /**
   * Retrieves data for all available bin locations
   * Uses silver data for process flow
   */
  async getInventoryBinLocations(
    aisle: string,
    level: string,
  ): Promise<InventoryBinLocationQueryResponse[]> {
    const bigQueryDb = this.dbProvider.bigQuery;
    const wmsInventory = bigQueryDb.getEdpFullTablePath('silver_wms_inventory');
    const multishuttleMovement = bigQueryDb.getEdpFullTablePath(
      'silver_multishuttle_movement',
    );
    const pickEvents = bigQueryDb.getEdpFullTablePath('silver_pick');

    // Get latest snapshot from wms_inventory
    // After getting inventory dump, use picking data to determine quantity removed
    // The most recent movement into a bin location tells us where each handling unit is
    const sql = `
      WITH
        inventory_timestamp AS (
          SELECT MAX(query_timestamp_utc) AS latestSnapshot
          FROM \`${wmsInventory}\` 
          WHERE query_timestamp_utc >= TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY)
        ),
        inventory_dump AS (
          SELECT DISTINCT
            location_id as bin_location,
            container_id as handling_unit_code,
            quantity as quantity,
            sku_style || sku_size as sku_code,
          FROM \`${wmsInventory}\` 
          WHERE query_timestamp_utc > TIMESTAMP_SUB(CURRENT_TIMESTAMP, INTERVAL 1 DAY)
            AND query_timestamp_utc = (SELECT latestSnapshot FROM inventory_timestamp)
            AND REGEXP_CONTAINS(location_id, r'MS[0-9]{12}') -- filter results to only get bin locations at the given aisle and level
            AND SUBSTRING(location_id, 3, 2) = @aisle
            AND SUBSTRING(location_id, 9, 2) = @level
        ),
        -- get quantity picked for each handling unit
        deduplicated_pick_events AS (
          SELECT DISTINCT
            source_handling_unit_code,
            sku_code,
            picked_qty
          FROM \`${pickEvents}\` 
          WHERE
            event_timestamp_utc > (SELECT latestSnapshot FROM inventory_timestamp) -- only get picks since last inventory dump
            AND picked_qty > 0
            -- do we need to filter by workflow_code = "PICK" here?
        ),
        pick_events AS (
          SELECT DISTINCT
            source_handling_unit_code as handling_unit_code,
            sku_code as sku_code,
            SUM(picked_qty) as picked_quantity,
          FROM deduplicated_pick_events
          GROUP BY source_handling_unit_code, sku_code
        ),
        updated_inventory AS (
          SELECT
            bin_location as bin_location,
            o.quantity - IFNULL(n.picked_quantity, 0) as quantity,
            o.sku_code as sku_code,
            o.handling_unit_code as handling_unit_code
          FROM inventory_dump o
          LEFT JOIN pick_events n
            ON n.handling_unit_code = o.handling_unit_code
            AND n.sku_code = o.sku_code
        ),
        recent_locations AS (
          -- get the most recent movement into a bin location for each handling unit
          -- this is in case a handling unit has moved since the inventory dump
          SELECT DISTINCT
            destination_location_code as bin_location,
            handling_unit_code,
            sku_code
          FROM
          (
            SELECT
              destination_location_code,
              handling_unit_code,
              sku_code,
              RANK() OVER(PARTITION BY handling_unit_code ORDER BY movement_end_timestamp_utc DESC) AS rnk
              FROM \`${multishuttleMovement}\` 
              WHERE
                movement_end_timestamp_utc = (SELECT latestSnapshot FROM inventory_timestamp)
                AND REGEXP_CONTAINS(destination_location_code, r'MS[0-9]{12}')
          )
          WHERE rnk = 1
        ),
        updated_locations AS (
          SELECT
            IFNULL(n.bin_location, o.bin_location) as bin_location,
            quantity as quantity,
            o.sku_code as sku_code,
            o.handling_unit_code
          FROM updated_inventory o
          LEFT JOIN recent_locations n
            ON n.handling_unit_code = o.handling_unit_code
            AND n.sku_code = o.sku_code
        ),
        -- ensure we include bin locations with no handling units in them
        all_bin_locations AS (
          SELECT DISTINCT * FROM (
            SELECT DISTINCT
              source_location_code as bin_location,
            FROM \`${multishuttleMovement}\` 
            WHERE
              REGEXP_CONTAINS(source_location_code, r'MS[0-9]{12}')
              AND SUBSTRING(source_location_code, 3, 2) = @aisle
              AND SUBSTRING(source_location_code, 9, 2) = @level
            
            UNION ALL

            SELECT DISTINCT
              destination_location_code as bin_location,
            FROM \`${multishuttleMovement}\` 
            WHERE
              REGEXP_CONTAINS(destination_location_code, r'MS[0-9]{12}')
              AND SUBSTRING(destination_location_code, 3, 2) = @aisle
              AND SUBSTRING(destination_location_code, 9, 2) = @level
          )
        )

        SELECT
          -- remove the last character since we want all items in the bin location, not just a single position in the raster
          LEFT(a.bin_location, 13) as binLocation,
          IFNULL(quantity, 0) as quantity,
          sku_code as skuCode, -- NULL if no skus in the bin location
          CASE
            WHEN SUBSTRING(a.bin_location, 5, 1) = "1" THEN "Left" 
            ELSE "Right"
          END as locationSide,
          CASE
            WHEN REGEXP_CONTAINS(handling_unit_code, 'T[0-9]{7}') THEN "GTP Pick Tote"
            WHEN REGEXP_CONTAINS(handling_unit_code, 'D[0-9]{12}') THEN "Donor Tote"
            WHEN REGEXP_CONTAINS(handling_unit_code, 'QT[0-9]{12}') THEN "Order Tote"
            WHEN REGEXP_CONTAINS(handling_unit_code, 'EMS-[0-9]{5}') THEN "Unknown"
            WHEN handling_unit_code IS NULL THEN NULL
            ELSE "Carton"
          END as containerType
        FROM all_bin_locations a
        LEFT JOIN updated_locations l
          ON l.bin_location = a.bin_location
          `;

    const sqlOptions: Query = {
      query: sql,
      params: {
        aisle,
        level,
      },
    };

    const [result] = await bigQueryDb.executeMonitoredJob(sqlOptions);

    return result;
  }
}
