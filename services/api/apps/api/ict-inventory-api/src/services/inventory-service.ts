import {
  ApiResponseArray,
  DiService,
  IctError,
  PaginatedResults,
  SortField,
} from 'ict-api-foundations';
import {DateTime} from 'luxon';
import {InventoryAccuracy} from '../defs/inventory-accuracy-def.ts';
import {InventoryAreaFilterDefinition} from '../defs/inventory-area-filter-def.ts';
import {
  InventoryHighImpactSKU,
  InventoryHighImpactSKUListServiceParams,
  InventoryHighImpactSkuPaginationInfo,
} from '../defs/inventory-high-impact-sku-def.ts';
import {InventoryPerformanceSeriesData} from '../defs/inventory-performance-series-def.ts';
import {
  InventorySkuQueryServiceParams,
  PostFacilityInventorySkusListResponse,
  PostWmsInventorySkusListResponse,
} from '../defs/inventory-sku-def.ts';
import {
  FlattenedInventoryContainerEventsDataItem,
  InventoryContainerEventsQueryServiceParams,
  InventoryContainerEventsQueryStoreParams,
} from '../defs/inventory-container-events-list-def.ts';
import {InventoryStorageUtilization} from '../defs/inventory-storage-utilization-def.ts';
import {InventoryHandlingUnitsTrayedData} from '../defs/inventory-total-handling-units-trayed-def.ts';
import {InventoryStore} from '../stores/inventory-store.ts';
import {InventoryAdvicesInProgressData} from '../defs/inventory-advices-in-progress-def.ts';
import {AdvicesOutstandingData} from '../defs/inventory-advices-outstanding-def.ts';
import {AdvicesFinishedData} from '../defs/inventory-finished-advices-def.ts';
import {AdvicesDetailsQueryResponse} from '../defs/inventory-advices-details-drawer-def.ts';
import {AdvicesCycleTimeData} from '../defs/inventory-advices-cycle-time-def.ts';
import {AdvicesListData} from '../defs/inventory-advices-list-def.ts';
import {InventoryStockDistributionOverData} from '../defs/inventory-stock-distribution-over-def.ts';
import {InventoryStockDistributionUnderData} from '../defs/inventory-stock-distribution-under-def.ts';
import {InventoryStockDistributionNoData} from '../defs/inventory-stock-distribution-no-def.ts';
import {InventoryStockDistributionAtData} from '../defs/inventory-distribution-stat-history-def.ts';
import {
  InventoryForecastDataAnalysisTimestamp,
  InventoryForecastListing,
  InventoryForecastListingData,
  InventoryForecastSkuLocationAreas,
  InventoryForecastSkuLocationByArea,
  InventoryForecastSkuLocationDetails,
  InventoryForecastSkuOrders,
  InventorySkuForecastLocationData,
  InventorySkuOrdersResponse,
  InventoryForecastTimestampResponse,
  InventoryForecastListingQueryResponse,
  InventorySkuForecastDetails,
  InventoryForecastSkuDetailsQueryResponse,
  InventoryForecastListingParams,
  InventoryForecastQueryParams,
  InventoryForecastListingConfig,
} from '../defs/inventory-forecast-def.ts';
import {
  FlattenedInventoryContainerData,
  PostInventoryContainersListResponse,
  InventoryContainerData,
  InventoryContainerQueryServiceParams,
} from '../defs/inventory-container-def.ts';
import {DataFlowManager} from '../utils/data-flow-manager.ts';
import {
  InventoryContainerEventsKpiContract,
  InventoryContainerEventsKpiData,
  InventoryContainerEventsKpiResponse,
} from '../defs/inventory-container-events-kpi-def.ts';
import {
  InventoryReplenishmentDetails,
  ShiftData,
  TaskTypeData,
} from '../defs/inventory-replenishment-details-def.ts';
import {
  InventoryUploadRecentActivity,
  InventoryUploadRecentActivityQueryResponse,
} from '../defs/inventory-upload-def.ts';
// eslint-disable-next-line import/no-cycle
import {InventoryBinLocation} from '../defs/inventory-bin-locations-def.ts';
import {
  DistributionType,
  InventoryDistributionStatusResponse,
} from '../defs/inventory-distribution-status-def.ts';

/**
 * Service that handles applying business logic for Inventory to store queries.
 */
@DiService()
export class InventoryService {
  constructor(private inventoryStore: InventoryStore) {}

  async getInventoryWmsSkuListAsync(
    params: InventorySkuQueryServiceParams,
  ): Promise<PostWmsInventorySkusListResponse> {
    const sortFields: SortField[] = params.sortFields ?? [];
    const limit: number = params.limit ?? 25;
    const page: number = params.page ?? 1;

    const results = await this.inventoryStore.getInventoryWmsSkuListAsync({
      filters: params.reqFilter,
      sortFields,
      limit,
      page,
      searchString: params.searchString,
    });

    const filtersContract: PostWmsInventorySkusListResponse = {
      data: results.list,
      metadata: {
        page,
        limit,
        totalResults: results.totalResults,
      },
    };

    return filtersContract;
  }

  async getInventoryFacilitySkuListAsync(
    params: InventorySkuQueryServiceParams,
  ): Promise<PostFacilityInventorySkusListResponse> {
    const sortFields: SortField[] = params.sortFields ?? [];
    const limit: number = params.limit ?? 25;
    const page: number = params.page ?? 1;

    const results = await this.inventoryStore.getInventoryFacilitySkuListAsync({
      filters: params.reqFilter,
      sortFields,
      limit,
      page,
      searchString: params.searchString,
    });

    const filtersContract: PostFacilityInventorySkusListResponse = {
      data: results.list,
      metadata: {
        page,
        limit,
        totalResults: results.totalResults,
      },
    };

    return filtersContract;
  }

  /**
   * Attempt to query the database for specific advices details.
   * @param adviceId The ID of the advice to get details for.
   * @returns Promise that resolves to an AdvicesDetailsQueryResponse object containing the details of the given advice.
   */
  async getAdviceDetails(
    adviceId: string,
  ): Promise<AdvicesDetailsQueryResponse[]> {
    return await this.inventoryStore.getAdviceDetails(adviceId);
  }

  async getAdvicesFinished(): Promise<AdvicesFinishedData> {
    const result = await this.inventoryStore.getAdvicesFinished();
    return {
      finishedAdvices: result.finished_advices,
    };
  }

  async getAdvicesList(): Promise<AdvicesListData[]> {
    const result = await this.inventoryStore.getAdvicesList();
    return result.map(r => ({
      adviceId: r.advice_id,
      adviceStatus: r.advice_status,
      ownerId: r.owner_id,
      adviceType: r.advice_type,
      supplierId: r.supplier_id,
      createdTime: r.created_time.value,
      startTime: r.start_time?.value ?? null,
      finishedTime: r.finished_time?.value ?? null,
      adviceLines: r.advice_lines,
      deliveredLines: r.delivered_lines,
      overdeliveredLines: r.overdelivered_lines,
      underdeliveredLines: r.underdelivered_lines,
    }));
  }

  async getAdvicesCycleTime(): Promise<AdvicesCycleTimeData> {
    const result = await this.inventoryStore.getAdvicesCycleTime();
    return {
      cycleTime: result.cycle_time,
    };
  }

  /**
   * Attempt to query the database for Inventory area filter data.
   * @returns Data Contract that contains the AreaFilterDefinition with area filter string.
   */
  async getInventoryAreaAsync(): Promise<
    InventoryAreaFilterDefinition | undefined
  > {
    return this.inventoryStore.getInventoryAreaFilterAsync();
  }

  /**
   * Attempts to query the database for average inventory accuracy within a given date range.
   * @param startDate Start date for the date range.
   * @param endDate End date for the date range.
   * @returns Promise that resolves to an InventoryAccuracy object, or undefined if no data is found.
   */
  async getAverageInventoryAccuracy(
    startDate: string,
    endDate: string,
    areaFilter: string,
  ): Promise<InventoryAccuracy> {
    return this.inventoryStore.getAverageInventoryAccuracy(
      startDate,
      endDate,
      areaFilter,
    );
  }

  async getInventoryAdvicesInProgress(): Promise<InventoryAdvicesInProgressData> {
    const inProgressData =
      await this.inventoryStore.getInventoryAdvicesInProgress();
    return {
      inProgressAdvices: inProgressData.in_progress_advices,
    };
  }

  async getAdvicesOutstanding(): Promise<AdvicesOutstandingData> {
    const data = await this.inventoryStore.getAdvicesOutstanding();
    return {outstandingAdvices: data.outstanding_advices};
  }

  /**
   * Attempts to query the database for inventory distribution over within a given date range.
   * @param startDate Start date for the date range
   * @param endDate End date for the date range
   * @returns Promise that resolves to InventoryStockDistributionOverData
   */
  async getOverInventoryPercentageHistoricalSeriesData(
    startDate: Date,
    endDate: Date,
  ): Promise<InventoryStockDistributionOverData> {
    return {
      overInventory:
        await this.inventoryStore.getOverInventoryPercentageHistoricalSeriesData(
          startDate,
          endDate,
        ),
    };
  }

  /**
   * Attempts to query the database for inventory distribution under within a given date range.
   * @param startDate Start date for the date range
   * @param endDate End date for the date range
   * @returns Promise that resolves to InventoryStockDistributionUnderData
   */
  async getUnderInventoryPercentageHistoricalSeriesData(
    startDate: Date,
    endDate: Date,
  ): Promise<InventoryStockDistributionUnderData> {
    return {
      underInventory:
        await this.inventoryStore.getUnderInventoryPercentageHistoricalSeriesData(
          startDate,
          endDate,
        ),
    };
  }

  /**
   * Attempts to query the database for inventory distribution no within a given date range.
   * @param startDate Start date for the date range
   * @param endDate End date for the date range
   * @returns Promise that resolves to InventoryStockDistributionNoData
   */
  async getNoInventoryPercentageHistoricalSeriesData(
    startDate: Date,
    endDate: Date,
  ): Promise<InventoryStockDistributionNoData> {
    return {
      noInventory:
        await this.inventoryStore.getNoInventoryPercentageHistoricalSeriesData(
          startDate,
          endDate,
        ),
    };
  }

  async getInventoryStorageUtilizationAsync(): Promise<InventoryStorageUtilization> {
    return this.inventoryStore.getInventoryStorageUtilization();
  }

  async getInventoryContainerEventsListAsync(
    containerId: string,
    params: InventoryContainerEventsQueryServiceParams,
  ): Promise<FlattenedInventoryContainerEventsDataItem[]> {
    const sortFields: SortField[] = params.sortFields ?? [];
    const limit: number = params.limit ?? 50;
    const page: number = params.page ?? 0;

    const queryParams: InventoryContainerEventsQueryStoreParams = {
      limit,
      page,
      sortFields,
      filters: params.reqFilter,
      months: params.months,
    };

    const results = await this.inventoryStore.getInventoryContainerEventsList(
      containerId,
      queryParams,
    );
    return results.list.map(r => ({
      timestamp: r.event_date?.value || 'Invalid Date',
      event: r.event_type,
      destinationContainer: r.destination_container,
      operator: r.operator_name,
      quantity: r.quantity,
      workstationCode: r.workstation_code,
      sku: r.sku_code,
    }));
  }

  async getInventoryContainerEventsKpiAsync(
    containerId: string,
  ): Promise<InventoryContainerEventsKpiContract> {
    const response: InventoryContainerEventsKpiResponse[] =
      await this.inventoryStore.getInventoryContainerEventsKpiAsync(
        containerId,
      );

    // Transform the response to extract timestamp values
    const transformedData: InventoryContainerEventsKpiData[] = response.map(
      item => ({
        containerId: item.container_id,
        locationId: item.location_id,
        zone: item.zone,
        sku: item.sku,
        quantity: item.quantity,
        lastActivityDate: item.last_activity_date?.value,
        lastCycleCount: item.last_cycle_count?.value,
        dataUpdated: item.data_updated?.value,
        cycleCountEventsToday: item.cycle_count_events_today,
        averageDailyCycleCount: item.average_daily_cycle_count,
        pickEventsToday: item.pick_events_today,
        averageDailyPickEvents: item.average_daily_pick_events,
      }),
    );

    return {
      events: transformedData,
    };
  }

  async getInventoryWMSContainerEventsKpiAsync(
    containerId: string,
  ): Promise<InventoryContainerEventsKpiContract> {
    const response: InventoryContainerEventsKpiResponse[] =
      await this.inventoryStore.getInventoryWMSContainerEventsKpiAsync(
        containerId,
      );

    // Transform the response to extract timestamp values
    const transformedData: InventoryContainerEventsKpiData[] = response.map(
      item => ({
        containerId: item.container_id,
        locationId: item.location_id,
        zone: item.zone,
        sku: item.sku,
        quantity: item.quantity,
        lastActivityDate: item.last_activity_date?.value,
        lastCycleCount: item.last_cycle_count?.value,
        dataUpdated: item.data_updated?.value,
        cycleCountEventsToday: item.cycle_count_events_today,
        averageDailyCycleCount: item.average_daily_cycle_count,
        pickEventsToday: item.pick_events_today,
        averageDailyPickEvents: item.average_daily_pick_events,
      }),
    );

    return {
      events: transformedData,
    };
  }

  /**
   * Gets the total number of handling units that have been trayed in a time period.
   * @param startDate String representation of the start date (usually ISO string)
   * @param endDate String representation of the end date (usually ISO string)
   * @returns Promise that resolves to the config and data objects for an ApiResponse.
   */
  async getInventoryHandlingUnitsTrayedAsync(
    startDate: string,
    endDate: string,
  ): Promise<{
    timePeriodInHours: number;
    data: InventoryHandlingUnitsTrayedData;
  }> {
    const start = DateTime.fromISO(startDate);
    const end = DateTime.fromISO(endDate);

    // @TODO: write the store function and query to get this data (seems to be Horizon data)
    // hardcode for now
    // After query, include 204 no content response when no data is available
    const timeDiffHoursExact = end.diff(start, 'hours').hours;

    const data: InventoryHandlingUnitsTrayedData = {
      handlingUnitsTrayed: 10 * timeDiffHoursExact,
    };
    return await Promise.resolve({
      timePeriodInHours: timeDiffHoursExact,
      data,
    });
  }

  async getInventoryHighImpactSkuListAsync(
    params: InventoryHighImpactSKUListServiceParams,
  ): Promise<
    ApiResponseArray<
      InventoryHighImpactSKU[],
      InventoryHighImpactSkuPaginationInfo
    >
  > {
    const sortFields: SortField[] = params.sortFields ?? [];
    const limit: number = params.limit ?? 25;
    const page: number = params.page ?? 1;

    const results: PaginatedResults<InventoryHighImpactSKU[]> =
      await this.inventoryStore.getInventoryHighImpactSkuListAsync({
        filters: params.reqFilter,
        sortFields,
        limit,
        page,
        startDate: new Date(params.startDate),
        endDate: new Date(params.endDate),
      });

    const filtersContract: ApiResponseArray<
      InventoryHighImpactSKU[],
      InventoryHighImpactSkuPaginationInfo
    > = {
      data: results.list,
      metadata: {
        page,
        limit,
        totalResults: results.totalResults,
      },
    };

    return filtersContract;
  }

  async getInventoryAccuracyMonthlyAggregatedSeries(settings: {
    startDate: string;
    endDate: string;
    departmentFilter: string;
  }): Promise<InventoryPerformanceSeriesData> {
    const queryResult =
      await this.inventoryStore.getInventoryAccuracyMonthlyAggregatedSeries(
        settings,
      );

    return {
      name: 'Inventory Accuracy',
      series: queryResult.map(qr => ({
        name: qr.aggregated_date.value,
        value: qr.percentage,
      })),
    };
  }

  async getOrderFulfillmentRateMonthlyAggregatedSeries(settings: {
    startDate: string;
    endDate: string;
    departmentFilter: string;
  }): Promise<InventoryPerformanceSeriesData> {
    const queryResult =
      await this.inventoryStore.getOrderFulfillmentRateMonthlyAggregatedSeries(
        settings,
      );

    return {
      name: 'Order Fulfillment',
      series: queryResult.map(qr => ({
        name: qr.aggregated_date.value,
        value: qr.percentage,
      })),
    };
  }

  async getStorageUtilizationMonthlyAggregatedSeries(settings: {
    startDate: string;
    endDate: string;
    departmentFilter: string;
  }): Promise<InventoryPerformanceSeriesData> {
    const queryResult =
      await this.inventoryStore.getStorageUtilizationMonthlyAggregatedSeries(
        settings,
      );

    return {
      name: 'Storage Utilization',
      series: queryResult.map(qr => ({
        name: qr.aggregated_date.value,
        value: qr.percentage,
      })),
    };
  }

  async getStockOutMonthlyAggregatedSeries(settings: {
    startDate: string;
    endDate: string;
    departmentFilter: string;
  }): Promise<InventoryPerformanceSeriesData> {
    const queryResult =
      await this.inventoryStore.getStockOutMonthlyAggregatedSeries(settings);

    return {
      name: 'Stock Out',
      series: queryResult.map(qr => ({
        name: qr.aggregated_date.value,
        value: qr.percentage,
      })),
    };
  }

  async getAtInventoryPercentageHistoricalDataAsync(
    startDate: Date,
    endDate: Date,
  ): Promise<InventoryStockDistributionAtData> {
    return {
      atInventory:
        await this.inventoryStore.getAtInventoryPercentageHistoricalDataAsync(
          startDate,
          endDate,
        ),
    };
  }

  async getInventoryPercentageHistoricalDataAsync(
    startDate: Date,
    endDate: Date,
    distributionType: DistributionType,
  ): Promise<InventoryDistributionStatusResponse> {
    const inventoryDistributionStatusSeriesData =
      await this.inventoryStore.getInventoryStatusDistributionHistoricalSeriesData(
        startDate,
        endDate,
        distributionType,
      );

    switch (distributionType) {
      case 'at':
        return {atInventory: inventoryDistributionStatusSeriesData};
      case 'under':
        return {underInventory: inventoryDistributionStatusSeriesData};
      case 'over':
        return {overInventory: inventoryDistributionStatusSeriesData};
      case 'no':
        return {noInventory: inventoryDistributionStatusSeriesData};
      default:
        throw IctError.internalServerError('Distribution type not found!');
    }
  }

  async getInventoryForecastTimestampAsync(): Promise<InventoryForecastDataAnalysisTimestamp> {
    const response: InventoryForecastTimestampResponse =
      await this.inventoryStore.getInventoryForecastTimestamps();
    return {
      analysisPerformedTimestamp: response.inference_datetime.value,
      dataUpdateTimestamp: response.inventory_timestamp?.value,
    };
  }

  async getInventoryForecastListingAsync(
    params: InventoryForecastListingParams,
  ): Promise<InventoryForecastListingData> {
    const updatedQueryParams: InventoryForecastQueryParams = {
      limit: params.limit,
      page: params.page,
      sortField: params.sortField,
      filters: params.reqFilter,
      searchString: params.searchString,
    };

    const response: PaginatedResults<InventoryForecastListingQueryResponse[]> =
      await this.inventoryStore.getInventoryForecastListing(updatedQueryParams);

    const listing: InventoryForecastListing[] = response.list.map(item => ({
      sku: item.sku,
      current: {
        reserveStorage: item.reserveStorage,
        forwardPick: item.forwardPick,
      },
      projected: {
        pendingReplenishment: item.pendingReplenishment,
        pendingPicks: item.pendingPicks,
        allocatedOrders: item.allocatedOrders,
        projectedForwardPick: item.projectedForwardPick,
      },
      forecast: {
        averageReplenishment: item.averageReplenishment,
        averageDemand: item.averageDemand,
        demandTomorrow: item.demandTomorrow,
        knownDemand: item.knownDemand,
        forwardPickTomorrow: item.forwardPickTomorrow,
        twoDayDemand: item.twoDayDemand,
        twoDayForwardPick: item.twoDayForwardPick,
      },
    }));

    const inventoryForecastListingConfig: InventoryForecastListingConfig = {
      page: params.page,
      limit: params.limit,
      totalResults: response.totalResults,
    };

    return {
      data: listing,
      metadata: inventoryForecastListingConfig,
    };
  }

  async getInventoryForecastSkuLocations(
    skuId: string,
  ): Promise<InventoryForecastSkuLocationAreas> {
    const response =
      await this.inventoryStore.getInventoryForecastDrawerLocations(skuId);

    const reserveLocations: InventoryForecastSkuLocationDetails[] =
      response[0].reserve.map(InventoryService.toInventoryLocationResult);

    const forwardLocations: InventoryForecastSkuLocationDetails[] =
      response[0].forward.map(InventoryService.toInventoryLocationResult);

    const reserveArea: InventoryForecastSkuLocationByArea = {
      area: 'Reserve Storage',
      details: reserveLocations,
    };

    const forwardPickArea: InventoryForecastSkuLocationByArea = {
      area: 'Forward Pick',
      details: forwardLocations,
    };

    return {data: [reserveArea, forwardPickArea]};
  }

  async getInventoryForecastSkuOrders(
    skuId: string,
  ): Promise<InventoryForecastSkuOrders> {
    const response: InventorySkuOrdersResponse[] =
      await this.inventoryStore.getInventoryForecastDrawerOrders(skuId);

    const orders = response.map(item => ({
      skuId: item.skuId,
      orderId: item.order_id,
      priority: item.priority,
      allocationDate: item.allocation_date.value,
      shipDate: item.ship_date.value,
      orderLines: item.order_lines,
      allocatedQty: item.qty_allocated,
    }));

    const total = orders.length;

    return {skuId, openOrderCount: total, data: orders};
  }

  async getInventoryForecastSkuForecastDetails(
    skuId: string,
  ): Promise<InventorySkuForecastDetails> {
    const response: InventoryForecastSkuDetailsQueryResponse =
      await this.inventoryStore.getInventoryForecastSkuDetails(skuId);

    const forecast: InventorySkuForecastDetails = {
      confidence: response.confidence_quantity,
      knownDemand: {
        quantity: response.known_demand_qty,
        percentage: response.known_demand_percentage,
      },
      shortTermDaily: response.short_term_daily_demand_quantity,
      shortTermDailyDays: response.short_term_daily_days,
      longTermDaily: response.long_term_daily_demand_quantity,
      longTermDailyDays: response.long_term_daily_days,
      nonZeroDemand: response.non_zero_demand_percentage,
      zeroDemandIntermittency: response.zero_demand_intermittency_days,
      predictedDemandSeries: response.forecasted_demand_series,
      actualDemandSeries: response.actual_demand_series,
      confidenceLowSeries: response.demand_lower_range_series,
      confidenceHighSeries: response.demand_upper_range_series,
    };

    return forecast;
  }

  static toInventoryLocationResult(
    item: InventorySkuForecastLocationData,
  ): InventoryForecastSkuLocationDetails {
    return {
      locationId: item.location_id,
      locationType: item.location_type,
      containerId: item.container_id,
      containerCount: item.container_count,
      quantity: item.quantity,
      containerQuantity: item.container_quantity ?? undefined,
      uom: item.uom ?? undefined,
      skuSize: item.sku_size,
      containerDimensions: InventoryService.formatContainerDimensions(
        item.length,
        item.width,
      ),
      conditionCode: item.condition_code,
      zone: item.zone,
      lastActivityDate: item.last_activity_date_local.value,
    };
  }

  static formatContainerDimensions(
    length: number | undefined,
    width: number | undefined,
  ): string | undefined {
    if (length && width && length > 0 && width > 0) {
      return `${length} x ${width}`;
    }
    if (length && length > 0) {
      return `${length}`;
    }
    if (width && width > 0) {
      return `${width}`;
    }
    return undefined;
  }

  async getInventoryContainerDataAsync(
    params: InventoryContainerQueryServiceParams,
  ): Promise<PostInventoryContainersListResponse> {
    const sortFields: SortField[] = params.sortFields ?? [];
    const limit: number = params.limit ?? 50;
    const page: number = params.page ?? 1;

    const results: PaginatedResults<InventoryContainerData[]> =
      await this.inventoryStore.getInventoryContainersList({
        filters: params.reqFilter,
        sortFields,
        limit,
        page,
        byPassConfigSetting: params.byPassConfigSetting,
        searchString: params.searchString,
      });

    // Transform the data to remove the .value property for date fields
    const transformedData: FlattenedInventoryContainerData[] = results.list.map(
      item => ({
        container_id: item.container_id,
        location_id: item.location_id,
        zone: item.zone,
        sku: item.sku,
        quantity: item.quantity,
        last_activity_date: item.last_activity_date?.value || null,
        last_cycle_count: item.last_cycle_count?.value || null,
        data_updated: item.data_updated?.value || null,
        free_cycle_count: item.free_cycle_count?.value || null,
      }),
    );

    return {
      data: transformedData,
      metadata: {
        page,
        limit,
        totalResults: results.totalResults,
      },
    };
  }

  async getInventoryWMSContainerDataAsync(
    params: InventoryContainerQueryServiceParams,
  ): Promise<PostInventoryContainersListResponse> {
    const sortFields: SortField[] = params.sortFields ?? [];
    const limit: number = params.limit ?? 50;
    const page: number = params.page ?? 1;

    const results: PaginatedResults<InventoryContainerData[]> =
      await this.inventoryStore.getInventoryWMSContainersList({
        filters: params.reqFilter,
        sortFields,
        limit,
        page,
        byPassConfigSetting: params.byPassConfigSetting,
        searchString: params.searchString,
      });

    // Transform the data to remove the .value property for date fields
    const transformedData: FlattenedInventoryContainerData[] = results.list.map(
      item => ({
        container_id: item.container_id,
        location_id: item.location_id,
        zone: item.zone,
        sku: item.sku,
        quantity: item.quantity,
        last_activity_date: item.last_activity_date?.value || null,
        last_cycle_count: item.last_cycle_count?.value || null,
        data_updated: item.data_updated?.value || null,
        free_cycle_count: item.free_cycle_count?.value || null,
      }),
    );

    return {
      data: transformedData,
      metadata: {
        page,
        limit,
        totalResults: results.totalResults,
      },
    };
  }

  async handleInventoryUploadKnownDemand(
    manager: Express.Multer.File,
    details: Express.Multer.File,
    recTime: number,
  ): Promise<{
    ordersProcessed: number;
    orderLinesProcessed: number;
  }> {
    // Extract the date from the manager file
    const managerDate = DataFlowManager.extractDateFromManager(manager.buffer);

    // Check if the report was already submitted or older than the most recent submission
    const lastUploadTime = await this.inventoryStore.getLastUploadTime();
    if (lastUploadTime && new Date(managerDate) <= new Date(lastUploadTime)) {
      throw IctError.badRequest(
        `The provided report is older than the most recent submission: ${lastUploadTime}.`,
      );
    }

    // Parse the manager file
    const managerData = DataFlowManager.parseXlsToJson(manager.buffer);

    // Parse the details file
    const detailsData = DataFlowManager.parseXlsToJson(details.buffer);

    // Add the date to both JSON outputs
    /* eslint-disable @typescript-eslint/no-explicit-any */
    const managerJson = managerData.map((item: any) => ({
      ...item,
      date: managerDate,
    }));
    /* eslint-disable @typescript-eslint/no-explicit-any */
    const detailsJson = detailsData.map((item: any) => ({
      ...item,
      date: managerDate,
    }));

    const maxSize = 200 * 1024; // 200 kB in bytes
    const managerChunks = DataFlowManager.splitJsonData(managerJson, maxSize);
    const detailsChunks = DataFlowManager.splitJsonData(detailsJson, maxSize);

    let totalManagerRecordsProcessed = 0;
    let totalDetailsRecordsProcessed = 0;

    // Send each chunk via pub/sub
    managerChunks.forEach((chunk: string | any[]) => {
      DataFlowManager.sendToPubSub(chunk, recTime, 'KnownOrder');
      totalManagerRecordsProcessed += chunk.length;
    });

    detailsChunks.forEach((chunk: string | any[]) => {
      DataFlowManager.sendToPubSub(chunk, recTime, 'KnownOrderLine');
      totalDetailsRecordsProcessed += chunk.length;
    });

    return {
      ordersProcessed: totalManagerRecordsProcessed,
      orderLinesProcessed: totalDetailsRecordsProcessed,
    };
  }

  async getInventoryReplenishmentDetails(
    startDate: Date,
    endDate: Date,
  ): Promise<InventoryReplenishmentDetails> {
    const response = await this.inventoryStore.getInventoryReplenishmentDetails(
      startDate,
      endDate,
    );

    const shiftData: ShiftData = {
      firstShift: response.first_shift.map(item => ({
        name: item.name.value,
        value: item.value,
      })),
      secondShift: response.second_shift?.map(item => ({
        name: item.name.value,
        value: item.value,
      })),
      thirdShift: response.third_shift?.map(item => ({
        name: item.name.value,
        value: item.value,
      })),
    };

    const shiftTimesResponse: {[key: string]: string} = response.shift_times;

    const mappedResponse: InventoryReplenishmentDetails = {
      // averageDailyReplenishments: response.average_replenishments,
      // averagePendingOrders: response.average_pending_orders,
      // averageCycleTimes: response.average_cycle_times,
      dailyReplenishments: response.daily_replenishments.map(item => ({
        name: item.name.value,
        value: item.value,
      })),
      dailyPendingOrders: response.daily_pending_orders.map(item => ({
        name: item.name.value,
        value: item.value,
      })),
      dailyCycleTimes: response.daily_cycle_times.map(item => ({
        name: item.name.value,
        value: item.value,
      })),
      shiftData: [shiftData],
      shiftTimes: shiftTimesResponse,
    };

    return mappedResponse;
  }

  /**
   * Gets replenishment task type data within a given date range.
   * @param startDate Start date for the data range
   * @param endDate End date for the data range
   * @returns Promise that resolves to TaskTypeData with daily breakdown by task type
   */
  async getReplenishmentTaskTypeSeriesData(
    startDate: Date,
    endDate: Date,
  ): Promise<TaskTypeData> {
    const response =
      await this.inventoryStore.getInventoryReplenishmentTaskTypeData(
        startDate,
        endDate,
      );

    return response;
  }

  async getInventoryUploadRecentActivity(): Promise<
    InventoryUploadRecentActivity[]
  > {
    const response: InventoryUploadRecentActivityQueryResponse[] =
      await this.inventoryStore.getInventoryUploadRecentActivity();
    return response.map(item => ({
      date: item.created_date_time_local.value,
      knownOrderCount: item.known_order_count,
      knownOrderLineCount: item.known_order_line_count,
    }));
  }

  async getInventoryBinLocations(
    aisle: string,
    level: string,
  ): Promise<InventoryBinLocation[]> {
    const response = await this.inventoryStore.getInventoryBinLocations(
      aisle,
      level,
    );

    // group items by binLocation and merge their skus
    const groupedData: {[binLocation: string]: InventoryBinLocation} = {};

    response.forEach(item => {
      if (!groupedData[item.binLocation]) {
        groupedData[item.binLocation] = {
          binLocation: item.binLocation,
          locationSide: item.locationSide,
          status: 'Empty',
          containerType: item.containerType || undefined,
          skus: [],
        };
      }

      // update the status if the container has sku
      // do not show sku in the list if the quantity is 0
      if (item.quantity > 0 && item.skuCode) {
        groupedData[item.binLocation].status = 'Occupied';
        groupedData[item.binLocation].skus.push({
          sku: item.skuCode,
          quantity: item.quantity,
        });
      }
    });

    // convert the grouped object to an array
    return Object.values(groupedData);
  }
}
