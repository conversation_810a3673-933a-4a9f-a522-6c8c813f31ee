import {BigQueryDatetime} from '@google-cloud/bigquery';
import {
  ApiResponseArray,
  BaseFilterType,
  FilterObjectParameters,
  SortField,
} from 'ict-api-foundations';

export interface InventoryContainerEventsData {
  event_date: BigQueryDatetime;
  event_type: string;
  destination_container: string;
  operator_name: string;
  quantity: number;
  workstation_code: string;
  sku_code: string;
}

export interface FlattenedInventoryContainerEventsDataItem {
  timestamp: string;
  event: string;
  destinationContainer: string;
  operator: string;
  quantity: number;
  workstationCode: string;
  sku: string;
}

export type PostInventoryContainerEventsListResponse = ApiResponseArray<
  FlattenedInventoryContainerEventsDataItem[],
  InventoryContainerEventsPaginationInfo
>;

export interface InventoryContainerEventsQueryServiceParams {
  limit?: number;
  page?: number;
  sortFields?: SortField[];
  reqFilter?: BaseFilterType;
  months?: number;
}

export interface InventoryContainerEventsQueryStoreParams {
  limit: number;
  page: number;
  sortFields: SortField[];
  filters?: BaseFilterType;
  filterParams?: FilterObjectParameters;
  months?: number;
}

export interface InventoryContainerEventsPaginationInfo {
  page: number;
  limit: number;
  totalResults?: number;
}
