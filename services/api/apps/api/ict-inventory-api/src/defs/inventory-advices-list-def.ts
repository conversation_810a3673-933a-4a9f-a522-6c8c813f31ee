import {BigQueryTimestamp} from '@google-cloud/bigquery';

export interface AdvicesListQueryResponse {
  advice_id: string;
  advice_status: string;
  owner_id: string;
  advice_type: string;
  supplier_id: string;
  created_time: BigQueryTimestamp;
  start_time: BigQueryTimestamp | null;
  finished_time: BigQueryTimestamp | null;
  advice_lines: number;
  delivered_lines: number;
  overdelivered_lines: number;
  underdelivered_lines: number;
}

export interface AdvicesListData {
  adviceId: string;
  adviceStatus: string;
  ownerId: string;
  adviceType: string;
  supplierId: string;
  createdTime: string;
  startTime: string | null;
  finishedTime: string | null;
  adviceLines: number;
  deliveredLines: number;
  overdeliveredLines: number;
  underdeliveredLines: number;
}

export type AdvicesList = {
  adviceList: AdvicesListData[];
};
