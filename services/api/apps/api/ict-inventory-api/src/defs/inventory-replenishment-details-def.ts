import {ChartSeriesData} from 'ict-api-foundations';

/**
 * DTO that defines the relevant details of the inventory replenishment details.
 */

export interface ShiftData {
  firstShift: ChartSeriesData[];
  secondShift?: ChartSeriesData[];
  thirdShift?: ChartSeriesData[];
}

export interface TaskTypeData {
  demand?: ChartSeriesData[];
  topOff?: ChartSeriesData[];
  relocation?: ChartSeriesData[];
}

export interface InventoryReplenishmentDetails {
  // averageDailyReplenishments: number;
  // averagePendingOrders: number;
  // averageCycleTimes: number;
  dailyReplenishments: ChartSeriesData[];
  dailyPendingOrders: ChartSeriesData[];
  dailyCycleTimes: ChartSeriesData[];
  shiftData: ShiftData[];
  shiftTimes: {};
}

export interface ChartTimeSeriesData {
  name: {value: string};
  value: number;
}

export interface InventoryReplenishmentQueryResponse {
  average_replenishments: number;
  average_pending_orders: number;
  average_cycle_times: number;
  daily_replenishments: ChartTimeSeriesData[];
  daily_pending_orders: ChartTimeSeriesData[];
  daily_cycle_times: ChartTimeSeriesData[];
  first_shift: ChartTimeSeriesData[];
  second_shift?: ChartTimeSeriesData[];
  third_shift?: ChartTimeSeriesData[];
  shift_times: {};
}
