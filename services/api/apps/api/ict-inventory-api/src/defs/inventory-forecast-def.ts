import {BigQueryDatetime} from '@google-cloud/bigquery';
import {
  ApiResponseArray,
  BaseFilterType,
  ChartSeriesData,
  FilterObjectParameters,
  SortField,
} from 'ict-api-foundations';

/**
 * DTO that defines the relevant details of the inventory forecast objects.
 */
export interface InventoryForecastDataAnalysisTimestamp {
  dataUpdateTimestamp?: string;
  analysisPerformedTimestamp: string;
}

export interface InventoryForecastTimestampResponse {
  inventory_timestamp: BigQueryDatetime | null;
  inference_datetime: BigQueryDatetime;
}

export type CurrentInventory = {
  reserveStorage: number;
  forwardPick: number;
};

export type ProjectedInventory = {
  pendingReplenishment: number;
  pendingPicks: number;
  allocatedOrders: number;
  projectedForwardPick: number;
};

export type ForecastedInventory = {
  averageReplenishment: number;
  averageDemand: number;
  demandTomorrow: null | number;
  knownDemand: null | number;
  forwardPickTomorrow: null | number;
  twoDayDemand: null | number;
  twoDayForwardPick: null | number;
};

export type InventoryForecastListingQueryResponse = {
  sku: string;
  reserveStorage: number;
  forwardPick: number;
  pendingReplenishment: number;
  pendingPicks: number;
  allocatedOrders: number;
  projectedForwardPick: number;
  averageReplenishment: number;
  averageDemand: number;
  demandTomorrow: null | number; // This value will be null until the inference has run for that day (~3:30 CST)
  knownDemand: null | number; // This value will be null until the inference has run for that day (~3:30 CST)
  forwardPickTomorrow: null | number; // This value will be null until the inference has run for that day (~3:30 CST)
  twoDayDemand: null | number; // This value will be null until the inference has run for that day (~3:30 CST)
  twoDayForwardPick: null | number; // This value will be null until the inference has run for that day (~3:30 CST)
};

export type InventoryForecastListing = {
  sku: string;
  current: CurrentInventory;
  projected: ProjectedInventory;
  forecast: ForecastedInventory;
};

export interface InventoryForecastListingParams {
  page: number;
  limit: number;
  sortField: SortField[];
  reqFilter?: BaseFilterType;
  searchString?: string;
}

export interface InventoryForecastQueryParams {
  page: number;
  limit: number;
  sortField: SortField[];
  filters?: BaseFilterType;
  filterParams?: FilterObjectParameters;
  searchString?: string;
}

export interface InventoryForecastListingConfig {
  page: number;
  limit: number;
  totalResults?: number;
}

export type InventoryForecastListingData = ApiResponseArray<
  InventoryForecastListing[],
  InventoryForecastListingConfig
>;

export type InventoryForecastSkuLocationDetails = {
  locationId: string;
  locationType: string;
  containerId: string | null;
  containerCount: number;
  quantity: number;
  containerQuantity?: number;
  uom?: string; // Unit of measure
  skuSize?: string;
  containerDimensions?: string;
  conditionCode: string;
  zone: string;
  lastActivityDate: string;
};

export type InventorySkuForecastLocationData = {
  location_id: string;
  location_type: string;
  container_id: string | null;
  container_count: number;
  quantity: number;
  container_quantity?: number;
  uom?: string;
  sku_size?: string;
  length?: number;
  width?: number;
  condition_code: string;
  zone: string;
  last_activity_date_local: {value: string};
};
export interface InventorySkuLocationResponse {
  reserve: InventorySkuForecastLocationData[];
  forward: InventorySkuForecastLocationData[];
}

export interface InventoryForecastZoneMapping {
  forward_pick: string[];
  reserve_storage: string[];
}

export type InventoryForecastSkuLocationByArea = {
  area: string;
  details: InventoryForecastSkuLocationDetails[];
};

export interface InventoryForecastSkuLocationAreas {
  data: InventoryForecastSkuLocationByArea[];
}

export type InventoryForecastSkuOrderDetails = {
  skuId: string;
  orderId: string;
  priority: string;
  allocationDate: string;
  shipDate: string;
  orderLines: number;
  allocatedQty: number;
};

export interface InventoryForecastSkuOrders {
  skuId: string;
  openOrderCount: number;
  data: InventoryForecastSkuOrderDetails[];
}

export interface InventorySkuOrdersResponse {
  order_id: string;
  priority: string;
  allocation_date: {value: string};
  ship_date: {value: string};
  order_lines: number;
  qty_allocated: number;
  skuId: string;
}

export type InventoryKnownDemand = {
  quantity: number;
  percentage: number;
};

export interface InventorySkuForecastDetails {
  confidence: number;
  knownDemand: InventoryKnownDemand;
  shortTermDaily: number;
  shortTermDailyDays: number;
  longTermDaily: number;
  longTermDailyDays: number;
  nonZeroDemand: number;
  zeroDemandIntermittency: number;
  predictedDemandSeries: ChartSeriesData[];
  actualDemandSeries: ChartSeriesData[];
  confidenceLowSeries: ChartSeriesData[];
  confidenceHighSeries: ChartSeriesData[];
}

export type InventoryForecastSkuDetailsQueryResponse = {
  actual_demand_series: ChartSeriesData[];
  forecasted_demand_series: ChartSeriesData[];
  demand_lower_range_series: ChartSeriesData[];
  demand_upper_range_series: ChartSeriesData[];
  confidence_quantity: number;
  known_demand_percentage: number;
  known_demand_qty: number;
  short_term_daily_demand_quantity: number;
  short_term_daily_days: number;
  long_term_daily_demand_quantity: number;
  long_term_daily_days: number;
  non_zero_demand_percentage: number;
  zero_demand_intermittency_days: number;
};
