/**
 * DTO that encapsulates the inventory table.
 */

import {
  SortField,
  FilterObjectParameters,
  BaseFilterType,
  ApiResponseArray,
} from 'ict-api-foundations';

export type PostWmsInventorySkusListResponse = ApiResponseArray<
  WmsInventorySku[],
  InventorySkuPaginationInfo
>;

export type PostFacilityInventorySkusListResponse = ApiResponseArray<
  FacilityInventorySku[],
  InventorySkuPaginationInfo
>;

export interface WmsInventorySku {
  sku: string;
  description: string;
  daysOnHand: number;
  status: string;
  averageDailyQuantity: number;
  averageDailyOrders: number;
  targetMultiplicity: number;
  velocityClassification: string;
  quantityAvailable: number;
  quantityAllocated: number;
  totalQuantity: number;
  locations: number;
  skuPositions: number;
  maxContainers: number;
  contOverage: number;
  latestInventorySnapshotTimestamp?: string;
  latestCycleCountTimestamp: string;
  latestActivityDateTimestamp: string;
}

export interface FacilityInventorySku {
  sku: string;
  description: string;
  daysOnHand: number;
  status: string;
  averageDailyQuantity: number;
  averageDailyOrders: number;
  quantityAvailable: number;
  quantityAllocated: number;
  totalQuantity: number;
  locations: number;
  skuPositions: number;
  latestInventorySnapshotTimestamp?: string;
  latestActivityDateTimestamp: string;
}

export interface InventorySkuQueryServiceParams {
  limit?: number;
  page?: number;
  sortFields?: SortField[];
  reqFilter?: BaseFilterType;
  searchString?: string;
}

export interface InventorySkuQueryStoreParams {
  limit: number;
  page: number;
  sortFields: SortField[];
  filters?: BaseFilterType;
  filterParams?: FilterObjectParameters;
  searchString?: string;
}

export interface InventorySkuPaginationInfo {
  page: number;
  limit: number;
  totalResults?: number;
}
