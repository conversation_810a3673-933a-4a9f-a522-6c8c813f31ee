import {ApiResponseArray} from 'ict-api-foundations';

/**
 * Data to hold in a contract for Handling Units Trayed.
 */
export interface InventoryHandlingUnitsTrayedData {
  handlingUnitsTrayed: number;
}

/**
 * Configuration to hold in a contract for Handling Units Trayed.
 */
export interface InventoryHandlingUnitsTrayedConfig {
  timePeriodInHours: number;
}

/**
 * Contract for Handling Units Trayed.
 */
export interface InventoryHandlingUnitsTrayedContract
  extends ApiResponseArray<
    InventoryHandlingUnitsTrayedData,
    InventoryHandlingUnitsTrayedConfig
  > {}
