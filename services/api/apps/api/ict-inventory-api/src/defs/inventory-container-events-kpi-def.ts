export interface InventoryContainerEventsKpiResponse {
  container_id: string;
  location_id: string;
  zone: string;
  sku: string;
  quantity: number;
  last_activity_date: {value: string};
  last_cycle_count: {value: string};
  data_updated: {value: string};
  cycle_count_events_today: number;
  average_daily_cycle_count: number;
  pick_events_today: number;
  average_daily_pick_events: number;
}

// Transformed data type
export type InventoryContainerEventsKpiData = {
  containerId: string;
  locationId: string;
  zone: string;
  sku: string;
  quantity: number;
  lastActivityDate: string | null;
  lastCycleCount: string | null;
  dataUpdated: string | null;
  cycleCountEventsToday: number;
  averageDailyCycleCount: number;
  pickEventsToday: number;
  averageDailyPickEvents: number;
};

export interface InventoryContainerEventsKpiContract {
  events: InventoryContainerEventsKpiData[];
}
