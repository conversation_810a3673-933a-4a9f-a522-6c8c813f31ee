/**
 * DTO that defines the relevant details of the bin location objects
 */
export interface InventoryBinLocation {
  binLocation: string;
  locationSide: 'Left' | 'Right';
  status: 'Occupied' | 'Empty';
  containerType?: string;
  skus: {sku: string; quantity: number}[];
}

export interface InventoryBinLocationsResponse {
  data: InventoryBinLocation[];
}

export interface InventoryBinLocationQueryResponse {
  binLocation: string;
  locationSide: 'Left' | 'Right';
  skuCode?: string;
  quantity: number;
  containerType?: string;
}
