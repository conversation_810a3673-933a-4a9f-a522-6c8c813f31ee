import {BigQueryDate} from '@google-cloud/bigquery';
import {ChartSeriesData} from 'ict-api-foundations';

export interface InventoryPerformanceSeriesQueryResponse {
  aggregated_date: BigQueryDate;
  percentage: number;
}

export interface InventoryPerformanceSeriesData {
  name: string;
  series: ChartSeriesData[];
}

export type InventoryPerformanceSeriesContract =
  InventoryPerformanceSeriesData[];
