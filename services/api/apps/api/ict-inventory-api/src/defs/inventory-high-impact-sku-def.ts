import {
  ApiResponseArray,
  BaseFilterType,
  FilterObjectParameters,
  SortField,
} from 'ict-api-foundations';

export interface InventoryHighImpactSKU {
  sku: string;
  accuracy: number;
  storageArea: string;
  quantity: number;
  cubeUtilization: number;
  daysOnHand: number;
}

export interface InventoryHighImpactSKUListServiceParams {
  startDate: string;
  endDate: string;
  reqFilter?: BaseFilterType;
  sortFields?: SortField[];
  limit?: number;
  page?: number;
}

export interface InventoryHighImpactSKUListStoreParams {
  startDate: Date;
  endDate: Date;
  filters?: BaseFilterType;
  filterParams?: FilterObjectParameters;
  sortFields: SortField[];
  limit: number;
  page: number;
}

export interface InventoryHighImpactSkuPaginationInfo {
  page: number;
  limit: number;
  totalResults?: number;
}

export type InventoryHighImpactSKUContract = ApiResponseArray<
  InventoryHighImpactSKU[],
  InventoryHighImpactSkuPaginationInfo
>;
