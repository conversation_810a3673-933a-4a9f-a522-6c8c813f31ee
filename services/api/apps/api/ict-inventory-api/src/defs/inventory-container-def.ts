import {
  ApiResponseArray,
  BaseFilterType,
  FilterObjectParameters,
  SortField,
} from 'ict-api-foundations';

export type InventoryContainerData = {
  container_id: string;
  location_id: string;
  zone: string;
  sku: string;
  quantity: number;
  last_activity_date: {value: string} | null;
  last_cycle_count: {value: string} | null;
  data_updated: {value: string} | null;
  free_cycle_count: {value: string} | null;
};

export type FlattenedInventoryContainerData = {
  container_id: string;
  location_id: string;
  zone: string;
  sku: string;
  quantity: number;
  last_activity_date: string | null;
  last_cycle_count: string | null;
  data_updated: string | null;
  free_cycle_count: string | null;
};

export type PostInventoryContainersListResponse = ApiResponseArray<
  FlattenedInventoryContainerData[],
  InventoryContainerPaginationInfo
>;

export interface InventoryContainerQueryServiceParams {
  limit?: number;
  page?: number;
  sortFields?: SortField[];
  reqFilter?: BaseFilterType;
  byPassConfigSetting?: boolean;
  searchString?: string;
}

export interface InventoryContainerQueryStoreParams {
  limit: number;
  page: number;
  sortFields: SortField[];
  filters?: BaseFilterType;
  filterParams?: FilterObjectParameters;
  byPassConfigSetting?: boolean;
  searchString?: string;
}

export interface InventoryContainerPaginationInfo {
  page: number;
  limit: number;
  totalResults?: number;
}
