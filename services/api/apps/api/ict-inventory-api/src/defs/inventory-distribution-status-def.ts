import {InventoryStockDistributionAtData} from './inventory-distribution-stat-history-def';
import {InventoryStockDistributionNoData} from './inventory-stock-distribution-no-def';
import {InventoryStockDistributionOverData} from './inventory-stock-distribution-over-def';
import {InventoryStockDistributionUnderData} from './inventory-stock-distribution-under-def';

export type DistributionType = 'at' | 'under' | 'over' | 'no';

export type InventoryDistributionStatusResponse =
  | InventoryStockDistributionNoData
  | InventoryStockDistributionAtData
  | InventoryStockDistributionOverData
  | InventoryStockDistributionUnderData;
