/* eslint-disable @typescript-eslint/no-explicit-any */
import {expect} from 'chai';
import {
  Container,
  ContextService,
  DatabaseProvider,
  DatabaseTypes,
  EdgeDirection,
  EnvironmentService,
  IctError,
  Neo4jDatabase,
  RedisClient,
  WinstonLogger,
} from 'ict-api-foundations';
import sinon from 'sinon';
import {
  Area,
  Edge,
  ProcessFlowGraphResponse,
} from '../../defs/inventory-process-flow-def.ts';
import {ProcessFlowStore} from '../../stores/process-flow-store.ts';

describe('ProcessFlowStore', () => {
  let contextService;
  let redisClient: sinon.SinonStubbedInstance<RedisClient>;
  let envService: sinon.SinonStubbedInstance<EnvironmentService>;
  let logger: sinon.SinonStubbedInstance<WinstonLogger>;
  let processFlowStore: ProcessFlowStore;
  let dbInstance: sinon.SinonStubbedInstance<Neo4jDatabase>;
  let runStub: sinon.SinonStub;

  const mockGraph: ProcessFlowGraphResponse = {
    areas: [
      {
        id: 'area1',
        label: 'Area 1',
        metrics: ['metric1'],
        nodeType: 'Area',
        position: {
          x: 500,
          y: 250,
        },
      },
    ],
    edges: [
      {
        id: 'edge1',
        source: 'area1',
        direction: EdgeDirection.Downstream,
        target: 'area2',
        metrics: ['metric2'],
      },
    ],
  };

  beforeEach(() => {
    contextService = Container.get(ContextService);
    redisClient = sinon.createStubInstance(RedisClient);
    envService = new EnvironmentService();
    logger = sinon.createStubInstance(WinstonLogger);

    sinon.stub(envService, 'redis').returns({
      get: () => ({
        localRequestCacheOverride: false,
      }),
    });

    dbInstance = sinon.createStubInstance(Neo4jDatabase);
    dbInstance.getDriver.returns({
      session: () => {
        return {
          run: runStub,
          close: sinon.stub(),
        };
      },
    } as any);

    runStub = sinon.stub().resolves({records: [{get: () => mockGraph}]});
    const dbProvider = new DatabaseProvider();
    dbProvider.set(DatabaseTypes.Neo4j, dbInstance);

    sinon.stub(contextService, 'dbProvider').get(() => dbProvider);

    sinon.stub(contextService, 'datasetId').get(() => 'testDataset');

    processFlowStore = new ProcessFlowStore(
      contextService,
      redisClient,
      logger,
      envService
    );
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getAreaGraph', () => {
    beforeEach(() => {
      runStub.resetHistory();
    });

    it('should return cached data if available', async () => {
      const cachedData = JSON.stringify(mockGraph);
      redisClient.get.resolves(cachedData);
      redisClient.ttl.resolves(300);

      const result = await processFlowStore.getAreaGraph();
      expect(redisClient.get.calledOnce).to.be.true;
      expect(result).to.deep.equal(mockGraph);
    });

    it('should query Neo4J and cache the result if cache is empty', async () => {
      redisClient.get.resolves(null);
      runStub.resolves({
        records: [
          {get: () => ({areas: mockGraph.areas, edges: mockGraph.edges})},
        ],
      });

      const result = await processFlowStore.getAreaGraph();
      expect(redisClient.get.calledOnce).to.be.true;
      expect(runStub.calledOnce).to.be.true;
      expect(redisClient.setEx.calledOnce).to.be.true;
      expect(result).to.deep.equal(mockGraph);
    });

    it('should throw IctError.noContent if no records are found', async () => {
      redisClient.get.resolves(null);
      runStub.resolves({records: []});

      await expect(
        processFlowStore.getAreaGraph()
      ).to.eventually.be.rejectedWith(IctError);
    });

    it('should handle and throw IctError for any database errors', async () => {
      redisClient.get.resolves(null);
      runStub.rejects(new Error('Database error'));

      await expect(
        processFlowStore.getAreaGraph()
      ).to.eventually.be.rejectedWith(IctError);
    });
  });

  describe('getElement', () => {
    it('should query Neo4J with the provided id and return the metrics', async () => {
      const elementId = 'area1';
      const mockMetrics = {
        metrics: ['metric1'],
      };

      // Create a mock record with a get method that returns an object with a result property
      const mockRecord = {
        get: (key: string) => {
          if (key === 'result') {
            return mockMetrics;
          }
          return null;
        },
        toObject: () => ({result: mockMetrics}),
      };

      runStub.resolves({records: [mockRecord]});

      const result = await processFlowStore.getElement(elementId);
      expect(runStub.firstCall.args[1]).to.deep.equal({id: elementId});
      expect(result).to.deep.equal(mockMetrics);
    });

    it('should throw IctError.noContent if no records are found', async () => {
      const elementId = 'area1';
      runStub.resolves({records: []});

      await expect(
        processFlowStore.getElement(elementId)
      ).to.eventually.be.rejectedWith(IctError);
    });

    it('should handle and throw IctError for any database errors', async () => {
      const elementId = 'area1';
      runStub.rejects(new Error('Database error'));

      await expect(
        processFlowStore.getElement(elementId)
      ).to.eventually.be.rejectedWith(IctError);
    });
  });

  describe('updatePosition', () => {
    beforeEach(() => {
      runStub.resetHistory();
    });

    it('should query Neo4J with the new x and y positions', async () => {
      const area: Area = {
        id: 'area1',
        label: 'Area 1',
        metrics: [],
        nodeType: 'Area',
        position: {x: 100, y: 200},
      };

      runStub.resolves({
        records: [{get: () => area}],
      });

      const result = await processFlowStore.updatePosition(area, 'facility');
      expect(runStub.firstCall.args[1]).to.contain({
        id: area.id,
        x: area.position.x,
        y: area.position.y,
        view: 'facility',
      });
      expect(result).to.deep.equal(area);
    });

    it('should throw IctError.noContent if no records are found', async () => {
      const area: Area = {
        id: 'area1',
        label: 'Area 1',
        metrics: [],
        nodeType: 'Area',
        position: {x: 100, y: 200},
      };

      runStub.resolves({records: []});

      await expect(
        processFlowStore.updatePosition(area, 'facility')
      ).to.eventually.be.rejectedWith(IctError);
    });

    it('should handle and throw IctError for any Redis errors', async () => {
      const area: Area = {
        id: 'area1',
        label: 'Area 1',
        metrics: [],
        nodeType: 'Area',
        position: {x: 100, y: 200},
      };

      runStub.rejects(new Error('Redis error'));

      await expect(
        processFlowStore.updatePosition(area, 'facility')
      ).to.eventually.be.rejectedWith(IctError);
    });
  });

  describe('updateEdge', () => {
    beforeEach(() => {
      runStub.resetHistory();
    });

    it('should query Neo4J with the new downstream value', async () => {
      const edge: Edge = {
        id: 'area1',
        direction: EdgeDirection.Downstream,
        source: '',
        target: '',
        metrics: [],
      };

      const result = await processFlowStore.updateEdge(edge);
      expect(runStub.firstCall.args[1]).to.contain({
        id: edge.id,
        newDirection: edge.direction,
      });
      expect(result).to.deep.equal(edge);
    });

    it('should throw IctError.noContent if no records are found', async () => {
      const edge: Edge = {
        id: 'area1',
        direction: EdgeDirection.Downstream,
        source: '',
        target: '',
        metrics: [],
      };
      runStub.returns({records: []});

      expect(processFlowStore.updateEdge(edge)).to.eventually.be.rejectedWith(
        IctError
      );
    });

    it('should handle and throw IctError for any Redis errors', async () => {
      const edge: Edge = {
        id: 'area1',
        direction: EdgeDirection.Downstream,
        source: '',
        target: '',
        metrics: [],
      };

      runStub.rejects(new Error('Redis error'));

      expect(processFlowStore.updateEdge(edge)).to.eventually.be.rejectedWith(
        IctError
      );
    });
  });
});
