import * as chai from 'chai';
import chaiAsPromised from 'chai-as-promised';
import sinon, {SinonStub} from 'sinon';
import {
  AppConfigSetting,
  BigQueryDatabase,
  ChartSeriesData,
  ChartSeriesPercentageData,
  ConfigStore,
  ContextService,
  DatabaseProvider,
  HealthStatus,
  IctError,
  PaginatedResults,
  SingleFilterObject,
  SQLQueryGenerator,
} from 'ict-api-foundations';
import {
  BigQuery,
  BigQueryDate,
  BigQueryDatetime,
  BigQueryTimestamp,
} from '@google-cloud/bigquery';
import {DefaultConfigSettings} from 'ict-api-schema';
import {InventoryStore} from '../../stores/inventory-store.ts';
import {InventoryAreaFilterDefinition} from '../../defs/inventory-area-filter-def.ts';
import {InventoryAccuracy} from '../../defs/inventory-accuracy-def.ts';
import {InventoryHighImpactSKU} from '../../defs/inventory-high-impact-sku-def.ts';
import {InventoryContainerEventsData} from '../../defs/inventory-container-events-list-def.ts';
import {InventoryPerformanceSeriesQueryResponse} from '../../defs/inventory-performance-series-def.ts';
import {InventoryAdvicesInProgressQueryResponse} from '../../defs/inventory-advices-in-progress-def.ts';
import {AdvicesOutstandingQueryResponse} from '../../defs/inventory-advices-outstanding-def.ts';
import {AdvicesFinishedQueryResponse} from '../../defs/inventory-finished-advices-def.ts';
import {AdvicesCycleTimeQueryResponse} from '../../defs/inventory-advices-cycle-time-def.ts';
import {AdvicesListQueryResponse} from '../../defs/inventory-advices-list-def.ts';
import {AdvicesDetailsQueryResponse} from '../../defs/inventory-advices-details-drawer-def.ts';
import {
  WmsInventorySku,
  InventorySkuQueryStoreParams,
} from '../../defs/inventory-sku-def.ts';
import {
  InventoryForecastListingQueryResponse,
  InventoryForecastQueryParams,
  InventorySkuForecastLocationData,
  InventorySkuLocationResponse,
  InventorySkuOrdersResponse,
} from '../../defs/inventory-forecast-def.ts';
import {InventoryStorageUtilization} from '../../defs/inventory-storage-utilization-def.ts';
import {InventorySkuController} from '../../controllers/inventory-wms-sku-controller.ts';
import {
  InventoryReplenishmentQueryResponse,
  TaskTypeData,
} from '../../defs/inventory-replenishment-details-def.ts';
import {InventoryUploadRecentActivityQueryResponse} from '../../defs/inventory-upload-def.ts';
import {InventoryBinLocationQueryResponse} from '../../defs/inventory-bin-locations-def.ts';

const expect = chai.expect;
chai.use(chaiAsPromised);

describe('InventoryStore', () => {
  let inventoryStore: InventoryStore;
  let queryStub: sinon.SinonStubbedInstance<BigQuery>;
  let dbProvider: DatabaseProvider;
  let contextService: ContextService;
  let startDate: string;
  let endDate: string;
  let configStore: ConfigStore;
  let executeMonitoredJobStub: SinonStub;

  const workAreaFilterConfigSetting: AppConfigSetting = {
    id: 'ffff',
    name: 'inventory-work-area-code-filter-list',
    group: '',
    dataType: 'json',
    value: {areaCodes: []},
  };

  beforeEach(() => {
    // Create a BigQuery client
    queryStub = sinon.createStubInstance(BigQuery);
    dbProvider = new DatabaseProvider();
    const bqDatabase = new BigQueryDatabase(queryStub, 'testDataset');
    dbProvider.set(bqDatabase.getType(), bqDatabase);

    contextService = new ContextService();
    sinon.stub(contextService, 'dbProvider').get(() => dbProvider);
    executeMonitoredJobStub = sinon.stub(
      dbProvider.bigQuery,
      'executeMonitoredJob',
    );

    configStore = new ConfigStore(contextService);
    inventoryStore = new InventoryStore(contextService, configStore);

    startDate = '2023-10-10';
    endDate = '2023-10-11';
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getInventoryAreaFilterAsync', () => {
    it('should retrieve area filter data from the database', async () => {
      // Prepare test
      const mockResponse: InventoryAreaFilterDefinition = {
        areaFilterTable: ['Filter 1', 'Filter 2'],
      };
      // Mock the query response
      executeMonitoredJobStub.resolves([
        [{area_filter: 'Filter 1'}, {area_filter: 'Filter 2'}],
      ]);

      // Call the function
      const result = await inventoryStore.getInventoryAreaFilterAsync();
      // Assertions
      expect(executeMonitoredJobStub.calledOnce).to.be.true;
      expect(result).to.deep.equal(mockResponse);
    });
  });

  describe('getInventoryWmsSkuListAsync', () => {
    it('should retrieve sku data with accuracy information', async () => {
      const settingStub: sinon.SinonStub = sinon
        .stub(configStore, 'findMostRelevantSettingForUser')
        .resolves(undefined);
      const mockRequest: InventorySkuQueryStoreParams = {
        sortFields: [],
        limit: 50,
        page: 1,
      };
      // Prepare test
      const mockResponse: PaginatedResults<WmsInventorySku[]> = {
        list: InventorySkuController.exampleData,
        totalResults: 0,
      };
      // Mock the query response
      executeMonitoredJobStub.resolves([InventorySkuController.exampleData]);

      // Call the function
      const result =
        await inventoryStore.getInventoryWmsSkuListAsync(mockRequest);
      // Assertions
      expect(executeMonitoredJobStub.calledOnce).to.be.true;
      expect(result).to.deep.equal(mockResponse);
      expect(settingStub.calledOnce).to.be.true;
    });
  });

  describe('getAdvicesCycleTime', () => {
    let mockQueryResponse: AdvicesCycleTimeQueryResponse;

    beforeEach(() => {
      mockQueryResponse = {cycle_time: 2};
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);
    });

    it('should retrieve count of outstanding advices from the database', async () => {
      const result = await inventoryStore.getAdvicesCycleTime();
      const expected = {cycle_time: 2};
      expect(result).to.deep.equal(expected);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        inventoryStore.getAdvicesCycleTime(),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getAdvicesList', () => {
    let mockQueryResponse: AdvicesListQueryResponse[];

    beforeEach(() => {
      mockQueryResponse = [
        {
          advice_id: 'ADV01',
          advice_status: 'In progress',
          owner_id: 'OWN03',
          advice_type: 'SUPPLIER',
          supplier_id: 'SUP01',
          created_time: new BigQueryTimestamp('2024-01-01T00:00:00.000Z'),
          start_time: new BigQueryTimestamp('2024-01-02T00:00:00.000Z'),
          finished_time: null,
          advice_lines: 7,
          delivered_lines: 3,
          overdelivered_lines: 2,
          underdelivered_lines: 1,
        },
      ];
      executeMonitoredJobStub.resolves([mockQueryResponse]);
    });

    it('should retrieve list of advices from the database', async () => {
      const result = await inventoryStore.getAdvicesList();
      const expected = [
        {
          advice_id: 'ADV01',
          advice_status: 'In progress',
          owner_id: 'OWN03',
          advice_type: 'SUPPLIER',
          supplier_id: 'SUP01',
          created_time: new BigQueryTimestamp('2024-01-01T00:00:00.000Z'),
          start_time: new BigQueryTimestamp('2024-01-02T00:00:00.000Z'),
          finished_time: null,
          advice_lines: 7,
          delivered_lines: 3,
          overdelivered_lines: 2,
          underdelivered_lines: 1,
        },
      ];
      expect(result).to.deep.equal(expected);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        inventoryStore.getAdvicesList(),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getAdviceDetails', () => {
    const adviceId = 'ADV01';
    const mockQueryResponse: AdvicesDetailsQueryResponse[] = [
      {
        supplier_id: '001',
        handling_unit_id: 'HU-00001',
        handling_unit_type: 'PALLET',
        advice_line: 'ADV-00003#1',
        sku: 'SKU-00001',
        quantity: 10,
        packaging_level: 'CASE',
      },
    ];

    beforeEach(() => {
      executeMonitoredJobStub.resolves([mockQueryResponse]);
    });

    it('should return advice details', async () => {
      const adviceDetails = await inventoryStore.getAdviceDetails(adviceId);
      expect(adviceDetails).to.deep.equal(mockQueryResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        inventoryStore.getAdviceDetails(adviceId),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getAdvicesOutstanding', () => {
    let mockQueryResponse: AdvicesOutstandingQueryResponse;

    beforeEach(() => {
      mockQueryResponse = {outstanding_advices: 2};
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);
    });

    it('should retrieve count of outstanding advices from the database', async () => {
      const result = await inventoryStore.getAdvicesOutstanding();
      const expected = {outstanding_advices: 2};
      expect(result).to.deep.equal(expected);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        inventoryStore.getAdvicesOutstanding(),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getAdvicesFinished', () => {
    let mockQueryResponse: AdvicesFinishedQueryResponse;

    beforeEach(() => {
      mockQueryResponse = {finished_advices: 2};
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);
    });

    it('should retrieve count of outstanding advices from the database', async () => {
      const result = await inventoryStore.getAdvicesFinished();
      const expected = {finished_advices: 2};
      expect(result).to.deep.equal(expected);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        inventoryStore.getAdvicesFinished(),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getAverageInventoryAccuracy', () => {
    it('should retrieve inventory accuracy data from the database', async () => {
      const areaFilter = 'All';

      // Mock the query response
      const mockQueryResponse = {
        accuracy: 95,
      };

      const mockStoreResponse: InventoryAccuracy = {
        accuracy: mockQueryResponse.accuracy,
        status: HealthStatus.Ok,
        areaFilter,
      };

      executeMonitoredJobStub.resolves([[mockQueryResponse]]);
      const result = await inventoryStore.getAverageInventoryAccuracy(
        startDate,
        endDate,
        areaFilter,
      );

      // Assertions
      // BigQuery is only called one when functionality for getting valid area filters is not used
      expect(executeMonitoredJobStub.calledOnce).to.be.true;
      expect(result).to.deep.equal(mockStoreResponse);
    });

    it('should use All filter when an invalid filter is given', async () => {
      const areaFilter = 'invalidFilter';

      // Mock the query response
      const mockQueryResponse = {
        accuracy: 95,
      };

      const mockStoreResponse: InventoryAccuracy = {
        accuracy: mockQueryResponse.accuracy,
        status: HealthStatus.Ok,
        areaFilter: 'All',
      };

      executeMonitoredJobStub.resolves([[mockQueryResponse]]);
      const result = await inventoryStore.getAverageInventoryAccuracy(
        startDate,
        endDate,
        areaFilter,
      );

      // Assertions
      // BigQuery is only called one when functionality for getting valid area filters is not used
      expect(executeMonitoredJobStub.calledOnce).to.be.true;
      expect(result).to.deep.equal(mockStoreResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      const areaFilter = 'All';
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        inventoryStore.getAverageInventoryAccuracy(
          startDate,
          endDate,
          areaFilter,
        ),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getInventoryAdvicesInProgress', () => {
    let mockQueryResponse: InventoryAdvicesInProgressQueryResponse;

    beforeEach(() => {
      mockQueryResponse = {
        in_progress_advices: 1,
      };
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);
    });

    it('should retrieve count of in progress advices from the database', async () => {
      const result = await inventoryStore.getInventoryAdvicesInProgress();

      expect(result).to.deep.equal(mockQueryResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        inventoryStore.getInventoryAdvicesInProgress(),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getInventoryContainersList', () => {
    let settingStub: sinon.SinonStub;

    beforeEach(() => {
      sinon
        .stub(inventoryStore, 'getWorkAreaCodesToFilterBy')
        .resolves(['Zone A', 'Zone B']);
      executeMonitoredJobStub.resolves([
        [
          {
            totalResults: 1,
            container_id: 'AN0884',
            location_id: 'LOC987',
            zone: 'Zone A',
            sku: 'TRL1_STACK',
            quantity: 150,
            last_activity_date: {value: '2024-11-03 09:15:10 AM'},
            last_cycle_count: {value: '2024-10-30 11:45:20 AM'},
            data_updated: {value: '2024-11-05 08:00:00 AM'},
            free_cycle_count: null,
          },
        ],
      ]);

      settingStub = sinon.stub(configStore, 'findMostRelevantSettingForUser');
      settingStub.resolves(undefined);
    });

    afterEach(() => {
      sinon.restore();
    });
    it('should retrieve inventory containers list data with a skuId', async () => {
      const expectedResult = {
        list: [
          {
            container_id: 'AN0884',
            location_id: 'LOC987',
            zone: 'Zone A',
            sku: 'TRL1_STACK',
            quantity: 150,
            last_activity_date: {value: '2024-11-03 09:15:10 AM'},
            last_cycle_count: {value: '2024-10-30 11:45:20 AM'},
            data_updated: {value: '2024-11-05 08:00:00 AM'},
            free_cycle_count: null,
          },
        ],
        totalResults: 1,
      };

      // Apply filters to request
      const filters: SingleFilterObject = {
        type: 'single',
        name: 'sku',
        comparison: 'Equal',
        value: 'TRL1_STACK',
      };

      const result = await inventoryStore.getInventoryContainersList({
        filters,
        sortFields: [],
        page: 1,
        limit: 50,
      });

      expect(result).to.deep.equal(expectedResult);
      sinon.assert.calledOnce(executeMonitoredJobStub);
      sinon.assert.calledWithMatch(executeMonitoredJobStub, {
        query: sinon.match.string,
        params: sinon.match.object,
      });
      expect(settingStub.called).to.be.false;
    });

    it('should retrieve inventory containers list data without a skuId', async () => {
      const expectedResult = {
        list: [
          {
            container_id: 'AN0884',
            location_id: 'LOC987',
            zone: 'Zone A',
            sku: 'TRL1_STACK',
            quantity: 150,
            last_activity_date: {value: '2024-11-03 09:15:10 AM'},
            last_cycle_count: {value: '2024-10-30 11:45:20 AM'},
            data_updated: {value: '2024-11-05 08:00:00 AM'},
            free_cycle_count: null,
          },
        ],
        totalResults: 1,
      };

      const result = await inventoryStore.getInventoryContainersList({
        filters: undefined, // Check request with no filters
        sortFields: [],
        page: 1,
        limit: 50,
      });

      expect(result).to.deep.equal(expectedResult);
      expect(settingStub.called).to.be.false;
      sinon.assert.calledWithMatch(executeMonitoredJobStub, {
        query: sinon.match.string,
        params: sinon.match.object,
      });
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([{list: [], totalResults: 0}]);
      await expect(
        inventoryStore.getInventoryContainersList({
          filters: undefined,
          sortFields: [],
          page: 1,
          limit: 50,
        }),
      ).to.be.rejectedWith(IctError, 'No Content');
    });
  });

  describe('getInventoryContainerEventsList', () => {
    let containerId: string;
    let mockQueryResponse: PaginatedResults<InventoryContainerEventsData[]>;
    let paginatedQueryStub: sinon.SinonStub;

    beforeEach(() => {
      containerId = 'SK01';
      mockQueryResponse = {
        list: [
          {
            event_date: new BigQueryDatetime('2023-06-19T00:00:00.000Z'),
            event_type: 'Pick',
            destination_container: 'LPN56955',
            operator_name: 'John',
            quantity: 0,
            workstation_code: 'M12-GTP-12',
            sku_code: 'TRL1_STACK',
          },
        ],
        totalResults: 1,
      };
      // Stub SQLQueryGenerator.paginatedQuery to return our mock response.
      paginatedQueryStub = sinon
        .stub(SQLQueryGenerator, 'paginatedQuery')
        .resolves(mockQueryResponse);
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should retrieve inventory container events list data from the database', async () => {
      const params = {
        limit: 50,
        page: 1,
        sortFields: [],
        months: 6,
        filterParams: {
          counter: 0,
          months: 6,
          containerId: 'someContainerId',
        },
      };
      const result = await inventoryStore.getInventoryContainerEventsList(
        containerId,
        params,
      );
      expect(result).to.deep.equal(mockQueryResponse);
      sinon.assert.calledOnce(paginatedQueryStub);
      sinon.assert.calledWithMatch(
        paginatedQueryStub,
        sinon.match.any,
        sinon.match.any,
        sinon.match({
          selectFromTable: 'containerEventsList',
          allowedColumnNames: [
            'event_date',
            'event_type',
            'destination_container',
            'quantity',
            'operator_name',
            'workstation_code',
            'sku_code',
          ],
          cteSubquery: sinon.match.any,
          sortFields: [],
          page: 1,
          limit: 50,
        }),
        sinon.match.any,
        sinon.match({
          counter: 0,
          months: 6,
          containerId,
        }),
      );
    });

    it('should throw a No Content IctError if no data was found', async () => {
      paginatedQueryStub.restore();
      paginatedQueryStub = sinon
        .stub(SQLQueryGenerator, 'paginatedQuery')
        .resolves({list: [], totalResults: 0});
      sinon.stub(contextService, 'dbProvider').get(() => dbProvider);

      const params = {
        limit: 50,
        page: 1,
        sortFields: [],
        months: 6,
      };
      await expect(
        inventoryStore.getInventoryContainerEventsList(containerId, params),
      ).to.be.rejectedWith(IctError);
    });
  });

  describe('getInventoryWMSContainerEventsKpiAsync', () => {
    let containerId: string;
    let settingStub: SinonStub;

    beforeEach(() => {
      containerId = 'AN0884';
      settingStub = sinon.stub(configStore, 'findMostRelevantSettingForUser');
      settingStub.resolves('America/New York');
      // Mock the query response

      executeMonitoredJobStub.resolves([
        [
          {
            container_id: 'AN0884',
            location_id: 'LOC987',
            zone: 'Zone A',
            sku: 'TRL1_STACK',
            quantity: 150,
            last_activity_date: {value: '2024-11-03T09:15:10'},
            last_cycle_count: {value: '2024-10-30T11:45:20'},
            data_updated: {value: '2024-11-05T08:00:00'},
            cycle_count_events_today: 3,
            average_daily_cycle_count: 15,
            pick_events_today: 18,
            average_daily_pick_events: 29,
          },
        ],
      ]);
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should retrieve container KPI data for a specific containerId', async () => {
      const result =
        await inventoryStore.getInventoryWMSContainerEventsKpiAsync(
          containerId,
        );

      const expectedResult = [
        {
          container_id: 'AN0884',
          location_id: 'LOC987',
          zone: 'Zone A',
          sku: 'TRL1_STACK',
          quantity: 150,
          last_activity_date: {value: '2024-11-03T09:15:10'},
          last_cycle_count: {value: '2024-10-30T11:45:20'},
          data_updated: {value: '2024-11-05T08:00:00'},
          cycle_count_events_today: 3,
          average_daily_cycle_count: 15,
          pick_events_today: 18,
          average_daily_pick_events: 29,
        },
      ];

      expect(result).to.deep.equal(expectedResult);
      sinon.assert.calledOnce(executeMonitoredJobStub);
    });

    it('should throw a No Content IctError if no data is found', async () => {
      executeMonitoredJobStub.resolves([[]]); // Simulate no data found

      await expect(
        inventoryStore.getInventoryWMSContainerEventsKpiAsync(containerId),
      ).to.be.rejectedWith(IctError, 'No Content');

      sinon.assert.calledOnce(executeMonitoredJobStub);
    });

    it('should handle database errors gracefully', async () => {
      executeMonitoredJobStub.rejects(new Error('Database error')); // Simulate database error

      await expect(
        inventoryStore.getInventoryWMSContainerEventsKpiAsync(containerId),
      ).to.be.rejectedWith('Database error');

      sinon.assert.calledOnce(executeMonitoredJobStub);
    });
  });

  describe('getInventoryHighImpactSkuListAsync', () => {
    let queryResult: InventoryHighImpactSKU[];
    let mockResult: PaginatedResults<InventoryHighImpactSKU[]>;
    let settingStub: sinon.SinonStub;
    const configSetting: AppConfigSetting = {
      id: 'test',
      name: 'test',
      group: null,
      dataType: 'number',
      value: 10,
    };
    beforeEach(() => {
      startDate = '2023-10-10';
      endDate = '2023-10-11';
      queryResult = [
        {
          sku: '123456',
          accuracy: 0.9,
          storageArea: 'A',
          quantity: 100,
          cubeUtilization: 0.5,
          daysOnHand: 10,
        },
      ];
      executeMonitoredJobStub.resolves([queryResult]);
      mockResult = {
        list: queryResult,
        totalResults: 0,
      };
      settingStub = sinon
        .stub(configStore, 'findMostRelevantSettingForUser')
        .resolves(configSetting);
    });

    it('should retrieve high impact sku list from the database', async () => {
      const result = await inventoryStore.getInventoryHighImpactSkuListAsync({
        sortFields: [],
        page: 1,
        limit: 10,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
      });
      expect(result).to.deep.equal(mockResult);
    });

    it('should retrieve tenant config for high impact sku percentage', async () => {
      await inventoryStore.getInventoryHighImpactSkuListAsync({
        sortFields: [],
        page: 1,
        limit: 10,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
      });
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        inventoryStore.getInventoryHighImpactSkuListAsync({
          sortFields: [],
          page: 1,
          limit: 10,
          startDate: new Date(startDate),
          endDate: new Date(endDate),
        }),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getInventoryAccuracyMonthlyAggregatedSeries', () => {
    let departmentFilter: string;
    let mockResult: InventoryPerformanceSeriesQueryResponse[];

    beforeEach(() => {
      startDate = '2023-10-01';
      endDate = '2023-11-30';
      departmentFilter = 'All';
      mockResult = [
        {
          aggregated_date: new BigQueryDate('2023-10-01'),
          percentage: 90,
        },
        {
          aggregated_date: new BigQueryDate('2023-11-01'),
          percentage: 97,
        },
      ];
      executeMonitoredJobStub.resolves([mockResult]);
    });

    it('should retrieve monthly inventory accuracy data from the database', async () => {
      const result =
        await inventoryStore.getInventoryAccuracyMonthlyAggregatedSeries({
          startDate,
          endDate,
          departmentFilter,
        });
      expect(result).to.deep.equal(mockResult);
    });
  });

  describe('getOrderFulfillmentRateMonthlyAggregatedSeries', () => {
    let departmentFilter: string;
    let mockResult: InventoryPerformanceSeriesQueryResponse[];

    beforeEach(() => {
      startDate = '2023-10-01';
      endDate = '2023-11-30';
      departmentFilter = 'All';
      mockResult = [
        {
          aggregated_date: new BigQueryDate('2023-10-01'),
          percentage: 90,
        },
        {
          aggregated_date: new BigQueryDate('2023-11-01'),
          percentage: 97,
        },
      ];
      executeMonitoredJobStub.resolves([mockResult]);
    });

    it('should retrieve monthly order fulfillment rate data from the database', async () => {
      const result =
        await inventoryStore.getOrderFulfillmentRateMonthlyAggregatedSeries({
          startDate,
          endDate,
          departmentFilter,
        });
      expect(result).to.deep.equal(mockResult);
    });
  });

  describe('getStorageUtilizationMonthlyAggregatedSeries', () => {
    let departmentFilter: string;
    let mockResult: InventoryPerformanceSeriesQueryResponse[];

    beforeEach(() => {
      startDate = '2023-10-01';
      endDate = '2023-11-30';
      departmentFilter = 'All';
      mockResult = [
        {
          aggregated_date: new BigQueryDate('2023-10-01'),
          percentage: 90,
        },
        {
          aggregated_date: new BigQueryDate('2023-11-01'),
          percentage: 97,
        },
      ];
      executeMonitoredJobStub.resolves([mockResult]);
    });

    it.skip('should retrieve monthly storage utilization data from the database', async () => {
      const result =
        await inventoryStore.getStorageUtilizationMonthlyAggregatedSeries({
          startDate,
          endDate,
          departmentFilter,
        });
      expect(result).to.deep.equal(mockResult);
    });
  });

  describe('getStockOutMonthlyAggregatedSeries', () => {
    let departmentFilter: string;
    let mockResult: InventoryPerformanceSeriesQueryResponse[];

    beforeEach(() => {
      startDate = '2023-10-01';
      endDate = '2023-11-30';
      departmentFilter = 'All';
      mockResult = [
        {
          aggregated_date: new BigQueryDate('2023-10-01'),
          percentage: 90,
        },
        {
          aggregated_date: new BigQueryDate('2023-11-01'),
          percentage: 97,
        },
      ];
      executeMonitoredJobStub.resolves([mockResult]);
    });

    it.skip('should retrieve monthly stock out data from the database', async () => {
      const result = await inventoryStore.getStockOutMonthlyAggregatedSeries({
        startDate,
        endDate,
        departmentFilter,
      });
      expect(result).to.deep.equal(mockResult);
    });
  });

  describe('getInventoryStockDistributionOver', () => {
    let mockResult: ChartSeriesPercentageData[];
    let settingStub: sinon.SinonStub;

    beforeEach(() => {
      startDate = '2023-10-01';
      endDate = '2023-10-03';
      mockResult = [
        {
          name: '2023-10-01',
          value: 87,
          isValidPercentage: true,
        },
        {
          name: '2023-10-02',
          value: 55,
          isValidPercentage: true,
        },
        {
          name: '2023-10-03',
          value: 62,
          isValidPercentage: true,
        },
      ];
      executeMonitoredJobStub.resolves([mockResult]);
      settingStub = sinon
        .stub(configStore, 'findMostRelevantSettingForUser')
        .resolves(workAreaFilterConfigSetting);
    });

    it('should retrieve stock distribution over data for the given date range', async () => {
      const result =
        await inventoryStore.getOverInventoryPercentageHistoricalSeriesData(
          new Date(startDate),
          new Date(endDate),
        );
      expect(result).to.deep.equal(mockResult);
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should error when BigQuery throws an error', async () => {
      executeMonitoredJobStub.throws(new Error('Something bad happened'));

      const promise =
        inventoryStore.getOverInventoryPercentageHistoricalSeriesData(
          new Date(startDate),
          new Date(endDate),
        );
      expect(promise).to.eventually.be.rejectedWith(
        IctError.internalServerError(),
      );
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        inventoryStore.getOverInventoryPercentageHistoricalSeriesData(
          new Date(startDate),
          new Date(endDate),
        ),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getInventoryStockDistributionUnder', () => {
    let mockResult: ChartSeriesPercentageData[];
    let settingStub: SinonStub;

    beforeEach(() => {
      startDate = '2023-10-01';
      endDate = '2023-10-03';
      mockResult = [
        {
          name: '2023-10-01',
          value: 87,
          isValidPercentage: true,
        },
        {
          name: '2023-10-02',
          value: 55,
          isValidPercentage: true,
        },
        {
          name: '2023-10-03',
          value: 62,
          isValidPercentage: true,
        },
      ];
      executeMonitoredJobStub.resolves([mockResult]);
      settingStub = sinon
        .stub(configStore, 'findMostRelevantSettingForUser')
        .resolves(workAreaFilterConfigSetting);
    });

    it('should retrieve stock distribution under data for the given date range', async () => {
      const result =
        await inventoryStore.getUnderInventoryPercentageHistoricalSeriesData(
          new Date(startDate),
          new Date(endDate),
        );
      expect(result).to.deep.equal(mockResult);
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should error when BigQuery throws an error', async () => {
      executeMonitoredJobStub.throws(new Error('Something bad happened'));

      const promise =
        inventoryStore.getUnderInventoryPercentageHistoricalSeriesData(
          new Date(startDate),
          new Date(endDate),
        );
      expect(promise).to.eventually.be.rejectedWith(
        IctError.internalServerError(),
      );
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        inventoryStore.getUnderInventoryPercentageHistoricalSeriesData(
          new Date(startDate),
          new Date(endDate),
        ),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getInventoryStockDistributionNo', () => {
    let mockResult: ChartSeriesPercentageData[];
    let settingStub: SinonStub;

    beforeEach(() => {
      startDate = '2023-10-01';
      endDate = '2023-10-03';
      mockResult = [
        {
          name: '2023-10-01',
          value: 87,
          isValidPercentage: true,
        },
        {
          name: '2023-10-02',
          value: 55,
          isValidPercentage: true,
        },
        {
          name: '2023-10-03',
          value: 62,
          isValidPercentage: true,
        },
      ];
      executeMonitoredJobStub.resolves([mockResult]);
      settingStub = sinon
        .stub(configStore, 'findMostRelevantSettingForUser')
        .resolves(workAreaFilterConfigSetting);
    });

    it('should retrieve stock distribution no data for the given date range', async () => {
      const result =
        await inventoryStore.getNoInventoryPercentageHistoricalSeriesData(
          new Date(startDate),
          new Date(endDate),
        );
      expect(result).to.deep.equal(mockResult);
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should error when BigQuery throws an error', async () => {
      executeMonitoredJobStub.throws(new Error('Something bad happened'));

      const promise =
        inventoryStore.getNoInventoryPercentageHistoricalSeriesData(
          new Date(startDate),
          new Date(endDate),
        );
      expect(promise).to.eventually.be.rejectedWith(
        IctError.internalServerError(),
      );
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        inventoryStore.getNoInventoryPercentageHistoricalSeriesData(
          new Date(startDate),
          new Date(endDate),
        ),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getAtInventoryPercentageHistoricalDataAsync', () => {
    let mockResult: ChartSeriesPercentageData[];
    let settingStub: SinonStub;

    beforeEach(() => {
      startDate = '2023-10-01';
      endDate = '2023-10-03';
      mockResult = [
        {
          name: '2023-10-01',
          value: 87,
          isValidPercentage: true,
        },
        {
          name: '2023-10-02',
          value: 55,
          isValidPercentage: true,
        },
        {
          name: '2023-10-03',
          value: 62,
          isValidPercentage: true,
        },
      ];
      executeMonitoredJobStub.resolves([mockResult]);
      settingStub = sinon
        .stub(configStore, 'findMostRelevantSettingForUser')
        .resolves(workAreaFilterConfigSetting);
    });

    it('should retrieve stock distribution at data for the given date range', async () => {
      const result =
        await inventoryStore.getAtInventoryPercentageHistoricalDataAsync(
          new Date(startDate),
          new Date(endDate),
        );
      expect(result).to.deep.equal(mockResult);
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should error when BigQuery throws an error', () => {
      executeMonitoredJobStub.throws(new Error('Something bad happened'));

      const promise =
        inventoryStore.getAtInventoryPercentageHistoricalDataAsync(
          new Date(startDate),
          new Date(endDate),
        );
      expect(promise).to.eventually.be.rejectedWith(
        IctError.internalServerError(),
      );
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        inventoryStore.getAtInventoryPercentageHistoricalDataAsync(
          new Date(startDate),
          new Date(endDate),
        ),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('generateItemDistributionStatusQuery', () => {
    let settingStub: SinonStub;

    it('doesnt add the filter criteria if a setting isnt found', async () => {
      settingStub = sinon
        .stub(configStore, 'findMostRelevantSettingForUser')
        .resolves(undefined);

      const generatedSql =
        await inventoryStore.generateItemDistributionStatusQuery('at', 'wms');

      expect(generatedSql).not.includes('AND work_area_code IN');
      expect(settingStub.calledOnce).to.be.true;
    });

    it('doesnt add the filter criteria if a setting is found but doesnt have any values in areaCodes', async () => {
      const workAreaFilterConfigSettingNoAreaCodes: AppConfigSetting = {
        id: 'ffff',
        name: 'inventory-work-area-code-filter-list',
        group: '',
        dataType: 'json',
        value: {areaCodes: []},
      };
      settingStub = sinon
        .stub(configStore, 'findMostRelevantSettingForUser')
        .resolves(workAreaFilterConfigSettingNoAreaCodes);

      const generatedSql =
        await inventoryStore.generateItemDistributionStatusQuery(
          'under',
          'wms',
        );

      expect(generatedSql).not.includes('AND work_area_code IN');
      expect(settingStub.calledOnce).to.be.true;
    });

    it('Adds the filter criteria to the query if a setting is found', async () => {
      const workAreaFilterConfigSettingWithAreaCodes: AppConfigSetting = {
        id: 'ffff',
        name: 'inventory-work-area-code-filter-list',
        group: '',
        dataType: 'json',
        value: {areaCodes: ['ASRS', 'TESTAREA']},
      };
      settingStub = sinon
        .stub(configStore, 'findMostRelevantSettingForUser')
        .resolves(workAreaFilterConfigSettingWithAreaCodes);

      const generatedSql =
        await inventoryStore.generateItemDistributionStatusQuery(
          'under',
          'wms',
        );

      expect(generatedSql).includes("AND zone IN ('ASRS','TESTAREA')");
      expect(settingStub.calledOnce).to.be.true;
    });
  });

  describe('getInventoryForecastList', () => {
    let settingStub: sinon.SinonStub;
    const mockQueryResponse: InventoryForecastListingQueryResponse[] = [
      {
        sku: 'SKU01',
        reserveStorage: 4,
        forwardPick: 6,
        pendingReplenishment: 0,
        pendingPicks: 1,
        allocatedOrders: 2,
        projectedForwardPick: 4,
        averageReplenishment: 0.03333333333333333,
        averageDemand: 1,
        demandTomorrow: null,
        knownDemand: null,
        forwardPickTomorrow: null,
        twoDayDemand: null,
        twoDayForwardPick: null,
      },
      {
        sku: 'SKU02',
        reserveStorage: 0,
        forwardPick: 37,
        pendingReplenishment: 0,
        pendingPicks: 0,
        allocatedOrders: 0,
        projectedForwardPick: 37,
        averageReplenishment: 1.0333333333333334,
        averageDemand: 1,
        demandTomorrow: 0,
        knownDemand: 0,
        forwardPickTomorrow: 37,
        twoDayDemand: 0,
        twoDayForwardPick: 37,
      },
    ];

    const mockResponse: PaginatedResults<
      InventoryForecastListingQueryResponse[]
    > = {
      list: mockQueryResponse,
      totalResults: 0,
    };

    const mockParams: InventoryForecastQueryParams = {
      limit: 50,
      page: 2,
      sortField: [],
      filters: undefined,
      filterParams: undefined,
    };

    beforeEach(() => {
      configStore = new ConfigStore(contextService);
      inventoryStore = new InventoryStore(contextService, configStore);

      const configSetting: AppConfigSetting = {
        id: 'test',
        name: 'test',
        group: null,
        dataType: 'json',
        value: {
          reserve_storage: ['VNA'],
          forward_pick: ['DMS'],
        },
      };

      settingStub = sinon
        .stub(configStore, 'findMostRelevantSettingForUser')
        .resolves(configSetting);

      executeMonitoredJobStub.resolves([mockQueryResponse]);
    });

    it('should retrieve sku listing data from the database', async () => {
      const result =
        await inventoryStore.getInventoryForecastListing(mockParams);

      expect(executeMonitoredJobStub.calledOnce).to.be.true;
      expect(result).to.deep.equal(mockResponse);
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        inventoryStore.getInventoryForecastListing(mockParams),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getInventoryForecastDrawerLocations', () => {
    const mockQueryResponse: InventorySkuLocationResponse[] = [];
    let skuId: string;
    let mockReserve: InventorySkuForecastLocationData[];
    let mockForward: InventorySkuForecastLocationData[];
    let settingStub: sinon.SinonStub;

    beforeEach(() => {
      configStore = new ConfigStore(contextService);
      inventoryStore = new InventoryStore(contextService, configStore);

      const configSetting: AppConfigSetting = {
        id: 'test',
        name: 'test',
        group: null,
        dataType: 'json',
        value: {
          reserve_storage: ['VNA'],
          forward_pick: ['DMS'],
        },
      };

      settingStub = sinon
        .stub(configStore, 'findMostRelevantSettingForUser')
        .resolves(configSetting);

      skuId = 'SK01';
      mockReserve = [
        {
          location_id: 'CNVPAC01',
          location_type: 'STAGE',
          container_id: 'APP834',
          container_count: 1,
          quantity: 60,
          uom: 'EACHES',
          sku_size: 'L',
          length: 26,
          width: 34,
          condition_code: 'UNRESTRICTED',
          zone: 'CNVPAC01Z',
          last_activity_date_local: {value: '2024-09-03T02:31:58'},
        },
      ];
      mockForward = [
        {
          location_id: 'MS011107200211',
          location_type: 'FAST',
          container_id: null,
          container_count: 2,
          quantity: 120,
          uom: 'EACHES',
          sku_size: 'L',
          length: 26,
          width: 34,
          condition_code: 'UNRESTRICTED',
          zone: 'ASRS',
          last_activity_date_local: {value: '2024-07-11T02:39:14'},
        },
      ];

      mockQueryResponse.push({
        reserve: mockReserve,
        forward: mockForward,
      });

      executeMonitoredJobStub.resolves([mockQueryResponse]);
    });

    it('should retrieve sku location data from the database', async () => {
      const result =
        await inventoryStore.getInventoryForecastDrawerLocations(skuId);

      expect(result).to.deep.equal(mockQueryResponse);
      expect(settingStub.calledOnce).to.be.true;
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        inventoryStore.getInventoryForecastDrawerLocations(skuId),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getInventoryForecastDrawerOrders', () => {
    let skuId: string;

    const mockQueryResponse: InventorySkuOrdersResponse[] = [
      {
        skuId: 'SKU01',
        order_id: 'ORDER 1',
        priority: '1',
        allocation_date: {value: '2024-06-28 13:45:57 UTC'},
        ship_date: {value: '2024-06-28'},
        order_lines: 12,
        qty_allocated: 2,
      },
      {
        skuId: 'SKU01',
        order_id: 'ORDER 2',
        priority: '1',
        allocation_date: {value: '2024-06-28 05:12:22 UTC'},
        ship_date: {value: '2024-06-28'},
        order_lines: 5,
        qty_allocated: 10,
      },
    ];

    beforeEach(() => {
      configStore = new ConfigStore(contextService);
      inventoryStore = new InventoryStore(contextService, configStore);

      skuId = 'SKU01';

      executeMonitoredJobStub.resolves([mockQueryResponse]);
    });

    it('should retrieve sku order data from the database', async () => {
      const result =
        await inventoryStore.getInventoryForecastDrawerOrders(skuId);

      expect(result).to.deep.equal(mockQueryResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        inventoryStore.getInventoryForecastDrawerOrders(skuId),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getInventorySkuForecastDrawer', () => {
    const skuId = 'SKU01';

    const predictedDemandSeriesData: ChartSeriesData[] = [
      {
        name: '2024-10-08T12:05:06.615Z',
        value: 18,
      },
      {
        name: '2024-10-11T16:10:26.829Z',
        value: 0,
      },
      {
        name: '2024-10-14T03:16:45.028Z',
        value: 30,
      },
    ];

    const aactualDemandSeriesData: ChartSeriesData[] = [
      {
        name: '2024-10-08T12:05:06.615Z',
        value: 12,
      },
      {
        name: '2024-10-11T16:10:26.829Z',
        value: 0,
      },
      {
        name: '2024-10-14T03:16:45.028Z',
        value: 26,
      },
    ];

    const confidenceLowSeriesData: ChartSeriesData[] = [
      {
        name: '2024-10-08T12:05:06.615Z',
        value: 0,
      },
      {
        name: '2024-10-11T16:10:26.829Z',
        value: 8,
      },
      {
        name: '2024-10-14T03:16:45.028Z',
        value: 0,
      },
    ];

    const confidenceHighSeriesData: ChartSeriesData[] = [
      {
        name: '2024-10-08T12:05:06.615Z',
        value: 27,
      },
      {
        name: '2024-10-11T16:10:26.829Z',
        value: 6,
      },
      {
        name: '2024-10-14T03:16:45.028Z',
        value: 1,
      },
    ];

    const mockQueryResponse = [
      {
        actual_demand_series: aactualDemandSeriesData,
        forecasted_demand_series: predictedDemandSeriesData,
        demand_lower_range_series: confidenceLowSeriesData,
        demand_upper_range_series: confidenceHighSeriesData,
        confidence_quantity: 10,
        known_demand_percentage: 45,
        known_demand_qty: 85,
        short_term_daily_demand_quantity: 180,
        short_term_daily_days: 88,
        long_term_daily_demand_quantity: 178,
        long_term_daily_days: 360,
        non_zero_demand_percentage: 60,
        zero_demand_intermittency_days: 3,
      },
    ];

    beforeEach(() => {
      configStore = new ConfigStore(contextService);
      inventoryStore = new InventoryStore(contextService, configStore);
      executeMonitoredJobStub.resolves([mockQueryResponse]);
    });

    it('should retrieve sku inventory forecasting data from the database', async () => {
      const result = await inventoryStore.getInventoryForecastSkuDetails(skuId);

      expect([result]).to.deep.equal(mockQueryResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        inventoryStore.getInventoryForecastSkuDetails(skuId),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getInventoryStorageUtilization', () => {
    it('should retrieve storage utilization data from the database', async () => {
      const settingStub: sinon.SinonStub = sinon
        .stub(configStore, 'findMostRelevantSettingForUser')
        .resolves(undefined);

      // Prepare test
      const mockQueryResponse = {
        average_storage_utilization_percentage: 60,
      };

      const expectedStoreResponse: InventoryStorageUtilization = {
        utilizationPercentage: 60,
      };

      // Mock the query response
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);

      // Call the function
      const result = await inventoryStore.getInventoryStorageUtilization();
      // Assertions
      expect(executeMonitoredJobStub.calledOnce).to.be.true;
      expect(result).to.deep.equal(expectedStoreResponse);
      // settings for both aisle and area should be retrieved
      expect(settingStub.calledTwice).to.be.true;
    });

    it('should ensure there is a default area config setting defined', async () => {
      const settingNames: string[] = [];
      DefaultConfigSettings.forEach(setting => {
        settingNames.push(setting.name);
      });
      expect(DefaultConfigSettings).to.be.an('array');
      expect(settingNames).to.contain(
        'inventory-storage-utilization-area-filter-list',
      );
    });

    it('should ensure there is a default aisle config setting defined', async () => {
      const settingNames: string[] = [];
      DefaultConfigSettings.forEach(setting => {
        settingNames.push(setting.name);
      });
      expect(DefaultConfigSettings).to.be.an('array');
      expect(settingNames).to.contain(
        'inventory-storage-utilization-aisle-filter-list',
      );
    });
  });

  describe('getLastUploadTime', () => {
    beforeEach(() => {
      configStore = new ConfigStore(contextService);
      inventoryStore = new InventoryStore(contextService, configStore);
    });

    it('should retrieve the last upload time from the database', async () => {
      const timeResponse = '2024-12-17T12:37:24.999Z';
      const mockQueryResponseWithData = [
        {
          date: {
            value: timeResponse,
          },
        },
      ];
      executeMonitoredJobStub.resolves([mockQueryResponseWithData]);
      const result = await inventoryStore.getLastUploadTime();
      expect([result]).to.deep.equal([timeResponse]);
    });

    it('should return an empty value when there is no last upload time', async () => {
      executeMonitoredJobStub.resolves([0]);
      const result = await inventoryStore.getLastUploadTime();
      expect([result]).to.deep.equal([undefined]);
    });
  });

  describe('getInventoryReplenishmentDetails', () => {
    const start = new Date('2025-01-22T04:27:11.912Z');
    const end = new Date('2025-01-29T04:27:11.912Z');
    let settingStub: sinon.SinonStub;

    const configSetting: AppConfigSetting = {
      id: 'test',
      name: 'shift-start-end-times',
      group: null,
      description: 'The shift schedule in 24 Hour format.',
      dataType: 'json',
      value: {
        first_startTime: '6:00',
        first_endTime: '15:00',
        second_startTime: '15:30',
        second_endTime: '00:00',
      },
    };

    const mockQueryResponse: InventoryReplenishmentQueryResponse = {
      average_replenishments: 419,
      average_pending_orders: 570,
      average_cycle_times: 604,
      daily_replenishments: [
        {value: 312, name: {value: '2025-01-21'}},
        {value: 689, name: {value: '2025-01-22'}},
        {value: 452, name: {value: '2025-01-23'}},
        {value: 501, name: {value: '2025-01-24'}},
        {value: 0, name: {value: '2025-01-25'}},
        {value: 0, name: {value: '2025-01-26'}},
        {value: 583, name: {value: '2025-01-27'}},
      ],
      daily_pending_orders: [
        {value: 496, name: {value: '2025-01-21'}},
        {value: 602, name: {value: '2025-01-22'}},
        {value: 568, name: {value: '2025-01-23'}},
        {value: 603, name: {value: '2025-01-24'}},
        {value: 0, name: {value: '2025-01-25'}},
        {value: 0, name: {value: '2025-01-26'}},
        {value: 583, name: {value: '2025-01-27'}},
      ],
      daily_cycle_times: [
        {value: 296, name: {value: '2025-01-21'}},
        {value: 500, name: {value: '2025-01-22'}},
        {value: 568, name: {value: '2025-01-23'}},
        {value: 887, name: {value: '2025-01-24'}},
        {value: 0, name: {value: '2025-01-25'}},
        {value: 0, name: {value: '2025-01-26'}},
        {value: 840, name: {value: '2025-01-27'}},
      ],
      first_shift: [
        {value: 183, name: {value: '2025-01-21'}},
        {value: 294, name: {value: '2025-01-22'}},
        {value: 307, name: {value: '2025-01-23'}},
        {value: 277, name: {value: '2025-01-24'}},
        {value: 0, name: {value: '2025-01-25'}},
        {value: 0, name: {value: '2025-01-26'}},
        {value: 248, name: {value: '2025-01-27'}},
      ],
      second_shift: [
        {value: 125, name: {value: '2025-01-21'}},
        {value: 302, name: {value: '2025-01-22'}},
        {value: 127, name: {value: '2025-01-23'}},
        {value: 207, name: {value: '2025-01-24'}},
        {value: 0, name: {value: '2025-01-25'}},
        {value: 0, name: {value: '2025-01-26'}},
        {value: 329, name: {value: '2025-01-27'}},
      ],
      shift_times: {
        first_endTime: '15:00',
        second_endTime: '00:00',
        first_startTime: '6:00',
        second_startTime: '15:30',
      },
    };

    beforeEach(() => {
      configStore = new ConfigStore(contextService);

      settingStub = sinon
        .stub(configStore, 'findMostRelevantSettingForUser')
        .resolves(configSetting);
      inventoryStore = new InventoryStore(contextService, configStore);
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);
    });

    it('should return details about order replenishment history', async () => {
      const result = await inventoryStore.getInventoryReplenishmentDetails(
        start,
        end,
      );

      expect(result).to.deep.equal(mockQueryResponse);
      expect(settingStub.calledTwice).to.be.true;
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        inventoryStore.getInventoryReplenishmentDetails(start, end),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getInventoryReplenishmentTaskTypeData', () => {
    const start = new Date('2025-01-22T04:27:11.912Z');
    const end = new Date('2025-01-29T04:27:11.912Z');
    let settingStub: sinon.SinonStub;

    const mockQueryResponse: TaskTypeData = {
      demand: [
        {name: '2025-01-21', value: 15},
        {name: '2025-01-22', value: 12},
        {name: '2025-01-23', value: 18},
        {name: '2025-01-24', value: 10},
        {name: '2025-01-25', value: 0},
        {name: '2025-01-26', value: 0},
        {name: '2025-01-27', value: 14},
      ],
      topOff: [
        {name: '2025-01-21', value: 8},
        {name: '2025-01-22', value: 6},
        {name: '2025-01-23', value: 10},
        {name: '2025-01-24', value: 7},
        {name: '2025-01-25', value: 0},
        {name: '2025-01-26', value: 0},
        {name: '2025-01-27', value: 9},
      ],
      relocation: [
        {name: '2025-01-21', value: 5},
        {name: '2025-01-22', value: 3},
        {name: '2025-01-23', value: 8},
        {name: '2025-01-24', value: 4},
        {name: '2025-01-25', value: 0},
        {name: '2025-01-26', value: 0},
        {name: '2025-01-27', value: 6},
      ],
    };

    beforeEach(() => {
      configStore = new ConfigStore(contextService);

      // Mock timezone setting
      settingStub = sinon
        .stub(configStore, 'findMostRelevantSettingForUser')
        .resolves({
          id: '1',
          name: 'timezone',
          group: 'general',
          value: 'America/New_York',
          dataType: 'string',
        });

      inventoryStore = new InventoryStore(contextService, configStore);
      executeMonitoredJobStub.resolves([[mockQueryResponse]]);
    });

    it('should return task type breakdown data for replenishment activities', async () => {
      const result = await inventoryStore.getInventoryReplenishmentTaskTypeData(
        start,
        end,
      );

      expect(result).to.deep.equal(mockQueryResponse);
      expect(settingStub.calledOnce).to.be.true;
      sinon.assert.calledOnce(executeMonitoredJobStub);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([[]]);

      const outcome = await expect(
        inventoryStore.getInventoryReplenishmentTaskTypeData(start, end),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });

    it('should handle database errors gracefully', async () => {
      executeMonitoredJobStub.rejects(new Error('Database error'));

      await expect(
        inventoryStore.getInventoryReplenishmentTaskTypeData(start, end),
      ).to.be.rejectedWith('Database error');

      sinon.assert.calledOnce(executeMonitoredJobStub);
    });
  });

  describe('getInventoryUploadRecentActivity', () => {
    const mockQueryResponse: InventoryUploadRecentActivityQueryResponse[] = [
      {
        created_date_time_local: {
          value: '2025-02-18T13:48:59',
        },
        known_order_count: 2454,
        known_order_line_count: 31490,
      },
      {
        created_date_time_local: {
          value: '2025-02-18T11:33:59',
        },
        known_order_count: 2454,
        known_order_line_count: 31490,
      },
      {
        created_date_time_local: {
          value: '2025-02-17T15:41:59',
        },
        known_order_count: 2268,
        known_order_line_count: 30987,
      },
      {
        created_date_time_local: {
          value: '2025-02-17T12:23:00',
        },
        known_order_count: 2268,
        known_order_line_count: 30987,
      },
      {
        created_date_time_local: {
          value: '2025-02-17T07:13:00',
        },
        known_order_count: 2268,
        known_order_line_count: 30987,
      },
      {
        created_date_time_local: {
          value: '2025-02-16T13:41:00',
        },
        known_order_count: 2936,
        known_order_line_count: 33736,
      },
      {
        created_date_time_local: {
          value: '2025-02-16T12:34:59',
        },
        known_order_count: 2936,
        known_order_line_count: 33736,
      },
      {
        created_date_time_local: {
          value: '2025-02-15T14:42:00',
        },
        known_order_count: 1730,
        known_order_line_count: 16854,
      },
      {
        created_date_time_local: {
          value: '2025-02-15T12:12:59',
        },
        known_order_count: 1730,
        known_order_line_count: 16854,
      },
      {
        created_date_time_local: {
          value: '2025-02-14T11:27:00',
        },
        known_order_count: 2197,
        known_order_line_count: 23452,
      },
    ];

    beforeEach(() => {
      configStore = new ConfigStore(contextService);
      inventoryStore = new InventoryStore(contextService, configStore);
      executeMonitoredJobStub.resolves([mockQueryResponse]);
    });

    it('should return details about order replenishment history', async () => {
      const result = await inventoryStore.getInventoryUploadRecentActivity();

      expect(result).to.deep.equal(mockQueryResponse);
    });

    it('should throw a No Content IctError if no data was found', async () => {
      executeMonitoredJobStub.resolves([]);

      const outcome = await expect(
        inventoryStore.getInventoryUploadRecentActivity(),
      ).to.be.rejectedWith(IctError);

      expect(outcome.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('getInventoryBinLocations', () => {
    const aisle = '01';
    const level = '01';

    const mockQueryResponse: InventoryBinLocationQueryResponse[] = [
      {
        binLocation: 'MS000000000',
        locationSide: 'Left',
        skuCode: 'Test sku',
        quantity: 5,
        containerType: 'Inventory Tote',
      },
      {
        binLocation: 'MS000000000',
        locationSide: 'Left',
        skuCode: 'Test sku 2',
        quantity: 10,
        containerType: 'Inventory Tote',
      },
      {
        binLocation: 'MS111111111',
        locationSide: 'Left',
        skuCode: 'Test sku',
        quantity: 5,
        containerType: 'Inventory Tote',
      },
      {
        binLocation: 'MS222222222',
        locationSide: 'Left',
        skuCode: 'Test sku 2',
        quantity: 0,
        containerType: 'Inventory Tote',
      },
    ];

    beforeEach(() => {
      configStore = new ConfigStore(contextService);
      inventoryStore = new InventoryStore(contextService, configStore);
      executeMonitoredJobStub.resolves([mockQueryResponse]);
    });

    it('should retrieve bin location data from the database', async () => {
      const result = await inventoryStore.getInventoryBinLocations(
        aisle,
        level,
      );

      expect(result).to.deep.equal(mockQueryResponse);
    });
  });
});
