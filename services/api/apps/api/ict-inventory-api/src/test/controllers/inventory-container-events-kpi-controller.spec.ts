import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {InventoryService} from '../../services/inventory-service.ts';
import {InventoryContainerEventsKpiController} from '../../controllers/Inventory-container-events-kpi-controller.ts';
import {InventoryContainerEventsKpiContract} from '../../defs/inventory-container-events-kpi-def.ts';

describe('InventoryContainerEventsKpiController', () => {
  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Inventory Service. */
  let inventoryServiceStub: sinon.SinonStubbedInstance<InventoryService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('GET /inventory/container-events/{containerId} - Non-WMS Version', () => {
    const url: string = '/inventory/container-events/AN0884';

    describe('error scenarios', () => {
      it('should return a 500 error when the service throws an error', async () => {
        inventoryServiceStub = sinon.createStubInstance(InventoryService);
        Container.set(InventoryService, inventoryServiceStub);
        inventoryServiceStub.getInventoryContainerEventsKpiAsync.rejects(
          new Error('Something bad happened'),
        );

        const response = await request(app).get(url);

        expect(response.status).to.equal(500);
        expect(response.headers['content-type']).to.match(/json/);
        expect(response.body.status).to.equal(500);
        expect(response.body.title).to.equal('Internal Server Error');

        sinon.assert.calledOnce(
          inventoryServiceStub.getInventoryContainerEventsKpiAsync,
        );
      });
    });

    describe('success scenarios', () => {
      beforeEach(() => {
        inventoryServiceStub = sinon.createStubInstance(InventoryService);
        Container.set(InventoryService, inventoryServiceStub);

        // Wrap exampleData in the correct contract format
        const exampleResponse: InventoryContainerEventsKpiContract = {
          events: [InventoryContainerEventsKpiController.exampleData],
        };

        inventoryServiceStub.getInventoryContainerEventsKpiAsync.resolves(
          exampleResponse,
        );
      });

      it('should call "InventoryService.getInventoryContainerEventsKpiAsync" and return the data', async () => {
        const response = await request(app).get(url);

        expect(response.status).to.equal(200);
        expect(response.headers['content-type']).to.match(/json/);
        expect(response.body).to.deep.equal(
          InventoryContainerEventsKpiController.exampleResponse,
        );

        sinon.assert.calledOnce(
          inventoryServiceStub.getInventoryContainerEventsKpiAsync,
        );

        sinon.assert.calledWithExactly(
          inventoryServiceStub.getInventoryContainerEventsKpiAsync,
          'AN0884', // Verify the containerId is passed correctly
        );
      });
    });
  });

  describe('GET /inventory/wms/container-events/{containerId} - WMS Version', () => {
    const url: string = '/inventory/wms/container-events/AN0884';

    describe('error scenarios', () => {
      it('should return a 500 error when the WMS service throws an error', async () => {
        inventoryServiceStub = sinon.createStubInstance(InventoryService);
        Container.set(InventoryService, inventoryServiceStub);
        inventoryServiceStub.getInventoryWMSContainerEventsKpiAsync.rejects(
          new Error('Something bad happened'),
        );

        const response = await request(app).get(url);

        expect(response.status).to.equal(500);
        expect(response.headers['content-type']).to.match(/json/);
        expect(response.body.status).to.equal(500);
        expect(response.body.title).to.equal('Internal Server Error');

        sinon.assert.calledOnce(
          inventoryServiceStub.getInventoryWMSContainerEventsKpiAsync,
        );
      });
    });

    describe('success scenarios', () => {
      beforeEach(() => {
        inventoryServiceStub = sinon.createStubInstance(InventoryService);
        Container.set(InventoryService, inventoryServiceStub);

        // Wrap exampleData in the correct contract format
        const exampleResponse: InventoryContainerEventsKpiContract = {
          events: [InventoryContainerEventsKpiController.exampleData],
        };

        inventoryServiceStub.getInventoryWMSContainerEventsKpiAsync.resolves(
          exampleResponse,
        );
      });

      it('should call "InventoryService.getInventoryWMSContainerEventsKpiAsync" and return the data', async () => {
        const response = await request(app).get(url);

        expect(response.status).to.equal(200);
        expect(response.headers['content-type']).to.match(/json/);
        expect(response.body).to.deep.equal(
          InventoryContainerEventsKpiController.exampleResponse,
        );

        sinon.assert.calledOnce(
          inventoryServiceStub.getInventoryWMSContainerEventsKpiAsync,
        );

        sinon.assert.calledWithExactly(
          inventoryServiceStub.getInventoryWMSContainerEventsKpiAsync,
          'AN0884', // Verify the containerId is passed correctly
        );
      });
    });
  });
});
