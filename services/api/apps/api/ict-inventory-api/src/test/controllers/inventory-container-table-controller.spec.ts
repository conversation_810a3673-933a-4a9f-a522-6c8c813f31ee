import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup, ExportFormatter} from 'ict-api-foundations';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {InventoryService} from '../../services/inventory-service.ts';
import {InventoryContainerTableController} from '../../controllers/inventory-container-table-controller.ts';
import {IncludedColumns} from '../../defs/inventory-export-def.ts';

describe('InventoryContainerTableController', () => {
  const url: string = '/inventory/containers/list';
  const exportUrl: string = '/inventory/containers/list/export';

  const app = appSetup(RegisterRoutes);

  const includedColumns: IncludedColumns = {
    location_id: true,
    zone: true,
    sku: true,
    quantity: true,
    last_activity_date: true,
    last_cycle_count: true,
    data_updated: true,
    free_cycle_count: true,
  };

  const exampleRequestWithColumn = {
    limit: 50,
    page: 1,
    filters: {
      type: 'single',
      name: 'sku',
      comparison: 'Like',
      value: '%TBN%',
    },
    sortFields: [],
    columns: includedColumns,
  };

  /** Stub instance for the Inventory Service. */
  let inventoryServiceStub: sinon.SinonStubbedInstance<InventoryService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Inventory Container Table Controller error scenarios', () => {
    beforeEach(() => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
    });

    it('should return a 500 error when the service throws an error', async () => {
      inventoryServiceStub.getInventoryContainerDataAsync.rejects(
        new Error('Something bad happened'),
      );

      const response = await request(app)
        .post(url)
        .send({
          page: 1,
          limit: 10,
          filters: {
            type: 'single',
            comparison: 'EQUALS',
            name: 'sku',
            value: 'TRL1_STACK',
          },
          sortFields: [],
        });

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledOnce(
        inventoryServiceStub.getInventoryContainerDataAsync,
      );
    });
  });

  describe('Inventory Container Table Controller success scenarios', () => {
    beforeEach(() => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);

      inventoryServiceStub.getInventoryContainerDataAsync.resolves({
        data: [InventoryContainerTableController.exampleData],
        metadata: {
          page: 1,
          limit: 10,
          totalResults:
            InventoryContainerTableController.exampleConfig.totalResults,
        },
      });
    });

    it('should call "InventoryService.getInventoryContainerDataAsync" and return the data', async () => {
      const response = await request(app)
        .post(url)
        .send({
          page: 1,
          limit: 10,
          filters: {
            type: 'single',
            comparison: 'EQUALS',
            name: 'sku',
            value: 'TRL1_STACK',
          },
          sortFields: [],
        });

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        InventoryContainerTableController.exampleResponse,
      );

      sinon.assert.calledOnce(
        inventoryServiceStub.getInventoryContainerDataAsync,
      );
    });
  });

  describe('Inventory Container Controller Export Error scenarios', () => {
    it('should validate that page and limit params are numbers', async () => {
      const response = await request(app).post(exportUrl).send({
        page: 'one',
        limit: 'two',
        columns: {},
      });

      expect(response.status).to.equal(422);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details.paginatedRequest.message).to.match(
        /invalid integer number/,
      );
      expect(response.body.details.paginatedRequest.message).to.match(/one/);
      expect(response.body.details.paginatedRequest.message).to.match(/two/);
    });

    it('should error when the service throws an error', async () => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryContainerDataAsync.rejects(
        new Error('Something bad happened'),
      );

      const response = await request(app)
        .post(exportUrl)
        .send(exampleRequestWithColumn);

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
    });
  });
  describe('Inventory Container Controller Export success scenarios', () => {
    let mapResponseToColumnsStub: sinon.SinonStub;
    let generateExcelFileStub: sinon.SinonStub;

    beforeEach(() => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryContainerDataAsync.resolves({
        data: [InventoryContainerTableController.exampleData],
        metadata: InventoryContainerTableController.exampleConfig,
      });

      mapResponseToColumnsStub = sinon.stub(
        ExportFormatter,
        'mapResponseToColumns',
      );
      generateExcelFileStub = sinon.stub(ExportFormatter, 'generateExcelFile');
    });

    afterEach(() => {
      mapResponseToColumnsStub.restore();
      generateExcelFileStub.restore();
    });

    it('should call "InventoryService.getInventoryContainerDataAsync" and return the data', async () => {
      const response = await request(app)
        .post(exportUrl)
        .send(exampleRequestWithColumn);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/sheet/);
      expect(response.headers['content-disposition']).to.match(/.xlsx/);
      expect(mapResponseToColumnsStub.calledOnce).to.be.true; // Ensure the spy was called
      expect(generateExcelFileStub.calledOnce).to.be.true; // Ensure the spy was called
    });
  });
});
