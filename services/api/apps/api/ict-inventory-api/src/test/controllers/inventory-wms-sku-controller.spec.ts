import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup, ExportFormatter} from 'ict-api-foundations';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {InventoryService} from '../../services/inventory-service.ts';
import {InventorySkuController} from '../../controllers/inventory-wms-sku-controller.ts';
import {IncludedColumns} from '../../defs/inventory-export-def.ts';

describe('InventorySkuController', () => {
  const url: string = '/inventory/wms/skus/list';
  const exportUrl: string = '/inventory/wms/skus/list/export';

  const app = appSetup(RegisterRoutes);

  const start_date = '2023-01-01T00:00:00.000Z';
  const end_date = '2023-01-02T00:00:00.000Z';
  const exampleRequest = {
    page: InventorySkuController.exampleConfig.page,
    limit: InventorySkuController.exampleConfig.limit,
  };

  const includedColumns: IncludedColumns = {
    sku: true,
    quantityAvailable: true,
    quantityAllocated: true,
    daysOnHand: true,
    averageDailyQuantity: true,
    averageDailyOrders: true,
    skuPositions: true,
    latestActivityDateTimestamp: true,
    latestCycleCountTimestamp: true,
    description: false,
    targetMultiplicity: false,
    velocityClassification: false,
  };

  const exampleRequestWithColumn = {
    limit: 50,
    page: 1,
    filters: {
      type: 'single',
      name: 'sku',
      comparison: 'Like',
      value: '%TBN%',
    },
    sortFields: [],
    columns: includedColumns,
  };

  /** Stub instance for the Inventory Service. */
  let inventoryServiceStub: sinon.SinonStubbedInstance<InventoryService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Inventory Sku Controller Error scenarios', () => {
    it('should validate that page and limit params are numbers', async () => {
      const response = await request(app).post(url).send({
        start_date,
        end_date,
        page: 'one',
        limit: 'two',
      });

      expect(response.status).to.equal(422);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details['paginatedRequest.page'].message).to.equal(
        'invalid integer number',
      );
      expect(response.body.details['paginatedRequest.limit'].message).to.equal(
        'invalid integer number',
      );
    });

    it('should validate minimum values for page and limit params', async () => {
      const response = await request(app).post(url).send({
        start_date,
        end_date,
        page: -1,
        limit: 0,
      });

      expect(response.status).to.equal(422);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details['paginatedRequest.page'].message).to.equal(
        'min 0',
      );
      expect(response.body.details['paginatedRequest.limit'].message).to.equal(
        'min 1',
      );
    });

    it('should validate maximum value for limit param', async () => {
      const response = await request(app).post(url).send({
        start_date,
        end_date,
        page: 1,
        limit: 1001,
      });

      expect(response.status).to.equal(422);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details['paginatedRequest.limit'].message).to.equal(
        'max 1000',
      );
    });

    it('should error when the service throws an error', async () => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryWmsSkuListAsync.rejects(
        new Error('Something bad happened'),
      );

      const response = await request(app).post(url).send(exampleRequest);

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWithMatch(
        inventoryServiceStub.getInventoryWmsSkuListAsync,
        {
          page: InventorySkuController.exampleConfig.page,
          limit: InventorySkuController.exampleConfig.limit,
        },
      );
    });
  });
  describe('Inventory Sku Controller success scenarios', () => {
    beforeEach(() => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryWmsSkuListAsync.resolves({
        data: InventorySkuController.exampleData,
        metadata: InventorySkuController.exampleConfig,
      });
    });

    it('should call "InventoryService.getInventorySkuAsync" and return the data', async () => {
      const response = await request(app).post(url).send(exampleRequest);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        InventorySkuController.exampleResponse,
      );

      sinon.assert.calledWithMatch(
        inventoryServiceStub.getInventoryWmsSkuListAsync,
        {
          page: InventorySkuController.exampleConfig.page,
          limit: InventorySkuController.exampleConfig.limit,
        },
      );
    });
  });

  it('should call "InventoryService.getInventorySkuAsync" with searchString and return the data', async () => {
    inventoryServiceStub = sinon.createStubInstance(InventoryService);
    Container.set(InventoryService, inventoryServiceStub);
    inventoryServiceStub.getInventoryWmsSkuListAsync.resolves({
      data: InventorySkuController.exampleData,
      metadata: InventorySkuController.exampleConfig,
    });

    const searchString = 'Dematic#1000000';
    const exampleRequestWithSearchString = {
      ...exampleRequest,
      searchString,
    };

    const response = await request(app)
      .post(url)
      .send(exampleRequestWithSearchString);

    expect(response.status).to.equal(200);
    expect(response.headers['content-type']).to.match(/json/);
    expect(response.body).to.deep.equal(InventorySkuController.exampleResponse);

    sinon.assert.calledWithMatch(
      inventoryServiceStub.getInventoryWmsSkuListAsync,
      {
        page: InventorySkuController.exampleConfig.page,
        limit: InventorySkuController.exampleConfig.limit,
        searchString,
      },
    );
  });

  describe('Inventory Sku Controller Export Error scenarios', () => {
    it('should validate that page and limit params are numbers', async () => {
      const response = await request(app).post(exportUrl).send({
        page: 'one',
        limit: 'two',
        columns: {},
      });

      expect(response.status).to.equal(422);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details.paginatedRequest.message).to.match(
        /invalid integer number/,
      );
      expect(response.body.details.paginatedRequest.message).to.match(/one/);
      expect(response.body.details.paginatedRequest.message).to.match(/two/);
    });

    it('should error when the service throws an error', async () => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryWmsSkuListAsync.rejects(
        new Error('Something bad happened'),
      );

      const response = await request(app)
        .post(exportUrl)
        .send(exampleRequestWithColumn);

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
    });
  });
  describe('Inventory Sku Controller Export success scenarios', () => {
    let mapResponseToColumnsStub: sinon.SinonStub;
    let generateExcelFileStub: sinon.SinonStub;

    beforeEach(() => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryWmsSkuListAsync.resolves({
        data: InventorySkuController.exampleData,
        metadata: InventorySkuController.exampleConfig,
      });

      mapResponseToColumnsStub = sinon.stub(
        ExportFormatter,
        'mapResponseToColumns',
      );
      generateExcelFileStub = sinon.stub(ExportFormatter, 'generateExcelFile');
    });

    afterEach(() => {
      mapResponseToColumnsStub.restore();
      generateExcelFileStub.restore();
    });

    it('should call "InventoryService.getInventorySkuAsync" and return the data', async () => {
      const response = await request(app)
        .post(exportUrl)
        .send(exampleRequestWithColumn);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/sheet/);
      expect(response.headers['content-disposition']).to.match(/.xlsx/);
      expect(mapResponseToColumnsStub.calledOnce).to.be.true; // Ensure the spy was called
      expect(generateExcelFileStub.calledOnce).to.be.true; // Ensure the spy was called
    });
  });
});
