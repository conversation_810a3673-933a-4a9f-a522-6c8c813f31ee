import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {InventoryService} from '../../services/inventory-service.ts';
import {InventoryForecastTableController} from '../../controllers/inventory-forecast-table-controller.ts';
import {IncludedColumns} from '../../defs/inventory-export-def.ts';
import {ExportFormatter} from '../../../../../../libs/api/ict-api-foundations/src/helpers/export/export-formatter.ts';

describe('InventoryForecastTableController', () => {
  const url: string = '/inventory/forecast/list';
  const exportUrl: string = '/inventory/forecast/list/export';

  const app = appSetup(RegisterRoutes);

  const includedColumns: IncludedColumns = {
    sku: true,
    'current.reserveStorage': true,
    'current.forwardPick': true,
    'projected.pendingReplenishment': true,
    'projected.pendingPicks': true,
    'projected.allocatedOrders': true,
    'projected.projectedForwardPick': true,
    'forecast.averageReplenishment': true,
    'forecast.averageDemand': true,
    'forecast.demandTomorrow': true,
    'forecast.knownDemand': true,
    'forecast.forwardPickTomorrow': true,
    'forecast.twoDayDemand': true,
    'forecast.twoDayForwardPick': true,
  };

  const exampleRequestWithColumn = {
    limit: 50,
    page: 1,
    filters: null,
    sortFields: [],
    columns: includedColumns,
  };

  /** Stub instance for the Inventory Service. */
  let inventoryServiceStub: sinon.SinonStubbedInstance<InventoryService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Inventory Forecast Table Controller Error scenarios', () => {
    it('should error when the service throws an error', async () => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryForecastListingAsync.rejects(
        new Error('Something bad happened'),
      );

      const response = await request(app).post(url).send({});

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWith(
        inventoryServiceStub.getInventoryForecastListingAsync,
      );
    });
  });
  describe('Inventory Forecast Table Controller success scenarios', () => {
    beforeEach(() => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryForecastListingAsync.resolves(
        InventoryForecastTableController.exampleResponse,
      );
    });

    it('should call "InventoryServiceStub.getInventoryForecastTableAsync" and return the data', async () => {
      const response = await request(app).post(url).send({});

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        InventoryForecastTableController.exampleResponse,
      );

      sinon.assert.calledWith(
        inventoryServiceStub.getInventoryForecastListingAsync,
      );
    });
  });

  describe('Inventory Forecast Controller Export Error scenarios', () => {
    it('should validate that page and limit params are numbers', async () => {
      const response = await request(app).post(exportUrl).send({
        page: 'one',
        limit: 'two',
        columns: {},
      });

      expect(response.status).to.equal(422);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details.paginatedRequest.message).to.match(
        /invalid integer number/,
      );
      expect(response.body.details.paginatedRequest.message).to.match(/one/);
      expect(response.body.details.paginatedRequest.message).to.match(/two/);
    });

    it('should error when the service throws an error', async () => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryForecastListingAsync.rejects(
        new Error('Something bad happened'),
      );

      const response = await request(app)
        .post(exportUrl)
        .send(exampleRequestWithColumn);

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
    });
  });

  describe('Inventory Forecast Controller Export success scenarios', () => {
    let mapResponseToColumnsStub: sinon.SinonStub;
    let generateExcelFileStub: sinon.SinonStub;

    beforeEach(() => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryForecastListingAsync.resolves(
        InventoryForecastTableController.exampleResponse,
      );

      mapResponseToColumnsStub = sinon.stub(
        ExportFormatter,
        'mapResponseToColumns',
      );
      generateExcelFileStub = sinon.stub(ExportFormatter, 'generateExcelFile');
    });

    afterEach(() => {
      mapResponseToColumnsStub.restore();
      generateExcelFileStub.restore();
    });

    it('should call "InventoryService.getInventoryForecastListingAsync" and return Excel data', async () => {
      const response = await request(app)
        .post(exportUrl)
        .send(exampleRequestWithColumn);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/sheet/);
      expect(response.headers['content-disposition']).to.match(/.xlsx/);
      expect(mapResponseToColumnsStub.calledOnce).to.be.true;
      expect(generateExcelFileStub.calledOnce).to.be.true;
    });
  });
});
