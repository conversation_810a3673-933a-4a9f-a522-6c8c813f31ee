import sinon from 'sinon';
import request from 'supertest';
import {Container, IctError, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {InventoryService} from '../../services/inventory-service.ts';
import {InventoryStorageUtilizationController} from '../../controllers/inventory-storage-utilization-controller.ts';
import {InventoryStorageUtilization} from '../../defs/inventory-storage-utilization-def.ts';

describe('InventoryStorageUtilizationController', () => {
  const url: string = '/inventory/storage/utilization';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Inventory Service. */
  let inventoryServiceStub: sinon.SinonStubbedInstance<InventoryService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Inventory StorageUtilization Controller Error scenarios', () => {
    it('should error when the service throws an error', async () => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryStorageUtilizationAsync.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app).get(url);

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWith(
        inventoryServiceStub.getInventoryStorageUtilizationAsync
      );
    });

    it('should error when the utilizationPercentage is less than 0', async () => {
      const mockQueryResponse: InventoryStorageUtilization = {
        utilizationPercentage: -1,
      };

      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryStorageUtilizationAsync.resolves(
        mockQueryResponse
      );

      const start_date = '2023-01-01T00:00:00.000Z';
      const end_date = '2023-01-02T00:00:00.000Z';

      const response = await request(app).get(url).query({
        start_date,
        end_date,
      });

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
      expect(response.body.detail).to.equal(
        `Invalid Percentage: StorageUtilization utilizationPercentage must be between 0 and 100. Got: ${mockQueryResponse.utilizationPercentage}.`
      );

      sinon.assert.calledWith(
        inventoryServiceStub.getInventoryStorageUtilizationAsync
      );
    });

    it('should error when the utilizationPercentage is more than 100', async () => {
      const mockQueryResponse: InventoryStorageUtilization = {
        utilizationPercentage: 101,
      };

      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryStorageUtilizationAsync.resolves(
        mockQueryResponse
      );

      const start_date = '2023-01-01T00:00:00.000Z';
      const end_date = '2023-01-02T00:00:00.000Z';

      const response = await request(app).get(url).query({
        start_date,
        end_date,
      });

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
      expect(response.body.detail).to.equal(
        `Invalid Percentage: StorageUtilization utilizationPercentage must be between 0 and 100. Got: ${mockQueryResponse.utilizationPercentage}.`
      );

      sinon.assert.calledWith(
        inventoryServiceStub.getInventoryStorageUtilizationAsync
      );
    });

    it('should throw a No Content IctError if no data was found', async () => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryStorageUtilizationAsync.resolves(
        undefined
      );

      const response = await request(app).get(url);

      expect(response.statusCode).to.equal(IctError.noContent().statusCode);
    });
  });

  describe('Inventory StorageUtilization Controller success scenarios', () => {
    beforeEach(() => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryStorageUtilizationAsync.resolves(
        InventoryStorageUtilizationController.exampleData
      );
    });

    it('should call "InventoryService.getInventoryStorageUtilizationAsync" and return the data', async () => {
      const response = await request(app).get(url).query({});

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        InventoryStorageUtilizationController.exampleResponse
      );

      sinon.assert.calledOnce(
        inventoryServiceStub.getInventoryStorageUtilizationAsync
      );
    });
  });
});
