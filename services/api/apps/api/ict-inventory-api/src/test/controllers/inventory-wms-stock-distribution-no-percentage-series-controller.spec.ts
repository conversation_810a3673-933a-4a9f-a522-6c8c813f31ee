import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {InventoryService} from '../../services/inventory-service.ts';
import {InventoryStockDistributionNoPercentageSeriesController} from '../../controllers/inventory-wms-stock-distribution-no-percentage-series-controller.ts';

describe('InventoryStockDistributionNoController', () => {
  const url: string = '/inventory/wms/stock/distribution/no/percentage/series';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Inventory Service. */
  let inventoryServiceStub: sinon.SinonStubbedInstance<InventoryService>;

  const startDate = new Date('2023-10-01');
  const endDate = new Date('2023-10-03');

  beforeEach(() => {
    inventoryServiceStub = sinon.createStubInstance(InventoryService);
    Container.set(InventoryService, inventoryServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('Inventory Stock Distribution No Controller Error scenarios', () => {
    it('should validate that start_date and end_date params are passed', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details.start_date.message).to.equal(
        "'start_date' is required",
      );
      expect(response.body.details.end_date.message).to.equal(
        "'end_date' is required",
      );
    });

    it('should validate that start_date and end_date params are valid ISO8601 dates', async () => {
      const response = await request(app).get(url).query({
        start_date: '20-01-01',
        end_date: '01/01/2020 00:00:00',
      });

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details.start_date.message).to.equal(
        'invalid ISO 8601 datetime format, i.e. YYYY-MM-DDTHH:mm:ss',
      );
      expect(response.body.details.end_date.message).to.equal(
        'invalid ISO 8601 datetime format, i.e. YYYY-MM-DDTHH:mm:ss',
      );
    });

    it('should validate that start_date comes before end_date', async () => {
      const response = await request(app).get(url).query({
        start_date: '2023-01-02T00:00:00Z',
        end_date: '2023-01-01T00:00:00Z',
      });

      expect(response.status).to.equal(400);
      expect(response.body.status).to.equal(400);
      expect(response.body.title).to.equal('Bad Request');
    });

    it('should error when the service throws an error', async () => {
      inventoryServiceStub.getNoInventoryPercentageHistoricalSeriesData.rejects(
        new Error('Something bad happened'),
      );

      const response = await request(app).get(url).query({
        start_date: startDate,
        end_date: endDate,
      });

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWithMatch(
        inventoryServiceStub.getNoInventoryPercentageHistoricalSeriesData,
        startDate,
        endDate,
      );
    });
  });

  describe('Inventory Stock Distribution No Controller success scenarios', () => {
    beforeEach(() => {
      inventoryServiceStub.getNoInventoryPercentageHistoricalSeriesData.resolves(
        InventoryStockDistributionNoPercentageSeriesController.exampleData,
      );
    });

    it('should call "InventoryService.getNoInventoryPercentageHistoricalSeriesData" and return the data', async () => {
      const response = await request(app).get(url).query({
        start_date: startDate,
        end_date: endDate,
      });

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        InventoryStockDistributionNoPercentageSeriesController.exampleResponse,
      );

      sinon.assert.calledOnceWithMatch(
        inventoryServiceStub.getNoInventoryPercentageHistoricalSeriesData,
        startDate,
        endDate,
      );
    });
  });
});
