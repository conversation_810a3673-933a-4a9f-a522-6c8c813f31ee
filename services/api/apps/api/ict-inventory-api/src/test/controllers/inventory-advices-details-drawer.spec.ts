import 'reflect-metadata';
import * as sinon from 'sinon';
import request from 'supertest';
import {Container, IctError, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {InventoryService} from '../../services/inventory-service.ts';
import {InventoryAdvicesDetailsController} from '../../controllers/inventory-advices-details-controller.ts';
import {
  AdviceDetailsData,
  AdvicesDetailsQueryResponse,
} from '../../defs/inventory-advices-details-drawer-def.ts';
import {formatAdviceDetails} from '../../utils/advice-details-formatter.ts';

describe('InventoryAdvicesDetailsController', () => {
  const app = appSetup(RegisterRoutes);

  afterEach(() => {
    sinon.restore();
  });

  describe('inventory route', () => {
    let appInventoryServiceStub: sinon.SinonStubbedInstance<InventoryService>;

    const adviceId: string = 'ADV-00003#1';
    const url: string = '/inventory/advices/{adviceId}/details';

    beforeEach(() => {
      appInventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, appInventoryServiceStub);
    });

    describe('GET /advices/{adviceId}/details', async () => {
      it('can be called with a correct adviceId path param', async () => {
        appInventoryServiceStub.getAdviceDetails.resolves(
          InventoryAdvicesDetailsController.exampleAdviceDetailsData
        );

        const response = await request(app).get(url);

        expect(response.status).to.equal(200);
        expect(response.headers['content-type']).to.match(/json/);
        expect(response.body).to.deep.equal(
          InventoryAdvicesDetailsController.exampleResponse
        );
        expect(
          appInventoryServiceStub.getAdviceDetails.calledOnceWith(adviceId)
        );
        expect(
          appInventoryServiceStub.getAdviceDetails.calledOnceWith(adviceId)
        );
      });

      it('throws an error when the getAdviceDetails call on store throws an error', async () => {
        const errorMessage: string = 'bad things happening';
        appInventoryServiceStub.getAdviceDetails.throws(
          IctError.badRequest(errorMessage)
        );

        const response = await request(app).get(url);

        expect(response.status).to.equal(IctError.badRequest().statusCode);
        expect(response.body.detail).to.equal(errorMessage);
        expect(
          appInventoryServiceStub.getAdviceDetails.calledOnceWith(adviceId)
        );
      });
    });

    describe('adviceDetailsData formatter', async () => {
      const adviceDetails: AdvicesDetailsQueryResponse[] =
        InventoryAdvicesDetailsController.exampleAdviceDetailsData;

      const expectedResult: AdviceDetailsData = {
        supplierId: '001',
        itemsReceived: [
          {
            handlingUnit: 'HU-00001',
            handlingUnitType: 'PALLET',
            adviceLine: 'ADV-00003#1',
            sku: 'SKU-00001',
            quantity: 10,
            packagingLevel: 'CASE',
          },
        ],
      };

      it('should return the formatted advice details', () => {
        const result = formatAdviceDetails(adviceDetails);

        expect(result).to.deep.equal(expectedResult);
      });
    });
  });
});
