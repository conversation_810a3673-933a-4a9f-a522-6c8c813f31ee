import sinon from 'sinon';
import request from 'supertest';
import {Container, EdgeDirection, Metric, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {
  ProcessFlowResponse,
  Edge,
  ProcessFlowDetailsResponse,
  UpdatePositionPayload,
  UpdatedArea,
} from '../../defs/inventory-process-flow-def.ts';
import {ProcessFlowService} from '../../services/process-flow-service.ts';

describe('InventoryProcessFlowAreaController', () => {
  const url: string = '/inventory/process-flow/areas';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Inventory Service. */
  let processFlowServiceStub: sinon.SinonStubbedInstance<ProcessFlowService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('getAllAreas', () => {
    it('should return all areas', async () => {
      const mockResponse: ProcessFlowResponse = {
        areas: [
          {
            id: '1',
            label: 'Area 1',
            metrics: [],
            position: {
              x: 500,
              y: 250,
            },
            nodeType: 'Area',
          },
        ],
        edges: [
          {
            id: 'link1',
            source: '1',
            target: '2',
            direction: EdgeDirection.Upstream,
            metrics: [],
          },
        ],
        lastProcessedTime: '1739290887.78988',
      };

      processFlowServiceStub = sinon.createStubInstance(ProcessFlowService);
      processFlowServiceStub.getAllAreas.resolves(mockResponse);
      Container.set(ProcessFlowService, processFlowServiceStub);

      const response = await request(app).get(url);

      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal(mockResponse);
    });

    it('should error when the service throws an error', async () => {
      processFlowServiceStub = sinon.createStubInstance(ProcessFlowService);
      Container.set(ProcessFlowService, processFlowServiceStub);
      processFlowServiceStub.getAllAreas.rejects(
        new Error('Something bad happened')
      );
      const response = await request(app).get(url).query({});

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWith(processFlowServiceStub.getAllAreas);
    });
  });

  describe('getGraphDetails', () => {
    const detailsUrl = '/inventory/process-flow/details';
    it('should return graph details for the given element', async () => {
      const type = 'area';
      const elementId = '1';

      const mockResponse: ProcessFlowDetailsResponse = {
        metricGroups: [
          {
            title: 'Status',
            metrics: [
              {
                panelGroup: 'Status',
                id: 'dematic:grandrapids_mi:multishuttle:inbound_totes_count:units_per_hour',
                value: 15,
                type: 'hourly',
              },
              {
                panelGroup: 'Status',
                id: 'dematic:grandrapids_mi:multishuttle:outbound_totes_count:units_per_hour',
                value: 400,
                type: 'hourly',
              },
            ],
          },
        ],
      };

      processFlowServiceStub = sinon.createStubInstance(ProcessFlowService);
      processFlowServiceStub.getDetails.resolves(mockResponse);
      Container.set(ProcessFlowService, processFlowServiceStub);

      const response = await request(app).get(
        `${detailsUrl}/${type}/${elementId}`
      );

      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal(mockResponse);
      sinon.assert.calledWith(
        processFlowServiceStub.getDetails,
        type,
        elementId
      );
    });

    it('should return an error if the service throws an error', async () => {
      const type = 'area';
      const elementId = '1';

      processFlowServiceStub = sinon.createStubInstance(ProcessFlowService);
      processFlowServiceStub.getDetails.rejects(
        new Error('Something bad happened')
      );
      Container.set(ProcessFlowService, processFlowServiceStub);

      const response = await request(app).get(
        `${detailsUrl}/${type}/${elementId}`
      );

      expect(response.status).to.equal(500);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
      sinon.assert.calledWith(
        processFlowServiceStub.getDetails,
        type,
        elementId
      );
    });
  });

  describe('updatePosition', () => {
    it('should update an area and return the updated area', async () => {
      const areaId = 'test';
      const updatedArea: UpdatedArea = {
        identity: 1,
        labels: ['Updated Area'],
        properties: {
          name: 'Updated Area',
          created_by: 'test',
          views: ['multishuttle'],
          x: 600,
          y: 300,
        },
        elementId: 'testid',
      };

      const updatePayload: UpdatePositionPayload = {
        id: areaId,
        position: {
          x: 600,
          y: 300,
        },
      };

      processFlowServiceStub = sinon.createStubInstance(ProcessFlowService);
      processFlowServiceStub.updatePosition.resolves(updatedArea);
      Container.set(ProcessFlowService, processFlowServiceStub);

      const response = await request(app)
        .put(`${url}/${areaId}`)
        .send(updatePayload);

      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal(updatedArea);
      sinon.assert.calledWith(
        processFlowServiceStub.updatePosition,
        updatePayload
      );
    });

    it('should return an error if update fails', async () => {
      const areaId = '1';
      const updatePayload: UpdatePositionPayload = {
        id: areaId,
        position: {
          x: 600,
          y: 300,
        },
      };

      processFlowServiceStub = sinon.createStubInstance(ProcessFlowService);
      processFlowServiceStub.updatePosition.rejects(new Error('Update failed'));
      Container.set(ProcessFlowService, processFlowServiceStub);

      const response = await request(app)
        .put(`${url}/${areaId}`)
        .send(updatePayload);

      expect(response.status).to.equal(500);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
      sinon.assert.calledWith(
        processFlowServiceStub.updatePosition,
        updatePayload
      );
    });
  });

  describe('updateEdge', () => {
    const edgeUrl: string = '/inventory/process-flow/edges';

    it('should update an edge and return the updated edge', async () => {
      const edgeId = 'link1';
      const updatedEdge: Edge = {
        id: edgeId,
        source: '1',
        target: '2',
        direction: EdgeDirection.Upstream,
        metrics: [],
      };

      processFlowServiceStub = sinon.createStubInstance(ProcessFlowService);
      processFlowServiceStub.updateEdge.resolves(updatedEdge);
      Container.set(ProcessFlowService, processFlowServiceStub);

      const response = await request(app)
        .put(`${edgeUrl}/${edgeId}`)
        .send(updatedEdge);

      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal(updatedEdge);
      sinon.assert.calledWith(processFlowServiceStub.updateEdge, updatedEdge);
    });

    it('should return an error if update fails', async () => {
      const edgeId = 'link1';
      const updatedEdge: Edge = {
        id: edgeId,
        source: '1',
        target: '2',
        direction: EdgeDirection.Upstream,
        metrics: [],
      };

      processFlowServiceStub = sinon.createStubInstance(ProcessFlowService);
      processFlowServiceStub.updateEdge.rejects(new Error('Update failed'));
      Container.set(ProcessFlowService, processFlowServiceStub);

      const response = await request(app)
        .put(`${edgeUrl}/${edgeId}`)
        .send(updatedEdge);

      expect(response.status).to.equal(500);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
      sinon.assert.calledWith(processFlowServiceStub.updateEdge, updatedEdge);
    });
  });

  describe('updateMetric', () => {
    const metricUrl: string = '/inventory/process-flow/metrics';

    it('should update a metric and return the updated metric', async () => {
      const metricId = 'metric1';
      const updatedMetric: Metric = {
        id: metricId,
        type: 'normal',
        value: 5,
      };

      processFlowServiceStub = sinon.createStubInstance(ProcessFlowService);
      processFlowServiceStub.updateMetric.resolves(updatedMetric);
      Container.set(ProcessFlowService, processFlowServiceStub);

      const response = await request(app)
        .put(`${metricUrl}/${metricId}`)
        .send(updatedMetric);

      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal(updatedMetric);
      sinon.assert.calledWith(
        processFlowServiceStub.updateMetric,
        updatedMetric
      );
    });

    it('should return an error if update fails', async () => {
      const metricId = 'metric1';
      const updatedMetric: Metric = {
        id: metricId,
        type: 'normal',
        value: 5,
      };

      processFlowServiceStub = sinon.createStubInstance(ProcessFlowService);
      processFlowServiceStub.updateMetric.rejects(new Error('Update failed'));
      Container.set(ProcessFlowService, processFlowServiceStub);

      const response = await request(app)
        .put(`${metricUrl}/${metricId}`)
        .send(updatedMetric);

      expect(response.status).to.equal(500);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
      sinon.assert.calledWith(
        processFlowServiceStub.updateMetric,
        updatedMetric
      );
    });
  });
});
