import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {InventoryService} from '../../services/inventory-service.ts';
import {InventoryAdvicesInProgressController} from '../../controllers/inventory-advices-in-progress-controller.ts';

describe('InventoryAdvicesInProgressController', () => {
  const url: string = '/inventory/advices/in-progress';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Inventory Service. */
  let inventoryServiceStub: sinon.SinonStubbedInstance<InventoryService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Inventory Advices In Progress Controller Error scenarios', () => {
    it('should error when the service throws an error', async () => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryAdvicesInProgress.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app).get(url);

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledOnce(
        inventoryServiceStub.getInventoryAdvicesInProgress
      );
    });
  });
  describe('Inventory Advices In Progress Controller success scenarios', () => {
    beforeEach(() => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryAdvicesInProgress.resolves(
        InventoryAdvicesInProgressController.exampleData
      );
    });

    it('should call "InventoryService.getInventoryAdvicesInProgress" and return the data', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        InventoryAdvicesInProgressController.exampleResponse
      );

      sinon.assert.calledOnce(
        inventoryServiceStub.getInventoryAdvicesInProgress
      );
    });
  });
});
