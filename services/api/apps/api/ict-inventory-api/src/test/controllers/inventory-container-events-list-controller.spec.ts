import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup, ExportFormatter} from 'ict-api-foundations';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {InventoryService} from '../../services/inventory-service.ts';
import {InventoryContainerEventsListController} from '../../controllers/inventory-container-events-list-controller.ts';
import {IncludedColumns} from '../../defs/inventory-export-def.ts';

describe('InventoryContainerEventsListController', () => {
  const containerId: string = '12345';
  const url: string = `/inventory/container-events/list/${containerId}`;
  const exportUrl: string = `/inventory/container-events/list/export/${containerId}`;

  let mapResponseToColumnsStub: sinon.SinonStub;
  let generateExcelFileStub: sinon.SinonStub;

  const includedColumns: IncludedColumns = {
    timestamp: true,
    event: true,
    workstationCode: true,
    destinationContainer: true,
    operator: true,
    sku: true,
    quantity: true,
  };

  const exampleRequestWithColumn = {
    limit: 50,
    page: 1,
    filters: {
      type: 'single',
      name: 'sku',
      comparison: 'Like',
      value: '%TBN%',
    },
    sortFields: [],
    months: 2,
    columns: includedColumns,
  };

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Inventory Service. */
  let inventoryServiceStub: sinon.SinonStubbedInstance<InventoryService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Inventory Container Events List Controller error scenarios', () => {
    it('should error when the service throws an error', async () => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryContainerEventsListAsync.rejects(
        new Error('Something bad happened'),
      );

      const response = await request(app).post(url).send({page: 1, limit: 10}); // use POST and pass a body

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledOnce(
        inventoryServiceStub.getInventoryContainerEventsListAsync,
      );
    });
  });
  describe('Inventory Container Events List Controller success scenarios', () => {
    beforeEach(() => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryContainerEventsListAsync.resolves(
        InventoryContainerEventsListController.exampleData,
      );
    });

    it('should call "InventoryService.getInventoryContainerEventDetailsListAsync" and return the data', async () => {
      const response = await request(app).post(url).send({page: 1, limit: 10}); // use POST with a body

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        InventoryContainerEventsListController.exampleResponse,
      );

      sinon.assert.calledOnce(
        inventoryServiceStub.getInventoryContainerEventsListAsync,
      );
    });
  });

  describe('Inventory Container Events List Export Controller error scenarios', () => {
    it('should error when the service throws an error', async () => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryContainerEventsListAsync.rejects(
        new Error('Something bad happened'),
      );

      const response = await request(app)
        .post(exportUrl)
        .send(exampleRequestWithColumn); // use POST and pass a body

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');
    });

    it('should validate that page and limit params are numbers', async () => {
      const response = await request(app).post(exportUrl).send({
        page: 'one',
        limit: 'two',
        columns: {},
      });

      expect(response.status).to.equal(422);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details.paginatedRequest.message).to.match(
        /invalid integer number/,
      );
      expect(response.body.details.paginatedRequest.message).to.match(/one/);
      expect(response.body.details.paginatedRequest.message).to.match(/two/);
    });
  });

  describe('Inventory Container Events List Export Controller success scenarios', () => {
    beforeEach(() => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryContainerEventsListAsync.resolves(
        InventoryContainerEventsListController.exampleData,
      );

      mapResponseToColumnsStub = sinon.stub(
        ExportFormatter,
        'mapResponseToColumns',
      );
      generateExcelFileStub = sinon.stub(ExportFormatter, 'generateExcelFile');
    });

    afterEach(() => {
      mapResponseToColumnsStub.restore();
      generateExcelFileStub.restore();
    });

    it('should call "InventoryService.getInventoryContainerEventDetailsListAsync" and return the data', async () => {
      const response = await request(app)
        .post(exportUrl)
        .send(exampleRequestWithColumn); // use POST with a body

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/sheet/);
      expect(response.headers['content-disposition']).to.match(/.xlsx/);
      expect(mapResponseToColumnsStub.calledOnce).to.be.true; // Ensure the spy was called
      expect(generateExcelFileStub.calledOnce).to.be.true; // Ensure the spy was called
    });
  });
});
