import request from 'supertest';
import {appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';

// TODO - testing this controller has been unsuccessful due to the way TSOA + Multer
// interact with dependency injection. We should investigate again once we use
// the TSOA iocModule feature: https://tsoa-community.github.io/docs/di.html
describe('InventoryUploadKnownDemandController', () => {
  const app = appSetup(RegisterRoutes);

  describe('All test requests return 401 due to auth', () => {
    const url = '/inventory/upload/known-demand';
    it('should return 401', async () => {
      const response = await request(app)
        .post(url)
        .attach('manager', Buffer.from('file content'), 'manager.xls')
        .attach('details', Buffer.from('file content'), 'details.xls');

      expect(response.status).to.equal(401);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(401);
      expect(response.body.title).to.equal('Unauthorized');
    });
  });

  describe('All test requests return 401 due to auth, even through a different url', () => {
    const url = '/inventory/upload/recent-activity';
    it('should return 401', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(401);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(401);
      expect(response.body.title).to.equal('Unauthorized');
    });
  });
});
