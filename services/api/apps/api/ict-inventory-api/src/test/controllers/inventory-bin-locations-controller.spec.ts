import sinon from 'sinon';
import request from 'supertest';
import {Container, IctError, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {InventoryService} from '../../services/inventory-service.ts';
import {InventoryBinLocationsController} from '../../controllers/inventory-bin-locations-controller.ts';

describe('InventoryBinLocationsController', () => {
  const url: string = '/inventory/bin-locations';
  const aisle = '01';
  const level = '01';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Inventory Service. */
  let inventoryServiceStub: sinon.SinonStubbedInstance<InventoryService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Inventory Bin Locations Controller Error scenarios', () => {
    it('should validate that aisle and level params are passed', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details.aisle.message).to.equal(
        "'aisle' is required"
      );
      expect(response.body.details.level.message).to.equal(
        "'level' is required"
      );
    });
    it('should error when the service throws an error', async () => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryBinLocations.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app).get(url).query({
        aisle,
        level,
      });

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWith(inventoryServiceStub.getInventoryBinLocations);
    });
  });
  describe('Inventory Bin Locations Controller success scenarios', () => {
    beforeEach(() => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryBinLocations.resolves(
        InventoryBinLocationsController.exampleData
      );
    });

    it('should throw a No Content IctError if no data was found', async () => {
      inventoryServiceStub.getInventoryBinLocations.resolves([]);

      const response = await request(app).get(url).query({
        aisle,
        level,
      });
      expect(response.status).to.equal(IctError.noContent().statusCode);
    });

    it('should call the inventory service and return the data', async () => {
      const response = await request(app).get(url).query({
        aisle,
        level,
      });

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        InventoryBinLocationsController.exampleResponse
      );

      sinon.assert.calledWith(inventoryServiceStub.getInventoryBinLocations);
    });
  });
});
