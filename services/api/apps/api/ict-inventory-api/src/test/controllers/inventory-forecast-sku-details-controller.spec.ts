import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {InventoryService} from '../../services/inventory-service.ts';
import {InventoryForecastSkuDetailsController} from '../../controllers/inventory-forecast-sku-details-controller.ts';

describe('InventoryForecastSkuLocationDetailsController', () => {
  const url: string = '/inventory/forecast/{skuId}/locations';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Inventory Service. */
  let inventoryServiceStub: sinon.SinonStubbedInstance<InventoryService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Inventory Forecast Sku Location Details Controller Error scenarios', () => {
    it('should error when the service throws an error', async () => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryForecastSkuLocations.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app).get(url);

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWith(
        inventoryServiceStub.getInventoryForecastSkuLocations
      );
    });
  });
  describe('Inventory Forecast Sku Details Location Controller success scenarios', () => {
    beforeEach(() => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryForecastSkuLocations.resolves({
        data: [
          InventoryForecastSkuDetailsController.exampleReserveArea,
          InventoryForecastSkuDetailsController.examplePickArea,
        ],
      });
    });

    it('should call "InventoryServiceStub.getInventoryForecastSkuLocations" and return the data', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        InventoryForecastSkuDetailsController.exampleLocationResponse
      );

      sinon.assert.calledWith(
        inventoryServiceStub.getInventoryForecastSkuLocations
      );
    });
  });
});

describe('InventoryForecastSkuOrderDetailsController', () => {
  const url: string = '/inventory/forecast/{skuId}/orders';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Inventory Service. */
  let inventoryServiceStub: sinon.SinonStubbedInstance<InventoryService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Inventory Forecast Sku Order Details Controller Error scenarios', () => {
    it('should error when the service throws an error', async () => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryForecastSkuOrders.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app).get(url);

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWith(
        inventoryServiceStub.getInventoryForecastSkuOrders
      );
    });
  });
  describe('Inventory Forecast Sku Details Order Controller success scenarios', () => {
    beforeEach(() => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryForecastSkuOrders.resolves({
        skuId: 'SKU123',
        openOrderCount: 2,
        data: [
          InventoryForecastSkuDetailsController.exampleOrderOneData,
          InventoryForecastSkuDetailsController.exampleOrderTwoData,
        ],
      });
    });

    it('should call "InventoryServiceStub.getInventoryForecastSkuOrders" and return the data', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        InventoryForecastSkuDetailsController.exampleOrderResponse
      );

      sinon.assert.calledWith(
        inventoryServiceStub.getInventoryForecastSkuOrders
      );
    });
  });
});

describe('InventoryForecastSkuForecastDetailsController', () => {
  const url: string = '/inventory/forecast/{skuId}';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Inventory Service. */
  let inventoryServiceStub: sinon.SinonStubbedInstance<InventoryService>;

  afterEach(() => {
    sinon.restore();
  });

  describe('Inventory Forecast Sku Forecast Details Controller Error scenarios', () => {
    it('should error when the service throws an error', async () => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryForecastSkuForecastDetails.rejects(
        new Error('Something bad happened')
      );

      const response = await request(app).get(url);

      expect(response.status).to.equal(500);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body.status).to.equal(500);
      expect(response.body.title).to.equal('Internal Server Error');

      sinon.assert.calledWith(
        inventoryServiceStub.getInventoryForecastSkuForecastDetails
      );
    });
  });
  describe('Inventory Forecast Sku Details Forecast Controller success scenarios', () => {
    beforeEach(() => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryForecastSkuForecastDetails.resolves({
        confidence: 10,
        knownDemand:
          InventoryForecastSkuDetailsController.exampleKnownDemandData,
        shortTermDaily: 180,
        shortTermDailyDays: 88,
        longTermDaily: 178,
        longTermDailyDays: 360,
        nonZeroDemand: 60,
        zeroDemandIntermittency: 3,
        predictedDemandSeries:
          InventoryForecastSkuDetailsController.examplePredictedDemandSeriesData,
        actualDemandSeries:
          InventoryForecastSkuDetailsController.exampleActualDemandSeriesData,
        confidenceLowSeries:
          InventoryForecastSkuDetailsController.exampleConfidenceLowSeriesData,
        confidenceHighSeries:
          InventoryForecastSkuDetailsController.exampleConfidenceHighSeriesData,
      });
    });

    it('should call "InventoryServiceStub.getInventoryForecastSkuOrders" and return the data', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        InventoryForecastSkuDetailsController.exampleForecastResponse
      );

      sinon.assert.calledWith(
        inventoryServiceStub.getInventoryForecastSkuForecastDetails
      );
    });
  });
});
