import sinon from 'sinon';
import request from 'supertest';
import {Container, appSetup} from 'ict-api-foundations';
import {expect} from 'chai';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../../../build/routes.ts';
import {InventoryService} from '../../services/inventory-service.ts';
import {InventoryPerformanceSeriesController} from '../../controllers/inventory-performance-series-controller.ts';

describe('InventoryPerformanceSeriesController', () => {
  const url: string = '/inventory/performance/series';

  const app = appSetup(RegisterRoutes);

  /** Stub instance for the Inventory Service. */
  let inventoryServiceStub: sinon.SinonStubbedInstance<InventoryService>;

  const start_date = '2023-01-01T00:00:00.000Z';
  const end_date = '2023-01-02T00:00:00.000Z';
  const department_filter = 'Dept1';

  afterEach(() => {
    sinon.restore();
  });

  describe('Inventory PerformanceSeries Controller Error scenarios', () => {
    it('should validate that start_date and end_date params are passed', async () => {
      const response = await request(app).get(url);

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details.start_date.message).to.equal(
        "'start_date' is required"
      );
      expect(response.body.details.end_date.message).to.equal(
        "'end_date' is required"
      );
      expect(response.body.details.department_filter.message).to.equal(
        "'department_filter' is required"
      );
    });

    it('should validate that start_date and end_date params are valid ISO8601 dates', async () => {
      const response = await request(app).get(url).query({
        start_date: '20-01-01',
        end_date: '01/01/2020 00:00:00',
        department_filter,
      });

      expect(response.status).to.equal(422);
      expect(response.body.message).to.equal('Validation Failed');
      expect(response.body.details.start_date.message).to.equal(
        'invalid ISO 8601 datetime format, i.e. YYYY-MM-DDTHH:mm:ss'
      );
      expect(response.body.details.end_date.message).to.equal(
        'invalid ISO 8601 datetime format, i.e. YYYY-MM-DDTHH:mm:ss'
      );
    });

    it('should validate that start_date comes before end_date', async () => {
      const response = await request(app).get(url).query({
        start_date: '2023-01-02T00:00:00Z',
        end_date: '2023-01-01T00:00:00Z',
        department_filter,
      });

      expect(response.status).to.equal(400);
      expect(response.body.status).to.equal(400);
      expect(response.body.title).to.equal('Bad Request');
    });

    it('should error when a service throws an error', async () => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      const serviceMocks = [
        inventoryServiceStub.getInventoryAccuracyMonthlyAggregatedSeries,
        inventoryServiceStub.getOrderFulfillmentRateMonthlyAggregatedSeries,
        inventoryServiceStub.getStorageUtilizationMonthlyAggregatedSeries,
        inventoryServiceStub.getStockOutMonthlyAggregatedSeries,
      ];

      // Setup each service to fail on it's respective call but succeed on all other calls.
      for (let i: number = 0; i < serviceMocks.length; i++) {
        serviceMocks.forEach((serviceMock, si) => {
          if (si === i) {
            serviceMock.onCall(i).rejects(new Error('Something bad happened'));
          } else {
            serviceMock
              .onCall(i)
              .resolves(InventoryPerformanceSeriesController.exampleData[si]);
          }
        });
      }

      // Test that each individual service failing causes the api to fail.
      let response;
      for (const serviceMock of serviceMocks) {
        // eslint-disable-next-line no-await-in-loop
        response = await request(app).get(url).query({
          start_date,
          end_date,
          department_filter,
        });
        expect(response.status).to.equal(500);
        expect(response.headers['content-type']).to.match(/json/);
        expect(response.body.status).to.equal(500);
        expect(response.body.title).to.equal('Internal Server Error');

        sinon.assert.calledWithMatch(serviceMock, {
          startDate: start_date,
          endDate: end_date,
          departmentFilter: department_filter,
        });
      }
    });
  });

  describe('Inventory PerformanceSeries Controller success scenarios', () => {
    beforeEach(() => {
      inventoryServiceStub = sinon.createStubInstance(InventoryService);
      Container.set(InventoryService, inventoryServiceStub);
      inventoryServiceStub.getInventoryAccuracyMonthlyAggregatedSeries.resolves(
        InventoryPerformanceSeriesController.exampleInventoryAccuracyData
      );
      inventoryServiceStub.getOrderFulfillmentRateMonthlyAggregatedSeries.resolves(
        InventoryPerformanceSeriesController.exampleOrderFulfillmentData
      );
      inventoryServiceStub.getStorageUtilizationMonthlyAggregatedSeries.resolves(
        InventoryPerformanceSeriesController.exampleStorageUtilizationData
      );
      inventoryServiceStub.getStockOutMonthlyAggregatedSeries.resolves(
        InventoryPerformanceSeriesController.exampleStockOutData
      );
    });

    it('should call "InventoryService.getInventoryPerformanceSeriesAsync" and return the data', async () => {
      const response = await request(app).get(url).query({
        start_date,
        end_date,
        department_filter,
      });

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/json/);
      expect(response.body).to.deep.equal(
        InventoryPerformanceSeriesController.exampleResponse
      );

      sinon.assert.calledWithMatch(
        inventoryServiceStub.getInventoryAccuracyMonthlyAggregatedSeries,
        {
          startDate: start_date,
          endDate: end_date,
          departmentFilter: department_filter,
        }
      );
      sinon.assert.calledWithMatch(
        inventoryServiceStub.getOrderFulfillmentRateMonthlyAggregatedSeries,
        {
          startDate: start_date,
          endDate: end_date,
          departmentFilter: department_filter,
        }
      );
      sinon.assert.calledWithMatch(
        inventoryServiceStub.getStorageUtilizationMonthlyAggregatedSeries,
        {
          startDate: start_date,
          endDate: end_date,
          departmentFilter: department_filter,
        }
      );
      sinon.assert.calledWithMatch(
        inventoryServiceStub.getStockOutMonthlyAggregatedSeries,
        {
          startDate: start_date,
          endDate: end_date,
          departmentFilter: department_filter,
        }
      );
    });
  });
});
