import {expect} from 'chai';
import {InventoryAreaFilterDefinition} from '../../defs/inventory-area-filter-def.ts';

describe('InventoryAreaFilterDefinition', () => {
  it('should have a valid areaFilterTable property', () => {
    // Create a sample area filter table
    const areaFilterDefinition: InventoryAreaFilterDefinition = {
      areaFilterTable: ['All', 'Automated', 'Manual'],
    };

    expect(areaFilterDefinition).to.have.property('areaFilterTable');
  });

  it('should have an array of strings for the areaFilterTable property', () => {
    const areaFilterDefinition: InventoryAreaFilterDefinition = {
      areaFilterTable: ['All', 'Automated', 'Manual'],
    };

    expect(areaFilterDefinition.areaFilterTable).to.be.an('array');
    expect(
      areaFilterDefinition.areaFilterTable.every(
        (area: string) => typeof area === 'string'
      )
    ).to.be.true;
  });
});
