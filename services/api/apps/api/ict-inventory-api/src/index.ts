import express from 'express';

import {
  ApiMiddleware,
  Container,
  EnvironmentService,
  WinstonLogger,
} from 'ict-api-foundations';
import 'reflect-metadata';
// eslint-disable-next-line n/no-unpublished-import
import {RegisterRoutes} from '../build/routes.ts';

const app = express();
// setup middlewares
app.use(
  express.urlencoded({
    extended: true,
  })
);
app.use(express.json());
ApiMiddleware.applyApiDefaultMiddlewares(app);

RegisterRoutes(app);

ApiMiddleware.applyErrorMiddlewares(app);

// get the logger from DI
const logger = Container.get(WinstonLogger);
const envService = Container.get(EnvironmentService);
const port = envService.ports.inventory || 8080;
app.listen(port, () =>
  logger.info(`Inventory API listening on local port ${port}.`)
);
