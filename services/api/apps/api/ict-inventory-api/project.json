{"name": "ict-inventory-api", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/ict-inventory-api/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "ict-inventory-api:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "ict-inventory-api:build:development"}, "production": {"buildTarget": "ict-inventory-api:build:production"}}}}}