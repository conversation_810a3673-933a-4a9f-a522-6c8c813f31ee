{"name": "ict-typeorm-migrations-cr", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/ict-typeorm-migrations-cr/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "ict-typeorm-migrations-cr:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "ict-typeorm-migrations-cr:build:development"}, "production": {"buildTarget": "ict-typeorm-migrations-cr:build:production"}}}}}