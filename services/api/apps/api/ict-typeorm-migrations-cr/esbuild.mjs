import * as esbuild from 'esbuild';
import esbuildPluginTsc from 'esbuild-plugin-tsc';
import {copyFileSync} from 'fs';

await esbuild.build({
  bundle: true,
  entryPoints: ['./src/index.ts'],
  format: 'cjs',
  logLevel: 'info',
  outfile: './build/index.cjs',
  platform: 'node',
  plugins: [
    // Allows supporting `emitDecoratorMetadata`
    esbuildPluginTsc(),
  ],
  sourcemap: true,
  target: 'node18',
});

// add ict-api-schema datasources to be built
await esbuild.build({
  bundle: true,
  entryPoints: ['../../../libs/api/ict-api-schema/src/datasources/**/*.ts'],
  format: 'cjs',
  logLevel: 'info',
  outdir: './build',
  outExtension: {
    '.js': '.cjs',
  },
  platform: 'node',
  plugins: [
    // Allows supporting `emitDecoratorMetadata`
    esbuildPluginTsc(),
  ],
  sourcemap: true,
  target: 'node18',
});

copyFileSync(
  '../../../libs/api/ict-api-schema/src/default-data/config/sql/insert-default-settings.sql',
  './build/insert-default-settings.sql'
);

copyFileSync(
  '../../../libs/api/ict-api-schema/src/default-data/process-flow/sql/insert-default-metric-configs.sql',
  './build/insert-default-metric-configs.sql'
);
