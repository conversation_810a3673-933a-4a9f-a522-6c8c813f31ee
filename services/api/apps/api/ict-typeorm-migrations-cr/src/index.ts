// needed for dependency injection
import 'reflect-metadata';

import express from 'express';
import {
  <PERSON><PERSON><PERSON>,
  WinstonLogger,
  EnvironmentService,
} from 'ict-api-foundations';

const app = express();
const envService = Container.get(EnvironmentService);

// get the logger from DI
const logger = Container.get(WinstonLogger);

app.get('*', async (_req, res) => {
  res.status(200).send('ict-typeorm-migrations-cr container is active');
});

const port = envService.ports.migrations || 8080;
app.listen(port, () =>
  logger.info(`ICT Migrations listening on local port ${port}.`),
);
