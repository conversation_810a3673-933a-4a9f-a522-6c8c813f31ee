import pg from 'pg';
import {Environment} from '@ict/sdk-foundations/types/index.ts';
import {Database} from './database.ts';
import {DatabaseTypes} from './db-types.ts';
import {Container} from '../di/type-di.ts';
import {<PERSON><PERSON>ogger} from '../log/winston-logger.ts';
import {EnvironmentService} from '../services/environment-service.ts';
import {GCPSecretManager} from '../secrets/gcp-secret-manager.ts';

export interface PgPostgresDatabaseConnectionOptions {
  database: string;
  user: string;
  password: string;
  host: string;
  port: number;
}

export class PgPostgresDatabase extends Database {
  private logger: WinstonLogger;
  private pool: pg.Pool;

  constructor(options: PgPostgresDatabaseConnectionOptions) {
    super();
    this.logger = Container.get(WinstonLogger);

    this.pool = new pg.Pool({
      user: options.user,
      host: options.host,
      database: options.database,
      password: options.password,
      port: options.port,
    });
  }

  public async query<T extends pg.QueryResultRow>(
    text: string,
    params?: unknown[],
  ): Promise<pg.QueryResult<T>> {
    const start = Date.now();
    const res = await this.pool.query<T>(text, params);
    const duration = Date.now() - start;
    this.logger.info('PgPostgresDatabase: executed query', {
      text,
      duration,
      rows: res.rowCount,
    });

    return res;
  }

  public getType(): string {
    return DatabaseTypes.PgPostgres;
  }

  public static async generateDefaultPgPostgresOptions(
    database: string,
    secretName?: string,
    secretProjectId?: string,
  ): Promise<PgPostgresDatabaseConnectionOptions> {
    const envService = Container.get(EnvironmentService);

    const nodeEnv = envService.app.env;
    if (!nodeEnv || nodeEnv !== Environment.local) {
      const secretManager = Container.get(GCPSecretManager);
      if (!secretManager) {
        throw new Error(
          'A secret manager is required for connecting to a postgres db in this environment.',
        );
      }

      const defaultSecretName = envService.postgresDb.pgGcpSecretName || '';
      const query = Database.generateDefaultSecretQuery(
        secretName ?? defaultSecretName,
        secretProjectId,
      );
      const secretString = await secretManager.get(query);
      const options = JSON.parse(
        secretString,
      ) as PgPostgresDatabaseConnectionOptions;

      // validate the options
      if (
        !options ||
        typeof options.user !== 'string' ||
        typeof options.password !== 'string' ||
        typeof options.host !== 'string' ||
        typeof options.port !== 'number'
      ) {
        throw new Error(
          'Invalid Postgres database options retrieved from secret manager.',
        );
      }

      return {
        ...options,
        database,
      };
    }

    const portStr = envService.postgresDb.port;
    const port = portStr ? parseInt(portStr, 10) : 5432;
    return {
      database,
      user: envService.postgresDb.username || 'postgres',
      password: envService.postgresDb.password || 'devpassword',
      host: envService.postgresDb.host || 'localhost',
      port,
    };
  }
}
