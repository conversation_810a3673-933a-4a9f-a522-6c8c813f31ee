import {ContextService} from '../context/context-service.ts';
import {Container} from '../di/type-di.ts';
import {EnvironmentService} from '../services/environment-service.ts';
import {
  DatabaseOptions,
  DatabaseOptionTemplates,
  DatabaseTypes,
  DBMiddlewareDatabaseOption,
  PgPostgresDatabaseOptions,
} from './db-types.ts';

export const preparePgPostgresDatabaseOptions = (
  options?: DatabaseOptions,
): PgPostgresDatabaseOptions | undefined => {
  const pgPostgresOptions = options as PgPostgresDatabaseOptions | undefined;
  const contextService = Container.get(ContextService);
  const envService = Container.get(EnvironmentService);

  // secretName should be using the datasetId (facility specific) for System Availability
  let secretName = pgPostgresOptions?.secretName;
  if (secretName) {
    secretName = secretName.replace(
      DatabaseOptionTemplates.DatasetId,
      contextService.datasetId,
    );
  }

  let projectId = pgPostgresOptions?.secretProjectId;
  if (projectId) {
    projectId = projectId.replace(
      DatabaseOptionTemplates.Env_AvailabilityProject,
      envService.systemAvailability.projectId ?? '',
    );
  }

  let newOptions: PgPostgresDatabaseOptions | undefined = options;
  if (options) {
    newOptions = {
      ...options,
      secretName,
      secretProjectId: projectId,
    };
  }

  return newOptions;
};

export const systemAvailabilityDatabase = (): DBMiddlewareDatabaseOption => {
  const dbOptions: PgPostgresDatabaseOptions = {
    database: `${DatabaseOptionTemplates.DatasetId}_availability`,
    secretName: `${DatabaseOptionTemplates.DatasetId}_ct_availability_database_json`,
    secretProjectId: DatabaseOptionTemplates.Env_AvailabilityProject,
    prepareOptions: preparePgPostgresDatabaseOptions,
  };

  return {
    type: DatabaseTypes.PgPostgres,
    options: dbOptions,
  };
};
