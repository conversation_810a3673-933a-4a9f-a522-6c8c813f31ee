import {
  HttpStatusCodes,
  HttpStatusTitles,
} from '@ict/sdk-foundations/types/defs/errors-def.ts';

export class IctError extends Error {
  public message: string;
  public statusCode: number;
  public attributes?: {[key: string]: unknown};
  public errorType?: string;
  public error: unknown;

  constructor(statusCode: number, message: string, error?: unknown) {
    super(message);
    this.statusCode = statusCode;
    this.message = message;
    this.error = error;
  }

  /**
   * Static helper functions to create HTTP status specific error responses
   */

  public static badRequest(message?: string, error?: unknown) {
    return new IctError(
      HttpStatusCodes.BAD_REQUEST,
      message ?? HttpStatusTitles.BAD_REQUEST,
      error
    );
  }

  public static notFound(message?: string, error?: unknown) {
    return new IctError(
      HttpStatusCodes.NOT_FOUND,
      message ?? HttpStatusTitles.NOT_FOUND,
      error
    );
  }

  public static internalServerError(message?: string, error?: unknown) {
    return new IctError(
      HttpStatusCodes.INTERNAL_SERVER_ERROR,
      message ?? HttpStatusTitles.INTERNAL_SERVER_ERROR,
      error
    );
  }

  public static badGateway(message?: string, error?: unknown) {
    return new IctError(
      HttpStatusCodes.BAD_GATEWAY,
      message ?? HttpStatusTitles.BAD_GATEWAY,
      error
    );
  }

  public static conflict(message?: string, error?: unknown) {
    return new IctError(
      HttpStatusCodes.CONFLICT,
      message ?? HttpStatusTitles.CONFLICT,
      error
    );
  }

  public static noContent(message?: string, error?: unknown) {
    return new IctError(
      HttpStatusCodes.NO_CONTENT,
      message ?? HttpStatusTitles.NO_CONTENT,
      error
    );
  }

  public static forbidden(message?: string, error?: unknown) {
    return new IctError(
      HttpStatusCodes.FORBIDDEN,
      message ?? HttpStatusTitles.FORBIDDEN,
      error
    );
  }

  public static unauthorized(message?: string, error?: unknown) {
    return new IctError(
      HttpStatusCodes.UNAUTHORIZED,
      message ?? HttpStatusTitles.UNAUTHORIZED,
      error
    );
  }

  public static notModified(message?: string, error?: unknown) {
    return new IctError(
      HttpStatusCodes.NOT_MODIFIED,
      message ?? HttpStatusTitles.NOT_MODIFIED,
      error
    );
  }

  public static notImplemented(message?: string, error?: unknown) {
    return new IctError(
      HttpStatusCodes.NOT_IMPLEMENTED,
      message ?? HttpStatusTitles.NOT_IMPLEMENTED,
      error
    );
  }

  public static unprocessableRequest(message?: string, error?: unknown) {
    return new IctError(
      HttpStatusCodes.UNPROCESSABLE,
      message ?? HttpStatusTitles.UNPROCESSABLE_REQUEST,
      error
    );
  }
}
