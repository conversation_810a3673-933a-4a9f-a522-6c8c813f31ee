import {BaseFilterType, PaginatedRequestNoDates, SortField} from '../..';

export interface ExtractedPaginatedRequestNoDates {
  page: number;
  limit: number;
  sortFields: SortField[];
  filters?: BaseFilterType;
  searchString?: string;
}

/**
 * Extracts pagination information from a paginated request object.
 * Transforms sort and filter fields into ones BigQuery can handle.
 * While providing defaults for any missing or undefined fields.
 *
 * @param paginatedRequest Pagination request object
 * @param userProvidedDefaults Optional defaults
 * @returns extracted pagination information
 */
export function extractPaginatedRequestNoDates(
  paginatedRequest: PaginatedRequestNoDates,
  userProvidedDefaults?: {
    page?: number;
    limit?: number;
  },
): ExtractedPaginatedRequestNoDates {
  // Set defualts but allow user to override
  const defaults = {
    page: 0,
    limit: 50,
    ...userProvidedDefaults,
  };
  return {
    page: paginatedRequest.page ?? defaults.page,
    limit: paginatedRequest.limit ?? defaults.limit,
    sortFields: paginatedRequest.sortFields
      ? updateSortFields(paginatedRequest.sortFields)
      : [],
    filters: paginatedRequest.filters
      ? updateFilter(paginatedRequest.filters)
      : undefined,
    searchString: paginatedRequest.searchString,
  };
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function updateFilter(filter: any): BaseFilterType {
  if (filter.type === 'single') {
    const singleFilter = filter;
    const dotIndex = singleFilter.name.indexOf('.');
    if (dotIndex !== -1) {
      singleFilter.name = singleFilter.name.substring(dotIndex + 1);
    }
    return singleFilter;
  }

  const treeFilter = filter;
  treeFilter.left = updateFilter(treeFilter.left);
  treeFilter.right = updateFilter(treeFilter.right);
  return treeFilter;
}

export function updateSortFields(sortFields: SortField[]): SortField[] {
  return sortFields.map(sortField => {
    const updatedSortField = {...sortField};
    const dotIndex = updatedSortField.columnName.indexOf('.');
    if (dotIndex !== -1) {
      const field = updatedSortField.columnName.substring(dotIndex + 1);
      updatedSortField.columnName = field;
    }
    return updatedSortField;
  });
}
