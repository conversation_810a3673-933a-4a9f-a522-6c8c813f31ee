import {
  BaseFilterType,
  FilterObjectParameters,
  SingleFilterObject,
  SortField,
  TreeFieldFilterObject,
} from '@ict/sdk-foundations/types/index.ts';
import {FilterObject} from '../../defs/db/filter-fields.ts';
import {
  FilterSortingSelectQueryParams,
  InvalidColumnNameError,
  SQLFieldNames,
  SQLQueryTemplate,
  SQLSelectColumn,
  SortingSelectQueryParams,
  comparisonReturn,
} from './sql-query-types.ts';
import {Database} from '../../db/database.ts';
import {SQLBaseFieldConversionResult} from '../../defs/db/filter-def.ts';
import {
  SQLComparison,
  SQLFilterField,
  SQLFilterFieldTree,
} from '../../defs/db/sql-filter-fields.ts';
import {PaginatedResults} from '../../defs/db/paginated-results.ts';

export abstract class SQLQueryGenerator {
  public static async paginatedQuery<T>(
    sqlQueryGenerator: SQLQueryGenerator,
    database: Database,
    params: SortingSelectQueryParams,
    baseFilters?: BaseFilterType,
    paramValues?: FilterObjectParameters,
  ): Promise<PaginatedResults<T[]>> {
    const rows = await sqlQueryGenerator.executeSelectQuery(
      database,
      params,
      baseFilters,
      paramValues,
    );
    if (rows && rows.length) {
      // every row has the totalResult, but just need it from one
      const totalResults = rows[0][SQLFieldNames.TotalResultFieldName] ?? 0;
      const paginatedResults: PaginatedResults<T[]> = {
        // remove the totalResultFieldName from the results
        list: rows.map(
          ({
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            [SQLFieldNames.TotalResultFieldName]: resultCount,
            ...dataRows
          }) => dataRows,
        ) as T[],
        totalResults,
      };

      return paginatedResults;
    }

    return {list: [], totalResults: 0};
  }

  public async executeSelectQuery(
    database: Database,
    params: SortingSelectQueryParams,
    baseFilters?: BaseFilterType,
    paramValues?: FilterObjectParameters,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ): Promise<any[]> {
    let sqlParams: FilterSortingSelectQueryParams = {
      ...params,
    };
    if (baseFilters) {
      const result = this.baseFieldConversion(baseFilters);
      sqlParams = {
        ...sqlParams,
        filters: result.filter,
      };

      if (paramValues) {
        paramValues = {...result.filterParams, ...paramValues};
      } else {
        paramValues = result.filterParams;
      }
    }
    const sql = this.generateSelectQuery(sqlParams);
    return this.executeQuery(database, sql, paramValues);
  }

  public generateSelectQuery(params: FilterSortingSelectQueryParams): string {
    const filtersTemplate =
      params.filters || (params.searchString && params.searchableColumns)
        ? `${SQLQueryTemplate.Filters}`
        : '';
    const sortingTemplate = params.sortFields
      ? `${SQLQueryTemplate.Sorting}`
      : '';
    const limitTemplate = params.limit ? `${SQLQueryTemplate.Limit}` : '';
    const groupTemplate = params.groupByColumns
      ? `${SQLQueryTemplate.Grouping}`
      : '';
    const offsetTemplate =
      params.page && params.limit ? `${SQLQueryTemplate.Offset}` : '';
    const templates = `${filtersTemplate}${
      filtersTemplate.length ? ' ' : ''
    }${groupTemplate}${
      groupTemplate.length ? ' ' : ''
    }${sortingTemplate}${sortingTemplate.length ? ' ' : ''}${limitTemplate}${
      limitTemplate.length ? ' ' : ''
    }${offsetTemplate}${offsetTemplate.length ? ' ' : ''}`;

    if (params.selectColumns) {
      SQLQueryGenerator.validateSelectColumns(
        params.selectColumns,
        params.allowedColumnNames,
      );
    }
    const selectColumns = params.selectColumns
      ? params.selectColumns
          .map(ele =>
            ele.getSelectString(
              params.allowedColumnNames,
              params.distinctColumns,
            ),
          )
          .join(', ')
      : '*';

    const templateSQL = `${
      params.cteSubquery ? `${params.cteSubquery} ` : ''
    }SELECT ${selectColumns}, COUNT(*) OVER() as ${this.getTotalResultsFieldName()} FROM ${params.selectFromTable} ${templates}`;

    const filteredSQL =
      params.filters || params.searchString
        ? SQLQueryGenerator.addFilters(
            templateSQL,
            params.allowedColumnNames,
            params.filters,
            params.searchString,
            params.searchableColumns,
          )
        : templateSQL;
    const groupedSQL = params.groupByColumns
      ? SQLQueryGenerator.addGrouping(
          filteredSQL,
          params.groupByColumns,
          params.allowedColumnNames,
        )
      : filteredSQL;
    const sortedSQL = params.sortFields
      ? SQLQueryGenerator.addSorting(
          groupedSQL,
          params.sortFields,
          params.allowedColumnNames,
        )
      : groupedSQL;
    const limitedSQL = params.limit
      ? this.addLimit(sortedSQL, params.limit)
      : sortedSQL;
    const offsetSQL =
      params.page && params.limit
        ? this.addOffset(limitedSQL, params.page, params.limit)
        : limitedSQL;

    return offsetSQL;
  }

  protected static validateSelectColumns(
    selectColumns: SQLSelectColumn[],
    allowedColumnNames: string[],
  ): void {
    for (const selectColumn of selectColumns) {
      SQLQueryGenerator.validateColumnName(
        selectColumn.name,
        allowedColumnNames,
      );
      if (selectColumn.alias) {
        SQLQueryGenerator.validateColumnName(
          selectColumn.alias,
          allowedColumnNames,
        );
      }
    }
  }

  /**
   * Validates that the provided filter field(s) contain allowed column names and throws an InvalidColumnNameError if not.
   * @param filters Either single filter field or filter field tree to validate column names for.
   * @param allowedColumnNames List of column names that the filter field can be on.
   */
  public static validateFilterColumnNames(
    filters: FilterObject,
    allowedColumnNames: string[],
  ) {
    const columnNames = filters.getColumnNames();
    SQLQueryGenerator.validateColumnNames(columnNames, allowedColumnNames);
  }

  protected static validateColumnNames(
    columnNames: string[],
    allowedColumnNames: string[],
  ) {
    for (const colName of columnNames) {
      SQLQueryGenerator.validateColumnName(colName, allowedColumnNames);
    }
  }

  public static validateColumnName(
    columnName: string,
    allowedColumnNames: string[],
  ): void {
    const lowerTest = columnName.toLowerCase();
    const isAllowed =
      allowedColumnNames.filter(ele => ele.toLowerCase() === lowerTest).length >
      0;
    if (!isAllowed) {
      throw new InvalidColumnNameError(
        `${columnName} is not a valid column name.`,
      );
    }
  }

  /**
   * Removes the given filterName from the given filters, restructuring the filters as necessary
   * @param filters
   * @param filterName Filter to be removed
   * @returns New set of filters with given filterName removed
   */
  public static removeFilter(
    filters: TreeFieldFilterObject | SingleFilterObject,
    filterName: string,
  ): TreeFieldFilterObject | SingleFilterObject | undefined {
    // If filter is multiple, then check if left and right is single
    //  If a side is single, search for filterName and remove if its a match, if side is multiple then recurse
    // If filter is single, then check for match and remove if it is a match

    if (filters.type === 'multiple') {
      filters = filters as TreeFieldFilterObject;
      if (filters.left.type === 'multiple') {
        const newLeftFilters = this.removeFilter(
          filters.left as TreeFieldFilterObject,
          filterName,
        );

        // if the entire left side was removed, use the right side as the filter base
        if (!newLeftFilters) {
          return this.removeFilter(filters.right, filterName);
        }
        filters.left = newLeftFilters;
      } else {
        // left side is single
        filters.left = filters.left as SingleFilterObject;
        if (filters.left.name === filterName) {
          // match found, remove left side and keep searching
          return this.removeFilter(filters.right, filterName);
        }
      }
      if (filters.right.type === 'multiple') {
        const newRightFilters = this.removeFilter(filters.right, filterName);

        // if the entire right side was removed, use the left side as the filter base
        if (!newRightFilters) {
          return this.removeFilter(filters.left, filterName);
        }
        filters.right = newRightFilters;
      } else {
        // right side is single
        filters.right = filters.right as SingleFilterObject;
        if (filters.right.name === filterName) {
          // match found, remove left side and keep searching
          return this.removeFilter(filters.left, filterName);
        }
      }
    } else {
      // filter is single
      filters = filters as SingleFilterObject;
      if (filters.name === filterName) {
        // match found
        return undefined;
      }
      return filters;
    }

    return filters;
  }

  public baseFieldConversion(
    filterObject: BaseFilterType,
    filterParams?: FilterObjectParameters,
  ): SQLBaseFieldConversionResult {
    if (!filterParams) {
      filterParams = {counter: 0};
    }

    if (filterObject.type === 'single') {
      this.createParamsFromSingleField(
        filterObject as SingleFilterObject,
        filterParams,
      );
      const filter = SQLQueryGenerator.createSingleFilterField(
        filterObject as SingleFilterObject,
      );
      return {
        filter,
        filterParams,
      };
    }
    if (filterObject.type === 'multiple') {
      const filter = this.createMultiFilterField(
        filterObject as TreeFieldFilterObject,
        filterParams,
      );
      return {
        filter,
        filterParams,
      };
    }
    throw new Error(
      'Error converting filterField, invalid filter object type.',
    );
  }

  /**
   * Build and return the inner contents of a SQL ORDER BY clause.
   * @param sortFields Sorting fields to order by.
   * @returns The inner content of a ORDER BY clause for a BigQuery SQL statement.
   */
  public static buildOrderByContents(sortFields: SortField[]): string {
    let contentString = '';
    for (let i = 0; i < sortFields.length; i++) {
      const sortField = sortFields[i];
      contentString += `${sortField.columnName}${
        sortField.isDescending ? ' DESC' : ''
      }${i + 1 < sortFields.length ? ', ' : ''}`;
    }
    return contentString;
  }

  protected addLimit(sql: string, limit: number) {
    // validate that limit is a positive integer to prevent SQL injection
    if (!Number.isInteger(limit) || limit <= 0) {
      throw new Error(
        `Invalid limit value: ${limit}. It must be a positive integer.`,
      );
    }
    const replacementString = `LIMIT ${limit}`;
    return sql.replace(SQLQueryTemplate.Limit, replacementString);
  }

  protected addOffset(sql: string, page: number, limit: number) {
    // validate that limit is a positive integer to prevent SQL injection
    if (!Number.isInteger(limit) || limit <= 0) {
      throw new Error(
        `Invalid limit value: ${limit}. It must be a positive integer.`,
      );
    }
    if (!Number.isInteger(page) || page <= 0) {
      throw new Error(
        `Invalid page value: ${page}. It must be a positive integer.`,
      );
    }
    const offset = page * limit;
    const replacementString = `OFFSET ${offset}`;
    return sql.replace(SQLQueryTemplate.Offset, replacementString);
  }

  /**
   * Adds a list of programmatically defined filters to a templated SQL statement.
   * @param sql SQL string with a filtersTemplate instance to replace with a WHERE clause.
   * @param filters Filter or tree of filters to add to the SQL statement.
   * @param allowedColumnNames List of allowed column names that can be provided as a filter column name.
   * @returns SQL string with a WHERE clause according to the provided filter(s).
   */
  public static addFilters(
    sql: string,
    allowedColumnNames: string[],
    filters?: FilterObject,
    searchString?: string,
    searchableColumns?: string[],
  ): string {
    if (filters) {
      SQLQueryGenerator.validateFilterColumnNames(filters, allowedColumnNames);
    }

    // Add searchString filter if provided
    let searchFilter = '';
    if (searchString && searchableColumns) {
      searchFilter = `(${searchableColumns
        .map(column => `LOWER(${column}) LIKE LOWER('%${searchString}%')`)
        .join(' OR ')})`;
    }

    const fieldLevelFiltersWhereClause = filters?.buildWhereContents();
    const replacementString = `WHERE ${fieldLevelFiltersWhereClause ?? ''}${searchFilter ? `${fieldLevelFiltersWhereClause ? ' AND' : ''} (${searchFilter})` : ''}`;
    return sql.replace(SQLQueryTemplate.Filters, replacementString);
  }

  protected static addGrouping(
    sql: string,
    groupByColumns: string[],
    allowedColumnNames: string[],
  ): string {
    SQLQueryGenerator.validateColumnNames(groupByColumns, allowedColumnNames);
    const replacementString = `GROUP BY ${groupByColumns.join(', ')}`;
    return sql.replace(SQLQueryTemplate.Grouping, replacementString);
  }

  /**
   * Adds a list of programmatically defined sort fields to a templated BigQuery SQL statement.
   * @param sql SQL string with a sortingTemplate instance to replace with a ORDER BY clause.
   * @param sortFields Sorting fields to order by.
   * @param allowedColumnNames List of allowed column names that can be provided as a sort column name.
   * @returns SQL string with a ORDER BY clause according to the provided sort field(s).
   */
  public static addSorting(
    sql: string,
    sortFields: SortField[],
    allowedColumnNames: string[],
  ): string {
    for (const sortField of sortFields) {
      SQLQueryGenerator.validateColumnName(
        sortField.columnName,
        allowedColumnNames,
      );
    }
    let replacementString;
    if (sortFields.length > 0) {
      replacementString = `ORDER BY ${this.buildOrderByContents(sortFields)}`;
    } else {
      replacementString = '';
    }

    return sql.replace(SQLQueryTemplate.Sorting, replacementString);
  }

  protected static createSingleFilterField(
    filterObject: SingleFilterObject,
  ): SQLFilterField {
    const comparisonReturnVal = comparisonReturn(filterObject.comparison);
    if (filterObject.type === 'single') {
      return new SQLFilterField(
        filterObject.name,
        new SQLComparison(comparisonReturnVal, filterObject.value),
      );
    }
    throw new Error('Error creating SQL filterField');
  }

  // added by ICT-812
  protected createMultiFilterField(
    filterObject: TreeFieldFilterObject,
    filterParams?: FilterObjectParameters,
  ): SQLFilterFieldTree {
    if (filterObject.type === 'multiple') {
      const left = this.baseFieldConversion(filterObject.left, filterParams);
      const right = this.baseFieldConversion(filterObject.right, filterParams);
      return new SQLFilterFieldTree(
        left.filter,
        filterObject.andOr,
        right.filter,
      );
    }

    throw new Error('Error creating SQL tree');
  }

  protected abstract executeQuery(
    database: Database,
    sql: string,
    paramValues?: FilterObjectParameters,
  ) // eslint-disable-next-line @typescript-eslint/no-explicit-any
  : Promise<any[]>;
  protected abstract createParamsFromSingleField(
    filterObject: SingleFilterObject,
    filterParams: FilterObjectParameters,
  ): void;
  protected abstract getTotalResultsFieldName(): string;
}
