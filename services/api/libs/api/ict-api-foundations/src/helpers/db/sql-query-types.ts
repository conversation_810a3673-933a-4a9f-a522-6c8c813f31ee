/* eslint-disable no-useless-escape */
import {SortField} from '@ict/sdk-foundations/types/defs/request-filters/sort-fields.ts';
import {
  ComparisonType,
  ComparisonTypeValue,
  FilterObject,
} from '../../defs/db/filter-fields.ts';

/**
 * Thrown whenever a non-allowed column is a value in a filter or sort field.
 */
export class InvalidColumnNameError extends Error {}

export class SQLSelectColumn {
  constructor(
    public name: string,
    public alias?: string,
  ) {
    this.name = name;
    this.alias = alias;
  }

  getSelectString(
    allowedColumnNames: string[],
    distinctColumnNames: string[] = [],
  ): string {
    if (allowedColumnNames.indexOf(this.name) === -1) {
      throw new InvalidColumnNameError(
        `${this.name} is not a valid select column name.`,
      );
    }
    return `${distinctColumnNames.includes(this.name) ? 'DISTINCT ' : ''}${this.alias ? `${this.name} AS ${this.alias}` : this.name}`;
  }
}

export class PostgresSelectColumn extends SQLSelectColumn {
  getSelectString(
    allowedColumnNames: string[],
    distinctColumnNames: string[] = [],
  ): string {
    if (allowedColumnNames.indexOf(this.name) === -1) {
      throw new InvalidColumnNameError(
        `${this.name} is not a valid select column name.`,
      );
    }
    // ensure alias is quoted so that it doesn't return all lowercase and matches what's expected
    return `${distinctColumnNames.includes(this.name) ? 'DISTINCT ' : ''}${this.alias ? `${this.name} AS \"${this.alias}\"` : this.name}`;
  }
}

export interface SortingSelectQueryParams {
  selectFromTable: string;
  allowedColumnNames: string[];
  cteSubquery?: string;
  selectColumns?: SQLSelectColumn[];
  searchableColumns?: string[];
  sortFields?: SortField[];
  page?: number;
  limit?: number;
  groupByColumns?: string[];
  distinctColumns?: string[];
  searchString?: string;
}

export interface FilterSortingSelectQueryParams {
  selectFromTable: string;
  allowedColumnNames: string[];
  cteSubquery?: string;
  selectColumns?: SQLSelectColumn[];
  searchableColumns?: string[];
  filters?: FilterObject;
  sortFields?: SortField[];
  page?: number;
  limit?: number;
  groupByColumns?: string[];
  distinctColumns?: string[];
  searchString?: string;
}

export enum SQLQueryTemplate {
  Filters = '!~FILTERS~!',
  Grouping = '!~GROUPING~!',
  Sorting = '!~SORTING~!',
  Limit = '!~LIMIT~!',
  Offset = '!~OFFSET~!',
}

export enum SQLFieldNames {
  TotalResultFieldName = 'totalResults',
}

export const comparisonReturn = (input: string): ComparisonType => {
  const typeConvert = input as ComparisonTypeValue;
  return ComparisonType[typeConvert];
};
