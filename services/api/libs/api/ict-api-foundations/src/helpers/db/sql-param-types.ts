import {FilterObjectParameters} from '@ict/sdk-foundations/types';

export const addStartEndDateParamValues = (
  startDate: Date | string,
  endDate: Date | string,
  paramValues?: FilterObjectParameters,
): FilterObjectParameters => {
  if (!paramValues) {
    return {
      startDate,
      endDate,
      counter: 0,
    };
  }

  return {
    ...paramValues,
    startDate,
    endDate,
    counter: (paramValues.counter || 0),
  };
};