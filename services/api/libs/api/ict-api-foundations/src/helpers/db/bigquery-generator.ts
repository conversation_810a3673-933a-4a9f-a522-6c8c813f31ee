/* eslint-disable no-param-reassign */
import {Query} from '@google-cloud/bigquery';
import {
  FilterObjectParameters,
  SingleFilterObject,
} from '@ict/sdk-foundations/types/defs/request-filters/filter-types-def.ts';
import {SQLQueryGenerator} from './sql-query-generator.ts';
import {Database} from '../../db/database.ts';
import {BigQueryDatabase} from '../../db/bigquery.ts';
import {SQLFieldNames} from './sql-query-types.ts';

/**
 * Helpers that provide generic support to BigQuery SQL like adding filters, sorting, and pagination.
 */
export class BigQueryGenerator extends SQLQueryGenerator {
  protected async executeQuery(
    database: Database,
    sql: string,
    paramValues?: FilterObjectParameters,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ): Promise<any[]> {
    const sqlOptions: Query = {
      query: sql,
      params: {
        ...paramValues,
      },
      types: paramValues?.types,
    };
    const bqDatabase = database as BigQueryDatabase;
    if (!(bqDatabase instanceof BigQueryDatabase)) {
      throw new Error(
        'BigQueryGenerator can only be used with BigQueryDatabase',
      );
    }
    const [rows] = await bqDatabase.executeMonitoredJob(sqlOptions);
    return rows;
  }

  protected createParamsFromSingleField(
    filterObject: SingleFilterObject,
    filterParams: FilterObjectParameters,
  ) {
    filterParams[`parameter${filterParams.counter}`] = filterObject.value;
    filterObject.value = `@parameter${filterParams.counter}`;
    filterParams.counter += 1;
  }

  protected getTotalResultsFieldName(): string {
    return SQLFieldNames.TotalResultFieldName;
  }
}
