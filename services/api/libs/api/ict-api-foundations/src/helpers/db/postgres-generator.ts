/* eslint-disable no-useless-escape */
import {
  FilterObjectParameters,
  SingleFilterObject,
} from '@ict/sdk-foundations/types/index.ts';
import {Database} from '../../db/database.ts';
import {SQLQueryGenerator} from './sql-query-generator.ts';
import {PgPostgresDatabase} from '../../db/pg-postgres.ts';
import {SQLFieldNames} from './sql-query-types.ts';

export class PostgresQueryGenerator extends SQLQueryGenerator {
  protected async executeQuery(
    database: Database,
    sql: string,
    paramValues?: FilterObjectParameters,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ): Promise<any[]> {
    const pgDatabase = database as PgPostgresDatabase;
    if (!(pgDatabase instanceof PgPostgresDatabase)) {
      throw new Error(
        'PostgresQueryGenerator can only be used with PgPostgresDatabase',
      );
    }

    if (paramValues) {
      const keysToRemove = ['counter', 'types'];
      for (const key of keysToRemove) {
        if (key in paramValues) {
          delete paramValues[key];
        }
      }
    }

    const paramValuesArray = paramValues
      ? Object.values(paramValues)
      : undefined;
    const result = await pgDatabase.query(sql, paramValuesArray);
    return result.rows;
  }

  protected createParamsFromSingleField(
    filterObject: SingleFilterObject,
    filterParams: FilterObjectParameters,
  ): void {
    filterParams[`${filterParams.counter}`] = filterObject.value;
    filterObject.value = `$${filterParams.counter + 1}`; // Postgres uses $1, $2, etc. for parameterized queries, starting at $1
    filterParams.counter += 1;
  }

  protected getTotalResultsFieldName(): string {
    return `\"${SQLFieldNames.TotalResultFieldName}\"`; // Ensure the field name is quoted to match Postgres case sensitivity
  }
}
