/* eslint-disable @typescript-eslint/no-explicit-any */
import {utils, WorkBook, write} from 'xlsx';

export type IncludedColumns = {
  [key: string]: boolean;
};

export interface RemappedResponse {
  [key: string]: any;
}

export class ExportFormatter {
  /**
   * Takes a json response and maps it to an excel formatted buffer response.
   * @returns An excel buffer
   */
  static generateExcelFile = (dataPayload: any[]): Buffer => {
    // Create a new workbook
    const workbook: WorkBook = utils.book_new();

    // Define the headers based on the keys of the first data object
    const headers = Object.keys(dataPayload[0]);

    const preprocessData = (data: any[]) => {
      return data.map(item => {
        return Object.values(item).map(value => {
          if (typeof value === 'object' && value !== null) {
            // Type guard to check if the object has properties 's', 'e', and 'c'
            if (
              's' in value &&
              'e' in value &&
              'c' in value &&
              Array.isArray(value.c)
            ) {
              // Assuming 'c' array contains the digits of the number
              return parseFloat(value.c.join(''));
            }
            return JSON.stringify(value);
          }
          return value;
        });
      });
    };

    // Create an array with the headers as the first row
    const dataWithHeaders = [headers, ...preprocessData(dataPayload)];

    // Create a worksheet from the data with headers
    const worksheet = utils.aoa_to_sheet(dataWithHeaders);

    // Append the worksheet to the workbook
    utils.book_append_sheet(workbook, worksheet, 'Sheet1');

    // Write the workbook to a buffer
    const buffer: Buffer = write(workbook, {
      bookType: 'xlsx',
      type: 'buffer',
    });

    return buffer;
  };

  /**
   * Remaps JSON response object to remove columns that are flagged as not selected.
   * @returns JSON response object
   */
  static mapResponseToColumns(
    columns: IncludedColumns,
    dataPayload: any[],
  ): RemappedResponse[] {
    return dataPayload.map(item => {
      const filteredResponse: RemappedResponse = {};
      for (const key in item) {
        if (columns[key as keyof IncludedColumns]) {
          filteredResponse[key] = item[key];
        }
      }
      return filteredResponse;
    });
  }
}
