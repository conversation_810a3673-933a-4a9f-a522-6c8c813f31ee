import {
  AllowedSettingValue,
  EntityTypes,
  FacilitySetting,
  Setting,
  SettingLog,
  TenantSetting,
  UserSetting,
} from 'ict-api-schema';
import {FindManyOptions} from 'typeorm';
import {
  AppConfigSetting,
  AppConfigSettingSource,
  AppConfigSettingName,
} from '@ict/sdk-foundations/types/index.ts';
import {PostgresDatabase} from '../db/postgres.ts';
import {Container, DiService} from '../di/type-di.ts';
import {ContextService} from '../context/context-service.ts';
import {PostgresFactory} from '../db/postgres-factory.ts';

/**
 * Store class for getting User and Tenant config data from the database.
 */
@DiService()
export class ConfigStore {
  public static readonly type: string = 'config-store';

  constructor(protected contextService: ContextService) {}

  async getPostgresDb(): Promise<PostgresDatabase> {
    const postgresFactory = Container.get(PostgresFactory) as PostgresFactory;
    const database = this.contextService.databaseId;
    return postgresFactory.getDatabase(database, EntityTypes.Config);
  }

  /**
   * Query for a Setting and return it, if found.
   * If both setting id and name are given, a setting is only returned if both id and name match.
   * @param settingId Id of the setting to query for.
   * @param settingName Name of the setting to query for.
   * @returns Setting entity, if found.
   */
  async findSetting(
    settingId?: string,
    settingName?: string,
  ): Promise<Setting | undefined> {
    const settingRepo = (await this.getPostgresDb()).datasource.getRepository(
      Setting,
    );
    let setting;
    if (settingId && settingName) {
      setting = await settingRepo.findOneBy({
        id: settingId,
        name: settingName,
      });
    } else if (settingId) {
      setting = await settingRepo.findOneBy({
        id: settingId,
      });
    } else if (settingName) {
      setting = await settingRepo.findOneBy({
        name: settingName,
      });
    }
    return setting ?? undefined;
  }

  /**
   * Query for a Setting and return it, if found.
   * @param settingId Id of the setting to query for.
   * @returns Setting entity, if found.
   */
  async findSettingById(settingId: string): Promise<Setting | undefined> {
    const settingRepo = (await this.getPostgresDb()).datasource.getRepository(
      Setting,
    );
    const setting = await settingRepo.findOneBy({id: settingId});
    return setting ?? undefined;
  }

  /**
   * Query for a Setting and return it, if found.
   * @param settingName Name of the setting to query for.
   * @returns Setting entity, if found.
   */
  async findSettingByName(settingName: string): Promise<Setting | undefined> {
    const settingRepo = (await this.getPostgresDb()).datasource.getRepository(
      Setting,
    );
    const setting = await settingRepo.findOneBy({name: settingName});
    return setting ?? undefined;
  }

  /**
   * Query for all Settings
   * @returns A list of all Settings
   */
  async findAllSettings(group?: string): Promise<Setting[] | undefined> {
    const settingRepo = (await this.getPostgresDb()).datasource.getRepository(
      Setting,
    );
    const filterOptions: FindManyOptions<Setting> = group
      ? {where: {group}}
      : {};
    const settings = await settingRepo.find(filterOptions);
    return settings ?? undefined;
  }

  /**
   * Query for setting logs
   * @returns A list of setting logs
   */
  async findSettingLogs(
    userId: string,
    settingId: string,
    limit: number,
    facilityId: string,
  ): Promise<SettingLog[]> {
    const settingLogRepo = (
      await this.getPostgresDb()
    ).datasource.getRepository(SettingLog);
    const normalizedLimit = Math.max(0, limit);
    return await settingLogRepo.find({
      where: [
        {settingId, source: 'tenant'},
        {settingId, source: 'facility', facilityId: facilityId},
        {settingId, source: 'user', changedBy: userId},
      ],
      order: {timestamp: 'DESC'},
      take: normalizedLimit,
    });
  }

  /**
   * Create or update a Setting in the database.
   * @param setting Setting object to save.
   * @returns Setting object, after it was saved to the database.
   */
  async saveSetting(setting: Setting): Promise<Setting> {
    const settingRepo = (await this.getPostgresDb()).datasource.getRepository(
      Setting,
    );
    const savedSetting = await settingRepo.save(setting);
    return savedSetting;
  }

  /**
   * Query for a list of allowed values for a setting, if found.
   * @param settingId Id of the setting to query for.
   * @returns AllowedSettingValue entities, if found.
   */
  async findAllowedValuesForSetting(
    settingId: string,
  ): Promise<AllowedSettingValue[] | undefined> {
    const allowedSettingRepo = (
      await this.getPostgresDb()
    ).datasource.getRepository(AllowedSettingValue);
    const allowedSettings = await allowedSettingRepo.find({
      where: {
        settingId,
      },
    });

    if (!allowedSettings || allowedSettings.length === 0) {
      return undefined;
    }
    return allowedSettings;
  }

  /**
   * Try to find an AllowedSettingValue by its ID if it exists.
   * @param allowedSettingValueId ID of the AllowedSettingValue to query for.
   * @returns AllowedSettingValue entity, if found.
   */
  async findAllowedSettingValue(
    allowedSettingValueId: string,
  ): Promise<AllowedSettingValue | undefined> {
    const allowedSettingRepo = (
      await this.getPostgresDb()
    ).datasource.getRepository(AllowedSettingValue);
    const allowedSetting = await allowedSettingRepo.findOneBy({
      id: allowedSettingValueId,
    });
    return allowedSetting ?? undefined;
  }

  /**
   * Create or update an AllowedSettingValue in the database.
   * @param allowedSettingValue AllowedSettingValue entity to save.
   * @returns AllowedSettingValue entity, after it was saved to the database.
   */
  async saveAllowedSettingValue(
    allowedSettingValue: AllowedSettingValue,
  ): Promise<AllowedSettingValue> {
    const allowedSettingRepo = (
      await this.getPostgresDb()
    ).datasource.getRepository(AllowedSettingValue);
    const savedAllowedSettingValue =
      await allowedSettingRepo.save(allowedSettingValue);
    return savedAllowedSettingValue;
  }

  /**
   * Try to find a UserSetting by its userId and settingId, if it exists.
   * @param userId ID of the user to query a setting for.
   * @param settingId ID of the setting to query for.
   * @returns UserSetting entity, if found.
   */
  async findUserSetting(
    userId: string,
    settingId: string,
  ): Promise<UserSetting | undefined> {
    const userSettingRepo = (
      await this.getPostgresDb()
    ).datasource.getRepository(UserSetting);
    const userSetting = await userSettingRepo.findOneBy({
      userId,
      settingId,
    });
    return userSetting ?? undefined;
  }

  /**
   * Find all UserSettings for a given user.
   * @param userId User ID to query settings for.
   * @returns Array of UserSetting entities, if found.
   */
  async findAllUserSettings(
    userId: string,
    group?: string,
  ): Promise<UserSetting[] | undefined> {
    const userSettingRepo = (
      await this.getPostgresDb()
    ).datasource.getRepository(UserSetting);
    const userSettings = await userSettingRepo.find({
      where: {
        userId,
        ...(group ? {setting: {group}} : {}),
      },
    });

    if (!userSettings || userSettings.length === 0) {
      return undefined;
    }
    return userSettings;
  }

  /**
   * Create or update a UserSetting in the database.
   * @param userSetting UserSetting entity to save.
   * @returns UserSetting entity, after it was saved to the database.
   */
  async saveUserSetting(userSetting: UserSetting): Promise<UserSetting> {
    const userSettingRepo = (
      await this.getPostgresDb()
    ).datasource.getRepository(UserSetting);
    const savedUserSetting = await userSettingRepo.save(userSetting);
    return savedUserSetting;
  }

  /**
   * Try to find a TenantSetting by its tenantId and settingId, if it exists.
   * @param tenantId ID of the tenant to query a setting for.
   * @param settingId ID of the setting to query for.
   * @returns TenantSetting entity, if found.
   */
  async findTenantSetting(
    settingId: string,
  ): Promise<TenantSetting | undefined> {
    const tenantSettingRepo = (
      await this.getPostgresDb()
    ).datasource.getRepository(TenantSetting);
    const tenantSetting = await tenantSettingRepo.findOneBy({
      settingId,
    });
    return tenantSetting ?? undefined;
  }

  /**
   * Find all TenantSettings for a given tenant.
   * @param tenantId ID of the tenant to query settings for.
   * @returns Array of TenantSetting entities, if found.
   */
  async findAllTenantSettings(
    group?: string,
  ): Promise<TenantSetting[] | undefined> {
    const tenantSettingRepo = (
      await this.getPostgresDb()
    ).datasource.getRepository(TenantSetting);
    const filterOptions: FindManyOptions<TenantSetting> = group
      ? {where: {setting: {group}}}
      : {};
    const tenantSettings = await tenantSettingRepo.find(filterOptions);

    if (!tenantSettings || tenantSettings.length === 0) {
      return undefined;
    }
    return tenantSettings;
  }

  /**
   * Finds the most relevant setting for a user, first looking for a UserSetting, then a TenantSetting, then finally the default value of a Setting.
   * Either the setting name or setting id will be used to find the specific setting value
   * If both are given, a setting will only be returned if BOTH the name and id match
   * @param userId ID of the user to query settings for.
   * @param settingId Id of the setting to query for./
   * @param settingName Name of the setting to query for.
   */
  async findMostRelevantSettingForUser(
    userId: string,
    settingId?: string,
    settingName?: string,
  ): Promise<AppConfigSetting | undefined> {
    const setting = await this.findSetting(settingId, settingName);

    if (!setting) {
      return undefined;
    }

    // user settings do not have a name, so we must use the retrieved id
    const userSetting = await this.findUserSetting(userId, setting.id);
    // explicity check for null or undefined values because false is a valid boolean setting value
    if (
      userSetting &&
      (userSetting.allowedSettingValue ||
        userSetting.value !== undefined ||
        userSetting.value !== null)
    ) {
      return {
        id: userSetting.settingId,
        name: userSetting.setting.name,
        group: setting.group,
        description: setting.description,
        dataType: userSetting.setting.dataType,
        value: userSetting.value ?? undefined,
        source: AppConfigSettingSource.user,
        tags: setting.tags,
        parentSetting: setting.parentSetting,
      };
    }

    // tenant settings do not have a name, so we must use the retrieved id
    const tenantSetting = await this.findTenantSetting(setting.id);
    // explicity check for null or undefined values because false is a valid boolean setting value
    if (
      tenantSetting &&
      (tenantSetting.allowedSettingValue ||
        tenantSetting.value !== undefined ||
        tenantSetting.value !== null)
    ) {
      return {
        id: tenantSetting.settingId,
        name: tenantSetting.setting.name,
        group: setting.group,
        description: setting.description,
        dataType: tenantSetting.setting.dataType,
        value: tenantSetting.value ?? undefined,
        source: AppConfigSettingSource.tenant,
        tags: setting.tags,
        parentSetting: setting.parentSetting,
      };
    }

    return {
      id: setting.id,
      name: setting.name,
      group: setting.group,
      description: setting.description,
      dataType: setting.dataType,
      value: setting.defaultValue ?? undefined,
      source: AppConfigSettingSource.default,
      tags: setting.tags,
      parentSetting: setting.parentSetting,
    };
  }

  /**
   * Finds a specific setting value given the setting name and desired setting value
   * Either the setting name or setting id will be used to find the specific setting value
   * If both are given, a setting will only be returned if BOTH the name and id match
   * @param settingType enum describing the setting value types - default, tenant, site, or user
   * @param settingId id of the setting to query for
   * @param settingName name of the setting to query for
   * @param userId user id - only required if settingType === user
   * @returns the DTO RelevantSetting if found, undefined if not
   */
  async findSpecificSettingValue(
    settingType: AppConfigSettingSource,
    settingId?: string,
    settingName?: AppConfigSettingName,
    userId?: string,
  ): Promise<AppConfigSetting | undefined> {
    const setting = await this.findSetting(settingId, settingName);

    if (!setting) {
      return undefined;
    }

    switch (settingType) {
      case AppConfigSettingSource.default:
        return {
          id: setting.id,
          name: setting.name,
          group: setting.group,
          description: setting.description,
          dataType: setting.dataType,
          value: setting.defaultValue ?? undefined,
          source: AppConfigSettingSource.default,
          tags: setting.tags,
          parentSetting: setting.parentSetting,
        };
      case AppConfigSettingSource.tenant: {
        // tenant settings do not have a name, so we must use the retrieved setting id
        const tenantSetting = await this.findTenantSetting(setting.id);
        if (
          tenantSetting &&
          (tenantSetting.allowedSettingValue || tenantSetting.value)
        ) {
          return {
            id: tenantSetting.settingId,
            name: tenantSetting.setting.name,
            group: setting.group,
            description: setting.description,
            dataType: tenantSetting.setting.dataType,
            value: tenantSetting.value ?? undefined,
            source: AppConfigSettingSource.tenant,
            tags: setting.tags,
            parentSetting: setting.parentSetting,
          };
        }
        return undefined;
      }
      // case SettingValueSource.site:
      //   // TODO: add site level stored setting
      //   return undefined;
      case AppConfigSettingSource.user: {
        if (!userId) return undefined;
        // tenant settings do not have a name, so we must use the retrieved setting id
        const userSetting = await this.findUserSetting(userId, setting.id);
        if (
          userSetting &&
          (userSetting.allowedSettingValue || userSetting.value)
        ) {
          return {
            id: userSetting.settingId,
            name: userSetting.setting.name,
            group: setting.group,
            description: setting.description,
            dataType: userSetting.setting.dataType,
            value: userSetting.value ?? undefined,
            source: AppConfigSettingSource.user,
            tags: setting.tags,
            parentSetting: setting.parentSetting,
          };
        }
        return undefined;
      }
      // makes all code paths return a value
      default: {
        return undefined;
      }
    }
  }

  /**
   * Create or update a TenantSetting in the database.
   * @param tenantSetting TenantSetting entity to save.
   * @returns TenantSetting entity, after it was saved to the database.
   */
  async saveTenantSetting(
    tenantSetting: TenantSetting,
  ): Promise<TenantSetting> {
    const tenantSettingRepo = (
      await this.getPostgresDb()
    ).datasource.getRepository(TenantSetting);
    const savedTenantSetting = await tenantSettingRepo.save(tenantSetting);
    return savedTenantSetting;
  }

  /**
   *
   * @param settingId the Setting ID to find the related tenant setting
   * @returns the number of records that were deleted
   */
  async deleteTenantSetting(settingId: string): Promise<number> {
    const tenantSettingRepo = (
      await this.getPostgresDb()
    ).datasource.getRepository(TenantSetting);
    const entitiesToRemove = await tenantSettingRepo.findBy({
      setting: {id: settingId},
    });
    let countRemoved = 0;
    if (entitiesToRemove)
      countRemoved = (await tenantSettingRepo.remove(entitiesToRemove)).length;
    return countRemoved;
  }

  /**
   *
   * @param settingId the Setting ID to find the related user setting
   * @returns  the number of records that were deleted
   */
  async deleteUserSetting(settingId: string, userId: string): Promise<number> {
    const userSettingRepo = (
      await this.getPostgresDb()
    ).datasource.getRepository(UserSetting);
    const entitiesToRemove = await userSettingRepo.findBy({
      setting: {
        id: settingId,
      },
      userId,
    });
    let countRemoved = 0;
    if (entitiesToRemove)
      countRemoved = (await userSettingRepo.remove(entitiesToRemove)).length;
    return countRemoved;
  }

  /**
   *
   * @param settingId id to delete
   * @returns  the number of records that were deleted
   */
  async deleteSetting(settingId: string): Promise<number> {
    const settingRepo = (await this.getPostgresDb()).datasource.getRepository(
      Setting,
    );
    const entitiesToRemove = await settingRepo.findBy({id: settingId});
    let countRemoved = 0;
    if (entitiesToRemove)
      countRemoved = (await settingRepo.remove(entitiesToRemove)).length;
    return countRemoved;
  }

  public getType(): string {
    return ConfigStore.type;
  }

  /**
   *
   * @param settingId the Setting ID to find and delete
   * @param facilityId the facility ID to find and delete
   * @returns  the number of records that were deleted
   */
  async deleteFacilitySetting(
    settingId: string,
    facilityId: string,
  ): Promise<number> {
    const repo = (await this.getPostgresDb()).datasource.getRepository(
      FacilitySetting,
    );
    const entitiesToRemove = await repo.findBy({
      setting: {
        id: settingId,
      },
      facilityId,
    });
    let countRemoved = 0;
    if (entitiesToRemove)
      countRemoved = (await repo.remove(entitiesToRemove)).length;
    return countRemoved;
  }

  /**
   * Create or update a FacilitySetting in the database.
   * @param facilitySetting FacilitySetting entity to save.
   * @returns FacilitySetting entity, after it was saved to the database.
   */
  async saveFacilitySetting(
    facilitySetting: FacilitySetting,
  ): Promise<FacilitySetting> {
    const repo = (await this.getPostgresDb()).datasource.getRepository(
      FacilitySetting,
    );
    const savedFacilitySetting = await repo.save(facilitySetting);
    return savedFacilitySetting;
  }

  /**
   * Try to find a FacilitySetting by its facilityId and settingId, if it exists.
   * @param facilityId ID of the user to query a setting for.
   * @param settingId ID of the setting to query for.
   * @returns UserSetting entity, if found.
   */
  public async findFacilitySetting(
    settingId: string,
  ): Promise<FacilitySetting | undefined> {
    const repo = (await this.getPostgresDb()).datasource.getRepository(
      FacilitySetting,
    );
    const facilityId = this.contextService.selectedFacilityId;
    if (facilityId) {
      const scopedSetting = await repo.findOneBy({settingId, facilityId});
      return scopedSetting ?? undefined;
    }

    return undefined;
  }

  public async findAllFacilitySettings(
    group?: string,
  ): Promise<FacilitySetting[] | undefined> {
    const repo = (await this.getPostgresDb()).datasource.getRepository(
      FacilitySetting,
    );
    const facilityId = this.contextService.selectedFacilityId;
    let facilitySettings;
    if (facilityId) {
      facilitySettings = await repo.find({
        where: {
          ...(group ? {setting: {group}, facilityId} : {facilityId}),
        },
      });
    } else {
      facilitySettings = await repo.find({
        where: {
          ...(group ? {setting: {group}} : {}),
        },
      });
    }
    if (!facilitySettings || facilitySettings.length === 0) {
      return undefined;
    }
    return facilitySettings;
  }
}
