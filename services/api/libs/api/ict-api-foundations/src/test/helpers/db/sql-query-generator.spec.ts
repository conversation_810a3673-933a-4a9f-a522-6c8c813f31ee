import {expect, should} from 'chai';
import {SortField} from '@ict/sdk-foundations/types/defs/request-filters/sort-fields.ts';
import sinon from 'sinon';
import {
  SingleFilterObject,
  TreeFieldFilterObject,
} from '@ict/sdk-foundations/types/defs/request-filters/filter-types-def.ts';
import {FilterFact} from '../../../defs/db/filter-fields.ts';
import {
  SQLFilterField,
  SQLFilterFieldTree,
} from '../../../defs/db/sql-filter-fields.ts';
import {
  InvalidColumnNameError,
  SQLQueryTemplate,
} from '../../../helpers/db/sql-query-types.ts';
import {SQLQueryGenerator} from '../../../helpers/db/sql-query-generator.ts';

describe('SQL Query Generator', () => {
  afterEach(() => {
    sinon.restore();
  });
  describe('addFilters', () => {
    it('should call validateFilterColumnNames and buildWhereContents', () => {
      const testFilterObj = new SQLFilterField('test', FilterFact.IsFalse);
      const validateFilterColumnNamesStub = sinon.stub(
        SQLQueryGenerator,
        'validateFilterColumnNames',
      );
      const buildWhereContentsStub = sinon
        .stub(testFilterObj, 'buildWhereContents')
        .returns('1=1');

      const sql = SQLQueryGenerator.addFilters(
        SQLQueryTemplate.Filters,
        [],
        testFilterObj,
      );

      expect(validateFilterColumnNamesStub.called).to.be.true;
      expect(buildWhereContentsStub.called).to.be.true;
      expect(sql).to.equal('WHERE 1=1');
    });

    it('should add the global search string if it exists', () => {
      const validateFilterColumnNamesStub = sinon.stub(
        SQLQueryGenerator,
        'validateFilterColumnNames',
      );

      const sql = SQLQueryGenerator.addFilters(
        SQLQueryTemplate.Filters,
        [],
        undefined,
        'testString',
        ['sku'],
      );

      expect(validateFilterColumnNamesStub.called).to.be.false; // no need to validate the filter names if we're only adding global search
      expect(sql).to.equal("WHERE  ((LOWER(sku) LIKE LOWER('%testString%')))");
    });
  });

  describe('addSorting', () => {
    it('should call validateColumnName and buildOrderByContents', () => {
      const validateColumnNameStub = sinon.stub(
        SQLQueryGenerator,
        'validateColumnName',
      );
      const buildOrderByContentsStub = sinon
        .stub(SQLQueryGenerator, 'buildOrderByContents')
        .returns('testCol, testCol2 DESC');

      const sql = SQLQueryGenerator.addSorting(
        SQLQueryTemplate.Sorting,
        [{columnName: 'testCol1', isDescending: false}],
        [],
      );

      expect(validateColumnNameStub.called).to.be.true;
      expect(buildOrderByContentsStub.called).to.be.true;
      expect(sql).to.equal('ORDER BY testCol, testCol2 DESC');
    });
  });

  describe('buildOrderByContents', () => {
    const templateSQL = `
    SELECT skuPk, timestamp, day_quantity 
    FROM sample_inventory_table
    ${SQLQueryTemplate.Sorting}
    LIMIT @limit
    OFFSET @offset`;

    const allowedColumns = ['skuPk', 'timestamp', 'day_quantity'];

    describe('should build order by contents with valid column name', () => {
      it('should build ORDER BY with valid column name', () => {
        const sortFields: SortField[] = [
          {
            columnName: 'timestamp',
            isDescending: true,
          },
          {
            columnName: 'day_quantity',
            isDescending: false,
          },
        ];
        const sql = SQLQueryGenerator.addSorting(
          templateSQL,
          sortFields,
          allowedColumns,
        );
        expect(sql).to.deep.equal(`
    SELECT skuPk, timestamp, day_quantity 
    FROM sample_inventory_table
    ORDER BY timestamp DESC, day_quantity
    LIMIT @limit
    OFFSET @offset`);
      });
      it('should return empty if given no columns', () => {
        const sortFields: SortField[] = [];
        const sortData = SQLQueryGenerator.buildOrderByContents(sortFields);
        expect(sortData).to.deep.equal('');
      });
    });
    it('should build descending order by contents with valid column name', () => {
      const sortFields: SortField[] = [
        {
          columnName: 'testCol',
          isDescending: true,
        },
      ];

      const expected = 'testCol DESC';
      const orderByContents =
        SQLQueryGenerator.buildOrderByContents(sortFields);
      expect(orderByContents).to.equal(expected);
    });
  });

  describe('validateColumnName', () => {
    it('should not throw an error with a valid column name', () => {
      SQLQueryGenerator.validateColumnName('testCol', ['testCol']);
    });

    it('should throw a InvalidColumnNameError when not provided a valid column name', () => {
      should().Throw(() => {
        SQLQueryGenerator.validateColumnName('pwdHash, testCol', ['testCol']); // test sql injection
      }, InvalidColumnNameError);
    });
  });

  describe('validateFilterColumnNames', () => {
    afterEach(() => {
      sinon.restore();
    });

    it('should call validateColumnName once on single filter field', () => {
      const validateColumnNameStub = sinon.stub(
        SQLQueryGenerator,
        'validateColumnName',
      );
      SQLQueryGenerator.validateFilterColumnNames(
        new SQLFilterField('testCol', FilterFact.IsFalse),
        ['testCol'],
      );

      expect(validateColumnNameStub.calledOnce).to.be.true;
    });

    it('should call validateColumnName multiple times on filter field tree', () => {
      const validateColumnNameStub = sinon.stub(
        SQLQueryGenerator,
        'validateColumnName',
      );

      const filterTree = new SQLFilterFieldTree(
        new SQLFilterField('testCol1', FilterFact.IsFalse),
        'AND',
        new SQLFilterField('testCol2', FilterFact.IsFalse),
      );
      SQLQueryGenerator.validateFilterColumnNames(filterTree, [
        'testCol1',
        'testCol2',
      ]);

      expect(validateColumnNameStub.calledWith('testCol1')).to.be.true;
      expect(validateColumnNameStub.calledWith('testCol2')).to.be.true;
    });
  });

  describe('removeFilter', () => {
    const mockSingleFilter: SingleFilterObject = {
      type: 'single',
      name: 'testName',
      comparison: 'Equal',
      value: '01',
    };

    const mockTreeFilter: TreeFieldFilterObject = {
      type: 'multiple',
      left: {
        type: 'single',
        name: 'testName1',
        comparison: 'Equal',
        value: '01',
      },
      right: {
        type: 'single',
        name: 'testName2',
        comparison: 'Equal',
        value: '02',
      },
      andOr: 'AND',
    };

    const mockDeepTreeFilter: TreeFieldFilterObject = {
      type: 'multiple',
      left: {
        type: 'multiple',
        left: {
          type: 'single',
          name: 'testName1',
          comparison: 'Equal',
          value: '01',
        },
        right: {
          type: 'single',
          name: 'testName2',
          comparison: 'Equal',
          value: '02',
        },
        andOr: 'AND',
      },
      right: {
        type: 'multiple',
        left: {
          type: 'single',
          name: 'testName2',
          comparison: 'Equal',
          value: '02',
        },
        right: {
          type: 'single',
          name: 'testName3',
          comparison: 'Equal',
          value: '01',
        },
        andOr: 'AND',
      },
      andOr: 'AND',
    };

    const mockDeepTreeFilter2: TreeFieldFilterObject = {
      type: 'multiple',
      left: {
        type: 'multiple',
        left: {
          type: 'single',
          name: 'testName1',
          comparison: 'Equal',
          value: '01',
        },
        right: {
          type: 'single',
          name: 'testName3',
          comparison: 'Equal',
          value: '02',
        },
        andOr: 'AND',
      },
      right: {
        type: 'multiple',
        left: {
          type: 'single',
          name: 'testName2',
          comparison: 'Equal',
          value: '02',
        },
        right: {
          type: 'single',
          name: 'testName2',
          comparison: 'Equal',
          value: '01',
        },
        andOr: 'AND',
      },
      andOr: 'AND',
    };

    it('should correctly remove a filter from a single filter', () => {
      const expected = undefined;
      const actual = SQLQueryGenerator.removeFilter(
        mockSingleFilter,
        'testName',
      );

      expect(actual).to.deep.equal(expected);
    });

    it('should not remove from a single filter if the given filter is not present', () => {
      const expected = mockSingleFilter;
      const actual = SQLQueryGenerator.removeFilter(
        mockSingleFilter,
        'notATestName',
      );

      expect(actual).to.deep.equal(expected);
    });

    it('should correctly remove a filter from a tree filter', () => {
      const expected = {
        type: 'single',
        name: 'testName1',
        comparison: 'Equal',
        value: '01',
      };
      const actual = SQLQueryGenerator.removeFilter(
        mockTreeFilter,
        'testName2',
      );

      expect(actual).to.deep.equal(expected);
    });

    it('should not remove from a tree filter if the given filter is not present', () => {
      const expected = mockTreeFilter;
      const actual = SQLQueryGenerator.removeFilter(
        mockTreeFilter as TreeFieldFilterObject,
        'notATestName',
      );

      expect(actual).to.deep.equal(expected);
    });

    it('should correctly remove a filter from a deep tree filter', () => {
      const expected = {
        type: 'multiple',
        left: {
          type: 'single',
          name: 'testName1',
          comparison: 'Equal',
          value: '01',
        },
        right: {
          type: 'single',
          name: 'testName3',
          comparison: 'Equal',
          value: '01',
        },
        andOr: 'AND',
      };
      const actual = SQLQueryGenerator.removeFilter(
        mockDeepTreeFilter,
        'testName2',
      );

      expect(actual).to.deep.equal(expected);
    });

    it('should not remove from a deep tree filter if the given filter is not present', () => {
      const expected = mockDeepTreeFilter;
      const actual = SQLQueryGenerator.removeFilter(
        mockDeepTreeFilter,
        'notATestName',
      );

      expect(actual).to.deep.equal(expected);
    });

    it('should correctly remove a filter from a deep tree filter when one side is all the given filter', () => {
      const expected = {
        type: 'multiple',
        left: {
          type: 'single',
          name: 'testName1',
          comparison: 'Equal',
          value: '01',
        },
        right: {
          type: 'single',
          name: 'testName3',
          comparison: 'Equal',
          value: '02',
        },
        andOr: 'AND',
      };
      const actual = SQLQueryGenerator.removeFilter(
        mockDeepTreeFilter2,
        'testName2',
      );

      expect(actual).to.deep.equal(expected);
    });
  });
});
