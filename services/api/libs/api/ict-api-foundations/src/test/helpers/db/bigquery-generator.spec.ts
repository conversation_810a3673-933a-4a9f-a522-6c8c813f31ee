import {expect} from 'chai';
import sinon from 'sinon';
import {
  SingleFilterObject,
  TreeFieldFilterObject,
} from '@ict/sdk-foundations/types/defs/request-filters/filter-types-def.ts';
import {ComparisonType, FilterFact} from '../../../defs/db/filter-fields.ts';
import {BigQueryGenerator} from '../../../helpers/db/bigquery-generator.ts';
import {
  SQLComparison,
  SQLFilterField,
  SQLFilterFieldTree,
} from '../../../defs/db/sql-filter-fields.ts';
import {
  FilterSortingSelectQueryParams,
  SQLSelectColumn,
  SQLFieldNames,
} from '../../../helpers/db/sql-query-types.ts';

describe('BigQuery Query Generator', () => {
  afterEach(() => {
    sinon.restore();
  });

  describe('baseFieldConversion', () => {
    it('should convert a single field to a SQLFilterField', () => {
      const inFilter: SingleFilterObject = {
        type: 'single',
        name: 'testCol',
        value: 'testVal',
        comparison: 'Equal',
      };
      const bqQueryGenerator = new BigQueryGenerator();
      const result = bqQueryGenerator.baseFieldConversion(inFilter);
      expect(result.filter).to.deep.equal(
        new SQLFilterField(
          'testCol',
          new SQLComparison(ComparisonType.Equal, '@parameter0'),
        ),
      );
      expect(result.filterParams).to.deep.equal({
        counter: 1,
        parameter0: 'testVal',
      });
    });

    it('should convert a tree field to a SQLFilterFieldTree', () => {
      const left: SingleFilterObject = {
        type: 'single',
        name: 'testCol',
        value: 'testVal',
        comparison: 'Equal',
      };
      const right: SingleFilterObject = {
        type: 'single',
        name: 'testCol2',
        value: 'testVal2',
        comparison: 'Equal',
      };
      const inFilter: TreeFieldFilterObject = {
        type: 'multiple',
        left,
        right,
        andOr: 'AND',
      };
      const bqQueryGenerator = new BigQueryGenerator();
      const result = bqQueryGenerator.baseFieldConversion(inFilter);
      expect(result.filter).to.deep.equal(
        new SQLFilterFieldTree(
          new SQLFilterField(
            'testCol',
            new SQLComparison(ComparisonType.Equal, '@parameter0'),
          ),
          'AND',
          new SQLFilterField(
            'testCol2',
            new SQLComparison(ComparisonType.Equal, '@parameter1'),
          ),
        ),
      );
      expect(result.filterParams).to.deep.equal({
        counter: 2,
        parameter0: 'testVal',
        parameter1: 'testVal2',
      });
    });
  });

  describe('generateSelectQuery', () => {
    const totalResultQueryPart = `COUNT(*) OVER() as ${SQLFieldNames.TotalResultFieldName}`;

    it('should generate simple select statement', () => {
      const params: FilterSortingSelectQueryParams = {
        selectFromTable: 'testTable',
        allowedColumnNames: [],
      };
      const expectedSQL = `SELECT *, ${totalResultQueryPart} FROM testTable`;
      const bqQueryGenerator = new BigQueryGenerator();
      const sql = bqQueryGenerator.generateSelectQuery(params);
      expect(sql.trim()).to.equal(expectedSQL.trim());
    });

    it('should generate select statement with filters', () => {
      const params: FilterSortingSelectQueryParams = {
        selectFromTable: 'testTable',
        allowedColumnNames: ['testCol'],
        filters: new SQLFilterField('testCol', FilterFact.IsFalse),
      };
      const expectedSQL = `SELECT *, ${totalResultQueryPart} FROM testTable WHERE testCol IS FALSE`;
      const bqQueryGenerator = new BigQueryGenerator();
      const sql = bqQueryGenerator.generateSelectQuery(params);
      expect(sql.trim()).to.equal(expectedSQL.trim());
    });

    it('should generate select statement with sorting', () => {
      const params: FilterSortingSelectQueryParams = {
        selectFromTable: 'testTable',
        allowedColumnNames: ['testCol'],
        sortFields: [{columnName: 'testCol', isDescending: false}],
      };
      const expectedSQL = `SELECT *, ${totalResultQueryPart} FROM testTable ORDER BY testCol`;
      const bqQueryGenerator = new BigQueryGenerator();
      const sql = bqQueryGenerator.generateSelectQuery(params);
      expect(sql.trim()).to.equal(expectedSQL.trim());
    });

    it('should generate select statement with limit', () => {
      const params: FilterSortingSelectQueryParams = {
        selectFromTable: 'testTable',
        allowedColumnNames: [],
        limit: 10,
      };
      const expectedSQL = `SELECT *, ${totalResultQueryPart} FROM testTable LIMIT 10`;
      const bqQueryGenerator = new BigQueryGenerator();
      const sql = bqQueryGenerator.generateSelectQuery(params);
      expect(sql.trim()).to.equal(expectedSQL.trim());
    });

    it('should generate select statement with pagination', () => {
      const params: FilterSortingSelectQueryParams = {
        selectFromTable: 'testTable',
        allowedColumnNames: [],
        limit: 10,
        page: 10,
      };
      const expectedSQL = `SELECT *, ${totalResultQueryPart} FROM testTable LIMIT 10 OFFSET 100`;
      const bqQueryGenerator = new BigQueryGenerator();
      const sql = bqQueryGenerator.generateSelectQuery(params);
      expect(sql.trim()).to.equal(expectedSQL.trim());
    });

    it('should generate select statement with pagination', () => {
      const params: FilterSortingSelectQueryParams = {
        selectFromTable: 'testTable',
        allowedColumnNames: [],
        limit: 10,
        page: 0,
      };
      const expectedSQL = `SELECT *, ${totalResultQueryPart} FROM testTable LIMIT 10`;
      const bqQueryGenerator = new BigQueryGenerator();
      const sql = bqQueryGenerator.generateSelectQuery(params);
      expect(sql.trim()).to.equal(expectedSQL.trim());
    });

    it('should generate select statement with filters and sorting', () => {
      const params: FilterSortingSelectQueryParams = {
        selectFromTable: 'testTable',
        allowedColumnNames: ['testCol'],
        filters: new SQLFilterField('testCol', FilterFact.IsFalse),
        sortFields: [{columnName: 'testCol', isDescending: false}],
      };
      const expectedSQL = `SELECT *, ${totalResultQueryPart} FROM testTable WHERE testCol IS FALSE ORDER BY testCol`;
      const bqQueryGenerator = new BigQueryGenerator();
      const sql = bqQueryGenerator.generateSelectQuery(params);
      expect(sql.trim()).to.equal(expectedSQL.trim());
    });

    it('should generate select statement with filters and limit', () => {
      const params: FilterSortingSelectQueryParams = {
        selectFromTable: 'testTable',
        allowedColumnNames: ['testCol'],
        filters: new SQLFilterField('testCol', FilterFact.IsFalse),
        limit: 10,
      };
      const expectedSQL = `SELECT *, ${totalResultQueryPart} FROM testTable WHERE testCol IS FALSE LIMIT 10`;
      const bqQueryGenerator = new BigQueryGenerator();
      const sql = bqQueryGenerator.generateSelectQuery(params);
      expect(sql.trim()).to.equal(expectedSQL.trim());
    });

    it('should generate select statement with filters and pagination', () => {
      const params: FilterSortingSelectQueryParams = {
        selectFromTable: 'testTable',
        allowedColumnNames: ['testCol'],
        filters: new SQLFilterField('testCol', FilterFact.IsFalse),
        limit: 10,
        page: 2,
      };
      const expectedSQL = `SELECT *, ${totalResultQueryPart} FROM testTable WHERE testCol IS FALSE LIMIT 10 OFFSET 20`;
      const bqQueryGenerator = new BigQueryGenerator();
      const sql = bqQueryGenerator.generateSelectQuery(params);
      expect(sql.trim()).to.equal(expectedSQL.trim());
    });

    it('should generate select statement with sorting and limit', () => {
      const params: FilterSortingSelectQueryParams = {
        selectFromTable: 'testTable',
        allowedColumnNames: ['testCol'],
        sortFields: [{columnName: 'testCol', isDescending: false}],
        limit: 10,
      };
      const expectedSQL = `SELECT *, ${totalResultQueryPart} FROM testTable ORDER BY testCol LIMIT 10`;
      const bqQueryGenerator = new BigQueryGenerator();
      const sql = bqQueryGenerator.generateSelectQuery(params);
      expect(sql.trim()).to.equal(expectedSQL.trim());
    });

    it('should generate select statement with sorting and pagination', () => {
      const params: FilterSortingSelectQueryParams = {
        selectFromTable: 'testTable',
        allowedColumnNames: ['testCol'],
        sortFields: [{columnName: 'testCol', isDescending: false}],
        limit: 10,
        page: 10,
      };
      const expectedSQL = `SELECT *, ${totalResultQueryPart} FROM testTable ORDER BY testCol LIMIT 10 OFFSET 100`;
      const bqQueryGenerator = new BigQueryGenerator();
      const sql = bqQueryGenerator.generateSelectQuery(params);
      expect(sql.trim()).to.equal(expectedSQL.trim());
    });

    it('should generate select statement with filters and sorting and limit', () => {
      const params: FilterSortingSelectQueryParams = {
        selectFromTable: 'testTable',
        allowedColumnNames: ['testCol'],
        filters: new SQLFilterField('testCol', FilterFact.IsFalse),
        sortFields: [{columnName: 'testCol', isDescending: false}],
        limit: 10,
      };
      const expectedSQL = `SELECT *, ${totalResultQueryPart} FROM testTable WHERE testCol IS FALSE ORDER BY testCol LIMIT 10`;
      const bqQueryGenerator = new BigQueryGenerator();
      const sql = bqQueryGenerator.generateSelectQuery(params);
      expect(sql.trim()).to.equal(expectedSQL.trim());
    });

    it('should generate select statement with filters and sorting and limit and offset', () => {
      const params: FilterSortingSelectQueryParams = {
        selectFromTable: 'testTable',
        allowedColumnNames: ['testCol'],
        filters: new SQLFilterField('testCol', FilterFact.IsFalse),
        sortFields: [{columnName: 'testCol', isDescending: false}],
        limit: 10,
        page: 1,
      };
      const expectedSQL = `SELECT *, ${totalResultQueryPart} FROM testTable WHERE testCol IS FALSE ORDER BY testCol LIMIT 10 OFFSET 10`;
      const bqQueryGenerator = new BigQueryGenerator();
      const sql = bqQueryGenerator.generateSelectQuery(params);
      expect(sql.trim()).to.equal(expectedSQL.trim());
    });

    it('should generate select statement with specified selected column', () => {
      const params: FilterSortingSelectQueryParams = {
        selectFromTable: 'testTable',
        allowedColumnNames: ['testCol1', 'testCol2'],
        selectColumns: [new SQLSelectColumn('testCol1')],
      };
      const expectedSQL = `SELECT testCol1, ${totalResultQueryPart} FROM testTable`;
      const bqQueryGenerator = new BigQueryGenerator();
      const sql = bqQueryGenerator.generateSelectQuery(params);
      expect(sql.trim()).to.equal(expectedSQL.trim());
    });

    it('should generate select statement with multiple specified selected columns', () => {
      const params: FilterSortingSelectQueryParams = {
        selectFromTable: 'testTable',
        allowedColumnNames: ['testCol1', 'testCol2'],
        selectColumns: [
          new SQLSelectColumn('testCol1'),
          new SQLSelectColumn('testCol2'),
        ],
      };
      const expectedSQL = `SELECT testCol1, testCol2, ${totalResultQueryPart} FROM testTable`;
      const bqQueryGenerator = new BigQueryGenerator();
      const sql = bqQueryGenerator.generateSelectQuery(params);
      expect(sql.trim()).to.equal(expectedSQL.trim());
    });

    it('should generate select statement with specified selected column with alias', () => {
      const params: FilterSortingSelectQueryParams = {
        selectFromTable: 'testTable',
        allowedColumnNames: ['testCol1', 'testCol2', 'testAlias1'],
        selectColumns: [new SQLSelectColumn('testCol1', 'testAlias1')],
      };
      const expectedSQL = `SELECT testCol1 AS testAlias1, ${totalResultQueryPart} FROM testTable`;
      const bqQueryGenerator = new BigQueryGenerator();
      const sql = bqQueryGenerator.generateSelectQuery(params);
      expect(sql.trim()).to.equal(expectedSQL.trim());
    });

    it('should generate select statement with multiple specified selected columns with aliases', () => {
      const params: FilterSortingSelectQueryParams = {
        selectFromTable: 'testTable',
        allowedColumnNames: [
          'testCol1',
          'testCol2',
          'testAlias1',
          'testAlias2',
        ],
        selectColumns: [
          new SQLSelectColumn('testCol1', 'testAlias1'),
          new SQLSelectColumn('testCol2', 'testAlias2'),
        ],
      };
      const expectedSQL = `SELECT testCol1 AS testAlias1, testCol2 AS testAlias2, ${totalResultQueryPart} FROM testTable`;
      const bqQueryGenerator = new BigQueryGenerator();
      const sql = bqQueryGenerator.generateSelectQuery(params);
      expect(sql.trim()).to.equal(expectedSQL.trim());
    });

    it('should generate select statement with cte subquery', () => {
      const params: FilterSortingSelectQueryParams = {
        selectFromTable: 'testTable',
        allowedColumnNames: [],
        cteSubquery: 'WITH testSubquery AS (SELECT * FROM testTable2)',
      };
      const expectedSQL = `WITH testSubquery AS (SELECT * FROM testTable2) SELECT *, ${totalResultQueryPart} FROM testTable`;
      const bqQueryGenerator = new BigQueryGenerator();
      const sql = bqQueryGenerator.generateSelectQuery(params);
      expect(sql.trim()).to.equal(expectedSQL.trim());
    });

    it('should generate select statement with total results field', () => {
      const params: FilterSortingSelectQueryParams = {
        selectFromTable: 'testTable',
        allowedColumnNames: [],
        cteSubquery: 'WITH testSubquery AS (SELECT * FROM testTable2)',
      };
      const expectedSQL = `WITH testSubquery AS (SELECT * FROM testTable2) SELECT *, ${totalResultQueryPart} FROM testTable`;
      const bqQueryGenerator = new BigQueryGenerator();
      const sql = bqQueryGenerator.generateSelectQuery(params);
      expect(sql.trim()).to.equal(expectedSQL.trim());
    });

    it('should generate select statement with group by clause', () => {
      const params: FilterSortingSelectQueryParams = {
        selectFromTable: 'testTable',
        allowedColumnNames: ['testCol'],
        groupByColumns: ['testCol'],
        selectColumns: [new SQLSelectColumn('testCol')],
      };
      const expectedSQL = `SELECT testCol, ${totalResultQueryPart} FROM testTable GROUP BY testCol`;
      const bqQueryGenerator = new BigQueryGenerator();
      const sql = bqQueryGenerator.generateSelectQuery(params);
      expect(sql.trim()).to.equal(expectedSQL.trim());
    });

    it('should generate select statement with distinct colummns', () => {
      const params: FilterSortingSelectQueryParams = {
        selectFromTable: 'testTable',
        allowedColumnNames: ['testCol'],
        distinctColumns: ['testCol'],
        selectColumns: [new SQLSelectColumn('testCol')],
      };
      const expectedSQL = `SELECT DISTINCT testCol, ${totalResultQueryPart} FROM testTable`;
      const bqQueryGenerator = new BigQueryGenerator();
      const sql = bqQueryGenerator.generateSelectQuery(params);
      expect(sql.trim()).to.equal(expectedSQL.trim());
    });
  });
});
