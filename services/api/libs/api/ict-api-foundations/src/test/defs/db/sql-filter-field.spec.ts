import {expect} from 'chai';
import {
  SQLBetweenComparison,
  SQLComparison,
  SQLFilterField,
  SQLFilterFieldTree,
} from '../../../defs/db/sql-filter-fields.ts';
import {ComparisonType, FilterFact} from '../../../defs/db/filter-fields.ts';

describe('SQL Filter Fields', () => {
  describe('buildWhereContents', () => {
    it('should build where contents with valid Equal comparison', () => {
      const expectedContents = 'testCol = testParam';
      const sql = new SQLFilterField(
        'testCol',
        new SQLComparison(ComparisonType.Equal, 'testParam')
      ).buildWhereContents();
      expect(sql).to.equal(expectedContents);
    });
    it('should build where contents with valid LessThan comparison', () => {
      const expectedContents = 'testCol < testParam';
      const sql = new SQLFilterField(
        'testCol',
        new SQLComparison(ComparisonType.LessThan, 'testParam')
      ).buildWhereContents();
      expect(sql).to.equal(expectedContents);
    });
    it('should build where contents with valid GreaterThan comparison', () => {
      const expectedContents = 'testCol > testParam';
      const sql = new SQLFilterField(
        'testCol',
        new SQLComparison(ComparisonType.GreaterThan, 'testParam')
      ).buildWhereContents();
      expect(sql).to.equal(expectedContents);
    });
    it('should build where contents with valid LessThanOrEqual comparison', () => {
      const expectedContents = 'testCol <= testParam';
      const sql = new SQLFilterField(
        'testCol',
        new SQLComparison(ComparisonType.LessThanOrEqual, 'testParam')
      ).buildWhereContents();
      expect(sql).to.equal(expectedContents);
    });
    it('should build where contents with valid GreaterThanOrEqual comparison', () => {
      const expectedContents = 'testCol >= testParam';
      const sql = new SQLFilterField(
        'testCol',
        new SQLComparison(ComparisonType.GreaterThanOrEqual, 'testParam')
      ).buildWhereContents();
      expect(sql).to.equal(expectedContents);
    });
    it('should build where contents with valid NotEqual comparison', () => {
      const expectedContents = 'testCol != testParam';
      const sql = new SQLFilterField(
        'testCol',
        new SQLComparison(ComparisonType.NotEqual, 'testParam')
      ).buildWhereContents();
      expect(sql).to.equal(expectedContents);
    });
    it('should build where contents with valid Like comparison', () => {
      const expectedContents = 'testCol LIKE testParam';
      const sql = new SQLFilterField(
        'testCol',
        new SQLComparison(ComparisonType.Like, 'testParam')
      ).buildWhereContents();
      expect(sql).to.equal(expectedContents);
    });
    it('should build where contents with valid NotLike comparison', () => {
      const expectedContents = 'testCol NOT LIKE testParam';
      const sql = new SQLFilterField(
        'testCol',
        new SQLComparison(ComparisonType.NotLike, 'testParam')
      ).buildWhereContents();
      expect(sql).to.equal(expectedContents);
    });
    it('should build where contents with valid Between comparison', () => {
      const expectedContents =
        'testCol BETWEEN testParamLower AND testParamHigher';
      const sql = new SQLFilterField(
        'testCol',
        new SQLBetweenComparison(
          ComparisonType.Between,
          'testParamLower',
          'testParamHigher'
        )
      ).buildWhereContents();
      expect(sql).to.equal(expectedContents);
    });
    it('should build where contents with valid NotBetween comparison', () => {
      const expectedContents =
        'testCol NOT BETWEEN testParamLower AND testParamHigher';
      const sql = new SQLFilterField(
        'testCol',
        new SQLBetweenComparison(
          ComparisonType.NotBetween,
          'testParamLower',
          'testParamHigher'
        )
      ).buildWhereContents();
      expect(sql).to.equal(expectedContents);
    });
    it('should build where contents with valid In comparison', () => {
      const expectedContents = 'testCol IN testParam';
      const sql = new SQLFilterField(
        'testCol',
        new SQLComparison(ComparisonType.In, 'testParam')
      ).buildWhereContents();
      expect(sql).to.equal(expectedContents);
    });
    it('should build where contents with valid NotIn comparison', () => {
      const expectedContents = 'testCol NOT IN testParam';
      const sql = new SQLFilterField(
        'testCol',
        new SQLComparison(ComparisonType.NotIn, 'testParam')
      ).buildWhereContents();
      expect(sql).to.equal(expectedContents);
    });
    it('should build where contents with valid IsTrue fact', () => {
      const expectedContents = 'testCol IS TRUE';
      const sql = new SQLFilterField(
        'testCol',
        FilterFact.IsTrue
      ).buildWhereContents();
      expect(sql).to.equal(expectedContents);
    });
    it('should build where contents with valid IsNotTrue fact', () => {
      const expectedContents = 'testCol IS NOT TRUE';
      const sql = new SQLFilterField(
        'testCol',
        FilterFact.IsNotTrue
      ).buildWhereContents();
      expect(sql).to.equal(expectedContents);
    });
    it('should build where contents with valid IsFalse fact', () => {
      const expectedContents = 'testCol IS FALSE';
      const sql = new SQLFilterField(
        'testCol',
        FilterFact.IsFalse
      ).buildWhereContents();
      expect(sql).to.equal(expectedContents);
    });
    it('should build where contents with valid IsNotFalse fact', () => {
      const expectedContents = 'testCol IS NOT FALSE';
      const sql = new SQLFilterField(
        'testCol',
        FilterFact.IsNotFalse
      ).buildWhereContents();
      expect(sql).to.equal(expectedContents);
    });
    it('should build where contents with valid IsNull fact', () => {
      const expectedContents = 'testCol IS NULL';
      const sql = new SQLFilterField(
        'testCol',
        FilterFact.IsNull
      ).buildWhereContents();
      expect(sql).to.equal(expectedContents);
    });
    it('should build where contents with valid IsNotNull fact', () => {
      const expectedContents = 'testCol IS NOT NULL';
      const sql = new SQLFilterField(
        'testCol',
        FilterFact.IsNotNull
      ).buildWhereContents();
      expect(sql).to.equal(expectedContents);
    });
    it('should build where contents with valid multi-level FilterFieldTree', () => {
      const expectedContents =
        '((testCol1 = testParam1) AND (testCol2 IS NOT NULL)) OR (((testCol3 BETWEEN testParam2 AND testParam3) OR (testCol3 >= testParam3)) AND (testCol4 IS TRUE))';
      const filterTree = new SQLFilterFieldTree(
        new SQLFilterFieldTree(
          new SQLFilterField(
            'testCol1',
            new SQLComparison(ComparisonType.Equal, 'testParam1')
          ),
          'AND',
          new SQLFilterField('testCol2', FilterFact.IsNotNull)
        ),
        'OR',
        new SQLFilterFieldTree(
          new SQLFilterFieldTree(
            new SQLFilterField(
              'testCol3',
              new SQLBetweenComparison(
                ComparisonType.Between,
                'testParam2',
                'testParam3'
              )
            ),
            'OR',
            new SQLFilterField(
              'testCol3',
              new SQLComparison(
                ComparisonType.GreaterThanOrEqual,
                'testParam3'
              )
            )
          ),
          'AND',
          new SQLFilterField('testCol4', FilterFact.IsTrue)
        )
      );
      const sql = filterTree.buildWhereContents();
      expect(sql).to.equal(expectedContents);
    });
  });
});
