import sinon from 'sinon';
import {expect} from 'chai';
import pg from 'pg';
import {PgPostgresDatabase} from '../../db/pg-postgres.ts';
import {Environment, GCPSecretManager} from '../../index.ts';

describe('PgPostgresDatabase', () => {
  beforeEach(() => {
    sinon.replace(
      GCPSecretManager.prototype,
      'get',
      sinon.fake.resolves(`{
      "host": "test-host",
      "port": 1234,
      "user": "test-user",
      "password": "test-password"
    }`),
    );
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('generateDefaultPgPostgresOptions', () => {
    it('should return default options for a dev environment', async () => {
      process.env.ENVIRONMENT = Environment.local;
      process.env.POSTGRES_HOST = 'test-host';
      process.env.POSTGRES_PORT = '1234';
      process.env.POSTGRES_USERNAME = 'test-user';
      process.env.POSTGRES_PASSWORD = 'test-password';
      const testDatabaseName = 'testDatabase';

      const options =
        await PgPostgresDatabase.generateDefaultPgPostgresOptions(
          testDatabaseName,
        );
      expect(options).to.deep.equal({
        host: 'test-host',
        port: 1234,
        user: 'test-user',
        password: 'test-password',
        database: testDatabaseName,
      });

      delete process.env.ENVIRONMENT;
      delete process.env.POSTGRES_HOST;
      delete process.env.POSTGRES_PORT;
      delete process.env.POSTGRES_USERNAME;
      delete process.env.POSTGRES_PASSWORD;
    });

    it('should return default options for a non-dev environment', async () => {
      process.env.ENVIRONMENT = Environment.dev;
      const testDatabaseName = 'testDatabase';
      const options =
        await PgPostgresDatabase.generateDefaultPgPostgresOptions(
          testDatabaseName,
        );
      expect(options).to.deep.equal({
        host: 'test-host',
        port: 1234,
        user: 'test-user',
        password: 'test-password',
        database: testDatabaseName,
      });
    });
  });

  describe('query', () => {
    const expectedResult = {
      rowCount: 1,
      rows: [{id: 1, name: 'Test'}],
    };
    beforeEach(() => {
      // stub the constructor for pg.Pool
      const poolStub = sinon.createStubInstance(pg.Pool);
      poolStub.query.resolves(expectedResult);
      sinon.stub(pg, 'Pool').returns(poolStub);
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should return the expected query result', async () => {
      const db = new PgPostgresDatabase({
        host: 'localhost',
        port: 5432,
        database: 'testdb',
        user: 'testuser',
        password: 'testpassword',
      });
      const result = await db.query('SELECT * FROM test_table WHERE id = $1', [
        1,
      ]);
      expect(result).to.deep.equal(expectedResult);
    });
  });
});
