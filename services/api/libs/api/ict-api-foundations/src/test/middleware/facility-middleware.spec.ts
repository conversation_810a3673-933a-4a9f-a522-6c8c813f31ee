import {expect} from 'chai';
import {NextFunction, Request, Response} from 'express';
import httpContext from 'express-http-context';
import sinon from 'sinon';
import {AppConfigSettingSource} from '@ict/sdk-foundations/types/index.ts';
import {ContextService} from '../../context/context-service.ts';
import {FacilityMiddleware} from '../../middleware/facility-middleware.ts';
import {ConfigStore, WinstonLogger} from '../../index.ts';
import {IctError} from '../../errors/ict-error.ts';

describe('FacilityMiddleware', () => {
  let facilityMiddleware: FacilityMiddleware;
  let contextService: ContextService;
  let httpContextMiddlewareStub: sinon.SinonStub;
  let httpContextGetStub: sinon.SinonStub;
  let httpContextSetStub: sinon.SinonStub;
  let nextFunction: NextFunction;
  let mockLogger: sinon.SinonStubbedInstance<WinstonLogger>;
  let mockConfigStore: sinon.SinonStubbedInstance<ConfigStore>;

  beforeEach(() => {
    mockConfigStore = sinon.createStubInstance(ConfigStore);
    contextService = new ContextService();
    httpContextMiddlewareStub = sinon.stub(httpContext, 'middleware');
    httpContextGetStub = sinon.stub(httpContext, 'get');
    httpContextSetStub = sinon.stub(httpContext, 'set');
    nextFunction = sinon.mock();
    mockLogger = sinon.createStubInstance(WinstonLogger);
    facilityMiddleware = new FacilityMiddleware(
      contextService,
      mockLogger,
      mockConfigStore,
    );
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('use', () => {
    it('should call httpContext.middleware', () => {
      const req = {} as Request;
      const res = {} as Response;

      facilityMiddleware.use(req, res, nextFunction);
      expect(httpContextMiddlewareStub.called).to.be.true;
    });
  });

  describe('addFacilityIdToContext', () => {
    let req: Partial<Request>;

    beforeEach(() => {
      req = {
        header: sinon.stub().returns(null),
      } as unknown as Request;
    });

    it('should set facility id from ict-facility-id header', () => {
      const facilityId = 'facility-123';
      req.header = sinon.stub().returns(facilityId);

      facilityMiddleware.addFacilityIdToContext(<Request>req);

      expect(httpContextSetStub.calledWith('selectedFacilityId', facilityId)).to
        .be.true;
    });

    it('should set facility id to null if header not provided', () => {
      facilityMiddleware.addFacilityIdToContext(<Request>req);
      expect(httpContextSetStub.calledWith('selectedFacilityId', null)).to.be
        .true;
    });
  });

  describe('updateFacilityContext', () => {
    let req: Partial<Request>;

    beforeEach(() => {
      req = {
        auth: {
          payload: {
            organization: {metadata: {dataset: 'test-dataset'}},
            sub: 'test-user-id',
            user_email: '<EMAIL>',
            'https://ict.dematic.cloud/roles': ['role1', 'role2'],
          },
        },
      } as unknown as Request;

      // Mock the context service getters
      httpContextGetStub.withArgs('userRoles').returns(['role1', 'role2']);
      httpContextGetStub.withArgs('selectedFacilityId').returns(null);
    });

    it('should throw IctError when facility maps loading fails', async () => {
      const error = new Error('Config store error');
      mockConfigStore.findSpecificSettingValue.rejects(error);

      try {
        await facilityMiddleware.updateFacilityContext(<Request>req);
        expect.fail('Expected error to be thrown');
      } catch (thrownError: any) {
        expect(thrownError).to.be.instanceOf(IctError);
        expect(thrownError.message).to.equal(
          'Failed to load required facility configuration',
        );
        expect(thrownError.statusCode).to.equal(500);
      }
    });

    it('should fallback to default level when facility maps not found at tenant level', async () => {
      const mockFacilityMaps = {
        id: 'facility-maps-id',
        name: 'facility-maps',
        group: 'facility',
        dataType: 'json' as const,
        value: [{id: 'facility1', dataset: 'dataset1'}],
        source: AppConfigSettingSource.default,
      };

      // Set up stub to return undefined for tenant and facility maps for default
      mockConfigStore.findSpecificSettingValue.callsFake(
        async (source, userId, setting) => {
          if (
            source === AppConfigSettingSource.tenant &&
            setting === 'facility-maps'
          ) {
            return undefined;
          }
          if (
            source === AppConfigSettingSource.default &&
            setting === 'facility-maps'
          ) {
            return mockFacilityMaps;
          }
          return undefined;
        },
      );

      await facilityMiddleware.updateFacilityContext(<Request>req);

      // Verify both calls were made with correct parameters
      expect(mockConfigStore.findSpecificSettingValue.calledTwice).to.be.true;

      // Verify the first call was for tenant level
      const firstCall = mockConfigStore.findSpecificSettingValue.getCall(0);
      expect(firstCall.args).to.deep.equal([
        AppConfigSettingSource.tenant,
        undefined,
        'facility-maps',
      ]);

      // Verify the second call was for default level
      const secondCall = mockConfigStore.findSpecificSettingValue.getCall(1);
      expect(secondCall.args).to.deep.equal([
        AppConfigSettingSource.default,
        undefined,
        'facility-maps',
      ]);

      // Verify facility maps were set in context
      expect(
        httpContextSetStub.calledWith('facilityMaps', mockFacilityMaps.value),
      ).to.be.true;
    });

    it('should throw unauthorized error when facility id is not in user roles', async () => {
      const mockFacilityMaps = {
        id: 'facility-maps-id',
        name: 'facility-maps',
        group: 'facility',
        dataType: 'json' as const,
        value: [{id: 'facility1', dataset: 'dataset1'}],
        source: AppConfigSettingSource.tenant,
      };

      mockConfigStore.findSpecificSettingValue.resolves(mockFacilityMaps);

      // Set a facility id that's not in the user's roles
      httpContextGetStub
        .withArgs('selectedFacilityId')
        .returns('unauthorized-facility');

      try {
        await facilityMiddleware.updateFacilityContext(<Request>req);
        expect.fail('Expected error to be thrown');
      } catch (thrownError: any) {
        expect(thrownError).to.be.instanceOf(IctError);
        expect(thrownError.message).to.equal('Invalid facility id');
        expect(thrownError.statusCode).to.equal(401);
      }
    });

    it('should set dataset id from facility map when facility id is valid', async () => {
      const mockFacilityMaps = {
        id: 'facility-maps-id',
        name: 'facility-maps',
        group: 'facility',
        dataType: 'json' as const,
        value: [{id: 'role1', dataset: 'facility-dataset-123'}],
        source: AppConfigSettingSource.tenant,
      };

      mockConfigStore.findSpecificSettingValue.resolves(mockFacilityMaps);

      // Set a facility id that matches a user role
      httpContextGetStub.withArgs('selectedFacilityId').returns('role1');
      httpContextGetStub
        .withArgs('facilityMaps')
        .returns(mockFacilityMaps.value);

      await facilityMiddleware.updateFacilityContext(<Request>req);

      expect(
        httpContextSetStub.calledWith('facilityMaps', mockFacilityMaps.value),
      ).to.be.true;
      expect(httpContextSetStub.calledWith('datasetId', 'facility-dataset-123'))
        .to.be.true;
    });

    it('should handle missing auth gracefully', async () => {
      req.auth = undefined;
      await facilityMiddleware.updateFacilityContext(<Request>req);
      // Should not throw an error
    });
  });
});
