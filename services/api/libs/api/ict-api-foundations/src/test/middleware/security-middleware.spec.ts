import {Request, Response} from 'express';
import sinon from 'sinon';
import {expect} from 'chai';
import {Container} from '../../di/type-di.ts';
import {SecurityMiddleware} from '../../middleware/security-middleware.ts';

describe('SecurityMiddleware', () => {
  let middleware: SecurityMiddleware;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let nextFunction: sinon.SinonSpy;
  let setHeaderSpy: sinon.SinonStub;

  beforeEach(() => {
    // Reset the container before each test
    Container.reset();

    // Create mocks
    mockRequest = {};
    setHeaderSpy = sinon.stub();
    mockResponse = {
      setHeader: setHeaderSpy,
    };
    nextFunction = sinon.spy();

    // Get middleware instance
    middleware = Container.get(SecurityMiddleware);
  });

  it('should set all security headers', () => {
    // Execute middleware
    middleware.use(
      mockRequest as Request,
      mockResponse as Response,
      nextFunction
    );

    // Verify all security headers were set
    expect(setHeaderSpy.calledWith('X-Content-Type-Options', 'nosniff')).to.be
      .true;
    expect(setHeaderSpy.calledWith('X-Frame-Options', 'SAMEORIGIN')).to.be.true;
    expect(setHeaderSpy.calledWith('X-XSS-Protection', '1; mode=block')).to.be
      .true;
    expect(
      setHeaderSpy.calledWith(
        'Referrer-Policy',
        'strict-origin-when-cross-origin'
      )
    ).to.be.true;
    expect(
      setHeaderSpy.calledWith(
        'Strict-Transport-Security',
        'max-age=31536000; includeSubDomains'
      )
    ).to.be.true;

    // Verify CSP header
    const cspCall = setHeaderSpy
      .getCalls()
      .find(call => call.args[0] === 'Content-Security-Policy');
    expect(cspCall).to.exist;
    if (cspCall) {
      const cspDirectives = cspCall.args[1].split('; ');
      expect(cspDirectives).to.include("default-src 'self'");
      expect(cspDirectives).to.include("script-src 'self'");
      expect(cspDirectives).to.include("style-src 'self'");
      expect(cspDirectives).to.include("img-src 'self' data:");
      expect(cspDirectives).to.include("font-src 'self' data:");
      expect(cspDirectives).to.include("connect-src 'self'");
      expect(cspDirectives).to.include("frame-ancestors 'self'");
      expect(cspDirectives).to.include("base-uri 'self'");
      expect(cspDirectives).to.include("form-action 'self'");
    }

    // Verify next was called
    expect(nextFunction.calledOnce).to.be.true;
  });

  it('should handle errors gracefully', () => {
    // Make setHeader throw an error
    setHeaderSpy.throws(new Error('Test error'));

    // Execute middleware and expect it to throw
    expect(() => {
      middleware.use(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );
    }).to.throw('Test error');

    // Verify next was not called when error occurs
    expect(nextFunction.called).to.be.false;
  });
});
