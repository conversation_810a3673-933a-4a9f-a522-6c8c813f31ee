import {FacilityMap} from '@ict/sdk-foundations/types';
import {AppConfigSettingSource} from '@ict/sdk-foundations/types/index.ts';
import {NextFunction, Request, Response} from 'express';
import httpContext from 'express-http-context';
import {AuthResult} from 'express-oauth2-jwt-bearer';
import {ContextService} from '../context/context-service.ts';
import {DiService} from '../di/type-di.ts';
import {IctError} from '../errors/ict-error.ts';
import {WinstonLogger} from '../log/winston-logger.ts';
import {ConfigStore} from '../stores/config-store.ts';

const FACILITY_ID_HEADER = 'ict-facility-id';

/**
 * This middleware adds facility information to the context including the facility id,
 * the facility maps, and the dataset id.
 */
@DiService()
export class FacilityMiddleware {
  constructor(
    private contextService: ContextService,
    private logger: <PERSON><PERSON><PERSON><PERSON>,
    private configStore: ConfigStore,
  ) {}

  public use = (req: Request, res: Response, next: NextFunction) => {
    httpContext.middleware(req, res, async () => {
      try {
        this.addFacilityIdToContext(req);
        await this.updateFacilityContext(req);
        next();
      } catch (error) {
        next(error);
      }
    });
  };

  /**
   * Adds the Auth0 metadata that gets added to "request.auth" to the context
   * for easier retrieval across the application.
   */
  async updateFacilityContext(req: Request) {
    if (req.auth) {
      const authResult: AuthResult = req.auth;
      const {organization, sub: userId, user_email} = authResult.payload;

      const roles = this.contextService.userRoles;
      // Add tenant facility mapping to context
      try {
        let facilityMaps = await this.configStore.findSpecificSettingValue(
          AppConfigSettingSource.tenant,
          undefined,
          'facility-maps',
        );
        if (!facilityMaps) {
          // If we don't find facility-maps at the tenant level, look at the default level
          facilityMaps = await this.configStore.findSpecificSettingValue(
            AppConfigSettingSource.default,
            undefined,
            'facility-maps',
          );
        }

        if (facilityMaps) {
          this.contextService.facilityMaps =
            facilityMaps.value as FacilityMap[];
        }
      } catch (error) {
        this.logger.error('Failed to load facility maps from config store', {
          error,
        });
        throw IctError.internalServerError(
          'Failed to load required facility configuration',
        );
      }

      // find facility map by id from header ict-facility-id
      const facilityId = this.contextService.selectedFacilityId;

      if (facilityId && !roles.includes(facilityId)) {
        this.logger.info('Facility id is not in the roles', {
          facilityId,
          roles,
        });
        throw IctError.unauthorized('Invalid facility id');
      }

      if (facilityId) {
        const facilityMap = this.contextService.facilityMaps?.find(
          map => map.id === facilityId,
        );
        const dataset = facilityMap?.dataset;
        if (dataset) {
          this.logger.info('Loaded dataset from facility map', {dataset});
          this.contextService.datasetId = dataset;
        }
      }

      this.logger.info('AuthResult', {organization, userId, user_email, roles});
    }
  }

  /**
   * Adds the facility id to the context.
   */
  addFacilityIdToContext(req: Request) {
    const facilityId = req.header(FACILITY_ID_HEADER);
    this.contextService.selectedFacilityId = facilityId ?? null;
  }
}
