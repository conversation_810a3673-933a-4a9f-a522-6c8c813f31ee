import {NextFunction, Request, Response} from 'express';
import {DiService} from '../di/type-di.ts';
import {EnvironmentService} from '../services/environment-service.ts';

/**
 * Express middleware that adds security headers to all responses
 */
@DiService()
export class SecurityMiddleware {
  constructor(private envService: EnvironmentService) {}

  public use = (req: Request, res: Response, next: NextFunction) => {
    // Add security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'SAMEORIGIN');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    res.setHeader(
      'Strict-Transport-Security',
      'max-age=31536000; includeSubDomains'
    );
    // Add CSP header
    const cspDirectives = [
      "default-src 'self'",
      "script-src 'self'",
      "style-src 'self'",
      "img-src 'self' data:",
      "font-src 'self' data:",
      "connect-src 'self'",
      "frame-ancestors 'self'",
      "base-uri 'self'",
      "form-action 'self'",
    ].join('; ');

    res.setHeader('Content-Security-Policy', cspDirectives);

    next();
  };
}
