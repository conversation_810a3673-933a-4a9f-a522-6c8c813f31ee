import {
  BetweenComparison,
  Co<PERSON><PERSON>on,
  <PERSON><PERSON><PERSON><PERSON>,
  FilterFieldTree,
} from './filter-fields.ts';

/**
 * SQL syntax specific filter comparison.
 */
export class SQLComparison extends Comparison {
  buildWhereContents(): string {
    return `${this.type} ${this.compareToParameter}`;
  }
}

/**
 * SQL syntax specific filter between comparison.
 */
export class SQLBetweenComparison extends BetweenComparison {
  buildWhereContents(): string {
    return `${this.type} ${this.lowerBoundParameter} AND ${this.higherBoundParameter}`;
  }
}

/**
 * SQL syntax specific singular filter field.
 */
export class SQLFilterField extends FilterField {
  buildWhereContents(): string {
    let filterStrategyStr = '';
    if (this.filterStrategy instanceof Comparison) {
      filterStrategyStr = this.filterStrategy.buildWhereContents();
    } else {
      filterStrategyStr = `${this.filterStrategy}`;
    }
    return `${this.columnName} ${filterStrategyStr}`;
  }
}

/**
 * SQL syntax specific tree of filters.
 */
export class <PERSON><PERSON><PERSON>ilter<PERSON>ield<PERSON>ree extends FilterFieldTree {
  buildWhereContents(): string {
    return `(${this.left.buildWhereContents()}) ${
      this.andOr
    } (${this.right.buildWhereContents()})`;
  }
}
