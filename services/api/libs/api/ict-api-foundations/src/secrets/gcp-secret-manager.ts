import {SecretManagerServiceClient} from '@google-cloud/secret-manager';
import {<PERSON><PERSON>ana<PERSON>, SecretManagerQuery} from './secret-manager.ts';
import {DiService} from '../di/type-di.ts';

export class GCPSecretManagerQuery extends SecretManagerQuery {
  /**
   * Name of the version of secret to get.
   */
  name: string = '';
  projectId?: string;
}

@DiService()
export class GCPSecretManager extends SecretManager {
  async get(query: SecretManagerQuery): Promise<string> {
    const gcpQuery = query as GCPSecretManagerQuery;
    if (!gcpQuery) {
      throw new Error('Invalid Secret query.');
    }

    const [version] = await this.client(gcpQuery.projectId).accessSecretVersion(
      {
        name: gcpQuery.name,
      },
    );
    if (version.payload && version.payload.data) {
      return version.payload.data.toString();
    }

    throw new Error('Invalid Secret.');
  }

  client(projectId?: string) {
    return new SecretManagerServiceClient({projectId});
  }
}
