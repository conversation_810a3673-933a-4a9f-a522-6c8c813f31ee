import {MigrationInterface, QueryRunner} from 'typeorm';

export class ICT6747FacilitySetting1747870108704 implements MigrationInterface {
  name = 'ICT6747FacilitySetting.ts1747870108704';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'CREATE TABLE "config"."facility_setting" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "description" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "settingId" uuid NOT NULL, "valueNumber" double precision, "valueString" character varying, "valueJson" jsonb, "valueBoolean" boolean, "lastChangedBy" character varying NOT NULL DEFAULT \'unknown\', "facilityId" character varying NOT NULL, "allowedSettingValueId" uuid, CONSTRAINT "REL_c688aaa184954ad4b842cb0363" UNIQUE ("allowedSettingValueId"), CONSTRAINT "PK_8bf7f00d58461c47d444079cd31" PRIMARY KEY ("id"))'
    );
    await queryRunner.query(
      'ALTER TABLE "config"."facility_setting" ADD CONSTRAINT "FK_8763d9e98b1d72b1ccdf4a4bd4c" FOREIGN KEY ("settingId") REFERENCES "config"."setting"("id") ON DELETE NO ACTION ON UPDATE NO ACTION'
    );
    await queryRunner.query(
      'ALTER TABLE "config"."facility_setting" ADD CONSTRAINT "FK_c688aaa184954ad4b842cb0363d" FOREIGN KEY ("allowedSettingValueId") REFERENCES "config"."allowed_setting_value"("id") ON DELETE NO ACTION ON UPDATE NO ACTION'
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE "config"."facility_setting" DROP CONSTRAINT "FK_c688aaa184954ad4b842cb0363d"'
    );
    await queryRunner.query(
      'ALTER TABLE "config"."facility_setting" DROP CONSTRAINT "FK_8763d9e98b1d72b1ccdf4a4bd4c"'
    );
    await queryRunner.query('DROP TABLE "config"."facility_setting"');
  }
}
