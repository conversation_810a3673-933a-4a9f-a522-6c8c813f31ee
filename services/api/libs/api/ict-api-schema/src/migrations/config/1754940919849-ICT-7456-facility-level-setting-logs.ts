import {MigrationInterface, QueryRunner} from 'typeorm';

export class ICT7456FacilityLevelSettingLogs1754940919849
  implements MigrationInterface
{
  name = 'ICT7456FacilityLevelSettingLogs1754940919849';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "config"."setting_log" ADD "facilityId" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "config"."setting_log" DROP COLUMN "facilityId"`,
    );
  }
}
