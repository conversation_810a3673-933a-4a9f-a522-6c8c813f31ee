import {MigrationInterface, QueryRunner} from 'typeorm';

export class ICT7058FacilityDefaultMetricConfig1749148682187
  implements MigrationInterface
{
  name = 'ICT7058FacilityDefaultMetricConfig1749148682187';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "default_config_id"',
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "facility_id" character varying NOT NULL',
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP CONSTRAINT "UQ_c34a2c15209519a4ee94080f6be"',
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "enabled"',
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "enabled" hstore',
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "active"',
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "active" hstore',
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP CONSTRAINT "UQ_b5047458d37c70e32b383dd0ae1"',
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ALTER COLUMN "enabled" SET NOT NULL',
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ALTER COLUMN "active" SET NOT NULL',
    );
    await queryRunner.query(
      'CREATE UNIQUE INDEX "UQ_default_metric_config_name" ON "process_flow"."default_metric_configurations" ("metric_config_name") ',
    );
    await queryRunner.query(
      'CREATE UNIQUE INDEX "UQ_custom_metric_config_facility" ON "process_flow"."custom_metric_configurations" ("metric_config_name", "facility_id") ',
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD CONSTRAINT "FK_b5047458d37c70e32b383dd0ae1" FOREIGN KEY ("metric_config_name") REFERENCES "process_flow"."default_metric_configurations"("metric_config_name") ON DELETE CASCADE ON UPDATE NO ACTION',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP CONSTRAINT "FK_b5047458d37c70e32b383dd0ae1"',
    );
    await queryRunner.query(
      'DROP INDEX "process_flow"."UQ_custom_metric_config_facility"',
    );
    await queryRunner.query(
      'DROP INDEX "process_flow"."UQ_default_metric_config_name"',
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ALTER COLUMN "active" DROP NOT NULL',
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ALTER COLUMN "enabled" DROP NOT NULL',
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD CONSTRAINT "UQ_b5047458d37c70e32b383dd0ae1" UNIQUE ("metric_config_name")',
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "active"',
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "active" boolean DEFAULT false',
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "enabled"',
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "enabled" boolean DEFAULT true',
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD CONSTRAINT "UQ_c34a2c15209519a4ee94080f6be" UNIQUE ("metric_config_name")',
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "facility_id"',
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "default_config_id" integer',
    );
  }
}
