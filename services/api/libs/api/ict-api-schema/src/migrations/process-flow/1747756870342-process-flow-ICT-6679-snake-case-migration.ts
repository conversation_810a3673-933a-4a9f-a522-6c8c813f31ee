import {MigrationInterface, QueryRunner} from 'typeorm';

export class ICT6679SnakeCaseMigration1747756870342
  implements MigrationInterface
{
  name = 'ICT6679SnakeCaseMigration1747756870342';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP CONSTRAINT "UQ_345d823e2c6f137000a5804e123"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "metricKey"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "factType"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "sourceSystem"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "displayName"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "configType"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "acceptableLowRange"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "acceptableHighRange"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "exampleMessage"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "matchConditions"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "redisParams"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "nameFormula"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "parentNodes"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "nodeName"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "metricType"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "timeWindowMins"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "graphOperation"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "redisOperation"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "metricUnits"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "huId"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "inboundArea"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "outboundArea"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "outboundNodeLabel"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "inboundParentNodes"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "outboundParentNodes"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP CONSTRAINT "UQ_827c98effda5a99ed01f95d22ed"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "metricKey"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "factType"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "sourceSystem"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "displayName"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "configType"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "acceptableLowRange"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "acceptableHighRange"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "exampleMessage"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "matchConditions"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "redisParams"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "nameFormula"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "parentNodes"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "nodeName"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "metricType"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "timeWindowMins"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "graphOperation"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "redisOperation"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "metricUnits"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "huId"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "inboundArea"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "outboundArea"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "outboundNodeLabel"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "inboundParentNodes"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "outboundParentNodes"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "metric_config_name" character varying(100) NOT NULL'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD CONSTRAINT "UQ_c34a2c15209519a4ee94080f6be" UNIQUE ("metric_config_name")'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "fact_type" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "source_system" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "display_name" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "config_type" character varying(50) NOT NULL'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "acceptable_low_range" double precision'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "acceptable_high_range" double precision'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "example_message" jsonb'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "match_conditions" jsonb NOT NULL'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "redis_params" jsonb'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "name_formula" jsonb'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "parent_nodes" character varying array'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "node_name" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "metric_type" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "time_window" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "graph_operation" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "redis_operation" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "metric_units" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "hu_id" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "inbound_area" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "outbound_area" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "outbound_node_label" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "inbound_parent_nodes" character varying array'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "outbound_parent_nodes" character varying array'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "metric_config_name" character varying(100) NOT NULL'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD CONSTRAINT "UQ_b5047458d37c70e32b383dd0ae1" UNIQUE ("metric_config_name")'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "fact_type" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "source_system" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "display_name" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "config_type" character varying(50) NOT NULL'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "acceptable_low_range" double precision'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "acceptable_high_range" double precision'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "example_message" jsonb'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "match_conditions" jsonb NOT NULL'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "redis_params" jsonb'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "name_formula" jsonb'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "parent_nodes" character varying array'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "node_name" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "metric_type" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "time_window" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "graph_operation" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "redis_operation" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "metric_units" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "hu_id" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "inbound_area" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "outbound_area" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "outbound_node_label" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "inbound_parent_nodes" character varying array'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "outbound_parent_nodes" character varying array'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ALTER COLUMN "active" DROP NOT NULL'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ALTER COLUMN "enabled" DROP NOT NULL'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "aggregation"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "aggregation" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ALTER COLUMN "active" DROP NOT NULL'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ALTER COLUMN "enabled" DROP NOT NULL'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "aggregation"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "aggregation" character varying(50)'
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "aggregation"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "aggregation" character varying(25)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ALTER COLUMN "enabled" SET NOT NULL'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ALTER COLUMN "active" SET NOT NULL'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "aggregation"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "aggregation" character varying(25)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ALTER COLUMN "enabled" SET NOT NULL'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ALTER COLUMN "active" SET NOT NULL'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "outbound_parent_nodes"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "inbound_parent_nodes"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "outbound_node_label"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "outbound_area"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "inbound_area"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "hu_id"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "metric_units"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "redis_operation"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "graph_operation"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "time_window"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "metric_type"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "node_name"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "parent_nodes"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "name_formula"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "redis_params"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "match_conditions"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "example_message"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "acceptable_high_range"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "acceptable_low_range"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "config_type"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "display_name"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "source_system"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "fact_type"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP CONSTRAINT "UQ_b5047458d37c70e32b383dd0ae1"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "metric_config_name"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "outbound_parent_nodes"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "inbound_parent_nodes"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "outbound_node_label"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "outbound_area"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "inbound_area"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "hu_id"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "metric_units"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "redis_operation"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "graph_operation"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "time_window"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "metric_type"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "node_name"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "parent_nodes"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "name_formula"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "redis_params"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "match_conditions"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "example_message"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "acceptable_high_range"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "acceptable_low_range"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "config_type"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "display_name"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "source_system"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "fact_type"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP CONSTRAINT "UQ_c34a2c15209519a4ee94080f6be"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" DROP COLUMN "metric_config_name"'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "outboundParentNodes" character varying array'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "inboundParentNodes" character varying array'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "outboundNodeLabel" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "outboundArea" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "inboundArea" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "huId" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "metricUnits" character varying(20)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "redisOperation" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "graphOperation" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "timeWindowMins" integer'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "metricType" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "nodeName" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "parentNodes" character varying array'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "nameFormula" jsonb'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "redisParams" jsonb'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "matchConditions" jsonb NOT NULL'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "exampleMessage" jsonb'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "acceptableHighRange" double precision'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "acceptableLowRange" double precision'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "configType" character varying(50) NOT NULL'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "displayName" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "sourceSystem" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "factType" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "metricKey" character varying(100) NOT NULL'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD CONSTRAINT "UQ_827c98effda5a99ed01f95d22ed" UNIQUE ("metricKey")'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "outboundParentNodes" character varying array'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "inboundParentNodes" character varying array'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "outboundNodeLabel" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "outboundArea" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "inboundArea" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "huId" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "metricUnits" character varying(20)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "redisOperation" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "graphOperation" character varying(50)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "timeWindowMins" integer'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "metricType" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "nodeName" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "parentNodes" character varying array'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "nameFormula" jsonb'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "redisParams" jsonb'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "matchConditions" jsonb NOT NULL'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "exampleMessage" jsonb'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "acceptableHighRange" double precision'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "acceptableLowRange" double precision'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "configType" character varying(50) NOT NULL'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "displayName" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "sourceSystem" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "factType" character varying(255)'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD "metricKey" character varying(100) NOT NULL'
    );
    await queryRunner.query(
      'ALTER TABLE "process_flow"."default_metric_configurations" ADD CONSTRAINT "UQ_345d823e2c6f137000a5804e123" UNIQUE ("metricKey")'
    );
  }
}
