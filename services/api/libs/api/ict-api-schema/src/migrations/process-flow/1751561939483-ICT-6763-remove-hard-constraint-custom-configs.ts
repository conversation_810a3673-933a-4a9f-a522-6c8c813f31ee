import {MigrationInterface, QueryRunner} from 'typeorm';

export class ICT6763RemoveHardConstraintCustomConfigs
  implements MigrationInterface
{
  name = 'ICT6763RemoveHardConstraintCustomConfigs.ts1751561939483';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "process_flow"."custom_metric_configurations" DROP CONSTRAINT "FK_b5047458d37c70e32b383dd0ae1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "process_flow"."custom_metric_configurations" ADD "defaultConfigId" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "process_flow"."custom_metric_configurations" ADD CONSTRAINT "FK_b0c903138475c7e856c14093e55" FOREIGN KEY ("defaultConfigId") REFERENCES "process_flow"."default_metric_configurations"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "process_flow"."custom_metric_configurations" DROP CONSTRAINT "FK_b0c903138475c7e856c14093e55"`,
    );
    await queryRunner.query(
      `ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "defaultConfigId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "process_flow"."custom_metric_configurations" ADD CONSTRAINT "FK_b5047458d37c70e32b383dd0ae1" FOREIGN KEY ("metric_config_name") REFERENCES "process_flow"."default_metric_configurations"("metric_config_name") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }
}
