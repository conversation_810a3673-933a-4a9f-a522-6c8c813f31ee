import {MigrationInterface, QueryRunner} from 'typeorm';

export class ICT6723AddEnabledBackToCustomMetricConfig1750162554321
  implements MigrationInterface
{
  name = 'ICT6723AddEnabledBackToCustomMetricConfig1750162554321';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "enabled" boolean NOT NULL DEFAULT true',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "enabled"',
    );
  }
}
