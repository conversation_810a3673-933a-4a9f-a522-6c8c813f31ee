import {MigrationInterface, QueryRunner} from 'typeorm';

export class ICT7136RemoveCustomEnabledField1749758228207
  implements MigrationInterface
{
  name = 'ICT7136RemoveCustomEnabledField1749758228207';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" DROP COLUMN "enabled"',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE "process_flow"."custom_metric_configurations" ADD "enabled" boolean NOT NULL DEFAULT true',
    );
  }
}
