import {MigrationInterface, QueryRunner} from 'typeorm';

export class ICT6561ProcessFlowMetricConfig1746648688835
  implements MigrationInterface
{
  name = 'ICT6561ProcessFlowMetricConfig1746648688835';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'CREATE TABLE "process_flow"."default_metric_configurations" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "description" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "metricKey" character varying(100) NOT NULL, "factType" character varying(255), "sourceSystem" character varying(255), "displayName" character varying(255), "configType" character varying(50) NOT NULL, "acceptableLowRange" double precision, "acceptableHighRange" double precision, "active" boolean NOT NULL DEFAULT false, "enabled" boolean NOT NULL DEFAULT true, "exampleMessage" jsonb, "views" character varying array NOT NULL, "matchConditions" jsonb NOT NULL, "redisParams" jsonb, "nameFormula" jsonb, "label" character varying(255), "parentNodes" character varying array, "nodeName" character varying(255), "metricType" character varying(255), "timeWindowMins" integer, "aggregation" character varying(25), "graphOperation" character varying(50), "redisOperation" character varying(50), "metricUnits" character varying(20), "huId" character varying(255), "inboundArea" character varying(50), "outboundArea" character varying(50), "units" character varying(50), "outboundNodeLabel" character varying(50), "inboundParentNodes" character varying array, "outboundParentNodes" character varying array, CONSTRAINT "UQ_345d823e2c6f137000a5804e123" UNIQUE ("metricKey"), CONSTRAINT "PK_8da9a6b20126eac460ea89b7e17" PRIMARY KEY ("id"))'
    );
    await queryRunner.query(
      'CREATE TABLE "process_flow"."custom_metric_configurations" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "description" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "metricKey" character varying(100) NOT NULL, "factType" character varying(255), "sourceSystem" character varying(255), "displayName" character varying(255), "configType" character varying(50) NOT NULL, "acceptableLowRange" double precision, "acceptableHighRange" double precision, "active" boolean NOT NULL DEFAULT false, "enabled" boolean NOT NULL DEFAULT true, "exampleMessage" jsonb, "views" character varying array NOT NULL, "matchConditions" jsonb NOT NULL, "redisParams" jsonb, "nameFormula" jsonb, "label" character varying(255), "parentNodes" character varying array, "nodeName" character varying(255), "metricType" character varying(255), "timeWindowMins" integer, "aggregation" character varying(25), "graphOperation" character varying(50), "redisOperation" character varying(50), "metricUnits" character varying(20), "huId" character varying(255), "inboundArea" character varying(50), "outboundArea" character varying(50), "units" character varying(50), "outboundNodeLabel" character varying(50), "inboundParentNodes" character varying array, "outboundParentNodes" character varying array, "default_config_id" integer, CONSTRAINT "UQ_827c98effda5a99ed01f95d22ed" UNIQUE ("metricKey"), CONSTRAINT "PK_78a877e846a5233d3b5b64081ba" PRIMARY KEY ("id"))'
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'DROP TABLE "process_flow"."custom_metric_configurations"'
    );
    await queryRunner.query(
      'DROP TABLE "process_flow"."default_metric_configurations"'
    );
  }
}
