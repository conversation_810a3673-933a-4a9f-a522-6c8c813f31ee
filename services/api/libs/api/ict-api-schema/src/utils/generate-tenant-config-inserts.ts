import * as fs from 'fs';
import * as path from 'path';
import {tenantConfigs} from '../default-data/config/tenant-configs/index.ts';
import {TenantSettingOverride} from '../default-data/config/types/tenant-config-override.ts';

/**
 * Generates SQL insert statements for tenant-specific setting overrides.
 * These will update existing default settings with tenant-specific values.
 */

function generateTenantSettingInsert(
  tenantName: string,
  override: TenantSettingOverride,
): string {
  const settingName = `'${override.name}'`;

  // Determine which value field to use and generate appropriate SQL
  if (override.valueString !== undefined) {
    const valueString = override.valueString.replace(/'/g, "''"); // Escape single quotes
    return `-- Update setting for tenant: ${tenantName}
UPDATE "config"."setting" 
SET "defaultValueString" = '${valueString}',
    "updatedAt" = now()
WHERE "name" = ${settingName};`;
  }

  if (override.valueNumber !== undefined) {
    return `-- Update setting for tenant: ${tenantName}
UPDATE "config"."setting" 
SET "defaultValueNumber" = ${override.valueNumber},
    "updatedAt" = now()
WHERE "name" = ${settingName};`;
  }

  if (override.valueBoolean !== undefined) {
    return `-- Update setting for tenant: ${tenantName}
UPDATE "config"."setting" 
SET "defaultValueBoolean" = ${override.valueBoolean},
    "updatedAt" = now()
WHERE "name" = ${settingName};`;
  }

  if (override.valueJson !== undefined) {
    const jsonValue = JSON.stringify(override.valueJson).replace(/'/g, "''");
    return `-- Update setting for tenant: ${tenantName}
UPDATE "config"."setting" 
SET "defaultValueJson" = '${jsonValue}'::jsonb,
    "updatedAt" = now()
WHERE "name" = ${settingName};`;
  }

  console.warn(
    `No value specified for setting ${override.name} in tenant ${tenantName}`,
  );
  return '';
}

// Generate SQL for each tenant
tenantConfigs.forEach(tenantConfig => {
  console.log(
    `Generating tenant-specific config for: ${tenantConfig.tenantName}`,
  );

  const sqlCommands = tenantConfig.settingOverrides
    .map(override =>
      generateTenantSettingInsert(tenantConfig.tenantName, override),
    )
    .filter(sql => sql.length > 0);

  if (sqlCommands.length === 0) {
    console.log(
      `No settings to override for tenant: ${tenantConfig.tenantName}`,
    );
    return;
  }

  const sql = [
    `-- Tenant-specific configuration overrides for: ${tenantConfig.tenantName}`,
    '-- Generated automatically - DO NOT EDIT MANUALLY',
    '',
    ...sqlCommands,
    '',
  ].join('\n');

  // Create tenant-specific SQL file
  const outputDir = 'src/default-data/config/sql/tenant-configs';
  const outputFile = path.join(
    outputDir,
    `${tenantConfig.tenantName}-config-overrides.sql`,
  );

  // Ensure directory exists
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, {recursive: true});
  }

  // Write the SQL file
  fs.writeFileSync(outputFile, sql);
  console.log(`Generated: ${outputFile}`);
});

console.log('Tenant configuration generation complete!');

export {};
