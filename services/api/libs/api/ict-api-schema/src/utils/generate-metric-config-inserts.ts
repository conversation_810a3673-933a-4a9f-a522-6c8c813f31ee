/* eslint-disable no-console */
import * as fs from 'fs';
import {IctError} from 'ict-api-foundations';
import {defaultMetricConfigs} from '../default-data/process-flow/default-metric-configs.ts';
import {MetricConfig} from '../default-data/process-flow/types/metric-configuration.ts';

/**
 * This script generates SQL insert statements for default metric configurations.
 * It processes all metric configs from the default-metric-configs.ts file, which
 * combines configurations from various facts (bin_utilization, connection_movement, etc).
 *
 * High-level flow:
 * 1. Process each metric config from the combined defaultMetricConfigs array
 * 2. Convert each config into a SQL INSERT statement with ON CONFLICT update
 * 3. Write all generated SQL statements to the output file
 */

// Define where the generated SQL will be written
const outputFile =
  'src/default-data/process-flow/sql/insert-default-metric-configs.sql';

/**
 * Converts a single metric config into a SQL INSERT statement.
 * Handles all data types (arrays, objects, strings, etc.) and generates
 * appropriate SQL syntax for each.
 */
function generateMetricConfigInsert(config: MetricConfig) {
  // Get all fields from the config object
  const fields = Object.keys(config);
  const columnNames = fields.map(field => `"${field}"`).join(', ');

  // Convert each field value to its SQL representation
  const values = fields
    .map(field => {
      const value = config[field as keyof MetricConfig];

      // Handle different types
      if (value === null || value === undefined) {
        return 'NULL';
      }

      // Handle arrays (like views, parentNodes, inboundParentNodes, outboundParentNodes)
      // These are stored as PostgreSQL native arrays
      // Example: ["facility", "multishuttle"] becomes ARRAY['facility', 'multishuttle']
      if (Array.isArray(value)) {
        // Map each array element to a quoted string and join with commas
        // This creates a PostgreSQL array literal with proper string escaping
        return `ARRAY[${value.map(v => `'${v}'`).join(', ')}]`;
      }

      // Handle objects (like matchConditions, redisParams, nameFormula)
      // These are stored as PostgreSQL JSONB type
      // Example: { "key": "value" } becomes '{"key": "value"}'::jsonb
      if (typeof value === 'object') {
        // Convert the object to a JSON string and cast it to JSONB
        // This allows storing complex nested structures and querying them later
        return `'${JSON.stringify(value)}'::jsonb`;
      }

      if (typeof value === 'boolean') {
        return value ? 'true' : 'false';
      }

      if (typeof value === 'string') {
        return `'${value.replace(/'/g, "''")}'`;
      }

      if (typeof value === 'number') {
        return String(value);
      }

      throw IctError.badRequest(
        `Unsupported type for field ${field}: ${typeof value}`
      );
    })
    .join(', ');

  // Build the ON CONFLICT update clause to update all fields
  // map over the fields and return a string that updates the field
  const updates = fields
    .map(field => `      "${field}" = EXCLUDED."${field}"`)
    .join(',\n');

  return `INSERT INTO "process_flow"."default_metric_configurations" (${columnNames}) 
    VALUES (${values}) 
    ON CONFLICT(metric_config_name) DO UPDATE
    SET 
${updates}
    ;`;
}

/**
 * Main function to generate SQL inserts for all default metric configs
 */
function generateSqlInserts() {
  // Process each metric config from the combined array
  const insertCommands: string[] = [];

  for (const config of defaultMetricConfigs) {
    try {
      const insertCommand = generateMetricConfigInsert(config);
      insertCommands.push(insertCommand);
    } catch (error) {
      console.error(
        `Error processing config ${config.metric_config_name}:`,
        error
      );
      throw IctError.unprocessableRequest(
        'SQL generation failed due to invalid config data',
        error
      );
    }
  }

  // Combine all successful INSERT statements into a single SQL string
  const sql = insertCommands.join('\n');

  // Write the generated SQL to the output file
  // The file is marked as auto-generated to prevent manual edits
  fs.writeFileSync(
    outputFile,
    `-- ****THIS FILE IS AUTO-GENERATED. DO NOT EDIT IT DIRECTLY******\n${sql}`
  );

  console.log('Default metric config sql generation complete.');
}

// Execute the main function
try {
  generateSqlInserts();
} catch (error) {
  console.error('SQL generation failed. No file was created.', error);

  // Ensure we're throwing an IctError
  if (error instanceof IctError) {
    throw error; // Re-throw the original IctError
  } else {
    // Wrap non-IctError in an IctError
    throw IctError.internalServerError(
      'SQL generation failed unexpectedly',
      error
    );
  }
}
