import {Entity, Column} from 'typeorm';
import {ICTEntity} from '../ict-entity.ts';
import type {
  ConfigType,
  GraphOperation,
  NodeLabel,
  MatchConditions,
  RedisParams,
  NameConfig,
  Aggregation,
  RedisOperation,
  TimeWindow,
} from '../../default-data/process-flow/types/metric-configuration.ts';

/**
 * Base entity for metric configurations.
 * Contains all shared fields between default and custom configurations.
 * Does not include any unique constraints or indexes.
 */
@Entity()
export class BaseMetricConfigurationEntity extends ICTEntity {
  /**
   * The unique identifier for this metric configuration.
   * This is used to reference the configuration in custom configurations and facility settings.
   * Example: shuttle_inventory_totes_shuffle_movements
   */
  @Column({length: 100, name: 'metric_config_name'})
  metricConfigName!: string;

  /**
   * The type of fact this metric processes
   * e.g. multishuttle_movement
   */
  @Column({length: 255, nullable: true, name: 'fact_type'})
  factType?: string;

  /**
   * The source system of the facts
   * e.g. diq
   */
  @Column({length: 255, nullable: true, name: 'source_system'})
  sourceSystem?: string;

  /**
   * Display name for the UI
   */
  @Column({length: 255, nullable: true, name: 'display_name'})
  displayName?: string;

  /**
   * Type of configuration
   */
  @Column({type: 'varchar', length: 50, name: 'config_type'})
  configType!: ConfigType;

  /**
   * Acceptable low range
   * Used to indicate if the actual metric value is acceptable
   */
  @Column({type: 'float', nullable: true, name: 'acceptable_low_range'})
  acceptableLowRange?: number;

  /**
   * Acceptable high range
   * Used to indicate if the actual metric value is acceptable
   */
  @Column({type: 'float', nullable: true, name: 'acceptable_high_range'})
  acceptableHighRange?: number;

  /**
   * Example message for testing
   */
  @Column({type: 'jsonb', nullable: true, name: 'example_message'})
  exampleMessage?: unknown;

  /**
   * The hierarchy graph view at which this metric applies
   * Example: `["facility", "multishuttle"]`
   */
  @Column({type: 'varchar', array: true})
  views!: string[];

  /**
   * Defines the conditions an event must meet for this metric to be processed
   */
  @Column({type: 'jsonb', name: 'match_conditions'})
  matchConditions!: MatchConditions;

  /**
   * Optional parameters required for metric processing
   * Example: `{"row_hash": "{row_hash}"}` dynamically fills `row_hash` with event data
   */
  @Column({type: 'jsonb', nullable: true, name: 'redis_params'})
  redisParams?: RedisParams;

  /**
   * Defines how to dynamically generate metric names based on source fields
   */
  @Column({type: 'jsonb', nullable: true, name: 'name_formula'})
  nameFormula?: NameConfig;

  /**
   * The label for the node. Defaults to "Area"
   */
  @Column({type: 'varchar', length: 255, nullable: true})
  label?: NodeLabel;

  /**
   * Defines the parent node hierarchy for this metric
   * Can be:
   * - undefined: No parent nodes
   * - string: Single parent node name
   * - string[]: Array of parent node names in hierarchical order
   * Example: `"multishuttle"` or `["multishuttle", "aisle_code"]`
   */
  @Column({type: 'varchar', array: true, nullable: true, name: 'parent_nodes'})
  parentNodes?: string[];

  // Node-specific fields
  @Column({length: 255, nullable: true, name: 'node_name'})
  nodeName?: string;

  @Column({length: 255, nullable: true, name: 'metric_type'})
  metricType?: string;

  @Column({type: 'varchar', length: 50, nullable: true, name: 'time_window'})
  timeWindow?: TimeWindow;

  @Column({type: 'varchar', length: 50, nullable: true, name: 'aggregation'})
  aggregation?: Aggregation;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    name: 'graph_operation',
  })
  graphOperation?: GraphOperation;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    name: 'redis_operation',
  })
  redisOperation?: RedisOperation;

  @Column({length: 50, nullable: true, name: 'metric_units'})
  metricUnits?: string;

  // Edge-specific fields
  @Column({length: 255, nullable: true, name: 'hu_id'})
  huId?: string;

  @Column({length: 50, nullable: true, name: 'inbound_area'})
  inboundArea?: string;

  @Column({length: 50, nullable: true, name: 'outbound_area'})
  outboundArea?: string;

  @Column({length: 50, nullable: true})
  units?: string;

  @Column({
    type: 'varchar',
    length: 255,
    name: 'outbound_node_label',
    nullable: true,
  })
  outboundNodeLabel?: NodeLabel;

  @Column({
    type: 'varchar',
    array: true,
    nullable: true,
    name: 'inbound_parent_nodes',
  })
  inboundParentNodes?: string[];

  @Column({
    type: 'varchar',
    array: true,
    nullable: true,
    name: 'outbound_parent_nodes',
  })
  outboundParentNodes?: string[];
}
