import {USER_WRITABLE_GROUP_NAME} from '../../user-writable-group-name.ts';
import {DefaultSettingDefinition} from '../default-setting-definition.ts';

export const DefaultUserFavorites: DefaultSettingDefinition = {
  name: 'user-favorites',
  group: USER_WRITABLE_GROUP_NAME,
  dataType: 'json',
  description: 'User-specific favorite pages stored as route IDs',
  defaultValueJson: {
    favorites: [],
  },
  jsonSchema: {
    $schema: 'http://json-schema.org/draft-07/schema#',
    title: 'Schema for user favorite pages',
    type: 'object',
    properties: {
      favorites: {
        type: 'array',
        items: {
          type: 'string',
          description: 'Route ID of the favorited page',
        },
      },
    },
    required: ['favorites'],
  },
};
