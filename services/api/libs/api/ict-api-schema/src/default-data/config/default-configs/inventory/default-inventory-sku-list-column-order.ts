import {DefaultSettingDefinition} from '../default-setting-definition.ts';

export const DefaultInventorySkuListColumnOrder: DefaultSettingDefinition = {
  name: 'inventory-sku-list-column-order',
  dataType: 'json',
  description:
    'Defines the order of the fields in the inventory sku list table on the inventory overview page',
  defaultValueJson: [
    'sku',
    'quantityAvailable',
    'quantityAllocated',
    'maxContainers',
    'skuPositions',
    'contOverage',
    'daysOnHand',
    'averageDailyQuantity',
    'averageDailyOrders',
    'latestActivityDateTimestamp',
    'latestCycleCountTimestamp',
    'description',
    'targetMultiplicity',
    'velocityClassification',
  ],
  jsonSchema: {
    $schema: 'http://json-schema.org/draft-07/schema#',
    title: 'Generated schema for Root',
    type: 'array',
    items: {
      type: 'string',
    },
  },
};
