import {DefaultSettingDefinition} from '../default-setting-definition.ts';

export const DefaultProcessFlowDetailPanelLayout: DefaultSettingDefinition = {
  name: 'ict-facility-process-flow-detail-panel-layout',
  dataType: 'json',
  description:
    'The layout of metrics and metric groups in the process flow detail panel.',
  defaultValueJson: {},
  jsonSchema: {
    $schema: 'http://json-schema.org/draft-07/schema#',
    type: 'object',
    additionalProperties: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
          },
          metrics: {
            type: 'array',
            items: {
              type: 'string',
            },
          },
        },
        required: ['name', 'metrics'],
        additionalProperties: false,
      },
    },
  },
};
