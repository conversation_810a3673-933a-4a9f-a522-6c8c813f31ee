import {DefaultSettingDefinition} from '../default-setting-definition.ts';

export const DefaultTableauTopLevelProjectName: DefaultSettingDefinition = {
  name: 'tableau-top-level-project-name',
  dataType: 'string',
  description:
    'The Tableau project name to act as the top level project for the UI navigation menu. Only workbooks in the subtree of this project will be shown in the UI. When not set or if the name is not found in the list of retrieved projects, workbooks from all projects will be shown. Project name is case-insensitive.',
  defaultValueString: '',
};
