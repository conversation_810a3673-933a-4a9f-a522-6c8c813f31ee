import {AppConfigSettingDataType} from '@ict/sdk-foundations/types/index.ts';
import {AppConfigSettingTag} from '@ict/sdk-foundations/types/config/app-config-setting-tag.ts';
import {AppConfigSettingName} from '@ict/sdk-foundations/types/config/app-config-setting-name.ts';

export interface DefaultSettingDefinition<T = unknown> {
  name: AppConfigSettingName;
  group?: string;
  description?: string;
  dataType: AppConfigSettingDataType;
  defaultValueBoolean?: boolean; // (used with boolean dataType)
  defaultValueString?: string; // (used with string dataType)
  defaultValueNumber?: number; // (used with number dataType)
  defaultValueJson?: T; // (used with json dataType)
  jsonSchema?: unknown; // (used with json dataType)
  tags?: AppConfigSettingTag[];
  parentSetting?: string; // (used in self-referencing Many-To-One relation, references Setting.name)
}

type DefaultConfigSetting<T extends AppConfigSettingDataType> = {
  name: AppConfigSettingName;
  description: string;
  group?: string;
  dataType: T;
};

export type StringTypeDefaultConfigSetting = DefaultConfigSetting<'string'> & {
  defaultValueString?: string;
};

export type BooleanTypeDefaultConfigSetting =
  DefaultConfigSetting<'boolean'> & {
    defaultValueBoolean?: boolean;
  };

export type NumberTypeDefaultConfigSetting = DefaultConfigSetting<'number'> & {
  defaultValueNumber?: number;
};

export type JsonTypeDefaultConfigSetting = DefaultConfigSetting<'json'> & {
  jsonSchema: unknown;
  defaultValueJson?: unknown;
};
