import {DefaultSettingDefinition} from '../default-setting-definition.ts';

export const DefaultInventoryStorageUtilizationAisleFilter: DefaultSettingDefinition =
  {
    name: 'inventory-storage-utilization-aisle-filter-list',
    dataType: 'json',
    description:
      'The list of aisles that the storage utilization KPI is filtered by. This list should either be empty or contain a list of valid aisles from the aisle column of fct_bin_utilization.',
    defaultValueJson: {
      aisleCodes: [],
    },
    jsonSchema: {
      $schema: 'http://json-schema.org/draft-07/schema#',
      title: 'Generated schema for Root',
      type: 'object',
      properties: {
        areaCodes: {
          type: 'array',
          items: {
            type: 'string',
          },
        },
      },
      required: ['aisleCodes'],
    },
  };
