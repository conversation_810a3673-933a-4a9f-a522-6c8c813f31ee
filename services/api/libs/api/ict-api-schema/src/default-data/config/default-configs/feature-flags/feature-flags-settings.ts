import {DefaultSettingDefinition} from '../default-setting-definition.ts';

const FEATURE_FLAG_GROUP_NAME = 'feature-flags';
enum FeatureFlagType {
  // a button, link, or other means of invoking an action that opens details, invokes an api, etc.
  Action = 'Action',
  // i.e. a widget/card
  IndividualComponent = 'Individual component',
  // something that affects the style of a component or page
  Style = 'Style',
}

export const DefaultFeatureFlagSettings: DefaultSettingDefinition[] = [
  {
    name: 'ict-facility-process-flow-edge-animation-effect',
    group: FEATURE_FLAG_GROUP_NAME,
    description:
      'Edges will conditionally render as animated dashed lines based on a status value.',
    dataType: 'boolean',
    defaultValueBoolean: false,
    tags: [
      {key: 'page', value: 'Facility Process Flow'},
      {key: 'type', value: FeatureFlagType.Style},
    ],
  },
  {
    name: 'ict-facility-process-flow-node-alerts',
    group: FEATURE_FLAG_GROUP_NAME,
    description: 'Alerts will be shown within nodes in Facility Process Flow.',
    dataType: 'boolean',
    defaultValueBoolean: false,
    tags: [
      {key: 'page', value: 'Facility Process Flow'},
      {key: 'type', value: FeatureFlagType.Style},
    ],
  },
  {
    name: 'ict-facility-process-flow-polling',
    group: FEATURE_FLAG_GROUP_NAME,
    description:
      'Facility Process Flow page will poll for new data on a configured time interval.',
    dataType: 'boolean',
    defaultValueBoolean: false,
    tags: [
      {key: 'page', value: 'Facility Process Flow'},
      {key: 'type', value: FeatureFlagType.Action},
    ],
  },
  {
    name: 'data-explorer-debug-data',
    group: FEATURE_FLAG_GROUP_NAME,
    description:
      'The debug data section in the results for the Data Explorer page',
    dataType: 'boolean',
    defaultValueBoolean: false,
    tags: [{key: 'type', value: FeatureFlagType.IndividualComponent}],
  },
  {
    name: 'ict-facility-process-flow-node-drilldown-button',
    group: FEATURE_FLAG_GROUP_NAME,
    description:
      'Nodes will have a button that when clicked, will take the user to a more detailed view of the process flow within that area.',
    dataType: 'boolean',
    defaultValueBoolean: false,
    tags: [{key: 'page', value: 'Facility Process Flow'}],
  },
  {
    name: 'ict-facility-process-flow-node-metrics',
    group: FEATURE_FLAG_GROUP_NAME,
    description:
      'KPI metrics will be displayed within nodes in the Facility Process Flow Chart.',
    dataType: 'boolean',
    defaultValueBoolean: false,
    tags: [
      {key: 'page', value: 'Facility Process Flow'},
      {key: 'type', value: FeatureFlagType.Style},
    ],
  },
  {
    name: 'ict-facility-process-flow-edge-label-status-indicators',
    group: FEATURE_FLAG_GROUP_NAME,
    description:
      'Edge label text will visually indicate that the value is outside of the expected range.',
    dataType: 'boolean',
    defaultValueBoolean: false,
    tags: [
      {key: 'page', value: 'Facility Process Flow'},
      {key: 'type', value: FeatureFlagType.Style},
    ],
  },
  {
    name: 'ict-facility-process-flow-edge-label-status-indicators',
    group: FEATURE_FLAG_GROUP_NAME,
    description:
      'Edge label text will visually indicate that the value is outside of the expected range.',
    dataType: 'boolean',
    defaultValueBoolean: false,
    tags: [
      {key: 'page', value: 'Facility Process Flow'},
      {key: 'type', value: FeatureFlagType.Style},
    ],
  },
  {
    name: 'ict-facility-process-flow-edge-label-alerts',
    group: FEATURE_FLAG_GROUP_NAME,
    description:
      'Edge alerts shown in edge label badges will be displayed if present in the data.',
    dataType: 'boolean',
    defaultValueBoolean: false,
    tags: [
      {key: 'page', value: 'Facility Process Flow'},
      {key: 'type', value: FeatureFlagType.Style},
    ],
  },
  {
    name: 'ict-facility-process-flow-node-metrics',
    group: FEATURE_FLAG_GROUP_NAME,
    description:
      'KPI metrics will be displayed within nodes in the Facility Process Flow Chart.',
    dataType: 'boolean',
    defaultValueBoolean: false,
    tags: [
      {key: 'page', value: 'Facility Process Flow'},
      {key: 'type', value: FeatureFlagType.Style},
    ],
  },
  {
    name: 'ict-facility-process-flow-detail-panel',
    group: FEATURE_FLAG_GROUP_NAME,
    description:
      'Detail panel will open when an area node or edge is clicked and put into the selected state.',
    dataType: 'boolean',
    defaultValueBoolean: false,
    tags: [
      {key: 'page', value: 'Facility Process Flow'},
      {key: 'type', value: FeatureFlagType.Action},
    ],
  },
  {
    name: 'ict-facility-process-flow-config-management',
    group: FEATURE_FLAG_GROUP_NAME,
    description:
      'Facility Process Flow configuration management will be available in the admin controls menu. This will allow users to configure and customize functionality within the graph and detail panels.',
    dataType: 'boolean',
    defaultValueBoolean: false,
    tags: [
      {key: 'page', value: 'Facility Process Flow'},
      {key: 'type', value: FeatureFlagType.Action},
    ],
  },
  {
    name: 'ict-facility-process-flow-admin-controls',
    group: FEATURE_FLAG_GROUP_NAME,
    description:
      'If enabled, the button to open the admin controls modal will be shown in Process Flow when interactive mode is enabled.',
    dataType: 'boolean',
    defaultValueBoolean: false,
    tags: [
      {key: 'page', value: 'Facility Process Flow'},
      {key: 'type', value: FeatureFlagType.Action},
    ],
  },
  {
    name: 'ict-facility-process-flow-edge-labels',
    group: FEATURE_FLAG_GROUP_NAME,
    description:
      'If enabled, edges within Facility Process Flow will display edge labels.',
    dataType: 'boolean',
    defaultValueBoolean: false,
    tags: [
      {key: 'page', value: 'Facility Process Flow'},
      {key: 'type', value: FeatureFlagType.Style},
    ],
  },
  {
    name: 'ict-nav-help-content',
    group: FEATURE_FLAG_GROUP_NAME,
    description: 'The help icon content on the header bar',
    dataType: 'boolean',
    defaultValueBoolean: false,
    tags: [{key: 'type', value: FeatureFlagType.Action}],
  },
] as const;
