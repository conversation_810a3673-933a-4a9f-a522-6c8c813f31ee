import {DefaultSettingDefinition} from './default-setting-definition.ts';
import {DefaultSystemSettings} from './system/index.ts';
import {DefaultFeatureFlagSettings} from './feature-flags/index.ts';
import {DefaultDashboardConfigs} from './dashboards/index.ts';
import {DefaultOrderSettings} from './orders/index.ts';
import {DefaultWorkstationSettings} from './workstation/index.ts';
import {DefaultFacilityProcessFlowSettings} from './facility-process-flow/index.ts';
import {DefaultTableauSettings} from './tableau/index.ts';
import {DefaultDataExplorerSettings} from './data-explorer/index.ts';
import {DefaultInventorySettings} from './inventory/index.ts';

export const DefaultConfigSettings: DefaultSettingDefinition[] = [
  ...DefaultSystemSettings,
  ...DefaultFeatureFlagSettings,
  ...DefaultDashboardConfigs,
  ...DefaultOrderSettings,
  ...DefaultInventorySettings,
  ...DefaultWorkstationSettings,
  ...DefaultFacilityProcessFlowSettings,
  ...DefaultTableauSettings,
  ...DefaultDataExplorerSettings,
];
