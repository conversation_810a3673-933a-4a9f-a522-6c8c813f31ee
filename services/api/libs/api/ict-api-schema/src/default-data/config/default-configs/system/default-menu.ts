import {DefaultMenuConfig} from '@ict/sdk-foundations/types/index.ts';
import {DefaultSettingDefinition} from '../default-setting-definition.ts';

export const DefaultMenuSetting: DefaultSettingDefinition = {
  name: 'menu',
  dataType: 'json',
  description: 'The menu configuration for the UI',
  defaultValueJson: DefaultMenuConfig,
  jsonSchema: {
    $schema: 'http://json-schema.org/draft-07/schema#',
    title: 'Menu Configuration',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: {type: 'string'},
        label: {type: 'string'},
        icon: {type: 'string'},
        viewType: {type: 'string'},
        viewConfigId: {type: 'string'},
        link: {type: 'string'},
        helpLink: {type: 'string'},
        children: {type: 'array', items: {type: 'object'}},
        roles: {type: 'array', items: {type: 'string'}},
        localOnly: {type: 'boolean'},
        visible: {type: 'boolean'},
        allowedParentIds: {
          type: 'array',
          items: {type: 'string'},
          description: 'List of parent IDs this menu item can be moved under',
        },
      },
      required: ['id', 'label'],
      additionalProperties: true,
    },
  },
};
