export const DataQueryJsonSchema = {
  $schema: 'http://json-schema.org/draft-07/schema#',
  title: 'Data Query',
  type: 'object',
  properties: {
    id: {type: 'string'},
    label: {type: 'string'},
    description: {type: 'string'},
    filters: {type: 'array', items: {type: 'string'}},
    metadata: {
      type: 'object',
      properties: {unit: {type: 'string'}, category: {type: 'string'}},
    },
    dataSources: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: {type: 'string'},
          type: {type: 'string'},
          table: {type: 'string'},
        },
      },
    },
    parameters: {
      type: 'object',
      properties: {
        required: {type: 'array', items: {type: 'string'}},
      },
    },
    query: {type: 'string'},
  },
};
