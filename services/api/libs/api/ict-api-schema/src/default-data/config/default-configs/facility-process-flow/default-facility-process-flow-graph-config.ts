import {DefaultSettingDefinition} from '../default-setting-definition.ts';

export const DefaultFacilityProcessFlowGraphConfig: DefaultSettingDefinition = {
  name: 'ict-facility-process-flow-graph-config',
  dataType: 'json',
  description:
    'Stores configuration values for how to render the nodes, and edges within the graph and customize functionality within the graph.',
  defaultValueJson: {},
  jsonSchema: {
    $schema: 'http://json-schema.org/draft-07/schema#',
    title: 'Process Flow Graph Configuration Settings',
    type: 'object',
    additionalProperties: {
      type: 'object',
      additionalProperties: {
        type: 'object',
        properties: {
          metrics: {
            type: 'array',
            items: {
              type: 'string',
            },
          },
          hasChildren: {
            type: 'boolean',
          },
        },
        required: ['metrics', 'hasChildren'],
        additionalProperties: false,
      },
    },
  },
};
