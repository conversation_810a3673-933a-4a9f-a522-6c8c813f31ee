import {DefaultSettingDefinition} from '../default-setting-definition';

export const DefaultWorkstationContainerDepartureEvents: DefaultSettingDefinition =
  {
    name: 'workstation-container-departure-events',
    dataType: 'json',
    description:
      'List of event codes that indicate a container has departed a workstation',
    defaultValueJson: ['release', 'departure'],
    jsonSchema: {
      $schema: 'http://json-schema.org/draft-07/schema#',
      type: 'array',
      items: {
        type: 'string',
      },
    },
  };
