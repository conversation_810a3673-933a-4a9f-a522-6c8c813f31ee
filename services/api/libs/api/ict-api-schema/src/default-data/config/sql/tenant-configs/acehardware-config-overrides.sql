-- Tenant-specific configuration overrides for: acehardware
-- Generated automatically - DO NOT EDIT MANUALLY

-- Update setting for tenant: acehardware
UPDATE "config"."setting" 
SET "defaultValueJson" = '[{"id":"acehardware#jeffersonga","name":"Jefferson, GA","dataset":"acehardware_jeffersonga","default":false,"tenantId":"acehardware","facilityId":"jeffersonga"},{"id":"acehardware#plantcityfl","name":"Plant City, FL","dataset":"acehardware_plantcityfl","default":false,"tenantId":"acehardware","facilityId":"plantcityfl"},{"id":"acehardware#visaliaca","name":"Visalia, CA","dataset":"acehardware_visaliaca","default":false,"tenantId":"acehardware","facilityId":"visaliaca"},{"id":"acehardware#wilmertx","name":"Wilmer, TX","dataset":"acehardware_wilmertx1","default":true,"tenantId":"acehardware","facilityId":"wilmertx1"}]'::jsonb,
    "updatedAt" = now()
WHERE "name" = 'facility-maps';
