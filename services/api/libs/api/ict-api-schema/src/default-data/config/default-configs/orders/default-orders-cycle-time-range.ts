import {DefaultSettingDefinition} from '../default-setting-definition';

export const DefaultOrderCycleTimeRange: DefaultSettingDefinition = {
  name: 'cycle-time-state-range',
  dataType: 'json',
  description:
    'Defines the start and end states that are used to determine an order cycle',
  defaultValueJson: {
    startStates: ['RELEASED', 'CREATED'],
    endStates: ['COMPLETE', 'COMPLETED', 'PICK_COMPLETE'],
  },
  jsonSchema: {
    $schema: 'http://json-schema.org/draft-07/schema#',
    title: 'Generated schema for Root',
    type: 'object',
    properties: {
      startStates: {
        type: 'array',
        items: {type: 'string'},
        minItems: 1,
      },
      endStates: {
        type: 'array',
        items: {type: 'string'},
        minItems: 1,
      },
    },
    required: ['startStates', 'endStates'],
  },
};
