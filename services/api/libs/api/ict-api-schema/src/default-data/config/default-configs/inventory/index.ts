import {DefaultSettingDefinition} from '../default-setting-definition.ts';
import {DefaultInventoryHighImpactSKUPercentage} from './default-inventory-high-impact-sku-percentage.ts';
import {DefaultInventoryWorkAreaCodesFilter} from './default-inventory-work-area-codes-filter.ts';
import {DefaultInventorySkuListColumnOrder} from './default-inventory-sku-list-column-order.ts';
import {DefaultInventoryZoneMapping} from './default-inventory-zone-mapping.ts';
import {DefaultInventoryStorageUtilizationAreaFilter} from './default-inventory-storage-utilization-area-filter.ts';
import {DefaultInventoryStorageUtilizationAisleFilter} from './default-inventory-storage-utilization-aisle-filter.ts';

export const DefaultInventorySettings: DefaultSettingDefinition[] = [
  DefaultInventoryHighImpactSKUPercentage,
  DefaultInventoryWorkAreaCodesFilter,
  DefaultInventorySkuListColumnOrder,
  DefaultInventoryZoneMapping,
  DefaultInventoryStorageUtilizationAreaFilter,
  DefaultInventoryStorageUtilizationAisleFilter,
] as const;
