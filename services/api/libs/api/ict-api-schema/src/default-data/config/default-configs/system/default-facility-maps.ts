import {FacilityMapSchema} from '@ict/sdk-foundations/types/config/system/facility-map-def.ts';
import {JsonTypeDefaultConfigSetting} from '../default-setting-definition.ts';

export const DefaultFacilityMapsSetting: JsonTypeDefaultConfigSetting = {
  name: 'facility-maps',
  description:
    'Array of Facility Map Definitions. Each facility can optionally specify a tableauProject field to filter Tableau workbooks by facility.',
  group: 'facility-maps',
  dataType: 'json',
  jsonSchema: FacilityMapSchema,
  defaultValueJson: [],
};
