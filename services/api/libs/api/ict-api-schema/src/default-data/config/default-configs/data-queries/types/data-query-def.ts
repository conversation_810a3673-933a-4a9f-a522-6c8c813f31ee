export type DataQuery = {
  id: string;
  type: DataQueryType;
  label: string;
  description: string;
  filters: string[];
  isDraft: boolean;
  metadata: {
    unit?: string;
    category: string;
  };
  dataSources: DataQuerySource[];
  config?: string[];
  parameters: DataQueryParameters;
  query: string;
};

export enum DataQueryType {
  DATA_POINT = 'data-point',
  FILTER = 'filter',
  TREND = 'trend',
  GRID = 'grid',
}

export type DataQuerySource = {
  id: string;
  type: string;
  table: string;
};

export type DataQueryParameters = {
  required: string[];
};
