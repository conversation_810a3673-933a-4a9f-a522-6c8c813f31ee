import {DefaultSettingDefinition} from '../default-setting-definition';

export const DefaultInventoryWorkAreaCodesFilter: DefaultSettingDefinition = {
  name: 'inventory-work-area-code-filter-list',
  dataType: 'json',
  description:
    'The list of work area codes that the inventory overview page components should be filtered by.  This is used to query fct_inventory and hst_item_by_zone and filter by the work_area_code (joined to dim_work_area). This list should either be empty or contain a list of valid work_area_codes from dim_work_area.',
  defaultValueJson: {
    areaCodes: [],
  },
  jsonSchema: {
    $schema: 'http://json-schema.org/draft-07/schema#',
    title: 'Generated schema for Root',
    type: 'object',
    properties: {
      areaCodes: {
        type: 'array',
        items: {
          type: 'string',
        },
      },
    },
    required: ['areaCodes'],
  },
};
