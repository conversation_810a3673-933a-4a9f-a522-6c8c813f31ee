import {TenantConfigOverride} from '../types/tenant-config-override.ts';
import {aceHardwareConfig} from './acehardware.ts';
import {dematicConfig} from './dematic.ts';
import {ictDevelopmentConfig} from './ict_development.ts';
import {qaAutomationConfig} from './qa_automation.ts';
import {superiorUniformConfig} from './superior_uniform.ts';
import {ttiConfig} from './tti.ts';

// Export all tenant configs
export const tenantConfigs: TenantConfigOverride[] = [
  superiorUniformConfig,
  aceHardwareConfig,
  dematicConfig,
  ttiConfig,
  qaAutomationConfig,
  ictDevelopmentConfig,
];

// Helper function to get config by tenant name
export function getTenantConfig(
  tenantName: string,
): TenantConfigOverride | undefined {
  return tenantConfigs.find(config => config.tenantName === tenantName);
}
