import {DefaultSettingDefinition} from '../default-setting-definition.ts';

export const DefaultWorkstationDestinationLocationSuffixes: DefaultSettingDefinition =
  {
    name: 'workstation-destination-locations-suffixes',
    dataType: 'json',
    description:
      'List of workstation destination locations suffixes, used in filtering for order status',
    defaultValueJson: {
      suffixes: [
        'B1',
        'B2',
        'B3',
        'B4',
        'B5',
        'F1',
        'F2',
        'F3',
        'F4',
        'F5',
        'F6',
      ],
    },
    jsonSchema: {
      $schema: 'http://json-schema.org/draft-07/schema#',
      title: 'Generated schema for Root',
      type: 'object',
      properties: {
        suffixes: {
          type: 'array',
          items: {
            type: 'string',
          },
        },
      },
      required: ['suffixes'],
    },
  };
