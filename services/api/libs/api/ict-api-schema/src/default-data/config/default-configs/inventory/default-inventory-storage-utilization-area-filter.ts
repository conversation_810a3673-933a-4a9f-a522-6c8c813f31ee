import {DefaultSettingDefinition} from '../default-setting-definition.ts';

export const DefaultInventoryStorageUtilizationAreaFilter: DefaultSettingDefinition =
  {
    name: 'inventory-storage-utilization-area-filter-list',
    dataType: 'json',
    description:
      'The list of areas that the storage utilization KPI is filtered by. This list should either be empty or contain a list of valid areas from the area column of fct_bin_utilization.',
    defaultValueJson: {
      areaCodes: [],
    },
    jsonSchema: {
      $schema: 'http://json-schema.org/draft-07/schema#',
      title: 'Generated schema for Root',
      type: 'object',
      properties: {
        areaCodes: {
          type: 'array',
          items: {
            type: 'string',
          },
        },
      },
      required: ['areaCodes'],
    },
  };
