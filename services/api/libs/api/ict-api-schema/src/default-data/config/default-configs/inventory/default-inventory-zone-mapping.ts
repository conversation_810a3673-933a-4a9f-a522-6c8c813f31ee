import {DefaultSettingDefinition} from '../default-setting-definition.ts';

export const DefaultInventoryZoneMapping: DefaultSettingDefinition = {
  name: 'inventory-zone-mapping',
  dataType: 'json',
  description:
    'Defines the intended use for the inventory zone areas. Can be defined by code or specific zone name (ex GTP or GTP02)',
  defaultValueJson: {
    reserve_storage: ['VNA', 'CNV', 'ALT', 'RCV'],
    forward_pick: ['DMS', 'GTP', 'ASRS'],
  },
};
