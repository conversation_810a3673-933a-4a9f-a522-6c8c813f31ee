import {DefaultSettingDefinition} from '../default-setting-definition';

export const DefaultOrdersCompleteEventCodes: DefaultSettingDefinition = {
  name: 'order-complete-event-codes',
  dataType: 'json',
  description:
    'An array of the event codes that should signify that an order is complete. These codes are related to the gold_pick_task.event_code field.',
  defaultValueJson: {
    codes: ['COMPLETE', 'COMPLETED', 'PICK_COMPLETE'],
  },
  jsonSchema: {
    $schema: 'http://json-schema.org/draft-07/schema#',
    title: 'Generated schema for Root',
    type: 'object',
    properties: {
      codes: {
        type: 'array',
        items: {type: 'string'},
        minItems: 1,
      },
    },
    required: ['codes'],
  },
};
