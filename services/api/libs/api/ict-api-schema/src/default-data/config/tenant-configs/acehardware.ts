import {TenantConfigOverride} from '../types/tenant-config-override';

export const aceHardwareConfig: TenantConfigOverride = {
  tenantName: 'acehardware',
  settingOverrides: [
    {
      name: 'facility-maps',
      valueJson: [
        {
          id: 'acehardware#jeffersonga',
          name: 'Jefferson, GA',
          dataset: 'acehardware_jeffersonga',
          default: false,
        },
        {
          id: 'acehardware#plantcityfl',
          name: 'Plant City, FL',
          dataset: 'acehardware_plantcityfl',
          default: false,
        },
        {
          id: 'acehardware#visaliaca',
          name: 'Visalia, CA',
          dataset: 'acehardware_visaliaca',
          default: false,
        },
        {
          id: 'acehardware#wilmertx',
          name: '<PERSON>il<PERSON>, TX',
          dataset: 'acehardware_wilmertx1',
          default: true,
        },
      ],
    },
  ],
};
