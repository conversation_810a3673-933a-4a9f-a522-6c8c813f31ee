import {TenantConfigOverride} from '../types/tenant-config-override';

export const aceHardwareConfig: TenantConfigOverride = {
  tenantName: 'acehardware',
  settingOverrides: [
    {
      name: 'facility-maps',
      valueJson: [
        {
          id: 'acehardware#jeffersonga',
          name: 'Jefferson, GA',
          dataset: 'acehardware_jeffersonga',
          default: false,
          tenantId: 'acehardware',
          facilityId: 'jeffersonga',
        },
        {
          id: 'acehardware#plantcityfl',
          name: 'Plant City, FL',
          dataset: 'acehardware_plantcityfl',
          default: false,
          tenantId: 'acehardware',
          facilityId: 'plantcityfl',
        },
        {
          id: 'acehardware#visaliaca',
          name: 'Visalia, CA',
          dataset: 'acehardware_visaliaca',
          default: false,
          tenantId: 'acehardware',
          facilityId: 'visaliaca',
        },
        {
          id: 'acehardware#wilmertx',
          name: 'Wilmer, TX',
          dataset: 'acehardware_wilmertx1',
          default: true,
          tenantId: 'acehardware',
          facilityId: 'wilmertx1',
        },
      ],
    },
  ],
};
