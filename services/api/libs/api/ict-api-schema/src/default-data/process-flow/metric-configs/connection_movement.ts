import {MetricConfig} from '../types/metric-configuration';

/**
 * Metrics configuration for connection movements
 */
const connectionMovementMetricConfigs: MetricConfig[] = [
  {
    metric_config_name: 'receiving_outbound_units_edge',
    config_type: 'outbound-edge',
    description:
      'Hourly rate of outbound units departing the receiving area based on last 15 minutes.',
    fact_type: 'connection_movement',
    hu_id: 'handling_unit_code',
    match_conditions: {
      source_location_code: 'RECV-INDUCT',
      handling_unit_code: '^.+$',
    },
    outbound_area: 'receiving',
    label: 'Area',
    units: 'handling_unit',
    views: ['facility', 'multishuttle'],
  },

  // Multishuttle Edge Metric Configs
  // Multishuttle Inbound Edge Config
  {
    metric_config_name: 'multishuttle_inbound_totes_edge',
    config_type: 'inbound-edge',
    description:
      'Hourly rate of Inbound units arriving at the DMS picking buffer based on last 15 minutes.',
    fact_type: 'connection_movement',
    graph_operation: 'area_edge',
    hu_id: 'handling_unit_code',
    inbound_area: 'multishuttle',
    label: 'Area',
    match_conditions: {
      destination_location_code: 'MSAI.*PS',
      handling_unit_code: '^.+$',
    },
    redis_operation: 'event_set',
    views: ['facility', 'workstations'],
  },

  // Multishuttle Outbound Edge Config
  {
    metric_config_name: 'multishuttle_outbound_totes_edge',
    config_type: 'outbound-edge',
    description:
      'Hourly rate of outbound units departing the DMS picking buffer based on last 15 minutes.',
    fact_type: 'connection_movement',
    hu_id: 'handling_unit_code',
    match_conditions: {
      source_location_code: 'MSAI.*DS',
      handling_unit_code: '^.+$',
    },
    outbound_area: 'multishuttle',
    label: 'Area',
    units: 'handling_unit',
    views: ['facility', 'workstations'],
  },

  // Multishuttle Aisle Inbound Edge Config
  {
    metric_config_name: 'multishuttle_aisle_inbound_totes_edge',
    config_type: 'inbound-edge',
    description:
      'Hourly rate of inbound units arriving at the DMS picking buffer per aisle based on last 15 minutes.',
    fact_type: 'connection_movement',
    graph_operation: 'area_edge',
    hu_id: 'handling_unit_code',
    inbound_area: '{destination_location_code}',
    inbound_parent_nodes: ['multishuttle'],
    label: 'Aisle',
    match_conditions: {
      destination_location_code: 'MSAI.*PS',
      handling_unit_code: '^.+$',
    },
    name_formula: {
      source: '{destination_location_code}',
      pattern: 'MSAI(?P<aisle>\\d{2}).*',
      template: 'MSAI{aisle}',
    },
    redis_operation: 'event_set',
    views: ['multishuttle'],
  },

  // Multishuttle Aisle Outbound Edge Config
  {
    metric_config_name: 'multishuttle_aisle_outbound_totes_edge',
    config_type: 'outbound-edge',
    description:
      'Hourly rate of outbound units departing the DMS picking buffer per aisle based on last 15 minutes.',
    fact_type: 'connection_movement',
    hu_id: 'handling_unit_code',
    label: 'Aisle',
    match_conditions: {
      source_location_code: 'MSAI.*DS',
      handling_unit_code: '^.+$',
    },
    name_formula: {
      source: '{source_location_code}',
      pattern: 'MSAI(?P<aisle>\\d{2}).*',
      template: 'MSAI{aisle}',
    },
    outbound_area: '{source_location_code}',
    outbound_parent_nodes: ['multishuttle'],
    units: 'handling_unit',
    views: ['multishuttle'],
  },

  // Workstations Inbound from Multishuttle Outbound Edge Config
  {
    metric_config_name: 'workstations_inbound_totes_edge',
    config_type: 'inbound-edge',
    description:
      'Hourly rate of inbound units arriving at the Workstations area based on last 15 minutes.',
    fact_type: 'connection_movement',
    graph_operation: 'area_edge',
    hu_id: 'handling_unit_code',
    inbound_area: 'workstations',
    label: 'Station',
    match_conditions: {
      destination_location_code: 'M.*GTP.*D1',
      handling_unit_code: '^.+$',
    },
    redis_operation: 'event_set',
    views: ['facility', 'multishuttle'],
  },

  // Workstations Outbound to Multishuttle Inbound Edge Config
  {
    metric_config_name: 'workstations_outbound_totes_edge',
    config_type: 'outbound-edge',
    description:
      'Hourly rate of outbound units departing the Workstations area based on last 15 minutes.',
    fact_type: 'connection_movement',
    hu_id: 'handling_unit_code',
    match_conditions: {
      source_location_code: 'M.*GTP.*D1',
      handling_unit_code: '^.+$',
    },
    outbound_area: 'workstations',
    label: 'Area',
    units: 'handling_unit',
    views: ['facility', 'multishuttle'],
  },

  // Workstations Workstation Inbound Edge Config
  {
    metric_config_name: 'workstations_workstation_inbound_totes_edge',
    config_type: 'inbound-edge',
    description:
      'Hourly rate of inbound units arriving at each work station based on last 15 minutes.',
    fact_type: 'connection_movement',
    graph_operation: 'area_edge',
    hu_id: 'handling_unit_code',
    inbound_area: '{destination_location_code}',
    inbound_parent_nodes: ['workstations'],
    label: 'Station',
    match_conditions: {
      destination_location_code: '^M.*GTP.*(B[1-5]|F[1-6])$',
      handling_unit_code: '^.+$',
    },
    name_formula: {
      source: '{destination_location_code}',
      pattern: '(?P<workstation_code>.*)(.{2}$)', // remove the last two characters from the location code to get the workstation code
      template: '{workstation_code}',
    },
    redis_operation: 'event_set',
    views: ['workstations'],
  },

  // Workstations Workstation Outbound Edge Config
  {
    metric_config_name: 'workstations_workstation_outbound_totes_edge',
    config_type: 'outbound-edge',
    description:
      'Hourly rate of outbound units departing each work station based on last 15 minutes.',
    fact_type: 'connection_movement',
    hu_id: 'handling_unit_code',
    label: 'Station',
    match_conditions: {
      source_location_code: '^M.*GTP.*D1$',
      handling_unit_code: '^.+$',
    },
    name_formula: {
      source: '{source_location_code}',
      pattern: '(?P<workstation_code>.*)(.{2}$)', // remove the last two characters from the location code to get the workstation code
      template: '{workstation_code}',
    },
    outbound_area: '{source_location_code}',
    outbound_parent_nodes: ['workstations'],
    units: 'handling_unit',
    views: ['workstations'],
  },

  // Receiving Area Metrics
  // Receiving Stock Time Start
  {
    metric_config_name: 'receiving_stock_time_start',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total time between a unit being inducted into a stock location and being inducted to the DMS picking buffer.',
    fact_type: 'connection_movement',
    graph_operation: 'area_node',
    match_conditions: {
      destination_location_code: 'RECV-INDUCT',
      handling_unit_code: '^.+$',
    },
    metric_type: 'stock_time',
    metric_units: 'mins',
    node_name: 'receiving',
    label: 'Area',
    redis_operation: 'cycle_time_start',
    redis_params: {instance_id: '{handling_unit_code}'},
    time_window: '60m_set',
    views: ['facility', 'multishuttle'],
  },

  // Receiving Stock Time End
  {
    metric_config_name: 'receiving_stock_time_stop',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total time between a unit being inducted into a stock location and being inducted to the DMS picking buffer.',
    fact_type: 'connection_movement',
    graph_operation: 'area_node',
    match_conditions: {
      source_location_code: 'RECV-INDUCT',
      handling_unit_code: '^.+$',
    },
    metric_type: 'stock_time',
    metric_units: 'mins',
    node_name: 'receiving',
    label: 'Area',
    redis_operation: 'cycle_time_stop',
    redis_params: {
      instance_id: '{handling_unit_code}',
    },
    time_window: '60m_set',
    views: ['facility', 'multishuttle'],
  },

  // Miniload Inbound Edge Config
  {
    metric_config_name: 'miniload_inbound_totes_edge',
    config_type: 'inbound-edge',
    description:
      'Hourly rate of inbound units arriving at the Miniload area based on last 15 minutes.',
    fact_type: 'connection_movement',
    graph_operation: 'area_edge',
    hu_id: 'handling_unit_code',
    inbound_area: 'miniload',
    label: 'Area',
    match_conditions: {
      destination_location_code: 'ML.*I.*',
      handling_unit_code: '^.+$',
    },
    redis_operation: 'event_set',
    views: ['facility'],
  },

  // Miniload Outbound Edge Config
  {
    metric_config_name: 'miniload_outbound_totes_edge',
    config_type: 'outbound-edge',
    description:
      'Hourly rate of outbound units departing the Miniload area based on last 15 minutes.',
    fact_type: 'connection_movement',
    hu_id: 'handling_unit_code',
    match_conditions: {
      source_location_code: 'ML.*O.*',
      handling_unit_code: '^.+$',
    },
    outbound_area: 'miniload',
    label: 'Area',
    units: 'handling_unit',
    views: ['facility'],
  },
];

export default connectionMovementMetricConfigs;
