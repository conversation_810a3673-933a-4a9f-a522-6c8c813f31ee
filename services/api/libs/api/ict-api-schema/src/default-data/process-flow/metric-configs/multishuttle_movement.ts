import {MetricConfig} from '../types/metric-configuration';

/**
 * Metrics configuration for multishuttle movements
 */
const multishuttleMovementMetricConfigs: MetricConfig[] = [
  // Distinct aisles count
  {
    metric_config_name: 'multishuttle_num_distinct_aisles',
    aggregation: 'count',
    config_type: 'node',
    description: 'Number of aisles within the DMS picking buffer',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      aisle_code: '^.+$',
    },
    metric_type: 'num_aisles',
    node_name: 'multishuttle',
    label: 'Area',
    redis_operation: 'distinct_item_count',
    redis_params: {
      identifier: '{aisle_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  // Distinct shuttles count
  {
    metric_config_name: 'multishuttle_num_distinct_shuttles',
    aggregation: 'count',
    config_type: 'node',
    description: 'Number of shuttles within the DMS picking buffer',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      shuttle_code: '^.+$',
    },
    metric_type: 'num_shuttles',
    node_name: 'multishuttle',
    label: 'Area',
    redis_operation: 'distinct_item_count',
    redis_params: {
      identifier: '{shuttle_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  // Distinct lifts count
  {
    metric_config_name: 'multishuttle_num_distinct_lifts',
    aggregation: 'count',
    config_type: 'node',
    description: 'Number of lifts within the DMS picking buffer',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      lift_code: '^.+$',
    },
    metric_type: 'num_lifts',
    node_name: 'multishuttle',
    label: 'Area',
    redis_operation: 'distinct_item_count',
    redis_params: {
      identifier: '{lift_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  // Total of all Movements
  {
    metric_config_name: 'multishuttle_total_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total movements within the DMS picking buffer in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      movement_type_code: '^.+$',
    },
    metric_type: 'total_movements',
    node_name: 'multishuttle',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },

  // All Movements - used to calculate movements-per-fault
  {
    metric_config_name: 'multishuttle_movements_per_fault_numerator',
    aggregation: 'ratio',
    config_type: 'node',
    description:
      'Total movements per fault within the DMS picking buffer in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      movement_type_code: '^(ByPass|Retrieval|Shuffle|Storage|IAT)$',
    },
    metric_type: 'movements_per_fault_numerator',
    node_name: 'multishuttle',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },

  // Retrieval Rate
  {
    metric_config_name: 'multishuttle_movement_retrieval_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of retrieval movements in the DMS picking buffer based on the last 15 minutes.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      movement_type_code: 'Retrieval',
    },
    metric_type: 'retrieval_rate',
    metric_units: '/hr',
    node_name: 'multishuttle',
    label: 'Area',
    redis_operation: 'event_set',
    redis_params: {
      ttl: 900,
    },
    time_window: '15m_set',
    views: ['facility'],
  },

  // Total Retrieval Movements
  {
    metric_config_name: 'multishuttle_movement_total_retrieval_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total retrieval movements within the DMS picking buffer in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      movement_type_code: 'Retrieval',
    },
    metric_type: 'total_retrieval_movements',
    node_name: 'multishuttle',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },

  // Storage Movement Rate
  {
    metric_config_name: 'multishuttle_storage_movement_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of storage movements in the DMS picking buffer based on the last 15 minutes.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      movement_type_code: 'Storage',
    },
    metric_type: 'storage_rate',
    metric_units: '/hr',
    node_name: 'multishuttle',
    label: 'Area',
    redis_operation: 'event_set',
    redis_params: {
      ttl: 900,
    },
    time_window: '15m_set',
    views: ['facility'],
  },

  // Storage Movements Total
  {
    metric_config_name: 'multishuttle_total_storage_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total storage movements within the DMS picking buffer in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      movement_type_code: 'Storage',
    },
    metric_type: 'total_storage_movements',
    node_name: 'multishuttle',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },

  // Shuffle Movements Total
  {
    metric_config_name: 'multishuttle_total_shuffle_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total shuffle movements within the DMS picking buffer in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      movement_type_code: 'Shuffle',
    },
    metric_type: 'total_shuffle_movements',
    node_name: 'multishuttle',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },

  // Shuffle Movement Rate
  {
    metric_config_name: 'multishuttle_shuffle_movement_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of shuffle movements in the DMS picking buffer based on the last 15 minutes.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      movement_type_code: 'Shuffle',
    },
    metric_type: 'shuffle_rate',
    metric_units: '/hr',
    node_name: 'multishuttle',
    label: 'Area',
    redis_operation: 'event_set',
    redis_params: {
      ttl: 900,
    },
    time_window: '15m_set',
    views: ['facility'],
  },

  // ByPass Movements
  {
    metric_config_name: 'multishuttle_total_bypass_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total bypass movements within the DMS picking buffer in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      movement_type_code: 'ByPass',
    },
    metric_type: 'total_bypass_movements',
    node_name: 'multishuttle',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },

  // ByPass Movement Rate
  {
    metric_config_name: 'multishuttle_bypass_movement_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of bypass movements in the DMS picking buffer based on the last 15 minutes.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      movement_type_code: 'ByPass',
    },
    metric_type: 'bypass_rate',
    metric_units: '/hr',
    node_name: 'multishuttle',
    label: 'Area',
    redis_operation: 'event_set',
    redis_params: {
      ttl: 900,
    },
    time_window: '15m_set',
    views: ['facility'],
  },

  // Intra Aisle Transfer Movements
  {
    metric_config_name: 'multishuttle_total_iat_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total intra aisle transfer movements within the DMS picking buffer in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      movement_type_code: 'IAT',
    },
    metric_type: 'total_iat_movements',
    node_name: 'multishuttle',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },

  // Intra Aisle Transfer Movement Rate
  {
    metric_config_name: 'multishuttle_iat_movement_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of intra-aisle transfer movements in the DMS picking buffer based on the last 15 minutes.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      movement_type_code: 'IAT',
    },
    metric_type: 'iat_rate',
    metric_units: '/hr',
    node_name: 'multishuttle',
    label: 'Area',
    redis_operation: 'event_set',
    redis_params: {
      ttl: 900,
    },
    time_window: '15m_set',
    views: ['facility'],
  },

  //= ========= Aisle Metrics =============================
  // Aisle - Distinct Lifts Count
  {
    metric_config_name: 'aisle_num_distinct_lifts',
    aggregation: 'count',
    config_type: 'node',
    description: 'Number of lifts within this DMS aisle',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      aisle_code: '^.+$',
      lift_code: '^.+$',
    },
    metric_type: 'num_lifts',
    node_name: 'MSAI{aisle_code}',
    redis_operation: 'distinct_item_count',
    redis_params: {
      identifier: '{lift_code}',
    },
    time_window: '60m_set',
    views: ['multishuttle'],
  },
  // Aisle - Distinct Shuttles Count
  {
    metric_config_name: 'aisle_num_distinct_shuttles',
    aggregation: 'count',
    config_type: 'node',
    description: 'Number of shuttles within this DMS aisle',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      aisle_code: '^.+$',
      shuttle_code: '^.+$',
    },
    metric_type: 'num_shuttles',
    node_name: 'MSAI{aisle_code}',
    redis_operation: 'distinct_item_count',
    redis_params: {
      identifier: '{shuttle_code}',
    },
    time_window: '60m_set',
    views: ['multishuttle'],
  },

  // Aisle - Total Movements
  {
    metric_config_name: 'aisle_total_movements',
    aggregation: 'count',
    config_type: 'node',
    description: 'Total movements within this DMS aisle in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      movement_type_code: '^Storage|Retrieval|Shuffle|ByPass|IAT$',
    },
    metric_type: 'total_movements',
    node_name: 'MSAI{aisle_code}',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['multishuttle'],
  },

  // Aisle - Total Storage Movements per aisle
  {
    metric_config_name: 'aisle_total_storage_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total storage movements within this DMS aisle in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      movement_type_code: 'Storage',
    },
    metric_type: 'total_storage_movements',
    node_name: 'MSAI{aisle_code}',
    parent_nodes: ['multishuttle'],
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['multishuttle'],
  },

  // Aisle - Storage Movement Rate per aisle
  {
    metric_config_name: 'aisle_storage_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of storage movements within this DMS aisle based on the last 15 minutes.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      movement_type_code: 'Storage',
    },
    metric_type: 'storage_rate',
    metric_units: '/hr',
    node_name: 'MSAI{aisle_code}',
    parent_nodes: ['multishuttle'],
    redis_operation: 'event_set',
    redis_params: {
      ttl: 900,
    },
    time_window: '15m_set',
    views: ['multishuttle'],
  },

  // Aisle - Total Retrieval Movements per aisle
  {
    metric_config_name: 'aisle_movement_total_retrieval_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total retrieval movements within this DMS aisle in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      movement_type_code: 'Retrieval',
    },
    metric_type: 'total_retrieval_movements',
    node_name: 'MSAI{aisle_code}',
    parent_nodes: ['multishuttle'],
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['multishuttle'],
  },

  // Aisle - Retrieval Rate per aisle
  {
    metric_config_name: 'aisle_retrieval_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of retrieval movements within this DMS aisle based on the last 15 minutes.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      movement_type_code: 'Retrieval',
    },
    metric_type: 'retrieval_rate',
    metric_units: '/hr',
    node_name: 'MSAI{aisle_code}',
    parent_nodes: ['multishuttle'],
    redis_operation: 'event_set',
    redis_params: {
      ttl: 900,
    },
    time_window: '15m_set',
    views: ['multishuttle'],
  },

  // Total Shuffle Movements per aisle
  {
    metric_config_name: 'aisle_total_shuffle_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total shuffle movements within this DMS aisle in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      movement_type_code: 'Shuffle',
    },
    metric_type: 'total_shuffle_movements',
    node_name: 'MSAI{aisle_code}',
    parent_nodes: ['multishuttle'],
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['multishuttle'],
  },

  // Aisle - Shuffle Rate per aisle
  {
    metric_config_name: 'aisle_shuffle_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of shuffle movements within this DMS aisle based on the last 15 minutes.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      movement_type_code: 'Shuffle',
    },
    metric_type: 'shuffle_rate',
    metric_units: '/hr',
    node_name: 'MSAI{aisle_code}',
    parent_nodes: ['multishuttle'],
    redis_operation: 'event_set',
    redis_params: {
      ttl: 900,
    },
    time_window: '15m_set',
    views: ['multishuttle'],
  },

  // Total ByPass Movements per aisle
  {
    metric_config_name: 'aisle_total_bypass_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total bypass movements within this DMS aisle in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      movement_type_code: 'ByPass',
    },
    metric_type: 'total_bypass_movements',
    node_name: 'MSAI{aisle_code}',
    parent_nodes: ['multishuttle'],
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['multishuttle'],
  },

  // Aisle - Bypass Rate per aisle
  {
    metric_config_name: 'aisle_bypass_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of bypass movements within this DMS aisle based on the last 15 minutes.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      movement_type_code: 'ByPass',
    },
    metric_type: 'bypass_rate',
    metric_units: '/hr',
    node_name: 'MSAI{aisle_code}',
    parent_nodes: ['multishuttle'],
    redis_operation: 'event_set',
    redis_params: {
      ttl: 900,
    },
    time_window: '15m_set',
    views: ['multishuttle'],
  },

  // Total Intra Aisle Transfer Movements per aisle
  {
    metric_config_name: 'aisle_total_iat_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total intra aisle transfer movements within this DMS aisle in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      movement_type_code: 'IAT',
    },
    metric_type: 'total_iat_movements',
    node_name: 'MSAI{aisle_code}',
    parent_nodes: ['multishuttle'],
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['multishuttle'],
  },

  {
    metric_config_name: 'aisle_iat_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of intra-aisle transfer movements within this DMS aisle based on the last 15 minutes.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      movement_type_code: 'IAT',
    },
    metric_type: 'iat_rate',
    metric_units: '/hr',
    node_name: 'MSAI{aisle_code}',
    parent_nodes: ['multishuttle'],
    redis_operation: 'event_set',
    redis_params: {
      ttl: 900,
    },
    time_window: '15m_set',
    views: ['multishuttle'],
  },

  // Aisle - Movements - used to calculate lift-movements-per-fault
  {
    metric_config_name: 'aisle_movements_per_fault_numerator',
    aggregation: 'ratio',
    config_type: 'node',
    description:
      'Total lift movements per fault within this DMS aisle over the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      movement_type_code: '^Storage|Retrieval|Shuffle|ByPass|IAT$',
    },
    metric_type: 'movements_per_fault_numerator',
    node_name: 'MSAI{aisle_code}',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['multishuttle'],
  },

  //= ========== Shuttle Metrics ======================
  // **Total Movements**
  {
    metric_config_name: 'shuttle_total_shuttle_movements',
    aggregation: 'count',
    config_type: 'node',
    description: 'Total movements made by this shuttle in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'shuttle_node',
    label: 'Shuttle',
    match_conditions: {
      aisle_code: '^.+$',
      shuttle_code: '^.+$',
    },
    metric_type: 'total_shuttle_movements',
    node_name: '{shuttle_code}',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['MSAI{aisle_code}'],
  },

  // **Storage Movements**
  {
    metric_config_name: 'shuttle_total_storage_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total storage movements made by this shuttle in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'shuttle_node',
    label: 'Shuttle',
    match_conditions: {
      aisle_code: '^.+$',
      movement_type_code: '^Storage$',
      shuttle_code: '^.+$',
    },
    metric_type: 'total_shuttle_storage_movements',
    node_name: '{shuttle_code}',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['MSAI{aisle_code}'],
  },

  {
    metric_config_name: 'shuttle_inventory_totes_storage_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total inventory tote storage movements made by this shuttle in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'shuttle_node',
    label: 'Shuttle',
    match_conditions: {
      aisle_code: '^.+$',
      movement_type_code: '^Storage$',
      shuttle_code: '^.+$',
      sku_code: '^.+$', // Ensures SKU exists
    },
    metric_type: 'inventory_totes_shuttle_storage_movements',
    node_name: '{shuttle_code}',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['MSAI{aisle_code}'],
  },

  {
    metric_config_name: 'shuttle_empty_totes_storage_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total empty tote storage movements made by this shuttle in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'shuttle_node',
    label: 'Shuttle',
    match_conditions: {
      aisle_code: '^.+$',
      movement_type_code: '^Storage$',
      shuttle_code: '^.+$',
      sku_code: '^(null|)$', // Matches missing SKU (null or empty)
    },
    metric_type: 'empty_totes_shuttle_storage_movements',
    node_name: '{shuttle_code}',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['MSAI{aisle_code}'],
  },

  // **Retrieval Movements**
  {
    metric_config_name: 'shuttle_total_retrieval_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total retrieval movements made by this shuttle in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'shuttle_node',
    label: 'Shuttle',
    match_conditions: {
      aisle_code: '^.+$',
      movement_type_code: '^Retrieval$',
      shuttle_code: '^.+$',
    },
    metric_type: 'total_shuttle_retrieval_movements',
    node_name: '{shuttle_code}',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['MSAI{aisle_code}'],
  },

  {
    metric_config_name: 'shuttle_inventory_totes_retrieval_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total inventory tote retrieval movements made by this shuttle in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'shuttle_node',
    label: 'Shuttle',
    match_conditions: {
      aisle_code: '^.+$',
      movement_type_code: '^Retrieval$',
      shuttle_code: '^.+$',
      sku_code: '^.+$', // SKU must be present
    },
    metric_type: 'inventory_totes_shuttle_retrieval_movements',
    node_name: '{shuttle_code}',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['MSAI{aisle_code}'],
  },

  {
    metric_config_name: 'shuttle_empty_totes_retrieval_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total empty tote retrieval movements made by this shuttle in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'shuttle_node',
    label: 'Shuttle',
    match_conditions: {
      aisle_code: '^.+$',
      movement_type_code: '^Retrieval$',
      shuttle_code: '^.+$',
      sku_code: '^(null|)$', // Empty tote condition
    },
    metric_type: 'empty_totes_shuttle_retrieval_movements',
    node_name: '{shuttle_code}',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['MSAI{aisle_code}'],
  },

  // **Shuffle Movements**
  {
    metric_config_name: 'shuttle_total_shuffle_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total shuffle movements made by this shuttle in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'shuttle_node',
    label: 'Shuttle',
    match_conditions: {
      aisle_code: '^.+$',
      movement_type_code: '^Shuffle$',
      shuttle_code: '^.+$',
    },
    metric_type: 'total_shuttle_shuffle_movements',
    node_name: '{shuttle_code}',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['MSAI{aisle_code}'],
  },

  {
    metric_config_name: 'shuttle_inventory_totes_shuffle_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total inventory tote shuffle movements made by this shuttle in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'shuttle_node',
    label: 'Shuttle',
    match_conditions: {
      aisle_code: '^.+$',
      movement_type_code: '^Shuffle$',
      shuttle_code: '^.+$',
      sku_code: '^.+$', // SKU must be present
    },
    metric_type: 'inventory_totes_shuttle_shuffle_movements',
    node_name: '{shuttle_code}',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['MSAI{aisle_code}'],
  },

  {
    metric_config_name: 'shuttle_empty_totes_shuffle_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total empty tote shuffle movements made by this shuttle in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'shuttle_node',
    label: 'Shuttle',
    match_conditions: {
      aisle_code: '^.+$',
      movement_type_code: '^Shuffle$',
      shuttle_code: '^.+$',
      sku_code: '^(null|)$', // Empty tote condition
    },
    metric_type: 'empty_totes_shuttle_shuffle_movements',
    node_name: '{shuttle_code}',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['MSAI{aisle_code}'],
  },

  // =========== Lift Metrics ======================
  // Lift edges to Aisles inside Aisle View
  {
    metric_config_name: 'edge_from_lift_to_aisle',
    config_type: 'complete-edge',
    description:
      'Hourly rate of storage movements from lift to aisle based on the last 15 minutes.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_edge',
    inbound_area: 'MSAI{aisle_code}',
    label: 'Aisle',
    match_conditions: {
      aisle_code: '^.+$',
      handling_unit_code: '^.+$',
      lift_code: '^MSAI.*ER.*LO01$',
      movement_type_code: '^Storage$',
    },
    outbound_area: '{lift_code}',
    outbound_node_label: 'Lift',
    redis_operation: 'event_set',
    views: ['MSAI{aisle_code}'],
  },

  // Lift edges from Aisles inside Aisle View
  {
    metric_config_name: 'edge_from_aisle_to_lift',
    config_type: 'complete-edge',
    description:
      'Hourly rate of retrieval movements from aisle to lift based on the last 15 minutes.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_edge',
    inbound_area: '{lift_code}',
    label: 'Lift',
    match_conditions: {
      aisle_code: '^.+$',
      handling_unit_code: '^.+$',
      lift_code: '^MSAI.*EL.*LO01$',
      movement_type_code: '^Retrieval$',
    },
    outbound_area: 'MSAI{aisle_code}',
    outbound_node_label: 'Aisle',
    redis_operation: 'event_set',
    views: ['MSAI{aisle_code}'],
  },

  // Lift Total Movements
  {
    metric_config_name: 'lift_total_movements',
    aggregation: 'count',
    config_type: 'node',
    description: 'Total movements made by this lift in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    label: 'Lift',
    match_conditions: {
      aisle_code: '^.+$',
      lift_code: '^.+$',
    },
    metric_type: 'total_movements',
    node_name: '{lift_code}',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['MSAI{aisle_code}'],
  },

  // Lift Storage Movements
  {
    metric_config_name: 'lift_total_storage_movements',
    aggregation: 'count',
    config_type: 'node',
    description: 'Total storage movements made by this lift in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    label: 'Lift',
    match_conditions: {
      aisle_code: '^.+$',
      lift_code: '^.+$',
      movement_type_code: '^Storage$',
    },
    metric_type: 'total_storage_movements',
    node_name: '{lift_code}',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['MSAI{aisle_code}'],
  },

  // Lift Retrieval Movements
  {
    metric_config_name: 'lift_total_retrieval_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total retrieval movements made by this lift in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    label: 'Lift',
    match_conditions: {
      aisle_code: '^.+$',
      lift_code: '^.+$',
      movement_type_code: '^Retrieval$',
    },
    metric_type: 'total_retrieval_movements',
    node_name: '{lift_code}',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['MSAI{aisle_code}'],
  },
  {
    metric_config_name: 'lift_retrieval_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of retrieval movements made by this lift based on the last 15 minutes.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    label: 'Lift',
    match_conditions: {
      aisle_code: '^.+$',
      lift_code: '^.+$',
      movement_type_code: '^Retrieval$',
    },
    metric_type: 'retrieval_rate',
    metric_units: '/hr',
    node_name: '{lift_code}',
    redis_operation: 'event_set',
    redis_params: {
      ttl: 900,
    },
    time_window: '15m_set',
    views: ['MSAI{aisle_code}'],
  },

  // Lift Shuffle Movements
  {
    metric_config_name: 'lift_total_shuffle_movements',
    aggregation: 'count',
    config_type: 'node',
    description: 'Total shuffle movements made by this lift in the last hour.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    label: 'Lift',
    match_conditions: {
      aisle_code: '^.+$',
      lift_code: '^.+$',
      movement_type_code: '^Shuffle$',
    },
    metric_type: 'total_shuffle_movements',
    node_name: '{lift_code}',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['MSAI{aisle_code}'],
  },

  // Lift Storage Rate
  {
    metric_config_name: 'lift_storage_movement_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of storage movements made by this lift based on the last 15 minutes.',
    fact_type: 'multishuttle_movement',
    graph_operation: 'area_node',
    label: 'Lift',
    match_conditions: {
      aisle_code: '^.+$',
      lift_code: '^.+$',
      movement_type_code: '^Storage$',
    },
    metric_type: 'storage_rate',
    metric_units: '/hr',
    node_name: '{lift_code}',
    redis_operation: 'event_set',
    redis_params: {
      ttl: 900,
    },
    time_window: '15m_set',
    views: ['MSAI{aisle_code}'],
  },
];

export default multishuttleMovementMetricConfigs;
