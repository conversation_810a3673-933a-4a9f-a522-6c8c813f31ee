import {MetricConfig} from '../types/metric-configuration';

/**
 * Metrics configuration for visualization event logs
 */
const vizEventLogMetricConfigs: MetricConfig[] = [
  // Miniload - Faults - used to calculate miniload movements_per_fault
  {
    metric_config_name: 'miniload_movements_per_fault_denominator',
    aggregation: 'ratio',
    config_type: 'node',
    description:
      'Total movements per fault across all aisles within the Mini-Load buffer in the last hour.',
    fact_type: 'viz_event_log',
    graph_operation: 'area_node',
    match_conditions: {
      equipment_code: '^ML.+$',
      or_condition: [{area: '.*ML.*'}, {area: '.*Miniload.*'}],
    },
    metric_type: 'movements_per_fault_denominator',
    node_name: 'miniload',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },
];

export default vizEventLogMetricConfigs;
