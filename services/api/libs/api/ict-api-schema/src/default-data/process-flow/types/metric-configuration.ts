import {
  CompleteEdgeMetricConfig,
  InboundEdgeMetricConfig,
  OutboundEdgeMetricConfig,
} from './edge-metric-configuration';
import {NodeMetricConfig} from './node-metric-configuration';

export type ConfigType =
  | 'node'
  | 'inbound-edge'
  | 'outbound-edge'
  | 'complete-edge';

export type GraphOperation = 'area_node' | 'area_edge' | 'shuttle_node';

export type NodeLabel =
  | 'Aisle'
  | 'Area'
  | 'Lift'
  | 'Station'
  | 'StationLocation'
  | 'Level'
  | 'Metric'
  | 'Shuttle';

export type MatchConditions = Record<
  string,
  string | Array<Record<string, string>>
>;

export type RedisParams = Record<string, string | number>;

export type TimeWindow = '15m_set' | '30m_set' | '60m_set' | 'value';

export type Aggregation =
  | 'avg'
  | 'count'
  | 'destination_position_ratio'
  | 'hourly_rate'
  | 'ratio'
  | 'static'
  | 'sum'
  | 'sum_item_values';

export type RedisOperation =
  | 'boolean_toggle'
  | 'complex_event_set'
  | 'cycle_time_start'
  | 'cycle_time_stop'
  | 'distinct_item_count'
  | 'distinct_item_subtract'
  | 'event_set'
  | 'storage_location_distribution_available'
  | 'storage_location_distribution_occupied'
  | 'storage_utilization'
  | 'store_static_value'
  | 'total_storage_locations_occupied';

/**
 * Defines the structure for dynamically generating metric names based on event data for inbound or outbound edges
 */
export interface NameConfig {
  /**
   * A format string representing the source field from the event data.
   * Example: `"{source_location_code}"` (must match a key in event data).
   */
  source: string;

  /**
   * A regular expression pattern for extracting dynamic values from `source`.
   * Example: `"MSAI(?P<aisle>\\d{2}).*"` extracts an `aisle` number from a multishuttle location.
   */
  pattern: string;

  /**
   * A format string that defines how to generate the final metric name using extracted values.
   * Example: `"MSAI{aisle}"` produces `"MSAI01"`, `"MSAI02"`, etc.
   */
  template: string;
}

/**
 * Base interface for all metric configurations
 */
export interface BaseMetricConfig {
  /**
   * Identifier for the metric config
   * Example: multishuttle_aisle_storage_location_distribution_available
   */
  metric_config_name: string;

  /**
   * The hierarchy graph view at which this metric applies
   * Each view pertains to a unique graph node.
   * Example: ["facility", "multishuttle"]
   */
  views: string[];

  /**
   * Conditions an event must meet for this metric to be processed
   */
  match_conditions: MatchConditions;

  /**
   * The type of metric being processed (node or edge)
   */
  config_type: ConfigType;

  /**
   * The type of graph database operation
   */
  graph_operation?: GraphOperation;

  /**
   * Optional parameters required for metric processing
   * Example: {"row_hash": "{row_hash}"}
   */
  redis_params?: RedisParams;

  /**
   * The label for the node. Defaults to "Area"
   */
  label: NodeLabel;

  /**
   * Defines the parent node hierarchy for this metric
   */
  parent_nodes?: string[];

  /**
   * Defines how to dynamically generate metric names based on source fields.
   */
  name_formula?: NameConfig;

  /**
   * The type of fact this metric represents
   */
  fact_type?: string;

  /**
   * Description of the metric
   */
  description?: string;
}

/**
 * Union type for all metric configurations
 */
export type MetricConfig =
  | NodeMetricConfig
  | InboundEdgeMetricConfig
  | OutboundEdgeMetricConfig
  | CompleteEdgeMetricConfig;
