import type {
  Aggregation,
  BaseMetricConfig,
  GraphOperation,
  NameConfig,
  RedisOperation,
  TimeWindow,
} from './metric-configuration';

// If the node_name prop is present, the name_formula prop should be omitted
type MetricConfigWithNodeName = BaseMetricConfig & {
  node_name: string;
  name_formula?: never;
};

// If the node_name prop is omitted, the name_formula prop should be present
type MetricConfigWithNameFormula = BaseMetricConfig & {
  node_name?: never;
  name_formula: NameConfig;
};

// This creates a type in which the config should have either a node_name or a name formula, but not both
type MetricConfigWithNodeNameOrNameFormula =
  | MetricConfigWithNodeName
  | MetricConfigWithNameFormula;

/**
 * Node Metric Configuration
 */
export type NodeMetricConfig = MetricConfigWithNodeNameOrNameFormula & {
  config_type: 'node';

  /**
   * The type of operation performed on the graph database
   * Example: "area_node"
   */
  graph_operation: GraphOperation;

  /**
   * The type of metric being measured
   * Example: "stock_time"
   */
  metric_type: string;

  /**
   * The time window for the metric
   */
  time_window: TimeWindow;

  /**
   * The aggregation method for the metric
   */
  aggregation: Aggregation;

  /**
   * The Redis method to be performed
   * Example: "event_set"
   */
  redis_operation: RedisOperation;

  /**
   * Optional units for the metric
   * Example: "/hr"
   */
  metric_units?: string;
};
