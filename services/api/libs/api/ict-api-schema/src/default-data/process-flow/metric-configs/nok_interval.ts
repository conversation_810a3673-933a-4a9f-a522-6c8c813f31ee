import {MetricConfig} from '../types/metric-configuration';

/**
 * Metrics configuration for NOK intervals
 */
const nokIntervalMetricConfigs: MetricConfig[] = [
  // Workstations - Faults - used to calculate arrivals_per_fault
  {
    metric_config_name: 'workstations_arrivals_per_fault_denominator',
    aggregation: 'ratio',
    config_type: 'node',
    description:
      'Total arrivals per fault across all stations in the last hour.',
    fact_type: 'nok_interval',
    graph_operation: 'area_node',
    match_conditions: {
      equipment_code: '.*GTP-\\d{2}.*',
    },
    metric_type: 'arrivals_per_fault_denominator',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },
  // Workstation - Faults - used to calculate arrivals_per_fault
  {
    metric_config_name: 'workstation_arrivals_per_fault_denominator',
    aggregation: 'ratio',
    config_type: 'node',
    description: 'Total arrivals per fault in this station in the last hour.',
    fact_type: 'nok_interval',
    graph_operation: 'area_node',
    label: 'Station',
    match_conditions: {
      equipment_code: '.*GTP-\\d{2}.*',
    },
    metric_type: 'arrivals_per_fault_denominator',
    name_formula: {
      source: '{equipment_code}',
      pattern: '.*(?P<gtp_part>GTP-\\d{2}).*',
      template: 'M11-{gtp_part}',
    },
    parent_nodes: ['workstations'],
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['workstations'],
  },
  //= ===================================================================
  // Multishuttle - Faults - used to calculate movements_per_fault
  {
    metric_config_name: 'multishuttle_movements_per_fault_denominator',
    aggregation: 'ratio',
    config_type: 'node',
    description:
      'Total movements per fault in the DMS picking buffer in the last hour.',
    fact_type: 'nok_interval',
    graph_operation: 'area_node',
    match_conditions: {
      equipment_code: '^MSAI.*$',
    },
    metric_type: 'movements_per_fault_denominator',
    node_name: 'multishuttle',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },
  // Aisle - Lift Faults - used to calculate lift-movements-per-fault
  {
    metric_config_name: 'aisle_movements_per_fault_denominator',
    aggregation: 'ratio',
    config_type: 'node',
    description:
      'Total movements per fault in this DMS aisle in the last hour.',
    fact_type: 'nok_interval',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      equipment_code: '^MSAI.*(?:E[RL].*LO01|LV.*SH01)$',
    },
    metric_type: 'movements_per_fault_denominator',
    name_formula: {
      source: '{equipment_code}',
      pattern: '(?P<aisle_code>MSAI\\d{2}).*',
      template: '{aisle_code}',
    },
    parent_nodes: ['multishuttle'],
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['multishuttle'],
  },
];

export default nokIntervalMetricConfigs;
