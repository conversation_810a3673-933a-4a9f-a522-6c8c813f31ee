import {MetricConfig} from '../types/metric-configuration';

/**
 * Metrics configuration for pick operations
 */
const pickMetricConfigs: MetricConfig[] = [
  // Workstations - Cycle Count
  {
    metric_config_name: 'workstations_cycle_count',
    aggregation: 'count',
    config_type: 'node',
    description: 'Total cycle counts across all stations in the last hour.',
    fact_type: 'pick',
    graph_operation: 'area_node',
    match_conditions: {
      induction_zone_code: '^M.*GTP.*(B[1-5]|F[1-6])$',
      workflow_code: 'CycleCount',
    },
    metric_type: 'cycle_count',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },

  // Workstations - Cycle Count Rate
  {
    metric_config_name: 'workstations_cycle_count_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of cycle counts across all stations based on the last 15 minutes.',
    fact_type: 'pick',
    graph_operation: 'area_node',
    match_conditions: {
      induction_zone_code: '^M.*GTP.*(B[1-5]|F[1-6])$',
      workflow_code: 'CycleCount',
    },
    metric_type: 'cycle_count_rate',
    metric_units: '/hr',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '15m_set',
    views: ['facility'],
  },

  // Workstations - Orders Picked Rate
  {
    metric_config_name: 'workstations_orders_picked_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description: 'Orders picked hourly rate based on the last 15 minutes.',
    fact_type: 'pick',
    graph_operation: 'area_node',
    match_conditions: {
      induction_zone_code: '^M.*GTP.*(B[1-5]|F[1-6])$',
      workflow_code: 'GTPPrint',
    },
    metric_type: 'orders_picked_rate',
    metric_units: '/hr',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '15m_set',
    views: ['facility'],
  },
  // Workstations - Orders Picked Count
  {
    metric_config_name: 'workstations_orders_picked_count',
    aggregation: 'count',
    description: 'Total orders picked in the last hour.',
    config_type: 'node',
    fact_type: 'pick',
    graph_operation: 'area_node',
    match_conditions: {
      induction_zone_code: '^M.*GTP.*(B[1-5]|F[1-6])$',
      workflow_code: 'GTPPrint',
    },
    metric_type: 'orders_picked_count',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },

  // Workstations - Order Lines Picked Rate
  {
    metric_config_name: 'workstations_order_lines_picked_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of order lines picked across all stations based on the last 15 minutes.',
    fact_type: 'pick',
    graph_operation: 'area_node',
    match_conditions: {
      induction_zone_code: '^M.*GTP.*(B[1-5]|F[1-6])$',
      workflow_code: 'PICK',
    },
    metric_type: 'order_lines_picked_rate',
    metric_units: '/hr',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '15m_set',
    views: ['facility'],
  },

  // Workstations - Order Lines Picked Count
  {
    metric_config_name: 'workstations_order_lines_picked_count',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total order lines picked across all stations in the last hour.',
    graph_operation: 'area_node',
    fact_type: 'pick',
    match_conditions: {
      induction_zone_code: '^M.*GTP.*(B[1-5]|F[1-6])$',
      workflow_code: 'PICK',
    },
    metric_type: 'order_lines_picked_count',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },

  // Workstations - Order Quantity Picked
  {
    metric_config_name: 'workstations_order_quantity_picked',
    aggregation: 'sum',
    config_type: 'node',
    description:
      'Total order quantity picked across all stations in the last hour.',
    fact_type: 'pick',
    graph_operation: 'area_node',
    match_conditions: {
      induction_zone_code: '^M.*GTP.*(B[1-5]|F[1-6])$',
      workflow_code: 'PICK',
    },
    metric_type: 'order_quantity_picked',
    node_name: 'workstations',
    label: 'Area',
    redis_operation: 'event_set',
    redis_params: {
      value: '{picked_qty}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  // Work Station Metrics
  // Workstation - Order Lines Picked Rate
  {
    metric_config_name: 'workstation_order_lines_picked_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of order lines picked in this station based on the last 15 minutes.',
    fact_type: 'pick',
    graph_operation: 'area_node',
    label: 'Station',
    match_conditions: {
      induction_zone_code: '^M.*GTP.*(B[1-5]|F[1-6])$',
      workflow_code: 'PICK',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'order_lines_picked_rate',
    metric_units: '/hr',
    node_name: '{workstation_code}',
    parent_nodes: ['workstations'],
    redis_operation: 'event_set',
    time_window: '15m_set',
    views: ['workstations'],
  },

  {
    metric_config_name: 'workstation_order_lines_picked_count',
    aggregation: 'count',
    config_type: 'node',
    description: 'Total order lines picked in this station in the last hour.',
    fact_type: 'pick',
    graph_operation: 'area_node',
    label: 'Station',
    match_conditions: {
      induction_zone_code: '^M.*GTP.*(B[1-5]|F[1-6])$',
      workflow_code: 'PICK',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'order_lines_picked',
    node_name: '{workstation_code}',
    parent_nodes: ['workstations'],
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['workstations'],
  },

  {
    metric_config_name: 'workstation_cycle_counts',
    aggregation: 'count',
    config_type: 'node',
    description: 'Total cycle counts in this station in the last hour.',
    fact_type: 'pick',
    graph_operation: 'area_node',
    label: 'Station',
    match_conditions: {
      induction_zone_code: '^M.*GTP.*(B[1-5]|F[1-6])$',
      workflow_code: 'CycleCount',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'cycle_counts',
    node_name: '{workstation_code}',
    parent_nodes: ['workstations'],
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['workstations'],
  },

  // Workstation - Cycle Count Rate
  {
    metric_config_name: 'workstation_cycle_count_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of cycle counts in this station based on the last 15 minutes.',
    fact_type: 'pick',
    graph_operation: 'area_node',
    label: 'Station',
    match_conditions: {
      induction_zone_code: '^M.*GTP.*(B[1-5]|F[1-6])$',
      workflow_code: 'CycleCount',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'cycle_count_rate',
    metric_units: '/hr',
    node_name: '{workstation_code}',
    redis_operation: 'event_set',
    time_window: '15m_set',
    views: ['workstations'],
  },

  // Workstation - Order Lines Picked By Destination
  {
    metric_config_name: 'workstation_order_lines_picked_by_destination',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total order lines picked across all destination locations in this station in the last hour.',
    fact_type: 'pick',
    graph_operation: 'area_node',
    label: 'Station',
    match_conditions: {
      induction_zone_code: '^M.*GTP.*(B[1-5]|F[1-6])$',
      workflow_code: 'PICK',
      workstation_code: '^M.*GTP-\\d{2}$',
    },
    metric_type: 'order_lines_picked_{induction_zone_code}',
    node_name: '{workstation_code}',
    parent_nodes: ['workstations'],
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['workstations'],
  },
];

export default pickMetricConfigs;
