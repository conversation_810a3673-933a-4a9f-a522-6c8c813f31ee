import {MetricConfig} from '../types/metric-configuration';

/**
 * Metrics configuration for vehicle movement
 */
const vehicleMovementMetricConfigs: MetricConfig[] = [
  // Miniload Components
  {
    metric_config_name: 'miniload_num_miniloads_available',
    aggregation: 'count',
    config_type: 'node',
    description: 'Number of available miniloads',
    fact_type: 'vehicle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      equipment_code: '^ML.*',
    },
    metric_type: 'miniloads_available',
    node_name: 'miniload',
    label: 'Area',
    redis_operation: 'distinct_item_count',
    redis_params: {
      identifier: '{equipment_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  // Miniload Movements
  // mini load total OK aisle movements
  {
    metric_config_name: 'miniload_aisle_total_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total movements across all aisles within the Mini-Load buffer in the last hour.',
    fact_type: 'vehicle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      or_condition: [
        {source_location_code: '^ML.*'},
        {destination_location_code: '^ML.*'},
      ],
      movement_status_code: '^OK$',
    },
    metric_type: 'aisle_total_movements',
    node_name: 'miniload',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },

  // mini load total OK storage movements
  {
    metric_config_name: 'miniload_aisle_storage_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total storage movements across all aisles within the Mini-Load buffer in the last hour.',
    fact_type: 'vehicle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      or_condition: [
        {source_location_code: '^ML.*'},
        {destination_location_code: '^ML.*'},
      ],
      movement_status_code: '^OK$',
      movement_type_code: '^Storage$',
    },
    metric_type: 'aisle_storage_movements',
    node_name: 'miniload',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },

  // mini load total OK retrieval movements
  {
    metric_config_name: 'miniload_aisle_retrieval_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total retrieval movements across all aisles within the Mini-Load buffer in the last hour.',
    fact_type: 'vehicle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      or_condition: [
        {source_location_code: '^ML.*'},
        {destination_location_code: '^ML.*'},
      ],
      movement_status_code: '^OK$',
      movement_type_code: '^Retrieval$',
    },
    metric_type: 'aisle_retrieval_movements',
    node_name: 'miniload',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },

  // mini load total OK bypass movements
  {
    metric_config_name: 'miniload_aisle_bypass_movements',
    aggregation: 'count',
    config_type: 'node',
    description:
      'Total bypass movements across all aisles within the Mini-Load buffer in the last hour.',
    fact_type: 'vehicle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      or_condition: [
        {source_location_code: '^ML.*'},
        {destination_location_code: '^ML.*'},
      ],
      movement_status_code: '^OK$',
      movement_type_code: '^Bypass$',
    },
    metric_type: 'aisle_bypass_movements',
    node_name: 'miniload',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },

  // Miniload Rates
  {
    metric_config_name: 'miniload_storage_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of storage movements across all aisles in the Mini-Load buffer based on the last 15 minutes.',
    fact_type: 'vehicle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      or_condition: [
        {source_location_code: '^ML.*'},
        {destination_location_code: '^ML.*'},
      ],
      movement_status_code: '^OK$',
      movement_type_code: '^Storage$',
    },
    metric_type: 'storage_rate',
    metric_units: '/hr',
    node_name: 'miniload',
    label: 'Area',
    redis_operation: 'event_set',
    redis_params: {
      ttl: 900,
    },
    time_window: '15m_set',
    views: ['facility'],
  },

  {
    metric_config_name: 'miniload_retrieval_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of retrieval movements across all aisles in the Mini-Load buffer based on the last 15 minutes.',
    fact_type: 'vehicle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      or_condition: [
        {source_location_code: '^ML.*'},
        {destination_location_code: '^ML.*'},
      ],
      movement_status_code: '^OK$',
      movement_type_code: '^Retrieval$',
    },
    metric_type: 'retrieval_rate',
    metric_units: '/hr',
    node_name: 'miniload',
    label: 'Area',
    redis_operation: 'event_set',
    redis_params: {
      ttl: 900,
    },
    time_window: '15m_set',
    views: ['facility'],
  },

  {
    metric_config_name: 'miniload_bypass_rate',
    aggregation: 'hourly_rate',
    config_type: 'node',
    description:
      'Hourly rate of bypass movements across all aisles in the Mini-Load buffer based on the last 15 minutes.',
    fact_type: 'vehicle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      or_condition: [
        {source_location_code: '^ML.*'},
        {destination_location_code: '^ML.*'},
      ],
      movement_status_code: '^OK$',
      movement_type_code: '^Bypass$',
    },
    metric_type: 'bypass_rate',
    metric_units: '/hr',
    node_name: 'miniload',
    label: 'Area',
    redis_operation: 'event_set',
    redis_params: {
      ttl: 900,
    },
    time_window: '15m_set',
    views: ['facility'],
  },

  {
    metric_config_name: 'miniload_movements_per_fault_numerator',
    aggregation: 'ratio',
    config_type: 'node',
    description:
      'Total movements per fault across all aisles within the Mini-Load buffer in the last hour.',
    fact_type: 'vehicle_movement',
    graph_operation: 'area_node',
    match_conditions: {
      or_condition: [
        {source_location_code: '^ML.*'},
        {destination_location_code: '^ML.*'},
      ],
      movement_status_code: '^OK$',
    },
    metric_type: 'movements_per_fault_numerator',
    node_name: 'miniload',
    label: 'Area',
    redis_operation: 'event_set',
    time_window: '60m_set',
    views: ['facility'],
  },
];

export default vehicleMovementMetricConfigs;
