import {
  BaseMetricConfig,
  GraphOperation,
  RedisOperation,
} from './metric-configuration';

/**
 * Inbound Edge Metric Configuration
 * Tracks movement INTO an area
 */
export type InboundEdgeMetricConfig = BaseMetricConfig & {
  config_type: 'inbound-edge';

  /**
   * The key in event data that identifies the handling unit
   * Example: "handling_unit_code"
   */
  hu_id: string;

  /**
   * The name of the area where the handling unit is arriving
   * Example: "multishuttle"
   */
  inbound_area: string;

  /**
   * The Redis operation performed upon arrival
   * Example: "event_set"
   */
  redis_operation: RedisOperation;

  /**
   * The type of operation performed on the graph database
   * Example: "area_node"
   */
  graph_operation: GraphOperation;

  /**
   * Optional units for the metric
   * Default: "units/hr"
   */
  metric_units?: string;

  /**
   * The parent nodes for the inbound area
   */
  inbound_parent_nodes?: string[];
};

/**
 * Outbound Edge Metric Configuration
 * Tracks movement OUT OF an area
 */
export type OutboundEdgeMetricConfig = BaseMetricConfig & {
  config_type: 'outbound-edge';

  /**
   * The name of the area the handling unit is leaving
   * Example: "multishuttle"
   */
  outbound_area: string;

  /**
   * The key in event data that identifies the handling unit
   * Example: "handling_unit_code"
   */
  hu_id: string;

  /**
   * The type of units being counted
   * Example: "handling_unit"
   */
  units: string;

  /**
   * The parent nodes for the outbound area
   */
  outbound_parent_nodes?: string[];
};

/**
 * Complete Edge Metric Configuration
 * Tracks movement BETWEEN two areas
 */
export interface CompleteEdgeMetricConfig extends BaseMetricConfig {
  config_type: 'complete-edge';

  /**
   * The name of the area where the handling unit is arriving
   * Example: "multishuttle"
   */
  inbound_area: string;

  /**
   * The name of the area the handling unit is leaving
   * Example: "multishuttle"
   */
  outbound_area: string;

  /**
   * The Redis operation performed
   * Example: "event_set"
   */
  redis_operation: RedisOperation;

  /**
   * The type of operation performed on the graph database
   * Example: "area_node"
   */
  graph_operation: GraphOperation;

  /**
   * The label for the outbound node
   * Default: "Area"
   */
  outbound_node_label?: string;

  /**
   * The parent nodes for the inbound area
   */
  inbound_parent_nodes?: string[];

  /**
   * The parent nodes for the outbound area
   */
  outbound_parent_nodes?: string[];

  /**
   * Optional units for the metric
   * Default: "units/hr"
   */
  metric_units?: string;
}
