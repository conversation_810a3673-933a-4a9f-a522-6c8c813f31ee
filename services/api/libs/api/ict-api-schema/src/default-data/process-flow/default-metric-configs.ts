import binUtilizationMetricConfigs from './metric-configs/bin_utilization';
import connectionMovementMetricConfigs from './metric-configs/connection_movement';
import faultEventMetricConfigs from './metric-configs/fault_event';
import multishuttleMovementMetricConfigs from './metric-configs/multishuttle_movement';
import nokIntervalMetricConfigs from './metric-configs/nok_interval';
import pickActivityMetricConfigs from './metric-configs/pick_activity';
import pickMetricConfigs from './metric-configs/pick';
import vehicleMovementMetricConfigs from './metric-configs/vehicle_movement';
import vizEventLogMetricConfigs from './metric-configs/viz_event_log';
import {MetricConfig} from './types/metric-configuration';

export const defaultMetricConfigs: MetricConfig[] = [
  ...binUtilizationMetricConfigs,
  ...connectionMovementMetricConfigs,
  ...faultEventMetricConfigs,
  ...multishuttleMovementMetricConfigs,
  ...nokIntervalMetricConfigs,
  ...pickMetricConfigs,
  ...pickActivityMetricConfigs,
  ...vehicleMovementMetricConfigs,
  ...vizEventLogMetricConfigs,
];
