import {MetricConfig} from '../types/metric-configuration';

/**
 * Metrics configuration for bin utilization
 */
const binUtilizationMetricConfigs: MetricConfig[] = [
  // Multishuttle Storage Location Distribution
  {
    metric_config_name: 'multishuttle_total_locations_available',
    aggregation: 'sum_item_values',
    config_type: 'node',
    description: 'Number of available locations within the DMS Picking Buffer',
    fact_type: 'bin_utilization',
    graph_operation: 'area_node',
    match_conditions: {
      aisle_code: '^.+$',
      area_code: '^MS - Storage$',
      empty_location_position_count: '^.+$',
    },
    metric_type: 'total_locations_available',
    node_name: 'multishuttle',
    label: 'Area',
    redis_operation: 'complex_event_set',
    redis_params: {
      identifier: '{aisle_code}',
      value: '{empty_location_position_count}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  {
    metric_config_name: 'multishuttle_total_locations_occupied',
    aggregation: 'sum_item_values',
    config_type: 'node',
    description: 'Number of occupied locations within the DMS Picking Buffer',
    fact_type: 'bin_utilization',
    graph_operation: 'area_node',
    match_conditions: {
      aisle_code: '^.+$',
      area_code: '^MS - Storage$',
      empty_location_position_count: '^.+$',
      total_location_position_count: '^.+$',
    },
    metric_type: 'total_locations_occupied',
    node_name: 'multishuttle',
    label: 'Area',
    redis_operation: 'total_storage_locations_occupied',
    time_window: '60m_set',
    views: ['facility'],
  },

  // Multishuttle Aisles - Locations Available
  {
    metric_config_name: 'multishuttle_aisle_locations_available_count',
    aggregation: 'static',
    config_type: 'node',
    description: 'Number of available locations within this DMS aisle.',
    fact_type: 'bin_utilization',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      aisle_code: '^.+$',
      area_code: '^MS - Storage$',
      empty_location_position_count: '^.+$',
    },
    metric_type: 'locations_available',
    node_name: 'MSAI{aisle_code}',
    parent_nodes: ['multishuttle'],
    redis_operation: 'storage_location_distribution_available',
    time_window: 'value',
    views: ['multishuttle'],
  },

  // Multishuttle Aisles - Locations Occupied
  {
    metric_config_name: 'multishuttle_aisle_locations_occupied_count',
    aggregation: 'static',
    config_type: 'node',
    description: 'Number of occupied locations within this DMS aisle.',
    fact_type: 'bin_utilization',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      aisle_code: '^.+$',
      area_code: '^MS - Storage$',
      empty_location_position_count: '^.+$',
      total_location_position_count: '^.+$',
    },
    metric_type: 'locations_occupied',
    node_name: 'MSAI{aisle_code}',
    parent_nodes: ['multishuttle'],
    redis_operation: 'storage_location_distribution_occupied',
    time_window: 'value',
    views: ['multishuttle'],
  },

  // Miniload - Distinct number of aisles
  {
    metric_config_name: 'miniload_aisle_available_count',
    aggregation: 'count',
    config_type: 'node',
    description: 'Number of available aisles within the Mini-Load Buffer',
    fact_type: 'bin_utilization',
    graph_operation: 'area_node',
    match_conditions: {
      area_code: '^ML - Storage$',
      empty_location_position_count: '^.+$', // greater than 0
      aisle_code: '^.+$',
    },
    metric_type: 'aisles_available',
    node_name: 'miniload',
    label: 'Area',
    redis_operation: 'distinct_item_count',
    redis_params: {identifier: '{aisle_code}'},
    time_window: '60m_set',
    views: ['facility'],
  },

  // Miniload - number of available locations (across all aisles)
  {
    metric_config_name: 'miniload_total_locations_available',
    aggregation: 'sum_item_values',
    config_type: 'node',
    description:
      'Number of available locations per aisle within the Mini-Load Buffer',
    fact_type: 'bin_utilization',
    graph_operation: 'area_node',
    match_conditions: {
      area_code: '^ML - Storage$',
      empty_location_position_count: '^.+$',
      aisle_code: '^.+$',
    },
    metric_type: 'total_locations_available',
    node_name: 'miniload',
    label: 'Area',
    redis_operation: 'complex_event_set',
    redis_params: {
      value: '{empty_location_position_count}',
      identifier: '{aisle_code}',
    },
    time_window: '60m_set',
    views: ['facility'],
  },

  // Miniload - number of occupied locations (across all aisles)
  {
    metric_config_name: 'miniload_total_locations_occupied',
    aggregation: 'sum_item_values',
    config_type: 'node',
    description: 'Number of occupied locations within the Mini-Load Buffer',
    fact_type: 'bin_utilization',
    graph_operation: 'area_node',
    match_conditions: {
      area_code: '^ML - Storage$',
      empty_location_position_count: '^.+$',
      total_location_position_count: '^.+$',
      aisle_code: '^.+$',
    },
    metric_type: 'total_locations_occupied',
    node_name: 'miniload',
    label: 'Area',
    redis_operation: 'total_storage_locations_occupied',
    time_window: '60m_set',
    views: ['facility'],
  },

  // Multishuttle Aisle Module Metrics
  // Aisle - Storage Utilization
  {
    metric_config_name: 'multishuttle_aisle_storage_utilization',
    aggregation: 'static',
    config_type: 'node',
    description:
      'Ratio of occupied locations to total locations within this DMS aisle.',
    fact_type: 'bin_utilization',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      area_code: '^MS - Storage$',
      empty_location_position_count: '^.+$',
      total_location_position_count: '^.+$',
      aisle_code: '^.+$',
    },
    metric_type: 'storage_utilization',
    metric_units: '%',
    node_name: 'MSAI{aisle_code}',
    parent_nodes: ['multishuttle'],
    redis_operation: 'storage_utilization',
    time_window: 'value',
    views: ['multishuttle', 'MSAI{aisle_code}'],
  },

  // Aisle - Storage Location Distribution - Available
  {
    metric_config_name:
      'multishuttle_aisle_storage_location_distribution_available',
    aggregation: 'static',
    config_type: 'node',
    description: 'Number of available locations within this DMS aisle.',
    fact_type: 'bin_utilization',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      area_code: '^MS - Storage$',
      empty_location_position_count: '^.+$',
      aisle_code: '^.+$',
    },
    metric_type: 'location_distribution_available',
    node_name: 'MSAI{aisle_code}',
    parent_nodes: ['multishuttle'],
    redis_operation: 'storage_location_distribution_available',
    time_window: 'value',
    views: ['multishuttle', 'MSAI{aisle_code}'],
  },

  // Aisle - Storage Location Distribution - Occupied
  {
    metric_config_name:
      'multishuttle_aisle_storage_location_distribution_occupied',
    aggregation: 'static',
    config_type: 'node',
    description: 'Number of occupied locations within this DMS aisle.',
    fact_type: 'bin_utilization',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      area_code: '^MS - Storage$',
      empty_location_position_count: '^.+$',
      total_location_position_count: '^.+$',
      aisle_code: '^.+$',
    },
    metric_type: 'location_distribution_occupied',
    node_name: 'MSAI{aisle_code}',
    parent_nodes: ['multishuttle'],
    redis_operation: 'storage_location_distribution_occupied',
    time_window: 'value',
    views: ['multishuttle', 'MSAI{aisle_code}'],
  },

  // Aisle Metrics within miniload graph-view
  // Miniload Locations Per Aisle Available
  {
    metric_config_name: 'miniload_aisle_locations_available_count',
    aggregation: 'static',
    config_type: 'node',
    description: 'Number of available locations within this Mini-Load aisle',
    fact_type: 'bin_utilization',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      aisle_code: '^.+$',
      area_code: '^ML - Storage$',
      empty_location_position_count: '^.+$',
    },
    metric_type: 'locations_available',
    node_name: 'MLAI{aisle_code}',
    parent_nodes: ['miniload'],
    redis_operation: 'storage_location_distribution_available',
    time_window: 'value',
    views: ['miniload'],
  },

  // Miniload Locations Per Aisle Occupied
  {
    metric_config_name: 'miniload_aisle_locations_occupied',
    aggregation: 'static',
    config_type: 'node',
    description: 'Number of occupied locations within this Mini-Load aisle.',
    fact_type: 'bin_utilization',
    graph_operation: 'area_node',
    label: 'Aisle',
    match_conditions: {
      area_code: '^ML - Storage$',
      empty_location_position_count: '^.+$',
      total_location_position_count: '^.+$',
      aisle_code: '^.+$',
    },
    metric_type: 'locations_occupied',
    node_name: 'MLAI{aisle_code}',
    parent_nodes: ['miniload'],
    redis_operation: 'storage_location_distribution_occupied',
    time_window: 'value',
    views: ['miniload'],
  },
];

export default binUtilizationMetricConfigs;
