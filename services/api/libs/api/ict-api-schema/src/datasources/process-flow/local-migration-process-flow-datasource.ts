import {DataSource, DataSourceOptions} from 'typeorm';
import {DataSourceUtils, EntityTypes} from '../../utils/datasource-utils.ts';

const options: DataSourceOptions = {
  type: 'postgres',
  host: 'localhost',
  port: 5432,
  username: 'postgres',
  password: 'devpassword',
  database: process.env.CURRENT_TENANT ?? 'ict_development',
  schema: 'process_flow',
  migrations: DataSourceUtils.getMigrationListFromEntityType(
    EntityTypes.ProcessFlow
  ),
  entities: DataSourceUtils.getEntityListFromEntityType(
    EntityTypes.ProcessFlow
  ),
};

const dataSource = new DataSource(options);
export default dataSource;
