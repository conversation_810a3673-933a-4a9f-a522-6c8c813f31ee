import {AppConfigSettingName} from './app-config-setting-name';
import {AppConfigSettingSource} from './app-config-setting-source';
import {AppConfigSettingDataType} from './app-config-setting-data-type';
import {AppConfigSettingGroupName} from './app-config-setting-group';
/**
 * DTO for a setting with its value set to the most relevant value for the user.
 */
export interface AppConfigSetting {
  id: string;
  name: AppConfigSettingName | string;
  group?: AppConfigSettingGroupName | string | null;
  description?: string | null;
  dataType: AppConfigSettingDataType;
  source?: AppConfigSettingSource;
  value?: unknown;
  tags?: {[key: string]: string} | null;
  parentSetting?: string | null;
}

export type AppConfigSettingArrayOrAppConfigSetting =
  | AppConfigSetting[]
  | AppConfigSetting;
