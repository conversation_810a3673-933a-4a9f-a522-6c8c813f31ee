import {SystemSettingNames} from './system/system-setting-name.ts';
import {DashboardSettingNames} from './dashboards/dashboard-setting-name.ts';
import {FeatureFlagNames} from './feature-flags/feature-flag-name.ts';
import {InventorySettingNames} from './inventory/inventory-setting-name.ts';
import {WorkstationSettingNames} from './workstation/workstation-setting-name.ts';
import {DataExplorerSettingNames} from './data-explorer/data-explorer-setting-name.ts';
import {TableauSettingNames} from './tableau/tableau-setting-name.ts';
import {OrdersSettingNames} from './orders/orders-setting-name.ts';

// Combine all our feature settings into a single const.
export const AppConfigSettingNames = [
  ...SystemSettingNames,
  ...DashboardSettingNames,
  ...FeatureFlagNames,
  ...InventorySettingNames,
  ...OrdersSettingNames,
  ...WorkstationSettingNames,
  ...DataExplorerSettingNames,
  ...TableauSettingNames,
] as const;

export type AppConfigSettingName =
  | (typeof AppConfigSettingNames)[number]
  | (string & {});
