@startuml
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

LAYOUT_TOP_DOWN()

title L3 Control Tower API Terraform Functions

System_Boundary(terraform, "git://control-tower-api", "gitlab") {

  Container_Ext(apigee, "Apigee", "") {
    Container(enableApigeeServices, "enableGCPServices")

    Container(createApigeeOrg, "APIGEE Organization Creation")
    Container(createApigeeEnv, "APIGEE Environment Creation")
    Container(createApigeeIns, "APIGEE Instance Creation", "in gcp PSC Zone")
    Container(createApigeeMIG, "APIGEE MIG Bridge", "...pending...")
    Container(createApigeeLB, "APIGEE LB", "...testing...")

   }
  Container_Ext(cloudRunP2, "CloudRun-Phase2", "") {
            Container(enableCloudRunP2Services, "enableGCPServices")
            Container(runMockDataAPI, "Mock API Data")
            Container(runEquipmentAPI, "Equipment API")
            Container(runInventoryAPI, "Inventory API")
            Container(runOperatorsAPI, "Operators API")
            Container(runOrdersAPI, "Orders API")
            Container(oauth2Client, "Oauth2 ID for Run Loadbalancer")
            Container(runLB, "Cloud Run Loadbalancer")
            Container(runCA, "Run Security Policy [Cloud Armor]", "...pending...")
  }

  Container_Ext(cloudSQL, "CloudSQL", "") { 
            Container(enableCloudSQLServices, "enableGCPServices")
            Container(cloudSQLPostGres, "Postgres SQL Instance")
  }
  Container_Ext(cloudStorage, "CloudStorage", "") { 
            Container(enableCloudStorageServices, "enableGCPServices")
            Container(mockAPIData, "Mock Cloud Storage Bucket")

  }
  Container_Ext(configs, "Configs", "") {
            Container(tfVarsFiles, "Environment Config Files")

   }
  Container_Ext(memorystoreP2, "Memorystore-Phase2", "") {
            Container(enableMemorystoreP2Services, "enableGCPServices")
            Container(memorystoreRedisP2, "PSC Connected Redis")

   }

  Container_Ext(networkP2, "Network-Phase2", "") {
            Container(enableNetworkP2Services, "enableGCPServices")
            Container(cloudNAT, "Cloud NAT", "Required for outbound connections from all CloudRuns")
            Container(subnetVPCServiceConnect, "VPC for Service Connect")
            Container(allocatedRangePSC, "Allocated Range /20 for Private Service Connections")
            Container(vpcAccessConnector, "VPC Serverless Connector for all Cloud Runs")

   }

  Container_Ext(vpc, "VPC", "temp") { 
            Container(enableVPCServices, "enableGCPServices")
            Container(networkAPI, "Creates API network and temp subnets as needed")

  }

}

Rel_D(vpc, networkP2, "Step 0")
Rel_D(networkP2, memorystoreP2, "Step1")
Rel_D(networkP2, cloudSQL, "Step1")
Rel_D(memorystoreP2, cloudRunP2, "Step2")
Rel_D(cloudSQL, cloudRunP2, "Step2")


Rel_U(apigee, cloudRunP2, "Step3")



' CloudRunP2
Rel(runLB, runCA, "https")
Rel(runLB, oauth2Client, "Auth")
Rel(runLB, runMockDataAPI, "mock")
Rel(runLB, runEquipmentAPI, "equipment")
Rel(runLB, runInventoryAPI, "inventory")
Rel(runLB, runOperatorsAPI, "operators")
Rel(runLB, runOrdersAPI, "orderes")

' Apigee
Rel(createApigeeOrg, createApigeeEnv, "Apigee ENV")
Rel_D(createApigeeEnv, createApigeeIns, "Ext GCP Apigee")
Rel_D(createApigeeEnv, createApigeeMIG, "ENV Association Bridge")
Rel(createApigeeLB, createApigeeMIG, "Managed Instange Group Rules")
Rel(createApigeeMIG, createApigeeIns, "Bridge Established")


center footer Generated for Dematic.com Intelligent Control Tower 

@enduml