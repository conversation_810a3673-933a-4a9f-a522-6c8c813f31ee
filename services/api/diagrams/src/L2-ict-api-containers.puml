@startuml
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml


title L2 Control Tower API Development Phase

System_Boundary(browser, "WebBrowser") {
Person(person<PERSON><PERSON>s, "ClientUser", "WebBrowser")
System(ictUI, "ControlTowerUI", "FrontEnd Static Code")
}

System_Boundary(gcpUI, "ict-{env}", "folder") {
System(ictUIStaticCode, "Control Tower UI Code", "LoadBalancer")
}

System_Boundary(gcpAPI, "ict-{env}-api", "folder") {
  Container(runLoadBalancer, "ICT CloudRun LoadBalancer", "multiple subcomp")
  Container(apigeeLoadBalancer, "ICT APigee LoadBalancer", "multiple subcomp")
  Container(apigeeMIG, "ICT APigee MIG Bridge", "managed instance groups", "pending")


' Using the same cloud armor policy out the gate
  Container(cloudArmor, "Cloud Armor Policy", "pending...")

' GCP Services 
  Container(bigQuery, "BigQuery Dataset")
  Container(iap, "IAP")


  Container_Ext(vpcServerlessConnector, "VPC Serverless Connector", "/28") {
      Container(cloudRun, "All CloudRun Functions", "vpc connected")
  }

' Allocated IP Range
' Technically, this EXT is OUT of our folder, but I'm keeping it IN for sake of clarity
  Container_Ext(privateServiceConnect, "PSC", "192.168.{x}/20"){
      Container(memorystoreRedis, "MemoryStore Redis", "vpc connected", "managed /28")
      Container(cloudSQL, "Cloud SQL", "vpc connected", "managed /24 per region")
      Container(apigeeZONE, "GCP Apigee", "vpc connected", "managed /22")
  }


}

' UI Download 
Rel(personAlias, ictUI, "local")
BiRel(personAlias, ictUIStaticCode, "download over https")

' Auth and Securty relationships
Rel_U(runLoadBalancer, iap, "auth")
Rel_U(apigeeLoadBalancer, iap, "auth")
Rel_U(runLoadBalancer, cloudArmor, "WAF")
Rel_U(apigeeLoadBalancer, cloudArmor,  "WAF")


' API Paths Internals

BiRel(ictUI, cloudRun, "Insecure Path")

BiRel(ictUI, runLoadBalancer, "Current Path")
BiRel_D(runLoadBalancer, cloudRun, "Current Path")

BiRel(ictUI, apigeeLoadBalancer, "Ideal Path")
BiRel(apigeeLoadBalancer, apigeeMIG, "Ideal Path")
BiRel(apigeeMIG, apigeeZONE, "Ideal Path")
BiRel(apigeeZONE, runLoadBalancer, "Ideal Path (South)")

' Cloud Run Internals
BiRel_U(cloudRun, cloudSQL, "Postgres AUTH")
BiRel_U(cloudRun, memorystoreRedis, "REDIS")
BiRel_U(cloudRun, bigQuery, "Big Data")

center footer Generated for Dematic.com Intelligent Control Tower 

@enduml