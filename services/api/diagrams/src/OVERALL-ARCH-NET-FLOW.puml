@startuml
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml


title  ICT Control Tower API Overall ARCH Deployed (Current)



System_Boundary(browser, "WebBrowser") {
Person(person<PERSON><PERSON><PERSON>, "ClientUser", "WebBrowser")
System(ictUI, "ControlTowerUI", "FrontEnd Static Code")
}

System_Boundary(gcpUI, "ict-{env}", "folder") {
System(ictUIStaticCode, "Control Tower UI Code", "LoadBalancer")
}

System_Boundary(gcpAPI, "ict-{env}-api GCP Project", "project") {


System_Boundary(gcpAPIVPC, "VPC ict-{env}-api ************/20", "vpc") {
  Container(runLoadBalancer, "ICT CloudRun LoadBalancer", "TLS1.2+")
  Container(subnetPub, "Public Subnet", "Not in use", "************/24")
  Container(subnetPrivate, "Private Subnet", "Not in use", "************/24")
note right: this is a precursor to Apigee integration

}

System_Boundary(gcpSAS, "ict-{env}-api Google Hosted Services") {

  ' Using the same cloud armor policy out the gate '
  Container(cloudArmor, "Cloud Armor Policy", "pending...")

  ' GCP Services '
  Container(iap, "IAP/AUTH0")

  Container_Ext(vpcServerlessConnector, "VPC Serverless Connector", "************/28") {
      Container(cloudRun, "All CloudRun Functions", "vpc connected")
  }

' Allocated IP Range
' Technically, this EXT is OUT of our folder, but I'm keeping it IN for sake of clarity
  Container_Ext(privateServiceConnect, "Private Service Connect", "192.168.{x}/20"){
      Container(memorystoreRedis, "MemoryStore Redis", "Google Autoselected /28", "One per region")
      Container(cloudSQL, "Cloud SQL", "Google Autoselected /24", "One per region")
  }
}
  Container(bigQuery, "BigQuery Dataset", "External Google API Access")

}


' UI Download 
Rel(personAlias, ictUI, "Local Client")
BiRel(personAlias, ictUIStaticCode, "download over HTTPS Public IP", "TLS1.2/1.3")

' Auth and Securty relationships
Rel_R(runLoadBalancer, iap, "auth")
Rel_R(runLoadBalancer, cloudArmor, "WAF")


' API Paths Internals

BiRel(ictUI, cloudRun, "Development TLS1.2 HTTPS Direct Invocation Path", "Pre AUTH")

BiRel(ictUI, runLoadBalancer, "TLS1.2")
BiRel_D(runLoadBalancer, cloudRun, "Current Path")


' Cloud Run Internals
BiRel_D(cloudRun, cloudSQL, "Postgres AUTH", "TLS1.2")
BiRel_D(cloudRun, memorystoreRedis, "REDIS", "TLS1.2")
BiRel_R(cloudRun, bigQuery, "TLS1.2", "test")


center footer Generated for Dematic.com Intelligent Control Tower 

@enduml