@startuml
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

LAYOUT_TOP_DOWN()

title L3 Control Tower API Terraform Functions (OLD DEV ZONE)

System_Boundary(terraform, "git://control-tower-api", "gitlab") {


  Container_Ext(cloudBuild, "CloudBuild", "notimplemented") { 
        Container(enableCloudBuildServices, "enableGCPServices")

  }
  Container_Ext(cloudFunctions, "CloudFunctions", "tobedepricated") {   
        Container(enableCFServices, "enableGCPServices")
        Container(cloudFunctionsDev, "Old CloudFunctions")
 }

  Container_Ext(cloudRun, "CloudRun - to be deleted", "to-be-depricated") { 
            Container(runOMockDataAPI, "Mock API Data")
            Container(runOEquipmentAPI, "Equipment API")
            Container(runOInventoryAPI, "Inventory API")
            Container(runOOperatorsAPI, "Operators API")
            Container(runOOrdersAPI, "Orders API")
            Container(enableCloudRunServices, "enableGCPServices")

  }

  Container_Ext(configs, "Configs", "") {
            Container(tfVarsFiles, "Environment Config Files")

   }

  Container_Ext(memorystore, "Memorystore - to be deleted", "to-be-depricated") {
            Container(enableMemorystoreServices, "enableGCPServices")
            Container(memorystoreRedisP1, "old Redis (to be removed)")

   }

  Container_Ext(network, "Network - to be deleted", "to-be-depricated") {
            Container(enableNetworkServices, "enableGCPServices")
            Container(networkAPI, "Creates API network and temp subnets as needed")

  }

}

center footer Generated for Dematic.com Intelligent Control Tower 

@enduml