@startuml
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml


title L1 Control Tower UI Development Phase

System_Boundary(browser, "WebBrowser") {
Person(personAlias, "ClientUser", "WebBrowser")
System(ictUI, "ControlTowerUI", "FrontEnd Static Code")
}

System_Boundary(gcpUI, "ict-{env}", "folder") {
System(ictUIStaticCode, "Control Tower UI Code", "LoadBalancer")
}

System_Boundary(gcpAPI, "ict-{env}-api", "folder") {
System(ictAPIRunLB, "Control Tower API CloudRun", "LoadBalancer", "IDP Auth")
System(ictAPIAPIGEE, "Control Tower API APIGEE", "LoadBalancer", "IDP AUTH + openapi")
System(ictAPICloudRun, "Direct CloudRun Functions", "CloudRun", "https")
}

' UI Download 
Rel(person<PERSON>lia<PERSON>, ictUI, "local")
BiRel(personAlias, ictUIStaticCode, "download over https")

' API Paths
BiRel(ictUI, ictAPIRunLB, "IDP + Path")
BiRel(ictUI, ictAPICloudRun, "Direct HTTPS Call to CloudRun")
BiRel(ictUI, ictAPIAPIGEE, "Ideal Path")

Rel(ictAPIRunLB, ictAPICloudRun, "")
Rel_R(ictAPIAPIGEE, ictAPICloudRun, "")




center footer Generated for Dematic.com Intelligent Control Tower 

@enduml