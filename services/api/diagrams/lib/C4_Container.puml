' convert it with additional command line argument -DRELATIVE_INCLUDE="relative/absolute" to use locally
!if %variable_exists("RELATIVE_INCLUDE")
  !include ./C4_Context.puml
!else
  !include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Context.puml
!endif

' Scope: A single software system.
' Primary elements: Containers within the software system in scope.
' Supporting elements: People and software systems directly connected to the containers.
' Intended audience: Technical people inside and outside of the software development team; including software architects, developers and operations/support staff.

' Colors
' ##################################

!$CONTAINER_FONT_COLOR ?= $ELEMENT_FONT_COLOR
!$CONTAINER_BG_COLOR ?= "#438DD5"
!$CONTAINER_BORDER_COLOR ?= "#3C7FC0"

!$CONTAINER_BOUNDARY_COLOR ?= $BOUNDARY_COLOR
!$CONTAINER_BOUNDARY_BG_COLOR ?= $BOUNDARY_BG_COLOR
!$CONTAINER_BOUNDARY_BORDER_STYLE ?= $BOUNDARY_BORDER_STYLE

!$EXTERNAL_CONTAINER_FONT_COLOR ?= $ELEMENT_FONT_COLOR
!$EXTERNAL_CONTAINER_BG_COLOR ?= "#B3B3B3"
!$EXTERNAL_CONTAINER_BORDER_COLOR ?= "#A6A6A6"

' Styling
' ##################################
UpdateElementStyle("container", $CONTAINER_BG_COLOR, $CONTAINER_FONT_COLOR, $CONTAINER_BORDER_COLOR)
UpdateElementStyle("external_container", $EXTERNAL_CONTAINER_BG_COLOR, $EXTERNAL_CONTAINER_FONT_COLOR, $EXTERNAL_CONTAINER_BORDER_COLOR)

UpdateBoundaryStyle("container", $bgColor=$CONTAINER_BOUNDARY_BG_COLOR, $fontColor=$CONTAINER_BOUNDARY_COLOR, $borderColor=$CONTAINER_BOUNDARY_COLOR, $type="Container")

' shortcuts with default colors
!unquoted procedure AddContainerTag($tagStereo, $bgColor="", $fontColor="", $borderColor="", $shadowing="", $shape="", $sprite="", $techn="", $legendText="", $legendSprite="")
  $addElementTagInclReuse("container", $tagStereo, $bgColor, $fontColor, $borderColor, $shadowing, $shape, $sprite, $techn, $legendText, $legendSprite)
!endprocedure
!unquoted procedure AddExternalContainerTag($tagStereo, $bgColor="", $fontColor="", $borderColor="", $shadowing="", $shape="", $sprite="", $techn="", $legendText="", $legendSprite="")
  $addElementTagInclReuse("external_container", $tagStereo, $bgColor, $fontColor, $borderColor, $shadowing, $shape, $sprite, $techn, $legendText, $legendSprite)
!endprocedure

!unquoted procedure UpdateContainerBoundaryStyle($bgColor=$CONTAINER_BOUNDARY_BG_COLOR, $fontColor=$CONTAINER_BOUNDARY_COLOR, $borderColor=$CONTAINER_BOUNDARY_COLOR, $shadowing="", $shape="", $type="Container", $legendText="")
  UpdateBoundaryStyle("container", $bgColor, $fontColor, $borderColor, $shadowing, $shape, $type, $legendText)
!endprocedure

' Layout
' ##################################

SetDefaultLegendEntries("person\nsystem\ncontainer\nexternal_person\nexternal_system\nexternal_container\nenterprise_boundary\nsystem_boundary\ncontainer_boundary\nboundary")

!procedure LAYOUT_WITH_LEGEND()
hide stereotype
legend right
|<color:$LEGEND_TITLE_COLOR>**Legend**</color> |
|<$PERSON_BG_COLOR> person |
|<$SYSTEM_BG_COLOR> system |
|<$CONTAINER_BG_COLOR> container |
|<$EXTERNAL_PERSON_BG_COLOR> external person |
|<$EXTERNAL_SYSTEM_BG_COLOR> external system |
|<$EXTERNAL_CONTAINER_BG_COLOR> external container |
endlegend
!endprocedure

' Elements
' ##################################

!function $getContainer($label, $techn, $descr, $sprite)
  !return $getElementBase($label, $techn, $descr, $sprite)
!endfunction

!unquoted procedure Container($alias, $label, $techn="", $descr="", $sprite="", $tags="", $link="")
!$sprite=$toElementArg($sprite, $tags, "ElementTagSprite", "container")
!$techn=$toElementArg($techn, $tags, "ElementTagTechn", "container")
rectangle "$getContainer($label, $techn, $descr, $sprite)$getProps()" $toStereos("container", $tags) as $alias $getLink($link)
!endprocedure

!unquoted procedure ContainerDb($alias, $label, $techn="", $descr="", $sprite="", $tags="", $link="")
!$sprite=$toElementArg($sprite, $tags, "ElementTagSprite", "container")
!$techn=$toElementArg($techn, $tags, "ElementTagTechn", "container")
database "$getContainer($label, $techn, $descr, $sprite)$getProps()" $toStereos("container", $tags) as $alias $getLink($link)
!endprocedure

!unquoted procedure ContainerQueue($alias, $label, $techn="", $descr="", $sprite="", $tags="", $link="")
!$sprite=$toElementArg($sprite, $tags, "ElementTagSprite", "container")
!$techn=$toElementArg($techn, $tags, "ElementTagTechn", "container")
queue "$getContainer($label, $techn, $descr, $sprite)$getProps()" $toStereos("container", $tags) as $alias $getLink($link)
!endprocedure

!unquoted procedure Container_Ext($alias, $label, $techn="", $descr="", $sprite="", $tags="", $link="")
!$sprite=$toElementArg($sprite, $tags, "ElementTagSprite", "external_container")
!$techn=$toElementArg($techn, $tags, "ElementTagTechn", "external_container")
rectangle "$getContainer($label, $techn, $descr, $sprite)$getProps()" $toStereos("external_container", $tags) as $alias $getLink($link)
!endprocedure

!unquoted procedure ContainerDb_Ext($alias, $label, $techn="", $descr="", $sprite="", $tags="", $link="")
!$sprite=$toElementArg($sprite, $tags, "ElementTagSprite", "external_container")
!$techn=$toElementArg($techn, $tags, "ElementTagTechn", "external_container")
database "$getContainer($label, $techn, $descr, $sprite)$getProps()" $toStereos("external_container", $tags) as $alias $getLink($link)
!endprocedure

!unquoted procedure ContainerQueue_Ext($alias, $label, $techn="", $descr="", $sprite="", $tags="", $link="")
!$sprite=$toElementArg($sprite, $tags, "ElementTagSprite", "external_container")
!$techn=$toElementArg($techn, $tags, "ElementTagTechn", "external_container")
queue "$getContainer($label, $techn, $descr, $sprite)$getProps()" $toStereos("external_container", $tags) as $alias $getLink($link)
!endprocedure

' Boundaries
' ##################################

!unquoted procedure Container_Boundary($alias, $label, $tags="", $link="")
  !if ($tags != "")
    !$allTags = $tags + '+container'
  !else
    !$allTags = 'container'
  !endif
  ' $type defined via $tag style
  Boundary($alias, $label, "", $allTags, $link)
!endprocedure
