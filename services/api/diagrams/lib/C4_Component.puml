' convert it with additional command line argument -DRELATIVE_INCLUDE="relative/absolute" to use locally
!if %variable_exists("RELATIVE_INCLUDE")
  !include ./C4_Container.puml
!else
  !include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml
!endif

' Scope: A single container.
' Primary elements: Components within the container in scope.
' Supporting elements: Containers (within the software system in scope) plus people and software systems directly connected to the components.
' Intended audience: Software architects and developers.

' Colors
' ##################################

!$COMPONENT_FONT_COLOR ?= "#000000"
!$COMPONENT_BG_COLOR ?= "#85BBF0"
!$COMPONENT_BORDER_COLOR ?= "#78A8D8"

!$EXTERNAL_COMPONENT_FONT_COLOR ?= $COMPONENT_FONT_COLOR
!$EXTERNAL_COMPONENT_BG_COLOR ?= "#CCCCCC"
!$EXTERNAL_COMPONENT_BORDER_COLOR ?= "#BFBFBF"

' Styling
' ##################################

UpdateElementStyle("component", $COMPONENT_BG_COLOR, $COMPONENT_FONT_COLOR, $COMPONENT_BORDER_COLOR)
UpdateElementStyle("external_component", $EXTERNAL_COMPONENT_BG_COLOR, $EXTERNAL_COMPONENT_FONT_COLOR, $EXTERNAL_COMPONENT_BORDER_COLOR)

' shortcuts with default colors
!unquoted procedure AddComponentTag($tagStereo, $bgColor="", $fontColor="", $borderColor="", $shadowing="", $shape="", $sprite="", $techn="", $legendText="", $legendSprite="")
  $addElementTagInclReuse("component", $tagStereo, $bgColor, $fontColor, $borderColor, $shadowing, $shape, $sprite, $techn, $legendText, $legendSprite)
!endprocedure
!unquoted procedure AddExternalComponentTag($tagStereo, $bgColor="", $fontColor="", $borderColor="", $shadowing="", $shape="", $sprite="", $techn="", $legendText="", $legendSprite="")
  $addElementTagInclReuse("external_component", $tagStereo, $bgColor, $fontColor, $borderColor, $shadowing, $shape, $sprite, $techn, $legendText, $legendSprite)
!endprocedure

' Layout
' ##################################

SetDefaultLegendEntries("person\nsystem\ncontainer\ncomponent\nexternal_person\nexternal_system\nexternal_container\nexternal_component\nenterprise_boundary\nsystem_boundary\ncontainer_boundary\nboundary")

!procedure LAYOUT_WITH_LEGEND()
hide stereotype
legend right
|<color:$LEGEND_TITLE_COLOR>**Legend**</color> |
|<$PERSON_BG_COLOR> person |
|<$SYSTEM_BG_COLOR> system |
|<$CONTAINER_BG_COLOR> container |
|<$COMPONENT_BG_COLOR> component |
|<$EXTERNAL_PERSON_BG_COLOR> external person |
|<$EXTERNAL_SYSTEM_BG_COLOR> external system |
|<$EXTERNAL_CONTAINER_BG_COLOR> external container |
|<$EXTERNAL_COMPONENT_BG_COLOR> external component |
endlegend
!endprocedure

' Elements
' ##################################

!function $getComponent($label, $techn, $descr, $sprite)
  !return $getElementBase($label, $techn, $descr, $sprite)
!endfunction

!unquoted procedure Component($alias, $label, $techn="", $descr="", $sprite="", $tags="", $link="")
!$sprite=$toElementArg($sprite, $tags, "ElementTagSprite", "component")
!$techn=$toElementArg($techn, $tags, "ElementTagTechn", "component")
rectangle "$getComponent($label, $techn, $descr, $sprite)$getProps()" $toStereos("component",$tags) as $alias $getLink($link)
!endprocedure

!unquoted procedure ComponentDb($alias, $label, $techn="", $descr="", $sprite="", $tags="", $link="")
!$sprite=$toElementArg($sprite, $tags, "ElementTagSprite", "component")
!$techn=$toElementArg($techn, $tags, "ElementTagTechn", "component")
database "$getComponent($label, $techn, $descr, $sprite)$getProps()" $toStereos("component",$tags) as $alias $getLink($link)
!endprocedure

!unquoted procedure ComponentQueue($alias, $label, $techn="", $descr="", $sprite="", $tags="", $link="")
!$sprite=$toElementArg($sprite, $tags, "ElementTagSprite", "component")
!$techn=$toElementArg($techn, $tags, "ElementTagTechn", "component")
queue "$getComponent($label, $techn, $descr, $sprite)$getProps()" $toStereos("component",$tags) as $alias $getLink($link)
!endprocedure

!unquoted procedure Component_Ext($alias, $label, $techn="", $descr="", $sprite="", $tags="", $link="")
!$sprite=$toElementArg($sprite, $tags, "ElementTagSprite", "external_component")
!$techn=$toElementArg($techn, $tags, "ElementTagTechn", "external_component")
rectangle "$getComponent($label, $techn, $descr, $sprite)$getProps()" $toStereos("external_component",$tags) as $alias $getLink($link)
!endprocedure

!unquoted procedure ComponentDb_Ext($alias, $label, $techn="", $descr="", $sprite="", $tags="", $link="")
!$sprite=$toElementArg($sprite, $tags, "ElementTagSprite", "external_component")
!$techn=$toElementArg($techn, $tags, "ElementTagTechn", "external_component")
database "$getComponent($label, $techn, $descr, $sprite)$getProps()" $toStereos("external_component",$tags) as $alias $getLink($link)
!endprocedure

!unquoted procedure ComponentQueue_Ext($alias, $label, $techn="", $descr="", $sprite="", $tags="", $link="")
!$sprite=$toElementArg($sprite, $tags, "ElementTagSprite", "external_component")
!$techn=$toElementArg($techn, $tags, "ElementTagTechn", "external_component")
queue "$getComponent($label, $techn, $descr, $sprite)$getProps()" $toStereos("external_component",$tags) as $alias $getLink($link)
!endprocedure
