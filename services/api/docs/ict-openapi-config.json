{"inputs": [{"inputFile": "./ict-openapi-template.yaml"}, {"inputFile": "../apps/api/ict-ai-api/docs/openapi.yaml", "operationSelection": {"includeTags": ["ai"]}}, {"inputFile": "../apps/api/ict-config-api/docs/openapi.yaml", "operationSelection": {"includeTags": ["config"]}}, {"inputFile": "../apps/api/ict-dataexplorer-api/docs/openapi.yaml", "operationSelection": {"includeTags": ["data-explorer"]}}, {"inputFile": "../apps/api/ict-diagnostics-api/docs/openapi.yaml", "operationSelection": {"includeTags": ["diagnostics"]}}, {"inputFile": "../apps/api/ict-inventory-api/docs/openapi.yaml", "operationSelection": {"includeTags": ["inventory"]}}, {"inputFile": "../apps/api/ict-equipment-api/docs/openapi.yaml", "operationSelection": {"includeTags": ["equipment"]}}, {"inputFile": "../apps/api/ict-operators-api/docs/openapi.yaml", "operationSelection": {"includeTags": ["operators"]}}, {"inputFile": "../apps/api/ict-orders-api/docs/openapi.yaml", "operationSelection": {"includeTags": ["orders"]}}, {"inputFile": "../apps/api/ict-workstation-api/docs/openapi.yaml", "operationSelection": {"includeTags": ["workstation"]}}, {"inputFile": "../apps/api/ict-simulation-api/docs/openapi.yaml", "operationSelection": {"includeTags": ["simulation"]}}, {"inputFile": "../apps/api/ict-tableau-management-api/docs/openapi.yaml", "operationSelection": {"includeTags": ["tableau-management"]}}, {"inputFile": "../apps/api/ict-admin-api/docs/openapi.yaml", "operationSelection": {"includeTags": ["admin"]}}, {"inputFile": "../apps/api/ict-availability-api/docs/openapi.yaml", "operationSelection": {"includeTags": ["availability"]}}], "output": "./control-tower-openapi.yaml"}