meta {
  name: PostAlarmsListWithFaultDurationSort
  type: http
  seq: 13
}

post {
  url: {{api_host}}/availability/alarms/list
  body: json
  auth: inherit
}

headers {
  ict-facility-id: acehardware#jeffersonga
}

body:json {
  {
    "start_date": "2025-07-01T00:00:00.000Z",
    "end_date": "2025-07-31T23:59:59.999Z",
    "sortFields": [
      {
        "columnName": "faultDuration",
        "isDescending": true
      }
    ],
    "limit": 20,
    "offset": 0
  }
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should have data and metadata", function() {
    expect(res.getBody()).to.have.property('data');
    expect(res.getBody()).to.have.property('metadata');
  });
  
  test("Data should be sorted by fault duration (descending)", function() {
    const data = res.getBody().data;
    expect(data).to.be.an('array');
    
    // Check that fault durations are sorted in descending order
    let lastDuration = null;
    
    for (const alarm of data) {
      const currentDuration = parseInt(alarm.timing.duration);
      
      if (lastDuration !== null) {
        expect(currentDuration).to.be.lessThanOrEqual(lastDuration);
      }
      
      lastDuration = currentDuration;
    }
  });
  
  test("All alarms should have timing.duration field", function() {
    const data = res.getBody().data;
    for (const alarm of data) {
      expect(alarm).to.have.property('timing');
      expect(alarm.timing).to.have.property('duration');
      expect(alarm.timing.duration).to.be.a('string');
      expect(parseInt(alarm.timing.duration)).to.be.a('number');
    }
  });
  
  test("Should show duration variety in dataset", function() {
    const data = res.getBody().data;
    const durations = [];
    
    for (const alarm of data) {
      durations.push(parseInt(alarm.timing.duration));
    }
    
    const minDuration = Math.min(...durations);
    const maxDuration = Math.max(...durations);
    const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
    
    console.log('Duration statistics:', {
      min: minDuration + 'ms',
      max: maxDuration + 'ms', 
      avg: Math.round(avgDuration) + 'ms',
      count: durations.length
    });
    
    // Verify we have variety in durations
    if (maxDuration > minDuration) {
      console.log('✅ Found duration variety - sort testing is meaningful');
    }
  });
}
