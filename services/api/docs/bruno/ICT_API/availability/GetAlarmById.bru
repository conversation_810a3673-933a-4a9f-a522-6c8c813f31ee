meta {
  name: GetAlarmById
  type: http
  seq: 4
}

get {
  url: {{api_host}}/availability/alarms/b307f935
  body: none
  auth: inherit
}

headers {
  ict-facility-id: acehardware#jeffersonga
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should be a single alarm object", function() {
    const alarm = res.getBody();
    expect(alarm).to.be.an('object');
    expect(alarm).to.have.property('id');
    expect(alarm).to.have.property('title');
    expect(alarm).to.have.property('description');
    expect(alarm).to.have.property('tag');
    if (alarm.severity !== undefined) {
      expect(alarm.severity).to.be.a('string');
    }
    expect(alarm).to.have.property('location');
    expect(alarm).to.have.property('timing');
    expect(alarm).to.have.property('status');
    expect(alarm).to.have.property('reason');
  });
  
  test("Location should have required fields", function() {
    const location = res.getBody().location;
    expect(location).to.have.property('area');
    expect(location).to.have.property('section');
    expect(location).to.have.property('equipment');
  });
  
  test("Timing should have required fields", function() {
    const timing = res.getBody().timing;
    // Reference times (REF) - required
    expect(timing).to.have.property('startTime');
    expect(timing).to.have.property('duration');
    expect(timing.duration).to.be.a('string');
    
    // Split times (SPLIT) - optional
    if (timing.splitStartTime) {
      expect(timing.splitStartTime).to.be.a('string');
      expect(new Date(timing.splitStartTime).toISOString()).to.equal(timing.splitStartTime);
    }
    if (timing.splitEndTime) {
      expect(timing.splitEndTime).to.be.a('string');
      expect(new Date(timing.splitEndTime).toISOString()).to.equal(timing.splitEndTime);
    }
    if (timing.splitDuration) {
      expect(timing.splitDuration).to.be.a('string');
    }
    
    // Original times (ORIG) - optional
    if (timing.origStartTime) {
      expect(timing.origStartTime).to.be.a('string');
      expect(new Date(timing.origStartTime).toISOString()).to.equal(timing.origStartTime);
    }
    if (timing.origEndTime) {
      expect(timing.origEndTime).to.be.a('string');
      expect(new Date(timing.origEndTime).toISOString()).to.equal(timing.origEndTime);
    }
    if (timing.origDuration) {
      expect(timing.origDuration).to.be.a('string');
    }
  });
  
  test("Should match expected alarm ID", function() {
    expect(res.getBody().id).to.equal('b307f935');
  });
  
  test("Should have expected alarm details", function() {
    const alarm = res.getBody();
    expect(alarm.title).to.equal('ENET32 COMMUNICATION ERROR');
    expect(alarm.tag).to.equal('ENET32_COMM_ERR');
    expect(alarm.status).to.equal('KEPT');
  });
  
  test("Should have correct location details", function() {
    const location = res.getBody().location;
    expect(location.area).to.equal('DMSInboundMergeSorter');
    expect(location.section).to.equal('ENET32');
    expect(location.equipment).to.equal('ENET32_COMM');
  });
  
  test("Timing should have valid dates and duration", function() {
    const timing = res.getBody().timing;
    expect(timing.startTime).to.be.a('string');
    expect(timing.endTime).to.be.a('string');
    expect(timing.duration).to.equal('56000');
    
    // Verify dates are valid ISO strings
    expect(new Date(timing.startTime).toISOString()).to.equal(timing.startTime);
    expect(new Date(timing.endTime).toISOString()).to.equal(timing.endTime);
  });
}