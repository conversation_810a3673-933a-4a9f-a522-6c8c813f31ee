meta {
  name: PostAlarmsListValidateTimePerspectives
  type: http
  seq: 10
}

post {
  url: {{api_host}}/availability/alarms/list
  body: json
  auth: inherit
}

headers {
  ict-facility-id: acehardware#jeffersonga
}

body:json {
  {
    "start_date": "2025-07-01T00:00:00.000Z",
    "end_date": "2025-07-31T23:59:59.999Z",
    "sortFields": [
      {
        "columnName": "startTime",
        "isDescending": true
      }
    ],
    "limit": 50,
    "offset": 0
  }
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should have data and metadata", function() {
    expect(res.getBody()).to.have.property('data');
    expect(res.getBody()).to.have.property('metadata');
  });
  
  test("All time perspectives should be present and valid", function() {
    const data = res.getBody().data;
    expect(data).to.be.an('array');
    
    if (data.length > 0) {
      for (const alarm of data) {
        const timing = alarm.timing;
        
        // Reference times (REF) - should always be present
        expect(timing).to.have.property('startTime');
        expect(timing).to.have.property('endTime');
        expect(timing).to.have.property('duration');
        expect(timing.startTime).to.be.a('string');
        expect(timing.duration).to.be.a('string');
        
        // Split times (SPLIT) - should be present based on real data
        expect(timing).to.have.property('splitStartTime');
        expect(timing).to.have.property('splitEndTime');
        expect(timing).to.have.property('splitDuration');
        expect(timing.splitStartTime).to.be.a('string');
        expect(timing.splitDuration).to.be.a('string');
        
        // Original times (ORIG) - should be present based on real data
        expect(timing).to.have.property('origStartTime');
        expect(timing).to.have.property('origEndTime');
        expect(timing).to.have.property('origDuration');
        expect(timing.origStartTime).to.be.a('string');
        expect(timing.origDuration).to.be.a('string');
        
        // Validate ISO date formats
        expect(new Date(timing.startTime).toISOString()).to.equal(timing.startTime);
        expect(new Date(timing.splitStartTime).toISOString()).to.equal(timing.splitStartTime);
        expect(new Date(timing.origStartTime).toISOString()).to.equal(timing.origStartTime);
        
        // Validate duration formats (should be numeric strings)
        expect(parseInt(timing.duration)).to.be.a('number');
        expect(parseInt(timing.splitDuration)).to.be.a('number');
        expect(parseInt(timing.origDuration)).to.be.a('number');
      }
    }
  });
  
  test("Should find alarms with different time perspectives", function() {
    const data = res.getBody().data;
    let foundDifferentPerspectives = false;
    
    for (const alarm of data) {
      const timing = alarm.timing;
      
      // Check if split duration differs from reference duration
      if (parseInt(timing.splitDuration) !== parseInt(timing.duration)) {
        foundDifferentPerspectives = true;
        console.log('Found alarm with different split/ref durations:', {
          id: alarm.id,
          refDuration: timing.duration,
          splitDuration: timing.splitDuration,
          origDuration: timing.origDuration
        });
        break;
      }
      
      // Check if original duration differs from reference duration
      if (parseInt(timing.origDuration) !== parseInt(timing.duration)) {
        foundDifferentPerspectives = true;
        console.log('Found alarm with different orig/ref durations:', {
          id: alarm.id,
          refDuration: timing.duration,
          origDuration: timing.origDuration
        });
        break;
      }
    }
    
    // This demonstrates that the time perspectives are actually working
    if (foundDifferentPerspectives) {
      console.log('✅ Time perspectives are working - found alarms with different duration values');
    } else {
      console.log('ℹ️ All alarms in this dataset have identical time perspectives');
    }
  });
  
  test("Duplicated alarms should represent split time periods", function() {
    const data = res.getBody().data;
    const faultIdCounts = {};
    
    // Count occurrences of each fault ID
    for (const alarm of data) {
      if (faultIdCounts[alarm.faultId]) {
        faultIdCounts[alarm.faultId]++;
      } else {
        faultIdCounts[alarm.faultId] = 1;
      }
    }
    
    // Find duplicated fault IDs (split alarms)
    let foundSplitAlarms = false;
    for (const [faultId, count] of Object.entries(faultIdCounts)) {
      if (count > 1) {
        foundSplitAlarms = true;
        console.log(`Found split alarm - Fault ID ${faultId} appears ${count} times`);
        
        // Verify that these represent different time periods
        const splitAlarms = data.filter(alarm => alarm.faultId === faultId);
        if (splitAlarms.length >= 2) {
          const firstSplit = splitAlarms[0].timing;
          const secondSplit = splitAlarms[1].timing;
          
          console.log('Split time periods:', {
            first: { start: firstSplit.splitStartTime, end: firstSplit.splitEndTime },
            second: { start: secondSplit.splitStartTime, end: secondSplit.splitEndTime }
          });
        }
        break;
      }
    }
    
    if (foundSplitAlarms) {
      console.log('✅ Found evidence of alarm splitting functionality');
    } else {
      console.log('ℹ️ No split alarms found in this dataset');
    }
  });
}
