meta {
  name: PostAlarmsListWithFaultTagSort
  type: http
  seq: 9
}

post {
  url: {{api_host}}/availability/alarms/list
  body: json
  auth: inherit
}

headers {
  ict-facility-id: acehardware#jeffersonga
}

body:json {
  {
    "start_date": "2025-07-01T00:00:00.000Z",
    "end_date": "2025-07-31T23:59:59.999Z",
    "sortFields": [
      {
        "columnName": "faultTag",
        "isDescending": false
      }
    ],
    "limit": 20,
    "offset": 0
  }
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should have data and metadata", function() {
    expect(res.getBody()).to.have.property('data');
    expect(res.getBody()).to.have.property('metadata');
  });
  
  test("Data should be sorted by fault tag (ascending)", function() {
    const data = res.getBody().data;
    expect(data).to.be.an('array');
    
    // Check that fault tags are sorted alphabetically
    let lastTag = null;
    
    for (const alarm of data) {
      if (alarm.tag && lastTag) {
        expect(alarm.tag.localeCompare(lastTag)).to.be.greaterThanOrEqual(0);
      }
      if (alarm.tag) {
        lastTag = alarm.tag;
      }
    }
  });
  
  test("All alarms should have fault tag field", function() {
    const data = res.getBody().data;
    for (const alarm of data) {
      expect(alarm).to.have.property('tag');
      expect(alarm.tag).to.be.a('string');
    }
  });
  
  test("Fault tags should follow expected naming pattern", function() {
    const data = res.getBody().data;
    for (const alarm of data) {
      if (alarm.tag) {
        // Most fault tags contain underscores and uppercase letters
        expect(alarm.tag).to.be.a('string');
        expect(alarm.tag.length).to.be.greaterThan(0);
      }
    }
  });
}
