meta {
  name: PostAlarmsList
  type: http
  seq: 3
}

post {
  url: {{api_host}}/availability/alarms/list
  body: json
  auth: inherit
}

headers {
  ict-facility-id: acehardware#jeffersonga
}

body:json {
  {
    "start_date": "2025-07-01T00:00:00.000Z",
    "end_date": "2025-07-31T23:59:59.999Z",
    "filters": {
      "areas": ["DMSInboundMergeSorter"],
      "sections": ["CC001"],
      "equipment": ["CA003_AGL_L1"],
      "statuses": ["Active"]
    },
    "sortFields": [
      {
        "columnName": "startTime",
        "isDescending": true
      }
    ],
    // Available sort fields: area, section, equipment, faultId, description, tag, faultTag, severity, startTime, endTime, faultDuration, splitStartTime, splitEndTime, splitDuration, origStartTime, origEndTime, origDuration, refDuration, status, reason, updatedStartTime, updatedEndTime, updatedDuration, duration, comments
    "groupByFields": [],
    "limit": 50,
    "offset": 0,
    "searchString": "grid lock"
  }
}