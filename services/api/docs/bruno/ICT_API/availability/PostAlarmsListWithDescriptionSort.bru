meta {
  name: PostAlarmsListWithDescriptionSort
  type: http
  seq: 11
}

post {
  url: {{api_host}}/availability/alarms/list
  body: json
  auth: inherit
}

headers {
  ict-facility-id: acehardware#jeffersonga
}

body:json {
  {
    "start_date": "2025-07-01T00:00:00.000Z",
    "end_date": "2025-07-31T23:59:59.999Z",
    "sortFields": [
      {
        "columnName": "description",
        "isDescending": false
      }
    ],
    "limit": 20,
    "offset": 0
  }
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should have data and metadata", function() {
    expect(res.getBody()).to.have.property('data');
    expect(res.getBody()).to.have.property('metadata');
  });
  
  test("Data should be sorted by description (ascending)", function() {
    const data = res.getBody().data;
    expect(data).to.be.an('array');
    
    // Check that descriptions are sorted alphabetically
    let lastDescription = null;
    
    for (const alarm of data) {
      if (alarm.description && lastDescription) {
        expect(alarm.description.localeCompare(lastDescription)).to.be.greaterThanOrEqual(0);
      }
      if (alarm.description) {
        lastDescription = alarm.description;
      }
    }
  });
  
  test("All alarms should have description field", function() {
    const data = res.getBody().data;
    for (const alarm of data) {
      expect(alarm).to.have.property('description');
      expect(alarm.description).to.be.a('string');
    }
  });
}
