meta {
  name: PostAlarmsListWithSplitTimeSort
  type: http
  seq: 7
}

post {
  url: {{api_host}}/availability/alarms/list
  body: json
  auth: inherit
}

headers {
  ict-facility-id: acehardware#jeffersonga
}

body:json {
  {
    "start_date": "2025-07-01T00:00:00.000Z",
    "end_date": "2025-07-31T23:59:59.999Z",
    "sortFields": [
      {
        "columnName": "splitStartTime",
        "isDescending": false
      }
    ],
    "limit": 20,
    "offset": 0
  }
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should have data and metadata", function() {
    expect(res.getBody()).to.have.property('data');
    expect(res.getBody()).to.have.property('metadata');
  });
  
  test("Data should be sorted by split start time (ascending)", function() {
    const data = res.getBody().data;
    expect(data).to.be.an('array');
    
    // If we have multiple alarms with split start times, they should be sorted
    let lastSplitStartTime = null;
    
    for (const alarm of data) {
      if (alarm.timing.splitStartTime && lastSplitStartTime) {
        const lastTime = new Date(lastSplitStartTime);
        const currentTime = new Date(alarm.timing.splitStartTime);
        expect(currentTime.getTime()).to.be.greaterThanOrEqual(lastTime.getTime());
      }
      if (alarm.timing.splitStartTime) {
        lastSplitStartTime = alarm.timing.splitStartTime;
      }
    }
  });
  
  test("Split time fields should be valid ISO dates when present", function() {
    const data = res.getBody().data;
    for (const alarm of data) {
      const timing = alarm.timing;
      
      if (timing.splitStartTime) {
        expect(timing.splitStartTime).to.be.a('string');
        expect(new Date(timing.splitStartTime).toISOString()).to.equal(timing.splitStartTime);
      }
      
      if (timing.splitEndTime) {
        expect(timing.splitEndTime).to.be.a('string');
        expect(new Date(timing.splitEndTime).toISOString()).to.equal(timing.splitEndTime);
      }
      
      if (timing.splitDuration) {
        expect(timing.splitDuration).to.be.a('string');
        expect(parseInt(timing.splitDuration)).to.be.a('number');
      }
    }
  });
}
