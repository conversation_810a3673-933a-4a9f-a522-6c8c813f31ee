meta {
  name: PostAlarmsListWithReasonSort
  type: http
  seq: 12
}

post {
  url: {{api_host}}/availability/alarms/list
  body: json
  auth: inherit
}

headers {
  ict-facility-id: acehardware#jeffersonga
}

body:json {
  {
    "start_date": "2025-07-01T00:00:00.000Z",
    "end_date": "2025-07-31T23:59:59.999Z",
    "sortFields": [
      {
        "columnName": "reason",
        "isDescending": false
      }
    ],
    "limit": 20,
    "offset": 0
  }
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should have data and metadata", function() {
    expect(res.getBody()).to.have.property('data');
    expect(res.getBody()).to.have.property('metadata');
  });
  
  test("Data should be sorted by reason (ascending)", function() {
    const data = res.getBody().data;
    expect(data).to.be.an('array');
    
    // Check that reasons are sorted alphabetically
    let lastReason = null;
    
    for (const alarm of data) {
      if (alarm.reason && lastReason) {
        expect(alarm.reason.localeCompare(lastReason)).to.be.greaterThanOrEqual(0);
      }
      if (alarm.reason) {
        lastReason = alarm.reason;
      }
    }
  });
  
  test("All alarms should have reason field", function() {
    const data = res.getBody().data;
    for (const alarm of data) {
      expect(alarm).to.have.property('reason');
      expect(alarm.reason).to.be.a('string');
    }
  });
  
  test("Should find alarms with various reason values", function() {
    const data = res.getBody().data;
    const reasons = new Set();
    
    for (const alarm of data) {
      if (alarm.reason && alarm.reason.trim() !== '') {
        reasons.add(alarm.reason);
      }
    }
    
    console.log('Found reason values:', Array.from(reasons));
    
    // Log if we found multiple different reasons
    if (reasons.size > 1) {
      console.log('✅ Found multiple different reason values - sorting is testable');
    } else {
      console.log('ℹ️ Found limited reason variety in this dataset');
    }
  });
}
