meta {
  name: PostAlarmsListWithOrigTimeSort
  type: http
  seq: 8
}

post {
  url: {{api_host}}/availability/alarms/list
  body: json
  auth: inherit
}

headers {
  ict-facility-id: acehardware#jeffersonga
}

body:json {
  {
    "start_date": "2025-07-01T00:00:00.000Z",
    "end_date": "2025-07-31T23:59:59.999Z",
    "sortFields": [
      {
        "columnName": "origDuration",
        "isDescending": true
      }
    ],
    "limit": 20,
    "offset": 0
  }
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should have data and metadata", function() {
    expect(res.getBody()).to.have.property('data');
    expect(res.getBody()).to.have.property('metadata');
  });
  
  test("Data should be sorted by original duration (descending)", function() {
    const data = res.getBody().data;
    expect(data).to.be.an('array');
    
    // If we have multiple alarms with original durations, they should be sorted
    let lastOrigDuration = null;
    
    for (const alarm of data) {
      if (alarm.timing.origDuration && lastOrigDuration) {
        const lastDuration = parseInt(lastOrigDuration);
        const currentDuration = parseInt(alarm.timing.origDuration);
        expect(currentDuration).to.be.lessThanOrEqual(lastDuration);
      }
      if (alarm.timing.origDuration) {
        lastOrigDuration = alarm.timing.origDuration;
      }
    }
  });
  
  test("Original time fields should be valid when present", function() {
    const data = res.getBody().data;
    for (const alarm of data) {
      const timing = alarm.timing;
      
      if (timing.origStartTime) {
        expect(timing.origStartTime).to.be.a('string');
        expect(new Date(timing.origStartTime).toISOString()).to.equal(timing.origStartTime);
      }
      
      if (timing.origEndTime) {
        expect(timing.origEndTime).to.be.a('string');
        expect(new Date(timing.origEndTime).toISOString()).to.equal(timing.origEndTime);
      }
      
      if (timing.origDuration) {
        expect(timing.origDuration).to.be.a('string');
        expect(parseInt(timing.origDuration)).to.be.a('number');
      }
    }
  });
  
  test("Should verify time perspective consistency when all fields present", function() {
    const data = res.getBody().data;
    for (const alarm of data) {
      const timing = alarm.timing;
      
      // If both original start and end times are present, duration should match
      if (timing.origStartTime && timing.origEndTime && timing.origDuration) {
        const startTime = new Date(timing.origStartTime);
        const endTime = new Date(timing.origEndTime);
        const calculatedDuration = endTime.getTime() - startTime.getTime();
        const actualDuration = parseInt(timing.origDuration);
        
        // Allow for small differences due to rounding
        expect(Math.abs(calculatedDuration - actualDuration)).to.be.lessThan(1000);
      }
    }
  });
}
