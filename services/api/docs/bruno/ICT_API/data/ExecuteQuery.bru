meta {
  name: ExecuteQuery
  type: http
  seq: 1
}

post {
  url: {{api_host}}/data/query?start_date=T&end_date=T
  body: json
  auth: inherit
}

params:query {
  start_date: T
  end_date: T
}

body:json {
  {
    "id": "active-operators",
    "label": "Active Operators",
    "type": "data-point",
    "query": "SELECT \n   online_operators AS value\n      FROM {{edp}}\n      WHERE etl_create_timestamp_utc <= @end_date\n      ORDER BY etl_create_timestamp_utc DESC\n      LIMIT 1",
    "filters": [],
    "metadata": {
      "unit": "points",
      "category": "operators"
    },
    "parameters": {
      "required": [
        "start_date",
        "end_date"
      ]
    },
    "dataSources": [
      {
        "id": "edp",
        "type": "edp-bigquery",
        "table": "platinum_operators_online"
      }
    ],
    "description": "The number of active operators",
    "isDraft": false
  }
}
