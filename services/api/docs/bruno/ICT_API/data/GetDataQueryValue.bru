meta {
  name: GetDataQueryValue
  type: http
  seq: 2
}

get {
  url: {{api_host}}/data/:data-query-id/value?start_date=T&end_date=T
  body: none
  auth: inherit
}

params:query {
  start_date: T
  end_date: T
}

params:path {
  data-query-id: active-operators
}

body:json {
  {
    "id": "active-operators",
    "label": "Active Operators",
    "query": "\n      SELECT \n   online_operators AS value\n      FROM {{edp.tablePath}}\n      WHERE etl_create_timestamp_utc <= @end_date\n      ORDER BY etl_create_timestamp_utc DESC\n      LIMIT 1 \n    ",
    "filters": [],
    "metadata": {
      "unit": "points",
      "category": "operators"
    },
    "parameters": {
      "optional": [],
      "required": [
        "start_date",
        "end_date"
      ]
    },
    "dataSources": [
      {
        "id": "edp",
        "type": "bigquery",
        "table": "platinum_operators_online"
      }
    ],
    "description": "The number of active operators"
  }
}
