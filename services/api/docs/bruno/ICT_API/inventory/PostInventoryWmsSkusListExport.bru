meta {
  name: PostInventoryWmsSkusListExport
  type: http
  seq: 39
}

post {
  url: {{api_host}}/inventory/wms/skus/list/export
  body: json
  auth: inherit
}

body:json {
  {
    "columns": {
      "sku": true,
      "quantityAvailable": true,
      "quantityAllocated": true,
      "maxContainers": true,
      "contOverage": true,
      "daysOnHand": true,
      "averageDailyQuantity": true,
      "averageDailyOrders": true,
      "skuPositions": true,
      "latestActivityDateTimestamp": true,
      "latestCycleCountTimestamp": true,
      "description": true,
      "targetMultiplicity": true,
      "velocityClassification": true
    }
  }
}
