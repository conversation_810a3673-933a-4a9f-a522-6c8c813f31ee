meta {
  name: PostInventoryContainerList (non-WMS)
  type: http
  seq: 44
}

post {
  url: {{api_host}}/inventory/containers/list/
  body: json
  auth: inherit
}

body:json {
  {
      "limit": 50,
      "page": 0,
      "sortFields": [
          {
              "columnName": "container_id",
              "isDescending": true
          }
      ],
      "byPassConfigSetting": false,
      "searchString": "BWB"
  }
}
