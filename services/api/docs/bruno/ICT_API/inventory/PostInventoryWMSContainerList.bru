meta {
  name: PostInventoryWMSContainerList
  type: http
  seq: 45
}

post {
  url: {{api_host}}/inventory/wms/containers/list/
  body: json
  auth: inherit
}

body:json {
  {
      "limit": 50,
      "page": 0,
      "sortFields": [
          {
              "columnName": "container_id",
              "isDescending": true
          }
      ],
      "byPassConfigSetting": false,
      "searchString": "BWB"
  }
}
