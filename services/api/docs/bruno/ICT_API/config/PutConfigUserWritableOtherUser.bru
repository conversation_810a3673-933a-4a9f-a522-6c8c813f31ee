meta {
  name: PutConfigUserWritableOtherUser
  type: http
  seq: 7
}

put {
  url: {{api_host}}/config/user-writable
  body: json
  auth: inherit
}

body:json {
  {
    "name": "user-favorites",
    "group": "user-writable",
    "dataType": "json",
    "value": {
      "favorites": []
    },
    "levelToUpdate": "user",
    "userId": "other-user-id",
    "description": "User-specific settings"
  }
} 