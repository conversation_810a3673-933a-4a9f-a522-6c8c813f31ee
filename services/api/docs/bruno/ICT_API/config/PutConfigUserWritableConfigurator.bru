meta {
  name: PutConfigUserWritableConfigurator
  type: http
  seq: 8
}

put {
  url: {{api_host}}/config/user-writable
  body: json
  auth: inherit
}

body:json {
  {
    "name": "user-favorites",
    "group": "user-writable",
    "dataType": "json",
    "value": {
      "favorites": ["route-1", "route-2"]
    },
    "levelToUpdate": "user",
    "userId": "target-user-id",
    "description": "User-specific settings"
  }
}

script:pre-request {
  console.log('Request URL:', req.getUrl());
  console.log('Auth Token:', req.getHeader('Authorization'));
}

script:post-response {
  console.log('Response Status:', res.getStatus());
  console.log('Response Body:', res.getBody());
} 