meta {
  name: PutConfigSettings
  type: http
  seq: 4
}

put {
  url: {{api_host}}/config/settings
  body: json
  auth: inherit
}

body:json {
  {
    "id": "71ea5bdd-071b-45f4-9c0c-e4968eaa3c01",
    "name": "facility-maps",
    "group": "facility-maps",
    "description": "Array of Facility Map Definitions",
    "dataType": "json",
    "value": [
      {
        "id": "superior_uniform#eudoraar",
        "name": "Eudora, AR",
        "default": true,
        "dataset": "superior_uniform"
      }
    ],
    "levelToUpdate": "tenant"
  }
}
