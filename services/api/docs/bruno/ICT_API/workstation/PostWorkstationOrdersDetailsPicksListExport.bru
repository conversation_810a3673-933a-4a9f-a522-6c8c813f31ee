meta {
  name: PostWorkstationOrdersDetailsPicksListExport
  type: http
  seq: 17
}

post {
  url: {{api_host}}/workstation/orders/:orderId/picks/list/export?start_date=T&end_date=T
  body: json
  auth: none
}

params:query {
  start_date: T
  end_date: T
}

params:path {
  orderId: 0008168811
}

body:json {
  {
    "page": 0,
    "limit": 50,
    "columns": {
      "container": true,
      "status": true,
      "orderLine": true,
      "style": true,
      "size": true,
      "qtyOrdered": true,
      "qtyPicked": true,
      "containers": true
    }
  }
}
