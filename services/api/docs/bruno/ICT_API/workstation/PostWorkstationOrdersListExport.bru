meta {
  name: PostWorkstationOrdersListExport
  type: http
  seq: 14
}

post {
  url: {{api_host}}/workstation/orders/list/export?start_date=T&end_date=T
  body: json
  auth: none
}

params:query {
  start_date: T
  end_date: T
}

body:json {
  {
    "page": 0,
    "limit": 50,
    "sortFields": [
      {
        "columnName": "dwell_time",
        "isDescending": true
      }
    ],
    "columns": {
      "station": true,
      "position": true,
      "dwellTime": true,
      "orderStatus": true,
      "pickTask": true,
      "orderId": true,
      "container": true,
      "completedPicks": true,
      "arrivalTime": true
    }
  }
}
