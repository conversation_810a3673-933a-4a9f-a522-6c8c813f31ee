meta {
  name: PostWorkstationDailyPerformanceListExport
  type: http
  seq: 10
}

post {
  url: {{api_host}}/workstation/daily-performance/list/export?start_date=2025-08-04T00:00:00.000Z&end_date=2025-08-11T23:59:59.999Z
  body: json
  auth: inherit
}

params:query {
  start_date: 2025-08-04T00:00:00.000Z
  end_date: 2025-08-11T23:59:59.999Z
}

body:json {
  {
    "workstations": [],
    "filename": "Daily_Performance_Report",
    "columns": {
      "date": true,
      "totalLoggedInHours": true,
      "idlePercentage": true,
      "starvedPercentage": true,
      "starvedHours": true,
      "donorContainers": true,
      "gtpContainers": true,
      "linesPicked": true,
      "qtyPerLine": true,
      "pickLineQty": true,
      "linesPerHour": true,
      "avgLinesPickedPerHr1stShiftPercentage": true,
      "avgLinesPickedPerHr2ndShiftPercentage": true,
      "retrievalFromDMS": true,
      "storageToDMS": true,
      "retrievalFromASRS": true,
      "storageToASRS": true
    }
  }
}
