meta {
  name: PostWorkstationListExport
  type: http
  seq: 11
}

post {
  url: {{api_host}}/workstation/list/export
  body: json
  auth: inherit
}

body:json {
  {
    "columns": {
        "workstation": true,
        "status": true,
        "workMode": true,
        "workflowStatus": true,
        "operatorId": true,
        "activeTimeSecs": true,
        "starvedTimeSecs": true,
        "idleTimeSecs": true,
        "blockedTimeSecs": true,
        "quantityPerHour": true,
        "weightedQuantityPerHour": true,
        "linesPerHour": true,
        "weightedLinesPerHour": true,
        "donorTotesPerHour": true,
        "orderTotesPerHour": true
    }
  }
}
