// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/docker-existing-docker-compose
{
  "name": "Existing Docker Compose (Extend)",

  // Update the 'dockerComposeFile' list if you have more compose files or use different names.
  // The .devcontainer/docker-compose.yml file contains any overrides you need/want to make.
  "dockerComposeFile": ["../docker-compose.yml", "docker-compose.yml"],

  // The 'service' property is the name of the service for the container that VS Code should
  // use. Update this value and .devcontainer/docker-compose.yml to the real service name.
  "service": "api",

  // The optional 'workspaceFolder' property is the path VS Code should open by default when
  // connected. This is typically a file mount in .devcontainer/docker-compose.yml
  // "workspaceMount": "source=api,target=/workspace,type=volume",
  // "workspaceFolder": "/workspace",
  "mounts": [
    "source=node_modules,target=${containerWorkspaceFolder}/node_modules,type=volume",
  ],
  "workspaceFolder": "/workspaces/${localWorkspaceFolderBasename}",
  "features": {
    "ghcr.io/dhoeric/features/google-cloud-cli:1": {},
  },

  // Features to add to the dev container. More info: https://containers.dev/features.
  // "features": {},

  // Use 'forwardPorts' to make a list of ports inside the container available locally.
  // "forwardPorts": [],

  // Uncomment the next line if you want start specific services in your Docker Compose config.
  // "runServices": [],

  // Uncomment the next line if you want to keep your containers running after VS Code shuts down.
  // "shutdownAction": "none",

  // Uncomment the next line to run commands after the container is created.
  // "postCreateCommand": "cat /etc/os-release",

  // Configure tool-specific properties.
  // "customizations": {},

  // Uncomment to connect as an existing user other than the container default. More info: https://aka.ms/dev-containers-non-root.
  // "remoteUser": "devcontainer"
}
