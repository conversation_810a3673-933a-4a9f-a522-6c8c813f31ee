#!/bin/bash
source ./.env

ACTION=$1
TENANT_CONFIG_FILE="../../config/tenants/${2}.tenants.json"
DATASOURCE=$3

show_help() {
  echo "Usage: $0 <tf_config_file>"
  echo 
  echo "This script is used to create local tenants for development based on the tenants defined in a terraform config file."
  echo "This expects a postgres instance to be running locally and accessible on port 5432."
  echo 
  echo "Arguments:"
  echo "  <action>: Action to run [create-dbs/migrate]"
  echo "  <tf_config_file>: Terraform config file"
  echo 
  echo "Actions:"
  echo "  create-tenants: create a database for each tenant specified in the config file"
  echo "  migrate: migrate the database for each tenant specified in the config file"
  echo "  revert <tf_config_file> <datasource_file>: revert the specified datasource for each tenant specified in the config file"
  echo 
  echo "Examples:"
  echo "  $0 create-tenants ict-dev-us-east1" # Create tenants based on the tenants defined in the terraform config file for the ict-dev-us-east1 environment
  echo "  $0 migrate ict-dev-us-east1" # Migrate tenants based on the tenants defined in the terraform config file for the ict-dev-us-east1 environment
}

if [[ "$1" == "-h" ]] || [[ "$1" == "--help" ]] || [[ "$1" == "help" ]] || [[ "$#" == 0 ]]; then
  show_help
  exit 0
fi

if [[ -z "$ACTION" ]]; then
  echo "ACTION is not set"
  echo "Command for more info: $0 --help"
  exit 1
fi

if [[ -z "$2" ]]; then
  echo "Environment is not set"
  echo "Command for more info: $0 --help"
  exit 1
fi

# Check if tenant config file exists
if [[ ! -f "$TENANT_CONFIG_FILE" ]]; then
  echo "Tenant config file not found: $TENANT_CONFIG_FILE"
  echo "Available configs:"
  ls -la ../../config/tenants/*.tenants.json 2>/dev/null || echo "  No tenant config files found"
  exit 1
fi

# Check if jq is available
if ! command -v jq >/dev/null 2>&1; then
  echo "jq is required but not installed"
  echo "Install jq: https://jqlang.github.io/jq/download/"
  exit 1
fi

# Extract tenant names from JSON
IFS=' ' read -r -a TENANTS <<< $(jq -r '.[].name' "$TENANT_CONFIG_FILE" | tr '\n' ' ')

if [[ -z "$TENANTS" ]]; then
  echo "No tenants found in $TENANT_CONFIG_FILE"
  echo "-----------------------------------"
  echo "Please ensure $TENANT_CONFIG_FILE contains valid tenant definitions"
  echo "-----------------------------------"
  exit 1
fi

function tenantLoop(){
  for TENANT in "${TENANTS[@]}"; do
    $1 $TENANT
  done
}

function createTenantDatabase(){
  local TENANT=$1
  
  # Check if database already exists
  if PGPASSWORD=$POSTGRES_PASSWORD psql -h localhost -p 5432 -U $POSTGRES_USERNAME -lqt | cut -d \| -f 1 | grep -qw $TENANT; then
    echo "Database $TENANT already exists, skipping creation"
  else
    echo "Creating database for local tenant: $TENANT"
    PGPASSWORD=$POSTGRES_PASSWORD psql --quiet -h localhost -p 5432 -U $POSTGRES_USERNAME -c "CREATE DATABASE $TENANT;"
    echo "Finished creating database for local tenant: $TENANT"
  fi
}

function createTenantDatabases() {
  echo "Creating local tenants..."
  tenantLoop "createTenantDatabase"
  echo "Finished creating databases for all local tenants."
}

function checkMigrationStatus(){
  local TENANT=$1
  local DATASOURCE=$2
  local SCHEMA_NAME=$3
  
  # Check if migrations table exists and has entries
  local MIGRATION_COUNT=$(PGPASSWORD=$POSTGRES_PASSWORD psql -h localhost -p 5432 -d $TENANT -U $POSTGRES_USERNAME -tAc "
    SELECT CASE 
      WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = '$SCHEMA_NAME' AND table_name = 'migrations') 
      THEN (SELECT COUNT(*) FROM $SCHEMA_NAME.migrations)
      ELSE 0 
    END;
  " 2>/dev/null || echo "0")
  
  echo $MIGRATION_COUNT
}

function migrateTenant(){
  local TENANT=$1
  
  # config migrations
  echo "Checking config migrations for local tenant: $TENANT"
  PGPASSWORD=$POSTGRES_PASSWORD psql -h localhost -p 5432 -d $TENANT -U $POSTGRES_USERNAME -c "CREATE SCHEMA IF NOT EXISTS config;"
  
  local CONFIG_MIGRATION_COUNT=$(checkMigrationStatus $TENANT "./src/datasources/config/local-migration-config-datasource.ts" "config")
  if [ "$CONFIG_MIGRATION_COUNT" -eq "0" ]; then
    echo "Running config migrations for local tenant: $TENANT"
    CURRENT_TENANT=$TENANT yarn run -T typeorm-ts-node-esm migration:run --dataSource ./src/datasources/config/local-migration-config-datasource.ts
    echo "Finished config migrations for local tenant: $TENANT"
  else
    echo "Config migrations already applied for tenant: $TENANT (found $CONFIG_MIGRATION_COUNT migrations)"
  fi

  # data-explorer migrations
  echo "Checking data-explorer migrations for local tenant: $TENANT"
  PGPASSWORD=$POSTGRES_PASSWORD psql -h localhost -p 5432 -d $TENANT -U $POSTGRES_USERNAME -c "CREATE SCHEMA IF NOT EXISTS data_explorer;"
  
  local DE_MIGRATION_COUNT=$(checkMigrationStatus $TENANT "./src/datasources/data-explorer/local-migration-data-explorer-datasource.ts" "data_explorer")
  if [ "$DE_MIGRATION_COUNT" -eq "0" ]; then
    echo "Running data-explorer migrations for local tenant: $TENANT"
    CURRENT_TENANT=$TENANT yarn run -T typeorm-ts-node-esm migration:run --dataSource ./src/datasources/data-explorer/local-migration-data-explorer-datasource.ts
    echo "Finished data-explorer migrations for local tenant: $TENANT"
  else
    echo "Data-explorer migrations already applied for tenant: $TENANT (found $DE_MIGRATION_COUNT migrations)"
  fi

  # process-flow migrations
  echo "Checking process-flow migrations for local tenant: $TENANT"
  PGPASSWORD=$POSTGRES_PASSWORD psql -h localhost -p 5432 -d $TENANT -U $POSTGRES_USERNAME -c "CREATE SCHEMA IF NOT EXISTS process_flow;"
  
  local PF_MIGRATION_COUNT=$(checkMigrationStatus $TENANT "./src/datasources/process-flow/local-migration-process-flow-datasource.ts" "process_flow")
  if [ "$PF_MIGRATION_COUNT" -eq "0" ]; then
    echo "Running process-flow migrations for local tenant: $TENANT"
    CURRENT_TENANT=$TENANT yarn run -T typeorm-ts-node-esm migration:run --dataSource ./src/datasources/process-flow/local-migration-process-flow-datasource.ts
    echo "Finished process-flow migrations for local tenant: $TENANT"
  else
    echo "Process-flow migrations already applied for tenant: $TENANT (found $PF_MIGRATION_COUNT migrations)"
  fi
}

function migrate() {
  echo "Running migrations for local tenants..."
  cd ./libs/api/ict-api-schema/
  tenantLoop "migrateTenant"
  pwd
  echo "Finished running migrations for all local tenants."
}

function revertTenantMigration(){
  local TENANT=$1
  echo "Running config migration reverts for local tenant: $TENANT"
  CURRENT_TENANT=$TENANT yarn run -T typeorm-ts-node-esm migration:revert --dataSource $DATASOURCE
  echo "Finished config migrations for local tenant: $TENANT"
}

function revert() {
  if [[ -z "$DATASOURCE" ]]; then
    echo "DATASOURCE is not set"
    echo "Command for more info: $0 --help"
    exit 1
  fi

  echo "Running migration reverts for local tenants for datasource $DATASOURCE..."
  cd ./libs/api/ict-api-schema/
  tenantLoop "revertTenantMigration"
  pwd
  echo "Finished running migrations for all local tenants for datasource $DATASOURCE."
}

function syncSettingsForTenantDB() {
  local TENANT=$1

  SQL_FILE="./src/default-data/config/sql/insert-default-settings.sql"
  echo "Synchronizing settings for local tenant: $TENANT using file $SQL_FILE"
  PGPASSWORD=$POSTGRES_PASSWORD psql -h localhost -p 5432 -d $TENANT -U $POSTGRES_USERNAME -f "$SQL_FILE"
  echo "Finished settings sync for local tenant: $TENANT"
}

function syncDefaultSettings() {
  echo "Synchronizing settings for local tenants..."
  cd ./libs/api/ict-api-schema/
  tenantLoop "syncSettingsForTenantDB"
  pwd
  echo "Finished sync of settings for all local tenants."
}

function syncMetricConfigsForTenantDB() {
  local TENANT=$1

  SQL_FILE="./src/default-data/process-flow/sql/insert-default-metric-configs.sql"
  echo "Synchronizing metric configs for local tenant: $TENANT using file $SQL_FILE"
  PGPASSWORD=$POSTGRES_PASSWORD psql -h localhost -p 5432 -d $TENANT -U $POSTGRES_USERNAME -f "$SQL_FILE"
  echo "Finished metric configs sync for local tenant: $TENANT"
}

function syncDefaultMetricConfigs() {
  echo "Synchronizing metric configs for local tenants..."
  cd ./libs/api/ict-api-schema/
  tenantLoop "syncMetricConfigsForTenantDB"
  pwd
  echo "Finished sync of metric configs for all local tenants."
}

function syncTenantConfigsForTenantDB() {
  local TENANT=$1
  local SQL_FILE="./src/default-data/config/sql/tenant-configs/${TENANT}-config-overrides.sql"
  
  if [[ -f "$SQL_FILE" ]]; then
    echo "Synchronizing tenant-specific configs for local tenant: $TENANT using file $SQL_FILE"
    PGPASSWORD=$POSTGRES_PASSWORD psql -h localhost -p 5432 -d $TENANT -U $POSTGRES_USERNAME -f "$SQL_FILE"
    echo "Finished tenant configs sync for local tenant: $TENANT"
  else
    echo "No tenant-specific config file found for: $TENANT (this is normal if no overrides are defined)"
  fi
}

function syncTenantConfigs() {
  echo "Synchronizing tenant-specific configs for local tenants..."
  cd ./libs/api/ict-api-schema/
  tenantLoop "syncTenantConfigsForTenantDB"
  pwd
  echo "Finished sync of tenant configs for all local tenants."
}

case $ACTION in
  create-tenants)
    createTenantDatabases
    ;;
  migrate)
    migrate
    ;;
  revert)
    revert
    ;;
  seed)
    syncDefaultSettings
    syncDefaultMetricConfigs
    syncTenantConfigs
    ;;
  *)
    echo "Invalid action: $ACTION"
    echo "-----------------------------------"
    show_help
    exit 1
    ;;
esac
