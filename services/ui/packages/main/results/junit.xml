<?xml version="1.0"?>
<testsuite name="Chrome Headless 130.0.0.0 (Linux x86_64)" package="control-tower-ui" timestamp="2025-03-11T18:35:35" id="0" hostname="NBIGB25NYFY8" tests="1164" errors="0" failures="0" time="10.134">
  <properties>
    <property name="browser.fullName" value="Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/130.0.0.0 Safari/537.36"/>
  </properties>
  <testcase name="DataExplorerApi baseUrl should return a defined endpoint" time="0" classname=".DataExplorerApi"/>
  <testcase name="DataExplorerApi config should return a defined endpoint" time="0" classname=".DataExplorerApi"/>
  <testcase name="DataExplorerApi get_data_explorer_search should return a defined endpoint" time="0" classname=".DataExplorerApi"/>
  <testcase name="DataExplorerApi get_data_explorer_results should return a defined endpoint" time="0" classname=".DataExplorerApi"/>
  <testcase name="DataExplorerApi get_data_explorer_results_id should return a defined endpoint" time="0" classname=".DataExplorerApi"/>
  <testcase name="DataExplorerApi put_data_explorer_results should return a defined endpoint" time="0" classname=".DataExplorerApi"/>
  <testcase name="DataExplorerApi delete_data_explorer_results_id should return a defined endpoint" time="0" classname=".DataExplorerApi"/>
  <testcase name="DataExplorerApi get_data_explorer_suggested_recommendations should return a defined endpoint" time="0" classname=".DataExplorerApi"/>
  <testcase name="EquipmentApi baseUrl should return a defined endpoint" time="0" classname=".EquipmentApi"/>
  <testcase name="EquipmentApi config should return a defined endpoint" time="0" classname=".EquipmentApi"/>
  <testcase name="EquipmentApi get_equipment_faults_details should return a defined endpoint" time="0" classname=".EquipmentApi"/>
  <testcase name="EquipmentApi get_equipment_events_aislenodeevents should return a defined endpoint" time="0" classname=".EquipmentApi"/>
  <testcase name="InventoryApi baseUrl should return a defined endpoint" time="0" classname=".InventoryApi"/>
  <testcase name="InventoryApi config should return a defined endpoint" time="0" classname=".InventoryApi"/>
  <testcase name="InventoryApi get_inventory_category_filter should return a defined endpoint" time="0" classname=".InventoryApi"/>
  <testcase name="TableauProxyApi baseUrl should return a defined endpoint" time="0" classname=".TableauProxyApi"/>
  <testcase name="TableauProxyApi config should return a defined endpoint" time="0" classname=".TableauProxyApi"/>
  <testcase name="TableauProxyApi get_tableau_proxy should return a defined endpoint" time="0" classname=".TableauProxyApi"/>
  <testcase name="APIClient axios config tests axios was called with correct arguments - GET" time="0" classname=".APIClient axios config tests"/>
  <testcase name="APIClient axios config tests request config can be overridden - GET" time="0.001" classname=".APIClient axios config tests"/>
  <testcase name="APIClient axios config tests axios was called with correct arguments - PUT" time="0" classname=".APIClient axios config tests"/>
  <testcase name="APIClient axios config tests request config can be overridden - PUT" time="0" classname=".APIClient axios config tests"/>
  <testcase name="APIClient axios config tests axios was called with correct arguments - POST" time="0" classname=".APIClient axios config tests"/>
  <testcase name="APIClient axios config tests request config can be overridden - POST" time="0" classname=".APIClient axios config tests"/>
  <testcase name="APIClient axios config tests axios was called with correct arguments - DELETE" time="0" classname=".APIClient axios config tests"/>
  <testcase name="APIClient axios config tests request config can be overridden - DELETE" time="0" classname=".APIClient axios config tests"/>
  <testcase name="APIClient API client axios interceptor tests if a startTime exists, duration is calculated" time="0.001" classname=".APIClient API client axios interceptor tests"/>
  <testcase name="APIClient API client axios interceptor tests if a startTime doesnt exist, duration is not calculated" time="0" classname=".APIClient API client axios interceptor tests"/>
  <testcase name="APIClient API client axios interceptor tests request config has valid start time" time="0.001" classname=".APIClient API client axios interceptor tests"/>
  <testcase name="APIClient API client axios interceptor tests promise rejected if HTTP error" time="0.001" classname=".APIClient API client axios interceptor tests"/>
  <testcase name="ApiEndpoint can create a valid ApiEndpoint" time="0" classname=".ApiEndpoint"/>
  <testcase name="ApiEndpoint will return base url when endpoint def is invalid." time="0.001" classname=".ApiEndpoint"/>
  <testcase name="ApiEndpoint can add to the path from def with no base url" time="0" classname=".ApiEndpoint"/>
  <testcase name="ApiEndpoint can add to the path from def with base url" time="0" classname=".ApiEndpoint"/>
  <testcase name="ApiEndpoint can update a variable in the path" time="0" classname=".ApiEndpoint"/>
  <testcase name="ApiEndpoint can update a variable in the path with base url defined" time="0" classname=".ApiEndpoint"/>
  <testcase name="ApiEndpoint can resolve path with no ending slash and baseUrl with no ending slash and add path has beginning path slash." time="0" classname=".ApiEndpoint"/>
  <testcase name="ApiEndpoint can resolve path with ending slash and baseUrl with no ending slash and add path has beginning path slash." time="0" classname=".ApiEndpoint"/>
  <testcase name="ApiEndpoint can resolve path with ending slash and baseUrl with ending slash and add path has beginning path slash." time="0" classname=".ApiEndpoint"/>
  <testcase name="ApiEndpoint can resolve path with ending slash and baseUrl with ending slash and add path has no beginning path slash." time="0" classname=".ApiEndpoint"/>
  <testcase name="IctOpenApiClientWrapper can create a new SDK client." time="0.008" classname=".IctOpenApiClientWrapper"/>
  <testcase name="IctOpenApiClientWrapper can create a new SDK client with config overrides" time="0.007" classname=".IctOpenApiClientWrapper"/>
  <testcase name="IctOpenApiClientWrapper can transform an OperationMethod." time="0.008" classname=".IctOpenApiClientWrapper"/>
  <testcase name="DefaultPackages should have 2 default menu groups" time="0.002" classname=".DefaultPackages"/>
  <testcase name="MenuProvider filterMenuBasedOnFeatureFlags Menu item is kept when there is no feature flag property defined" time="0" classname=".MenuProvider filterMenuBasedOnFeatureFlags"/>
  <testcase name="MenuProvider filterMenuBasedOnFeatureFlags Menu group is kept when there is at least one child menu item whose feature is enabled" time="0" classname=".MenuProvider filterMenuBasedOnFeatureFlags"/>
  <testcase name="MenuProvider filterMenuBasedOnFeatureFlags Menu item is removed when there is a feature flag property defined and is not enabled" time="0" classname=".MenuProvider filterMenuBasedOnFeatureFlags"/>
  <testcase name="MenuProvider filterMenuBasedOnFeatureFlags Menu group is removed when all children have feature flags that arent enabled" time="0" classname=".MenuProvider filterMenuBasedOnFeatureFlags"/>
  <testcase name="ConfigSettingsClient getFeatureFlags returns a list of relevant settings" time="0" classname=".ConfigSettingsClient getFeatureFlags"/>
  <testcase name="ConfigSettingsClient getFeatureFlags returns undefined when an error is thrown" time="0" classname=".ConfigSettingsClient getFeatureFlags"/>
  <testcase name="ConfigSettingsClient updateSetting returns an updated setting on successful update" time="0" classname=".ConfigSettingsClient updateSetting"/>
  <testcase name="ConfigSettingsClient updateSetting returns undefined when an error is thrown" time="0" classname=".ConfigSettingsClient updateSetting"/>
  <testcase name="ConfigSettingsClient deleteSetting returns true when a setting is deleted" time="0" classname=".ConfigSettingsClient deleteSetting"/>
  <testcase name="ConfigSettingsClient deleteSetting returns false when a setting is not deleted" time="0" classname=".ConfigSettingsClient deleteSetting"/>
  <testcase name="ConfigSettingsClient deleteSetting returns undefined when an error is thrown" time="0" classname=".ConfigSettingsClient deleteSetting"/>
  <testcase name="feature flags feature is enabled if its value is true, not enabled if false" time="0.001" classname=".feature flags"/>
  <testcase name="feature flags feature is not enabled if it doesnt exist" time="0" classname=".feature flags"/>
  <testcase name="feature flags correct feature flag is found by ID" time="0" classname=".feature flags"/>
  <testcase name="feature flags refresh all calls getFeatureFlags API" time="0" classname=".feature flags"/>
  <testcase name="User hasRole hasRole should return true when logged in user has the requested role" time="0" classname=".User hasRole"/>
  <testcase name="User hasRole hasRole should return false when logged in user does not have any roles" time="0.001" classname=".User hasRole"/>
  <testcase name="User hasRole hasRole should return false when logged in user does not have the matching roles" time="0" classname=".User hasRole"/>
  <testcase name="User hasRole hasRole should return true when the roles to match are empty" time="0" classname=".User hasRole"/>
  <testcase name="User getUser User info populates correctly when token has correct data" time="0.002" classname=".User getUser"/>
  <testcase name="benchmark-gauge should display current value label" time="0.003" classname=".benchmark-gauge"/>
  <testcase name="benchmark-gauge current value is set properly" time="0" classname=".benchmark-gauge"/>
  <testcase name="DguMetaDashboardCategoryGrid should be registered" time="0" classname=".DguMetaDashboardCategoryGrid"/>
  <testcase name="DguMetaDashboardCategoryGrid can be rendered." time="0.001" classname=".DguMetaDashboardCategoryGrid"/>
  <testcase name="DguStatistic should be registered" time="0" classname=".DguStatistic"/>
  <testcase name="DguStatistic should render correct title" time="0.001" classname=".DguStatistic"/>
  <testcase name="DguStatistic should render correct data value" time="0" classname=".DguStatistic"/>
  <testcase name="ProgressGauge progress gauge renders with multiple values" time="0.038" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with multiple values" time="0.006" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with multiple values" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with multiple values" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with multiple values" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with multiple heights" time="0.001" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with multiple heights" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with multiple heights" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with multiple heights" time="0.004" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with multiple heights" time="0.004" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with multiple widths" time="0.003" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with multiple widths" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with multiple widths" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with multiple widths" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with multiple widths" time="0.003" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with multiple radius settings" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with multiple radius settings" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with multiple radius settings" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with multiple radius settings" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with multiple radius settings" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders at multiple bar color settings" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders at multiple bar color settings" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders at multiple bar color settings" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders at multiple bar color settings" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders the correct color #c84031 with value of 0" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders the correct color #c84031 with value of 20" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders the correct color #dd8a31 with value of 40" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders the correct color #f2bd42 with value of 55" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders the correct color #58a45c with value of 75" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders the correct color #58a45c with value of 100" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders the correct color #58a45c with value of 0 when reversed" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders the correct color #58a45c with value of 20 when reversed" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders the correct color #f2bd42 with value of 40 when reversed" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders the correct color #dd8a31 with value of 55 when reversed" time="0.001" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders the correct color #c84031 with value of 75 when reversed" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders the correct color #c84031 with value of 100 when reversed" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders the correct color #c84031 when the&#xA;          segments are 15, 40, and 70" time="0.003" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders the correct color #c84031 when the&#xA;          segments are 35, 40, and 70" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders the correct color #dd8a31 when the&#xA;          segments are 10, 25, and 70" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders the correct color #f2bd42 when the&#xA;          segments are 15, 40, and 90" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders the correct color #58a45c when the&#xA;          segments are 15, 40, and 70" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders the correct color #58a45c when the segments are 15, 40, and 70 and reversed" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders the correct color #58a45c when the segments are 35, 40, and 70 and reversed" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders the correct color #f2bd42 when the segments are 10, 25, and 70 and reversed" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders the correct color #dd8a31 when the segments are 15, 40, and 90 and reversed" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders the correct color #c84031 when the segments are 15, 40, and 70 and reversed" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders ticks depending on the setting true" time="0.003" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders ticks depending on the setting false" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders labels depending on the setting true" time="0.005" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders labels depending on the setting false" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with min 10 and max 30" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with min 50 and max 130" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with min -20 and max 40" time="0.001" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with min -30 and max 120" time="0.002" classname=".ProgressGauge"/>
  <testcase name="ProgressGauge progress gauge renders with min 7.5 and max 50.3" time="0.001" classname=".ProgressGauge"/>
  <testcase name="IctSeriesDataChart renders with correct title" time="0.002" classname=".IctSeriesDataChart"/>
  <testcase name="IctSeriesDataChart renders with correct seriesData" time="0.002" classname=".IctSeriesDataChart"/>
  <testcase name="IctSeriesDataChart renders with correct title position" time="0.001" classname=".IctSeriesDataChart"/>
  <testcase name="IctSeriesDataChart renders with correct y-axis position" time="0.001" classname=".IctSeriesDataChart"/>
  <testcase name="IctSeriesDataChart renders with correct y-axis position" time="0.001" classname=".IctSeriesDataChart"/>
  <testcase name="IctSeriesDataChart renders with correct grid position" time="0.001" classname=".IctSeriesDataChart"/>
  <testcase name="IctSeriesDataChart renders with correct legend position" time="0.001" classname=".IctSeriesDataChart"/>
  <testcase name="IctGridstackDashboard should initialize Gridstack on firstUpdated" time="0" classname=".IctGridstackDashboard"/>
  <testcase name="IctGridstackDashboard should remove widget successfully" time="0" classname=".IctGridstackDashboard"/>
  <testcase name="IctGridstackDashboard should call grid save when grid changed" time="0" classname=".IctGridstackDashboard"/>
  <testcase name="IctGridstackDashboard should call grid remove and load when loading dashboard" time="0.001" classname=".IctGridstackDashboard"/>
  <testcase name="IctGridstackDashboard should dispatch GridstackChangeEvent on change" time="0" classname=".IctGridstackDashboard"/>
  <testcase name="IctGridstackDashboard static mode and edit mode should by in sync" time="0.001" classname=".IctGridstackDashboard"/>
  <testcase name="IctExportAsExcelButton should render correctly" time="0.001" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should handle populateColumnMap with empty columnOrder" time="0.001" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should update the exportColumnsSelection when handleExportColumnSelection is triggered" time="0.001" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should not throw error when handleExportSelectAll is called with no columns" time="0.001" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should select all columns when handleExportSelectAll is called" time="0" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should deselect all columns when handleExportDeselectAll is called" time="0.001" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should update helper text based on the entered file name" time="0.001" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should update helperText with default file name if fileName is empty" time="0.001" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should initialize fileName with default format when no prefix is provided" time="0" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should validate export inputs correctly" time="0.001" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should update fileName and helperText when handleFileNamechange is called" time="0.001" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should handle extractFilters with missing filter details gracefully" time="0.001" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should extract filters correctly from filters property" time="0.001" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should initialize fileName with fileNamePrefix" time="0" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should correctly update exportColumnsSelection when toggling columns" time="0" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should correctly initialize columnMap and exportColumnsSelection in populateColumnMap" time="0" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should handle exportColumnsSelection with columnOrder fallback in populateColumnMap" time="0.001" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should correctly evaluate feature flag for isFeatureDisabled" time="0.001" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should return true for isFeatureDisabled when featureFlag is unset" time="0.001" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should correctly open the dialog and populate appliedFilters" time="0.001" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should correctly disable export button if inputs are invalid" time="0.001" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should initialize columnMap when updated with columnOrder" time="0" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should disable export button when no valid inputs are provided" time="0.001" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should call dialogController.show when handleOpenExportDialog is triggered" time="0" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should not render the export button when the feature flag is disabled" time="0.001" classname=".IctExportAsExcelButton"/>
  <testcase name="IctExportAsExcelButton should render the export button when the feature flag is enabled" time="0.002" classname=".IctExportAsExcelButton"/>
  <testcase name="&lt;ict-filter-table> ict-filter-table should be registered" time="0" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> ict-filter-table is available" time="0" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> should render with filtering enabled or disabled" time="0.04" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> should render with filtering enabled or disabled" time="0.009" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> should render with filters initially open or closed" time="0.01" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> should render with filters initially open or closed" time="0.01" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> should render with reset button enabled or disabled" time="0.011" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> should render with reset button enabled or disabled" time="0.009" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> should render with chips button enabled or disabled" time="0.01" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> should render with chips button enabled or disabled" time="0.01" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> should have the refresh button enabled" time="0.006" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> should have the filters enabled" time="0.007" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> should have the filters initially open on render" time="0.005" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> should have the refresh button not highlighted on render" time="0.005" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> should have the chips button not disabled on render" time="0.006" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> should have multi-sort disabled on render" time="0.006" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> should have refresh button available" time="0.009" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> should have chips button available" time="0.009" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> should have sort chips group container available" time="0.009" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> should have filter chips group container available" time="0.014" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> should display pagination if pagination is set to true" time="0.016" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> should not display pagination if pagination is set to false" time="0.01" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> should toggle horizontal alignment based on toolbarAlignment property" time="0.009" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> should toggle view mode between table and card" time="0.014" classname=".&lt;ict-filter-table>"/>
  <testcase name="&lt;ict-filter-table> filtering and sorting of ict-filter-table sorts are built correctly with single sort" time="0.012" classname=".&lt;ict-filter-table> filtering and sorting of ict-filter-table"/>
  <testcase name="&lt;ict-filter-table> filtering and sorting of ict-filter-table sorts are built correctly with multiple sorts" time="0.01" classname=".&lt;ict-filter-table> filtering and sorting of ict-filter-table"/>
  <testcase name="&lt;ict-filter-table> filtering and sorting of ict-filter-table filters are built correctly with single filter" time="0.011" classname=".&lt;ict-filter-table> filtering and sorting of ict-filter-table"/>
  <testcase name="&lt;ict-filter-table> filtering and sorting of ict-filter-table filters are built correctly with multiple filters" time="0.011" classname=".&lt;ict-filter-table> filtering and sorting of ict-filter-table"/>
  <testcase name="&lt;ict-filter-table> filtering and sorting of ict-filter-table sorts and filters are built correctly with multiple sorts and filters" time="0.011" classname=".&lt;ict-filter-table> filtering and sorting of ict-filter-table"/>
  <testcase name="IctKeyValuePairList should be registered as a custom element" time="0" classname=".IctKeyValuePairList"/>
  <testcase name="IctKeyValuePairList should render correct key-value pairs" time="0.001" classname=".IctKeyValuePairList"/>
  <testcase name="IctKeyValuePairList should render note under the key when notePosition is &quot;underKey&quot;" time="0" classname=".IctKeyValuePairList"/>
  <testcase name="IctKeyValuePairList should render note under the value when notePosition is &quot;underValue&quot;" time="0.001" classname=".IctKeyValuePairList"/>
  <testcase name="&lt;ict-loading-shimmer> ict-loading-shimmer should be registered" time="0" classname=".&lt;ict-loading-shimmer>"/>
  <testcase name="&lt;ict-loading-shimmer> ict-loading-shimmer is available" time="0" classname=".&lt;ict-loading-shimmer>"/>
  <testcase name="&lt;ict-loading-shimmer> ict-loading-shimmer renders for a table type" time="0.001" classname=".&lt;ict-loading-shimmer>"/>
  <testcase name="&lt;ict-loading-shimmer> ict-loading-shimmer renders for a row type" time="0" classname=".&lt;ict-loading-shimmer>"/>
  <testcase name="&lt;ict-loading-shimmer> ict-loading-shimmer renders for a card type" time="0.002" classname=".&lt;ict-loading-shimmer>"/>
  <testcase name="&lt;ict-loading-shimmer> ict-loading-shimmer renders defaults to a row type" time="0.001" classname=".&lt;ict-loading-shimmer>"/>
  <testcase name="ict-filter-selector is available" time="0" classname=".ict-filter-selector"/>
  <testcase name="ict-filter-selector should render with no filters provided." time="0.001" classname=".ict-filter-selector"/>
  <testcase name="ict-filter-selector should render DguComboBox with single select filter." time="0.004" classname=".ict-filter-selector"/>
  <testcase name="ict-filter-selector should render DguComboBoxMultiSelect with multi select filter." time="0.002" classname=".ict-filter-selector"/>
  <testcase name="ict-filter-selector should throw error for an invalid filter type." time="0" classname=".ict-filter-selector"/>
  <testcase name="ict-filter-header is available" time="0" classname=".ict-filter-header"/>
  <testcase name="ict-filter-header should render." time="0.001" classname=".ict-filter-header"/>
  <testcase name="ict-filter-header should render single select filter" time="0.003" classname=".ict-filter-header"/>
  <testcase name="ict-filter-selected-group is available" time="0" classname=".ict-filter-selected-group"/>
  <testcase name="ict-filter-selected-group should render with no filters provided." time="0.001" classname=".ict-filter-selected-group"/>
  <testcase name="ict-filter-selected-group should render with no single filter group provided." time="0" classname=".ict-filter-selected-group"/>
  <testcase name="ict-filter-selected-group should render with no multi filter group provided." time="0.001" classname=".ict-filter-selected-group"/>
  <testcase name="ict-filters-selection is available" time="0" classname=".ict-filters-selection"/>
  <testcase name="ict-filters-selection can be created" time="0" classname=".ict-filters-selection"/>
  <testcase name="ict-filters-selection render can be called." time="0" classname=".ict-filters-selection"/>
  <testcase name="ict-filters-selection connectedCallback can be called." time="0" classname=".ict-filters-selection"/>
  <testcase name="ict-filters-selection can be loaded with no filters" time="0.008" classname=".ict-filters-selection"/>
  <testcase name="ict-filters-selection can be loaded with filters" time="0.032" classname=".ict-filters-selection"/>
  <testcase name="ict-page-filters is available" time="0" classname=".ict-page-filters"/>
  <testcase name="ict-page-filters filters property should be undefined if none are given." time="0.001" classname=".ict-page-filters"/>
  <testcase name="ict-page-filters filters property should be defined when defined." time="0.009" classname=".ict-page-filters"/>
  <testcase name="ict-page-filters can be created." time="0" classname=".ict-page-filters"/>
  <testcase name="ict-page-filters render can be called." time="0" classname=".ict-page-filters"/>
  <testcase name="ict-page-filters connectedCallback can be called." time="0.001" classname=".ict-page-filters"/>
  <testcase name="ict-page-filters should be able to init filters." time="0" classname=".ict-page-filters"/>
  <testcase name="ict-page-filters should be able to clear filters." time="0" classname=".ict-page-filters"/>
  <testcase name="ict-page-filters should be able to return applied filters." time="0" classname=".ict-page-filters"/>
  <testcase name="ict-page-filters should be able to delete applied filters." time="0" classname=".ict-page-filters"/>
  <testcase name="ict-page-filters should be able to update filters multiple times." time="0" classname=".ict-page-filters"/>
  <testcase name="SeriesTypeBarComposer can compose Series Data" time="0" classname=".SeriesTypeBarComposer"/>
  <testcase name="SeriesTypeComposerFactory can build a line composer" time="0.001" classname=".SeriesTypeComposerFactory"/>
  <testcase name="SeriesTypeComposerFactory can build a bar composer" time="0" classname=".SeriesTypeComposerFactory"/>
  <testcase name="SeriesTypeComposerFactory will throw error for unsupported series type." time="0" classname=".SeriesTypeComposerFactory"/>
  <testcase name="SeriesTypeLineComposer can compose Series Data" time="0" classname=".SeriesTypeLineComposer"/>
  <testcase name="IctSeriesChart ict-series-chart should be registered" time="0" classname=".IctSeriesChart"/>
  <testcase name="IctSeriesChart ict-series-chart should be registered" time="0.004" classname=".IctSeriesChart"/>
  <testcase name="OrdersCompletionController getAggregateCompletionData should request a host update" time="0.001" classname=".OrdersCompletionController getAggregateCompletionData"/>
  <testcase name="OrdersCompletionController getAggregateCompletionData should handle errors calling the api" time="0.001" classname=".OrdersCompletionController getAggregateCompletionData"/>
  <testcase name="OrdersCustomerCompletionController should set estimatedCompletionMetricData to the expected format" time="0.001" classname=".OrdersCustomerCompletionController"/>
  <testcase name="OrdersCustomerCompletionController should request a host update" time="0" classname=".OrdersCustomerCompletionController"/>
  <testcase name="OrdersCustomerCompletionController should handle errors calling the api" time="0" classname=".OrdersCustomerCompletionController"/>
  <testcase name="&lt;ict-range-indicator> should be registered" time="0" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple lowrange values" time="0.002" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple lowrange values" time="0.001" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple lowrange values" time="0.001" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple lowrange values" time="0.001" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple lowrange values" time="0" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple highrange values" time="0" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple highrange values" time="0.001" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple highrange values" time="0.001" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple highrange values" time="0.001" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple highrange values" time="0.001" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple curvalue values" time="0" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple curvalue values" time="0" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple curvalue values" time="0" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple curvalue values" time="0" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple curvalue values" time="0" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple min values" time="0" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple min values" time="0.001" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple min values" time="0" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple min values" time="0.001" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple min values" time="0.001" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple max values" time="0.001" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple max values" time="0" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple max values" time="0.001" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple max values" time="0.001" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple max values" time="0.001" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple label values" time="0.001" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple label values" time="0.001" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple helptext values" time="0.001" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple helptext values" time="0.001" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple units values" time="0.001" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple units values" time="0" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with multiple rangecolor values" time="0.001" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render with correct tooltipText value" time="0.001" classname=".&lt;ict-range-indicator>"/>
  <testcase name="&lt;ict-range-indicator> should render all styles correctly" time="0.028" classname=".&lt;ict-range-indicator>"/>
  <testcase name="TableauDashboard width is being set as expected" time="0.003" classname=".TableauDashboard"/>
  <testcase name="TableauDashboard width is being set as expected" time="0.001" classname=".TableauDashboard"/>
  <testcase name="TableauDashboard width is being set as expected" time="0" classname=".TableauDashboard"/>
  <testcase name="TableauDashboard width is being set as expected" time="0.001" classname=".TableauDashboard"/>
  <testcase name="TableauDashboard width is being set as expected" time="0.001" classname=".TableauDashboard"/>
  <testcase name="TableauDashboard height is being set as expected" time="0.001" classname=".TableauDashboard"/>
  <testcase name="TableauDashboard height is being set as expected" time="0.001" classname=".TableauDashboard"/>
  <testcase name="TableauDashboard height is being set as expected" time="0" classname=".TableauDashboard"/>
  <testcase name="TableauDashboard height is being set as expected" time="0" classname=".TableauDashboard"/>
  <testcase name="TableauDashboard height is being set as expected" time="0" classname=".TableauDashboard"/>
  <testcase name="TableauDashboard url is being set as expected" time="0.001" classname=".TableauDashboard"/>
  <testcase name="TableauDashboard url is being set as expected" time="0.001" classname=".TableauDashboard"/>
  <testcase name="TableauDashboard url is being set as expected" time="0.001" classname=".TableauDashboard"/>
  <testcase name="TableauDashboard url is being set as expected" time="0.001" classname=".TableauDashboard"/>
  <testcase name="TableauDashboard url is being set as expected" time="0.001" classname=".TableauDashboard"/>
  <testcase name="TimeViewerController getFacilityTime() returns expected data type." time="0.001" classname=".TimeViewerController"/>
  <testcase name="TimeViewer day [5] is being displayed as expected" time="0.012" classname=".TimeViewer"/>
  <testcase name="TimeViewer day [30] is being displayed as expected" time="0.003" classname=".TimeViewer"/>
  <testcase name="TimeViewer time is being displayed as expected in 24hr format past 12" time="0.002" classname=".TimeViewer"/>
  <testcase name="TimeViewer time is being displayed as expected in 24hr format before 12" time="0.002" classname=".TimeViewer"/>
  <testcase name="TimeViewer time zone [America/New_York] is displayed as expected" time="0.002" classname=".TimeViewer"/>
  <testcase name="TimeViewer time zone [America/Los_Angeles] is displayed as expected" time="0.001" classname=".TimeViewer"/>
  <testcase name="TimeViewer month [Jan] is being displayed as expected" time="0.001" classname=".TimeViewer"/>
  <testcase name="TimeViewer month [Feb] is being displayed as expected" time="0.001" classname=".TimeViewer"/>
  <testcase name="TimeViewer month [Mar] is being displayed as expected" time="0.001" classname=".TimeViewer"/>
  <testcase name="TimeViewer month [Apr] is being displayed as expected" time="0.001" classname=".TimeViewer"/>
  <testcase name="TimeViewer month [May] is being displayed as expected" time="0.001" classname=".TimeViewer"/>
  <testcase name="TimeViewer month [Jun] is being displayed as expected" time="0.001" classname=".TimeViewer"/>
  <testcase name="TimeViewer month [Jul] is being displayed as expected" time="0.001" classname=".TimeViewer"/>
  <testcase name="TimeViewer month [Aug] is being displayed as expected" time="0.001" classname=".TimeViewer"/>
  <testcase name="TimeViewer month [Sep] is being displayed as expected" time="0.001" classname=".TimeViewer"/>
  <testcase name="TimeViewer month [Oct] is being displayed as expected" time="0.001" classname=".TimeViewer"/>
  <testcase name="TimeViewer month [Nov] is being displayed as expected" time="0.002" classname=".TimeViewer"/>
  <testcase name="TimeViewer month [Dec] is being displayed as expected" time="0.001" classname=".TimeViewer"/>
  <testcase name="TimeViewer year [1900] is being displayed as expected" time="0.001" classname=".TimeViewer"/>
  <testcase name="TimeViewer year [1990] is being displayed as expected" time="0.002" classname=".TimeViewer"/>
  <testcase name="TimeViewer year [2000] is being displayed as expected" time="0.001" classname=".TimeViewer"/>
  <testcase name="TimeViewer year [2225] is being displayed as expected" time="0.001" classname=".TimeViewer"/>
  <testcase name="TimeViewer year [2026] is being displayed as expected" time="0.001" classname=".TimeViewer"/>
  <testcase name="TimeViewer year [2023] is being displayed as expected" time="0.001" classname=".TimeViewer"/>
  <testcase name="&lt;ict-tooltip> should be registered" time="0" classname=".&lt;ict-tooltip>"/>
  <testcase name="&lt;ict-tooltip> should render with multiple text values" time="0" classname=".&lt;ict-tooltip>"/>
  <testcase name="&lt;ict-tooltip> should render with multiple text values" time="0" classname=".&lt;ict-tooltip>"/>
  <testcase name="&lt;ict-tooltip> should render with multiple text values" time="0" classname=".&lt;ict-tooltip>"/>
  <testcase name="&lt;ict-tooltip> should render with multiple position values" time="0.001" classname=".&lt;ict-tooltip>"/>
  <testcase name="&lt;ict-tooltip> should render with multiple position values" time="0.001" classname=".&lt;ict-tooltip>"/>
  <testcase name="&lt;ict-tooltip> should render with multiple position values" time="0" classname=".&lt;ict-tooltip>"/>
  <testcase name="&lt;ict-tooltip> should render with multiple position values" time="0.001" classname=".&lt;ict-tooltip>"/>
  <testcase name="&lt;ict-view-list-filter> should be registered" time="0" classname=".&lt;ict-view-list-filter>"/>
  <testcase name="&lt;ict-view-list-filter> should render with proper caption" time="0.004" classname=".&lt;ict-view-list-filter>"/>
  <testcase name="&lt;ict-view-list-filter> should render with correct items" time="0.003" classname=".&lt;ict-view-list-filter>"/>
  <testcase name="&lt;ict-view-list-filter> should render with correct selected Item" time="0.002" classname=".&lt;ict-view-list-filter>"/>
  <testcase name="&lt;ict-view-list-filter> the first item in items is the default value when query param is supplied" time="0.002" classname=".&lt;ict-view-list-filter>"/>
  <testcase name="IctOpenApiClientController successful response tests should return a successful response." time="0.001" classname=".IctOpenApiClientController successful response tests"/>
  <testcase name="IctOpenApiClientController successful response tests should return expected data value" time="0" classname=".IctOpenApiClientController successful response tests"/>
  <testcase name="IctOpenApiClientController successful response tests should return expected status value" time="0" classname=".IctOpenApiClientController successful response tests"/>
  <testcase name="IctOpenApiClientController successful response tests should invoke method to add metadata to host" time="0" classname=".IctOpenApiClientController successful response tests"/>
  <testcase name="IctOpenApiClientController un-successful response tests should return an unsuccessful response." time="0.001" classname=".IctOpenApiClientController un-successful response tests"/>
  <testcase name="IctOpenApiClientController un-successful response tests should return expected data value" time="0.001" classname=".IctOpenApiClientController un-successful response tests"/>
  <testcase name="IctOpenApiClientController un-successful response tests should return expected status value" time="0.001" classname=".IctOpenApiClientController un-successful response tests"/>
  <testcase name="EnterpriseSearchController getEnterpriseSearchResults should make a request to &quot;GetAiEnterpriseSearch&quot;" time="0" classname=".EnterpriseSearchController getEnterpriseSearchResults"/>
  <testcase name="EnterpriseSearchController getEnterpriseSearchResults should make a request with searchText as a query param" time="0" classname=".EnterpriseSearchController getEnterpriseSearchResults"/>
  <testcase name="EnterpriseSearchController getEnterpriseSearchResults should set searchResults to the expected data" time="0.001" classname=".EnterpriseSearchController getEnterpriseSearchResults"/>
  <testcase name="EnterpriseSearchController getEnterpriseSearchResults should request a host update" time="0.001" classname=".EnterpriseSearchController getEnterpriseSearchResults"/>
  <testcase name="EnterpriseSearchController getEnterpriseSearchResults should set errorMessage when an error is thrown" time="0" classname=".EnterpriseSearchController getEnterpriseSearchResults"/>
  <testcase name="&lt;ict-dematic-answers> should be available" time="0" classname=".&lt;ict-dematic-answers>"/>
  <testcase name="&lt;ict-enterprise-search> should be available" time="0" classname=".&lt;ict-enterprise-search>"/>
  <testcase name="&lt;ict-enterprise-search> should have an empty inputErrorText property upon initialization" time="0.016" classname=".&lt;ict-enterprise-search>"/>
  <testcase name="&lt;ict-enterprise-search> should have an empty searchText property upon initialization" time="0.012" classname=".&lt;ict-enterprise-search>"/>
  <testcase name="&lt;ict-enterprise-search> dgu-value-changed event should set the searchText property" time="0.011" classname=".&lt;ict-enterprise-search>"/>
  <testcase name="&lt;ict-enterprise-search> should set inputErrorText when the Search button is pressed with no query string" time="0.012" classname=".&lt;ict-enterprise-search>"/>
  <testcase name="&lt;ict-enterprise-search> should set inputErrorText back to empty when the Search button is pressed with a valid query string" time="0.013" classname=".&lt;ict-enterprise-search>"/>
  <testcase name="IctDataExplorerBookmarksCard renders correctly" time="0.002" classname=".IctDataExplorerBookmarksCard"/>
  <testcase name="IctDataExplorerBookmarksCard should render title within the element" time="0.016" classname=".IctDataExplorerBookmarksCard"/>
  <testcase name="IctDataExplorerBookmarksCard should render ict-data-explorer-bookmarks-card within the element" time="0.016" classname=".IctDataExplorerBookmarksCard"/>
  <testcase name="IctDataExplorerBookmarkCardController MockedBookmarkData should make a request to the correct endpoint" time="0.01" classname=".IctDataExplorerBookmarkCardController MockedBookmarkData"/>
  <testcase name="IctDataExplorerBookmarkCardController MockedBookmarkData should set MockedBookmarkData correctly" time="0" classname=".IctDataExplorerBookmarkCardController MockedBookmarkData"/>
  <testcase name="IctDataExplorerBookmarkCardController MockedBookmarkData should handle invalid response payloads" time="0.001" classname=".IctDataExplorerBookmarkCardController MockedBookmarkData"/>
  <testcase name="IctDataExplorerBookmarksPage renders correctly" time="0" classname=".IctDataExplorerBookmarksPage"/>
  <testcase name="IctDataExplorerBookmarksPage should render title within the element" time="0.014" classname=".IctDataExplorerBookmarksPage"/>
  <testcase name="IctDataExplorerBookmarksPage should render ict-data-explorer-bookmarks-page within the element" time="0.033" classname=".IctDataExplorerBookmarksPage"/>
  <testcase name="IctDataExplorerBookmarkResultController MockedBookmarkData should make a request to the correct endpoint" time="0.001" classname=".IctDataExplorerBookmarkResultController MockedBookmarkData"/>
  <testcase name="IctDataExplorerBookmarkResultController MockedBookmarkData should set MockedBookmarkData correctly" time="0" classname=".IctDataExplorerBookmarkResultController MockedBookmarkData"/>
  <testcase name="IctDataExplorerBookmarkResultController MockedBookmarkData should handle invalid response payloads" time="0.001" classname=".IctDataExplorerBookmarkResultController MockedBookmarkData"/>
  <testcase name="IctDataExplorerBookmarkResultController MockedBookmarkData should handle errors and set errorMessage" time="0.001" classname=".IctDataExplorerBookmarkResultController MockedBookmarkData"/>
  <testcase name="IctDataExplorerFeedback renders correctly" time="0.001" classname=".IctDataExplorerFeedback"/>
  <testcase name="IctDataExplorerFeedback should render thumbs up and thumbs down icons" time="0.001" classname=".IctDataExplorerFeedback"/>
  <testcase name="IctDataExplorerFeedback should render thumbs up and thumbs down icons within the element" time="0.001" classname=".IctDataExplorerFeedback"/>
  <testcase name="IctDataExplorerFeedback should render ict-data-explorer-feedback within the element" time="0.001" classname=".IctDataExplorerFeedback"/>
  <testcase name="IctDataExplorerRecentSearchesPage renders correctly" time="0.003" classname=".IctDataExplorerRecentSearchesPage"/>
  <testcase name="IctDataExplorerRecentSearchesPage should render title within the element" time="0.033" classname=".IctDataExplorerRecentSearchesPage"/>
  <testcase name="IctDataExplorerRecentSearchesPage should render ict-data-explorer-recent-search-card within the element" time="0.032" classname=".IctDataExplorerRecentSearchesPage"/>
  <testcase name="IctDataExplorerDeleteRecentSearchController deleteRecentSearch should return true for successful deletion" time="0.001" classname=".IctDataExplorerDeleteRecentSearchController deleteRecentSearch"/>
  <testcase name="IctDataExplorerDeleteRecentSearchController deleteRecentSearch should return false for failed deletion" time="0.001" classname=".IctDataExplorerDeleteRecentSearchController deleteRecentSearch"/>
  <testcase name="IctDataExplorerRecentSearchesController MockedRecentSearchesData should make a request to the correct endpoint" time="0.001" classname=".IctDataExplorerRecentSearchesController MockedRecentSearchesData"/>
  <testcase name="IctDataExplorerRecentSearchesController MockedRecentSearchesData should set MockedRecentSearchesData correctly" time="0" classname=".IctDataExplorerRecentSearchesController MockedRecentSearchesData"/>
  <testcase name="IctDataExplorerRecentSearchesController MockedRecentSearchesData should handle invalid response payloads" time="0" classname=".IctDataExplorerRecentSearchesController MockedRecentSearchesData"/>
  <testcase name="IctDataExplorerRecentSearchesPage renders correctly" time="0.025" classname=".IctDataExplorerRecentSearchesPage"/>
  <testcase name="IctDataExplorerRecentSearchesPage should render title within the element" time="0.033" classname=".IctDataExplorerRecentSearchesPage"/>
  <testcase name="IctDataExplorerRecentSearchesPage should render ict-data-explorer-recent-search-card within the element" time="0.033" classname=".IctDataExplorerRecentSearchesPage"/>
  <testcase name="IctDataExplorerRecentSearchesResultController MockedRecentSearchesData should make a request to the correct endpoint" time="0.001" classname=".IctDataExplorerRecentSearchesResultController MockedRecentSearchesData"/>
  <testcase name="IctDataExplorerRecentSearchesResultController MockedRecentSearchesData should set MockedRecentSearchesData correctly" time="0.001" classname=".IctDataExplorerRecentSearchesResultController MockedRecentSearchesData"/>
  <testcase name="IctDataExplorerRecentSearchesResultController MockedRecentSearchesData should handle invalid response payloads" time="0.001" classname=".IctDataExplorerRecentSearchesResultController MockedRecentSearchesData"/>
  <testcase name="IctDataExplorerRecentSearchesResultController MockedRecentSearchesData should handle errors and set errorMessage" time="0" classname=".IctDataExplorerRecentSearchesResultController MockedRecentSearchesData"/>
  <testcase name="IctDataExplorerBookmarkController putDataExplorerBookmark successful status code returns true" time="0.001" classname=".IctDataExplorerBookmarkController putDataExplorerBookmark"/>
  <testcase name="IctDataExplorerBookmarkController putDataExplorerBookmark unsuccessful status code returns false" time="0.001" classname=".IctDataExplorerBookmarkController putDataExplorerBookmark"/>
  <testcase name="IctDataExplorerBookmarkController putDataExplorerBookmark should use resultId as a path parameter in the URL" time="0.001" classname=".IctDataExplorerBookmarkController putDataExplorerBookmark"/>
  <testcase name="IctDataExplorerSearch setViewState should update viewState and call requestUpdate to setState to showingRecentSearchPage" time="0.001" classname=".IctDataExplorerSearch setViewState"/>
  <testcase name="IctDataExplorerSearch setViewState should update viewState and call requestUpdate to setState to showingRecentSearchView" time="0" classname=".IctDataExplorerSearch setViewState"/>
  <testcase name="IctDataExplorerSearch setViewState should update viewState and call requestUpdate to setState to showingRetrievedResult" time="0" classname=".IctDataExplorerSearch setViewState"/>
  <testcase name="IctDataExplorerSearch setViewState should update viewState and call requestUpdate to setState to showingSavedResult" time="0" classname=".IctDataExplorerSearch setViewState"/>
  <testcase name="IctDataExplorerSearch setViewState should update viewState and call requestUpdate to setState to showingBookmarks" time="0" classname=".IctDataExplorerSearch setViewState"/>
  <testcase name="IctDataExplorerSearch setViewState should update viewState and call requestUpdate to setState to showingBookmarkResult" time="0" classname=".IctDataExplorerSearch setViewState"/>
  <testcase name="IctDataExplorerSearch displayBookmark should set bookmarkResult and viewState when displayBookmark is called" time="0" classname=".IctDataExplorerSearch displayBookmark"/>
  <testcase name="IctDataExplorerSearch displayRecentSearch should set recentSearchResult and viewState when showingSavedResult is called" time="0.001" classname=".IctDataExplorerSearch displayRecentSearch"/>
  <testcase name="IctDataExplorerSearch search searches when input is valid" time="0.001" classname=".IctDataExplorerSearch search"/>
  <testcase name="IctDataExplorerSearch search does not search when input is invalid" time="0" classname=".IctDataExplorerSearch search"/>
  <testcase name="IctDataExplorerSearch search sets search prompt and initiates search" time="0.001" classname=".IctDataExplorerSearch search"/>
  <testcase name="IctDataExplorerSearch search updates search text from event" time="0.001" classname=".IctDataExplorerSearch search"/>
  <testcase name="IctDataExplorerSearchResultView renders correctly" time="0.001" classname=".IctDataExplorerSearchResultView"/>
  <testcase name="IctDataExplorerSearchResultView result processing of AI search results viewData should have a table with expected column names" time="0.011" classname=".IctDataExplorerSearchResultView result processing of AI search results"/>
  <testcase name="IctDataExplorerSearchResultView result processing of AI search results if AI response has a modified prompt, that should be used as the question in the results" time="0.006" classname=".IctDataExplorerSearchResultView result processing of AI search results"/>
  <testcase name="IctDataExplorerSearchResultView data explorer search result view bookmark handling bookmark icon name is set correctly based on the bookmark state" time="0.007" classname=".IctDataExplorerSearchResultView data explorer search result view bookmark handling"/>
  <testcase name="IctDataExplorerSearchResultView data explorer search result view bookmark handling successful bookmark toggle sets bookmarked property to true" time="0.007" classname=".IctDataExplorerSearchResultView data explorer search result view bookmark handling"/>
  <testcase name="IctDataExplorerSearchResultView data explorer search result view feedback handling should update feedback status correctly on successful API call" time="0.007" classname=".IctDataExplorerSearchResultView data explorer search result view feedback handling"/>
  <testcase name="IctDataExplorerSearchResultView data explorer search result view feedback handling should handle the case where viewData or result is undefined" time="0.006" classname=".IctDataExplorerSearchResultView data explorer search result view feedback handling"/>
  <testcase name="IctDataExplorerSearchResultView chart rendering renders the chart with correct options" time="0.006" classname=".IctDataExplorerSearchResultView chart rendering"/>
  <testcase name="IctFileUpload should render correctly" time="0.016" classname=".IctFileUpload"/>
  <testcase name="IctFileUpload should render IctFileUploadPanel within the element" time="0.033" classname=".IctFileUpload"/>
  <testcase name="IctInventoryForecast should render correctly" time="0.032" classname=".IctInventoryForecast"/>
  <testcase name="IctInventoryForecast should render title within the element" time="0.015" classname=".IctInventoryForecast"/>
  <testcase name="IctInventoryForecast should open the export dialog when the &quot;Export as PDF&quot; button is clicked" time="0.031" classname=".IctInventoryForecast"/>
  <testcase name="IctInventoryForecast should close the export dialog when the &quot;Cancel&quot; button is clicked" time="0.033" classname=".IctInventoryForecast"/>
  <testcase name="IctInventoryForecast should render the SKUs in the dialog content" time="0.033" classname=".IctInventoryForecast"/>
  <testcase name="IctInventoryForecastDataAnalysisTimestamp should render correctly" time="0.031" classname=".IctInventoryForecastDataAnalysisTimestamp"/>
  <testcase name="IctInventoryForecastDataAnalysisTimestamp should render title within the element" time="0.032" classname=".IctInventoryForecastDataAnalysisTimestamp"/>
  <testcase name="IctInventoryForecastDataAnalysisTimestamp should render timestamps" time="0.033" classname=".IctInventoryForecastDataAnalysisTimestamp"/>
  <testcase name="IctInventoryForecastDataAnalysisTimestampController getTimestampData should fetch and process timestamp data" time="0" classname=".IctInventoryForecastDataAnalysisTimestampController getTimestampData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData should fetch and transform inventory forecast data" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData should request a host update" time="0" classname=".InventoryForecastListController getInventoryForecastListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData should handle invalid API responses gracefully" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData should handle an empty response payload gracefully" time="0" classname=".InventoryForecastListController getInventoryForecastListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData should skip non-numerical values during rounding" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData should handle an invalid response without a payload" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData should set inventoryForecastList to an empty array when processAxiosResponse returns invalid" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData should handle null or undefined values in rounding logic" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData should handle missing properties gracefully" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData should return the fetched inventory forecast list from fetchData" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should register custom element" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should render the table" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should render a percentage for a valid number" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should render an empty template for undefined or null values" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should render &quot;0%&quot; for a zero value" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should trigger the drawer with the correct content" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should close the drawer" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should handle selected items change and update selection count" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should clear the selection" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should open the pick list export dialog" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should close the pick list export dialog" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should update table data on refresh" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should have default enteredFileName as Pick_List" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should update enteredFileName on handleFileNameChange" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should call handleExportAsPDF when handleKeydown detects Enter key" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should fetch timestamps" time="0.018" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should fetch all SKU location details" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should render a percentage for negative numbers correctly" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should handle clearSelection when selectedSkus is already empty" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should handle errors in handleExportAsPDF and reset generatingDocument" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should handle handleSelectedItemsChanged with no indices" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should handle empty input for handleFileNameChange" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should renderDialogContent correctly with multiple SKUs" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should handle large numbers in percentageRenderer" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should not enqueue snackbar message when locations are found for the selected SKU" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should not call snackbar if skuLocationDetails is empty but noLocationsFound is false" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should handle handleSelectedItemsChanged when selected indices are on another page" time="0.007" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should handle handleSelectedItemsChanged when sortedAndFilteredItems is empty" time="0.003" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should handle handleSelectedItemsChanged with overlapping selections across pages" time="0.02" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should handle handleSelectedItemsChanged and remove deselected items from map" time="0.012" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should handle whitespace input for handleFileNameChange" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should not throw error when handleFileNameChange receives null detail" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should register and unregister event listeners in connectedCallback and disconnectedCallback" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should build table columns with correct properties" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should generate drawerContent when triggering the drawer" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should handle Enter key in handleKeydown" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should handle empty drawerContent in triggerDrawer" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should gracefully handle missing indices in handleSelectedItemsChanged" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should clear selections and reset state in clearSelection" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should handle whitespace and null input in handleFileNameChange" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should correctly render dialog content with selected SKUs" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should clear selections when onPaginationChange is called" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should clear selections when onPageSizeChange is called" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should clear selections when filters change" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable should clear selections when sorters change" time="0.002" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuLocationsPDFController getSkuLocationDetails should fetch and filter SKU location details by &quot;Reserve Storage&quot;" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctSkuLocationsPDFController getSkuLocationDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuLocationsPDFController getSkuLocationDetails should make a request to the correct endpoint" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctSkuLocationsPDFController getSkuLocationDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuLocationsPDFController getSkuLocationDetails should handle invalid responses gracefully" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctSkuLocationsPDFController getSkuLocationDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuLocationsPDFController getSkuLocationDetails should set noLocationsFound to true and enqueue snackbar when no Reserve Storage locations are found" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctSkuLocationsPDFController getSkuLocationDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuLocationsPDFController getSkuLocationDetails should sort locations with &quot;CNV&quot; prefix first, then by quantity in ascending order" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctSkuLocationsPDFController getSkuLocationDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuDetailsBaseCard renders correctly" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData IctSkuDetailsBaseCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuDetailsBaseCard should not render title within the base element" time="0.016" classname=".InventoryForecastListController getInventoryForecastListData IctSkuDetailsBaseCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuDetailsBaseCard should render ict-sku-details-base-card within the element" time="0.018" classname=".InventoryForecastListController getInventoryForecastListData IctSkuDetailsBaseCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetails custom element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetails should call controller method when skuId is set" time="0.011" classname=".InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetails controller is only called once the skuId is set" time="0.006" classname=".InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetails should render loading state initially" time="0.006" classname=".InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetailsChart custom element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetailsChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetailsChart should call controller method when skuId is set" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetailsChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetailsChart should render loading state initially" time="0.007" classname=".InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetailsChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetailsChart should render loaded state with chart data" time="0.007" classname=".InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetailsChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetailsController getSkuForecastDetails should set skuForecastDetails to the expected data" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetailsController getSkuForecastDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetailsController getSkuForecastDetails should request a host update" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetailsController getSkuForecastDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetailsController getSkuForecastDetails should set skuForecastDetails to undefined if the response is unsuccessful" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetailsController getSkuForecastDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetailsController getSkuForecastDetails should catch errors during the API call" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctSkuForecastingDetailsController getSkuForecastDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-sku-locations-details> custom element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-sku-locations-details>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-sku-locations-details> should call controller method when skuId is set" time="0.007" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-sku-locations-details>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-sku-locations-details> controller is only called once the skuId is set" time="0.007" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-sku-locations-details>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuLocationsDetailsController getSkuLocationDetails should set skuLocationDetails to the expected data" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctSkuLocationsDetailsController getSkuLocationDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuLocationsDetailsController getSkuLocationDetails should request a host update" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctSkuLocationsDetailsController getSkuLocationDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuLocationsDetailsController getSkuLocationDetails should set skuLocationDetails to undefined if the response is unsuccessful" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctSkuLocationsDetailsController getSkuLocationDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuLocationsDetailsController getSkuLocationDetails should catch errors during the API call" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctSkuLocationsDetailsController getSkuLocationDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuOrdersDetails custom element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctSkuOrdersDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuOrdersDetails should call controller method when skuId is set" time="0.007" classname=".InventoryForecastListController getInventoryForecastListData IctSkuOrdersDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuOrdersDetails controller is only called once the skuId is set" time="0.006" classname=".InventoryForecastListController getInventoryForecastListData IctSkuOrdersDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuOrdersDetails should render loading state initially" time="0.007" classname=".InventoryForecastListController getInventoryForecastListData IctSkuOrdersDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuOrdersDetailsController getSkuOrderDetails should set skuOrderDetails to the expected data" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctSkuOrdersDetailsController getSkuOrderDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuOrdersDetailsController getSkuOrderDetails should request a host update" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctSkuOrdersDetailsController getSkuOrderDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuOrdersDetailsController getSkuOrderDetails should set skuOrderDetails to undefined if the response is unsuccessful" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctSkuOrdersDetailsController getSkuOrderDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuOrdersDetailsController getSkuOrderDetails should catch errors during the API call" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctSkuOrdersDetailsController getSkuOrderDetails"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListDrawer should render correctly" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListDrawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListDrawer should set the default active tab to Locations" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListDrawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListDrawer should handle onTabSelected event and switch to Orders tab" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListDrawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListDrawer should handle onTabSelected event and switch to Forecasting tab" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListDrawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListDrawer should render ict-no-data-available when no selectedSkuId is set" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListDrawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListDrawer should set the selectedSkuId and call requestUpdate when onSkuSelected is called" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListDrawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryForecastListDrawer should render no data available state correctly" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryForecastListDrawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctCuratedDataDropdownController getCuratedTableNames should make a request to the expected endpoint" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctCuratedDataDropdownController getCuratedTableNames"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctCuratedDataDropdownController getCuratedTableNames should convert table names data to DguComboBoxItemInterface&lt;string>[]" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctCuratedDataDropdownController getCuratedTableNames"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctCuratedDataDropdownController getCuratedTableNames should request a host update" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctCuratedDataDropdownController getCuratedTableNames"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctCuratedDataDropdownController getCuratedTableNames should catch errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctCuratedDataDropdownController getCuratedTableNames"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer ict-app-settings-details-drawer should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer ict-app-settings-details-drawer is available" time="0" classname=".InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer changing values updates the setting property" time="0.069" classname=".InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer clicking save calls updateSetting with the given setting" time="0.027" classname=".InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer clicking save trims the setting name" time="0.025" classname=".InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer clicking save doesnt call updateSetting when there is no name" time="0.025" classname=".InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer clicking save does not call updateSetting when there is no value for a non-string type" time="0.025" classname=".InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer clicking save calls updateSetting to set an empty string when there is no value for string setting" time="0.024" classname=".InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer group field isnt editable when set to readonly" time="0.022" classname=".InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer datatype field isnt rendered when set to invisible" time="0.021" classname=".InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer the id field should not be editable" time="0.022" classname=".InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer text area appears when datatype is json and default value is readonly" time="0.024" classname=".InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer accordion should not render if a new setting is being made" time="0.013" classname=".InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer accordion should render if a setting is being edited" time="0.021" classname=".InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer accordion should show a message if no logs are returned from the API" time="0.015" classname=".InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer accordion should return a table and no buttons if &lt;initialLogs logs were found" time="0.02" classname=".InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer accordion should return a table and buttons if >=initialLogs logs were found" time="0.021" classname=".InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer accordion should disable buttons if buttons were shown and, after clicked, found all logs" time="0.021" classname=".InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer User role conditions fields arent editable when user doesnt have the proper permission" time="0.018" classname=".InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer User role conditions"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer User role conditions save and delete buttons arent rendered when user doesnt have the proper permission" time="0.019" classname=".InventoryForecastListController getInventoryForecastListData App Settings Configuration Drawer User role conditions"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData Feature Flag Configuration Page ict-feature-flag-config should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData Feature Flag Configuration Page"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData Feature Flag Configuration Page ict-feature-flag-config is available" time="0" classname=".InventoryForecastListController getInventoryForecastListData Feature Flag Configuration Page"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData Feature Flag Configuration Page tableItems equal the list of Appconfig.featureFlags" time="0.048" classname=".InventoryForecastListController getInventoryForecastListData Feature Flag Configuration Page"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData Feature Flag Configuration Page Add/edit feature flag drawer drawer opens with the proper setting in context" time="0.034" classname=".InventoryForecastListController getInventoryForecastListData Feature Flag Configuration Page Add/edit feature flag drawer"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData Feature Flag Configuration Page feature toggles from table toggling a feature that no longer exists doesnt make an API call" time="0.034" classname=".InventoryForecastListController getInventoryForecastListData Feature Flag Configuration Page feature toggles from table"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData Feature Flag Configuration Page feature toggles from table toggling a feature makes an API call to updateSetting" time="0.044" classname=".InventoryForecastListController getInventoryForecastListData Feature Flag Configuration Page feature toggles from table"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData Feature Flag Configuration Page feature toggles from table deleting a feature from the table calls the API and refreshes the table" time="0.035" classname=".InventoryForecastListController getInventoryForecastListData Feature Flag Configuration Page feature toggles from table"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData Feature Flag Configuration Page feature toggles from table clicking the delete button shows a DguDialog and doesnt call the API if cancelled by the user" time="0.034" classname=".InventoryForecastListController getInventoryForecastListData Feature Flag Configuration Page feature toggles from table"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData Feature Flag Configuration Page feature toggles from table delete button is disabled if user doesnt have the right role" time="0.034" classname=".InventoryForecastListController getInventoryForecastListData Feature Flag Configuration Page feature toggles from table"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData EditDashboardPanelController should get the dashboard" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData EditDashboardPanelController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData EditDashboardPanelController should handle errors" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData EditDashboardPanelController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctEditDashboardPanel should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctEditDashboardPanel"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctEditDashboardPanel should have the test component in the widgets list" time="0.002" classname=".InventoryForecastListController getInventoryForecastListData IctEditDashboardPanel"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctEditDashboardPanel should show the widget when searched for" time="0.003" classname=".InventoryForecastListController getInventoryForecastListData IctEditDashboardPanel"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctEditDashboardPanel should not show widgets when search field text doesn't contain them" time="0.002" classname=".InventoryForecastListController getInventoryForecastListData IctEditDashboardPanel"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData CustomDashboardMenuController should fetch menu items and populate customDashboardSettings" time="0" classname=".InventoryForecastListController getInventoryForecastListData CustomDashboardMenuController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData CustomDashboardMenuController should not fetch menu items if feature flag is disabled" time="0" classname=".InventoryForecastListController getInventoryForecastListData CustomDashboardMenuController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData CustomDashboardMenuController should return custom dashboards menu items" time="0" classname=".InventoryForecastListController getInventoryForecastListData CustomDashboardMenuController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData CustomDashboardMenuController should handle empty settings" time="0" classname=".InventoryForecastListController getInventoryForecastListData CustomDashboardMenuController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData CustomDashboardMenuController should return empty array if settings are undefined" time="0" classname=".InventoryForecastListController getInventoryForecastListData CustomDashboardMenuController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DashboardManagerController should get user dashboards" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DashboardManagerController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DashboardManagerController should handle single dashboard response" time="0" classname=".InventoryForecastListController getInventoryForecastListData DashboardManagerController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DashboardManagerController should handle empty response" time="0" classname=".InventoryForecastListController getInventoryForecastListData DashboardManagerController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DashboardManagerController should favor tenant setting over user setting" time="0" classname=".InventoryForecastListController getInventoryForecastListData DashboardManagerController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDashboardManager element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDashboardManager"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDashboardManager should fetch dashboards on firstUpdated" time="0.004" classname=".InventoryForecastListController getInventoryForecastListData IctDashboardManager"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDashboardManager should refresh dashboard list" time="0.003" classname=".InventoryForecastListController getInventoryForecastListData IctDashboardManager"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDashboardManager publish button appears when dashboard is not published" time="0.005" classname=".InventoryForecastListController getInventoryForecastListData IctDashboardManager"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDashboardManager unpublish button appears when dashboard is published" time="0.005" classname=".InventoryForecastListController getInventoryForecastListData IctDashboardManager"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDashboardManager should set active dashboard" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDashboardManager"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDashboardManager should render loading shimmer" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDashboardManager"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDashboardManager should open dialog when clicking on New Dashboard button" time="0.005" classname=".InventoryForecastListController getInventoryForecastListData IctDashboardManager"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDashboardManager should delete dashboard when confirmed" time="0.004" classname=".InventoryForecastListController getInventoryForecastListData IctDashboardManager"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDashboardManager should not delete dashboard when not confirmed" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctDashboardManager"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDashboardManager should handle error when deleting setting" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDashboardManager"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctNewCustomDashboardDialog should rename an existing dashboard" time="0.005" classname=".InventoryForecastListController getInventoryForecastListData IctNewCustomDashboardDialog"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctNewCustomDashboardDialog should handle error when updating setting" time="0.003" classname=".InventoryForecastListController getInventoryForecastListData IctNewCustomDashboardDialog"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctNewCustomDashboardDialog should validate dashboard name" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctNewCustomDashboardDialog"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctNewCustomDashboardDialog should validate existing dashboard copy options" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctNewCustomDashboardDialog"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctNewCustomDashboardDialog should validate setting name is unique when unique" time="0.007" classname=".InventoryForecastListController getInventoryForecastListData IctNewCustomDashboardDialog"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctNewCustomDashboardDialog should validate setting name is unique - not unique" time="0.002" classname=".InventoryForecastListController getInventoryForecastListData IctNewCustomDashboardDialog"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctNewCustomDashboardDialog should validate setting name is unique - error" time="0.003" classname=".InventoryForecastListController getInventoryForecastListData IctNewCustomDashboardDialog"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctNewCustomDashboardDialog should get all renderer types from render config" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctNewCustomDashboardDialog"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctNewCustomDashboardDialog should rename a dashboard with publishedTo" time="0.005" classname=".InventoryForecastListController getInventoryForecastListData IctNewCustomDashboardDialog"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctNewCustomDashboardDialog DGU Render Config to Gridstack converter should convert DGU render config to Gridstack config" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctNewCustomDashboardDialog DGU Render Config to Gridstack converter"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctTableauConfiguredAlertsController should initialize correctly" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctTableauConfiguredAlertsController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctTableauConfiguredAlertsController getTableauConfiguredAlerts should populate alertList with a list of tableau alerts with details" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctTableauConfiguredAlertsController getTableauConfiguredAlerts"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctTableauConfiguredAlertsController getTableauConfiguredAlerts should set state to no data available if no alerts are found" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctTableauConfiguredAlertsController getTableauConfiguredAlerts"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctTableauConfiguredAlertsController getTableauConfiguredAlerts should handle cases where the alert list api returns an error" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctTableauConfiguredAlertsController getTableauConfiguredAlerts"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctTableauConfiguredAlertsController getTableauConfiguredAlerts should handle cases where the alert detail api returns an error" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctTableauConfiguredAlertsController getTableauConfiguredAlerts"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctTableauConfiguredAlertsController getTableauConfiguredAlerts should handle case where user does not have access to Tableau" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctTableauConfiguredAlertsController getTableauConfiguredAlerts"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-overall-outbound-rate> custom element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-overall-outbound-rate>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-overall-outbound-rate> should render the correct header for the kpi-card" time="0.01" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-overall-outbound-rate>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-overall-outbound-rate> should reflect controller's data in embedded component" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-overall-outbound-rate>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData OverallOutboundRateController getOutboundRateData should make a request to the expected endpoint" time="0" classname=".InventoryForecastListController getInventoryForecastListData OverallOutboundRateController getOutboundRateData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData OverallOutboundRateController getOutboundRateData should set overallOutboundRateMetricData to the expected data" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData OverallOutboundRateController getOutboundRateData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData OverallOutboundRateController getOutboundRateData should request a host update" time="0" classname=".InventoryForecastListController getInventoryForecastListData OverallOutboundRateController getOutboundRateData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData OverallOutboundRateController getOutboundRateData should catch errors calling the api" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData OverallOutboundRateController getOutboundRateData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctUnitsRemaining should have a header of &quot;Units Remaining&quot;" time="0.01" classname=".InventoryForecastListController getInventoryForecastListData IctUnitsRemaining"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctUnitsRemaining should have a default value of null" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData IctUnitsRemaining"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctUnitsRemainingController API error should result in should reset of unitsRemainingData" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctUnitsRemainingController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctUnitsRemainingController getUnitsRemaining should set unitsRemainingData" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctUnitsRemainingController getUnitsRemaining"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctUnitsRemainingController getUnitsRemaining should request an update" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctUnitsRemainingController getUnitsRemaining"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAutomationOverviewMenuController getSummaryAreasAsync should make a request to the &quot;GetEquipmentSummaryAreas&quot; endpoint" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctAutomationOverviewMenuController getSummaryAreasAsync"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAutomationOverviewMenuController getSummaryAreasAsync should make a request with start_date and end_date query params as a query param" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctAutomationOverviewMenuController getSummaryAreasAsync"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAutomationOverviewMenuController getSummaryAreasAsync should set searchResults to the expected data" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctAutomationOverviewMenuController getSummaryAreasAsync"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-active-faults-card> custom element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-active-faults-card>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-active-faults-card> should render title within the &lt;ict-active-faults-card> element" time="0.012" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-active-faults-card>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-active-faults-card> should render the view-details button" time="0.01" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-active-faults-card>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctActiveFaultsController getActiveFaultsData should make a request to the &quot;GetEquipmentFaultsActiveList&quot; endpoint" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctActiveFaultsController getActiveFaultsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctActiveFaultsController getActiveFaultsData should set activeFaults to the expectedData" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctActiveFaultsController getActiveFaultsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctActiveFaultsController getActiveFaultsData should catch errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctActiveFaultsController getActiveFaultsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctActiveFaultsController getActiveFaultsData should request a host update" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctActiveFaultsController getActiveFaultsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-event-detail-list> custom element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-event-detail-list>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-event-detail-list> should render the correct title for the ict-event-detail-list" time="0.018" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-event-detail-list>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-event-detail-list> should render the table" time="0.016" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-event-detail-list>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-event-detail-list> should set state to loading and call updateSkuData when the refresh button is clicked" time="0.017" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-event-detail-list>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctEventDetailListController getEventDetailData should make a request to &quot;/equipment/faults/details&quot; to return faults details" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctEventDetailListController getEventDetailData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctEventDetailListController getEventDetailData should set eventDetails to the expectedData" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctEventDetailListController getEventDetailData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctEventDetailListController getEventDetailData should catch errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctEventDetailListController getEventDetailData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctEventDetailListController getEventDetailData should request a host update" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctEventDetailListController getEventDetailData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-recent-events-card> custom element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-recent-events-card>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-recent-events-card> should render title within the &lt;ict-recent-events-card> element" time="0.01" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-recent-events-card>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-recent-events-card> should render the view-details button" time="0.011" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-recent-events-card>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctRecentEventsController getRecentEventsData should make a request to &quot;GetEquipmentFaultsEvents&quot; endpoint" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctRecentEventsController getRecentEventsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctRecentEventsController getRecentEventsData should set recentEvents to the expectedData" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctRecentEventsController getRecentEventsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctRecentEventsController getRecentEventsData should catch errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctRecentEventsController getRecentEventsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctRecentEventsController getRecentEventsData should request a host update" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctRecentEventsController getRecentEventsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainersList should render correctly" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctContainersList"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainersList should not render title within the element" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctContainersList"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainersList should have no breadcrumbs until container ID selected" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctContainersList"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainersList should only show the ict-container-events-list and breadcrumbs when a container ID is selected" time="0.048" classname=".InventoryForecastListController getInventoryForecastListData IctContainersList"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainersListTable should render correctly" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctContainersListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainersListTable should call isFilteredOnInitialLoad on firstUpdated when sku is present in URL" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctContainersListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainersListTable should call isFilteredOnInitialLoad in updated when sku changes" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctContainersListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainersListTable should not call isFilteredOnInitialLoad on firstUpdated when sku is not present in URL" time="0.002" classname=".InventoryForecastListController getInventoryForecastListData IctContainersListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainersListTable should render ict-containers-list-table within the element" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctContainersListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainersListTable should call getContainersListData on controller when refreshContainerList is called" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctContainersListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainersListTable should apply filters when SKU is provided" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctContainersListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainersListTable should load all data when no SKU is provided" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctContainersListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainersListTable should update bypassFilter when the checkbox is toggled" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctContainersListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainersListTable should call refreshContainerList when bypass filter is toggled" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctContainersListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData ContainersListTableController getContainersListData should fetch and populate container data" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData ContainersListTableController getContainersListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData ContainersListTableController getContainersListData should handle invalid API responses by clearing container data" time="0" classname=".InventoryForecastListController getInventoryForecastListData ContainersListTableController getContainersListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData ContainersListTableController getContainersListData should return fetched container data from fetchData" time="0" classname=".InventoryForecastListController getInventoryForecastListData ContainersListTableController getContainersListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData ContainersListTableController getContainersListData should correctly format and set lastSnapshotDateTime based on the first record" time="0" classname=".InventoryForecastListController getInventoryForecastListData ContainersListTableController getContainersListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData ContainersListTableController getContainersListData should set lastSnapshotDateTime to undefined if no data is available" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData ContainersListTableController getContainersListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-container-detail-card> should render correctly" time="0" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-container-detail-card>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-container-detail-card> should be registered as a custom element" time="0" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-container-detail-card>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-container-detail-card> should call fetchContainerKpiData when containerId is set" time="0" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-container-detail-card>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-container-detail-card> should not fetch data if containerId is null" time="0" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-container-detail-card>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctCycleCountEventsToday should render correctly" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctCycleCountEventsToday"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctCycleCountEventsToday should be registered as a custom element" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctCycleCountEventsToday"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctCycleCountEventsToday should call fetchContainerKpiData when containerId is set" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctCycleCountEventsToday"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctCycleCountEventsToday should not fetch data if containerId is null" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctCycleCountEventsToday"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctCycleCountEventsToday should render dgu-statistic with correct attributes" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctCycleCountEventsToday"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctCycleCountEventsToday should render no data state when pickEventsToday is missing" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctCycleCountEventsToday"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAverageDailyCycleCountEvents should render correctly" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctAverageDailyCycleCountEvents"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAverageDailyCycleCountEvents should be registered as a custom element" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctAverageDailyCycleCountEvents"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAverageDailyCycleCountEvents should call fetchContainerKpiData when containerId is set" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctAverageDailyCycleCountEvents"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAverageDailyCycleCountEvents should not fetch data if containerId is null" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctAverageDailyCycleCountEvents"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAverageDailyCycleCountEvents should render dgu-statistic with correct attributes" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctAverageDailyCycleCountEvents"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAverageDailyCycleCountEvents should render no data state when pickEventsToday is missing" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctAverageDailyCycleCountEvents"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAverageDailyPickEvents should render correctly" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctAverageDailyPickEvents"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAverageDailyPickEvents should be registered as a custom element" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctAverageDailyPickEvents"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAverageDailyPickEvents should call fetchContainerKpiData when containerId is set" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctAverageDailyPickEvents"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAverageDailyPickEvents should not fetch data if containerId is null" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctAverageDailyPickEvents"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAverageDailyPickEvents should render dgu-statistic with correct attributes" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctAverageDailyPickEvents"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAverageDailyPickEvents should render no data state when pickEventsToday is missing" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctAverageDailyPickEvents"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventsKpiController fetchContainerKpiData should fetch and populate container KPI data" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventsKpiController fetchContainerKpiData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventsKpiController fetchContainerKpiData should handle invalid API responses by setting containerKpiData to undefined" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventsKpiController fetchContainerKpiData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventsKpiController fetchContainerKpiData should correctly format dates within container KPI data" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventsKpiController fetchContainerKpiData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctPickEventsToday should render correctly" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctPickEventsToday"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctPickEventsToday should be registered as a custom element" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctPickEventsToday"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctPickEventsToday should call fetchContainerKpiData when containerId is set" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctPickEventsToday"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctPickEventsToday should not fetch data if containerId is null" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctPickEventsToday"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctPickEventsToday should render dgu-statistic with correct attributes" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctPickEventsToday"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctPickEventsToday should render no data state when pickEventsToday is missing" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctPickEventsToday"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsList should render correctly" time="0.043" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsList"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsList should not render title within the element" time="0.042" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsList"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsList should correctly parse containerId from URL parameters" time="0.042" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsList"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTable should render correctly" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTable should render empty state when items are empty" time="0.015" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTable should call refreshContainerEventDetailsList on firstUpdated" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTable should render event table within the element" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTable should call getContainerEventDetailsListData on controller when refreshContainerEventDetailsList is called" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTable should update fileName with Unknown_Container when containerId is not in the URL" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTable should handle missing containerId gracefully in updateFileName" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTable should handle unexpected URL structures in updateFileName" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTable should update the state and trigger rendering" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTable should update the months property and trigger refresh when updateMonths is called directly" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTableController getContainerEventDetailsListData should fetch and populate container event details data" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTableController getContainerEventDetailsListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTableController getContainerEventDetailsListData should handle invalid API responses by clearing container event details data" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTableController getContainerEventDetailsListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTableController getContainerEventDetailsListData should set state to NoDataAvailable and clear data when response is invalid" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTableController getContainerEventDetailsListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTableController getContainerEventDetailsListData should set state to NoDataAvailable when no events are returned" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTableController getContainerEventDetailsListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTableController getContainerEventDetailsListData should return fetched container event details data from fetchData" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTableController getContainerEventDetailsListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTableController getContainerEventDetailsListData should use the correct months parameter when fetching data" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTableController getContainerEventDetailsListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTableController getContainerEventDetailsListData should update the months property when updateMonths is called directly" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctContainerEventDetailsListTableController getContainerEventDetailsListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryListTable should render correctly" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryListTable should apply AppStyles and TableStyles" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData InventoryListController getInventoryListData should set skus to the expectedData" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData InventoryListController getInventoryListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData InventoryListController getInventoryListData should catch errors calling the api" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData InventoryListController getInventoryListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData InventoryListController getInventoryListData should request a host update" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData InventoryListController getInventoryListData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-accuracy> custom element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-accuracy>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-accuracy> should render the correct header for the ict-metric-card" time="0.01" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-accuracy>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-accuracy> should reflect controller data when context is provided" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-accuracy>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-accuracy> controller is only called once the context is set" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-accuracy>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctKpiInventoryAccuracyController getInventoryAccuracyData should set inventoryAccuracyData to the expected data" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctKpiInventoryAccuracyController getInventoryAccuracyData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctKpiInventoryAccuracyController getInventoryAccuracyData should request a host update" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctKpiInventoryAccuracyController getInventoryAccuracyData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctKpiInventoryAccuracyController getInventoryAccuracyData should catch errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctKpiInventoryAccuracyController getInventoryAccuracyData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-orders-outstanding> custom element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-orders-outstanding>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-orders-outstanding> should render the correct header for the kpi-card" time="0.01" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-orders-outstanding>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-orders-outstanding> should reflect controllers data in embedded component" time="0.01" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-orders-outstanding>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-orders-outstanding> should only call controller once the context is set" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-orders-outstanding>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctKpiInventoryOrdersOutstandingController getOutstandingData uses the start_date as a query param" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctKpiInventoryOrdersOutstandingController getOutstandingData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctKpiInventoryOrdersOutstandingController getOutstandingData uses the end_date as a query param" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctKpiInventoryOrdersOutstandingController getOutstandingData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-projected-order-fulfillment> should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-projected-order-fulfillment>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-projected-order-fulfillment> should correctly render the header for the ict-metric-card" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-projected-order-fulfillment>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-projected-order-fulfillment> should reflect controllers data in embedded component" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-projected-order-fulfillment>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-projected-order-fulfillment> controller is only called once the context is set" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-inventory-projected-order-fulfillment>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-orders-pick-cycle-count> custom element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-orders-pick-cycle-count>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-orders-pick-cycle-count> should render the correct header for the ict-metric-card" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-orders-pick-cycle-count>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-orders-pick-cycle-count> should reflect controller data when context is provided" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-orders-pick-cycle-count>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-orders-pick-cycle-count> controller is only called once the context is set" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-kpi-orders-pick-cycle-count>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctKpiOrdersPickCycleCountController getOrdersPickCycleCountData should set cycleCount to the expected data" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctKpiOrdersPickCycleCountController getOrdersPickCycleCountData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctKpiOrdersPickCycleCountController getOrdersPickCycleCountData should request a host update" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctKpiOrdersPickCycleCountController getOrdersPickCycleCountData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctKpiOrdersPickCycleCountController getOrdersPickCycleCountData should catch errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctKpiOrdersPickCycleCountController getOrdersPickCycleCountData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryStatus should be registered as a custom element" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryStatus"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryStatus should render an inventory status chart with the correct properties" time="0.018" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryStatus"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryStatus should render a header that displays the number of days from the data length" time="0.004" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryStatus"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryStatus should render the divider element" time="0.003" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryStatus"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryStatusChart should be registered as a custom element" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryStatusChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryStatusChart should return default y-axis values when no data is provided" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryStatusChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryStatusChart should calculate y-axis min and max based on data" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryStatusChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryStatusChart should generate empty labels matching the length of data" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryStatusChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryStatusChart should generate a dataset from the provided data" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryStatusChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryStatusChart should return label options for max annotation when configured max differs from computed max" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryStatusChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryStatusChart should return undefined for label options when chart max equals computed max" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryStatusChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAvgCycleTimesStatusCard custom element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctAvgCycleTimesStatusCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAvgCycleTimesStatusCard should render the correct header in dgu-statistic" time="0.01" classname=".InventoryForecastListController getInventoryForecastListData IctAvgCycleTimesStatusCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAvgCycleTimesStatusCard should reflect controller data when facilityDateFilter is provided" time="0.015" classname=".InventoryForecastListController getInventoryForecastListData IctAvgCycleTimesStatusCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAvgCycleTimesStatusCard controller should only be called after facilityDateFilter is set" time="0.01" classname=".InventoryForecastListController getInventoryForecastListData IctAvgCycleTimesStatusCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAvgDailyReplenishmentsStatusCard custom element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctAvgDailyReplenishmentsStatusCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAvgDailyReplenishmentsStatusCard should render the correct header in dgu-statistic" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData IctAvgDailyReplenishmentsStatusCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAvgDailyReplenishmentsStatusCard should reflect controller data when facilityDateFilter is provided" time="0.014" classname=".InventoryForecastListController getInventoryForecastListData IctAvgDailyReplenishmentsStatusCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAvgDailyReplenishmentsStatusCard controller should only be called after facilityDateFilter is set" time="0.01" classname=".InventoryForecastListController getInventoryForecastListData IctAvgDailyReplenishmentsStatusCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAvgPendingOrdersStatusCard custom element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctAvgPendingOrdersStatusCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAvgPendingOrdersStatusCard should render the correct header in dgu-statistic" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData IctAvgPendingOrdersStatusCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAvgPendingOrdersStatusCard should reflect controller data when facilityDateFilter is provided" time="0.013" classname=".InventoryForecastListController getInventoryForecastListData IctAvgPendingOrdersStatusCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctAvgPendingOrdersStatusCard controller should only be called after facilityDateFilter is set" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData IctAvgPendingOrdersStatusCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctReplenishmentDetailsController retrieveReplenishmentsData should assign series data and numeric averages from the API response on a valid response" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctReplenishmentDetailsController retrieveReplenishmentsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctReplenishmentDetailsController retrieveReplenishmentsData should set the series data and numeric averages to undefined on an invalid response" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctReplenishmentDetailsController retrieveReplenishmentsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByShiftChart should render correctly" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByShiftChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByShiftChart should render daily replenishment chart within the element" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByShiftChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByShiftChart should call fetchDailyReplenishmentsSeries on refreshData" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByShiftChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByShiftChart should refresh data when facilityDateFilter changes" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByShiftChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByShiftChartController should set xAxisOptions correctly based on dailyReplenishments" time="0.002" classname=".InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByShiftChartController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByShiftChartController should set yAxisOptions correctly" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByShiftChartController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByShiftChartController should build series for each shift with stack property &quot;replenishments&quot;" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByShiftChartController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByShiftChartController should error if startDate or endDate is missing" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByShiftChartController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByShiftChartController should successfully load when both start and end dates are provided" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByShiftChartController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByTypeChart should be registered as a custom element" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByTypeChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByTypeChart should render correctly" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByTypeChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByTypeChart should render the series chart inside the element" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByTypeChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByTypeChart should call fetchDailyReplenishmentsSeries on refreshData" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByTypeChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByTypeChartController should set xAxisOptions correctly based on dailyReplenishments" time="0.002" classname=".InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByTypeChartController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByTypeChartController should set yAxisOptions correctly" time="0.002" classname=".InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByTypeChartController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByTypeChartController should build series for each type with stack property &quot;replenishments&quot;" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByTypeChartController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByTypeChartController should successfully load when both start and end dates are provided" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctDailyReplenishmentsByTypeChartController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDateMenuDropDown should render with 3 items" time="0.004" classname=".InventoryForecastListController getInventoryForecastListData IctDateMenuDropDown"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDateMenuDropDown should have default selectedItemValue of 7" time="0.003" classname=".InventoryForecastListController getInventoryForecastListData IctDateMenuDropDown"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDateMenuDropDown should render the correct caption" time="0.003" classname=".InventoryForecastListController getInventoryForecastListData IctDateMenuDropDown"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDateMenuDropDown should dispatch a date filter change event when selectedItemValue changes" time="0.003" classname=".InventoryForecastListController getInventoryForecastListData IctDateMenuDropDown"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOperatorsActiveKpiCard element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOperatorsActiveKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOperatorsActiveKpiCard should render the correct header for the kpi-card" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData IctOperatorsActiveKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOperatorsActiveKpiCard should reflect controller's data in embedded component" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData IctOperatorsActiveKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOperatorsActiveKpiCardController getAggregateActiveOperatorsData Default value should be undefined" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOperatorsActiveKpiCardController getAggregateActiveOperatorsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOperatorsActiveKpiCardController getAggregateActiveOperatorsData should make a request to the &quot;GetOperatorsActive&quot; endpoint&quot;" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOperatorsActiveKpiCardController getAggregateActiveOperatorsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOperatorsActiveKpiCardController getAggregateActiveOperatorsData should request a host update" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOperatorsActiveKpiCardController getAggregateActiveOperatorsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOperatorsActiveKpiCardController getAggregateActiveOperatorsData operatorCount be returned with expected value." time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOperatorsActiveKpiCardController getAggregateActiveOperatorsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOperatorsActiveKpiCardController getAggregateActiveOperatorsData should handle errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOperatorsActiveKpiCardController getAggregateActiveOperatorsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerCycleTimeKpiCard element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerCycleTimeKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerCycleTimeKpiCard should render the correct header for the kpi-card" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerCycleTimeKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerCycleTimeKpiCard should reflect controller's data in embedded component" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerCycleTimeKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerCycleTimeKpiCardController getOrderCycleTimeKpiData Default value should be undefined" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerCycleTimeKpiCardController getOrderCycleTimeKpiData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerCycleTimeKpiCardController getOrderCycleTimeKpiData order cycle time count to be returned with expected value." time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerCycleTimeKpiCardController getOrderCycleTimeKpiData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerCycleTimeKpiCardController getOrderCycleTimeKpiData should handle errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerCycleTimeKpiCardController getOrderCycleTimeKpiData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerProgressKpiCardController getAggregateOrderProgressData should request a host update" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerProgressKpiCardController getAggregateOrderProgressData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerProgressKpiCardController getAggregateOrderProgressData should catch errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerProgressKpiCardController getAggregateOrderProgressData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerThroughputKpiCard element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerThroughputKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerThroughputKpiCard should render the correct header for the kpi-card" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerThroughputKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerThroughputKpiCard should reflect controller's data in embedded component" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerThroughputKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerThroughputKpiCardController getThroughputRateKpiData Default value should be undefined" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerThroughputKpiCardController getThroughputRateKpiData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerThroughputKpiCardController getThroughputRateKpiData operatorCount be returned with expected value." time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerThroughputKpiCardController getThroughputRateKpiData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerThroughputKpiCardController getThroughputRateKpiData should handle errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerThroughputKpiCardController getThroughputRateKpiData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCycleTimeKpiCard element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCycleTimeKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCycleTimeKpiCard should render the correct header for the kpi-card" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCycleTimeKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCycleTimeKpiCard should reflect controller's data in embedded component" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCycleTimeKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCycleTimeKpiCardController getOrderCycleTimeKpiData Default value should be undefined" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCycleTimeKpiCardController getOrderCycleTimeKpiData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCycleTimeKpiCardController getOrderCycleTimeKpiData should request a host update" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCycleTimeKpiCardController getOrderCycleTimeKpiData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCycleTimeKpiCardController getOrderCycleTimeKpiData order cycle time count to be returned with expected value." time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCycleTimeKpiCardController getOrderCycleTimeKpiData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCycleTimeKpiCardController getOrderCycleTimeKpiData should handle errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCycleTimeKpiCardController getOrderCycleTimeKpiData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersLineprogressKpiCard element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersLineprogressKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersLineprogressKpiCard should render the correct header for the kpi-card" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersLineprogressKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersLineprogressKpiCard should reflect controller's data in embedded component" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersLineprogressKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersLineprogressKpiCardController getOrderLineProgressKpiData Default value should be undefined" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersLineprogressKpiCardController getOrderLineProgressKpiData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersLineprogressKpiCardController getOrderLineProgressKpiData should request a host update" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersLineprogressKpiCardController getOrderLineProgressKpiData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersLineprogressKpiCardController getOrderLineProgressKpiData orderLineProgressData be returned with expected value." time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersLineprogressKpiCardController getOrderLineProgressKpiData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersProgressKpiCardController getAggregateOrderProgressData should request a host update" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersProgressKpiCardController getAggregateOrderProgressData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersProgressKpiCardController getAggregateOrderProgressData should catch errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersProgressKpiCardController getAggregateOrderProgressData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersShippedKpiCardController getAggregateShippedData Default value should be undefined" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersShippedKpiCardController getAggregateShippedData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersShippedKpiCardController getAggregateShippedData orderShipped property set properly" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersShippedKpiCardController getAggregateShippedData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersShippedKpiCardController getAggregateShippedData should request a host update" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersShippedKpiCardController getAggregateShippedData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersThroughputKpiCard element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersThroughputKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersThroughputKpiCard should render the correct header for the kpi-card" time="0.011" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersThroughputKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersThroughputKpiCard should reflect controller's data in embedded component" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersThroughputKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersThroughputKpiCardController getThroughputRateKpiData Default value should be undefined" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersThroughputKpiCardController getThroughputRateKpiData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersThroughputKpiCardController getThroughputRateKpiData operatorCount be returned with expected value." time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersThroughputKpiCardController getThroughputRateKpiData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersThroughputKpiCardController getThroughputRateKpiData should handle errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersThroughputKpiCardController getThroughputRateKpiData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerLineProgressSeriesChartController getOrderLinesCustomerProgressSeries property chartData should not be null" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerLineProgressSeriesChartController getOrderLinesCustomerProgressSeries"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerLineProgressSeriesChartController getOrderLinesCustomerProgressSeries property chartData should return expected data" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerLineProgressSeriesChartController getOrderLinesCustomerProgressSeries"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerLineProgressSeriesChartController getOrderLinesCustomerProgressSeries should request a host update" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerLineProgressSeriesChartController getOrderLinesCustomerProgressSeries"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerLineProgressSeriesChartController getOrderLinesCustomerProgressSeries should catch errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerLineProgressSeriesChartController getOrderLinesCustomerProgressSeries"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerLineThroughputSeriesChartController getOrdersCustomerLineThroughputSeries property chartData should not be null" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerLineThroughputSeriesChartController getOrdersCustomerLineThroughputSeries"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerLineThroughputSeriesChartController getOrdersCustomerLineThroughputSeries property chartData should return expected data" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerLineThroughputSeriesChartController getOrdersCustomerLineThroughputSeries"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerLineThroughputSeriesChartController getOrdersCustomerLineThroughputSeries should request a host update" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerLineThroughputSeriesChartController getOrdersCustomerLineThroughputSeries"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerLineThroughputSeriesChartController getOrdersCustomerLineThroughputSeries should catch errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerLineThroughputSeriesChartController getOrdersCustomerLineThroughputSeries"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOutboundChartOrderProgressController getOrdersCustomerProgressSeriesData property chartData should not be null" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctOutboundChartOrderProgressController getOrdersCustomerProgressSeriesData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOutboundChartOrderProgressController getOrdersCustomerProgressSeriesData property chartData should return expected data" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctOutboundChartOrderProgressController getOrdersCustomerProgressSeriesData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOutboundChartOrderProgressController getOrdersCustomerProgressSeriesData should catch errors calling the api" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctOutboundChartOrderProgressController getOrdersCustomerProgressSeriesData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData OutboundOverviewChartController getThroughputRateChartData property chartData should not be null" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData OutboundOverviewChartController getThroughputRateChartData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData OutboundOverviewChartController getThroughputRateChartData property chartData should return expected data" time="0" classname=".InventoryForecastListController getInventoryForecastListData OutboundOverviewChartController getThroughputRateChartData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData OutboundOverviewChartController getThroughputRateChartData should catch errors calling the api" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData OutboundOverviewChartController getThroughputRateChartData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersPickLineThroughputSeriesChartController getOrdersPickLineThroughputSeries property chartData should not be null" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersPickLineThroughputSeriesChartController getOrdersPickLineThroughputSeries"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersPickLineThroughputSeriesChartController getOrdersPickLineThroughputSeries property chartData should return expected data" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersPickLineThroughputSeriesChartController getOrdersPickLineThroughputSeries"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersPickLineThroughputSeriesChartController getOrdersPickLineThroughputSeries should request a host update" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersPickLineThroughputSeriesChartController getOrdersPickLineThroughputSeries"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersPickLineThroughputSeriesChartController getOrdersPickLineThroughputSeries should catch errors calling the api" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersPickLineThroughputSeriesChartController getOrdersPickLineThroughputSeries"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerLineThroughputAreaKpiCardController getOrderCustomerLineThroughput Default value should be undefined" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerLineThroughputAreaKpiCardController getOrderCustomerLineThroughput"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerLineThroughputAreaKpiCardController getOrderCustomerLineThroughput orderLineThroughput should be returned with expected value." time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerLineThroughputAreaKpiCardController getOrderCustomerLineThroughput"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersCustomerLineThroughputAreaKpiCardController getOrderCustomerLineThroughput should handle errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersCustomerLineThroughputAreaKpiCardController getOrderCustomerLineThroughput"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersLineThroughputAreaKpiCardController getThroughputRateAreaKpiData Default value should be undefined" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersLineThroughputAreaKpiCardController getThroughputRateAreaKpiData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersLineThroughputAreaKpiCardController getThroughputRateAreaKpiData operatorCount be returned with expected value." time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersLineThroughputAreaKpiCardController getThroughputRateAreaKpiData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrdersLineThroughputAreaKpiCardController getThroughputRateAreaKpiData should handle errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrdersLineThroughputAreaKpiCardController getThroughputRateAreaKpiData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByAisleBarChartController getFaultAisleData should set faultsByAisleData equal to list of faults from response data" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByAisleBarChartController getFaultAisleData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByAisleBarChartController getFaultAisleData should requestUpdate from the host" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByAisleBarChartController getFaultAisleData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByAisleBarChartController getFaultAisleData should add start/end date filters to body" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByAisleBarChartController getFaultAisleData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByAisleBarChartController getFaultAisleData should add filters to body if they exist" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByAisleBarChartController getFaultAisleData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByAisleBarChartController getFaultAisleData should not add filters to body if none are given" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByAisleBarChartController getFaultAisleData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByAisleBarChartController getFaultAisleData should group faults by aisle" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByAisleBarChartController getFaultAisleData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceIdBarChartController getFaultDeviceIdData should set faultsByDeviceIdData equal to list of faults from response data" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceIdBarChartController getFaultDeviceIdData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceIdBarChartController getFaultDeviceIdData should requestUpdate from the host" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceIdBarChartController getFaultDeviceIdData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceIdBarChartController getFaultDeviceIdData should add start/end date filters to body" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceIdBarChartController getFaultDeviceIdData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceIdBarChartController getFaultDeviceIdData should add filters to body if they exist" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceIdBarChartController getFaultDeviceIdData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceIdBarChartController getFaultDeviceIdData should not add filters to body if none are given" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceIdBarChartController getFaultDeviceIdData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceIdBarChartController getFaultDeviceIdData should group faults by device id" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceIdBarChartController getFaultDeviceIdData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceTypeBarChartController getFaultDeviceTypeData should set faultsByDeviceTypeData equal to list of faults from response data" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceTypeBarChartController getFaultDeviceTypeData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceTypeBarChartController getFaultDeviceTypeData should requestUpdate from the host" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceTypeBarChartController getFaultDeviceTypeData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceTypeBarChartController getFaultDeviceTypeData should add start/end date filters to body" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceTypeBarChartController getFaultDeviceTypeData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceTypeBarChartController getFaultDeviceTypeData should add filters to body if they exist" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceTypeBarChartController getFaultDeviceTypeData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceTypeBarChartController getFaultDeviceTypeData should not add filters to body if none are given" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceTypeBarChartController getFaultDeviceTypeData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceTypeBarChartController getFaultDeviceTypeData should group faults by device type" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByDeviceTypeBarChartController getFaultDeviceTypeData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByLevelBarChartController getFaultLevelData should set faultsByLevelData equal to list of fault from response data" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByLevelBarChartController getFaultLevelData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByLevelBarChartController getFaultLevelData should requestUpdate from the host" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByLevelBarChartController getFaultLevelData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByLevelBarChartController getFaultLevelData should add start/end date filters to body" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByLevelBarChartController getFaultLevelData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByLevelBarChartController getFaultLevelData should add filters to body if they exist" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByLevelBarChartController getFaultLevelData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByLevelBarChartController getFaultLevelData should not add filters to body if none are given" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByLevelBarChartController getFaultLevelData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByLevelBarChartController getFaultLevelData should group faults by level" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByLevelBarChartController getFaultLevelData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByStatusBarChartController getFaultStatusData should set faultsByStatusData equal to list of faults from response data" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByStatusBarChartController getFaultStatusData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByStatusBarChartController getFaultStatusData should requestUpdate from the host" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByStatusBarChartController getFaultStatusData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByStatusBarChartController getFaultStatusData should add start/end date filters to body" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByStatusBarChartController getFaultStatusData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByStatusBarChartController getFaultStatusData should add filters to body if they exist" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByStatusBarChartController getFaultStatusData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByStatusBarChartController getFaultStatusData should not add filters to body if none are given" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByStatusBarChartController getFaultStatusData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByStatusBarChartController getFaultStatusData should group faults by status" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByStatusBarChartController getFaultStatusData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByStatusDurationBarChartController getFaultStatusDurationData should set faultsByStatusDurationData equal to list of faults from response data" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByStatusDurationBarChartController getFaultStatusDurationData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByStatusDurationBarChartController getFaultStatusDurationData should requestUpdate from the host" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByStatusDurationBarChartController getFaultStatusDurationData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByStatusDurationBarChartController getFaultStatusDurationData should add start/end date filters to body" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByStatusDurationBarChartController getFaultStatusDurationData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByStatusDurationBarChartController getFaultStatusDurationData should add filters to body if they exist" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByStatusDurationBarChartController getFaultStatusDurationData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByStatusDurationBarChartController getFaultStatusDurationData should not add filters to body if none are given" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByStatusDurationBarChartController getFaultStatusDurationData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByCategoryBarChart should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByCategoryBarChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsByCategoryBarChart should render correct heading" time="0.016" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsByCategoryBarChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData ict-faults-detail-table should be available" time="0" classname=".InventoryForecastListController getInventoryForecastListData ict-faults-detail-table"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData ict-faults-detail-table should render title within the element" time="0.026" classname=".InventoryForecastListController getInventoryForecastListData ict-faults-detail-table"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData ict-faults-detail-table should render the table" time="0.025" classname=".InventoryForecastListController getInventoryForecastListData ict-faults-detail-table"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsDetailTableController updateFilterOptions should return fault list data" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsDetailTableController updateFilterOptions"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsDetailTableController updateFilterOptions should transform Date Field to formatted string" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsDetailTableController updateFilterOptions"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsDetailTableController updateFilterOptions should request a host update" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsDetailTableController updateFilterOptions"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFaultsDetailTableController updateFilterOptions should merge page filters and table filters" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctFaultsDetailTableController updateFilterOptions"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData ApiRequestFilterBuilder build with no filters." time="0" classname=".InventoryForecastListController getInventoryForecastListData ApiRequestFilterBuilder"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData ApiRequestFilterBuilder build single level-filter options." time="0.001" classname=".InventoryForecastListController getInventoryForecastListData ApiRequestFilterBuilder"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData ApiRequestFilterBuilder build multiple level-filter options." time="0" classname=".InventoryForecastListController getInventoryForecastListData ApiRequestFilterBuilder"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData ApiRequestFilterBuilder build multiple aisle-filter options." time="0.001" classname=".InventoryForecastListController getInventoryForecastListData ApiRequestFilterBuilder"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData ApiRequestFilterBuilder build multiple device-type-filter options." time="0" classname=".InventoryForecastListController getInventoryForecastListData ApiRequestFilterBuilder"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData ApiRequestFilterBuilder build multiple status-filter options." time="0" classname=".InventoryForecastListController getInventoryForecastListData ApiRequestFilterBuilder"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData ApiRequestFilterBuilder build combined single filter types." time="0.001" classname=".InventoryForecastListController getInventoryForecastListData ApiRequestFilterBuilder"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData ApiRequestFilterBuilder build combined single filter and multiple types." time="0" classname=".InventoryForecastListController getInventoryForecastListData ApiRequestFilterBuilder"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationMetricsOperatorsKpiCard renders correctly" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationMetricsOperatorsKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationMetricsOperatorsKpiCard shows loading state initially" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationMetricsOperatorsKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationMetricsOperatorsKpiCard should render heading within the element" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationMetricsOperatorsKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationMetricsOperatorsKpiCard should render ict-workstation-metrics-operators-kpi-card within the element" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationMetricsOperatorsKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationMetricsOperatorsKpiCardController getLoggedinOperatorsData default value should be undefined" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationMetricsOperatorsKpiCardController getLoggedinOperatorsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationMetricsOperatorsKpiCardController getLoggedinOperatorsData should request a host update" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationMetricsOperatorsKpiCardController getLoggedinOperatorsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationMetricsOperatorsKpiCardController getLoggedinOperatorsData should return logged in operator target lines per hour value" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationMetricsOperatorsKpiCardController getLoggedinOperatorsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationMetricsOperatorsKpiCardController getLoggedinOperatorsData should return logged in operator actual lines per hour value" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationMetricsOperatorsKpiCardController getLoggedinOperatorsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationMetricsOperatorsKpiCardController getLoggedinOperatorsData should handle errors calling the api" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationMetricsOperatorsKpiCardController getLoggedinOperatorsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationKpiTotalWorkstations renders correctly" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationKpiTotalWorkstations"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationKpiTotalWorkstations should render title within the element" time="0.007" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationKpiTotalWorkstations"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationKpiTotalWorkstations should render ict-workstation-kpi-total-workstations within the element" time="0.007" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationKpiTotalWorkstations"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData WorkstationKpiTotalWorkstationsController getTotalWorkstationsData default value should be undefined" time="0" classname=".InventoryForecastListController getInventoryForecastListData WorkstationKpiTotalWorkstationsController getTotalWorkstationsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData WorkstationKpiTotalWorkstationsController getTotalWorkstationsData should request a host update" time="0" classname=".InventoryForecastListController getInventoryForecastListData WorkstationKpiTotalWorkstationsController getTotalWorkstationsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData WorkstationKpiTotalWorkstationsController getTotalWorkstationsData should return total workstation count with expected value" time="0" classname=".InventoryForecastListController getInventoryForecastListData WorkstationKpiTotalWorkstationsController getTotalWorkstationsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData WorkstationKpiTotalWorkstationsController getTotalWorkstationsData should return total workstation count with expected workstationMode values" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData WorkstationKpiTotalWorkstationsController getTotalWorkstationsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData WorkstationKpiTotalWorkstationsController getTotalWorkstationsData should return total workstation count with expected status values" time="0" classname=".InventoryForecastListController getInventoryForecastListData WorkstationKpiTotalWorkstationsController getTotalWorkstationsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData WorkstationKpiTotalWorkstationsController getTotalWorkstationsData should handle errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData WorkstationKpiTotalWorkstationsController getTotalWorkstationsData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationHealthKpiCard renders correctly" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationHealthKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationHealthKpiCard shows loading state initially" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationHealthKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationHealthKpiCard should render ict-workstation-health-kpi-card within the element" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationHealthKpiCard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationHealthKpiCardController getStationHealthData default value should be undefined" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationHealthKpiCardController getStationHealthData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationHealthKpiCardController getStationHealthData should request a host update" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationHealthKpiCardController getStationHealthData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationHealthKpiCardController getStationHealthData should return total downtime" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationHealthKpiCardController getStationHealthData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationHealthKpiCardController getStationHealthData should handle errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationHealthKpiCardController getStationHealthData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationKpiPerformance renders correctly" time="0.007" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationKpiPerformance"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationKpiPerformance should render title within the element" time="0.007" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationKpiPerformance"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationKpiPerformance should render ict-workstation-kpi-performance within the element" time="0.007" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationKpiPerformance"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationKpiPerformanceController getWorkstationPerformanceData default value should be undefined" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationKpiPerformanceController getWorkstationPerformanceData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationKpiPerformanceController getWorkstationPerformanceData should request a host update" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationKpiPerformanceController getWorkstationPerformanceData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationKpiPerformanceController getWorkstationPerformanceData should return workstation performance with expected active time value" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationKpiPerformanceController getWorkstationPerformanceData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationKpiPerformanceController getWorkstationPerformanceData should return workstation performance with expected starved time value" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationKpiPerformanceController getWorkstationPerformanceData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationKpiPerformanceController getWorkstationPerformanceData should handle errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationKpiPerformanceController getWorkstationPerformanceData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationCardView should render correctly" time="0.003" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationCardView"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationCardView should render the card with expected elements" time="0.016" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationCardView"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationCardView should render the correct number of workstation cards" time="0.016" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationCardView"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationCardView should update the workstation status correctly when a menu item is selected" time="0.015" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationCardView"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationCardView should apply filters correctly" time="0.015" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationCardView"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationCardViewLineChart should initialize and render correctly" time="0.024" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationCardViewLineChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationCardViewLineChart should render loading state initially" time="0.018" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationCardViewLineChart"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData WorkstationCardViewLineChartController getChartData should initialize with the default metric" time="0" classname=".InventoryForecastListController getInventoryForecastListData WorkstationCardViewLineChartController getChartData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData WorkstationCardViewLineChartController getChartData should fetch and process chart data" time="0" classname=".InventoryForecastListController getInventoryForecastListData WorkstationCardViewLineChartController getChartData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData WorkstationCardViewLineChartController getChartData should fetch workstation data" time="0" classname=".InventoryForecastListController getInventoryForecastListData WorkstationCardViewLineChartController getChartData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData WorkstationCardViewLineChartController getChartData should update the graph description" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData WorkstationCardViewLineChartController getChartData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData WorkstationCardViewLineChartController getChartData should not change the metric if the new metric is the same as the current one" time="0" classname=".InventoryForecastListController getInventoryForecastListData WorkstationCardViewLineChartController getChartData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData WorkstationCardViewLineChartController getChartData should return the correct chart options" time="0" classname=".InventoryForecastListController getInventoryForecastListData WorkstationCardViewLineChartController getChartData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationCardViewFilters should render loading state initially" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationCardViewFilters"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationCardViewFilters should set up event listeners on connectedCallback" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationCardViewFilters"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationCardViewFilters should remove event listeners on disconnectedCallback" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationCardViewFilters"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationCardViewFilters should handle page filter refresh data event" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationCardViewFilters"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationCardViewFilters should reset filters correctly" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationCardViewFilters"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData WorkstationCardViewFiltersController init should initialize filters correctly" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData WorkstationCardViewFiltersController init"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData WorkstationCardViewFiltersController addFilter should add a filter with correct properties" time="0" classname=".InventoryForecastListController getInventoryForecastListData WorkstationCardViewFiltersController addFilter"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData WorkstationCardViewFiltersController fetchFilterItems should fetch filter items correctly" time="0" classname=".InventoryForecastListController getInventoryForecastListData WorkstationCardViewFiltersController fetchFilterItems"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationListTable renders correctly" time="0.035" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationListTable should render title within the element" time="0.036" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationListTable should render ict-workstation-list-table within the element" time="0.035" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationListTable should disable all workstations" time="0.034" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationListTable should enable all workstations" time="0.034" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationListTable should clear selected workstations" time="0.035" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctWorkstationListTable should toggle charts visibility" time="0.035" classname=".InventoryForecastListController getInventoryForecastListData IctWorkstationListTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData WorkstationListTableController fetchData should return workstation list data" time="0" classname=".InventoryForecastListController getInventoryForecastListData WorkstationListTableController fetchData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData WorkstationListTableController fetchData should transform seconds to formatted string for specific properties" time="0" classname=".InventoryForecastListController getInventoryForecastListData WorkstationListTableController fetchData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData WorkstationListTableController fetchData should replace null or empty string values with &quot;--&quot;" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData WorkstationListTableController fetchData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData WorkstationListTableController fetchData should request a host update" time="0" classname=".InventoryForecastListController getInventoryForecastListData WorkstationListTableController fetchData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData WorkstationListTableController fetchData should handle errors calling the api" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData WorkstationListTableController fetchData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyPerformance element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDailyPerformance"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyPerformance should reflect controllers data in the table" time="0.031" classname=".InventoryForecastListController getInventoryForecastListData IctDailyPerformance"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyPerformance apply button should be disabled by default" time="0.03" classname=".InventoryForecastListController getInventoryForecastListData IctDailyPerformance"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyPerformance Clicking &quot;Export as Excel&quot; opens the export dialog" time="0.036" classname=".InventoryForecastListController getInventoryForecastListData IctDailyPerformance"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyPerformance &quot;Export&quot; should be disable if inputs are invalid" time="6.055" classname=".InventoryForecastListController getInventoryForecastListData IctDailyPerformance"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyPerformanceController getWorkstations should make a request to the &quot;GetWorkstations&quot; endpoint" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDailyPerformanceController getWorkstations"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyPerformanceController getWorkstations should set the workstations property to the response data" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDailyPerformanceController getWorkstations"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyPerformanceController getWorkstations should catch errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDailyPerformanceController getWorkstations"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyPerformanceController getWorkstations should request a host update" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDailyPerformanceController getWorkstations"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyPerformanceController getDailyPerformanceData should make a request to the &quot;PostWorkstationDailyPerformanceList&quot; endpoint" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDailyPerformanceController getDailyPerformanceData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyPerformanceController getDailyPerformanceData should set the performanceData property to the response data" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDailyPerformanceController getDailyPerformanceData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyPerformanceController getDailyPerformanceData should catch errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDailyPerformanceController getDailyPerformanceData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDailyPerformanceController getDailyPerformanceData should request a host update" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDailyPerformanceController getDailyPerformanceData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryDateFilter should render with 4 items" time="0.003" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryDateFilter"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctInventoryDateFilter should render with default selectedItemValue value of 3" time="0.002" classname=".InventoryForecastListController getInventoryForecastListData IctInventoryDateFilter"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DepartmentFilterController getDepartmentsAsync should set departmentFilterTable" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DepartmentFilterController getDepartmentsAsync"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DepartmentFilterController getDepartmentsAsync should order items in departmentFilterTable alphabetically with All at the front" time="0" classname=".InventoryForecastListController getInventoryForecastListData DepartmentFilterController getDepartmentsAsync"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DepartmentFilterController getDepartmentsAsync should default to All when an error occurs" time="0" classname=".InventoryForecastListController getInventoryForecastListData DepartmentFilterController getDepartmentsAsync"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDepartmentFilter should call the controller on first update" time="0.004" classname=".InventoryForecastListController getInventoryForecastListData IctDepartmentFilter"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDepartmentFilter should map departments into DguComboBoxInterface" time="0.003" classname=".InventoryForecastListController getInventoryForecastListData IctDepartmentFilter"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData HighImpactSkusController getHighImpactSkus should set highImpactSkus equal to list of skus from response data" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData HighImpactSkusController getHighImpactSkus"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData HighImpactSkusController getHighImpactSkus should requestUpdate from the host" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData HighImpactSkusController getHighImpactSkus"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData HighImpactSkusController getHighImpactSkus should add start/end date filters to body" time="0" classname=".InventoryForecastListController getInventoryForecastListData HighImpactSkusController getHighImpactSkus"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData HighImpactSkusController getHighImpactSkus should add sorts to body if they exist" time="0" classname=".InventoryForecastListController getInventoryForecastListData HighImpactSkusController getHighImpactSkus"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData HighImpactSkusController getHighImpactSkus fetchData should not call getHighImpactSkus if missing departmentFilter and dateFilter" time="0" classname=".InventoryForecastListController getInventoryForecastListData HighImpactSkusController getHighImpactSkus"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctHighImpactSkuTable custom element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctHighImpactSkuTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctHighImpactSkuTable does not call controller on first update" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData IctHighImpactSkuTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctHighImpactSkuTable does not call controller if date filter isnt set" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData IctHighImpactSkuTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctHighImpactSkuTable should call controller when both date filter and dept filter are provided" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData IctHighImpactSkuTable"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-inventory-accuracy-performance> custom element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-inventory-accuracy-performance>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-inventory-accuracy-performance> should render the correct header for the dgu-statistic" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-inventory-accuracy-performance>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-inventory-accuracy-performance> should reflect controllers data in embedded component when filters are set" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-inventory-accuracy-performance>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-inventory-accuracy-performance> controller is only called once the filters are set" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-inventory-accuracy-performance>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData InventoryAccuracyPerformanceController getInventoryAccuracyData should make a request to &quot;GetInventoryAccuracy&quot;" time="0" classname=".InventoryForecastListController getInventoryForecastListData InventoryAccuracyPerformanceController getInventoryAccuracyData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData InventoryAccuracyPerformanceController getInventoryAccuracyData should set inventoryAccuracyData to the expected data" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData InventoryAccuracyPerformanceController getInventoryAccuracyData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData InventoryAccuracyPerformanceController getInventoryAccuracyData should request a host update" time="0" classname=".InventoryForecastListController getInventoryForecastListData InventoryAccuracyPerformanceController getInventoryAccuracyData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData InventoryAccuracyPerformanceController getInventoryAccuracyData should catch errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData InventoryAccuracyPerformanceController getInventoryAccuracyData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-order-fulfillment-rate> custom element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-order-fulfillment-rate>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-order-fulfillment-rate> should render the correct header for the dgu-statistic" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-order-fulfillment-rate>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-order-fulfillment-rate> should reflect controllers data in embedded component when filters are set" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-order-fulfillment-rate>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-order-fulfillment-rate> controller is only called once the filters are set" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-order-fulfillment-rate>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrderFulfillmentRateController getOrderFulfillmentRate should set orderFulfillmentMetricData to the expected data" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrderFulfillmentRateController getOrderFulfillmentRate"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrderFulfillmentRateController getOrderFulfillmentRate should request a host update" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrderFulfillmentRateController getOrderFulfillmentRate"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctOrderFulfillmentRateController getOrderFulfillmentRate should catch errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctOrderFulfillmentRateController getOrderFulfillmentRate"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-stock-out> custom element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-stock-out>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-stock-out> should render the correct header for the ict-metric-card" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-stock-out>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-stock-out> should reflect controllers data in embedded component when filters are set" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-stock-out>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-stock-out> controller is only called once the filters are set" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-stock-out>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctStockOutController getStockOut should make a request to the &quot;GetInventoryStockDistributionNoPercentageSeries&quot; endpoint" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctStockOutController getStockOut"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctStockOutController getStockOut should set stockOutMetricData to the expected data" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctStockOutController getStockOut"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctStockOutController getStockOut should request a host update" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctStockOutController getStockOut"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctStockOutController getStockOut should catch errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctStockOutController getStockOut"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctStorageUtilization custom element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctStorageUtilization"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctStorageUtilization should render the correct header for the kpi-card" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData IctStorageUtilization"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctStorageUtilization should reflect controllers data in embedded component when filters are set" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData IctStorageUtilization"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctStorageUtilization controller is only called once the filters are set" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData IctStorageUtilization"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctStorageUtilizationController getStorageUtilization should make a request to the &quot;GetInventoryStorageUtilization&quot; endpoint" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctStorageUtilizationController getStorageUtilization"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctStorageUtilizationController getStorageUtilization should set storageUtilizationMetricData to the expected data" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctStorageUtilizationController getStorageUtilization"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctStorageUtilizationController getStorageUtilization should request a host update" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctStorageUtilizationController getStorageUtilization"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctStorageUtilizationController getStorageUtilization should catch errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctStorageUtilizationController getStorageUtilization"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-sku-event-detail-list-drawer> custom element should be registered" time="0" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-sku-event-detail-list-drawer>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-sku-event-detail-list-drawer> should render the correct title" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-sku-event-detail-list-drawer>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData &lt;ict-sku-event-detail-list-drawer> should render the table" time="0.015" classname=".InventoryForecastListController getInventoryForecastListData &lt;ict-sku-event-detail-list-drawer>"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuEventDetailListDrawerController getSkuEventDetailList should make a request to &quot;PostInventoryContainerEventsListStub&quot; to return sku detail information" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctSkuEventDetailListDrawerController getSkuEventDetailList"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuEventDetailListDrawerController getSkuEventDetailList should catch errors calling the api" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctSkuEventDetailListDrawerController getSkuEventDetailList"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctSkuEventDetailListDrawerController getSkuEventDetailList should transform timestamps correctly" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctSkuEventDetailListDrawerController getSkuEventDetailList"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData PerformanceAnalysisTableauController should initialize correctly" time="0" classname=".InventoryForecastListController getInventoryForecastListData PerformanceAnalysisTableauController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData PerformanceAnalysisTableauController getTableauMenuItems should populate menuItems with Tableau workbooks and views" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData PerformanceAnalysisTableauController getTableauMenuItems"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData PerformanceAnalysisTableauController getTableauMenuItems should handle case where no top level project ID is set" time="0" classname=".InventoryForecastListController getInventoryForecastListData PerformanceAnalysisTableauController getTableauMenuItems"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData PerformanceAnalysisTableauController getTableauMenuItems should handle case where no matching top level project is found" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData PerformanceAnalysisTableauController getTableauMenuItems"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData PerformanceAnalysisTableauController getTableauMenuItems should handle case where user does not have access to Tableau" time="0" classname=".InventoryForecastListController getInventoryForecastListData PerformanceAnalysisTableauController getTableauMenuItems"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDetailPanelMetricInfo should be registered as a custom element" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDetailPanelMetricInfo"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDetailPanelMetricInfo should render the correct label and value" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctDetailPanelMetricInfo"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDetailPanelMetricInfo should update the rendered content when attributes change" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDetailPanelMetricInfo"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlow should be available" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlow"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlow should render correctly" time="0.011" classname=".InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlow"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlow should render the main panel and detail panel" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlow"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlow should handle node selection" time="0.01" classname=".InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlow"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlow should handle edge selection" time="0.011" classname=".InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlow"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlow should clear selection" time="0.009" classname=".InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlow"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDetailPanel should be registered as a custom element" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDetailPanel"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDetailPanel should render the title" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData IctDetailPanel"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDetailPanel should close detail panel when close button is clicked" time="0.012" classname=".InventoryForecastListController getInventoryForecastListData IctDetailPanel"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDetailPanel should handle selected panel change event" time="0.008" classname=".InventoryForecastListController getInventoryForecastListData IctDetailPanel"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDetailPanelController should initialize with empty metricGroups" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDetailPanelController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDetailPanelController should get detail panel config for an element" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDetailPanelController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDetailPanelController should return empty array if no config found for an element" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDetailPanelController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDetailPanelController should process metrics with default visibility" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctDetailPanelController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDetailPanelController should process metrics according to config" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDetailPanelController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctDetailPanelController should log error if getMetricsForElement fails" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctDetailPanelController"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData toggleMetricGroupVisibility should toggle the visibility of the metric group with the given title" time="0" classname=".InventoryForecastListController getInventoryForecastListData toggleMetricGroupVisibility"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData toggleMetricGroupVisibility should not change the visibility of other metric groups" time="0" classname=".InventoryForecastListController getInventoryForecastListData toggleMetricGroupVisibility"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData toggleMetricGroupVisibility should return the same array if the title is not found" time="0" classname=".InventoryForecastListController getInventoryForecastListData toggleMetricGroupVisibility"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctNodeAlerts renders without crashing" time="0.006" classname=".InventoryForecastListController getInventoryForecastListData IctNodeAlerts"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctNodeAlerts renders the correct number of alerts" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctNodeAlerts"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctCustomControls should render custom controls" time="0.011" classname=".InventoryForecastListController getInventoryForecastListData IctCustomControls"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctCustomControls should render custom admin controls" time="0.003" classname=".InventoryForecastListController getInventoryForecastListData IctCustomControls"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctCustomControls should handle zoom in and zoom out" time="0.005" classname=".InventoryForecastListController getInventoryForecastListData IctCustomControls"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctCustomControls should handle Interactive Mode Toggle" time="0.003" classname=".InventoryForecastListController getInventoryForecastListData IctCustomControls"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData NodeBreadcrumbs renders without crashing" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData NodeBreadcrumbs"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData NodeBreadcrumbs handles breadcrumb click" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData NodeBreadcrumbs"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlowChartLitWrapperController mapEdgesToReactFlowEdges should map edges with metrics to ReactFlow edges" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlowChartLitWrapperController mapEdgesToReactFlowEdges"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlowChartLitWrapperController mapEdgesToReactFlowEdges should filter out edges without metrics" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlowChartLitWrapperController mapEdgesToReactFlowEdges"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlowChartLitWrapperController mapEdgesToReactFlowEdges should format metric value correctly" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlowChartLitWrapperController mapEdgesToReactFlowEdges"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlowChartLitWrapperController getProcessFlowData should fetch process flow data by nodeId" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlowChartLitWrapperController getProcessFlowData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlowChartLitWrapperController getProcessFlowData should fetch process flow data without nodeId" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlowChartLitWrapperController getProcessFlowData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlowChartLitWrapperController getProcessFlowData should handle error when areas or edges are missing" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctFacilityProcessFlowChartLitWrapperController getProcessFlowData"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData test resolveNodeBreadcrumbName utility function to ensure proper breadcrumb labels should display All if no particular parentnode name" time="0" classname=".InventoryForecastListController getInventoryForecastListData test resolveNodeBreadcrumbName utility function to ensure proper breadcrumb labels"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData test resolveNodeBreadcrumbName utility function to ensure proper breadcrumb labels second breadcrumb label should be Aisles for multishuttle" time="0" classname=".InventoryForecastListController getInventoryForecastListData test resolveNodeBreadcrumbName utility function to ensure proper breadcrumb labels"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData test resolveNodeBreadcrumbName utility function to ensure proper breadcrumb labels second breadcrumb label should be Aisles for Mini Load" time="0" classname=".InventoryForecastListController getInventoryForecastListData test resolveNodeBreadcrumbName utility function to ensure proper breadcrumb labels"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData test resolveNodeBreadcrumbName utility function to ensure proper breadcrumb labels second breadcrumb label should be Stations for recontainerizing" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData test resolveNodeBreadcrumbName utility function to ensure proper breadcrumb labels"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DmsHistoricalMovementsFaultsOverviewDashboard TableauDashboard width is being set as expected" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DmsHistoricalMovementsFaultsOverviewDashboard TableauDashboard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DmsHistoricalMovementsFaultsOverviewDashboard TableauDashboard DmsHistoricalMovementsFaultsOverviewDashboard height is being set as expected" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DmsHistoricalMovementsFaultsOverviewDashboard TableauDashboard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DmsHistoricalMovementsFaultsOverviewDashboard TableauDashboard DmsHistoricalMovementsFaultsOverviewDashboard url is being set as expected" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DmsHistoricalMovementsFaultsOverviewDashboard TableauDashboard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DmsMovementsFaultsDetailsDashboard TableauDashboard width is being set as expected" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DmsMovementsFaultsDetailsDashboard TableauDashboard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DmsMovementsFaultsDetailsDashboard TableauDashboard DmsMovementsFaultsDetailsDashboard height is being set as expected" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DmsMovementsFaultsDetailsDashboard TableauDashboard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DmsMovementsFaultsDetailsDashboard TableauDashboard DmsMovementsFaultsDetailsDashboard url is being set as expected" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DmsMovementsFaultsDetailsDashboard TableauDashboard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DmsMovementsFaultsOverviewDashboard TableauDashboard width is being set as expected" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DmsMovementsFaultsOverviewDashboard TableauDashboard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DmsMovementsFaultsOverviewDashboard TableauDashboard DmsMovementsFaultsOverviewDashboard height is being set as expected" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DmsMovementsFaultsOverviewDashboard TableauDashboard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DmsMovementsFaultsOverviewDashboard TableauDashboard DmsMovementsFaultsOverviewDashboard url is being set as expected" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DmsMovementsFaultsOverviewDashboard TableauDashboard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DmsMovementsOverviewDashboard TableauDashboard width is being set as expected" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DmsMovementsOverviewDashboard TableauDashboard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DmsMovementsOverviewDashboard TableauDashboard DmsMovementsOverviewDashboard height is being set as expected" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DmsMovementsOverviewDashboard TableauDashboard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DmsMovementsOverviewDashboard TableauDashboard DmsMovementsOverviewDashboard url is being set as expected" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DmsMovementsOverviewDashboard TableauDashboard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DmsShuttleHardwareAnalysisDashboard TableauDashboard width is being set as expected" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DmsShuttleHardwareAnalysisDashboard TableauDashboard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DmsShuttleHardwareAnalysisDashboard TableauDashboard DmsShuttleHardwareAnalysisDashboard height is being set as expected" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DmsShuttleHardwareAnalysisDashboard TableauDashboard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DmsShuttleHardwareAnalysisDashboard TableauDashboard DmsShuttleHardwareAnalysisDashboard url is being set as expected" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DmsShuttleHardwareAnalysisDashboard TableauDashboard"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData TableauApiClient signIn should return a successful signIn in response." time="0" classname=".InventoryForecastListController getInventoryForecastListData TableauApiClient signIn">
    <skipped/>
  </testcase>
  <testcase name="InventoryForecastListController getInventoryForecastListData TableauApiClient getWorkbooks should be able to get a list of workbooks." time="0" classname=".InventoryForecastListController getInventoryForecastListData TableauApiClient getWorkbooks">
    <skipped/>
  </testcase>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctBreadcrumbMixin should initialize breadcrumbs as an empty array" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctBreadcrumbMixin"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctBreadcrumbMixin should set breadcrumbs correctly" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctBreadcrumbMixin"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctBreadcrumbMixin should render breadcrumbs correctly" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctBreadcrumbMixin"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctBreadcrumbMixin should call navigate with the correct route on breadcrumb click" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctBreadcrumbMixin"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctBreadcrumbMixin should handle multiple breadcrumb clicks" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData IctBreadcrumbMixin"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IctBreadcrumbMixin should bind the breadcrumb click handler correctly" time="0" classname=".InventoryForecastListController getInventoryForecastListData IctBreadcrumbMixin"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData ApplicationConfigError should contain the message" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData ApplicationConfigError"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IsTrue subtracting 1 to be true" time="0" classname=".InventoryForecastListController getInventoryForecastListData IsTrue"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IsTrue subtracting true to be true" time="0" classname=".InventoryForecastListController getInventoryForecastListData IsTrue"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IsTrue subtracting True to be true" time="0" classname=".InventoryForecastListController getInventoryForecastListData IsTrue"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IsTrue subtracting TRUE to be true" time="0" classname=".InventoryForecastListController getInventoryForecastListData IsTrue"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IsTrue subtracting 0 to be false" time="0" classname=".InventoryForecastListController getInventoryForecastListData IsTrue"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData IsTrue subtracting False to be false" time="0" classname=".InventoryForecastListController getInventoryForecastListData IsTrue"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData FormatUtils roundNumber will round a number value." time="0" classname=".InventoryForecastListController getInventoryForecastListData FormatUtils roundNumber"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils subtracting 1 from 2019-03-13T00:00:00.000Z should give back 2019-02-13T00:00:00.000Z" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils subtracting 4 from 2019-03-13T00:00:00.000Z should give back 2018-11-13T00:00:00.000Z" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils subtracting 12 from 2019-03-13T00:00:00.000Z should give back 2018-03-13T00:00:00.000Z" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils subtracting 6 from 2019-03-13T00:00:00.000Z should give back 2018-09-12T23:00:00.000Z" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils setting date to start of month 2019-03-01T00:00:00.000Z" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils setting date to start of month 2018-11-01T00:00:00.000Z" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils setting date to start of month 2017-08-31T23:00:00.000Z" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils should format UTC time to human readable HH:MM:SS format" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils should format UTC time to human readable HH:MM:SS format" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils should format UTC time to human readable HH:MM:SS format" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils should format UTC time to human readable HH:MM:SS format" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils should format UTC time to human readable HH:MM:SS format" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils should format UTC time to human readable HH:MM format" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils should format UTC time to human readable HH:MM format" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils should format UTC time to human readable HH:MM format" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils should format UTC time to human readable HH:MM format" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils should format UTC time to human readable HH:MM format" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils should format UTC time to human readable HH:MM:SS AM/PM, MM/DD/YYYY format" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils should format UTC time to human readable HH:MM:SS AM/PM, MM/DD/YYYY format" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils should format UTC time to human readable HH:MM:SS AM/PM, MM/DD/YYYY format" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils should format UTC time to human readable HH:MM:SS AM/PM, MM/DD/YYYY format" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils should format UTC time to human readable HH:MM:SS AM/PM, MM/DD/YYYY format" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils should form UTC time to human readable MM/DD/YYYY HH:MM format" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils should form UTC time to human readable MM/DD/YYYY HH:MM format" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils should form UTC time to human readable MM/DD/YYYY HH:MM format" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils should form UTC time to human readable MM/DD/YYYY HH:MM format" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils should form UTC time to human readable MM/DD/YYYY HH:MM format" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils formatISOTimeToReadableDateTime Should return &quot;Invalid Date&quot; for Invalid Date" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils formatISOTimeToReadableDateTime"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils formatISOTimeToReadableDateTime Should return &quot;Invalid Date&quot; for " time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils formatISOTimeToReadableDateTime"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils formatISOTimeToReadableDateTime Should return &quot;Invalid Date&quot; for 2024 jan 19th" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils formatISOTimeToReadableDateTime"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils formatISOTimeToReadableDateTime Should return valid formatted date given valid iso date string" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DateUtils formatISOTimeToReadableDateTime"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils formatJSDateToReadableDateTimeWithTimezone Should return valid formatted date with timezone given valid js date" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils formatJSDateToReadableDateTimeWithTimezone"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils toLocaleDateStringNoTimezone should return the correct date by ignoring tz" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils toLocaleDateStringNoTimezone"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils toLocaleDateStringNoTimezone should return the correct date if no tz provided" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils toLocaleDateStringNoTimezone"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils formatISOTimeToDate should Formats the UTC timestamp to a human readable date format" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData DateUtils formatISOTimeToDate"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils formatISOTimeToMMDD should Formats the UTC timestamp to a human-readable date format" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils formatISOTimeToMMDD"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils formatISOTimeToYYYYMMDD should Formats the UTC timestamp to a human-readable date format" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils formatISOTimeToYYYYMMDD"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils formatISOTimeToYYYYMMDD should return Invalid Date when no site date is not valid." time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils formatISOTimeToYYYYMMDD"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils formatISOTimeToAbbrDayMonthDate should format a valid ISO timestamp correctly" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils formatISOTimeToAbbrDayMonthDate"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData DateUtils formatISOTimeToAbbrDayMonthDate should return &quot;Invalid Date&quot; for an invalid ISO string" time="0" classname=".InventoryForecastListController getInventoryForecastListData DateUtils formatISOTimeToAbbrDayMonthDate"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData FormatUtils roundNumber will take input value of 98.782 with precision of 0 and sanitize it to 99 value" time="0.001" classname=".InventoryForecastListController getInventoryForecastListData FormatUtils roundNumber"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData FormatUtils roundNumber will take input value of 5.123 with precision of 0 and sanitize it to 5 value" time="0" classname=".InventoryForecastListController getInventoryForecastListData FormatUtils roundNumber"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData FormatUtils roundNumber will take input value of 0.0123 with precision of 3 and sanitize it to 0.012 value" time="0" classname=".InventoryForecastListController getInventoryForecastListData FormatUtils roundNumber"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData FormatUtils roundNumber will take input value of -540.987213 with precision of 7 and sanitize it to -540.987213 value" time="0" classname=".InventoryForecastListController getInventoryForecastListData FormatUtils roundNumber"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData PositiveNumberChecker validatePositiveNumber will take input value of 98.782 and true display whether the value is positive" time="0" classname=".InventoryForecastListController getInventoryForecastListData PositiveNumberChecker validatePositiveNumber"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData PositiveNumberChecker validatePositiveNumber will take input value of -5.123 and false display whether the value is positive" time="0" classname=".InventoryForecastListController getInventoryForecastListData PositiveNumberChecker validatePositiveNumber"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData PositiveNumberChecker validatePositiveNumber will take input value of 0.0123 and true display whether the value is positive" time="0" classname=".InventoryForecastListController getInventoryForecastListData PositiveNumberChecker validatePositiveNumber"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData PositiveNumberChecker validatePositiveNumber will take input value of -540.987213 and false display whether the value is positive" time="0" classname=".InventoryForecastListController getInventoryForecastListData PositiveNumberChecker validatePositiveNumber"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData InventoryForecastPDFGenerator should add page header to PDF" time="0.004" classname=".InventoryForecastListController getInventoryForecastListData InventoryForecastPDFGenerator"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData InventoryForecastPDFGenerator should add page footer to PDF" time="0" classname=".InventoryForecastListController getInventoryForecastListData InventoryForecastPDFGenerator"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData conversions secondsToMinutes will take input value of 60 convert to 1 minutes" time="0" classname=".InventoryForecastListController getInventoryForecastListData conversions secondsToMinutes"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData conversions secondsToMinutes will take input value of 30 convert to 0.5 minutes" time="0" classname=".InventoryForecastListController getInventoryForecastListData conversions secondsToMinutes"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData conversions secondsToMinutes will take input value undefined and return undefined" time="0" classname=".InventoryForecastListController getInventoryForecastListData conversions secondsToMinutes"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData conversions secondsToMinutes will take input value 0 and return 0" time="0" classname=".InventoryForecastListController getInventoryForecastListData conversions secondsToMinutes"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData conversions toHoursAndMinutes will take input value of 0 convert to 0 mins formatted string" time="0" classname=".InventoryForecastListController getInventoryForecastListData conversions toHoursAndMinutes"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData conversions toHoursAndMinutes will take input value of 60 convert to 1 hr formatted string" time="0" classname=".InventoryForecastListController getInventoryForecastListData conversions toHoursAndMinutes"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData conversions toHoursAndMinutes will take input value of 120 convert to 2 hrs formatted string" time="0" classname=".InventoryForecastListController getInventoryForecastListData conversions toHoursAndMinutes"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData conversions toHoursAndMinutes will take input value of 65 convert to 1 hr 5 mins formatted string" time="0" classname=".InventoryForecastListController getInventoryForecastListData conversions toHoursAndMinutes"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData conversions toHoursAndMinutes will take input value of 61 convert to 1 hr 1 min formatted string" time="0" classname=".InventoryForecastListController getInventoryForecastListData conversions toHoursAndMinutes"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData conversions formatSecondsToHoursMinutes will take input value of 0 convert to 0 mins formatted string" time="0" classname=".InventoryForecastListController getInventoryForecastListData conversions formatSecondsToHoursMinutes"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData conversions formatSecondsToHoursMinutes will take input value of 3600 convert to 1 hrs formatted string" time="0" classname=".InventoryForecastListController getInventoryForecastListData conversions formatSecondsToHoursMinutes"/>
  <testcase name="InventoryForecastListController getInventoryForecastListData conversions formatSecondsToHoursMinutes will take input value of 8790 convert to 2 hrs 26 mins formatted string" time="0" classname=".InventoryForecastListController getInventoryForecastListData conversions formatSecondsToHoursMinutes"/>
  <system-out>
    <![CDATA[
]]>
  </system-out>
  <system-err/>
</testsuite>