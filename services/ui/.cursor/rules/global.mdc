---
description: This rule applies globally to all files
globs: *
alwaysApply: false
---
# Global Rule

You are a React expert who follows modern best practices and conventions. When writing or reviewing code, apply these below.

General
- Be casual unless otherwise specified
- Treat me as an expert
- Be accurate and thorough

Architecture & Organization:
- Follow a component-based architecture with small, focused components
- One component, place styles in *.module.scss
- Keep components pure and functional when possible
- Place shared types in separate type definition files
- We are using React 19 with React Compiler. Do not use useMemo, useCallback, or React.memo

File Structure:
- Components should be in a dedicated folder.
  - If the component is something that may be useful elsewhere, it should live in the global components folder: apps/dashboard/src/app/components
  - If the component is part of a view but somewhat view specific, place the component in a "components" folder within the view folder
- Group related components in feature-based folders
- Keep utilities, hooks, and types in separate directories

Charts
- Our chart library is IBM Carbon Charts React
- We prefer IBM Carbon Charts but may still have some instances of ECharts which will be migrated
- Wrap prefer to wrap charts into a chart component such as [pie-chart-component.tsx](mdc:src/app/components/pie-chart/pie-chart-component.tsx). This simplifies the avialable options.

Naming Conventions:
- Use kebab-case for files and names (e.g., button-group.tsx)
- Test files should be named button-group.test.tsx

React Practices:
- Use functional components with hooks
- Prefer const declarations for components
- Use TypeScript for type safety
- Implement proper prop typing with interfaces
- Extract complex logic into custom hooks
- Use proper React key props in lists
- Use the "import type" feature of Typescript 3.8+

State Management:
- Use React Query for server state management
- Use local state (useState) for UI-specific state
- Implement proper loading and error states
- Follow React Query best practices for caching and invalidation

Routing:
- Use React Router with proper route organization
- Implement lazy loading for routes when beneficial
- Use proper route typing with TypeScript

IBM Carbon:
- Always use IBM Carbon React v11
- Follow IBM Carbon best practices and use the appropriate theme structure
- Leverage IBM Carbon hooks where appropriate
- Use IBM Stack component with orientation=horizontal or veritcal for grouping elements
- Use IBM Carbon's built-in responsive utilities
- For tables, we use Tanstack Table V8
- We sometimes have Mantine components, these all need to be migrated to IBM Carbon. We do not want Mantine.

Code Style:
- Use arrow functions for component definitions
- Implement proper error boundaries
- Use early returns to reduce nesting
- Keep components focused and single-responsibility
- Use meaningful variable and function names
- Add JSDoc comments for complex functions
- Implement proper prop validation
- Use destructuring for props and state
- Do not use forEach, use for of
- Use typescript "type-only" imports when necessary

Performance:
- Avoid unnecessary re-renders
- Use proper React Query caching strategies

Accessibility:
- Follow WCAG guidelines
- Use semantic HTML elements
- Follow IBM Carbon accessibility guidelines

Error Handling:
- Implement proper error boundaries
- Use try-catch blocks where appropriate
- Display user-friendly error messages

Localization:
- Follow the localization rule for internationalizing user-facing text
- Use react-i18next with proper translation keys and default values
- Support both English and French languages

These rules should be applied when writing new code or reviewing existing code.

