---
description: Tips for writing unit tests
globs: *.test.tsx
alwaysApply: false
---

# Your rule content

- Use render and other utils from [index.ts](mdc:src/test-utils/index.ts)
- Keep unit tests simple. We want to have good coverage but overly verbose tests hurt performance.
- Use Vitest
- Example unit test: [pie-chart-component.test.tsx](mdc:src/app/components/pie-chart/pie-chart-component.test.tsx), [pie-chart-widget-options.test.tsx](mdc:src/app/widgets/pie-chart-widget/pie-chart-widget-options.test.tsx)
- If mocking "@carbon/react", you must mock GlobalTheme, example:

    vi.mock("@carbon/react", () => ({
      ...
      GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
    }));

- Do not use "require()" withing tests. Information from another test:
  - Removed the require() approach:
  - Instead of using require() and vi.mocked(), create a mock function (useAuthMock) that we can control directly
  - This approach is more compatible with ESM modules in Vite/Vitest
- Do not use jest functions. Use vi.mock, vi.mocked, vi.doMock, etc. 
- Do not over mock Carbon Components. It's perfectly fine to do something like this: (note async is required)

// Only mock the useTheme hook
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    useTheme: () => ({ theme: mockTheme }),
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});