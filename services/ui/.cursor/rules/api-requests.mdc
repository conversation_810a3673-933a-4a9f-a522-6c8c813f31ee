---
description: This rule is related to performing any sort of API request
globs: *
alwaysApply: false
---
When performing API requests, we use an OpenAPI sdk client which wraps React-Query. This can be seen in [ict-api.ts](mdc:src/app/api/ict-api.ts).

## For GET requests, use ictApi.client

Example:
  const { data, error, isLoading } = ictApi.client.useQuery(
    "get",
    "/inventory/advices/{adviceId}/details",
    { params: { path: { adviceId } } },
    { enabled: opened && !!adviceId },
  );

## If we are loading data for a component and need to use a POST, for example, we can also use ictApi.client.useQuery:

  const { data, error, isLoading } = ictApi.client.useQuery(
    "post",
    "/workstation/daily-performance/list",
    {
      body: { workstations },
      params: {
        query: {
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
        },
      },
    },
    { enabled: !!workstations && !!startDate && !!endDate },
  );

## If we need to respond to some user action and need to perform an API request as a response, use this pattern:

  const response = ictApi.queryClient.fetchQuery(
    ictApi.client.queryOptions("put", "/config/settings", {
      body: updatedSetting,
    }),
  );

## To Invalidate Queries, which may be useful if we perform an update:
  
ictApi.queryClient.invalidateQueries({
  queryKey: ictApi.client.queryOptions("get", "/config/settings").queryKey,
});