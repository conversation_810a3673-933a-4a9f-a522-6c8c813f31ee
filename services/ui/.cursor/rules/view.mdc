---
description: This rule applies to application "views", which are pages that are dynamically loaded based on the URL
globs: **/views/**
alwaysApply: false
---
A view is a dynamically loaded page. Views are stored in in the folder "apps/dashboard/src/app/views". The way views work:

* A view is dynamically loaded based on the URL path via [dynamic-route-view.tsx](mdc:src/app/router/components/dynamic-route-view.tsx)
* Views are "registered" as part of [views-config.ts](mdc:src/app/views/views-config.ts) which the view registry uses to lookup the id
