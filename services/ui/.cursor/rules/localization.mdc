---
description: 
globs: 
alwaysApply: false
---
# Localization Rule

We use react-i18next for internationalization with English and French support.

## Implementation
```javascript
import { useTranslation } from "react-i18next";
const { t } = useTranslation();

// Replace hardcoded strings with t() calls
<button>{t("actions.save", "Save")}</button>
```

## Translation Keys
- Use dot notation: `componentName.keyName` or `common.keyName`
- Always provide default values (English text)
- Add translations to both `public/locales/en/translation.json` and `public/locales/fr/translation.json`

## What to Localize
- **DO:** User-facing text, button labels, form labels, error messages shown to users
- **DON'T:** Internal errors, API endpoints, technical identifiers, console logs
