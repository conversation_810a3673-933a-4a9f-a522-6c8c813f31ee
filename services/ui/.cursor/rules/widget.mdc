---
description: This task applies to "widgets" which are components within our Dashboard view
globs: **/widgets/**
alwaysApply: false
---
A "widget" is a component that lives within our [dashboard.tsx](mdc:src/app/views/dashboard/dashboard.tsx). The dashboard is a dynamic view that allows the user to move around and add widgets. It uses react-grid-layout under the hood. 

- Note that all components are not widgets. Widgets are specific components that live in a dashboard and within the widgets folder.
- Store widgets in their own folder in the apps/dashboard/src/app/widgets folder.
- Widgets must be registered in [widget-config.ts](mdc:src/app/widgets/widget-config.ts) once created, this allows us to dynamically load them
- Widgets typically have an Options file
- A good example of a widget to follow is Pie Chart Widget, [pie-chart-widget-options.tsx](mdc:src/app/widgets/pie-chart-widget/pie-chart-widget-options.tsx), [pie-chart-widget.tsx](mdc:src/app/widgets/pie-chart-widget/pie-chart-widget.tsx)
  - Please note that the "useQuery" approach should be followed if we are loading kpi data (kpiResolver) or chart data (chartResolver). These are temporary functions we use to mimic our future API state. Once we have properly implemented these endpoints in our API we will move to our standard api-requests format as outlined in the [api-requests.mdc](mdc:.cursor/rules/api-requests.mdc)
- We use [widget-container.tsx](mdc:src/app/components/widget-container/widget-container.tsx) to wrap the widget content. This has the ability for loading and error states.
- For the widget and the widget-options, use "export default" - this is the standard for widget config imports.

Example of loading a kpi value in a widget, assuming we have a "type" option for selecing a KPI. 

  import { metricKpiResolver } from "../../api/resolver/kpi-resolver/kpi-resolver";
 
  const {
    data: metric,
    isLoading,
    error,
  } = useQuery({
    queryKey: [
      "metric",
      options.type,
      options.filters,
      filters.datePeriodRange,
    ],
    queryFn: () =>
      metricKpiResolver.getMetric(options.type as MetricKpiType, mergedFilters),
    enabled: !!options.type,
  });

Example of loading a chart value in a widget:

  import { chartResolver } from "../../api/resolver/time-chart-resolver/time-chart-resolver";

  const {
    data: response,
    isLoading,
    error,
  } = useQuery({
    queryKey: [
      `category-chart-${mergedFilters?.datePeriodRange}-${mergedFilters?.groupBy}`,
      options.type,
      options.filters,
      filters.datePeriodRange,
    ],
    queryFn: () => chartResolver.getCategoryChart(options.type, mergedFilters),
    enabled: !!options.type,
  });

