import { Dropdown, TextInput } from "@carbon/react";
import {
  ContainerListEndpointType,
  ContainerListWidgetOptions,
} from "./container-list-widget";
import { OptionsAccordion } from "../../../components/options/options-accordion/options-accordion";
import { OptionsAccordionGroup } from "../../../components/options/options-accordion-group/options-accordion-group";
import { Analytics, DataBase } from "@carbon/icons-react";

interface ContainerListWidgetOptionsProps {
  options: ContainerListWidgetOptions;
  onChange: (options: ContainerListWidgetOptions) => void;
}

const defaultDataSources = [
  {
    id: "standard",
    text: "Standard",
  },
  {
    id: "wms",
    text: "WMS",
  },
];

const ContainerListWidgetOptionsForm = ({
  options,
  onChange,
}: ContainerListWidgetOptionsProps) => {
  const handleDataSourceChange = (selectedId: string | null) => {
    if (!selectedId) {
      return;
    }

    const selectedEndpoint = defaultDataSources.find(
      (info) => info.id === selectedId,
    );
    if (selectedEndpoint) {
      onChange({
        ...options,
        type: selectedId as ContainerListEndpointType,
      });
    }
  };

  return (
    <OptionsAccordion>
      <OptionsAccordionGroup
        id="display"
        title="Display"
        icon={<Analytics size="24" />}
      >
        <TextInput
          labelText="Title"
          id="title"
          value={options.title}
          onChange={(e) => onChange({ ...options, title: e.target.value })}
        />
      </OptionsAccordionGroup>

      <OptionsAccordionGroup
        id="data"
        title="Data"
        icon={<DataBase size="24" />}
      >
        <Dropdown
          titleText="Data Source"
          label=""
          id="data-source"
          items={[...defaultDataSources]}
          selectedItem={
            options.type
              ? {
                  id: options.type,
                  text:
                    defaultDataSources.find((info) => info.id === options.type)
                      ?.text || "",
                }
              : null
          }
          onChange={({ selectedItem }) => {
            if (selectedItem) {
              handleDataSourceChange(selectedItem.id);
            }
          }}
          itemToString={(item) => (item ? item.text : "")}
        />
      </OptionsAccordionGroup>
    </OptionsAccordion>
  );
};

export default ContainerListWidgetOptionsForm;
