import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { ChangeEvent, useEffect, useMemo, useState } from "react";
import { useLocation, useNavigate } from "react-router";
import { ictApi } from "../../../api/ict-api";
import { transformFilters } from "../../../api/util/filter-transform-util";
import { ContainerListTable } from "./components/container-list-table/container-list-table";
import type { ContainerItem, SortField } from "./types";
import { useTranslation } from "react-i18next";
import { Checkbox, Tag } from "@carbon/react";
import styles from "./container-list.module.css";
import { ExportModal } from "../../../components/export-modal/export-modal";
import { useApiErrorState } from "../../../hooks/use-api-error-state";
import { WidgetContainer } from "../../../components/widget-container/widget-container";
import { useMeasure } from "@uidotdev/usehooks";
import { BaseWidgetProps } from "../../widget.types";
import { useConfigSetting } from "../../../config/hooks/use-config";
import { formatISOTimeToReadableDateTime } from "../../../utils";

export interface ContainerListWidgetOptions {
  type: ContainerListEndpointType;
  title: string;
  [key: string]: unknown;
}

// Simple interface extension now works with proper generic types
interface ContainerListWidgetProps
  extends BaseWidgetProps<ContainerListWidgetOptions> {
  options: ContainerListWidgetOptions;
}

export type ContainerListEndpointType = "standard" | "wms";

export const ContainerListWidget = ({ options }: ContainerListWidgetProps) => {
  const { t } = useTranslation();

  const { setting: timezoneConfig } = useConfigSetting("site-time-zone");
  const timezone = timezoneConfig?.value as string;

  const [lastUpdated, setLastUpdated] = useState<number | null>(null);
  const location = useLocation();
  const navigate = useNavigate();
  const [containerRef, containerMeasures] = useMeasure();
  const [bypassFiltering, setBypassFiltering] = useState(false);
  const [globalFilter, setGlobalFilter] = useState<string>("");
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 50,
  });
  const [sorting, setSorting] = useState<SortingState>([
    { id: "container_id", desc: false },
  ]);
  // Add state for the export dialog
  const [exportDialogOpen, setExportDialogOpen] = useState(false);

  // Get the SKU from the URL query parameters
  const queryParams = useMemo(
    () => new URLSearchParams(location.search),
    [location.search],
  );
  const skuParam = queryParams.get("sku");

  // Initialize column filters with the SKU filter if provided in URL
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>(() => {
    const initialFilters: ColumnFiltersState = [];

    // If SKU parameter is provided in the URL, add it as a filter
    if (skuParam) {
      initialFilters.push({
        id: "sku",
        value: skuParam,
      });
    }

    return initialFilters;
  });

  // Update column filters when SKU parameter changes
  useEffect(() => {
    setColumnFilters((prev) => {
      // Check if we already have a SKU filter
      const skuFilterIndex = prev.findIndex((filter) => filter.id === "sku");

      if (skuParam) {
        if (skuFilterIndex >= 0) {
          // Update existing SKU filter
          const newFilters = [...prev];
          newFilters[skuFilterIndex] = {
            id: "sku",
            value: skuParam,
          };
          return newFilters;
        }

        // Add new SKU filter
        return [
          ...prev,
          {
            id: "sku",
            value: skuParam,
          },
        ];
      } else {
        // Remove SKU filter if skuParam is null/undefined
        if (skuFilterIndex >= 0) {
          const newFilters = [...prev];
          newFilters.splice(skuFilterIndex, 1);
          return newFilters;
        }
        return prev;
      }
    });
  }, [skuParam]);

  // Handle dismissing the SKU tag
  const handleDismissSku = () => {
    // Remove the SKU filter from columnFilters
    setColumnFilters((prev) => prev.filter((filter) => filter.id !== "sku"));

    // Remove the SKU from URL query params
    const newParams = new URLSearchParams(location.search);
    newParams.delete("sku");
    navigate({ search: newParams.toString() });
  };

  const sortFields: SortField[] = useMemo(
    () =>
      sorting.map((sort) => ({
        columnName: sort.id,
        isDescending: sort.desc,
      })),
    [sorting],
  );

  // Function to generate column definitions for the export dialog
  const getColumnDefinitions = () => {
    return [
      { id: "container_id", header: "Container ID" },
      { id: "location_id", header: "Location ID" },
      { id: "zone", header: "Zone" },
      { id: "sku", header: "SKU" },
      { id: "quantity", header: "Quantity" },
      {
        id: "last_activity_date",
        header: "Last Activity",
      },
      {
        id: "last_cycle_count",
        header: "Last Cycle Count",
      },
      { id: "data_updated", header: "Data Updated" },
      {
        id: "free_cycle_count",
        header: "Free Cycle Count",
      },
    ];
  };

  const apiFilters = useMemo(
    () => transformFilters(columnFilters),
    [columnFilters],
  );

  // Standard containers query
  const standardQuery = ictApi.client.useQuery(
    "post",
    "/inventory/containers/list",
    {
      body: {
        limit: pagination.pageSize,
        page: pagination.pageIndex,
        filters: apiFilters,
        sortFields,
        byPassConfigSetting: bypassFiltering,
        ...(globalFilter !== "" && { searchString: globalFilter }),
      },
    },
    {
      enabled: options.type === "standard",
      keepPreviousData: true,
      retry: false,
      refetchOnWindowFocus: false,
    },
  );

  // WMS containers query
  const wmsQuery = ictApi.client.useQuery(
    "post",
    "/inventory/wms/containers/list",
    {
      body: {
        limit: pagination.pageSize,
        page: pagination.pageIndex,
        filters: apiFilters,
        sortFields,
        byPassConfigSetting: bypassFiltering,
        ...(globalFilter !== "" && { searchString: globalFilter }),
      },
    },
    {
      enabled: options.type === "wms",
      keepPreviousData: true,
      retry: false,
      refetchOnWindowFocus: false,
    },
  );

  // Select the active query based on options.type
  const activeQuery = options.type === "wms" ? wmsQuery : standardQuery;
  const { data, dataUpdatedAt, error, isLoading, isFetching, refetch } =
    activeQuery;

  const isNoDataAvailable = useApiErrorState(error);

  const containerData = useMemo(
    () => (data?.data ?? []) as unknown as ContainerItem[],
    [data],
  );

  useEffect(() => {
    if (dataUpdatedAt) {
      setLastUpdated(dataUpdatedAt);
    }
  }, [dataUpdatedAt]);

  const formattedLastUpdated = lastUpdated
    ? formatISOTimeToReadableDateTime(
        new Date(lastUpdated).toISOString(),
        timezone,
      )
    : t("containerList.loading", "Loading...");

  const handleRefresh = () => {
    refetch();
  };

  // Event handlers for container selection
  const handleContainerSelect = (containerId: string) => {
    // Navigate to the container detail page with datasource info
    navigate(
      `/ict-container-list/container/${containerId}?datasource=${options.type}`,
    );
  };

  // Export handler to use selected columns
  const handleExport = async (
    fileName: string,
    selectedColumns: Record<string, boolean>,
    signal: AbortSignal,
  ) => {
    console.log("Starting export process...");

    const exportUrl =
      options.type === "wms"
        ? "/inventory/wms/containers/list/export"
        : "/inventory/containers/list/export";

    const { error, data: blob } = await ictApi.fetchClient.POST(exportUrl, {
      body: {
        page: pagination.pageIndex,
        limit: pagination.pageSize,
        filters: transformFilters(columnFilters),
        sortFields,
        columns: selectedColumns,
        searchString: globalFilter || undefined,
        byPassConfigSetting: bypassFiltering,
      },
      parseAs: "blob",
      signal,
    });

    if (error) {
      throw new Error("Export failed");
    }
    if (!blob) {
      throw new Error("No data to export");
    }

    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    URL.revokeObjectURL(url);
    document.body.removeChild(a);
    console.log("Export completed successfully!");
  };

  // Function to handle modal close
  const handleExportModalClose = () => {
    setExportDialogOpen(false);
  };

  // default appearance when no data source is selected
  if (!options.type) {
    return <WidgetContainer title={options.title} initializing />;
  }

  // set the table height to the container height minus the controls height and a buffer
  const tableHeight = containerMeasures.height
    ? containerMeasures.height - 80
    : 300;

  return (
    <WidgetContainer title={options.title}>
      <div
        ref={containerRef}
        data-testid="container-list"
        className={styles.tableContainer}
        onMouseDown={(e) => e.stopPropagation()}
        onTouchStart={(e) => e.stopPropagation()}
      >
        <div className={styles.controls}>
          {skuParam && (
            <div className={styles.filterTag}>
              <span>Filter:</span>
              <Tag type="high-contrast" filter onClose={handleDismissSku}>
                SKU: {skuParam}
              </Tag>
            </div>
          )}
          <Checkbox
            className={styles.checkbox}
            labelText={
              bypassFiltering
                ? "Uncheck this for automation zone filtering"
                : "Check this to reset automation zone filtering"
            }
            id="bypass-filtering-checkbox"
            checked={bypassFiltering}
            onChange={(evt: ChangeEvent<HTMLInputElement>) =>
              setBypassFiltering(evt.target.checked)
            }
          />
          <p
            className={styles.lastUpdated}
            data-testid="container-list-last-update"
          >
            {t(
              "containerList.lastUpdated",
              "Last Updated: {{formattedLastUpdated}}",
              { formattedLastUpdated: formattedLastUpdated },
            )}
          </p>
        </div>
        <div style={{ height: `${tableHeight}px` }}>
          <ContainerListTable
            data={containerData}
            pagination={pagination}
            setPagination={setPagination}
            sorting={sorting}
            setSorting={setSorting}
            columnFilters={columnFilters}
            setColumnFilters={setColumnFilters}
            isLoading={isLoading}
            isFetching={isFetching}
            error={isNoDataAvailable ? null : error}
            rowCount={data?.metadata.totalResults ?? 0}
            timezone={timezone}
            onRefresh={handleRefresh}
            onExport={() => setExportDialogOpen(true)}
            setGlobalFilter={setGlobalFilter}
            onContainerSelect={handleContainerSelect}
          />
        </div>
      </div>
      <ExportModal
        open={exportDialogOpen}
        onClose={handleExportModalClose}
        onExport={handleExport}
        filters={columnFilters}
        columnDefs={getColumnDefinitions()}
        globalFilter={globalFilter}
        baseFileName="Containers_Forward-Pick_"
      />
    </WidgetContainer>
  );
};

export default ContainerListWidget;
