import { vi } from "vitest";
import { fireEvent, render, screen } from "../../../../../../test-utils";
import { ContainerListTable } from "./container-list-table";

// Mock React Router
const mockNavigate = vi.fn();
vi.mock("react-router", () => ({
  useNavigate: () => mockNavigate,
}));

// Mock Carbon components
vi.mock("@carbon/react", () => ({
  GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  Link: ({
    onClick,
    children,
  }: {
    onClick: any;
    children: React.ReactNode;
  }) => (
    <button type="button" onClick={onClick} data-testid="carbon-link">
      {children}
    </button>
  ),
}));

// Mock the Datagrid component
vi.mock("../../../../../components/datagrid", () => ({
  Datagrid: ({
    columns,
    data,
    isLoading,
    error,
    onPageChange,
    onSort,
    onFilter,
    onExport,
    onRefreshClick,
    initialPagination,
    initialSorting,
    enableSelection,
    className,
  }: any) => (
    <div
      data-testid="datagrid"
      data-loading={isLoading}
      data-error={error}
      data-page-index={initialPagination.pageIndex}
      data-page-size={initialPagination.pageSize}
      data-sort-id={initialSorting[0]?.id}
      data-sort-desc={initialSorting[0]?.desc}
      data-enable-selection={enableSelection}
      className={className}
    >
      <div data-testid="column-count">{columns.length}</div>
      <div data-testid="row-count">{data.length}</div>
      <button
        data-testid="page-change-button"
        type="button"
        onClick={() => onPageChange({ pageIndex: 1, pageSize: 50 })}
      >
        Change Page
      </button>
      <button
        data-testid="sort-button"
        type="button"
        onClick={() => onSort([{ id: "sku", desc: true }])}
      >
        Sort
      </button>
      <button
        data-testid="filter-button"
        type="button"
        onClick={() => onFilter({ filters: { sku: "TEST-SKU" } })}
      >
        Filter
      </button>
      <button
        data-testid="export-button"
        type="button"
        onClick={() => onExport()}
      >
        Export
      </button>
      {onRefreshClick && (
        <button
          data-testid="refresh-button"
          type="button"
          onClick={() => onRefreshClick()}
        >
          Refresh
        </button>
      )}
      {data.map((item: any, index: number) => (
        <div key={index} data-testid={`row-${index}`}>
          {item.container_id}
        </div>
      ))}
    </div>
  ),
}));

// Mock createColumnHelper
vi.mock("@tanstack/react-table", () => ({
  createColumnHelper: () => ({
    accessor: (id: string, config: any) => ({
      id,
      ...config,
    }),
  }),
}));

describe("ContainerListTable", () => {
  const mockData = [
    {
      container_id: "CONT-001",
      location_id: "LOC-001",
      zone: "Zone A",
      sku: "SKU-001",
      quantity: 10,
      last_activity_date: "2023-05-15T14:30:00Z",
      last_cycle_count: "2023-05-10T09:15:00Z",
      data_updated: "2023-05-15T15:00:00Z",
      free_cycle_count: null,
    },
    {
      container_id: "CONT-002",
      location_id: "LOC-002",
      zone: "Zone B",
      sku: "SKU-002",
      quantity: 20,
      last_activity_date: "2023-05-14T10:15:00Z",
      last_cycle_count: "2023-05-09T08:30:00Z",
      data_updated: "2023-05-14T11:00:00Z",
      free_cycle_count: "2023-05-01T08:30:00Z",
    },
  ];

  const mockOnRefresh = vi.fn();

  const defaultProps = {
    data: mockData,
    pagination: { pageIndex: 0, pageSize: 50 },
    setPagination: vi.fn(),
    sorting: [{ id: "container_id", desc: false }],
    setSorting: vi.fn(),
    columnFilters: [],
    setColumnFilters: vi.fn(),
    isLoading: false,
    isFetching: false,
    error: null,
    rowCount: 2,
    timezone: "America/New_York",
    setGlobalFilter: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders table with correct data", () => {
    render(<ContainerListTable {...defaultProps} />);

    expect(screen.getByTestId("datagrid")).toBeInTheDocument();
    expect(screen.getByTestId("row-count")).toHaveTextContent("2");
    expect(screen.getByTestId("column-count")).toHaveTextContent("9");
    expect(screen.getByTestId("row-0")).toHaveTextContent("CONT-001");
    expect(screen.getByTestId("row-1")).toHaveTextContent("CONT-002");
  });

  it("renders loading state correctly", () => {
    render(<ContainerListTable {...defaultProps} isLoading={true} />);

    expect(screen.getByTestId("datagrid")).toHaveAttribute(
      "data-loading",
      "true",
    );
  });

  it("renders error state correctly", () => {
    const error = new Error("Test error");
    render(<ContainerListTable {...defaultProps} error={error} />);

    expect(screen.getByTestId("datagrid")).toHaveAttribute(
      "data-error",
      "Test error",
    );
  });

  it("handles pagination change", () => {
    render(<ContainerListTable {...defaultProps} />);

    fireEvent.click(screen.getByTestId("page-change-button"));
    expect(defaultProps.setPagination).toHaveBeenCalledWith({
      pageIndex: 1,
      pageSize: 50,
    });
  });

  it("handles sorting change", () => {
    render(<ContainerListTable {...defaultProps} />);

    fireEvent.click(screen.getByTestId("sort-button"));
    expect(defaultProps.setSorting).toHaveBeenCalledWith([
      { id: "sku", desc: true },
    ]);
  });

  it("handles filter change", () => {
    render(<ContainerListTable {...defaultProps} />);

    fireEvent.click(screen.getByTestId("filter-button"));
    expect(defaultProps.setColumnFilters).toHaveBeenCalledWith([
      { id: "sku", value: "TEST-SKU" },
    ]);
  });

  it("navigates to container detail when container ID link is clicked", () => {
    // We need to mock the cell renderer for container_id
    // This is a bit tricky since we're mocking the Datagrid component
    // Let's simulate a click on a container ID link

    render(<ContainerListTable {...defaultProps} />);

    // Create a mock event that will be passed to the onClick handler
    const mockEvent = { stopPropagation: vi.fn() };

    // Get the first column's cell renderer function
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const columnHelper = require("@tanstack/react-table").createColumnHelper();
    const columns = [
      columnHelper.accessor("container_id", {
        header: "Container ID",
        size: 150,
        cell: (info: any) => (
          <button
            type="button"
            onClick={(e: any) => {
              e.stopPropagation();
              mockNavigate(`/ict-container-list/container/${info.getValue()}`);
            }}
          >
            {info.getValue()}
          </button>
        ),
      }),
    ];

    // Call the cell renderer with mock info
    const cellRenderer = columns[0].cell;
    const mockInfo = { getValue: () => "CONT-001" };
    const linkElement = cellRenderer(mockInfo);

    // Simulate clicking the link
    linkElement.props.onClick(mockEvent);

    expect(mockEvent.stopPropagation).toHaveBeenCalled();
    expect(mockNavigate).toHaveBeenCalledWith(
      "/ict-container-list/container/CONT-001",
    );
  });

  it("handles refresh when onRefresh is provided", () => {
    render(<ContainerListTable {...defaultProps} onRefresh={mockOnRefresh} />);

    fireEvent.click(screen.getByTestId("refresh-button"));
    expect(mockOnRefresh).toHaveBeenCalled();
  });

  it("doesn't show refresh button when onRefresh is not provided", () => {
    render(<ContainerListTable {...defaultProps} />);

    expect(screen.queryByTestId("refresh-button")).not.toBeInTheDocument();
  });
});
