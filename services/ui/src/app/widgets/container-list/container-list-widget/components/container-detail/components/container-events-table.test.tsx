import { vi } from "vitest";
import { render, screen } from "../../../../../../../test-utils";
import ContainerEventsTable from "./container-events-table";

// Mock Carbon components
vi.mock("@carbon/react", () => ({
  GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock the WidgetContainer component
vi.mock(
  "../../../../../../components/widget-container/widget-container",
  () => ({
    WidgetContainer: ({
      title,
      children,
    }: {
      title: string;
      children: React.ReactNode;
    }) => (
      <div data-testid="widget-container">
        <div data-testid="widget-title">{title}</div>
        <div data-testid="widget-content">{children}</div>
      </div>
    ),
  }),
);

// Mock the Datagrid component
vi.mock("../../../../../../components/datagrid", () => ({
  Datagrid: ({
    columns,
    data,
    isLoading,
  }: {
    columns: any[];
    data: any[];
    isLoading: boolean;
  }) => (
    <div data-testid="datagrid" data-loading={isLoading}>
      <div data-testid="column-count">{columns.length}</div>
      <div data-testid="row-count">{data.length}</div>
      {isLoading ? (
        <div data-testid="loading-state">Loading...</div>
      ) : (
        <div data-testid="data-state">
          {data.map((item, index) => (
            <div key={index} data-testid={`row-${index}`}>
              {JSON.stringify(item)}
            </div>
          ))}
        </div>
      )}
    </div>
  ),
}));

// Mock createColumnHelper
vi.mock("@tanstack/react-table", () => ({
  createColumnHelper: () => ({
    accessor: (id: string, config: any) => ({
      id,
      ...config,
    }),
  }),
}));

describe("ContainerEventsTable", () => {
  const mockEvents = [
    {
      timestamp: "2023-05-15T14:30:00Z",
      event: "PICK",
      destinationContainer: "DEST-001",
      operator: "John Doe",
      quantity: 5,
      workstationCode: "WS-123",
      sku: "SKU-789",
    },
    {
      timestamp: "2023-05-14T10:15:00Z",
      event: "CYCLE_COUNT",
      destinationContainer: null,
      operator: "Jane Smith",
      quantity: 42,
      workstationCode: "WS-456",
      sku: "SKU-789",
    },
  ];

  test("renders loading state correctly", () => {
    render(
      <ContainerEventsTable
        events={[]}
        isLoading={true}
        timezone="America/New_York"
      />,
    );

    expect(screen.getByTestId("datagrid")).toHaveAttribute(
      "data-loading",
      "true",
    );
    expect(screen.getByTestId("loading-state")).toBeInTheDocument();
  });

  test("renders events data correctly when loaded", () => {
    render(
      <ContainerEventsTable
        events={mockEvents as any}
        isLoading={false}
        timezone="America/New_York"
      />,
    );

    expect(screen.getByTestId("widget-title")).toHaveTextContent(
      "Container Events",
    );
    expect(screen.getByTestId("row-count")).toHaveTextContent("2");
    expect(screen.getByTestId("column-count")).toHaveTextContent("7");

    // Check that data is passed to the Datagrid
    expect(screen.getByTestId("row-0")).toHaveTextContent("PICK");
    expect(screen.getByTestId("row-0")).toHaveTextContent("DEST-001");
    expect(screen.getByTestId("row-1")).toHaveTextContent("CYCLE_COUNT");
  });

  test("handles empty events array", () => {
    render(
      <ContainerEventsTable
        events={[]}
        isLoading={false}
        timezone="America/New_York"
      />,
    );

    expect(screen.getByTestId("row-count")).toHaveTextContent("0");
  });

  test("creates correct column configuration", () => {
    render(
      <ContainerEventsTable
        events={mockEvents as any}
        isLoading={false}
        timezone="America/New_York"
      />,
    );

    // Check that we have the expected number of columns
    expect(screen.getByTestId("column-count")).toHaveTextContent("7");
  });

  test("applies initial sorting configuration", () => {
    const { container } = render(
      <ContainerEventsTable
        events={mockEvents as any}
        isLoading={false}
        timezone="America/New_York"
      />,
    );

    // This is a basic check that the component renders without errors
    // A more detailed test would require more complex mocking of the Datagrid component
    expect(container).toBeInTheDocument();
  });
});
