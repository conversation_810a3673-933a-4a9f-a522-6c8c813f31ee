import { createColumnHelper } from "@tanstack/react-table";
import type { components } from "@ict/sdk/openapi-react-query";
import { Datagrid } from "../../../../../../components/datagrid";
import { WidgetContainer } from "../../../../../../components/widget-container/widget-container";
import styles from "./container-events-table.module.css";
import { formatISOTimeToReadableDateTime } from "../../../../../../utils";

type InventoryContainerEventsListDataItem =
  components["schemas"]["FlattenedInventoryContainerEventsDataItem"];

interface ContainerEventsTableProps {
  events: InventoryContainerEventsListDataItem[];
  isLoading: boolean;
  timezone: string;
  onExport?: () => void;
}

export const ContainerEventsTable = ({
  events,
  isLoading,
  timezone,
  onExport,
}: ContainerEventsTableProps) => {
  // Format cell value or show placeholder for empty values
  const formatCellValue = (value: string | number | null | undefined) => {
    if (value === null || value === undefined || value === "") {
      return <span className={styles.emptyCell}>--</span>;
    }
    return value;
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    return formatISOTimeToReadableDateTime(timestamp, timezone);
  };

  // Define columns using createColumnHelper
  const columnHelper =
    createColumnHelper<InventoryContainerEventsListDataItem>();
  const columns = [
    columnHelper.accessor("timestamp", {
      header: "Timestamp",
      size: 180,
      cell: (info) => formatTimestamp(info.getValue()),
    }),
    columnHelper.accessor("event", {
      header: "Event",
      size: 120,
      cell: (info) => formatCellValue(info.getValue()),
    }),
    columnHelper.accessor("destinationContainer", {
      header: "Destination Container",
      size: 180,
      cell: (info) => formatCellValue(info.getValue()),
    }),
    columnHelper.accessor("operator", {
      header: "Operator",
      size: 120,
      cell: (info) => formatCellValue(info.getValue()),
    }),
    columnHelper.accessor("quantity", {
      header: "Quantity",
      size: 100,
      cell: (info) => formatCellValue(info.getValue()),
    }),
    columnHelper.accessor("workstationCode", {
      header: "Workstation",
      size: 150,
      cell: (info) => formatCellValue(info.getValue()),
    }),
    columnHelper.accessor("sku", {
      header: "SKU",
      size: 120,
      cell: (info) => formatCellValue(info.getValue()),
    }),
  ];

  // Initial sorting configuration
  const initialSorting = [
    {
      id: "timestamp",
      desc: true,
    },
  ];

  return (
    <WidgetContainer title="Container Events">
      <div
        className={styles.tableContainer}
        onMouseDown={(e) => e.stopPropagation()}
        onTouchStart={(e) => e.stopPropagation()}
      >
        <Datagrid
          columns={columns}
          data={events}
          isLoading={isLoading}
          initialSorting={initialSorting}
          initialPagination={{ pageIndex: 0, pageSize: 10 }}
          initialDensity="compact"
          mode="client"
          enableSelection={false}
          onExport={onExport}
        />
      </div>
    </WidgetContainer>
  );
};

export default ContainerEventsTable;
