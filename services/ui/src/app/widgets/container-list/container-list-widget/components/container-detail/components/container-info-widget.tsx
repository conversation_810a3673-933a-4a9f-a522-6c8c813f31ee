import { SkeletonText } from "@carbon/react";
import type { components } from "@ict/sdk/openapi-react-query";
import { WidgetContainer } from "../../../../../../components/widget-container/widget-container";
import styles from "./container-info-widget.module.css";
import { formatISOTimeToReadableDateTime } from "../../../../../../utils";

type InventoryContainerEventsKpiData =
  components["schemas"]["InventoryContainerEventsKpiData"];

interface ContainerInfoWidgetProps {
  containerInfo: InventoryContainerEventsKpiData;
  isLoading: boolean;
  timezone: string;
}

export const ContainerInfoWidget = ({
  containerInfo,
  isLoading,
  timezone,
}: ContainerInfoWidgetProps) => {
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "--";
    return formatISOTimeToReadableDateTime(dateString, timezone);
  };

  const InfoItem = ({
    label,
    value,
  }: {
    label: string;
    value: React.ReactNode;
  }) => (
    <div className={styles.infoItem}>
      <p
        className="cds--type-helper-text-01"
        style={{ fontWeight: 500, color: "#6f6f6f" }}
      >
        {label}
      </p>
      {isLoading ? (
        <SkeletonText width="80%" />
      ) : (
        <p className="cds--type-body-01">{value || "--"}</p>
      )}
    </div>
  );

  return (
    <div data-testid="container-information">
      <WidgetContainer title="Container Information" loading={isLoading}>
        <div data-testid="container-details" className={styles.infoGrid}>
          <div className={styles.infoRow}>
            <div data-testid="container-id" className={styles.infoCol}>
              <InfoItem
                label="Container ID"
                value={containerInfo?.containerId}
              />
            </div>
            <div data-testid="sku" className={styles.infoCol}>
              <InfoItem label="SKU" value={containerInfo?.sku} />
            </div>
            <div data-testid="location-id" className={styles.infoCol}>
              <InfoItem label="Location ID" value={containerInfo?.locationId} />
            </div>
            <div data-testid="last-activity-date" className={styles.infoCol}>
              <InfoItem
                label="Last Activity Date"
                value={formatDate(containerInfo?.lastActivityDate)}
              />
            </div>
            <div data-testid="quantity" className={styles.infoCol}>
              <InfoItem label="Quantity" value={containerInfo?.quantity} />
            </div>
            <div data-testid="zone" className={styles.infoCol}>
              <InfoItem label="Zone" value={containerInfo?.zone} />
            </div>
            <div data-testid="last-cycle-count" className={styles.infoCol}>
              <InfoItem
                label="Last Cycle Count"
                value={formatDate(containerInfo?.lastCycleCount)}
              />
            </div>
            <div data-testid="date-updated" className={styles.infoCol}>
              <InfoItem
                label="Data Updated"
                value={formatDate(containerInfo?.dataUpdated)}
              />
            </div>
          </div>
        </div>
      </WidgetContainer>
    </div>
  );
};

export default ContainerInfoWidget;
