import { vi } from "vitest";
import type { components } from "@ict/sdk/openapi-react-query";
import { render, screen } from "../../../../../../../test-utils";
import { ContainerInfoWidget } from "./container-info-widget";

type InventoryContainerEventsKpiData =
  components["schemas"]["InventoryContainerEventsKpiData"];

// Mock Carbon components
vi.mock("@carbon/react", () => ({
  GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  SkeletonText: ({ width }: { width: string }) => (
    <div data-testid="skeleton-text" style={{ width }}>
      Loading...
    </div>
  ),
}));

// Mock the WidgetContainer component
vi.mock(
  "../../../../../../components/widget-container/widget-container",
  () => ({
    WidgetContainer: ({
      title,
      loading,
      children,
    }: {
      title: string;
      loading?: boolean;
      children: React.ReactNode;
    }) => (
      <div data-testid="widget-container" data-loading={loading}>
        <div data-testid="widget-title">{title}</div>
        <div data-testid="widget-content">{children}</div>
      </div>
    ),
  }),
);

describe("ContainerInfoWidget", () => {
  const mockContainerInfo: InventoryContainerEventsKpiData = {
    containerId: "CONT-12345",
    sku: "SKU-789",
    locationId: "LOC-456",
    lastActivityDate: "2023-05-15T14:30:00Z",
    quantity: 42,
    zone: "Zone A",
    lastCycleCount: "2023-05-10T09:15:00Z",
    dataUpdated: "2023-05-15T15:00:00Z",
    averageDailyPickEvents: 100,
    pickEventsToday: 50,
    averageDailyCycleCount: 10,
    cycleCountEventsToday: 3,
  };

  test("renders loading state correctly", () => {
    render(
      <ContainerInfoWidget
        containerInfo={mockContainerInfo}
        isLoading={true}
        timezone="America/New_York"
      />,
    );

    expect(screen.getByTestId("widget-container")).toHaveAttribute(
      "data-loading",
      "true",
    );
    expect(screen.getAllByTestId("skeleton-text").length).toBeGreaterThan(0);
  });

  test("renders container information correctly when loaded", () => {
    render(
      <ContainerInfoWidget
        containerInfo={mockContainerInfo}
        isLoading={false}
        timezone="America/New_York"
      />,
    );

    expect(screen.getByTestId("widget-title")).toHaveTextContent(
      "Container Information",
    );

    // Check that key information is displayed
    expect(screen.getByText("Container ID")).toBeInTheDocument();
    expect(screen.getByText("CONT-12345")).toBeInTheDocument();

    expect(screen.getByText("SKU")).toBeInTheDocument();
    expect(screen.getByText("SKU-789")).toBeInTheDocument();

    expect(screen.getByText("Location ID")).toBeInTheDocument();
    expect(screen.getByText("LOC-456")).toBeInTheDocument();

    expect(screen.getByText("Quantity")).toBeInTheDocument();
    expect(screen.getByText("42")).toBeInTheDocument();

    expect(screen.getByText("Zone")).toBeInTheDocument();
    expect(screen.getByText("Zone A")).toBeInTheDocument();
  });

  test("formats dates correctly", () => {
    render(
      <ContainerInfoWidget
        containerInfo={mockContainerInfo}
        isLoading={false}
        timezone="America/New_York"
      />,
    );

    // Check that dates are formatted as locale strings
    // Note: The exact format will depend on the locale of the test environment
    expect(screen.getByText("Last Activity Date")).toBeInTheDocument();
    expect(screen.getByText("Last Cycle Count")).toBeInTheDocument();
    expect(screen.getByText("Data Updated")).toBeInTheDocument();
  });

  test("handles missing or undefined values", () => {
    const incompleteInfo = {
      containerId: "CONT-12345",
      // Missing other fields
    } as InventoryContainerEventsKpiData;

    render(
      <ContainerInfoWidget
        containerInfo={incompleteInfo}
        isLoading={false}
        timezone="America/New_York"
      />,
    );

    // Check that placeholders are shown for missing values
    const placeholders = screen.getAllByText("--");
    expect(placeholders.length).toBeGreaterThan(0);
  });
});
