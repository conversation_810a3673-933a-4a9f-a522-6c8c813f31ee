import { Link } from "@carbon/react";
import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { createColumnHelper } from "@tanstack/react-table";
import { useCallback } from "react";
import { Datagrid } from "../../../../../components/datagrid";
import type { FilterState } from "../../../../../components/datagrid";
import type { ContainerItem } from "../../types";
import { useTranslation } from "react-i18next";
import { formatISOTimeToReadableDateTime } from "../../../../../utils";

interface ContainerListTableProps {
  data: ContainerItem[];
  pagination: PaginationState;
  setPagination: (pagination: PaginationState) => void;
  sorting: SortingState;
  setSorting: (sorting: SortingState) => void;
  columnFilters: ColumnFiltersState;
  setColumnFilters: (filters: ColumnFiltersState) => void;
  isLoading: boolean;
  isFetching: boolean;
  error: Error | null;
  rowCount: number;
  timezone: string;
  onRefresh?: () => void;
  onExport?: () => void;
  setGlobalFilter: (globalFilter: string) => void;
  onContainerSelect?: (containerId: string) => void;
}

export function ContainerListTable({
  data,
  pagination,
  setPagination,
  sorting,
  setSorting,
  columnFilters,
  setColumnFilters,
  isLoading,
  isFetching,
  error,
  rowCount,
  timezone,
  onRefresh,
  onExport,
  setGlobalFilter,
  onContainerSelect,
}: ContainerListTableProps) {
  // Define columns using createColumnHelper
  const columnHelper = createColumnHelper<ContainerItem>();

  const { t } = useTranslation();

  const formatTimestamp = (timestamp: string | null) => {
    if (!timestamp) return t("containerListTable.notAvailable", "N/A");
    return formatISOTimeToReadableDateTime(timestamp, timezone);
  };

  const columns = [
    columnHelper.accessor("container_id", {
      header: t("containerListTable.containerId", "Container ID"),
      size: 150,
      cell: (info) => (
        <Link
          onClick={(e) => {
            e.stopPropagation();
            if (onContainerSelect) {
              onContainerSelect(info.getValue());
            }
          }}
          style={{ cursor: "pointer" }}
        >
          {info.getValue()}
        </Link>
      ),
    }),
    columnHelper.accessor("location_id", {
      header: t("containerListTable.locationId", "Location ID"),
      size: 120,
    }),
    columnHelper.accessor("zone", {
      header: t("containerListTable.zone", "Zone"),
      size: 100,
    }),
    columnHelper.accessor("sku", {
      header: t("containerListTable.sku", "SKU"),
      size: 120,
    }),
    columnHelper.accessor("quantity", {
      header: t("containerListTable.quantity", "Quantity"),
      size: 100,
    }),
    columnHelper.accessor("last_activity_date", {
      header: t("containerListTable.lastActivity", "Last Activity"),
      size: 180,
      cell: (info) => {
        return formatTimestamp(info.getValue());
      },
    }),
    columnHelper.accessor("last_cycle_count", {
      header: t("containerListTable.lastCycleCount", "Last Cycle Count"),
      size: 180,
      cell: (info) => {
        return formatTimestamp(info.getValue());
      },
    }),
    columnHelper.accessor("data_updated", {
      header: t("containerListTable.dataUpdated", "Data Updated"),
      size: 180,
      cell: (info) => {
        return formatTimestamp(info.getValue());
      },
    }),
    columnHelper.accessor("free_cycle_count", {
      header: t("containerListTable.freeCycleCount", "Free Cycle Count"),
      size: 180,
      cell: (info) => {
        return formatTimestamp(info.getValue());
      },
    }),
  ];

  // Handlers for Datagrid callbacks
  const handlePageChange = useCallback(
    (newPagination: PaginationState) => {
      setPagination(newPagination);
    },
    [setPagination],
  );

  const handleSort = useCallback(
    (newSorting: SortingState) => {
      setSorting(newSorting);
    },
    [setSorting],
  );

  const handleFilter = useCallback(
    (newFilters: FilterState) => {
      // Convert FilterState to ColumnFiltersState
      const newColumnFilters: ColumnFiltersState = Object.entries(
        newFilters.filters,
      ).map(([id, value]) => ({
        id,
        value,
      }));

      // Keep the sku filter if it is set in the columnFilters
      const skuFilter = columnFilters.find((filter) => filter.id === "sku");
      if (skuFilter) {
        newColumnFilters.push(skuFilter);
      }

      setColumnFilters(newColumnFilters);
      setGlobalFilter(newFilters.globalFilter);
    },
    [setColumnFilters, setGlobalFilter],
  );

  const handleExport = useCallback(() => {
    if (onExport) {
      onExport();
    }
  }, [onExport]);

  return (
    <Datagrid
      columns={columns}
      data={data}
      mode="server"
      totalRows={rowCount}
      isLoading={isLoading || isFetching}
      error={error ? error.message : undefined}
      onPageChange={handlePageChange}
      onSort={handleSort}
      onFilter={handleFilter}
      onExport={handleExport}
      onRefreshClick={onRefresh}
      showRefreshButton={!!onRefresh}
      initialPagination={pagination}
      initialSorting={sorting}
      enableSelection={false}
    />
  );
}
