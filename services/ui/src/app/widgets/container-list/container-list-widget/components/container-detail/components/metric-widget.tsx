import { Heading } from "@carbon/react";
import { WidgetContainer } from "../../../../../../components/widget-container/widget-container";
import styles from "./metric-widget.module.css";

interface MetricWidgetProps {
  title: string;
  value?: number | string;
  isLoading: boolean;
}

export const MetricWidget = ({
  title,
  value,
  isLoading,
}: MetricWidgetProps) => {
  return (
    <WidgetContainer title={title} loading={isLoading}>
      <div className={styles.metricContainer}>
        <Heading data-testid="widget-value">
          {value !== undefined
            ? typeof value === "number"
              ? value.toLocaleString()
              : value
            : "--"}
        </Heading>
      </div>
    </WidgetContainer>
  );
};

export default MetricWidget;
