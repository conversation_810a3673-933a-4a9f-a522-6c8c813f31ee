.container {
  display: flex;
  flex-direction: column;
  height: calc(100% - 48px); /* Adjust for ViewBar height */
  padding: 20px;
}

.content {
  padding: 0;
  flex: 1;
  overflow: auto;
  width: 100%;
}

.row {
  overflow-x: hidden;
  width: 100%;
}

.rowWithMargin {
  margin-bottom: 1.5rem;
}

.metricsContainer {
  display: flex;
  flex-wrap: wrap;
  margin: -0.75rem;
}

.metricItem {
  padding: 0.75rem;
  width: 25%;
  min-width: 250px;
  flex: 1 1 250px;
}

.titleContainer {
  display: flex;
  align-items: center;
}

.titleLink {
  color: inherit;
  text-decoration: none;
  cursor: pointer;
}

.titleText {
  font-weight: bold;
}

.titleSeparator {
  margin: 0 8px;
}

.errorContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  width: 100%;
}
