import { ictApi } from "../../../../../api/ict-api";
import type { components } from "@ict/sdk/openapi-react-query";
import { ViewBar } from "../../../../../components/view-bar/view-bar";
import ContainerEventsTable from "./components/container-events-table";
import { ContainerInfoWidget } from "./components/container-info-widget";
import MetricWidget from "./components/metric-widget";
import styles from "./container-detail.module.css";
import { useTranslation } from "react-i18next";
import { ExportModal } from "../../../../../components/export-modal/export-modal";
import { useState } from "react";
import { authService } from "../../../../../auth/auth-service";
import { ContainerListEndpointType } from "../../container-list-widget";
import { useConfigSetting } from "../../../../../config/hooks/use-config";

/**
 * Type for container events data from API
 */
type InventoryContainerEventsKpiData =
  components["schemas"]["InventoryContainerEventsKpiData"];

interface ContainerDetailProps {
  datasource: ContainerListEndpointType;
  containerId: string;
  onBack: () => void;
}

/**
 * Container Detail View
 *
 * Displays detailed information about a specific container including:
 * - Container information
 * - Key metrics (cycle counts, pick events)
 * - Container event history
 */
export function ContainerDetail({
  datasource,
  containerId,
  onBack,
}: ContainerDetailProps) {
  const { t } = useTranslation();

  const { setting: timezoneConfig } = useConfigSetting("site-time-zone");
  const timezone = timezoneConfig?.value as string;

  const [exportDialogOpen, setExportDialogOpen] = useState(false);

  // Standard container query
  const standardContainerQuery = ictApi.client.useQuery(
    "get",
    "/inventory/container-events/{containerId}",
    { params: { path: { containerId: String(containerId) } } },
    { enabled: !!containerId },
  );

  // WMS container query
  const wmsContainerQuery = ictApi.client.useQuery(
    "get",
    "/inventory/wms/container-events/{containerId}",
    { params: { path: { containerId: String(containerId) } } },
    { enabled: !!containerId },
  );

  // Fetch container events (last month)
  // Both standard and WMS use the same endpoint
  const { data: eventsData, isLoading: isEventsLoading } =
    ictApi.client.useQuery(
      "post",
      "/inventory/container-events/list/{containerId}",
      {
        params: {
          path: { containerId: String(containerId) },
        },
        body: { months: 1 },
      },
      { enabled: !!containerId },
    );

  // Select the active query based on the datasource
  const containerQuery =
    datasource === "wms" ? wmsContainerQuery : standardContainerQuery;
  const {
    data: containerData,
    isLoading: isContainerLoading,
    error: _containerError,
  } = containerQuery;

  const containerInfo = containerData
    ?.events?.[0] as InventoryContainerEventsKpiData;
  const containerEvents = eventsData?.data || [];

  /**
   * Renders the breadcrumb-style title with link back to Container List
   */
  const renderTitleContent = () => (
    <div data-testid="container-detail" className={styles.titleContainer}>
      <span onClick={onBack} className={styles.titleLink}>
        <span
          className="cds--type-productive-heading-03"
          style={{ fontWeight: "bold" }}
        >
          {t("containerDetail.containerList", "Container List")}
        </span>
      </span>
      <span
        className="cds--type-productive-heading-03"
        style={{ margin: "0 8px" }}
      >
        /
      </span>
      <span className="cds--type-productive-heading-03">{containerId}</span>
    </div>
  );

  // Function to generate column definitions for the export dialog
  const getColumnDefinitions = () => {
    return [
      { id: "timestamp", header: "Timestamp" },
      { id: "event", header: "Event" },
      { id: "destinationContainer", header: "Destination Container" },
      { id: "operator", header: "Operator" },
      { id: "quantity", header: "Quantity" },
      { id: "workstationCode", header: "Workstation" },
      { id: "sku", header: "SKU" },
    ];
  };

  // Export handler to use selected columns
  const handleExport = async (
    fileName: string,
    selectedColumns: Record<string, boolean>,
    signal: AbortSignal,
  ) => {
    console.log("Starting export process...");
    const accessToken = await authService.getAccessToken();
    const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;
    const exportUrl = `${API_BASE_URL}/inventory/container-events/list/export/${containerId}`;

    // Create request body with only selected columns
    const requestBody = {
      columns: selectedColumns,
      months: 1, // Match the data fetch period
    };

    const response = await fetch(exportUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
      signal,
    });

    if (!response.ok) {
      throw new Error(`Export failed with status: ${response.status}`);
    }

    const blob = await response.blob();
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    URL.revokeObjectURL(url);
    document.body.removeChild(a);
    console.log("Export completed successfully!");
  };

  // Function to handle modal close
  const handleExportModalClose = () => {
    setExportDialogOpen(false);
  };

  return (
    <div>
      <ViewBar title={renderTitleContent()} />

      <div className={styles.container}>
        <div className={styles.content}>
          <div className={styles.row}>
            {/* First row - Container Information */}
            <div className={styles.rowWithMargin}>
              <ContainerInfoWidget
                containerInfo={containerInfo}
                isLoading={isContainerLoading}
                timezone={timezone}
              />
            </div>

            {/* Second row - Metrics */}
            <div className={styles.rowWithMargin}>
              <div className={styles.metricsContainer}>
                <div
                  data-testid="cycle-count-events-today"
                  className={styles.metricItem}
                >
                  <MetricWidget
                    title={t(
                      "containerDetail.cycleCountEventsToday",
                      "Cycle Count Events Today",
                    )}
                    value={containerInfo?.cycleCountEventsToday ?? 0}
                    isLoading={isContainerLoading}
                  />
                </div>
                <div
                  data-testid="avg-daily-cycle-count"
                  className={styles.metricItem}
                >
                  <MetricWidget
                    title={t(
                      "containerDetail.avgDailyCycleCount",
                      "Avg Daily Cycle Count",
                    )}
                    value={containerInfo?.averageDailyCycleCount ?? 0}
                    isLoading={isContainerLoading}
                  />
                </div>
                <div
                  data-testid="pick-events-today"
                  className={styles.metricItem}
                >
                  <MetricWidget
                    title={t(
                      "containerDetail.pickEventsToday",
                      "Pick Events Today",
                    )}
                    value={containerInfo?.pickEventsToday ?? 0}
                    isLoading={isContainerLoading}
                  />
                </div>
                <div
                  data-testid="avg-daily-pick-events"
                  className={styles.metricItem}
                >
                  <MetricWidget
                    title={t(
                      "containerDetail.avgDailyPickEvents",
                      "Avg Daily Pick Events",
                    )}
                    value={containerInfo?.averageDailyPickEvents ?? 0}
                    isLoading={isContainerLoading}
                  />
                </div>
              </div>
            </div>

            {/* Third row - Events Table */}
            <div className={styles.row}>
              <ContainerEventsTable
                events={containerEvents}
                isLoading={isEventsLoading}
                onExport={() => setExportDialogOpen(true)}
                timezone={timezone}
              />
            </div>
          </div>
        </div>
      </div>

      <ExportModal
        open={exportDialogOpen}
        onClose={handleExportModalClose}
        onExport={handleExport}
        filters={[]}
        columnDefs={getColumnDefinitions()}
        baseFileName={`Events_${containerId}_Forward-Pick_`}
      />
    </div>
  );
}

export default ContainerDetail;
