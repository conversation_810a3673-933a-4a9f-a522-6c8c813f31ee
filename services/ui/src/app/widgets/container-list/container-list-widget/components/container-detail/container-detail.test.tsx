import { Mock, vi } from "vitest";
import { render, screen, waitFor } from "../../../../../../test-utils";
import { ictApi } from "../../../../../api/ict-api";
import { ContainerDetail } from "./container-detail";

// Mock the React Router hooks
vi.mock("react-router", () => ({
  useParams: () => ({ containerId: "TEST-123" }),
  Link: ({ to, className, children }: any) => (
    <a href={to} className={className}>
      {children}
    </a>
  ),
}));

// Mock the Carbon components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
    Modal: ({
      children,
      open,
      modalHeading,
      primaryButtonText,
      secondaryButtonText,
      onRequestSubmit,
      onRequestClose,
      primaryButtonDisabled,
      ..._props
    }: any) =>
      open ? (
        <div data-testid="modal" {..._props}>
          <h2>{modalHeading}</h2>
          {children}
          <div className="modal-buttons">
            <button
              onClick={onRequestSubmit}
              disabled={primaryButtonDisabled}
              data-testid="modal-primary-button"
            >
              {primaryButtonText}
            </button>
            <button
              onClick={onRequestClose}
              data-testid="modal-secondary-button"
            >
              {secondaryButtonText}
            </button>
          </div>
        </div>
      ) : null,
  };
});

// Mock the useConfigSetting hook
vi.mock("../../../../../config/hooks/use-config", () => ({
  useConfigSetting: vi.fn(() => ({ setting: { value: "America/New_York" } })),
}));

// Mock child components
vi.mock("../../../../../components/view-bar/view-bar", () => ({
  ViewBar: ({ title }: { title: React.ReactNode }) => (
    <div data-testid="view-bar">{title}</div>
  ),
}));

vi.mock("./components/container-info-widget", () => ({
  ContainerInfoWidget: ({ isLoading }: { isLoading: boolean }) => (
    <div data-testid="container-info-widget">
      {isLoading ? "Loading..." : "Container Info"}
    </div>
  ),
}));

vi.mock("./components/metric-widget", () => ({
  __esModule: true,
  default: ({
    title,
    value,
    isLoading,
  }: {
    title: string;
    value: number;
    isLoading: boolean;
  }) => (
    <div data-testid={`metric-widget-${title}`}>
      {isLoading ? "Loading..." : `${title}: ${value}`}
    </div>
  ),
}));

vi.mock("./components/container-events-table", () => ({
  __esModule: true,
  default: ({ isLoading }: { isLoading: boolean }) => (
    <div data-testid="container-events-table">
      {isLoading ? "Loading events..." : "Container Events Table"}
    </div>
  ),
}));

describe("ContainerDetail", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test("renders loading state correctly", () => {
    // Mock loading state for both API calls
    ictApi.client.useQuery = vi.fn().mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
    });

    render(
      <ContainerDetail
        datasource="standard"
        containerId="test-container-123"
        onBack={() => {}}
      />,
    );

    expect(screen.getByTestId("container-info-widget")).toHaveTextContent(
      "Loading...",
    );
    expect(screen.getByTestId("container-events-table")).toHaveTextContent(
      "Loading events...",
    );
  });

  test("renders container data correctly when loaded", async () => {
    // Mock successful API responses
    const mockContainerData = {
      events: [
        {
          cycleCountEventsToday: 5,
          averageDailyCycleCount: 3.5,
          pickEventsToday: 10,
          averageDailyPickEvents: 8.2,
        },
      ],
    };

    const mockEventsData = {
      events: [
        { id: 1, type: "PICK", timestamp: "2023-01-01T12:00:00Z" },
        { id: 2, type: "CYCLE_COUNT", timestamp: "2023-01-02T14:30:00Z" },
      ],
    };

    // handle mocks for both standard and wms datasources
    (ictApi.client.useQuery as Mock)
      .mockReturnValueOnce({
        data: mockContainerData,
        isLoading: false,
        error: null,
      })
      .mockReturnValueOnce({
        data: undefined,
        isLoading: false,
        error: null,
      })
      .mockReturnValueOnce({
        data: mockEventsData,
        isLoading: false,
        error: null,
      });

    render(
      <ContainerDetail
        datasource="standard"
        containerId="test-container-123"
        onBack={() => {}}
      />,
    );

    await waitFor(() => {
      expect(screen.getByTestId("container-info-widget")).toHaveTextContent(
        "Container Info",
      );
      expect(screen.getByTestId("container-events-table")).toHaveTextContent(
        "Container Events Table",
      );
    });
  });

  test("makes API calls with correct parameters", () => {
    ictApi.client.useQuery = vi.fn().mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
    });

    render(
      <ContainerDetail
        datasource="standard"
        containerId="test-container-123"
        onBack={() => {}}
      />,
    );

    // Check first API call
    expect(ictApi.client.useQuery).toHaveBeenCalledWith(
      "get",
      "/inventory/container-events/{containerId}",
      { params: { path: { containerId: "test-container-123" } } },
      { enabled: true },
    );

    // Check second API call
    expect(ictApi.client.useQuery).toHaveBeenCalledWith(
      "post",
      "/inventory/container-events/list/{containerId}",
      {
        params: {
          path: { containerId: "test-container-123" },
        },
        body: { months: 1 },
      },
      { enabled: true },
    );
  });

  test("renders title with container ID", () => {
    ictApi.client.useQuery = vi.fn().mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
    });

    render(
      <ContainerDetail
        datasource="standard"
        containerId="test-container-123"
        onBack={() => {}}
      />,
    );

    const breadcrumbs = screen.getByTestId("container-detail");
    expect(breadcrumbs).toHaveTextContent("Container List");
    expect(breadcrumbs).toHaveTextContent("test-container-123");
  });
});
