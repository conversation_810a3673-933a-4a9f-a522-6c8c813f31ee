import { Analytics, DataBase } from "@carbon/icons-react";
import { ComboBox, TextInput, Toggle } from "@carbon/react";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { chartResolver } from "../../api/resolver/time-chart-resolver/time-chart-resolver";
import type { ChartInfo } from "../../api/resolver/time-chart-resolver/time-chart-resolver-types";
import type { CategoryChartType } from "../../api/resolver/time-chart-resolver/time-chart-types";
import { OptionsAccordionGroup } from "../../components/options/options-accordion-group/options-accordion-group";
import { OptionsAccordion } from "../../components/options/options-accordion/options-accordion";
import type { BaseWidgetOptionsProps } from "../widget.types";
import type { PieChartWidgetOptions } from "./types";
import { useTranslation } from "react-i18next";
import { DatePeriod } from "../../types";
import { PieType } from "src/app/components/pie-chart/pie-chart-component";
export const PieChartWidgetOptionsForm = ({
  options,
  onChange,
}: BaseWidgetOptionsProps<PieChartWidgetOptions>) => {
  const { t } = useTranslation();
  const pieChartOptions = options;

  const [selectedChart, setSelectedChart] = useState<ChartInfo | null>(null);

  const { data: categoryChartInfo } = useQuery({
    queryKey: ["category-chart-info"],
    queryFn: () => chartResolver.getCategoryChartInfo(),
  });

  useEffect(() => {
    if (categoryChartInfo) {
      const selectedMetric = categoryChartInfo?.find(
        (info) => info.id === pieChartOptions.type,
      );
      setSelectedChart(selectedMetric || null);
    }
  }, [categoryChartInfo, pieChartOptions.type]);

  const handleChartTypeChange = (selectedId: string | null) => {
    if (!selectedId) {
      return;
    }

    const newChartType = selectedId as CategoryChartType;
    const selected = categoryChartInfo?.find((info) => info.id === selectedId);
    if (selected) {
      onChange({
        ...pieChartOptions,
        type: newChartType,
        groupBy: "",
      });
    }
  };

  const handleGroupByChange = (selectedGroupBy: string | null) => {
    if (!selectedGroupBy) {
      return;
    }

    onChange({
      ...options,
      filters: {
        datePeriodRange: options.filters?.datePeriodRange || DatePeriod.today,
        ...options.filters,
        groupBy: selectedGroupBy,
      },
    });
  };

  const handlePieTypeChange = (selectedPieType: string | null) => {
    if (!selectedPieType) {
      return;
    }

    onChange({
      ...pieChartOptions,
      pieType: selectedPieType as PieType,
    });
  };

  const groupByOptions = selectedChart?.groupBy || [];

  return (
    <OptionsAccordion>
      <OptionsAccordionGroup
        id="display"
        title={t("pieChartWidgetOptions.displayTitle", "Display")}
        icon={<Analytics size="24" />}
      >
        <TextInput
          labelText={t("pieChartWidgetOptions.chartTitle", "Chart Title")}
          id="chart-title"
          value={pieChartOptions.title}
          onChange={(e) =>
            onChange({ ...pieChartOptions, title: e.target.value })
          }
        />
        <ComboBox
          titleText={t("pieChartWidgetOptions.pieType", "Pie Type")}
          id="pie-type"
          items={[
            {
              id: "donut",
              text: t("pieChartWidgetOptions.pieTypeDonut", "Donut"),
            },
            { id: "pie", text: t("pieChartWidgetOptions.pieTypePie", "Pie") },
          ]}
          selectedItem={
            pieChartOptions.pieType
              ? {
                  id: pieChartOptions.pieType,
                  // This transformation might need adjustment if 'Donut'/'Pie' needs translation itself
                  text:
                    pieChartOptions.pieType.charAt(0).toUpperCase() +
                    pieChartOptions.pieType.slice(1),
                }
              : {
                  id: "donut",
                  text: t("pieChartWidgetOptions.pieTypeDonut", "Donut"),
                }
          }
          onChange={({ selectedItem }) => {
            if (selectedItem) {
              handlePieTypeChange(selectedItem.id);
            }
          }}
          itemToString={(item) => (item ? item.text : "")}
        />
        <Toggle
          labelText={t(
            "pieChartWidgetOptions.showPercentage",
            "Show Percentage",
          )}
          id="show-percentage"
          toggled={pieChartOptions.showPercentage}
          onToggle={(toggled) =>
            onChange({
              ...pieChartOptions,
              showPercentage: toggled,
            })
          }
        />
        <Toggle
          labelText="Show Legend"
          id="show-legend"
          toggled={pieChartOptions.showLegend !== false}
          onToggle={(toggled) =>
            onChange({
              ...pieChartOptions,
              showLegend: toggled,
            })
          }
        />
      </OptionsAccordionGroup>
      <OptionsAccordionGroup
        id="data"
        title={t("pieChartWidgetOptions.dataTitle", "Data")}
        icon={<DataBase size="24" />}
      >
        <ComboBox
          titleText={t("pieChartWidgetOptions.chartType", "Chart Type")}
          id="chart-type"
          items={
            categoryChartInfo
              ? [...categoryChartInfo]
                  .sort((a, b) => a.title.localeCompare(b.title))
                  .map((info) => ({
                    id: info.id,
                    text: info.title, // Assuming title is translated or a key
                  }))
              : []
          }
          selectedItem={
            pieChartOptions.type
              ? {
                  id: pieChartOptions.type,
                  text:
                    categoryChartInfo?.find(
                      (info) => info.id === pieChartOptions.type,
                    )?.title || "",
                }
              : null
          }
          onChange={({ selectedItem }) => {
            if (selectedItem) {
              handleChartTypeChange(selectedItem.id);
            }
          }}
          itemToString={(item) => (item ? item.text : "")}
        />
        {pieChartOptions.type && groupByOptions.length > 0 && (
          <ComboBox
            titleText={t("pieChartWidgetOptions.groupBy", "Group By")}
            id="group-by"
            items={groupByOptions.map((option) => ({
              id: option,
              // Assuming this transformed text doesn't need translation
              text: option
                .split("_")
                .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                .join(" "),
            }))}
            selectedItem={
              pieChartOptions?.filters?.groupBy
                ? {
                    id: pieChartOptions.filters.groupBy,
                    text: pieChartOptions.filters.groupBy
                      .split("_")
                      .map(
                        (word) => word.charAt(0).toUpperCase() + word.slice(1),
                      )
                      .join(" "),
                  }
                : null
            }
            onChange={({ selectedItem }) => {
              if (selectedItem) {
                handleGroupByChange(selectedItem.id);
              }
            }}
            itemToString={(item) => (item ? item.text : "")}
          />
        )}
      </OptionsAccordionGroup>
    </OptionsAccordion>
  );
};

export default PieChartWidgetOptionsForm;
