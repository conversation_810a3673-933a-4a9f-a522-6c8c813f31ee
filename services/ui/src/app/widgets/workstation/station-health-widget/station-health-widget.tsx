import { Section } from "@carbon/react";
import { ictApi } from "../../../api/ict-api";
import { WidgetContainer } from "../../../components/widget-container/widget-container";
import type { WidgetFilters } from "../../widget.types";
import styles from "./station-health-widget.module.scss";
import { useTranslation } from "react-i18next";
import { useDates } from "../../../hooks/use-dates";
import { useWidgetAutoRefresh } from "../../../hooks/use-widget-auto-refresh";
import { Duration } from "../../../components/duration/duration";

export interface StationHealthWidgetProps {
  filters: WidgetFilters;
}

export const StationHealthWidget = ({ filters }: StationHealthWidgetProps) => {
  const { startDate, endDate } = useDates(filters.datePeriodRange);

  const { t } = useTranslation();

  const { data, error, isLoading, refetch } = ictApi.client.useQuery(
    "get",
    "/workstation/metrics/health",
    {
      params: {
        query: {
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
        },
      },
    },
    { enabled: true },
  );

  // Handle refresh click
  const handleRefresh = () => {
    refetch();
  };

  // Auto-refresh functionality using refetch directly
  useWidgetAutoRefresh({
    filters,
    refetch: handleRefresh,
  });

  if (isLoading)
    return (
      <WidgetContainer
        title={t("stationHealthWidget.title", "Station Health")}
        loading
      />
    );
  if (!data)
    return (
      <WidgetContainer
        title={t("stationHealthWidget.title", "Station Health")}
        noData={true}
      />
    );
  if (error)
    return (
      <WidgetContainer
        title={t("stationHealthWidget.title", "Station Health")}
        error={error}
      />
    );

  return (
    <WidgetContainer title={t("stationHealthWidget.title", "Station Health")}>
      <div className={styles.stationHealthMetrics}>
        <Section level={4} className={styles.downtime}>
          <span>{t("stationHealthWidget.downtime", "Downtime")}</span>
          <Duration duration={data.totalDowntime} type="minutes" />
        </Section>
      </div>
    </WidgetContainer>
  );
};

export default StationHealthWidget;
