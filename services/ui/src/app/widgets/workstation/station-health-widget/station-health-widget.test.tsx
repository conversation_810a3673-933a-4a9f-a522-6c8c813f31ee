import { DatePeriod } from "../../../types";

import { render, screen } from "../../../../test-utils";
import type { WidgetFilters } from "../../widget.types";
import { StationHealthWidget } from "./station-health-widget";

// Mock formatMinutes utility function
vi.mock("../../../utils", () => ({
  formatMinutes: (minutes: number) => `${minutes} mins`,
}));

vi.mock("../../../hooks/use-dates", () => ({
  useDates: () => ({
    startDate: new Date("2023-01-01"),
    endDate: new Date("2023-01-31"),
  }),
}));

// Mock ictApi.client.useQuery hook
const mockUseQuery = vi.fn();
vi.mock("../../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: (...args: unknown[]) => mockUseQuery(...args),
    },
  },
}));

describe("StationHealthWidget", () => {
  // Mock the required WidgetFilters with datePeriodRange using DatePeriod enum
  const mockFilters: WidgetFilters = {
    datePeriodRange: DatePeriod.thisMonth,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render loading state when data is being fetched", () => {
    mockUseQuery.mockReturnValue({
      isLoading: true,
      error: null,
      data: null,
    });

    render(<StationHealthWidget filters={mockFilters} />);
    expect(screen.getByTestId("widget-container-loader")).toBeInTheDocument();
  });

  it("hould show no data state when there is an error", () => {
    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: new Error("Test error"),
      data: null,
    });

    render(<StationHealthWidget filters={mockFilters} />);
    expect(screen.getByTestId("widget-container-no-data")).toBeInTheDocument();
  });

  it("should render widget content with correct data", () => {
    const mockData = {
      totalDowntime: 120, // 120 minutes
    };

    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: null,
      data: mockData,
    });

    render(<StationHealthWidget filters={mockFilters} />);

    // Check for title
    expect(screen.getByText("Station Health")).toBeInTheDocument();
    expect(screen.getByText("Downtime")).toBeInTheDocument();

    // Check for formatted downtime value
    expect(screen.getByTestId("labeled-value-Hours-value")).toHaveTextContent(
      "2",
    );
    expect(screen.getByTestId("labeled-value-Minutes-value")).toHaveTextContent(
      "0",
    );
  });

  it("should display 0 mins when totalDowntime is not provided", () => {
    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: null,
      data: {},
    });

    render(<StationHealthWidget filters={mockFilters} />);

    // Check for formatted downtime value with default of 0
    expect(screen.getByTestId("labeled-value-Hours-value")).toBeInTheDocument();
  });

  it("should make API call with correct parameters", () => {
    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: null,
      data: { totalDowntime: 60 },
    });

    render(<StationHealthWidget filters={mockFilters} />);

    // Verify the API call parameters
    expect(mockUseQuery).toHaveBeenCalledWith(
      "get",
      "/workstation/metrics/health",
      {
        params: {
          query: {
            start_date: "2023-01-01T00:00:00.000Z",
            end_date: "2023-01-31T00:00:00.000Z",
          },
        },
      },
      { enabled: true },
    );
  });
});
