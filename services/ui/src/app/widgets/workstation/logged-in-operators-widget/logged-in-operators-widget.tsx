import { Heading, ProgressBar, Section, Stack } from "@carbon/react";
import { ictApi } from "../../../api/ict-api";
import { WidgetContainer } from "../../../components/widget-container/widget-container";
import type { WidgetFilters } from "../../widget.types";
import styles from "./logged-in-operators-widget.module.scss";
import { useTranslation } from "react-i18next";
import { useWidgetAutoRefresh } from "../../../hooks/use-widget-auto-refresh";
import { LabeledValue } from "../../../components/labeled-value/labeled-value";

export interface LoggedInOperatorsWidgetProps {
  filters: WidgetFilters;
}

export const LoggedInOperatorsWidget = ({
  filters,
}: LoggedInOperatorsWidgetProps) => {
  const { t } = useTranslation();
  const { data, error, isLoading, refetch } = ictApi.client.useQuery(
    "get",
    "/workstation/metrics/operators",
    {
      params: {},
    },
    { enabled: true },
  );

  // Handle refresh click
  const handleRefresh = () => {
    refetch();
  };

  // Auto-refresh functionality using refetch directly
  useWidgetAutoRefresh({
    filters,
    refetch: handleRefresh,
  });

  if (isLoading)
    return (
      <WidgetContainer
        title={t("loggedInWidgetOperators.title", "Logged in Operators")}
        loading
      />
    );
  if (!data)
    return (
      <WidgetContainer
        title={t("loggedInWidgetOperators.title", "Logged in Operators")}
        noData={true}
      />
    );
  if (error)
    return (
      <WidgetContainer
        title={t("loggedInWidgetOperators.title", "Logged in Operators")}
        error={error}
      />
    );

  // Calculate progress percentage
  const progressPercentage =
    data.targetLinesPerHour > 0
      ? Math.round((data.actualLinesPerHour / data.targetLinesPerHour) * 100)
      : 0;

  return (
    <WidgetContainer
      title={t("loggedInWidgetOperators.title", "Logged in Operators")}
    >
      <Stack gap={4}>
        <Section level={1} className={styles.operatorCount}>
          <Heading data-testid="total-operators">{data.totalOperators}</Heading>
        </Section>
        <Section className={styles.linesPerHour}>
          <p className={styles.label}>
            {t(
              "loggedInWidgetOperators.totalLinesPerHour",
              "Total lines per hr",
            )}
          </p>
          <ProgressBar
            data-testid="progress-percentage"
            value={progressPercentage}
            max={100}
            label=""
            helperText={`${progressPercentage}%`}
            size="big"
          />
        </Section>
        <Section className={styles.lineCount}>
          <span>{t("loggedInWidgetOperators.lineCount", "Line Count")}</span>
          <div className={styles.lineCountMetrics}>
            <LabeledValue
              value={data.actualLinesPerHour}
              label={t("loggedInWidgetOperators.actualLabel", "Actual")}
            />
            <LabeledValue
              value={`/ ${data.targetLinesPerHour}`}
              size={1.5}
              label={t("loggedInWidgetOperators.expectedLabel", "Expected")}
            />
          </div>
        </Section>
      </Stack>
    </WidgetContainer>
  );
};

export default LoggedInOperatorsWidget;
