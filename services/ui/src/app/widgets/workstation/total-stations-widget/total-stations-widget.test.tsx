import { DatePeriod } from "../../../types";

import { render, screen } from "../../../../test-utils";
import type { WidgetFilters } from "../../widget.types";
import { TotalStationsWidget } from "./total-stations-widget";

// Mock utility functions
vi.mock("../../../hooks/use-dates", () => ({
  useDates: () => ({
    startDate: new Date("2023-01-01"),
    endDate: new Date("2023-01-31"),
  }),
}));

// Mock ictApi.client.useQuery hook
const mockUseQuery = vi.fn();
vi.mock("../../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: (...args: unknown[]) => mockUseQuery(...args),
    },
  },
}));

describe("TotalStationsWidget", () => {
  // Mock the required WidgetFilters with datePeriodRange using DatePeriod enum
  const mockFilters: WidgetFilters = {
    datePeriodRange: DatePeriod.thisMonth,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render loading state when data is being fetched", () => {
    mockUseQuery.mockReturnValue({
      isLoading: true,
      error: null,
      data: null,
    });

    render(<TotalStationsWidget filters={mockFilters} />);

    expect(screen.getByTestId("widget-container-loader")).toBeInTheDocument();
  });

  it("should show no data state when there is an error", () => {
    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: new Error("Test error"),
      data: null,
    });

    render(<TotalStationsWidget filters={mockFilters} />);

    expect(screen.getByTestId("widget-container-no-data")).toBeInTheDocument();
  });

  it("should render widget content with correct data", () => {
    const mockData = {
      totalWorkstations: 42,
      status: [
        { type: "Active", count: 30 },
        { type: "Inactive", count: 12 },
      ],
      workstationMode: [
        { type: "Workstation", count: 25 },
        { type: "Maintenance", count: 10 },
        { type: "Offline", count: 7 },
      ],
    };

    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: null,
      data: mockData,
    });

    render(<TotalStationsWidget filters={mockFilters} />);

    // Check for title and total count
    expect(screen.getByText("Total Stations")).toBeInTheDocument();
    expect(screen.getByText("42")).toBeInTheDocument();

    // Check for status section
    expect(screen.getByText("Status")).toBeInTheDocument();
    expect(screen.getByText("Active")).toBeInTheDocument();
    expect(screen.getByTestId("labeled-value-Active-value").innerHTML).toBe(
      "30",
    );
    expect(screen.getByText("Inactive")).toBeInTheDocument();
    expect(screen.getByTestId("labeled-value-Inactive-value").innerHTML).toBe(
      "12",
    );

    // Check for mode section
    expect(screen.getByText("Mode")).toBeInTheDocument();
    expect(screen.getByText("Workstation")).toBeInTheDocument();
    expect(
      screen.getByTestId("labeled-value-Workstation-value").innerHTML,
    ).toBe("25");
    expect(screen.getByText("Maintenance")).toBeInTheDocument();
    expect(
      screen.getByTestId("labeled-value-Maintenance-value").innerHTML,
    ).toBe("10");
    expect(screen.getByText("Offline")).toBeInTheDocument();
    expect(screen.getByTestId("labeled-value-Offline-value").innerHTML).toBe(
      "7",
    );
  });

  it("should handle missing status data", () => {
    const mockData = {
      totalWorkstations: 42,
      status: [], // Empty status array
      workstationMode: [
        { type: "Workstation", count: 25 },
        { type: "Maintenance", count: 10 },
      ],
    };

    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: null,
      data: mockData,
    });

    render(<TotalStationsWidget filters={mockFilters} />);

    // Check that the component renders without errors
    expect(screen.getByText("Total Stations")).toBeInTheDocument();
    expect(screen.getByText("42")).toBeInTheDocument();

    // Status values should be undefined
    expect(screen.getByText("Active")).toBeInTheDocument();
    expect(screen.getByTestId("labeled-value-Active-value").innerHTML).toBe(
      "--",
    );
    expect(screen.getByText("Inactive")).toBeInTheDocument();
    expect(screen.getByTestId("labeled-value-Inactive-value").innerHTML).toBe(
      "--",
    );
  });

  it("should handle missing workstationMode data", () => {
    const mockData = {
      totalWorkstations: 42,
      status: [
        { type: "Active", count: 30 },
        { type: "Inactive", count: 12 },
      ],
      workstationMode: [], // Empty workstationMode array
    };

    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: null,
      data: mockData,
    });

    render(<TotalStationsWidget filters={mockFilters} />);

    // Check that the component renders without errors
    expect(screen.getByText("Total Stations")).toBeInTheDocument();
    expect(screen.getByText("Mode")).toBeInTheDocument();
    // The workstationMode section should be empty but not crash
  });

  it("should make API call with correct parameters", () => {
    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: null,
      data: {
        totalWorkstations: 42,
        status: [
          { type: "Active", count: 30 },
          { type: "Inactive", count: 12 },
        ],
        workstationMode: [],
      },
    });

    render(<TotalStationsWidget filters={mockFilters} />);

    // Verify the API call parameters
    expect(mockUseQuery).toHaveBeenCalledWith(
      "get",
      "/workstation/metrics/summary",
      {
        params: {
          query: {
            start_date: "2023-01-01T00:00:00.000Z",
            end_date: "2023-01-31T00:00:00.000Z",
          },
        },
      },
      { enabled: true },
    );
  });
});
