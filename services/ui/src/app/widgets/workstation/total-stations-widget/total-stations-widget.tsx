import { Heading, Section, Stack } from "@carbon/react";
import { ictApi } from "../../../api/ict-api";
import { WidgetContainer } from "../../../components/widget-container/widget-container";
import type { WidgetFilters } from "../../widget.types";
import styles from "./total-stations-widget.module.scss";
import { useTranslation } from "react-i18next";
import { useDates } from "../../../hooks/use-dates";
import { useWidgetAutoRefresh } from "../../../hooks/use-widget-auto-refresh";
import { LabeledValue } from "../../../components/labeled-value/labeled-value";

export interface TotalStationsWidgetProps {
  filters: WidgetFilters;
}

export const TotalStationsWidget = ({ filters }: TotalStationsWidgetProps) => {
  const { t } = useTranslation();
  const { startDate, endDate } = useDates(filters.datePeriodRange);

  const { data, error, isLoading, refetch } = ictApi.client.useQuery(
    "get",
    "/workstation/metrics/summary",
    {
      params: {
        query: {
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
        },
      },
    },
    { enabled: true },
  );

  // Handle refresh click
  const handleRefresh = () => {
    refetch();
  };

  // Auto-refresh functionality using refetch directly
  useWidgetAutoRefresh({
    filters,
    refetch: handleRefresh,
  });

  if (isLoading)
    return (
      <WidgetContainer
        title={t("totalStationsWidgetProps.title", "Total Stations")}
        loading
      />
    );

  if (error)
    return (
      <WidgetContainer
        title={t("totalStationsWidgetProps.title", "Total Stations")}
        noData={true}
      />
    );

  const activeNumber = data?.status.find((s) => s.type === "Active")?.count;
  const inactiveNumber = data?.status.find((s) => s.type === "Inactive")?.count;

  return (
    <WidgetContainer
      title={t("totalStationsWidgetProps.title", "Total Stations")}
    >
      <Stack gap={4}>
        <Section level={1} className={styles.totalWorkstations}>
          <Heading data-testid="total-workstations">
            {data?.totalWorkstations}
          </Heading>
        </Section>
        <div className={styles.workstationMetrics}>
          <Section level={4} className={styles.status}>
            <span>{t("totalStationsWidgetProps.statusHeading", "Status")}</span>
            <div className={styles.statusMetrics}>
              <LabeledValue value={activeNumber ?? "--"} label="Active" />
              <LabeledValue value={inactiveNumber ?? "--"} label="Inactive" />
            </div>
          </Section>
          <Section level={4} className={styles.mode}>
            <span>{t("totalStationsWidgetProps.modeHeading", "Mode")}</span>
            <div
              className={styles.modeMetrics}
              style={{
                gridTemplateColumns: `repeat(${data?.workstationMode.length}, 1fr)`,
              }}
            >
              {data?.workstationMode.map((m) => (
                <LabeledValue key={m.type} value={m.count} label={m.type} />
              ))}
            </div>
          </Section>
        </div>
      </Stack>
    </WidgetContainer>
  );
};

export default TotalStationsWidget;
