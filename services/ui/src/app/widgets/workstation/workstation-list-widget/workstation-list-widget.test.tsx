import { vi } from "vitest";
import { fireEvent, render, screen } from "../../../../test-utils";
import { ictApi } from "../../../api/ict-api";
import { DatePeriod } from "../../../types/date-types";
import { WorkstationListWidget } from "./workstation-list-widget";

const startDate = new Date("2023-01-01");
const endDate = new Date("2023-01-31");

// Mock the ictApi
vi.mock("../../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: vi.fn(),
    },
  },
}));

vi.mock("../../../hooks/use-dates", () => ({
  useDates: () => ({
    startDate,
    endDate,
  }),
}));

// Mock the useMeasure hook
vi.mock("@uidotdev/usehooks", () => ({
  useMeasure: () => [vi.fn(), { width: 1000, height: 500 }],
}));

// Mock Carbon components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

// Mock the Datagrid component
vi.mock("../../../components/datagrid", () => ({
  Datagrid: ({
    data,
    isLoading,
    columns,
    showPagination,
    onRefreshClick,
  }: any) => (
    <div data-testid="datagrid">
      <div data-testid="loading-state">
        {isLoading ? "Loading..." : "Loaded"}
      </div>
      <div data-testid="row-count">{data?.length || 0} rows</div>
      <div data-testid="column-count">{columns?.length || 0} columns</div>
      <div data-testid="pagination-state">
        {showPagination ? "Pagination Enabled" : "Pagination Disabled"}
      </div>
      {onRefreshClick && (
        <button
          data-testid="refresh-button"
          type="button"
          onClick={onRefreshClick}
        >
          Refresh
        </button>
      )}
      <table>
        <thead>
          <tr>
            {columns?.map((col: any, i: number) => (
              <th key={i}>{col.header}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data?.map((row: any, i: number) => (
            <tr key={i}>
              {columns?.map((col: any, j: number) => (
                <td key={j}>
                  {col.cell({ getValue: () => row[col.accessorKey] })}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  ),
}));

// Mock the WidgetContainer component
vi.mock("../../../components/widget-container/widget-container", () => ({
  WidgetContainer: ({ children, title, noData }: any) => (
    <div data-testid="widget-container">
      <h3>{title}</h3>
      {noData ? (
        <div data-testid="widget-container-no-data">No Data Available</div>
      ) : (
        children
      )}
    </div>
  ),
}));

// Mock the utility functions
vi.mock("../../../utils", async () => {
  const actual = await vi.importActual("../../../utils");
  return {
    ...actual,
    formatMinutes: (minutes: number) => `${minutes} min`,
  };
});

describe("WorkstationListWidget", () => {
  const mockWorkstationData = {
    workstationList: [
      {
        workstation: "WS001",
        status: "Available",
        workMode: "Standard",
        workflowStatus: "Active",
        operatorId: "OP123",
        activeTime: 120,
        starvedTime: 10,
        idleTime: 5,
        blockedTime: 0,
        quantityPerHour: 45,
        weightedQuantityPerHour: 42.5,
        linesPerHour: 15,
        weightedLinesPerHour: 14.2,
        donorTotesPerHour: 8,
        orderTotesPerHour: 10,
      },
      {
        workstation: "WS002",
        status: "Paused",
        workMode: "Express",
        workflowStatus: "Paused",
        operatorId: "OP124",
        activeTime: 90,
        starvedTime: 20,
        idleTime: 15,
        blockedTime: 5,
        quantityPerHour: 35,
        weightedQuantityPerHour: 32.5,
        linesPerHour: 12,
        weightedLinesPerHour: 11.5,
        donorTotesPerHour: 6,
        orderTotesPerHour: 8,
      },
    ],
  };

  const defaultFilters = {
    datePeriodRange: DatePeriod.today,
  };

  const mockRefetch = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Default mock implementation for successful data fetch
    ictApi.client.useQuery = vi.fn().mockReturnValue({
      data: mockWorkstationData,
      isLoading: false,
      error: null,
      refetch: mockRefetch,
    });
  });

  it("renders the widget with workstation data", async () => {
    render(<WorkstationListWidget filters={defaultFilters} />);

    // Check if widget container is rendered with correct title
    expect(screen.getByTestId("widget-container")).toBeInTheDocument();
    expect(screen.getByText("Workstation List")).toBeInTheDocument();

    // Check if datagrid is rendered with correct data
    expect(screen.getByTestId("datagrid")).toBeInTheDocument();
    expect(screen.getByTestId("row-count")).toHaveTextContent("2 rows");

    // Check for column count (15 columns defined in the component)
    expect(screen.getByTestId("column-count")).toHaveTextContent("15 columns");

    // Check if loading state is correct
    expect(screen.getByTestId("loading-state")).toHaveTextContent("Loaded");

    // Check if pagination is disabled
    expect(screen.getByTestId("pagination-state")).toHaveTextContent(
      "Pagination Disabled",
    );
  });

  it("refreshes data when the refresh button is clicked", async () => {
    render(<WorkstationListWidget filters={defaultFilters} />);

    // Verify the refetch function hasn't been called yet
    expect(mockRefetch).not.toHaveBeenCalled();

    // Find and click the refresh button
    const refreshButton = screen.getByTestId("refresh-button");
    fireEvent.click(refreshButton);

    // Verify the refetch function was called
    expect(mockRefetch).toHaveBeenCalledTimes(1);
  });

  it("shows loading state while fetching data", async () => {
    ictApi.client.useQuery = vi.fn().mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
    });

    render(<WorkstationListWidget filters={defaultFilters} />);

    // Check if loading state is correct
    expect(screen.getByTestId("loading-state")).toHaveTextContent("Loading...");
  });

  it("should show no data state when API request fails", async () => {
    const mockError = { status: 404, message: "Not Found" };
    ictApi.client.useQuery = vi.fn().mockReturnValue({
      data: undefined,
      isLoading: false,
      error: mockError,
    });

    render(<WorkstationListWidget filters={defaultFilters} />);

    // Check if no data message is displayed
    expect(screen.getByTestId("widget-container-no-data")).toBeInTheDocument();
    expect(screen.getByTestId("widget-container-no-data")).toHaveTextContent(
      "No Data Available",
    );
  });

  it("formats cell values correctly, showing placeholders for empty values", async () => {
    // Mock data with some empty values
    const mockDataWithEmptyValues = {
      workstationList: [
        {
          workstation: "WS003",
          status: "Closed",
          workMode: "",
          workflowStatus: null,
          operatorId: undefined,
          activeTime: 0,
          starvedTime: null,
          idleTime: undefined,
          blockedTime: "",
          quantityPerHour: null,
          weightedQuantityPerHour: undefined,
          linesPerHour: 0,
          weightedLinesPerHour: "",
          donorTotesPerHour: null,
          orderTotesPerHour: undefined,
        },
      ],
    };

    vi.mocked(ictApi.client.useQuery).mockReturnValue({
      data: mockDataWithEmptyValues,
      isLoading: false,
      error: null,
    } as any);

    render(<WorkstationListWidget filters={defaultFilters} />);

    // Check if the widget renders with the mock data
    expect(screen.getByTestId("widget-container")).toBeInTheDocument();
    expect(screen.getByTestId("row-count")).toHaveTextContent("1 rows");
  });

  it("makes API request with correct date parameters", async () => {
    render(<WorkstationListWidget filters={defaultFilters} />);

    // Check if the API was called with the correct parameters
    expect(ictApi.client.useQuery).toHaveBeenCalledWith(
      "post",
      "/workstation/list",
      {
        body: {
          page: 0,
          limit: 50,
          filters: undefined,
          searchString: undefined,
          sortFields: [],
        },
      },
      { enabled: true },
    );
  });

  it("uses the date range from filters", async () => {
    const customFilters = {
      datePeriodRange: DatePeriod.yesterday,
    };

    render(<WorkstationListWidget filters={customFilters} />);

    // Check if the API was called with the correct date parameters
    expect(ictApi.client.useQuery).toHaveBeenCalledWith(
      "post",
      "/workstation/list",
      {
        body: {
          page: 0,
          limit: 50,
          filters: undefined,
          searchString: undefined,
          sortFields: [],
        },
      },
      { enabled: true },
    );
  });

  it("renders status tags with correct colors", async () => {
    // This test is more limited since we're mocking the Datagrid component
    // In a real test environment, we could check the actual rendered colors
    render(<WorkstationListWidget filters={defaultFilters} />);

    // We can at least verify the component renders without errors
    expect(screen.getByTestId("widget-container")).toBeInTheDocument();
  });
});
