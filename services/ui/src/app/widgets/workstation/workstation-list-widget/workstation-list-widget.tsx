import {
  ColumnFiltersState,
  create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { useMeasure } from "@uidotdev/usehooks";
import { useCallback, useMemo, useState } from "react";
import { ictApi } from "../../../api/ict-api";
import { Datagrid, FilterState } from "../../../components/datagrid";
import { ExportModal } from "../../../components/export-modal/export-modal";
import { WidgetContainer } from "../../../components/widget-container/widget-container";
import { formatMinutes, formatTimeValue } from "../../../utils";
import type { WidgetFilters } from "../../widget.types";
import styles from "./workstation-list-widget.module.css";
import { useTranslation } from "react-i18next";
import { useWidgetAutoRefresh } from "../../../hooks/use-widget-auto-refresh";
import { transformFilters } from "../../../api/util/filter-transform-util";

export interface WorkstationListWidgetProps {
  filters: WidgetFilters;
}

type WorkstationData = {
  workstation: string;
  status: string;
  workMode: string;
  workflowStatus: string;
  operatorId: string;
  activeTimeSecs: number;
  starvedTimeMins: number;
  idleTimeMins: number;
  blockedTimeMins: number;
  quantityPerHour: number | null;
  weightedQuantityPerHour: number | null;
  linesPerHour: number | null;
  weightedLinesPerHour: number | null;
  donorTotesPerHour: number | null;
  orderTotesPerHour: number | null;
};

export const WorkstationListWidget = ({
  filters,
}: WorkstationListWidgetProps) => {
  const { t } = useTranslation();
  const [containerRef, containerMeasures] = useMeasure();
  const [exportModalOpen, setExportModalOpen] = useState(false);
  const [pagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 50,
  });
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>("");
  const sortFields = useMemo(
    () =>
      sorting.map((sort) => ({
        columnName: sort.id,
        isDescending: sort.desc,
      })),
    [sorting],
  );
  const apiFilters = useMemo(
    () => transformFilters(columnFilters),
    [columnFilters],
  );

  const { data, error, isLoading, isRefetching, refetch } =
    ictApi.client.useQuery(
      "post",
      "/workstation/list",
      {
        body: {
          page: pagination.pageIndex,
          limit: pagination.pageSize,
          filters: apiFilters,
          sortFields,
          searchString: globalFilter || undefined,
        },
      },
      { enabled: true },
    );

  // Handle refresh click
  const handleRefresh = () => {
    refetch();
  };

  // Auto-refresh functionality using refetch directly
  useWidgetAutoRefresh({
    filters,
    refetch: handleRefresh,
  });

  // Function to generate column definitions for the export dialog
  const getColumnDefinitions = () => [
    {
      id: "workstation",
      header: t("chartWidgetOptions.workstation", "Workstation"),
    },
    { id: "status", header: t("chartWidgetOptions.status", "Status") },
    { id: "workMode", header: t("chartWidgetOptions.workMode", "Work Mode") },
    {
      id: "workflowStatus",
      header: t("chartWidgetOptions.workflowStatus", "Workflow Status"),
    },
    {
      id: "operatorId",
      header: t("chartWidgetOptions.operatorId", "Operator ID"),
    },
    {
      id: "activeTimeSecs",
      header: t("chartWidgetOptions.activeTime", "Active Time"),
    },
    {
      id: "starvedTimeMins",
      header: t("chartWidgetOptions.starvedTime", "Starved Time"),
    },
    {
      id: "idleTimeMins",
      header: t("chartWidgetOptions.idleTime", "Idle Time"),
    },
    {
      id: "blockedTimeMins",
      header: t("chartWidgetOptions.blockedTime", "Blocked Time"),
    },
    {
      id: "linesPerHour",
      header: t("chartWidgetOptions.linesPerHour", "Lines/Hour"),
    },
    {
      id: "weightedLinesPerHour",
      header: t(
        "chartWidgetOptions.weightedLinesPerHour",
        "Weighted Lines/Hour",
      ),
    },
    {
      id: "quantityPerHour",
      header: t("chartWidgetOptions.quantityPerHour", "Quantity/Hour"),
    },
    {
      id: "weightedQuantityPerHour",
      header: t(
        "chartWidgetOptions.weightedQuantityPerHour",
        "Weighted Quantity/Hour",
      ),
    },
    {
      id: "donorTotesPerHour",
      header: t("chartWidgetOptions.donorTotesPerHour", "Donor Totes/Hour"),
    },
    {
      id: "orderTotesPerHour",
      header: t("chartWidgetOptions.orderTotesPerHour", "Order Totes/Hour"),
    },
  ];

  // Export handler
  const handleExport = async (
    fileName: string,
    selectedColumns: Record<string, boolean>,
  ) => {
    const exportUrl = "/workstation/list/export";

    const requestBody = {
      page: pagination.pageIndex,
      limit: pagination.pageSize,
      filters: apiFilters,
      sortFields,
      searchString: globalFilter || undefined,
      columns: selectedColumns,
    };

    try {
      const { error, data: blob } = await ictApi.fetchClient.POST(exportUrl, {
        body: requestBody,
        parseAs: "blob",
      });

      if (error) {
        throw new Error(`Export failed with error: ${error}`);
      }

      if (!blob) {
        throw new Error("No data to export");
      }

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (err: unknown) {
      // Changed to unknown
      if (err instanceof Error && err.name === "AbortError") {
        // Type check
        console.log("Export cancelled by user.");
      } else {
        console.error("Error during export submission:", err);
        alert("An error occurred during export. Please try again.");
      }
      throw err;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Available":
        return "green";
      case "Paused":
        return "gray";
      case "Closed":
        return "red";
      default:
        return "gray";
    }
  };

  const StatusTag = ({ value }: { value: string }) => (
    <div className={styles.statusCell}>
      <span
        style={{
          backgroundColor:
            getStatusColor(value) === "green"
              ? "#4caf50"
              : getStatusColor(value) === "red"
                ? "#f44336"
                : "#9e9e9e",
          color: "white",
          padding: "4px 8px",
          borderRadius: "4px",
          fontSize: "12px",
          fontWeight: "bold",
        }}
      >
        {value}
      </span>
    </div>
  );

  const formatCellValue = (value: number | string | null | undefined) => {
    if (value === null || value === undefined || value === "") {
      return <span className={styles.emptyCell}>--</span>;
    }
    return value;
  };

  const columnHelper = createColumnHelper<WorkstationData>();

  const columns = [
    columnHelper.accessor("workstation", {
      header: t("chartWidgetOptions.workstation", "Workstation"),
      size: 150,
      cell: (info) => info.getValue(),
    }),
    columnHelper.accessor("status", {
      header: t("chartWidgetOptions.status", "Status"),
      size: 150,
      cell: (info) => <StatusTag value={info.getValue()} />,
    }),
    columnHelper.accessor("workMode", {
      header: t("chartWidgetOptions.workMode", "Work Mode"),
      size: 120,
      cell: (info) => formatCellValue(info.getValue()),
    }),
    columnHelper.accessor("workflowStatus", {
      header: t("chartWidgetOptions.workflowStatus", "Workflow Status"),
      size: 150,
      cell: (info) => formatCellValue(info.getValue()),
    }),
    columnHelper.accessor("operatorId", {
      header: t("chartWidgetOptions.operatorId", "Operator ID"),
      size: 120,
      cell: (info) => formatCellValue(info.getValue()),
    }),
    columnHelper.accessor("activeTimeSecs", {
      header: t("chartWidgetOptions.activeTime", "Active Time"),
      size: 120,
      cell: (info) =>
        formatCellValue(
          info.getValue() !== null && info.getValue() !== undefined
            ? formatTimeValue(info.getValue(), "seconds")
            : null,
        ),
    }),
    columnHelper.accessor("starvedTimeMins", {
      header: t("chartWidgetOptions.starvedTime", "Starved Time"),
      size: 120,
      cell: (info) =>
        formatCellValue(
          info.getValue() !== null && info.getValue() !== undefined
            ? formatMinutes(info.getValue())
            : null,
        ),
    }),
    columnHelper.accessor("idleTimeMins", {
      header: t("chartWidgetOptions.idleTime", "Idle Time"),
      size: 120,
      cell: (info) =>
        formatCellValue(
          info.getValue() !== null && info.getValue() !== undefined
            ? formatMinutes(info.getValue())
            : null,
        ),
    }),
    columnHelper.accessor("blockedTimeMins", {
      header: t("chartWidgetOptions.blockedTime", "Blocked Time"),
      size: 120,
      cell: (info) =>
        formatCellValue(
          info.getValue() !== null && info.getValue() !== undefined
            ? formatMinutes(info.getValue())
            : null,
        ),
    }),
    columnHelper.accessor("linesPerHour", {
      header: t("chartWidgetOptions.linesPerHour", "Lines/Hour"),
      size: 120,
      cell: (info) => formatCellValue(info.getValue()),
    }),
    columnHelper.accessor("weightedLinesPerHour", {
      header: t(
        "chartWidgetOptions.weightedLinesPerHour",
        "Weighted Lines/Hour",
      ),
      size: 150,
      cell: (info) => formatCellValue(info.getValue()),
    }),
    columnHelper.accessor("quantityPerHour", {
      header: t("chartWidgetOptions.quantityPerHour", "Quantity/Hour"),
      size: 130,
      cell: (info) => formatCellValue(info.getValue()),
    }),
    columnHelper.accessor("weightedQuantityPerHour", {
      header: t(
        "chartWidgetOptions.weightedQuantityPerHour",
        "Weighted Quantity/Hour",
      ),
      size: 180,
      cell: (info) => formatCellValue(info.getValue()),
    }),
    columnHelper.accessor("donorTotesPerHour", {
      header: t("chartWidgetOptions.donorTotesPerHour", "Donor Totes/Hour"),
      size: 150,
      cell: (info) => formatCellValue(info.getValue()),
    }),
    columnHelper.accessor("orderTotesPerHour", {
      header: t("chartWidgetOptions.orderTotesPerHour", "Order Totes/Hour"),
      size: 150,
      cell: (info) => formatCellValue(info.getValue()),
    }),
  ];

  if (error) {
    return (
      <WidgetContainer
        title={t("chartWidgetOptions.workstationListTitle", "Workstation List")}
        noData={true}
      />
    );
  }

  const handleFilter = useCallback(
    (newFilters: FilterState) => {
      // Convert FilterState to ColumnFiltersState
      const newColumnFilters: ColumnFiltersState = Object.entries(
        newFilters.filters,
      ).map(([id, value]) => {
        // Extract just the property name if it contains a dot
        const columnName = id.includes(".") ? id.split(".").pop()! : id;
        return {
          id: columnName,
          value,
        };
      });
      setColumnFilters(newColumnFilters);
      setGlobalFilter(newFilters.globalFilter);
    },
    [setColumnFilters, setGlobalFilter],
  );

  const handleSort = useCallback(
    (newSorting: SortingState) => {
      setSorting(newSorting);
    },
    [setSorting],
  );

  const tableHeight = containerMeasures.height
    ? containerMeasures.height - 20
    : 300;

  return (
    <WidgetContainer
      title={t("chartWidgetOptions.workstationListTitle", "Workstation List")}
    >
      <div
        ref={containerRef}
        className={styles.tableContainer}
        onMouseDown={(e) => e.stopPropagation()}
        onTouchStart={(e) => e.stopPropagation()}
        style={{
          height: "100%",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <div style={{ height: `${tableHeight}px` }}>
          <Datagrid
            initialPagination={pagination}
            // mode="server"
            columns={columns}
            onSort={handleSort}
            onFilter={handleFilter}
            data={data?.workstationList || []}
            enableSelection={false}
            isLoading={isLoading || isRefetching}
            showPagination={false}
            showRefreshButton={true}
            onRefreshClick={handleRefresh}
            showExportButton={true}
            onExport={() => setExportModalOpen(true)}
          />
        </div>
      </div>
      <ExportModal
        open={exportModalOpen}
        onClose={() => setExportModalOpen(false)}
        onExport={handleExport}
        columnDefs={getColumnDefinitions()}
        filters={[]}
        globalFilter={globalFilter}
        baseFileName="Workstation_Stations_List"
      />
    </WidgetContainer>
  );
};

export default WorkstationListWidget;
