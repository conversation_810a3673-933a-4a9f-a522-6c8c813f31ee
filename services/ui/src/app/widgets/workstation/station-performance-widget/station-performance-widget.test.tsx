import { DatePeriod } from "../../../types";

import { render, screen } from "../../../../test-utils";
import type { WidgetFilters } from "../../widget.types";
import { StationPerformanceWidget } from "./station-performance-widget";

// Mock formatMinutes utility function
vi.mock("../../../utils", () => ({
  formatMinutes: (minutes: number) => `${minutes} mins`,
}));

vi.mock("../../../hooks/use-dates", () => ({
  useDates: () => ({
    startDate: new Date("2023-01-01"),
    endDate: new Date("2023-01-31"),
  }),
}));

// Mock ictApi.client.useQuery hook
const mockUseQuery = vi.fn();
vi.mock("../../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: (...args: unknown[]) => mockUseQuery(...args),
    },
  },
}));

describe("StationPerformanceWidget", () => {
  // Mock the required WidgetFilters with datePeriodRange using DatePeriod enum
  const mockFilters: WidgetFilters = {
    datePeriodRange: DatePeriod.thisMonth,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render loading state when data is being fetched", () => {
    mockUseQuery.mockReturnValue({
      isLoading: true,
      error: null,
      data: null,
    });

    render(<StationPerformanceWidget filters={mockFilters} />);

    expect(screen.getByTestId("widget-container-loader")).toBeInTheDocument();
  });

  it("hould show no data state when there is an error", () => {
    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: new Error("Test error"),
      data: null,
    });

    render(<StationPerformanceWidget filters={mockFilters} />);

    expect(screen.getByTestId("widget-container-no-data")).toBeInTheDocument();
  });

  it("should render widget content with correct data", () => {
    const mockData = {
      totalActiveTime: 482, // 482 minutes
      totalStarvationTime: 123, // 123 minutes
    };

    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: null,
      data: mockData,
    });

    render(<StationPerformanceWidget filters={mockFilters} />);

    // Check for title
    expect(screen.getByText("Station Performance")).toBeInTheDocument();

    // Check for active time section
    expect(screen.getByText("Total Active Time")).toBeInTheDocument();
    expect(
      screen.getAllByTestId("labeled-value-Hours-value")[0],
    ).toHaveTextContent("8");
    expect(
      screen.getAllByTestId("labeled-value-Minutes-value")[0],
    ).toHaveTextContent("2");

    // Check for starved time section
    expect(screen.getByText("Total Starved Time")).toBeInTheDocument();
    expect(
      screen.getAllByTestId("labeled-value-Hours-value")[1],
    ).toHaveTextContent("2");
    expect(
      screen.getAllByTestId("labeled-value-Minutes-value")[1],
    ).toHaveTextContent("3");
  });

  it("should make API call with correct parameters", () => {
    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: null,
      data: { totalActiveTime: 300, totalStarvationTime: 60 },
    });

    render(<StationPerformanceWidget filters={mockFilters} />);

    // Verify the API call parameters
    expect(mockUseQuery).toHaveBeenCalledWith(
      "get",
      "/workstation/metrics/performance",
      {
        params: {
          query: {
            start_date: "2023-01-01T00:00:00.000Z",
            end_date: "2023-01-31T00:00:00.000Z",
          },
        },
      },
      { enabled: true },
    );
  });
});
