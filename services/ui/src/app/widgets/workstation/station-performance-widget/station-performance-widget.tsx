import { Section } from "@carbon/react";
import { ictApi } from "../../../api/ict-api";
import { WidgetContainer } from "../../../components/widget-container/widget-container";
import type { WidgetFilters } from "../../widget.types";
import styles from "./station-performance-widget.module.scss";
import { useDates } from "../../../hooks/use-dates";
import { useTranslation } from "react-i18next";
import { useWidgetAutoRefresh } from "../../../hooks/use-widget-auto-refresh";
import { Duration } from "../../../components/duration/duration";

export interface StationPerformanceWidgetProps {
  filters: WidgetFilters;
}

export const StationPerformanceWidget = ({
  filters,
}: StationPerformanceWidgetProps) => {
  const { t } = useTranslation();
  const { startDate, endDate } = useDates(filters.datePeriodRange);

  const { data, error, isLoading, refetch } = ictApi.client.useQuery(
    "get",
    "/workstation/metrics/performance",
    {
      params: {
        query: {
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
        },
      },
    },
    { enabled: true },
  );

  // Handle refresh click
  const handleRefresh = () => {
    refetch();
  };

  // Auto-refresh functionality using refetch directly
  useWidgetAutoRefresh({
    filters,
    refetch: handleRefresh,
  });

  if (isLoading)
    return (
      <WidgetContainer
        title={t("stationPerformanceWidget.title", "Station Performance")}
        loading
      />
    );
  if (!data)
    return (
      <WidgetContainer
        title={t("stationPerformanceWidget.title", "Station Performance")}
        noData={true}
      />
    );
  if (error)
    return (
      <WidgetContainer
        title={t("stationPerformanceWidget.title", "Station Performance")}
        error={error}
      />
    );

  return (
    <WidgetContainer
      title={t("stationPerformanceWidget.title", "Station Performance")}
    >
      <div className={styles.stationPerformanceMetrics}>
        <Section level={4} className={styles.totalActiveTime}>
          <span>
            {t("stationPerformanceWidget.totalActiveTime", "Total Active Time")}
          </span>
          <Duration duration={data.totalActiveTime} type="minutes" />
        </Section>
        <Section level={4} className={styles.totalStarvedTime}>
          <span>
            {t(
              "stationPerformanceWidget.totalStarvedTime",
              "Total Starved Time",
            )}
          </span>
          <Duration duration={data.totalStarvationTime} type="minutes" />
        </Section>
      </div>
    </WidgetContainer>
  );
};

export default StationPerformanceWidget;
