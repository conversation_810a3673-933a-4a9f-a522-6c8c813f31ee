import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router";
import { useCallback } from "react";
import { WidgetFilters } from "../../widget.types";
import { ictApi } from "../../../api/ict-api";
import { useDates } from "../../../hooks/use-dates";
import { WidgetContainer } from "../../../components/widget-container/widget-container";
import { useWidgetAutoRefresh } from "../../../hooks/use-widget-auto-refresh";
import {
  Heading,
  InlineNotification,
  Link,
  Section,
  Stack,
} from "@carbon/react";
import {
  ArrowRight,
  CheckmarkFilled,
  WarningAltFilled,
} from "@carbon/icons-react";
import styles from "./workstation-order-status-widget.module.scss";

export interface WorkstationOrderStatusWidgetProps {
  filters: WidgetFilters;
}

export const WorkstationOrderStatusWidget = ({
  filters,
}: WorkstationOrderStatusWidgetProps) => {
  const { startDate, endDate } = useDates(filters.datePeriodRange);
  const { t } = useTranslation();
  const navigate = useNavigate();

  const { data, error, isLoading, refetch } = ictApi.client.useQuery(
    "get",
    "/workstation/orders/status",
    {
      params: {
        query: {
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
        },
      },
    },
  );

  // Handle refresh click
  const handleRefresh = () => {
    refetch();
  };

  // Auto-refresh functionality using refetch directly
  useWidgetAutoRefresh({
    filters,
    refetch: handleRefresh,
  });

  const widgetTitle = t("workstation.orderStatusWidget.title", "Order Status");

  if (isLoading) {
    return <WidgetContainer title={widgetTitle} loading />;
  }

  if (error) {
    return <WidgetContainer title={widgetTitle} error={error} />;
  }

  if (!data) {
    return <WidgetContainer title={widgetTitle} noData />;
  }

  const hasDelayed = data.delayedOrders > 0;

  const handleDrillDown = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      navigate("/ict-workstation-active-orders");
    },
    [navigate],
  );

  return (
    <WidgetContainer title={widgetTitle}>
      <div
        className={styles.container}
        onMouseDown={(e: React.MouseEvent<HTMLDivElement>) =>
          e.stopPropagation()
        }
        onTouchStart={(e: React.TouchEvent<HTMLDivElement>) =>
          e.stopPropagation()
        }
      >
        <Stack gap={4}>
          <Section level={1}>
            <Heading className={styles.statusHeader}>
              {hasDelayed ? (
                <>
                  <WarningAltFilled
                    size={24}
                    className={styles.warningIconFill}
                  />{" "}
                  {t(
                    "workstation.orderStatusWidget.status.delayed",
                    "{{amount}} Delayed",
                    {
                      amount: data.delayedOrders,
                    },
                  )}
                </>
              ) : (
                <>
                  <CheckmarkFilled
                    size={24}
                    className={styles.checkmarkIconFill}
                  />{" "}
                  {t("workstation.orderStatusWidget.status.healthy", "Healthy")}
                </>
              )}
            </Heading>
          </Section>
          <Section className={styles.activeOrders}>
            {data.activeOrders === 1
              ? t(
                  "workstation.orderStatusWidget.activeOrder.singular",
                  "1 active order",
                )
              : t(
                  "workstation.orderStatusWidget.activeOrders.plural",
                  "{{amount}} active orders",
                  { amount: data.activeOrders },
                )}
          </Section>
          <Section>
            {hasDelayed && (
              <InlineNotification
                className={styles.notification}
                hideCloseButton={true}
                kind="warning-alt"
                title={t(
                  "workstation.orderStatusWidget.notice.title",
                  "Notice",
                )}
                subtitle={
                  data.delayedOrders === 1
                    ? t(
                        "workstation.orderStatusWidget.notice.subtitle.singular",
                        "1 active order has been open for over 45 minutes",
                      )
                    : t(
                        "workstation.orderStatusWidget.notice.subtitle.plural",
                        "{{amount}} active orders have been open for over 45 minutes",
                        { amount: data.delayedOrders },
                      )
                }
              />
            )}
          </Section>
        </Stack>
        <div className={styles.drillDownArrow}>
          <Link
            style={{ cursor: "pointer" }}
            renderIcon={ArrowRight}
            onClick={handleDrillDown}
            onMouseDown={(e: React.MouseEvent) => e.stopPropagation()}
            onTouchStart={(e: React.TouchEvent) => e.stopPropagation()}
          />
        </div>
      </div>
    </WidgetContainer>
  );
};

export default WorkstationOrderStatusWidget;
