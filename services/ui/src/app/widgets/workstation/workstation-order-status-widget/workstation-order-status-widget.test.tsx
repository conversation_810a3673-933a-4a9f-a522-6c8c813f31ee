import { DatePeriod } from "../../../types";
import { WidgetFilters } from "../../widget.types";
import WorkstationOrderStatusWidget from "./workstation-order-status-widget";
import { render, screen } from "../../../../test-utils";

// Mock React Router
const mockNavigate = vi.fn();
vi.mock("react-router", () => ({
  useNavigate: () => mockNavigate,
}));

// Mock utility functions
vi.mock("../../../hooks/use-dates", () => ({
  useDates: () => ({
    startDate: new Date("2023-01-01"),
    endDate: new Date("2023-01-31"),
  }),
}));

// Mock ictApi.client.useQuery hook
const mockUseQuery = vi.fn();
vi.mock("../../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: (...args: unknown[]) => mockUseQuery(...args),
    },
  },
}));

describe(WorkstationOrderStatusWidget.name, () => {
  // Mock the required WidgetFilters with datePeriodRange using DatePeriod enum
  const mockFilters: WidgetFilters = {
    datePeriodRange: DatePeriod.today,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render loading state when data is being fetched", () => {
    mockUseQuery.mockReturnValue({
      isLoading: true,
      error: null,
      data: null,
    });

    render(<WorkstationOrderStatusWidget filters={mockFilters} />);

    expect(screen.getByTestId("widget-container-loader")).toBeInTheDocument();
  });

  it("should show error state when there is an error", () => {
    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: new Error("Test error"),
      data: null,
    });

    render(<WorkstationOrderStatusWidget filters={mockFilters} />);

    expect(screen.getByTestId("widget-container-error")).toBeInTheDocument();
  });

  it("should render widget as healthy when no delayed orders", () => {
    const mockData = {
      activeOrders: 27,
      delayedOrders: 0,
    };

    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: null,
      data: mockData,
    });

    render(<WorkstationOrderStatusWidget filters={mockFilters} />);

    expect(screen.getByText("Healthy")).toBeInTheDocument();
    expect(screen.getByText("27 active orders")).toBeInTheDocument();
    expect(screen.queryByText("Delayed")).not.toBeInTheDocument();
  });

  it("should render widget as delayed when there are delayed orders", () => {
    const mockData = {
      activeOrders: 27,
      delayedOrders: 1,
    };

    mockUseQuery.mockReturnValue({
      isLoading: false,
      error: null,
      data: mockData,
    });

    render(<WorkstationOrderStatusWidget filters={mockFilters} />);

    expect(screen.getByText("1 Delayed")).toBeInTheDocument();
    expect(screen.getByText("27 active orders")).toBeInTheDocument();
    expect(
      screen.getByText("1 active order has been open for over 45 minutes"),
    ).toBeInTheDocument();
  });
});
