import { metricDefinitions } from "./kpi-resolver/kpi-resolver-data";
import { timeChartDefinitions } from "./time-chart-resolver/time-chart-resolver-data";

// Export all metric and chart definitions to easily find the endpoint based on the id
export const metricAndChartDefinitions = [
  ...metricDefinitions.map((metricDef) => ({
    id: metricDef.id,
    endpoint: metricDef.endpoint,
  })),
  ...timeChartDefinitions.map((chartDef) => ({
    id: chartDef.id,
    endpoint: chartDef.endpoint,
  })),
];
