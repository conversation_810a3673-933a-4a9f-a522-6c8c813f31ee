import { logger } from "../../utils";
import { TableauProject, TableauWorkbook } from "./endpoints";

export class TableauApiUtil {
  /**
   * Filters workbooks to only include those in the specified top-level project's tree.
   */
  static filterWorkbooksByTopLevelProject(
    workbooks: TableauWorkbook[],
    projects: TableauProject[],
    topLevelProjectName: string,
  ): TableauWorkbook[] {
    const projectHierarchy = new Map<string, string | undefined>();
    const topLevelProject = projects.find(
      (project) =>
        project.name.toUpperCase() === topLevelProjectName.toUpperCase(),
    );

    if (!topLevelProject) {
      logger.warn(`Top level project '${topLevelProjectName}' not found`);
      return [];
    }

    // Build project hierarchy
    for (const project of projects) {
      projectHierarchy.set(project.id, project.parentProjectId);
    }

    return workbooks.filter((workbook) =>
      this.isProjectInProjectTree(
        workbook.project.id,
        projectHierarchy,
        topLevelProject.id,
      ),
    );
  }

  /**
   * Checks if a project is within the specified top-level project's tree.
   *
   * @param projectId - Project ID to check
   * @param projectHierarchy - Map of project relationships
   * @param topProjectId - ID of the top-level project
   * @returns True if the projectId is in the topProjectId's project tree
   */
  static isProjectInProjectTree(
    projectId: string,
    projectHierarchy: Map<string, string | undefined>,
    topProjectId: string,
  ): boolean {
    if (projectId === topProjectId) {
      return true;
    }

    const parentProjectId = projectHierarchy.get(projectId);
    if (!parentProjectId) {
      return false;
    }

    return this.isProjectInProjectTree(
      parentProjectId,
      projectHierarchy,
      topProjectId,
    );
  }

  /**
   * Filters projects to only include those in the specified facility's tree.
   */
  static filterProjectsByFacility(
    projects: TableauProject[],
    facilityName: string,
  ): TableauProject[] {
    const projectHierarchy = new Map<string, string | undefined>();
    const topLevelProject = projects.find(
      (project) => project.name.toUpperCase() === facilityName.toUpperCase(),
    );

    if (!topLevelProject) {
      logger.warn(
        `Top level project for facility '${facilityName}' not found for alert filtering`,
      );
      return [];
    }

    // Build project hierarchy
    for (const project of projects) {
      projectHierarchy.set(project.id, project.parentProjectId);
    }

    return projects.filter((project) =>
      this.isProjectInProjectTree(
        project.id,
        projectHierarchy,
        topLevelProject.id,
      ),
    );
  }
}
