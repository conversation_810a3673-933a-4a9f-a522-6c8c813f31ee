import type { MiddlewareCallbackParams } from "openapi-fetch";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { useTableNamesStore } from "../../stores/table-names-store";
import { tableNamesMiddleware } from "./table-names-middleware";

// Mock the table names store
vi.mock("../../stores/table-names-store", () => ({
  useTableNamesStore: {
    getState: vi.fn(),
  },
}));

describe("tableNamesMiddleware", () => {
  const options: any = {};
  const mockSetTableNames = vi.fn();

  // Create a complete middleware callback params object
  const createCallbackParams = (
    request: Request,
    response: Response,
  ): MiddlewareCallbackParams & { response: Response } => ({
    request,
    response,
    schemaPath: "/test",
    params: {},
    id: "test",
    options,
  });

  beforeEach(() => {
    vi.resetAllMocks();
    vi.mocked(useTableNamesStore.getState).mockReturnValue({
      setTableNames: mockSetTableNames,
      getTableNames: vi.fn(),
      tableNamesMap: new Map(),
    });
  });

  it("should extract table names from response headers and store them", async () => {
    // Arrange
    const requestUrl = "https://api.example.com/api/data";
    const request = new Request(requestUrl);
    const tableNames = "table1,table2,table3";

    const response = new Response("{}", {
      headers: {
        "X-Table-Names": tableNames,
      },
    });

    // Act
    const result = await tableNamesMiddleware.onResponse!(
      createCallbackParams(request, response),
    );

    // Assert
    expect(useTableNamesStore.getState).toHaveBeenCalledTimes(1);
    expect(mockSetTableNames).toHaveBeenCalledWith("/api/data", tableNames);
    expect(result).toBe(response);
  });

  it("should not call setTableNames when X-Table-Names header is not present", async () => {
    // Arrange
    const requestUrl = "https://api.example.com/api/data";
    const request = new Request(requestUrl);

    const response = new Response("{}", {
      headers: {
        "Content-Type": "application/json",
      },
    });

    // Act
    const result = await tableNamesMiddleware.onResponse!(
      createCallbackParams(request, response),
    );

    // Assert
    expect(useTableNamesStore.getState).not.toHaveBeenCalled();
    expect(mockSetTableNames).not.toHaveBeenCalled();
    expect(result).toBe(response);
  });

  it("should extract path correctly from complex URLs", async () => {
    // Arrange
    const requestUrl =
      "https://api.example.com/api/v1/users/123?filter=active&sort=name";
    const request = new Request(requestUrl);
    const tableNames = "users,user_profiles";

    const response = new Response("{}", {
      headers: {
        "X-Table-Names": tableNames,
      },
    });

    // Act
    const result = await tableNamesMiddleware.onResponse!(
      createCallbackParams(request, response),
    );

    // Assert
    expect(mockSetTableNames).toHaveBeenCalledWith(
      "/api/v1/users/123",
      tableNames,
    );
    expect(result).toBe(response);
  });

  it("should handle root path correctly", async () => {
    // Arrange
    const requestUrl = "https://api.example.com/";
    const request = new Request(requestUrl);
    const tableNames = "root_table";

    const response = new Response("{}", {
      headers: {
        "X-Table-Names": tableNames,
      },
    });

    // Act
    const result = await tableNamesMiddleware.onResponse!(
      createCallbackParams(request, response),
    );

    // Assert
    expect(mockSetTableNames).toHaveBeenCalledWith("/", tableNames);
    expect(result).toBe(response);
  });

  it("should not call setTableNames when X-Table-Names header is empty", async () => {
    // Arrange
    const requestUrl = "https://api.example.com/api/data";
    const request = new Request(requestUrl);

    const response = new Response("{}", {
      headers: {
        "X-Table-Names": "",
      },
    });

    // Act
    const result = await tableNamesMiddleware.onResponse!(
      createCallbackParams(request, response),
    );

    // Assert
    expect(useTableNamesStore.getState).not.toHaveBeenCalled();
    expect(mockSetTableNames).not.toHaveBeenCalled();
    expect(result).toBe(response);
  });

  it("should handle response with no headers", async () => {
    // Arrange
    const requestUrl = "https://api.example.com/api/data";
    const request = new Request(requestUrl);

    const response = new Response("{}");

    // Act
    const result = await tableNamesMiddleware.onResponse!(
      createCallbackParams(request, response),
    );

    // Assert
    expect(useTableNamesStore.getState).not.toHaveBeenCalled();
    expect(mockSetTableNames).not.toHaveBeenCalled();
    expect(result).toBe(response);
  });

  it("should handle malformed URLs gracefully", async () => {
    // Arrange
    const requestUrl = "http://invalid-url-without-proper-format";
    const request = new Request(requestUrl);
    const tableNames = "table1";

    const response = new Response("{}", {
      headers: {
        "X-Table-Names": tableNames,
      },
    });

    // Act
    const result = await tableNamesMiddleware.onResponse!(
      createCallbackParams(request, response),
    );

    // Assert
    // Should still attempt to process, but path extraction might fail
    expect(useTableNamesStore.getState).toHaveBeenCalledTimes(1);
    expect(result).toBe(response);
  });
});
