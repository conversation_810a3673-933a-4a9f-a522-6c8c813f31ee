import type { Middleware } from "openapi-fetch";
import { useFacilityStore } from "../../stores/facility-store";

// This middleware is used to add the selected facility ID to request headers
export const facilityMiddleware: Middleware = {
  async onRequest({ request }) {
    const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;
    const API_LOCALHOST_BASE_URL = import.meta.env.VITE_API_LOCAL_BASE_URL;
    const IS_LOCALHOST = import.meta.env.VITE_LOCALHOST;
    // Only add facility header if the request URL is for our API
    if (
      request.url.startsWith(API_BASE_URL) ||
      (IS_LOCALHOST && request.url.startsWith(API_LOCALHOST_BASE_URL))
    ) {
      // Get the current facility from the store
      const { selectedFacility } = useFacilityStore.getState();

      if (selectedFacility?.id) {
        request.headers.set("ict-facility-id", selectedFacility.id);
      }
    }

    return request;
  },
};
