import type { Middleware } from "openapi-fetch";
import { useTableNamesStore } from "../../stores/table-names-store";

export const tableNamesMiddleware: Middleware = {
  async onResponse({ response, request }) {
    const tableNames = response.headers.get("X-Table-Names");
    if (tableNames) {
      // Extract just the path from the URL
      const url = new URL(request.url);
      const path = url.pathname;
      useTableNamesStore.getState().setTableNames(path, tableNames);
    }
    return response;
  },
};
