import { useEffect } from "react";
import { useAuth } from "../auth/hooks/use-auth";
import { useFacilityStore } from "../stores/facility-store";

/**
 * Hook to monitor auth context changes and clear facility store when user/organization changes
 * This ensures that facility selection is reset when switching organizations
 */
export function useAuthContextMonitor() {
  const { user, isAuthenticated } = useAuth();
  const { setUserKey } = useFacilityStore();

  useEffect(() => {
    if (isAuthenticated && user?.sub) {
      setUserKey(user.sub);
    } else {
      setUserKey("");
    }
  }, [user?.sub, isAuthenticated, setUserKey]);
}
