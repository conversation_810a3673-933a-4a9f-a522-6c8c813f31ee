import { useLocation } from "react-router";

/**
 * A safe wrapper around useLocation that provides a fallback when not in a Router context
 * This is useful for components that need to access location but might be rendered in tests without a Router
 *
 * @returns A location object similar to what useLocation() returns, or a fallback with empty values
 */
export function useSafeLocation() {
  try {
    // Try to use the real useLocation hook
    return useLocation();
  } catch (_error) {
    // If we're not in a Router context, return a fallback location object
    return {
      pathname: "",
      search: "",
      hash: "",
      state: null,
      key: "default",
    };
  }
}
