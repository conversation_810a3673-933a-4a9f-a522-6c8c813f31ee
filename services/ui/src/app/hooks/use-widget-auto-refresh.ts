import { useEffect, useRef } from "react";
import { WidgetFilters } from "../widgets/widget.types";

interface UseWidgetAutoRefreshProps {
  filters: WidgetFilters;
  refetch: () => void;
  enabled?: boolean;
}

/**
 * Hook to handle auto-refresh functionality for widgets using the refetch function
 * from useQuery instead of invalidating queries.
 *
 * This approach is more efficient as it directly refetches the specific query
 * instead of invalidating the entire query cache.
 */
export function useWidgetAutoRefresh({
  filters,
  refetch,
  enabled = true,
}: UseWidgetAutoRefreshProps) {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    // Only set up auto-refresh if it's enabled and the widget should auto-refresh
    if (
      enabled &&
      filters.autoRefresh?.enabled &&
      filters.autoRefresh.interval > 0
    ) {
      const intervalMs = filters.autoRefresh.interval * 1000;

      // Validate interval (30 seconds to 24 hours)
      if (intervalMs >= 30000 && intervalMs <= 86400000) {
        intervalRef.current = setInterval(() => {
          // Only refetch if the browser tab is active
          if (!document.hidden) {
            refetch();
          }
        }, intervalMs);
      }
    }

    // Cleanup on unmount or when dependencies change
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [
    filters.autoRefresh?.enabled,
    filters.autoRefresh?.interval,
    refetch,
    enabled,
  ]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);
}
