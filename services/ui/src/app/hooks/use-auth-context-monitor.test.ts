import { describe, expect, it, vi, beforeEach } from "vitest";
import { renderHook } from "../../test-utils";
import { useAuthContextMonitor } from "./use-auth-context-monitor";
import { useAuth } from "../auth/hooks/use-auth";
import { useFacilityStore } from "../stores/facility-store";

// Mock the dependencies
vi.mock("../auth/hooks/use-auth", () => ({
  useAuth: vi.fn(),
}));

vi.mock("../stores/facility-store", () => ({
  useFacilityStore: vi.fn(),
}));

describe("useAuthContextMonitor", () => {
  const mockSetUserKey = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Default mock implementations
    (useAuth as any).mockReturnValue({
      user: null,
      isAuthenticated: false,
    });

    (useFacilityStore as any).mockReturnValue({
      setUserKey: mockSetUserKey,
    });
  });

  it("calls setUserKey with empty string when user is not authenticated", () => {
    (useAuth as any).mockReturnValue({
      user: null,
      isAuthenticated: false,
    });

    renderHook(() => useAuthContextMonitor());

    expect(mockSetUserKey).toHaveBeenCalledWith("");
  });

  it("calls setUserKey with empty string when user is authenticated but has no subject ID", () => {
    (useAuth as any).mockReturnValue({
      user: { name: "Test User" }, // No sub property
      isAuthenticated: true,
    });

    renderHook(() => useAuthContextMonitor());

    expect(mockSetUserKey).toHaveBeenCalledWith("");
  });

  it("calls setUserKey when user is authenticated and has subject ID", () => {
    (useAuth as any).mockReturnValue({
      user: { sub: "user123", name: "Test User" },
      isAuthenticated: true,
    });

    renderHook(() => useAuthContextMonitor());

    expect(mockSetUserKey).toHaveBeenCalledWith("user123");
  });

  it("calls setUserKey based on user subject ID regardless of organization", () => {
    (useAuth as any).mockReturnValue({
      user: {
        sub: "user123",
        name: "Test User",
        organization: { id: "org456" },
      },
      isAuthenticated: true,
    });

    renderHook(() => useAuthContextMonitor());

    expect(mockSetUserKey).toHaveBeenCalledWith("user123");
  });

  it("calls setUserKey with updated subject ID when user changes", () => {
    const { rerender } = renderHook(() => useAuthContextMonitor());

    // Initial user
    (useAuth as any).mockReturnValue({
      user: { sub: "user123", name: "Test User" },
      isAuthenticated: true,
    });

    rerender();

    expect(mockSetUserKey).toHaveBeenCalledWith("user123");

    // Change user
    (useAuth as any).mockReturnValue({
      user: { sub: "user456", name: "Different User" },
      isAuthenticated: true,
    });

    rerender();

    expect(mockSetUserKey).toHaveBeenCalledWith("user456");
    expect(mockSetUserKey).toHaveBeenCalledTimes(3); // Initial empty + user123 + user456
  });

  it("handles authentication state changes", () => {
    const { rerender } = renderHook(() => useAuthContextMonitor());

    // Start unauthenticated
    (useAuth as any).mockReturnValue({
      user: null,
      isAuthenticated: false,
    });

    rerender();

    expect(mockSetUserKey).toHaveBeenCalledWith("");

    // Become authenticated
    (useAuth as any).mockReturnValue({
      user: { sub: "user123", name: "Test User" },
      isAuthenticated: true,
    });

    rerender();

    expect(mockSetUserKey).toHaveBeenCalledWith("user123");

    // Become unauthenticated again
    (useAuth as any).mockReturnValue({
      user: null,
      isAuthenticated: false,
    });

    rerender();

    expect(mockSetUserKey).toHaveBeenCalledWith("");
    expect(mockSetUserKey).toHaveBeenCalledTimes(3); // Initial empty + auth + unauth
  });

  it("calls setUserKey correctly for the same user on multiple renders", () => {
    const { rerender } = renderHook(() => useAuthContextMonitor());

    const authState = {
      user: { sub: "user123", name: "Test User" },
      isAuthenticated: true,
    };

    (useAuth as any).mockReturnValue(authState);

    rerender();
    rerender();
    rerender();

    // Should be called once initially (empty) + once when auth state is set
    expect(mockSetUserKey).toHaveBeenCalledTimes(2);
    expect(mockSetUserKey).toHaveBeenCalledWith("user123");
  });

  it("handles user subject ID changes (simulating organization switching)", () => {
    const { rerender } = renderHook(() => useAuthContextMonitor());

    // First user subject ID (which might include org info)
    (useAuth as any).mockReturnValue({
      user: { sub: "org1:user123", name: "Test User" },
      isAuthenticated: true,
    });

    rerender();

    expect(mockSetUserKey).toHaveBeenCalledWith("org1:user123");

    // Switch to different organization (reflected in new subject ID)
    (useAuth as any).mockReturnValue({
      user: { sub: "org2:user123", name: "Test User" },
      isAuthenticated: true,
    });

    rerender();

    expect(mockSetUserKey).toHaveBeenCalledWith("org2:user123");
    expect(mockSetUserKey).toHaveBeenCalledTimes(3); // Initial empty + org1:user123 + org2:user123
  });

  it("calls setUserKey with empty string for undefined user object", () => {
    (useAuth as any).mockReturnValue({
      user: undefined,
      isAuthenticated: true,
    });

    renderHook(() => useAuthContextMonitor());

    expect(mockSetUserKey).toHaveBeenCalledWith("");
  });

  it("calls setUserKey with empty string for user object with null sub property", () => {
    (useAuth as any).mockReturnValue({
      user: { sub: null, name: "Test User" },
      isAuthenticated: true,
    });

    renderHook(() => useAuthContextMonitor());

    expect(mockSetUserKey).toHaveBeenCalledWith("");
  });

  it("calls setUserKey with empty string for user object with empty string sub property", () => {
    (useAuth as any).mockReturnValue({
      user: { sub: "", name: "Test User" },
      isAuthenticated: true,
    });

    renderHook(() => useAuthContextMonitor());

    expect(mockSetUserKey).toHaveBeenCalledWith("");
  });

  it("handles complex user subject IDs", () => {
    const complexSubjects = [
      "auth0|507f1f77bcf86cd799439011",
      "google-oauth2|112233445566778899",
      "facebook|10154123456789012",
      "org123:division456:user789",
    ];

    complexSubjects.forEach((sub, index) => {
      vi.clearAllMocks();

      (useAuth as any).mockReturnValue({
        user: { sub, name: `User ${index}` },
        isAuthenticated: true,
      });

      renderHook(() => useAuthContextMonitor());

      expect(mockSetUserKey).toHaveBeenCalledWith(sub);
    });
  });
});
