import { vi, beforeEach, describe, it, expect } from "vitest";
import { render, screen, fireEvent, waitFor } from "../../../test-utils";
import { useFacilityStore } from "../../stores/facility-store";
import { TableauApiClient } from "../../api/tableau";
import { TableauApiUtil } from "../../api/tableau/tableau-api-util";
import IctConfiguredAlertsView from "./configured-alerts";

// Mock the TableauApiClient
vi.mock("../../api/tableau", () => ({
  TableauApiClient: vi.fn().mockImplementation(() => ({
    signIn: vi.fn(),
    getConfiguredAlertsForSite: vi.fn(),
    getProjectsForSite: vi.fn(),
    getConfiguredAlertDetailsForSite: vi.fn(),
  })),
}));

// Mock the facility store
vi.mock("../../stores/facility-store", () => ({
  useFacilityStore: vi.fn(),
}));

// Mock the TableauApiUtil
vi.mock("../../api/tableau/tableau-api-util", () => ({
  TableauApiUtil: {
    filterProjectsByFacility: vi.fn(),
  },
}));

// Mock the logger
vi.mock("../../utils", () => ({
  logger: {
    error: vi.fn(),
  },
}));

// Mock Carbon React components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    Modal: ({ children, open, onRequestClose, _passiveModal }: any) =>
      open ? (
        <div data-testid="modal">
          {children}
          <button data-testid="close-modal" onClick={onRequestClose}>
            Close
          </button>
        </div>
      ) : null,
    ModalBody: ({ children }: any) => (
      <div data-testid="modal-body">{children}</div>
    ),
    Link: ({ children, onClick, className }: any) => (
      <button
        data-testid="view-all-link"
        onClick={onClick}
        className={className}
      >
        {children}
      </button>
    ),
    UnorderedList: ({ children }: any) => (
      <ul data-testid="recipients-list">{children}</ul>
    ),
    ListItem: ({ children }: any) => (
      <li data-testid="recipient-item">{children}</li>
    ),
    GlobalTheme: ({ children }: any) => children,
  };
});

// Mock the Datagrid component
vi.mock("../../components/datagrid", () => ({
  Datagrid: ({
    columns,
    data,
    isLoading,
    error,
    onRefreshClick,
    showRefreshButton,
  }: any) => (
    <div data-testid="datagrid">
      <div data-testid="columns">
        {JSON.stringify(columns.map((c: any) => c.header))}
      </div>
      <div data-testid="data">{JSON.stringify(data)}</div>
      <div data-testid="loading">{isLoading ? "Loading" : "Not Loading"}</div>
      <div data-testid="error">{error || "No Error"}</div>
      {showRefreshButton && onRefreshClick && (
        <button data-testid="refresh-button" onClick={onRefreshClick}>
          Refresh
        </button>
      )}
    </div>
  ),
}));

// Mock the ViewBar component
vi.mock("../../components/view-bar/view-bar", () => ({
  ViewBar: ({ title }: any) => <div data-testid="view-bar">{title}</div>,
}));

// Mock the FullPageContainer component
vi.mock("../../components/full-page-container/full-page-container", () => ({
  FullPageContainer: ({ children }: any) => (
    <div data-testid="full-page-container">{children}</div>
  ),
}));

describe("IctConfiguredAlertsView", () => {
  const mockTableauApiClient = vi.mocked(TableauApiClient);
  const mockUseFacilityStore = vi.mocked(useFacilityStore);

  // Mock data
  const mockFacility = {
    id: "facility-1",
    name: "Test Facility",
    dataset: "test-dataset",
  };

  const mockProjects = [
    {
      id: "project-1",
      name: "Test Facility - Project 1",
      parentProjectId: undefined,
    },
    {
      id: "project-2",
      name: "Other Facility - Project 2",
      parentProjectId: undefined,
    },
  ];

  const mockAlerts = [
    {
      id: "alert-1",
      subject: "Test Alert 1",
      owner: { name: "John Doe" },
      view: {
        name: "Test View 1",
        project: { id: "project-1" },
      },
      frequency: "daily",
      public: false,
    },
    {
      id: "alert-2",
      subject: "Test Alert 2",
      owner: { name: "Jane Smith" },
      view: {
        name: "Test View 2",
        project: { id: "project-1" },
      },
      frequency: "weekly",
      public: true,
    },
  ];

  const mockAlertDetails = [
    {
      dataAlert: {
        recipients: {
          recipient: [
            { name: "<EMAIL>" },
            { name: "<EMAIL>" },
          ],
        },
        public: false,
      },
    },
    {
      dataAlert: {
        recipients: {
          recipient: [{ name: "<EMAIL>" }],
        },
        public: true,
      },
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock environment variable
    vi.stubEnv("VITE_TABLEAU_SERVER_URL", "https://test-tableau-server.com");

    // Setup facility store mock
    mockUseFacilityStore.mockReturnValue({
      selectedFacility: mockFacility,
      userKey: "test-user",
      setSelectedFacility: vi.fn(),
      setUserKey: vi.fn(),
    });

    // Setup facility store getState mock for the component's usage
    mockUseFacilityStore.getState = vi.fn().mockReturnValue({
      selectedFacility: mockFacility,
    });

    // Setup default TableauApiClient instance mocks
    const mockClientInstance = {
      signIn: vi.fn().mockResolvedValue(undefined),
      getConfiguredAlertsForSite: vi.fn().mockResolvedValue({
        dataAlerts: { dataAlert: mockAlerts },
      }),
      getProjectsForSite: vi.fn().mockResolvedValue({
        projects: { project: mockProjects },
      }),
      getConfiguredAlertDetailsForSite: vi.fn(),
    };

    // Mock individual alert details calls
    mockClientInstance.getConfiguredAlertDetailsForSite
      .mockResolvedValueOnce(mockAlertDetails[0])
      .mockResolvedValueOnce(mockAlertDetails[1]);

    mockTableauApiClient.mockImplementation(() => mockClientInstance as any);

    // Mock TableauApiUtil.filterProjectsByFacility
    vi.mocked(TableauApiUtil.filterProjectsByFacility).mockReturnValue([
      { id: "project-1" } as any,
    ]);
  });

  it("renders the component with correct title", () => {
    render(<IctConfiguredAlertsView />);

    expect(screen.getByTestId("view-bar")).toBeInTheDocument();
    expect(screen.getByText("Configured Alerts")).toBeInTheDocument();
  });

  it("renders the datagrid component", () => {
    render(<IctConfiguredAlertsView />);

    expect(screen.getByTestId("datagrid")).toBeInTheDocument();
    expect(screen.getByTestId("full-page-container")).toBeInTheDocument();
  });

  it("shows loading state initially", async () => {
    render(<IctConfiguredAlertsView />);

    // The component should show loading initially
    expect(screen.getByTestId("loading")).toHaveTextContent("Loading");
  });

  it("fetches and displays alerts data successfully", async () => {
    render(<IctConfiguredAlertsView />);

    // Wait for the loading to complete
    await waitFor(() => {
      expect(screen.getByTestId("loading")).toHaveTextContent("Not Loading");
    });

    // Verify the data is displayed
    const dataElement = screen.getByTestId("data");
    const dataText = dataElement.textContent;

    expect(dataText).toContain("Test Alert 1");
    expect(dataText).toContain("Test Alert 2");
    expect(dataText).toContain("John Doe");
    expect(dataText).toContain("Jane Smith");
  });

  it("handles error when no facility is found", async () => {
    // Mock no facility
    mockUseFacilityStore.getState = vi.fn().mockReturnValue({
      selectedFacility: null,
    });

    render(<IctConfiguredAlertsView />);

    await waitFor(() => {
      expect(screen.getByTestId("error")).toHaveTextContent(
        "No facility found for alert filtering",
      );
    });
  });

  it("handles API errors gracefully", async () => {
    // Mock API error
    const mockClientInstance = {
      signIn: vi.fn().mockRejectedValue(new Error("API Error")),
      getConfiguredAlertsForSite: vi.fn(),
      getProjectsForSite: vi.fn(),
      getConfiguredAlertDetailsForSite: vi.fn(),
    };

    mockTableauApiClient.mockImplementation(() => mockClientInstance as any);

    render(<IctConfiguredAlertsView />);

    await waitFor(() => {
      expect(screen.getByTestId("error")).toHaveTextContent("API Error");
    });
  });

  it("filters alerts by facility projects", async () => {
    render(<IctConfiguredAlertsView />);

    await waitFor(() => {
      expect(screen.getByTestId("loading")).toHaveTextContent("Not Loading");
    });

    // Verify TableauApiUtil.filterProjectsByFacility was called
    expect(
      vi.mocked(TableauApiUtil.filterProjectsByFacility),
    ).toHaveBeenCalledWith(mockProjects, "Test Facility");
  });

  it("renders recipients column correctly", async () => {
    render(<IctConfiguredAlertsView />);

    await waitFor(() => {
      expect(screen.getByTestId("loading")).toHaveTextContent("Not Loading");
    });

    // Verify the recipients data is in the table
    const dataElement = screen.getByTestId("data");
    const dataText = dataElement.textContent;

    expect(dataText).toContain("<EMAIL>");
    expect(dataText).toContain("<EMAIL>");
    expect(dataText).toContain("<EMAIL>");
  });

  it("modal functionality works with recipient data", async () => {
    render(<IctConfiguredAlertsView />);

    await waitFor(() => {
      expect(screen.getByTestId("loading")).toHaveTextContent("Not Loading");
    });

    // Modal should not be visible initially
    expect(screen.queryByTestId("modal")).not.toBeInTheDocument();

    // The component has modal state management for showing recipient details
    // This test verifies the basic modal structure exists in the component
    expect(screen.getByTestId("datagrid")).toBeInTheDocument();
  });

  it("handles refresh functionality", async () => {
    render(<IctConfiguredAlertsView />);

    await waitFor(() => {
      expect(screen.getByTestId("loading")).toHaveTextContent("Not Loading");
    });

    // Click refresh button
    const refreshButton = screen.getByTestId("refresh-button");
    fireEvent.click(refreshButton);

    // Should show loading again
    expect(screen.getByTestId("loading")).toHaveTextContent("Loading");

    await waitFor(() => {
      expect(screen.getByTestId("loading")).toHaveTextContent("Not Loading");
    });
  });

  it("handles alert details fetch failure gracefully", async () => {
    // Mock successful main API calls but failed details call
    const mockClientInstance = {
      signIn: vi.fn().mockResolvedValue(undefined),
      getConfiguredAlertsForSite: vi.fn().mockResolvedValue({
        dataAlerts: { dataAlert: mockAlerts },
      }),
      getProjectsForSite: vi.fn().mockResolvedValue({
        projects: { project: mockProjects },
      }),
      getConfiguredAlertDetailsForSite: vi
        .fn()
        .mockRejectedValue(new Error("Details fetch failed")),
    };

    mockTableauApiClient.mockImplementation(() => mockClientInstance as any);

    render(<IctConfiguredAlertsView />);

    await waitFor(() => {
      expect(screen.getByTestId("loading")).toHaveTextContent("Not Loading");
    });

    // Should still display data with default values for failed details
    const dataElement = screen.getByTestId("data");
    const dataText = dataElement.textContent;
    expect(dataText).toContain("Test Alert 1");
    expect(dataText).toContain("Test Alert 2");
  });

  it("capitalizes frequency field correctly", async () => {
    render(<IctConfiguredAlertsView />);

    await waitFor(() => {
      expect(screen.getByTestId("loading")).toHaveTextContent("Not Loading");
    });

    const dataElement = screen.getByTestId("data");
    const dataText = dataElement.textContent;

    // Should contain capitalized frequencies
    expect(dataText).toContain("Daily");
    expect(dataText).toContain("Weekly");
  });

  it("displays correct visibility values", async () => {
    render(<IctConfiguredAlertsView />);

    await waitFor(() => {
      expect(screen.getByTestId("loading")).toHaveTextContent("Not Loading");
    });

    const dataElement = screen.getByTestId("data");
    const dataText = dataElement.textContent;

    // Should contain visibility values
    expect(dataText).toContain("Private");
    expect(dataText).toContain("Public");
  });

  it("has correct column headers", () => {
    render(<IctConfiguredAlertsView />);

    const columnsElement = screen.getByTestId("columns");
    const columnsText = columnsElement.textContent;

    expect(columnsText).toContain("Subject");
    expect(columnsText).toContain("Owner");
    expect(columnsText).toContain("View");
    expect(columnsText).toContain("Recipients");
    expect(columnsText).toContain("Visibility");
    expect(columnsText).toContain("Email sent (at most)");
    expect(columnsText).toContain("Status");
  });
});
