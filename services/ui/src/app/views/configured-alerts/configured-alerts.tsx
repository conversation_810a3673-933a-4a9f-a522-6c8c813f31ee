import { createColumnHelper } from "@tanstack/react-table";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { TableauApiClient } from "../../api/tableau";
import { Datagrid } from "../../components/datagrid";
import { FullPageContainer } from "../../components/full-page-container/full-page-container";
import { ViewBar } from "../../components/view-bar/view-bar";
import type {
  DataAlert,
  CompleteDataAlert,
} from "../../api/tableau/endpoints/get-alerts-for-site-def";
import { Link, ListItem, Modal, ModalBody, UnorderedList } from "@carbon/react";
import "./configured-alerts.css";
import { useFacilityStore } from "../../stores/facility-store";
import { logger } from "../../utils";
import { TableauApiUtil } from "../../api/tableau/tableau-api-util";

interface TableauAlertRow {
  id: string;
  subject: string;
  ownerName: string;
  viewName: string;
  recipients: string[];
  visibility: string;
  frequency: string;
  suspended: string;
}

export default function IctConfiguredAlertsView() {
  const { t } = useTranslation();

  const [data, setData] = useState<TableauAlertRow[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [modalData, setModalData] = useState<TableauAlertRow | null>(null);

  // Define columns
  const columnHelper = createColumnHelper<TableauAlertRow>();

  const columns = useMemo(
    () => [
      columnHelper.accessor("subject", {
        header: t("configuredAlerts.subject", "Subject"),
        size: 250,
      }),
      columnHelper.accessor("ownerName", {
        header: t("configuredAlerts.owner", "Owner"),
        size: 225,
      }),
      columnHelper.accessor("viewName", {
        header: t("configuredAlerts.view", "View"),
        size: 175,
      }),
      columnHelper.accessor("recipients", {
        header: t("configuredAlerts.recipients", "Recipients"),
        size: 225,
        // Open multiple recipients in a modal for easier viewing
        cell: (info) => {
          if (info.getValue().length === 1) {
            return info.getValue()[0];
          }
          return (
            <Link
              className="viewAllLink"
              onClick={(e) => {
                e.stopPropagation();
                setModalData(info.row.original);
              }}
            >
              {t("configuredAlerts.viewAll", "View all")}
            </Link>
          );
        },
      }),
      columnHelper.accessor("visibility", {
        header: t("configuredAlerts.visibility", "Visibility"),
        size: 50,
      }),
      columnHelper.accessor("frequency", {
        header: t("configuredAlerts.frequency", "Email sent (at most)"),
        size: 100,
      }),
      columnHelper.accessor("suspended", {
        header: t("configuredAlerts.suspended", "Status"),
        size: 100,
      }),
    ],
    [columnHelper, t],
  );

  const fetchFacilityAlerts = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const client = new TableauApiClient(
        import.meta.env.VITE_TABLEAU_SERVER_URL,
      );
      await client.signIn();

      // Get all alerts
      const alertsResponse = await client.getConfiguredAlertsForSite();
      const allAlerts = alertsResponse.dataAlerts.dataAlert || [];

      // Get all projects for the facility so we can filter alerts by top level project (facility)
      const allProjects = (await client.getProjectsForSite()).projects.project;
      const selectedFacility =
        useFacilityStore.getState().selectedFacility?.name;

      if (!selectedFacility) {
        logger.error("No facility found for alert filtering");
        setError("No facility found for alert filtering");
        return;
      }

      // Only keep project ids for the current facility
      const faciltyProjects = TableauApiUtil.filterProjectsByFacility(
        allProjects,
        selectedFacility,
      ).map((project) => project.id);

      // Only keep alerts that relate to the facility's projects
      const facilityAlerts = allAlerts.filter((alert: DataAlert) => {
        return faciltyProjects.includes(alert.view.project.id);
      });

      // Get detailed information for each alert
      const alertDetails = await Promise.all(
        facilityAlerts.map(async (alert: DataAlert) => {
          try {
            const detailsResponse =
              await client.getConfiguredAlertDetailsForSite(alert.id);
            return {
              ...alert,
              recipients: detailsResponse.dataAlert.recipients,
              visibility: detailsResponse.dataAlert.public
                ? "Public"
                : "Private",
            } as CompleteDataAlert;
          } catch (detailError) {
            logger.error(
              `Failed to fetch details for alert ${alert.id}:`,
              detailError,
            );
            // Return alert with default values if details fail
            return {
              ...alert,
              recipients: { recipient: [] },
              visibility: alert.public ? "Public" : "Private",
            } as CompleteDataAlert;
          }
        }),
      );

      // Transform the data for the table
      const transformedData: TableauAlertRow[] = alertDetails.map(
        (alert: CompleteDataAlert) => ({
          id: alert.id,
          subject: alert.subject,
          ownerName: alert.owner.name,
          viewName: alert.view.name,
          recipients: alert.recipients.recipient.map((r) => r.name),
          visibility: alert.visibility,
          suspended: alert.suspended ? "Suspended" : "Active",
          // capitalize the frequency field since Tableau does it inconsistently
          frequency:
            alert.frequency[0].toUpperCase() + alert.frequency.slice(1),
        }),
      );

      setData(transformedData);
    } catch (fetchError) {
      logger.error("Failed to fetch alerts:", fetchError);
      setError(
        fetchError instanceof Error
          ? fetchError.message
          : "An unexpected error occurred.",
      );
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchFacilityAlerts();
  }, [fetchFacilityAlerts]);

  const handleRefresh = useCallback(() => {
    fetchFacilityAlerts();
  }, [fetchFacilityAlerts]);

  return (
    <>
      {modalData != null && (
        <Modal
          open={modalData != null}
          onRequestClose={() => setModalData(null)}
          passiveModal={true}
        >
          <ModalBody>
            <div className="recipientsModal">
              <h3>{`${modalData.subject} Recipients`}</h3>
              <UnorderedList>
                {modalData.recipients.map((name) => (
                  <ListItem key={name}>{name}</ListItem>
                ))}
              </UnorderedList>
            </div>
          </ModalBody>
        </Modal>
      )}
      <div>
        <ViewBar title={t("configuredAlerts.title", "Configured Alerts")} />
        <FullPageContainer>
          <Datagrid
            columns={columns}
            data={data}
            mode="client"
            isLoading={isLoading}
            error={error || undefined}
            enableSelection={false}
            initialPagination={{ pageIndex: 0, pageSize: 25 }}
            initialDensity="default"
            showRefreshButton={true}
            onRefreshClick={handleRefresh}
          />
        </FullPageContainer>
      </div>
    </>
  );
}
