import { Route, Routes } from "react-router";
import type { BaseViewProps } from "../view-registry.types";
import { UserList } from "./components/user-list/user-list";
import { UserView } from "./components/user-view/user-view";

export default function UserManagement(_props: BaseViewProps) {
  return (
    <Routes>
      <Route index element={<UserList />} />
      <Route path=":userId" element={<UserView />} />
    </Routes>
  );
}
