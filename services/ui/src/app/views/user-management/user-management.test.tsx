import { vi } from "vitest";
import { render, screen } from "../../../test-utils";
import UserManagement from "./user-management";

// Mock React Router
vi.mock("react-router", () => ({
  Route: ({ path, element }: { path?: string; element: React.ReactNode }) => (
    <div data-testid={`route-${path || "index"}`}>{element}</div>
  ),
  Routes: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="routes">{children}</div>
  ),
}));

// Mock the UserList component
vi.mock("./components/user-list/user-list", () => ({
  UserList: () => <div data-testid="user-list">User List Component</div>,
}));

// Mock the UserView component
vi.mock("./components/user-view/user-view", () => ({
  UserView: () => <div data-testid="user-view">User View Component</div>,
}));

describe("UserManagement", () => {
  const mockProps = {
    id: "user-management",
    options: { title: "User Management" },
  };

  it("should render Routes component", () => {
    render(<UserManagement {...mockProps} />);

    expect(screen.getByTestId("routes")).toBeInTheDocument();
  });

  it("should render index route with UserList", () => {
    render(<UserManagement {...mockProps} />);

    const indexRoute = screen.getByTestId("route-index");
    expect(indexRoute).toBeInTheDocument();
    expect(indexRoute).toHaveTextContent("User List Component");
  });

  it("should render user detail route with UserView", () => {
    render(<UserManagement {...mockProps} />);

    const detailRoute = screen.getByTestId("route-:userId");
    expect(detailRoute).toBeInTheDocument();
    expect(detailRoute).toHaveTextContent("User View Component");
  });
});
