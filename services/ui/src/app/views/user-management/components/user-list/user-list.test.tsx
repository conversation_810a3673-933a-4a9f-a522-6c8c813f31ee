import { vi } from "vitest";
import { render, screen } from "../../../../../test-utils";
import { UserList } from "./user-list";

// Mock react-i18next
vi.mock("react-i18next", async (importOriginal) => {
  const actual = await importOriginal<typeof import("react-i18next")>();
  return {
    ...actual,
    useTranslation: () => ({
      t: (_key: string, defaultValue: string) => defaultValue,
    }),
  };
});

// Mock API
const mockUseQuery = vi.fn();
vi.mock("../../../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: () => mockUseQuery(),
    },
  },
}));

// Mock hooks
vi.mock("../../../../hooks/use-api-error-state", () => ({
  useApiErrorState: vi.fn(() => false),
}));

// Mock components
vi.mock("../../../../components/view-bar/view-bar", () => ({
  ViewBar: ({ title }: any) => <div data-testid="view-bar">{title}</div>,
}));

vi.mock(
  "../../../../components/full-page-container/full-page-container",
  () => ({
    FullPageContainer: ({ children }: any) => (
      <div data-testid="full-page-container">{children}</div>
    ),
  }),
);

vi.mock("../user-management-table/user-management-table", () => ({
  UserManagementTable: (props: any) => (
    <div data-testid="user-management-table">
      {props.isLoading && <div>Loading...</div>}
      {props.data?.length > 0 && <div>Users loaded</div>}
    </div>
  ),
}));

describe("UserList", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockUseQuery.mockReturnValue({
      data: { data: [], metadata: { totalResults: 0 } },
      error: null,
      isLoading: false,
      isFetching: false,
      refetch: vi.fn(),
    });
  });

  it("should render title and table", () => {
    render(<UserList />);

    expect(screen.getByTestId("view-bar")).toHaveTextContent("User Management");
    expect(screen.getByTestId("user-management-table")).toBeInTheDocument();
  });

  it("should show loading state", () => {
    mockUseQuery.mockReturnValue({
      data: null,
      error: null,
      isLoading: true,
      isFetching: false,
      refetch: vi.fn(),
    });

    render(<UserList />);

    expect(screen.getByText("Loading...")).toBeInTheDocument();
  });

  it("should render users when data is available", () => {
    const mockUsers = [{ id: "1", email: "<EMAIL>" }];
    mockUseQuery.mockReturnValue({
      data: { data: mockUsers, metadata: { totalResults: 1 } },
      error: null,
      isLoading: false,
      isFetching: false,
      refetch: vi.fn(),
    });

    render(<UserList />);

    expect(screen.getByText("Users loaded")).toBeInTheDocument();
  });
});
