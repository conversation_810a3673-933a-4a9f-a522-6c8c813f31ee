import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { ictApi } from "../../../../api/ict-api";
import { transformFilters } from "../../../../api/util/filter-transform-util";
import { FullPageContainer } from "../../../../components/full-page-container/full-page-container";
import { ViewBar } from "../../../../components/view-bar/view-bar";
import { useApiErrorState } from "../../../../hooks/use-api-error-state";
import { UserManagementTable } from "../user-management-table/user-management-table";
import type { SortField, UserInfo } from "../../types";

export function UserList() {
  const { t } = useTranslation();

  // State for table pagination, sorting, and filtering
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 50,
  });

  const [sorting, setSorting] = useState<SortingState>([
    // Default sort by email
    { id: "email", desc: false },
  ]);

  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>("");

  // Transform sorting state to API format
  const sortFields: SortField[] = useMemo(
    () =>
      sorting.map((sort) => ({
        columnName: sort.id,
        isDescending: sort.desc,
      })),
    [sorting],
  );

  // Transform filters to API format
  const apiFilters = useMemo(
    () => transformFilters(columnFilters),
    [columnFilters],
  );

  // Fetch users data
  const { data, error, isLoading, isFetching, refetch } =
    ictApi.client.useQuery(
      "post",
      "/admin/auth/users/list",
      {
        body: {
          limit: pagination.pageSize,
          page: pagination.pageIndex,
          filters: apiFilters,
          sortFields,
          ...(globalFilter !== "" && { searchString: globalFilter }),
        },
      },
      {
        enabled: true,
        keepPreviousData: true,
        placeholderData: (prev) => prev,
        retry: false,
        refetchOnWindowFocus: false,
      },
    );

  const isNoDataAvailable = useApiErrorState(error);

  // Extract users data and total count from response
  const usersData = useMemo(() => (data?.data ?? []) as UserInfo[], [data]);

  const totalResults = data?.metadata?.totalResults ?? 0;

  const handleRefresh = () => {
    refetch();
  };

  if (isNoDataAvailable) {
    return (
      <FullPageContainer>
        <div>
          {t(
            "userManagement.noDataAvailable",
            "No user data is currently available.",
          )}
        </div>
      </FullPageContainer>
    );
  }

  const errorMessage = error
    ? t("widgetContainer.genericErrorOccurred", "An error occurred.")
    : undefined;

  return (
    <>
      <ViewBar title={t("userManagement.title", "User Management")} />
      <FullPageContainer>
        <UserManagementTable
          data={usersData}
          pagination={pagination}
          setPagination={setPagination}
          sorting={sorting}
          setSorting={setSorting}
          columnFilters={columnFilters}
          setColumnFilters={setColumnFilters}
          isLoading={isLoading}
          isFetching={isFetching}
          error={error ? errorMessage : undefined}
          rowCount={totalResults}
          onRefresh={handleRefresh}
          setGlobalFilter={setGlobalFilter}
        />
      </FullPageContainer>
    </>
  );
}
