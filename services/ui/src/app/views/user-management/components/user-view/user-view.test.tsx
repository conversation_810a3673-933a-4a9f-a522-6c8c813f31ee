import { vi } from "vitest";
import { render, screen, userEvent } from "../../../../../test-utils";
import { UserView } from "./user-view";

// Mock react-router
vi.mock("react-router", () => ({
  useParams: () => ({ userId: "test-user-id" }),
  Link: ({ to, children }: { to: string; children: React.ReactNode }) => (
    <a href={to} data-testid="back-link">
      {children}
    </a>
  ),
}));

// Mock ict-api
const mockUserData = {
  id: "test-user-id",
  email: "<EMAIL>",
  name: "Test User",
  lastLogin: "2023-05-01T12:00:00Z",
  roles: [
    {
      assignable: true,
      name: "admin",
      description: "Administrator role",
    },
    {
      assignable: true,
      name: "user",
      description: "User role",
    },
  ],
};

const mockUseQuery = vi.fn();
const mockFetchQuery = vi.fn();
const mockInvalidateQueries = vi.fn();

vi.mock("../../../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: () => mockUseQuery(),
      queryOptions: vi.fn(() => ({ queryKey: ["test-key"] })),
    },
    queryClient: {
      fetchQuery: () => mockFetchQuery(),
      invalidateQueries: () => mockInvalidateQueries(),
    },
  },
}));

vi.mock("../../../../auth/hooks/use-roles", () => ({
  useRoles: vi.fn().mockReturnValue({
    hasConfiguratorAccess: true,
    isFacilityAdmin: true,
  }),
}));

// Mock Carbon React
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
    DataTable: ({ rows, headers, children }: any) => {
      const tableProps = {
        rows: rows.map((row: any, _index: number) => ({
          ...row,
          id: row.id,
          cells: [{ value: row.name }, { value: row.description }],
        })),
        headers,
        getTableProps: () => ({}),
        getHeaderProps: () => ({}),
        getRowProps: ({ row }: any) => ({ row }),
      };

      return (
        <div data-testid="data-table">
          {typeof children === "function" ? children(tableProps) : children}
        </div>
      );
    },
    Table: ({ children }: any) => <table data-testid="table">{children}</table>,
    TableHead: ({ children }: any) => <thead>{children}</thead>,
    TableRow: ({ children }: any) => <tr>{children}</tr>,
    TableHeader: ({ children }: any) => <th>{children}</th>,
    TableBody: ({ children }: any) => <tbody>{children}</tbody>,
    TableCell: ({ children }: any) => <td>{children}</td>,
    Loading: () => <div data-testid="loading">Loading...</div>,
    Button: ({ onClick, children, disabled, ...props }: any) => (
      <button
        onClick={onClick}
        disabled={disabled}
        data-testid={props["data-testid"] || "button"}
      >
        {children}
      </button>
    ),
  };
});

// Mock ViewBar component
vi.mock("../../../../components/view-bar/view-bar", () => ({
  ViewBar: ({ title, children }: any) => (
    <div data-testid="view-bar">
      <div data-testid="view-bar-title">{title}</div>
      <div data-testid="view-bar-actions">{children}</div>
    </div>
  ),
}));

// Mock FullPageContainer component
vi.mock(
  "../../../../components/full-page-container/full-page-container",
  () => ({
    FullPageContainer: ({ children }: any) => (
      <div data-testid="full-page-container">{children}</div>
    ),
  }),
);

// Mock AssignRolesModal component
vi.mock("../assign-roles-modal/assign-roles-modal", () => ({
  AssignRolesModal: ({ open, onClose, onAssign }: any) =>
    open ? (
      <div data-testid="assign-roles-modal">
        <button data-testid="modal-close" onClick={onClose}>
          Close
        </button>
        <button
          data-testid="modal-assign"
          onClick={() => onAssign(["new-role"])}
        >
          Assign
        </button>
      </div>
    ) : null,
}));

describe("UserView", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockUseQuery.mockReturnValue({
      data: mockUserData,
      isLoading: false,
      error: null,
    });
  });

  it("should render loading state", () => {
    mockUseQuery.mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
    });

    render(<UserView />);

    expect(screen.getByTestId("loading")).toBeInTheDocument();
  });

  it("should render error state when user not found", () => {
    mockUseQuery.mockReturnValue({
      data: null,
      isLoading: false,
      error: new Error("User not found"),
    });

    render(<UserView />);

    expect(screen.getByText(/User not found/)).toBeInTheDocument();
  });

  it("should render user information", () => {
    render(<UserView />);

    expect(
      screen.getByRole("heading", { name: "Test User" }),
    ).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
    expect(screen.getByText("test-user-id")).toBeInTheDocument();
  });

  it("should render user roles table", () => {
    render(<UserView />);

    expect(screen.getByTestId("data-table")).toBeInTheDocument();
    expect(screen.getByText("admin")).toBeInTheDocument();
    expect(screen.getByText("user")).toBeInTheDocument();
  });

  it("should render breadcrumb link back to user management", () => {
    render(<UserView />);

    const backLink = screen.getByTestId("back-link");
    expect(backLink).toHaveAttribute("href", "/ict-user-management");
  });

  it("should open assign roles modal when assign button is clicked", async () => {
    render(<UserView />);

    const assignButton = screen.getByText("Assign Roles");
    await userEvent.click(assignButton);

    expect(screen.getByTestId("assign-roles-modal")).toBeInTheDocument();
  });

  it("should close assign roles modal when close button is clicked", async () => {
    render(<UserView />);

    // Open modal
    const assignButton = screen.getByText("Assign Roles");
    await userEvent.click(assignButton);

    // Close modal
    const closeButton = screen.getByTestId("modal-close");
    await userEvent.click(closeButton);

    expect(screen.queryByTestId("assign-roles-modal")).not.toBeInTheDocument();
  });

  it("should call API when roles are assigned", async () => {
    render(<UserView />);

    // Open modal
    const assignButton = screen.getByText("Assign Roles");
    await userEvent.click(assignButton);

    // Assign roles
    const assignRolesButton = screen.getByTestId("modal-assign");
    await userEvent.click(assignRolesButton);

    expect(mockFetchQuery).toHaveBeenCalled();
    expect(mockInvalidateQueries).toHaveBeenCalled();
  });

  it("should handle delete role action", async () => {
    render(<UserView />);

    // Find and click delete button
    const deleteButtons = screen.getAllByTestId("delete-role-button");
    await userEvent.click(deleteButtons[0]);

    expect(mockFetchQuery).toHaveBeenCalled();
    expect(mockInvalidateQueries).toHaveBeenCalled();
  });
});
