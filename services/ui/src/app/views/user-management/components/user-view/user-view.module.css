.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.titleContainer {
  display: flex;
  align-items: center;
}

.titleLink {
  text-decoration: none;
  color: var(--cds-text-primary);
}

.titleLink:hover {
  text-decoration: underline;
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--cds-layer-01);
}

.userAvatar {
  flex-shrink: 0;
}

.avatarCircle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--cds-button-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
  color: white;
}

.userDetails {
  flex: 1;
}

.userName {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--cds-text-primary);
}

.userMeta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.userMetaItem {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.label {
  font-weight: 600;
  color: var(--cds-text-secondary);
  min-width: 80px;
}

.value {
  color: var(--cds-text-primary);
  font-family: "IBM Plex Mono", monospace;
  font-size: 0.875rem;
}

.rolesSection {
  margin-top: 2rem;
}

.sectionHeader {
  margin-bottom: 1.5rem;
}

.sectionTitle {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--cds-text-primary);
}

.sectionDescription {
  color: var(--cds-text-secondary);
  font-size: 0.875rem;
  line-height: 1.4;
  margin-bottom: 1rem;
}

.rolesActions {
  margin-bottom: 1.5rem;
}

.rolesTable {
  background: var(--cds-layer-01);
  overflow: hidden;
}

.noRoles {
  padding: 2rem;
  text-align: center;
  color: var(--cds-text-secondary);
  font-style: italic;
}
