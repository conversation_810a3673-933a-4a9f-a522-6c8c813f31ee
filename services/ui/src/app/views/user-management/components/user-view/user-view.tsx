import { <PERSON>, useParams } from "react-router";
import { useTranslation } from "react-i18next";
import { ictApi } from "../../../../api/ict-api";
import { ViewBar } from "../../../../components/view-bar/view-bar";
import { FullPageContainer } from "../../../../components/full-page-container/full-page-container";
import {
  Button,
  Stack,
  DataTable,
  Table,
  TableHead,
  TableHeader,
  TableRow,
  TableBody,
  TableCell,
  Loading,
} from "@carbon/react";
import { Delete, Add } from "@carbon/icons-react";
import { useState } from "react";
import styles from "./user-view.module.css";
import { AssignRolesModal } from "../assign-roles-modal/assign-roles-modal";
import { UserInfo } from "../../types";
import { useRoles } from "../../../../auth/hooks/use-roles";

export function UserView() {
  const { userId } = useParams<{ userId: string }>();
  const { t } = useTranslation();
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);
  const [isAssigning, setIsAssigning] = useState(false);
  const { hasConfiguratorAccess, isFacilityAdmin } = useRoles();

  // Fetch user data from API
  const {
    data: userData,
    isLoading,
    error,
  } = ictApi.client.useQuery(
    "get",
    "/admin/auth/users/{id}",
    { params: { path: { id: String(userId) } } },
    { enabled: !!userId },
  );

  const user: UserInfo | undefined = userData;

  /**
   * Renders the breadcrumb-style title with link back to User Management
   */
  const renderTitleContent = () => (
    <div className={styles.titleContainer}>
      <Link to="/ict-user-management" className={styles.titleLink}>
        <span
          className="cds--type-productive-heading-03"
          style={{ fontWeight: "bold" }}
        >
          {t("userManagement.title", "User Management")}
        </span>
      </Link>
      <span
        className="cds--type-productive-heading-03"
        style={{ margin: "0 8px" }}
      >
        /
      </span>
      <span className="cds--type-productive-heading-03">
        {user?.name || user?.email || "User"}
      </span>
    </div>
  );

  const handleDeleteRole = async (roleId: string) => {
    if (!userId) return;

    setIsDeleting(roleId);
    try {
      await ictApi.queryClient.fetchQuery(
        ictApi.client.queryOptions(
          "delete",
          "/admin/auth/users/{userId}/roles/{roleName}",
          {
            params: { path: { userId, roleName: roleId } },
          },
        ),
      );

      // Invalidate user data to refresh the roles list
      ictApi.queryClient.invalidateQueries({
        queryKey: ictApi.client.queryOptions("get", "/admin/auth/users/{id}", {
          params: { path: { id: userId } },
        }).queryKey,
      });
    } catch (error) {
      console.error("Error deleting role:", error);
    } finally {
      setIsDeleting(null);
    }
  };

  const handleAssignRoles = () => {
    setIsAssignModalOpen(true);
  };

  const handleAssignRolesSubmit = async (roleNames: string[]) => {
    if (!userId) return;

    setIsAssigning(true);
    try {
      await ictApi.queryClient.fetchQuery(
        ictApi.client.queryOptions("post", "/admin/auth/users/{userId}/roles", {
          params: { path: { userId } },
          body: { roleNames },
        }),
      );

      // Invalidate user data to refresh the roles list
      ictApi.queryClient.invalidateQueries({
        queryKey: ictApi.client.queryOptions("get", "/admin/auth/users/{id}", {
          params: { path: { id: userId } },
        }).queryKey,
      });

      setIsAssignModalOpen(false);
    } catch (error) {
      console.error("Error assigning roles:", error);
    } finally {
      setIsAssigning(false);
    }
  };

  if (isLoading) {
    return (
      <FullPageContainer>
        <Loading />
      </FullPageContainer>
    );
  }

  if (error || !user) {
    return (
      <FullPageContainer>
        <div>
          {t(
            "userManagement.userNotFound",
            "User not found or could not be loaded.",
          )}
        </div>
      </FullPageContainer>
    );
  }

  // Prepare roles data for the table
  const roles = user.roles || [];
  const roleRows = roles.map((role, index: number) => ({
    id: role.name || String(index), // Use role name as ID for API calls
    name: role.name,
    isAssignable: role.assignable,
    description:
      role.description ||
      t("userManagement.noDescription", "No description available"),
  }));

  const roleHeaders = [
    { key: "name", header: t("userManagement.roleName", "Name") },
    {
      key: "description",
      header: t("userManagement.roleDescription", "Description"),
    },
    { key: "actions", header: "" },
  ];

  const canChangeRoles = hasConfiguratorAccess || isFacilityAdmin;

  return (
    <>
      <ViewBar title={renderTitleContent()}>
        {canChangeRoles && (
          <Button kind="primary" renderIcon={Add} onClick={handleAssignRoles}>
            {t("userManagement.assignRoles", "Assign Roles")}
          </Button>
        )}
      </ViewBar>
      <FullPageContainer>
        <div className={styles.container}>
          <div className={styles.userInfo}>
            <div className={styles.userAvatar}>
              <div className={styles.avatarCircle}>
                {(user.name || user.email || "U").charAt(0).toUpperCase()}
              </div>
            </div>

            <div className={styles.userDetails}>
              <h2 className={styles.userName}>{user.name || user.email}</h2>
              <div className={styles.userMeta}>
                <div className={styles.userMetaItem}>
                  <span className={styles.label}>
                    {t("userManagement.userId", "User ID")}:
                  </span>
                  <span className={styles.value}>{user.id || userId}</span>
                </div>
                {user.email && (
                  <div className={styles.userMetaItem}>
                    <span className={styles.label}>
                      {t("userManagement.email", "Email")}:
                    </span>
                    <span className={styles.value}>{user.email}</span>
                  </div>
                )}
                {user.lastLogin && (
                  <div className={styles.userMetaItem}>
                    <span className={styles.label}>
                      {t("userManagement.lastLogin", "Last Login")}:
                    </span>
                    <span className={styles.value}>
                      {new Date(user.lastLogin).toLocaleDateString()}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className={styles.rolesSection}>
            <Stack gap={4}>
              <div className={styles.sectionHeader}>
                <h3 className={styles.sectionTitle}>
                  {t("userManagement.assignedRoles", "Assigned Roles")}
                </h3>
                <p className={styles.sectionDescription}>
                  {t(
                    "userManagement.assignedRolesDescription",
                    "Assign the roles that {{userName}} has while accessing your application in the {{organization}} organization.",
                    {
                      userName: user.name || user.email,
                      organization: "Ace Hardware",
                    },
                  )}
                </p>
              </div>

              <div className={styles.rolesTable}>
                <DataTable rows={roleRows} headers={roleHeaders} size="md">
                  {({
                    rows,
                    headers,
                    getTableProps,
                    getHeaderProps,
                    getRowProps,
                  }) => (
                    <Table {...getTableProps()}>
                      <TableHead>
                        <TableRow>
                          {headers.map((header, index) => {
                            const { key: _key, ...headerProps } =
                              getHeaderProps({
                                header,
                              });
                            return (
                              <TableHeader key={index} {...headerProps}>
                                {header.header}
                              </TableHeader>
                            );
                          })}
                        </TableRow>
                      </TableHead>
                      <TableBody className={styles.roleTableBody}>
                        {rows.map((row, index) => {
                          const { key: _key, ...rowProps } = getRowProps({
                            row,
                          });
                          return (
                            <TableRow
                              key={index}
                              {...rowProps}
                              className={styles.roleRow}
                            >
                              <TableCell>{row.cells[0].value}</TableCell>
                              <TableCell>{row.cells[1].value}</TableCell>
                              <TableCell>
                                {canChangeRoles &&
                                  roleRows.find((r) => r.id === row.id)
                                    ?.isAssignable && (
                                    <Button
                                      kind="ghost"
                                      size="sm"
                                      renderIcon={Delete}
                                      hasIconOnly
                                      data-testid="delete-role-button"
                                      tooltipAlignment="end"
                                      iconDescription={t(
                                        "userManagement.deleteRole",
                                        "Delete role",
                                      )}
                                      onClick={() => handleDeleteRole(row.id)}
                                      disabled={isDeleting === row.id}
                                    />
                                  )}
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  )}
                </DataTable>

                {roles.length === 0 && (
                  <div className={styles.noRoles}>
                    <p>
                      {t(
                        "userManagement.noRoles",
                        "No roles assigned to this user.",
                      )}
                    </p>
                  </div>
                )}
              </div>
            </Stack>
          </div>
        </div>
      </FullPageContainer>

      <AssignRolesModal
        open={isAssignModalOpen}
        onClose={() => setIsAssignModalOpen(false)}
        onAssign={handleAssignRolesSubmit}
        assignedRoleNames={roles.map((role) => role.name || "")}
        isAssigning={isAssigning}
      />
    </>
  );
}
