import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { vi } from "vitest";
import { fireEvent, render, screen } from "../../../../../test-utils";
import { UserManagementTable } from "./user-management-table";

// Mock react-router
vi.mock("react-router", () => ({
  Link: ({ to, children }: { to: string; children: React.ReactNode }) => (
    <a href={to} data-testid="user-link">
      {children}
    </a>
  ),
}));

// Mock Carbon React
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

// Mock the Datagrid component
vi.mock("../../../../components/datagrid", () => ({
  Datagrid: ({
    columns,
    data,
    onPageChange,
    onSort,
    onFilter,
    onRefreshClick,
  }: {
    columns: any;
    data: any;
    onPageChange: any;
    onSort: any;
    onFilter: any;
    onRefreshClick: any;
  }) => (
    <div data-testid="datagrid">
      <div data-testid="columns">
        {JSON.stringify(columns.map((c: any) => c.header))}
      </div>
      <div data-testid="data">{JSON.stringify(data)}</div>
      <div data-testid="email-cell">
        {data.length > 0 &&
          columns
            .find((c: any) => c.accessorKey === "email")
            ?.cell?.({
              getValue: () => data[0]?.email,
              row: { original: data[0] },
            })}
      </div>
      <button
        type="button"
        data-testid="page-change"
        onClick={() => onPageChange({ pageIndex: 1, pageSize: 10 })}
      >
        Change Page
      </button>
      <button
        type="button"
        data-testid="sort"
        onClick={() => onSort([{ id: "email", desc: true }])}
      >
        Sort
      </button>
      <button
        type="button"
        data-testid="filter"
        onClick={() =>
          onFilter({
            filters: { email: "<EMAIL>" },
            globalFilter: "test",
          })
        }
      >
        Filter
      </button>
      {onRefreshClick && (
        <button type="button" data-testid="refresh" onClick={onRefreshClick}>
          Refresh
        </button>
      )}
    </div>
  ),
}));

const mockSetPagination = vi.fn();
const mockSetSorting = vi.fn();
const mockSetColumnFilters = vi.fn();
const mockSetGlobalFilter = vi.fn();
const mockOnRefresh = vi.fn();

describe("UserManagementTable", () => {
  const mockData = [
    {
      id: "1",
      email: "<EMAIL>",
      name: "Test User",
      emailVerified: true,
      createdAt: "2023-05-01T12:00:00Z",
      lastLogin: "2023-05-02T14:30:00Z",
      loginCount: 5,
      isSocialAuth: false,
      roles: [],
    },
    {
      id: "2",
      email: "<EMAIL>",
      name: "User Two",
      emailVerified: false,
      createdAt: "2023-05-01T10:00:00Z",
      lastLogin: undefined,
      loginCount: 0,
      isSocialAuth: true,
      roles: [],
    },
  ];

  const defaultProps = {
    data: mockData,
    pagination: { pageIndex: 0, pageSize: 10 } as PaginationState,
    setPagination: mockSetPagination,
    sorting: [] as SortingState,
    setSorting: mockSetSorting,
    columnFilters: [] as ColumnFiltersState,
    setColumnFilters: mockSetColumnFilters,
    isLoading: false,
    isFetching: false,
    error: null,
    rowCount: 2,
    setGlobalFilter: mockSetGlobalFilter,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render the datagrid with correct data", () => {
    render(<UserManagementTable {...defaultProps} />);

    expect(screen.getByTestId("datagrid")).toBeInTheDocument();

    const dataElement = screen.getByTestId("data");
    const parsedData = JSON.parse(dataElement.textContent || "[]");

    expect(parsedData).toHaveLength(2);
    expect(parsedData[0].email).toBe("<EMAIL>");
    expect(parsedData[1].email).toBe("<EMAIL>");
  });

  it("should render all expected columns", () => {
    render(<UserManagementTable {...defaultProps} />);

    const columnsElement = screen.getByTestId("columns");
    const columnHeaders = JSON.parse(columnsElement.textContent || "[]");

    const expectedHeaders = [
      "Email",
      "Name",
      "Email Verified",
      "Created At",
      "Last Login",
      "Login Count",
      "Social Auth",
    ];

    expect(columnHeaders).toEqual(expectedHeaders);
  });

  it("should render email as link", () => {
    render(<UserManagementTable {...defaultProps} />);

    const emailCell = screen.getByTestId("email-cell");
    expect(emailCell).toHaveTextContent("<EMAIL>");
    expect(screen.getByTestId("user-link")).toHaveAttribute(
      "href",
      "/ict-user-management/1",
    );
  });

  it("should handle pagination changes", () => {
    render(<UserManagementTable {...defaultProps} />);

    fireEvent.click(screen.getByTestId("page-change"));

    expect(mockSetPagination).toHaveBeenCalledWith({
      pageIndex: 1,
      pageSize: 10,
    });
  });

  it("should handle sorting changes", () => {
    render(<UserManagementTable {...defaultProps} />);

    fireEvent.click(screen.getByTestId("sort"));

    expect(mockSetSorting).toHaveBeenCalledWith([{ id: "email", desc: true }]);
  });

  it("should handle filter changes", () => {
    render(<UserManagementTable {...defaultProps} />);

    fireEvent.click(screen.getByTestId("filter"));

    expect(mockSetColumnFilters).toHaveBeenCalledWith([
      { id: "email", value: "<EMAIL>" },
    ]);
    expect(mockSetGlobalFilter).toHaveBeenCalledWith("test");
  });

  it("should handle refresh button click when onRefresh is provided", () => {
    render(<UserManagementTable {...defaultProps} onRefresh={mockOnRefresh} />);

    fireEvent.click(screen.getByTestId("refresh"));

    expect(mockOnRefresh).toHaveBeenCalled();
  });

  it("should not show refresh button when onRefresh is not provided", () => {
    render(<UserManagementTable {...defaultProps} />);

    expect(screen.queryByTestId("refresh")).not.toBeInTheDocument();
  });
});
