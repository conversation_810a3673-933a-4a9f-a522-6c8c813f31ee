import { vi } from "vitest";
import { render, screen, userEvent } from "../../../../../test-utils";
import { AssignRolesModal } from "./assign-roles-modal";

// Mock ict-api
const mockRolesData = [
  {
    name: "admin",
    description: "Administrator role",
  },
  {
    name: "user",
    description: "User role",
  },
  {
    name: "viewer",
    description: "Viewer role",
  },
];

const mockUseQuery = vi.fn();

vi.mock("../../../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: () => mockUseQuery(),
    },
  },
}));

// Mock Carbon React
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
    Modal: ({
      open,
      onRequestClose,
      onRequestSubmit,
      modalHeading,
      primaryButtonText,
      secondaryButtonText,
      primaryButtonDisabled,
      children,
    }: any) =>
      open ? (
        <div data-testid="modal">
          <div data-testid="modal-heading">{modalHeading}</div>
          <div data-testid="modal-content">{children}</div>
          <button
            data-testid="primary-button"
            onClick={onRequestSubmit}
            disabled={primaryButtonDisabled}
          >
            {primaryButtonText}
          </button>
          <button data-testid="secondary-button" onClick={onRequestClose}>
            {secondaryButtonText}
          </button>
        </div>
      ) : null,
    ModalBody: ({ children }: any) => (
      <div data-testid="modal-body">{children}</div>
    ),
    Loading: () => <div data-testid="loading">Loading...</div>,
    Checkbox: ({ labelText, helperText, checked, onChange, disabled }: any) => (
      <div data-testid="checkbox">
        <input
          type="checkbox"
          checked={checked}
          onChange={(e) => onChange(e, { checked: e.target.checked })}
          disabled={disabled}
          data-testid={`checkbox-${labelText}`}
        />
        <label data-testid={`label-${labelText}`}>{labelText}</label>
        {helperText && (
          <div data-testid={`helper-${labelText}`}>{helperText}</div>
        )}
      </div>
    ),
    Stack: ({ children }: any) => <div data-testid="stack">{children}</div>,
  };
});

describe("AssignRolesModal", () => {
  const mockOnClose = vi.fn();
  const mockOnAssign = vi.fn();

  const defaultProps = {
    open: true,
    onClose: mockOnClose,
    onAssign: mockOnAssign,
    assignedRoleNames: [],
    isAssigning: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseQuery.mockReturnValue({
      data: mockRolesData,
      isLoading: false,
      error: null,
    });
  });

  it("should not render when closed", () => {
    render(<AssignRolesModal {...defaultProps} open={false} />);

    expect(screen.queryByTestId("modal")).not.toBeInTheDocument();
  });

  it("should render modal when open", () => {
    render(<AssignRolesModal {...defaultProps} />);

    expect(screen.getByTestId("modal")).toBeInTheDocument();
    expect(screen.getByTestId("modal-heading")).toHaveTextContent(
      "Assign Roles",
    );
  });

  it("should render loading state", () => {
    mockUseQuery.mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
    });

    render(<AssignRolesModal {...defaultProps} />);

    expect(screen.getByTestId("loading")).toBeInTheDocument();
  });

  it("should render error state", () => {
    mockUseQuery.mockReturnValue({
      data: null,
      isLoading: false,
      error: new Error("Failed to load roles"),
    });

    render(<AssignRolesModal {...defaultProps} />);

    expect(
      screen.getByText(/Error loading available roles/),
    ).toBeInTheDocument();
  });

  it("should render available roles as checkboxes", () => {
    render(<AssignRolesModal {...defaultProps} />);

    expect(screen.getByTestId("checkbox-admin")).toBeInTheDocument();
    expect(screen.getByTestId("checkbox-user")).toBeInTheDocument();
    expect(screen.getByTestId("checkbox-viewer")).toBeInTheDocument();
  });

  it("should exclude already assigned roles", () => {
    render(
      <AssignRolesModal {...defaultProps} assignedRoleNames={["admin"]} />,
    );

    expect(screen.queryByTestId("checkbox-admin")).not.toBeInTheDocument();
    expect(screen.getByTestId("checkbox-user")).toBeInTheDocument();
    expect(screen.getByTestId("checkbox-viewer")).toBeInTheDocument();
  });

  it("should handle role selection", async () => {
    render(<AssignRolesModal {...defaultProps} />);

    const checkbox = screen.getByTestId("checkbox-admin");
    await userEvent.click(checkbox);

    expect(checkbox).toBeChecked();
  });

  it("should have primary button disabled when no roles selected", () => {
    render(<AssignRolesModal {...defaultProps} />);

    const primaryButton = screen.getByTestId("primary-button");
    expect(primaryButton).toBeDisabled();
  });

  it("should enable primary button when roles are selected", async () => {
    render(<AssignRolesModal {...defaultProps} />);

    const checkbox = screen.getByTestId("checkbox-admin");
    await userEvent.click(checkbox);

    const primaryButton = screen.getByTestId("primary-button");
    expect(primaryButton).not.toBeDisabled();
  });

  it("should call onAssign with selected roles", async () => {
    render(<AssignRolesModal {...defaultProps} />);

    // Select a role
    const checkbox = screen.getByTestId("checkbox-admin");
    await userEvent.click(checkbox);

    // Click assign button
    const assignButton = screen.getByTestId("primary-button");
    await userEvent.click(assignButton);

    expect(mockOnAssign).toHaveBeenCalledWith(["admin"]);
  });

  it("should call onClose when cancel button is clicked", async () => {
    render(<AssignRolesModal {...defaultProps} />);

    const cancelButton = screen.getByTestId("secondary-button");
    await userEvent.click(cancelButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it("should disable controls when assigning", () => {
    render(<AssignRolesModal {...defaultProps} isAssigning={true} />);

    const checkbox = screen.getByTestId("checkbox-admin");
    expect(checkbox).toBeDisabled();

    const primaryButton = screen.getByTestId("primary-button");
    expect(primaryButton).toBeDisabled();
  });

  it("should show message when no roles available", () => {
    render(
      <AssignRolesModal
        {...defaultProps}
        assignedRoleNames={["admin", "user", "viewer"]}
      />,
    );

    expect(
      screen.getByText(/No additional roles available/),
    ).toBeInTheDocument();
  });
});
