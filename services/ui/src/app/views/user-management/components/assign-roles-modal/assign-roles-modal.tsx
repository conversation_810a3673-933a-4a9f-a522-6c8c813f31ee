import { Checkbox, Loading, Modal, ModalB<PERSON>, Stack } from "@carbon/react";
import type React from "react";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { ictApi } from "../../../../api/ict-api";
import { Role } from "../../types";

interface AssignRolesModalProps {
  /**
   * Boolean to determine if the modal is open
   */
  open: boolean;

  /**
   * Function to handle closing the modal
   */
  onClose: () => void;

  /**
   * Function to handle assigning roles
   */
  onAssign: (roleNames: string[]) => Promise<void>;

  /**
   * Array of currently assigned role names to exclude from selection
   */
  assignedRoleNames?: string[];

  /**
   * Loading state for the assign operation
   */
  isAssigning?: boolean;
}

/**
 * Modal component for assigning roles to a user
 * Displays a list of available roles with checkboxes for selection
 */
export const AssignRolesModal: React.FC<AssignRolesModalProps> = ({
  open,
  onClose,
  onAssign,
  assignedRoleNames = [],
  isAssigning = false,
}) => {
  const { t } = useTranslation();
  const [selectedRoleNames, setSelectedRoleNames] = useState<string[]>([]);

  // Fetch available roles from API
  const {
    data: rolesData,
    isLoading,
    error,
  } = ictApi.client.useQuery("get", "/admin/auth/roles", {}, { enabled: open });

  const roles = rolesData || [];

  // Filter out already assigned roles
  const availableRoles = roles.filter(
    (role: Role) => !assignedRoleNames.includes(role.name || ""),
  );

  // Reset selection when modal opens/closes
  useEffect(() => {
    if (!open) {
      setSelectedRoleNames([]);
    }
  }, [open]);

  const handleRoleToggle = (roleName: string, checked: boolean) => {
    setSelectedRoleNames((prev) => {
      if (checked) {
        return [...prev, roleName];
      } else {
        return prev.filter((name) => name !== roleName);
      }
    });
  };

  const handleAssign = async () => {
    if (selectedRoleNames.length > 0) {
      await onAssign(selectedRoleNames);
    }
  };

  return (
    <Modal
      open={open}
      onRequestClose={onClose}
      modalHeading={t("userManagement.assignRoles", "Assign Roles")}
      primaryButtonText={t("userManagement.assign", "Assign")}
      secondaryButtonText={t("userManagement.cancel", "Cancel")}
      onRequestSubmit={handleAssign}
      primaryButtonDisabled={selectedRoleNames.length === 0 || isAssigning}
      size="md"
      preventCloseOnClickOutside={isAssigning}
    >
      <ModalBody>
        {isLoading && <Loading />}

        {error && (
          <div>
            {t(
              "userManagement.errorLoadingRoles",
              "Error loading available roles.",
            )}
          </div>
        )}

        {!isLoading && !error && (
          <Stack gap={4}>
            <p>
              {t(
                "userManagement.assignRolesDescription",
                "Select the roles you want to assign to this user.",
              )}
            </p>

            {availableRoles.length === 0 ? (
              <p>
                {t(
                  "userManagement.noAvailableRoles",
                  "No additional roles available to assign.",
                )}
              </p>
            ) : (
              <Stack gap={3}>
                {availableRoles.map((role: Role, index: number) => (
                  <Checkbox
                    key={role.name || index}
                    id={`role-${role.name || index}`}
                    labelText={role.name || ""}
                    helperText={
                      role.description ||
                      t(
                        "userManagement.noDescription",
                        "No description available",
                      )
                    }
                    checked={selectedRoleNames.includes(role.name || "")}
                    onChange={(_evt, { checked }) =>
                      handleRoleToggle(role.name || "", checked)
                    }
                    disabled={isAssigning}
                  />
                ))}
              </Stack>
            )}
          </Stack>
        )}
      </ModalBody>
    </Modal>
  );
};

export default AssignRolesModal;
