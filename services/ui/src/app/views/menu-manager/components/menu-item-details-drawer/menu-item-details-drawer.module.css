.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  width: 100%;
}

.errorContainer {
  padding: 1rem;
  border-left: 4px solid var(--cds-support-error);
  background-color: var(--cds-background);
  margin: 1rem 0;
}

.infoSection {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  padding: 1rem 0;
}

.infoGroup {
  min-width: 200px;
}

.infoLabel {
  font-size: 0.875rem;
  color: var(--cds-text-secondary);
  margin-bottom: 0.5rem;
}

.infoValue {
  font-size: 1rem;
  color: var(--cds-text-primary);
  margin: 0;
}

.divider {
  height: 1px;
  background-color: var(--cds-border-subtle);
  margin: 1rem 0;
}

.sectionHeading {
  font-size: 1.25rem;
  margin: 0;
  color: var(--cds-text-primary);
}

.tableContainer {
  width: 100%;
  margin-top: 0.5rem;
  /* Set a fixed height for the table to ensure proper scrolling */
  height: 400px;
}

.helperText {
  font-size: 0.75rem;
  color: var(--cds-text-secondary);
  margin-top: 0.5rem;
  margin-bottom: 0;
}

.rolesList {
  margin-top: 1rem;
}

.rolesList :global(.cds--contained-list) {
  max-width: 100%;
}

.rolesList :global(.cds--contained-list-item) {
  padding: 0.5rem 1rem;
}

.rolesList :global(.cds--contained-list-item__content) {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.roleItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.roleItem span {
  font-size: 0.875rem;
}

.centeredContainer {
  width: 100%;
  max-width: 100%;
  display: flex;
  justify-content: flex-start;
  margin-top: 2rem;
  margin-left: 2rem;
  overflow-x: hidden;
  box-sizing: border-box;
}

.centeredContainer :global(.cds--contained-list) {
  width: 100%;
  max-width: 324px;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* Icon display styles */
.currentIconDisplay {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.iconPreview {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background-color: var(--cds-background-selected);
  border: 1px solid var(--cds-border-subtle);
  border-radius: 0.375rem;
}

.iconLabel {
  font-size: 0.875rem;
  color: var(--cds-text-primary);
  font-weight: 500;
}

/* Add container styles to prevent horizontal scroll */
.drawerContainer {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

.drawerContainer :global(.cds--grid) {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
  padding-left: 0;
  padding-right: 0;
}

.drawerContainer :global(.cds--col) {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  box-sizing: border-box;
}

/* Ensure form inputs don't overflow */
.drawerContainer :global(.cds--text-input),
.drawerContainer :global(.cds--form-group) {
  max-width: 100%;
  box-sizing: border-box;
}

.drawerContainer :global(.cds--form-item) {
  margin-bottom: 1rem;
}
