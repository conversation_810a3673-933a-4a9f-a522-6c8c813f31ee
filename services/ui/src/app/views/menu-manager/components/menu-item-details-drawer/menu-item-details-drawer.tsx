import {
  Button,
  FormGroup,
  Tag,
  TextInput,
  Toggle,
  ContainedList,
  ContainedListItem,
  Grid,
  Column,
  TextArea,
} from "@carbon/react";
import { Drawer } from "../../../../components/drawer/drawer";
import { MenuItem } from "@ict/sdk/types";
import { useState, useEffect } from "react";
import { UserRoles } from "@ict/sdk/types/roles/security-roles";
import classes from "./menu-item-details-drawer.module.css";
import { useTranslation } from "react-i18next";
import { IconPickerModal } from "../../../../components/icon-picker";

interface MenuItemDetailDrawerProps {
  opened: boolean;
  onClose: () => void;
  menuItem: MenuItem;
  onItemChange: (updatedItem: MenuItem) => void;
  existingIds: string[];
}

export const MenuItemDetailsDrawer = ({
  opened,
  onClose,
  menuItem,
  onItemChange,
  existingIds,
}: MenuItemDetailDrawerProps) => {
  const [formData, setFormData] = useState<MenuItem>(menuItem);
  const [idError, setIdError] = useState<string>("");
  const [helpLinkError, setHelpLinkError] = useState<string>("");
  const { t } = useTranslation();

  // Update form data when menuItem prop changes
  useEffect(() => {
    setFormData(menuItem);
    setIdError("");
  }, [menuItem]);

  // Helper function to sanitize ID (lowercase, replace spaces with dashes)
  const sanitizeId = (input: string): string => {
    return input
      .toLowerCase()
      .replace(/\s+/g, "-")
      .replace(/[^a-z0-9-]/g, "");
  };

  // Helper function to validate ID uniqueness
  const validateId = (id: string): string => {
    if (!id) {
      return "ID is required";
    }
    if (id !== formData.id && existingIds.includes(id)) {
      return "ID must be unique";
    }
    if (id !== sanitizeId(id)) {
      return "ID can only contain lowercase letters, numbers, and dashes";
    }
    return "";
  };

  // Validation for the help link to ensure user does not include the base url
  const validateHelpLink = (value: string): string => {
    if (
      value.startsWith("http") ||
      value.startsWith("www") ||
      value.startsWith(import.meta.env.VITE_DOCUMENTATION_URL)
    ) {
      return `Help link will be prepended with ${import.meta.env.VITE_DOCUMENTATION_URL}, do not include it here.`;
    }
    return "";
  };

  const handleChange = (
    field: keyof MenuItem,
    value: string | boolean | string[],
  ) => {
    const updatedItem = { ...formData, [field]: value };

    // Special handling for ID changes
    if (field === "id" && typeof value === "string") {
      const sanitizedId = sanitizeId(value);
      updatedItem.id = sanitizedId;

      // Validate the ID
      const error = validateId(sanitizedId);
      setIdError(error);

      // Auto-update link to match ID
      updatedItem.link = `/${sanitizedId}`;

      // Auto-update viewConfigId for dashboard view types
      if (updatedItem.viewType === "dashboard") {
        updatedItem.viewConfigId = `dashboard-${sanitizedId}`;
      }
    }

    // Validate the help link
    if (field === "helpLink" && typeof value === "string") {
      const error = validateHelpLink(value);
      setHelpLinkError(error);
    }

    // Update local state
    setFormData(updatedItem);

    // Notify parent of changes
    onItemChange(updatedItem);
  };

  const handleRoleChange = (role: string, add: boolean) => {
    const currentRoles = formData.roles || [];
    const updatedRoles = add
      ? [...currentRoles, role]
      : currentRoles.filter((r) => r !== role);

    const updatedItem = { ...formData, roles: updatedRoles };
    setFormData(updatedItem);
    onItemChange(updatedItem);
  };

  const handleIconChange = (iconName: string) => {
    handleChange("icon", iconName);
  };

  // Format value or show placeholder for empty values
  const formatValue = (
    value: string | boolean | string[] | null | undefined,
  ) => {
    if (value === null || value === undefined || value === "") {
      return "Not Implemented";
    }
    return value;
  };

  // Custom footer with close button
  const drawerFooter = (
    <Button kind="secondary" onClick={onClose}>
      {"Close"}
    </Button>
  );

  const isCustom = !menuItem.custom === true;

  const renderItemType = () => {
    if (menuItem.children && menuItem.children.length > 0) {
      return <Tag type="green">{"Menu Group"}</Tag>;
    }
    return <Tag type="blue">{formatValue(formData.viewType)}</Tag>;
  };

  // Get the current selected icon name
  const selectedIconName = formData.icon || undefined;

  return (
    <Drawer
      open={opened}
      onClose={onClose}
      title={<h3>{"Menu Item Configuration"}</h3>}
      footer={drawerFooter}
      headerHeight={48}
    >
      <div className={classes.drawerContainer}>
        <Grid>
          <Column lg={10}>
            <FormGroup legendText="Item Details">
              <TextInput
                id="menu-item-id"
                readOnly={isCustom}
                labelText="ID"
                value={formData.id || ""}
                onChange={(e) => handleChange("id", e.target.value)}
                placeholder="Enter menu item ID"
                invalid={!!idError}
                invalidText={idError}
              />
              <TextInput
                id="menu-item-label"
                size="sm"
                readOnly={isCustom}
                labelText="Label"
                value={t(formData.label)}
                onChange={(e) => handleChange("label", e.target.value)}
                placeholder="Enter menu item label"
              />
              <TextArea
                id="menu-item-help-link"
                rows={2}
                labelText="Help Link"
                value={formData.helpLink || ""}
                onChange={(e) => handleChange("helpLink", e.target.value)}
                placeholder="Enter help link"
                invalid={!!helpLinkError}
                invalidText={helpLinkError}
                helperText={`Full link: ${import.meta.env.VITE_DOCUMENTATION_URL}${formData.helpLink}`}
              />
              {menuItem.isTop === true && (
                <FormGroup legendText="Icon">
                  {selectedIconName ? (
                    <div className={classes.currentIconDisplay}>
                      <IconPickerModal
                        selectedIcon={selectedIconName}
                        onIconSelect={handleIconChange}
                        iconSize={24}
                        placeholder="Select an icon"
                        selectedText="Change Icon"
                        modalTitle="Select Menu Item Icon"
                        buttonKind="secondary"
                        buttonSize="sm"
                      />
                    </div>
                  ) : (
                    <IconPickerModal
                      selectedIcon={selectedIconName}
                      onIconSelect={handleIconChange}
                      iconSize={24}
                      placeholder="Select an icon"
                      selectedText="Change icon"
                      modalTitle="Select Menu Item Icon"
                      buttonKind="primary"
                    />
                  )}
                </FormGroup>
              )}
            </FormGroup>
          </Column>
          <Column lg={6}>
            <FormGroup legendText="Visibility">
              <Toggle
                id="menu-item-visible"
                labelText=""
                toggled={formData.visible === true}
                onToggle={(toggled) => {
                  const updatedItem = { ...formData, visible: toggled };
                  setFormData(updatedItem);
                  onItemChange(updatedItem);
                }}
              />
            </FormGroup>
            <FormGroup legendText="Type">{renderItemType()}</FormGroup>
            <FormGroup legendText="Required Roles">
              {formData.roles && formData.roles.length > 0 ? (
                formData.roles.map((role) => (
                  <Tag key={role} type="blue" title={role}>
                    {role}
                  </Tag>
                ))
              ) : (
                <Tag type="gray" title={"No roles required"}>
                  {"No roles required"}
                </Tag>
              )}
            </FormGroup>
          </Column>
        </Grid>
        <div className={classes.centeredContainer}>
          <ContainedList label="Available Roles" size="lg">
            {UserRoles.map((role) => {
              const isRequired = formData.roles?.includes(role);
              return (
                <ContainedListItem
                  key={role}
                  action={
                    <Button
                      size="sm"
                      kind={isRequired ? "danger" : "secondary"}
                      style={{ width: "80px" }}
                      onClick={() => handleRoleChange(role, !isRequired)}
                    >
                      {isRequired ? "Remove" : "Add"}
                    </Button>
                  }
                >
                  <div className={classes.roleItem}>
                    <span>{role}</span>
                  </div>
                </ContainedListItem>
              );
            })}
          </ContainedList>
        </div>
      </div>
    </Drawer>
  );
};

export default MenuItemDetailsDrawer;
