import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, userEvent, within } from "../../../../../test-utils";
import { MenuItemDetailsDrawer } from "./menu-item-details-drawer";
import { MenuItem } from "@ict/sdk/types";
import { UserRoles } from "@ict/sdk/types/roles/security-roles";

// Mock Carbon React components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

// Mock the Drawer component
vi.mock("../../../../../app/components/drawer/drawer", () => ({
  Drawer: ({ children, open, title, footer, onClose }: any) =>
    open ? (
      <div data-testid="drawer">
        <div data-testid="drawer-header">{title}</div>
        <div data-testid="drawer-content">{children}</div>
        <div data-testid="drawer-footer">{footer}</div>
        <button onClick={onClose} data-testid="drawer-close">
          Close
        </button>
      </div>
    ) : null,
}));

// Mock the IconPickerModal component
vi.mock("../../../../../app/components/icon-picker", () => ({
  IconPickerModal: ({
    selectedIcon,
    onIconSelect,
    selectedText,
    buttonKind,
  }: any) => (
    <button
      onClick={() => onIconSelect("NewIcon")}
      data-testid={`icon-picker-${buttonKind || "primary"}`}
    >
      {selectedText || "Select Icon"} {selectedIcon && `(${selectedIcon})`}
    </button>
  ),
}));

describe("MenuItemDetailsDrawer", () => {
  const mockOnClose = vi.fn();
  const mockOnItemChange = vi.fn();
  const existingIds = ["existing-id", "another-id"];

  const mockMenuItem: MenuItem = {
    id: "test-item",
    label: "Test Item",
    link: "/test-item",
    viewType: "dashboard",
    viewConfigId: "dashboard-test-item",
    visible: true,
    icon: "Dashboard",
    custom: true,
    roles: ["ct_engineers"],
    isTop: true,
  };

  const defaultProps = {
    opened: true,
    onClose: mockOnClose,
    menuItem: mockMenuItem,
    onItemChange: mockOnItemChange,
    existingIds,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders drawer when opened", () => {
    render(<MenuItemDetailsDrawer {...defaultProps} />);

    expect(screen.getByTestId("drawer")).toBeInTheDocument();
    expect(screen.getByTestId("drawer-header")).toHaveTextContent(
      "Menu Item Configuration",
    );
    expect(screen.getByDisplayValue("test-item")).toBeInTheDocument();
    expect(screen.getByDisplayValue("Test Item")).toBeInTheDocument();
  });

  it("does not render drawer when closed", () => {
    render(<MenuItemDetailsDrawer {...defaultProps} opened={false} />);

    expect(screen.queryByTestId("drawer")).not.toBeInTheDocument();
  });

  it("displays menu item type correctly for dashboard", () => {
    render(<MenuItemDetailsDrawer {...defaultProps} />);

    expect(screen.getByText("dashboard")).toBeInTheDocument();
  });

  it("displays menu item type correctly for menu group", () => {
    const menuGroupItem: MenuItem = {
      ...mockMenuItem,
      children: [{ id: "child-1", label: "Child 1", link: "/child-1" }],
    };

    render(
      <MenuItemDetailsDrawer {...defaultProps} menuItem={menuGroupItem} />,
    );

    expect(screen.getByText("Menu Group")).toBeInTheDocument();
  });

  it("validates ID field correctly - shows error for empty ID", async () => {
    const user = userEvent.setup();
    render(<MenuItemDetailsDrawer {...defaultProps} />);

    const idInput = screen.getByDisplayValue("test-item");
    await user.clear(idInput);

    // Wait for validation to trigger
    expect(mockOnItemChange).toHaveBeenLastCalledWith(
      expect.objectContaining({
        id: "",
        link: "/",
        viewConfigId: "dashboard-",
      }),
    );
  });

  it("validates ID field correctly - shows error for duplicate ID", async () => {
    const user = userEvent.setup();
    render(<MenuItemDetailsDrawer {...defaultProps} />);

    const idInput = screen.getByDisplayValue("test-item");
    await user.clear(idInput);
    await user.type(idInput, "existing-id");

    expect(mockOnItemChange).toHaveBeenLastCalledWith(
      expect.objectContaining({
        id: "existing-id",
        link: "/existing-id",
        viewConfigId: "dashboard-existing-id",
      }),
    );
  });

  it("sanitizes ID input correctly", async () => {
    const user = userEvent.setup();
    render(<MenuItemDetailsDrawer {...defaultProps} />);

    const idInput = screen.getByDisplayValue("test-item");
    await user.clear(idInput);
    await user.type(idInput, "My Special ID! @#$");

    expect(mockOnItemChange).toHaveBeenLastCalledWith(
      expect.objectContaining({
        id: "my-special-id-",
        link: "/my-special-id-",
        viewConfigId: "dashboard-my-special-id-",
      }),
    );
  });

  it("updates link when ID changes", async () => {
    const user = userEvent.setup();
    render(<MenuItemDetailsDrawer {...defaultProps} />);

    const idInput = screen.getByDisplayValue("test-item");
    await user.clear(idInput);
    await user.type(idInput, "new-id");

    expect(mockOnItemChange).toHaveBeenLastCalledWith(
      expect.objectContaining({
        id: "new-id",
        link: "/new-id",
        viewConfigId: "dashboard-new-id",
      }),
    );
  });

  it("updates label correctly", async () => {
    const user = userEvent.setup();
    render(<MenuItemDetailsDrawer {...defaultProps} />);

    const labelInput = screen.getByDisplayValue("Test Item");
    await user.clear(labelInput);
    await user.type(labelInput, "New Label");

    expect(mockOnItemChange).toHaveBeenLastCalledWith(
      expect.objectContaining({
        label: "New Label",
      }),
    );
  });

  it("toggles visibility correctly", async () => {
    const user = userEvent.setup();
    render(<MenuItemDetailsDrawer {...defaultProps} />);

    const visibilityToggle = screen.getByRole("switch");
    await user.click(visibilityToggle);

    expect(mockOnItemChange).toHaveBeenLastCalledWith(
      expect.objectContaining({
        visible: false,
      }),
    );
  });

  it("shows icon picker for top-level items", () => {
    render(<MenuItemDetailsDrawer {...defaultProps} />);

    expect(screen.getByTestId("icon-picker-secondary")).toBeInTheDocument();
    expect(screen.getByText("Change Icon (Dashboard)")).toBeInTheDocument();
  });

  it("does not show icon picker for non-top-level items", () => {
    const nonTopItem = { ...mockMenuItem, isTop: false };
    render(<MenuItemDetailsDrawer {...defaultProps} menuItem={nonTopItem} />);

    expect(screen.queryByTestId("icon-picker-primary")).not.toBeInTheDocument();
    expect(
      screen.queryByTestId("icon-picker-secondary"),
    ).not.toBeInTheDocument();
  });

  it("updates icon when icon picker is used", async () => {
    const user = userEvent.setup();
    render(<MenuItemDetailsDrawer {...defaultProps} />);

    const iconPicker = screen.getByTestId("icon-picker-secondary");
    await user.click(iconPicker);

    expect(mockOnItemChange).toHaveBeenLastCalledWith(
      expect.objectContaining({
        icon: "NewIcon",
      }),
    );
  });

  it("displays current roles correctly", () => {
    render(<MenuItemDetailsDrawer {...defaultProps} />);

    // Check that ct_engineers appears in the required roles section
    const requiredRolesSection = screen
      .getByText("Required Roles")
      .closest("fieldset");
    expect(requiredRolesSection).toHaveTextContent("ct_engineers");
  });

  it("displays 'No roles required' when no roles are set", () => {
    const itemWithoutRoles = { ...mockMenuItem, roles: [] };
    render(
      <MenuItemDetailsDrawer {...defaultProps} menuItem={itemWithoutRoles} />,
    );

    expect(screen.getByText("No roles required")).toBeInTheDocument();
  });

  it("adds role correctly", async () => {
    const user = userEvent.setup();
    const itemWithoutRoles = { ...mockMenuItem, roles: [] };
    render(
      <MenuItemDetailsDrawer {...defaultProps} menuItem={itemWithoutRoles} />,
    );

    // Find the first Add button (for ct_engineers)
    const addButtons = screen.getAllByRole("button", { name: "Add" });
    await user.click(addButtons[0]);

    expect(mockOnItemChange).toHaveBeenLastCalledWith(
      expect.objectContaining({
        roles: expect.arrayContaining([UserRoles[0]]), // First role in the list
      }),
    );
  });

  it("removes role correctly", async () => {
    const user = userEvent.setup();
    render(<MenuItemDetailsDrawer {...defaultProps} />);

    // Find the remove button for the existing role
    const removeButton = screen.getByRole("button", { name: /Remove/ });
    await user.click(removeButton);

    expect(mockOnItemChange).toHaveBeenLastCalledWith(
      expect.objectContaining({
        roles: [],
      }),
    );
  });

  it("renders all available roles in the list", () => {
    render(<MenuItemDetailsDrawer {...defaultProps} />);

    // Check that all roles appear in the available roles list
    const availableRolesList = screen.getByLabelText("Available Roles");
    UserRoles.forEach((role) => {
      expect(availableRolesList).toHaveTextContent(role);
    });
  });

  it("shows correct button state for roles", () => {
    render(<MenuItemDetailsDrawer {...defaultProps} />);

    // The item has ct_engineers role, so the remove button should be present for it
    const roleItems = screen.getAllByText(
      /ct_engineers|ct_configurator|ct_tableau/,
    );
    expect(roleItems.length).toBeGreaterThan(0);

    // Check if Add and Remove buttons exist
    expect(screen.getByRole("button", { name: /Remove/ })).toBeInTheDocument();
    expect(screen.getAllByRole("button", { name: "Add" })).toHaveLength(
      UserRoles.length - 1,
    );
  });

  it("makes ID field read-only for non-custom items", () => {
    const nonCustomItem = { ...mockMenuItem, custom: false };
    render(
      <MenuItemDetailsDrawer {...defaultProps} menuItem={nonCustomItem} />,
    );

    const idInput = screen.getByDisplayValue("test-item");
    expect(idInput).toHaveAttribute("readonly");
  });

  it("makes label field read-only for non-custom items", () => {
    const nonCustomItem = { ...mockMenuItem, custom: false };
    render(
      <MenuItemDetailsDrawer {...defaultProps} menuItem={nonCustomItem} />,
    );

    const labelInput = screen.getByDisplayValue("Test Item");
    expect(labelInput).toHaveAttribute("readonly");
  });

  it("calls onClose when close button is clicked", async () => {
    const user = userEvent.setup();
    render(<MenuItemDetailsDrawer {...defaultProps} />);

    const closeButton = screen.getByTestId("drawer-close");
    await user.click(closeButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it("calls onClose when footer close button is clicked", async () => {
    const user = userEvent.setup();
    render(<MenuItemDetailsDrawer {...defaultProps} />);

    const footerCloseButton = within(
      screen.getByTestId("drawer-footer"),
    ).getByRole("button");
    await user.click(footerCloseButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it("updates form data when menuItem prop changes", () => {
    const { rerender } = render(<MenuItemDetailsDrawer {...defaultProps} />);

    const newMenuItem = {
      ...mockMenuItem,
      id: "updated-item",
      label: "Updated Item",
    };

    rerender(
      <MenuItemDetailsDrawer {...defaultProps} menuItem={newMenuItem} />,
    );

    expect(screen.getByDisplayValue("updated-item")).toBeInTheDocument();
    expect(screen.getByDisplayValue("Updated Item")).toBeInTheDocument();
  });

  it("handles undefined roles gracefully", () => {
    const itemWithUndefinedRoles = { ...mockMenuItem, roles: undefined };
    render(
      <MenuItemDetailsDrawer
        {...defaultProps}
        menuItem={itemWithUndefinedRoles}
      />,
    );

    expect(screen.getByText("No roles required")).toBeInTheDocument();
  });

  it("shows primary icon picker when no icon is selected", () => {
    const itemWithoutIcon = { ...mockMenuItem, icon: undefined };
    render(
      <MenuItemDetailsDrawer {...defaultProps} menuItem={itemWithoutIcon} />,
    );

    expect(screen.getByTestId("icon-picker-primary")).toBeInTheDocument();
    expect(screen.getByText("Change icon")).toBeInTheDocument();
  });
});
