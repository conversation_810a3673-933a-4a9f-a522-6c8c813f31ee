.treeContainer {
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--cds-border-subtle-01);
  border-radius: 4px;
  background: var(--cds-layer-01);
}

.treeContainer.dark {
  border-color: var(--cds-border-subtle-01);
  background: var(--cds-layer-01);
  --rct-color-focustree-item-selected-bg: #2c2c2c;
  --rct-color-focustree-item-selected-text: #ffffff;
  --rct-color-focustree-item-hover-bg: #3c3c3c;
  --rct-color-focustree-item-hover-text: #ffffff;
  --rct-color-focustree-item-active-bg: #4c4c4c;
  --rct-color-focustree-item-active-text: #ffffff;
  --rct-color-arrow: var(--cds-text-primary);
}

.treeControls {
  display: flex;
  gap: 8px;
  padding: 8px 12px;
  border-bottom: 1px solid var(--cds-border-subtle-00);
  background: var(--cds-layer-02);
}

.controlButton {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: 1px solid var(--cds-border-subtle-01);
  border-radius: 4px;
  background: var(--cds-layer-01);
  color: var(--cds-text-primary);
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s ease;
}

.controlButton:hover {
  background: var(--cds-layer-hover-01);
}

.controlButton:active {
  background: var(--cds-layer-selected-01);
}

.controlLabel {
  font-size: 12px;
}

.treeContent {
  flex: 1;
  overflow-y: auto;
  min-height: 0; /* This is important for flex child scrolling */
  max-height: calc(
    100vh - 200px
  ); /* Slightly less than container to account for controls */
}

.treeItem {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid var(--cds-border-subtle-00);
  transition: background-color 0.2s ease;
  color: var(--cds-text-primary);
  background: transparent;
  border: none;
  width: 100%;
  text-align: left;
}

.treeItem:hover {
  background-color: var(--cds-layer-hover-01);
}

.treeItem.selected {
  background-color: var(--cds-layer-selected-01);
  color: var(--cds-text-inverse);
}

.treeItem.focused {
  outline: 2px solid var(--cds-focus);
  outline-offset: -2px;
}

.treeItem.folder {
  font-weight: 500;
}

.treeItem.expanded {
  /* Add any specific styling for expanded folders if needed */
}

.itemContent {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 8px;
}

.folderIcon,
.itemIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: var(--cds-icon-primary);
}

.itemLabel {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Styles for hidden menu items */
.hiddenItem {
  opacity: 0.6;
}

.hiddenLabel {
  font-style: italic;
  color: var(--cds-text-secondary);
}

.visibilityIcon {
  flex-shrink: 0;
  color: var(--cds-icon-secondary);
  margin-left: auto;
}

.editButton {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem;
  border: none;
  background: none;
  color: var(--cds-icon-secondary);
  cursor: pointer;
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.editButton:hover {
  background-color: var(--cds-layer-hover-01);
  color: var(--cds-icon-primary);
}

.itemContent:hover .editButton {
  opacity: 1;
}

.defaultRouteIcon {
  color: var(--cds-icon-secondary);
  margin-right: 4px;
  vertical-align: middle;
}

.deleteButton {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: var(--cds-icon-danger);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.deleteButton:hover {
  background-color: var(--cds-layer-hover-01);
  border-radius: 4px;
}

.itemContent:hover .deleteButton,
.treeItem.selected .deleteButton {
  opacity: 1;
}
