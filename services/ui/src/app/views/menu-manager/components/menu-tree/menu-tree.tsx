import { useState, useEffect, useMemo } from "react";
import {
  ControlledTreeEnvironment,
  Tree,
  TreeItemIndex,
  TreeItem,
  DraggingPosition,
} from "react-complex-tree";
import "react-complex-tree/lib/style-modern.css";
import {
  ExpandAll,
  CollapseAll,
  ViewOff,
  View,
  Edit,
  Home,
  TrashCan,
} from "@carbon/icons-react";
import { useTranslation } from "react-i18next";
import { useTheme, ThemeMode } from "../../../../layout/theme";
import classes from "./menu-tree.module.css";
import { MenuItem } from "@ict/sdk/types";
import { Modal } from "@carbon/react";

interface MenuTreeProps {
  menuItems: MenuItem[];
  selectedItemId?: string;
  onItemSelect: (item: MenuItem) => void;
  onMenuReorder?: (reorderedMenuItems: MenuItem[]) => void;
  onDeleteItem?: (itemId: string) => void;
  onEnableAll?: () => void;
}

export const MenuTree = ({
  menuItems,
  selectedItemId,
  onItemSelect,
  onMenuReorder,
  onDeleteItem,
  onEnableAll,
}: MenuTreeProps) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === ThemeMode.DARK;

  // Controlled state for the tree
  const [focusedItem, setFocusedItem] = useState<string>();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  // Add state for delete confirmation modal
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<MenuItem | null>(null);

  // Create a flat map of all menu items for easy lookup
  const menuItemsMap = useMemo(() => {
    const map = new Map<string, MenuItem>();

    const flattenMenuItems = (items: MenuItem[]): void => {
      for (const item of items) {
        map.set(item.id, item);
        if (item.children) {
          flattenMenuItems(item.children);
        }
      }
    };

    flattenMenuItems(menuItems);
    return map;
  }, [menuItems]);

  // Convert menu items to react-complex-tree format
  const treeItems = useMemo(() => {
    const items: Record<TreeItemIndex, TreeItem> = {
      root: {
        index: "root",
        canMove: false,
        isFolder: true,
        children: menuItems.map((item) => item.id),
        data: { label: "Menu Root" },
        canRename: false,
      },
    };

    const convertMenuItem = (menuItem: MenuItem): void => {
      items[menuItem.id] = {
        index: menuItem.id,
        canMove: true,
        isFolder: !!(menuItem.children && menuItem.children.length > 0),
        children: menuItem.children?.map((child) => child.id) || [],
        data: menuItem,
        canRename: false,
      };

      if (menuItem.children) {
        for (const child of menuItem.children) {
          convertMenuItem(child);
        }
      }
    };

    for (const menuItem of menuItems) {
      convertMenuItem(menuItem);
    }

    return items;
  }, [menuItems]);

  // Update selected items when external selectedItemId changes
  useEffect(() => {
    if (selectedItemId && selectedItemId !== selectedItems[0]) {
      setSelectedItems([selectedItemId]);
    }
  }, [selectedItemId, selectedItems]);

  // Handle drag and drop reordering
  const handleDrop = (items: TreeItem[], target: DraggingPosition) => {
    if (!onMenuReorder) return;

    // Get the dragged item IDs
    const draggedItemIds = items.map((item) => String(item.index));

    // Find the dragged menu items
    const draggedItems = draggedItemIds
      .map((id) => menuItemsMap.get(id))
      .filter(Boolean) as MenuItem[];

    if (draggedItems.length === 0) return;

    // Create a deep copy of the menu items to modify
    const newMenuItems = JSON.parse(JSON.stringify(menuItems));

    // Remove dragged items from their current positions
    const removeDraggedItems = (items: MenuItem[]): MenuItem[] => {
      return items
        .filter((item) => !draggedItemIds.includes(item.id))
        .map((item) => ({
          ...item,
          children: item.children
            ? removeDraggedItems(item.children)
            : undefined,
        }));
    };

    let updatedMenuItems = removeDraggedItems(newMenuItems);

    // Insert dragged items at the new position
    if (target.targetType === "between-items") {
      // Dropping between items
      const parentId = target.parentItem;
      const childIndex = target.childIndex;

      if (parentId === "root") {
        // Insert at root level
        updatedMenuItems.splice(childIndex, 0, ...draggedItems);
      } else {
        // Insert in a specific parent's children
        const insertIntoParent = (items: MenuItem[]): MenuItem[] => {
          return items.map((item) => {
            if (item.id === parentId) {
              const children = item.children || [];
              children.splice(childIndex, 0, ...draggedItems);
              return { ...item, children };
            }
            return {
              ...item,
              children: item.children
                ? insertIntoParent(item.children)
                : undefined,
            };
          });
        };
        updatedMenuItems = insertIntoParent(updatedMenuItems);
      }
    } else if (target.targetType === "item" && target.targetItem) {
      // Dropping onto an item (make it a child)
      const targetItemId = String(target.targetItem);

      const insertAsChild = (items: MenuItem[]): MenuItem[] => {
        return items.map((item) => {
          if (item.id === targetItemId) {
            const children = item.children || [];
            return { ...item, children: [...children, ...draggedItems] };
          }
          return {
            ...item,
            children: item.children ? insertAsChild(item.children) : undefined,
          };
        });
      };
      updatedMenuItems = insertAsChild(updatedMenuItems);
    }

    onMenuReorder(updatedMenuItems);
  };

  const handleExpandAll = () => {
    const allFolderIds = Object.keys(treeItems).filter(
      (id) => treeItems[id].isFolder && id !== "root",
    );
    setExpandedItems(allFolderIds);
  };

  const handleCollapseAll = () => {
    setExpandedItems([]);
  };

  const renderVisibilityIndicator = (menuItem: MenuItem) => {
    if (menuItem.visible !== true) {
      return (
        <ViewOff
          size={16}
          className={classes.visibilityIcon}
          title={"Hidden from menu"}
        />
      );
    }
    return null;
  };

  const handleDeleteClick = (e: React.MouseEvent, menuItem: MenuItem) => {
    e.stopPropagation(); // Prevent tree item selection
    setItemToDelete(menuItem);
    setDeleteModalOpen(true);
  };

  const handleConfirmDelete = () => {
    if (itemToDelete && onDeleteItem) {
      onDeleteItem(itemToDelete.id);
    }
    setDeleteModalOpen(false);
    setItemToDelete(null);
  };

  const renderItemTitle = ({
    item,
    title,
  }: {
    item: TreeItem;
    title: string;
  }) => {
    const menuItem = item.data as MenuItem;
    const isHidden = menuItem.visible !== true;
    const isDefaultRoute = menuItem.defaultRoute === true;
    const isCustom = menuItem.custom === true;

    const handleEditClick = (e: React.MouseEvent) => {
      e.stopPropagation(); // Prevent tree item selection
      onItemSelect(menuItem);
    };

    return (
      <div
        className={`${classes.itemContent} ${isHidden ? classes.hiddenItem : ""}`}
      >
        <span
          className={`${classes.itemLabel} ${isHidden ? classes.hiddenLabel : ""}`}
        >
          {title}
        </span>
        {renderVisibilityIndicator(menuItem)}
        {isCustom && (
          <button
            className={classes.deleteButton}
            onClick={(e) => handleDeleteClick(e, menuItem)}
            title={t("Delete item")}
          >
            <TrashCan size={16} />
          </button>
        )}
        <button
          className={classes.editButton}
          onClick={handleEditClick}
          title={t("Edit item")}
        >
          <Edit size={16} />
        </button>
        {isDefaultRoute && (
          <Home
            size={14}
            className={classes.defaultRouteIcon}
            title={t("Default Route")}
          />
        )}
      </div>
    );
  };

  return (
    <div className={`${classes.treeContainer} ${isDark ? classes.dark : ""}`}>
      {/* Delete Confirmation Modal */}
      <Modal
        open={deleteModalOpen}
        modalHeading={t("Delete Menu Item")}
        primaryButtonText={t("Delete")}
        secondaryButtonText={t("Cancel")}
        onRequestClose={() => setDeleteModalOpen(false)}
        onRequestSubmit={handleConfirmDelete}
        danger
      >
        <p>{t("Are you sure you want to delete this menu item?")}</p>
      </Modal>

      {/* Expand/Collapse All Controls */}
      <div className={classes.treeControls}>
        <button
          className={classes.controlButton}
          onClick={handleExpandAll}
          title={t("Expand All")}
        >
          <ExpandAll size={16} />
          <span className={classes.controlLabel}>{t("Expand All")}</span>
        </button>
        <button
          className={classes.controlButton}
          onClick={handleCollapseAll}
          title={t("Collapse All")}
        >
          <CollapseAll size={16} />
          <span className={classes.controlLabel}>{t("Collapse All")}</span>
        </button>
        {onEnableAll && (
          <button
            className={classes.controlButton}
            onClick={onEnableAll}
            title={t("Enable All")}
          >
            <View size={16} />
            <span className={classes.controlLabel}>{t("Enable All")}</span>
          </button>
        )}
      </div>

      {/* Tree Content */}
      <div className={classes.treeContent}>
        <ControlledTreeEnvironment
          items={treeItems}
          getItemTitle={(item) => t(item.data.label)}
          canReorderItems={true}
          canDragAndDrop={true}
          canDropOnFolder={true}
          onDrop={handleDrop}
          viewState={{
            "menu-tree": {
              focusedItem,
              expandedItems,
              selectedItems,
            },
          }}
          onFocusItem={(item) => setFocusedItem(String(item.index))}
          onExpandItem={(item) =>
            setExpandedItems([...expandedItems, String(item.index)])
          }
          onCollapseItem={(item) =>
            setExpandedItems(
              expandedItems.filter(
                (expandedItemIndex) => expandedItemIndex !== String(item.index),
              ),
            )
          }
          renderItemTitle={renderItemTitle}
        >
          <Tree
            treeId="menu-tree"
            rootItem="root"
            treeLabel={t("Menu Manager Tree")}
          />
        </ControlledTreeEnvironment>
      </div>
    </div>
  );
};
