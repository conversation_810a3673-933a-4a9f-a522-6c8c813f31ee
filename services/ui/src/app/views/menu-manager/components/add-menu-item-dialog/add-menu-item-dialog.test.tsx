import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, userEvent } from "../../../../../test-utils";
import { AddMenuItemDialog } from "./add-menu-item-dialog";

// Mock the useCustomDashboards hook
const mockDashboards = [
  { id: "test-1", name: "Test Dashboard 1" },
  { id: "test-2", name: "Existing Dashboard" },
];

vi.mock("../../../../../app/config/hooks/use-config", () => ({
  useCustomDashboards: () => ({
    dashboards: mockDashboards,
    isLoading: false,
    error: null,
  }),
}));

// Mock Carbon React components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

describe("AddMenuItemDialog", () => {
  const mockOnClose = vi.fn();
  const mockOnAdd = vi.fn();
  const existingIds = ["existing-id", "another-id"];

  const defaultProps = {
    open: true,
    onClose: mockOnClose,
    onAdd: mockOnAdd,
    existingIds,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders modal when open", () => {
    render(<AddMenuItemDialog {...defaultProps} />);

    expect(screen.getByText("Add New View")).toBeInTheDocument();
    expect(screen.getByLabelText("View Name")).toBeInTheDocument();
    expect(screen.getByRole("combobox")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Add" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
  });

  it("handles modal open state correctly", () => {
    const { rerender } = render(
      <AddMenuItemDialog {...defaultProps} open={true} />,
    );

    // When open, input should be accessible
    expect(screen.getByLabelText("View Name")).toBeInTheDocument();

    // When closed, the modal should handle the state change
    rerender(<AddMenuItemDialog {...defaultProps} open={false} />);

    // The component should handle the closed state (Carbon Modal manages visibility)
    // We just verify the component doesn't crash and handles the prop change
    expect(true).toBe(true);
  });

  it("shows error when view name is empty", async () => {
    const user = userEvent.setup();
    render(<AddMenuItemDialog {...defaultProps} />);

    const addButton = screen.getByRole("button", { name: "Add" });
    await user.click(addButton);

    expect(screen.getByText("View name is required")).toBeInTheDocument();
    expect(mockOnAdd).not.toHaveBeenCalled();
  });

  it("shows error when dashboard name already exists", async () => {
    const user = userEvent.setup();
    render(<AddMenuItemDialog {...defaultProps} />);

    const input = screen.getByLabelText("View Name");
    await user.type(input, "Existing Dashboard");

    const addButton = screen.getByRole("button", { name: "Add" });
    await user.click(addButton);

    expect(
      screen.getByText("This view name is already in use"),
    ).toBeInTheDocument();
    expect(mockOnAdd).not.toHaveBeenCalled();
  });

  it("clears error when user types after error is shown", async () => {
    const user = userEvent.setup();
    render(<AddMenuItemDialog {...defaultProps} />);

    // First trigger an error
    const addButton = screen.getByRole("button", { name: "Add" });
    await user.click(addButton);
    expect(screen.getByText("View name is required")).toBeInTheDocument();

    // Then type something
    const input = screen.getByLabelText("View Name");
    await user.type(input, "New Dashboard");

    expect(screen.queryByText("View name is required")).not.toBeInTheDocument();
  });

  it("creates dashboard with correct properties", async () => {
    const user = userEvent.setup();
    render(<AddMenuItemDialog {...defaultProps} />);

    const input = screen.getByLabelText("View Name");
    await user.type(input, "My New Dashboard");

    const addButton = screen.getByRole("button", { name: "Add" });
    await user.click(addButton);

    expect(mockOnAdd).toHaveBeenCalledWith({
      id: "my-new-dashboard",
      label: "My New Dashboard",
      link: "/my-new-dashboard",
      viewType: "dashboard",
      viewConfigId: "dashboard-my-new-dashboard",
      visible: false,
      icon: "Dashboard",
      custom: true,
    });
  });

  it("sanitizes dashboard ID correctly", async () => {
    const user = userEvent.setup();
    render(<AddMenuItemDialog {...defaultProps} />);

    const input = screen.getByLabelText("View Name");
    await user.type(input, "My Special Dashboard! @#$%");

    const addButton = screen.getByRole("button", { name: "Add" });
    await user.click(addButton);

    expect(mockOnAdd).toHaveBeenCalledWith(
      expect.objectContaining({
        id: "my-special-dashboard-",
        link: "/my-special-dashboard-",
        viewConfigId: "dashboard-my-special-dashboard-",
      }),
    );
  });

  it("generates unique ID when base ID already exists", async () => {
    const user = userEvent.setup();
    const propsWithExistingId = {
      ...defaultProps,
      existingIds: [...existingIds, "existing-dashboard"],
    };

    render(<AddMenuItemDialog {...propsWithExistingId} />);

    const input = screen.getByLabelText("View Name");
    await user.type(input, "Existing Dashboard Name");

    const addButton = screen.getByRole("button", { name: "Add" });
    await user.click(addButton);

    // Should generate a unique ID by appending a number
    expect(mockOnAdd).toHaveBeenCalledWith(
      expect.objectContaining({
        id: "existing-dashboard-name",
        link: "/existing-dashboard-name",
        viewConfigId: "dashboard-existing-dashboard-name",
      }),
    );
  });

  it("handles multiple ID conflicts correctly", async () => {
    const user = userEvent.setup();
    const propsWithMultipleConflicts = {
      ...defaultProps,
      existingIds: [...existingIds, "test", "test-1", "test-2"],
    };

    render(<AddMenuItemDialog {...propsWithMultipleConflicts} />);

    const input = screen.getByLabelText("View Name");
    await user.type(input, "Test");

    const addButton = screen.getByRole("button", { name: "Add" });
    await user.click(addButton);

    // Should find the next available ID
    expect(mockOnAdd).toHaveBeenCalledWith(
      expect.objectContaining({
        id: "test-3",
        link: "/test-3",
        viewConfigId: "dashboard-test-3",
      }),
    );
  });

  it("calls onClose when Cancel button is clicked", async () => {
    const user = userEvent.setup();
    render(<AddMenuItemDialog {...defaultProps} />);

    const cancelButton = screen.getByRole("button", { name: "Cancel" });
    await user.click(cancelButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it("clears form and calls onClose after successful submission", async () => {
    const user = userEvent.setup();
    render(<AddMenuItemDialog {...defaultProps} />);

    const input = screen.getByLabelText("View Name");
    await user.type(input, "New Dashboard");

    const addButton = screen.getByRole("button", { name: "Add" });
    await user.click(addButton);

    expect(mockOnAdd).toHaveBeenCalled();
    expect(mockOnClose).toHaveBeenCalled();
  });

  it("resets form when handleClose is called", async () => {
    const user = userEvent.setup();
    render(<AddMenuItemDialog {...defaultProps} />);

    // Type something
    const input = screen.getByLabelText("View Name");
    await user.type(input, "Test Input");

    // Click cancel to trigger handleClose
    const cancelButton = screen.getByRole("button", { name: "Cancel" });
    await user.click(cancelButton);

    // Verify onClose was called (which would trigger form reset in parent)
    expect(mockOnClose).toHaveBeenCalled();
  });

  it("shows correct placeholder text", () => {
    render(<AddMenuItemDialog {...defaultProps} />);

    const input = screen.getByPlaceholderText("Enter view name");
    expect(input).toBeInTheDocument();
  });

  it("handles whitespace-only input correctly", async () => {
    const user = userEvent.setup();
    render(<AddMenuItemDialog {...defaultProps} />);

    const input = screen.getByLabelText("View Name");
    await user.type(input, "   ");

    const addButton = screen.getByRole("button", { name: "Add" });
    await user.click(addButton);

    expect(screen.getByText("View name is required")).toBeInTheDocument();
    expect(mockOnAdd).not.toHaveBeenCalled();
  });

  it("handles whitespace in view name correctly", async () => {
    const user = userEvent.setup();
    render(<AddMenuItemDialog {...defaultProps} />);

    const input = screen.getByLabelText("View Name");
    await user.type(input, "  Trimmed Dashboard  ");

    const addButton = screen.getByRole("button", { name: "Add" });
    await user.click(addButton);

    // The component uses the raw input value for label but sanitizes for ID
    expect(mockOnAdd).toHaveBeenCalledWith(
      expect.objectContaining({
        label: "  Trimmed Dashboard  ",
        id: "-trimmed-dashboard-",
      }),
    );
  });
});
