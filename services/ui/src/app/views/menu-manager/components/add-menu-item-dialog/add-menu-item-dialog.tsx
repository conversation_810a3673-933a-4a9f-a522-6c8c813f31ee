import { Modal, TextInput, ComboBox } from "@carbon/react";
import { useTranslation } from "react-i18next";
import { useState } from "react";
import { MenuItem } from "@ict/sdk/types";
import { useCustomDashboards } from "../../../../config/hooks/use-config";
import classes from "./add-menu-item-dialog.module.css";

interface AddMenuItemModalProps {
  open: boolean;
  onClose: () => void;
  onAdd: (item: MenuItem) => void;
  existingIds: string[];
}

const VIEW_TYPE_OPTIONS = [
  { id: "dashboard", text: "Dashboard" },
  { id: "iframe", text: "Iframe" },
];

export const AddMenuItemDialog = ({
  open,
  onClose,
  onAdd,
  existingIds,
}: AddMenuItemModalProps) => {
  const { t } = useTranslation();
  const [viewName, setViewName] = useState("");
  const [viewType, setViewType] = useState("dashboard");
  const [error, setError] = useState("");
  const { dashboards } = useCustomDashboards();

  // Helper function to sanitize ID (lowercase, replace spaces with dashes)
  const sanitizeId = (input: string): string => {
    return input
      .toLowerCase()
      .replace(/\s+/g, "-")
      .replace(/[^a-z0-9-]/g, "");
  };

  // Helper function to generate a unique ID
  const generateUniqueId = (baseId: string): string => {
    let id = baseId;
    let counter = 1;

    while (existingIds.includes(id)) {
      id = `${baseId}-${counter}`;
      counter++;
    }

    return id;
  };

  const handleSubmit = () => {
    if (!viewName.trim()) {
      setError(t("addViewModal.nameRequired", "View name is required"));
      return;
    }

    // Check if view name already exists (only for dashboards)
    if (viewType === "dashboard") {
      const isNameTaken = dashboards?.some(
        (dashboard) => dashboard.name === viewName.trim(),
      );

      if (isNameTaken) {
        setError(
          t("addViewModal.nameTaken", "This view name is already in use"),
        );
        return;
      }
    }

    const baseId = sanitizeId(viewName);
    const uniqueId = generateUniqueId(baseId);

    const newView: MenuItem = {
      id: uniqueId,
      label: viewName,
      link: `/${uniqueId}`,
      viewType,
      viewConfigId: `${viewType}-${uniqueId}`,
      visible: false,
      icon: viewType === "dashboard" ? "Dashboard" : "DashboardReference",
      custom: true,
    };

    onAdd(newView);
    handleClose();
  };

  const handleClose = () => {
    setViewName("");
    setViewType("dashboard");
    setError("");
    onClose();
  };

  return (
    <Modal
      open={open}
      modalHeading={"Add New View"}
      primaryButtonText={"Add"}
      secondaryButtonText={"Cancel"}
      onRequestSubmit={handleSubmit}
      onRequestClose={handleClose}
      className={classes.modal}
      passiveModal={false}
      size="sm"
    >
      <div className={classes.content}>
        <ComboBox
          id="view-type"
          titleText={t("addViewModal.typeLabel", "View Type")}
          items={VIEW_TYPE_OPTIONS}
          selectedItem={VIEW_TYPE_OPTIONS.find(
            (option) => option.id === viewType,
          )}
          onChange={({ selectedItem }) => {
            if (selectedItem) {
              setViewType(selectedItem.id);
            }
          }}
          itemToString={(item) => (item ? item.text : "")}
        />
        <TextInput
          id="view-name"
          labelText={t("addViewModal.nameLabel", "View Name")}
          value={viewName}
          onChange={(e) => {
            setViewName(e.target.value);
            setError("");
          }}
          placeholder={t("addViewModal.namePlaceholder", "Enter view name")}
          invalid={!!error}
          invalidText={error}
        />
      </div>
    </Modal>
  );
};
