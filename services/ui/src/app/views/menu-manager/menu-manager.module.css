/* Menu Manager styles will be added here */

.container {
  background: var(--cds-background);
  color: var(--cds-text-primary);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.container.dark {
  background: var(--cds-background);
  color: var(--cds-text-primary);
}

.grid {
  margin: 0;
  padding: 0;
  height: 100%;
  flex: 1;
}

.treeColumn {
  margin: 0;
  height: 100%;
}

.formColumn {
  margin: 0;
  margin-left: 1rem;
}

.treeContainer {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.formContainer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sectionTitle {
  margin-bottom: 1rem;
  color: var(--cds-text-primary);
}

.treeContainer > :nth-child(2),
.formContainer > :nth-child(2) {
  flex: 1;
  overflow: hidden;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #6f6f6f;
}

.content {
  display: flex;
  gap: 1rem;
  height: 100%;
}

.treePanel {
  flex: 1;
  min-width: 300px;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  height: 100%;
}

.formPanel {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  height: 100%;
}

/* Changes list styles */
.changesColumn {
  margin: 0;
  margin-left: 1rem;
  height: 100%;
}

.changesContainer {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: var(--cds-layer-01);
  border: 1px solid var(--cds-border-subtle-01);
  border-radius: var(--cds-border-radius);
  padding: 1rem;
}

.changesTitle {
  margin-bottom: 1rem;
  color: var(--cds-text-primary);
  font-size: 1.125rem;
}

.changesList {
  flex: 1;
  overflow-y: auto;
}

.beforeValue {
  color: var(--cds-text-secondary) !important;
}

.afterValue {
  color: var(--cds-text-primary) !important;
  font-weight: 500;
}
