import { <PERSON><PERSON><PERSON>, <PERSON>rid, Head<PERSON> } from "@carbon/react";
import { useTranslation } from "react-i18next";
import { useState, useEffect } from "react";
import { useTheme, ThemeMode } from "../../layout/theme";
import {
  useConfigSetting,
  updateConfigSetting,
} from "../../config/hooks/use-config";
import { MenuTree } from "./components/menu-tree/menu-tree";
import { ViewBar } from "../../components/view-bar/view-bar";
import { FullPageContainer } from "../../components/full-page-container/full-page-container";
import {
  Button,
  Tag,
  Table,
  TableHead,
  TableRow,
  TableHeader,
  TableBody,
  TableCell,
} from "@carbon/react";
import { Add } from "@carbon/icons-react";
import classes from "./menu-manager.module.css";
import { MenuConfig, MenuItem } from "@ict/sdk/types";
import { MenuItemDetailsDrawer } from "./components/menu-item-details-drawer/menu-item-details-drawer";
import { AddMenuItemDialog } from "./components/add-menu-item-dialog/add-menu-item-dialog";
import { useNotification } from "../../components/toast/use-notification";

interface MenuItemChange {
  id: string;
  timestamp: Date;
  itemName: string;
  property: string;
  beforeValue: unknown;
  afterValue: unknown;
}

interface MenuValidationResult {
  valid: boolean;
  errorMessage?: string;
}

export function MenuManager() {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === ThemeMode.DARK;
  const { setting: menuSetting, isLoading, error } = useConfigSetting("menu");
  const { success, error: showError } = useNotification();
  const [selectedMenuItem, setSelectedMenuItem] = useState<MenuItem | null>(
    null,
  );
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [isDirty, setIsDirty] = useState(false);
  const [drawerOpened, setDrawerOpened] = useState(false);
  const [addModalOpen, setAddModalOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [menuChanges, setMenuChanges] = useState<MenuItemChange[]>([]);

  // Extract menu items from the setting value and update local state when setting changes
  const settingMenuItems = menuSetting?.value as MenuConfig;

  // Initialize menuItems when setting loads or changes
  // NOTE: We use the raw menu items here without filtering so we can see hidden items
  useEffect(() => {
    if (settingMenuItems.length > 0 && !isDirty) {
      // Add isTop property to top-level items only when loading from settings
      const itemsWithTopFlag = settingMenuItems.map((item) => ({
        ...item,
        isTop: true,
      }));
      setMenuItems(itemsWithTopFlag);
    }
  }, [settingMenuItems, isDirty]);

  // Helper function to get all menu item IDs recursively
  const getAllMenuItemIds = (items: MenuItem[]): string[] => {
    const ids: string[] = [];
    for (const item of items) {
      ids.push(item.id);
      if (item.children) {
        ids.push(...getAllMenuItemIds(item.children));
      }
    }
    return ids;
  };

  // Helper function to find a menu item by ID recursively
  const findMenuItemById = (items: MenuItem[], id: string): MenuItem | null => {
    for (const item of items) {
      if (item.id === id) {
        return item;
      }
      if (item.children) {
        const found = findMenuItemById(item.children, id);
        if (found) return found;
      }
    }
    return null;
  };

  // Helper function to compare objects and find changed properties
  const findChangedProperties = (
    oldItem: MenuItem,
    newItem: MenuItem,
  ): Array<{ property: string; beforeValue: unknown; afterValue: unknown }> => {
    const changes: Array<{
      property: string;
      beforeValue: unknown;
      afterValue: unknown;
    }> = [];

    // Compare specific properties we care about
    const propertiesToCheck = [
      "label",
      "visible",
      "icon",
      "link",
      "viewType",
      "viewConfigId",
      "roles",
      "helpLink",
    ];

    propertiesToCheck.forEach((prop) => {
      if (oldItem[prop as keyof MenuItem] !== newItem[prop as keyof MenuItem]) {
        changes.push({
          property: prop,
          beforeValue: oldItem[prop as keyof MenuItem],
          afterValue: newItem[prop as keyof MenuItem],
        });
      }
    });

    return changes;
  };

  const handleItemSelect = (item: MenuItem) => {
    setSelectedMenuItem(item);
    setDrawerOpened(true);
  };

  const handleItemChange = (updatedItem: MenuItem) => {
    setSelectedMenuItem(updatedItem);

    // Find the original item to compare changes
    const originalItem = findMenuItemById(menuItems, updatedItem.id);

    if (originalItem) {
      // Find what properties changed
      const changedProperties = findChangedProperties(
        originalItem,
        updatedItem,
      );

      // Update or add change records for each changed property
      setMenuChanges((prev) => {
        const updatedChanges = [...prev];

        changedProperties.forEach((change) => {
          // Look for existing change record for this item and property
          const existingChangeIndex = updatedChanges.findIndex(
            (existingChange) =>
              existingChange.id.startsWith(
                `${updatedItem.id}-${change.property}-`,
              ),
          );

          const changeRecord: MenuItemChange = {
            id: `${updatedItem.id}-${change.property}-${Date.now()}`,
            timestamp: new Date(),
            itemName: String(
              updatedItem.label || originalItem.label || "Unnamed Item",
            ),
            property: change.property,
            beforeValue: change.beforeValue,
            afterValue: change.afterValue,
          };

          if (existingChangeIndex !== -1) {
            // Update existing change record, but keep the original beforeValue
            updatedChanges[existingChangeIndex] = {
              ...changeRecord,
              beforeValue: updatedChanges[existingChangeIndex].beforeValue,
            };
          } else {
            // Add new change record
            updatedChanges.push(changeRecord);
          }
        });

        return updatedChanges;
      });
    }

    // Update the menu items array with the changed item
    const updatedMenuItems = updateMenuItemInTree(menuItems, updatedItem);
    setMenuItems(updatedMenuItems);
    setIsDirty(true);
  };

  const handleDrawerClose = () => {
    setDrawerOpened(false);
  };

  const handleAddView = () => {
    setAddModalOpen(true);
  };

  const handleAddModalClose = () => {
    setAddModalOpen(false);
  };

  const handleAddModalSubmit = (newView: MenuItem) => {
    // Add the new view to the end of the menu items with isTop flag
    const newItem = {
      ...newView,
      isTop: true,
    };
    const updatedMenuItems = [...menuItems, newItem];
    setMenuItems(updatedMenuItems);
    setSelectedMenuItem(newItem);
    setDrawerOpened(true);
    setIsDirty(true);
  };

  const handleMenuReorder = (reorderedMenuItems: MenuItem[]) => {
    // Ensure isTop flag is preserved when reordering
    const itemsWithTopFlag = reorderedMenuItems.map((item) => ({
      ...item,
      isTop: true,
    }));
    setMenuItems(itemsWithTopFlag);
    setIsDirty(true);
  };

  const handleDeleteItem = (itemId: string) => {
    // Helper function to recursively remove an item from the tree
    const removeItemFromTree = (items: MenuItem[]): MenuItem[] => {
      return items
        .filter((item) => item.id !== itemId)
        .map((item) => ({
          ...item,
          children: item.children
            ? removeItemFromTree(item.children)
            : undefined,
        }));
    };

    const updatedMenuItems = removeItemFromTree(menuItems);
    setMenuItems(updatedMenuItems);
    setIsDirty(true);

    // If the deleted item was selected, close the drawer
    if (selectedMenuItem?.id === itemId) {
      setSelectedMenuItem(null);
      setDrawerOpened(false);
    }
  };

  const handleEnableAll = () => {
    // Helper function to recursively set visible to true for all menu items
    const enableAllItems = (items: MenuItem[]): MenuItem[] => {
      return items.map((item) => ({
        ...item,
        visible: true,
        children: item.children ? enableAllItems(item.children) : undefined,
      }));
    };

    const updatedMenuItems = enableAllItems(menuItems);
    setMenuItems(updatedMenuItems);
    setIsDirty(true);

    // Update the selected menu item if it's affected
    if (selectedMenuItem) {
      const updatedSelectedItem = { ...selectedMenuItem, visible: true };
      setSelectedMenuItem(updatedSelectedItem);
    }
  };

  const handleSaveOptions = async () => {
    if (!menuSetting) {
      console.error("No menu setting available to save");
      return;
    }

    const updatedSetting = {
      ...menuSetting,
      value: menuItems,
    };

    const isMenuValid = validateMenuAllowedParents(updatedSetting.value);
    if (isMenuValid.valid === false) {
      showError(
        t(
          "menuManager.failedToSaveConfigurationValidation",
          "Failed to save menu configuration due to failed menu validation: {{error}}",
          {
            error: isMenuValid.errorMessage,
          },
        ),
      );
      return;
    }

    setIsSaving(true);

    try {
      console.log(
        "MenuManager::handleSaveOptions - updatedSetting",
        updatedSetting,
      );

      await updateConfigSetting(updatedSetting, "facility");
      setIsDirty(false);
      setMenuChanges([]);
      success(t("Menu configuration has been saved successfully"));
    } catch (error) {
      console.error("Failed to save menu configuration:", error);
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      showError(
        t(
          "menuManager.failedToSaveConfiguration",
          "Failed to save menu configuration: {{error}}",
          {
            error: errorMessage,
          },
        ),
      );
    } finally {
      setIsSaving(false);
    }
  };

  const validateMenuAllowedParents = (newMenu: MenuItem[]) => {
    const childAllowedParentsMap = generateMap(settingMenuItems);
    return recursivelyValidateMenuStructure(
      newMenu,
      null,
      childAllowedParentsMap,
    );
  };

  const recursivelyValidateMenuStructure = (
    menu: MenuItem[],
    parentId: string | null,
    allowedParentIdsMap: Map<string, string[]>,
  ): MenuValidationResult => {
    for (const item of menu) {
      const itemAllowedParents = allowedParentIdsMap.get(item.id);

      if (itemAllowedParents) {
        if (parentId !== null) {
          if (itemAllowedParents.length === 0) {
            return {
              valid: false,
              errorMessage: `item ${item.id} must be at the root level but has parent ${parentId}`,
            };
          } else if (!itemAllowedParents.includes(parentId)) {
            return {
              valid: false,
              errorMessage: `Child ${item.id} is not assignable to parent ${parentId}`,
            };
          }
        } else if (itemAllowedParents.length > 0) {
          return {
            valid: false,
            errorMessage: `Item ${item.id} requires a parent from ${itemAllowedParents}`,
          };
        }
      }

      if (item.children && item.children.length > 0) {
        const childrenValid = recursivelyValidateMenuStructure(
          item.children,
          item.id,
          allowedParentIdsMap,
        );
        if (childrenValid && childrenValid.valid === false) {
          return childrenValid;
        }
      }
    }
    return {
      valid: true,
    };
  };

  const generateMap = (menu: MenuItem[]) => {
    const map = new Map<string, string[]>();

    const recursiveMapper = (menu: MenuItem[]) => {
      for (const item of menu) {
        if (item.allowedParentIds) {
          map.set(item.id, item.allowedParentIds);
        }
        if (item.children) {
          recursiveMapper(item.children);
        }
      }
    };

    recursiveMapper(menu);

    return map;
  };

  // Helper function to recursively update a menu item in the tree structure
  const updateMenuItemInTree = (
    items: MenuItem[],
    updatedItem: MenuItem,
  ): MenuItem[] => {
    return items.map((item) => {
      if (item.id === updatedItem.id) {
        return updatedItem;
      }
      if (item.children) {
        return {
          ...item,
          children: updateMenuItemInTree(item.children, updatedItem),
        };
      }
      return item;
    });
  };

  const formatValue = (value: unknown): string => {
    if (typeof value === "boolean") {
      return value ? "true" : "false";
    }
    if (value === null || value === undefined) {
      return "null";
    }
    if (Array.isArray(value)) {
      return value.length > 0 ? value.join(", ") : "none";
    }
    return String(value);
  };

  if (isLoading) {
    return (
      <div
        style={{
          flex: 1,
          width: "100%",
          height: "100%",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <ViewBar title={t("Menu Manager")} showDatePeriodRange={false} />
        <FullPageContainer>
          <div className={`${classes.container} ${isDark ? classes.dark : ""}`}>
            <Heading>{t("Loading...")}</Heading>
          </div>
        </FullPageContainer>
      </div>
    );
  }

  if (error) {
    return (
      <div
        style={{
          flex: 1,
          width: "100%",
          height: "100%",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <ViewBar title={t("Menu Manager")} showDatePeriodRange={false} />
        <FullPageContainer>
          <div className={`${classes.container} ${isDark ? classes.dark : ""}`}>
            <Heading>{t("Error loading menu configuration")}</Heading>
            <p>Error: {String(error)}</p>
          </div>
        </FullPageContainer>
      </div>
    );
  }

  if (!menuSetting) {
    return (
      <div
        style={{
          flex: 1,
          width: "100%",
          height: "100%",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <ViewBar title={t("Menu Manager")} showDatePeriodRange={false} />
        <FullPageContainer>
          <div className={`${classes.container} ${isDark ? classes.dark : ""}`}>
            <Heading>{t("Menu configuration not found")}</Heading>
            <p>{t("No menu setting found in the configuration")}</p>
          </div>
        </FullPageContainer>
      </div>
    );
  }

  return (
    <div
      style={{
        flex: 1,
        width: "100%",
        height: "100%",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <ViewBar
        title={t("Menu Manager")}
        showDatePeriodRange={false}
        showSave={true}
        saveEnabled={isDirty && !isSaving}
        onSaveClick={handleSaveOptions}
      >
        <Button
          kind="tertiary"
          size="sm"
          renderIcon={Add}
          onClick={handleAddView}
        >
          {t("Add View")}
        </Button>
      </ViewBar>
      <FullPageContainer>
        <div className={`${classes.container} ${isDark ? classes.dark : ""}`}>
          <Grid className={classes.grid} fullWidth>
            <Column sm={4} md={8} lg={6} className={classes.treeColumn}>
              <div className={classes.treeContainer}>
                {menuItems.length > 0 ? (
                  <MenuTree
                    menuItems={menuItems}
                    selectedItemId={selectedMenuItem?.id}
                    onItemSelect={handleItemSelect}
                    onMenuReorder={handleMenuReorder}
                    onDeleteItem={handleDeleteItem}
                    onEnableAll={handleEnableAll}
                  />
                ) : (
                  <p>{t("No menu items found")}</p>
                )}
              </div>
            </Column>
            {menuChanges.length > 0 && (
              <Column sm={4} md={8} lg={10} className={classes.changesColumn}>
                <div className={classes.container}>
                  <Heading className={classes.changesTitle}>
                    {t("Unsaved Changes")} ({menuChanges.length})
                  </Heading>
                  <div className={classes.changesList}>
                    <Table size="sm" useZebraStyles>
                      <TableHead>
                        <TableRow>
                          <TableHeader>{t("Menu Name")}</TableHeader>
                          <TableHeader>{t("Property Updated")}</TableHeader>
                          <TableHeader>{t("Old Value")}</TableHeader>
                          <TableHeader>{t("New Value")}</TableHeader>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {menuChanges
                          .slice(-10)
                          .reverse()
                          .map((change) => (
                            <TableRow key={change.id}>
                              <TableCell>{t(change.itemName)}</TableCell>
                              <TableCell>
                                <Tag type="blue" size="sm">
                                  {change.property}
                                </Tag>
                              </TableCell>
                              <TableCell className={classes.beforeValue}>
                                {formatValue(change.beforeValue)}
                              </TableCell>
                              <TableCell className={classes.afterValue}>
                                {formatValue(change.afterValue)}
                              </TableCell>
                            </TableRow>
                          ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </Column>
            )}
          </Grid>
        </div>
      </FullPageContainer>

      {/* Menu Item Details Drawer */}
      {selectedMenuItem && (
        <MenuItemDetailsDrawer
          opened={drawerOpened}
          onClose={handleDrawerClose}
          menuItem={selectedMenuItem}
          onItemChange={handleItemChange}
          existingIds={getAllMenuItemIds(menuItems)}
        />
      )}

      {/* Add View Modal */}
      <AddMenuItemDialog
        open={addModalOpen}
        onClose={handleAddModalClose}
        onAdd={handleAddModalSubmit}
        existingIds={getAllMenuItemIds(menuItems)}
      />
    </div>
  );
}

export default MenuManager;
