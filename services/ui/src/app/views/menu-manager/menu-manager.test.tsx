import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, userEvent, waitFor } from "../../../test-utils";
import { MenuManager } from "./menu-manager";
import { MenuItem } from "@ict/sdk/types";
import {
  useConfigSetting,
  updateConfigSetting,
} from "../../config/hooks/use-config";
import { MenuTree } from "./components/menu-tree/menu-tree";

// Mock the hooks
vi.mock("../../config/hooks/use-config", () => ({
  useConfigSetting: vi.fn(),
  updateConfigSetting: vi.fn(),
}));

// Mock Carbon React components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

// Mock the child components
vi.mock("./components/menu-tree/menu-tree", () => ({
  MenuTree: vi.fn(),
}));

vi.mock(
  "./components/menu-item-details-drawer/menu-item-details-drawer",
  () => ({
    MenuItemDetailsDrawer: ({ opened, menuItem, onItemChange }: any) =>
      opened ? (
        <div data-testid="menu-item-drawer">
          <div>Editing: {menuItem?.label}</div>
          <button
            data-testid="change-help-link"
            onClick={() =>
              onItemChange({ ...menuItem, helpLink: "/new-help-link" })
            }
          >
            Change Help Link
          </button>
          <button
            data-testid="change-label"
            onClick={() => onItemChange({ ...menuItem, label: "New Label" })}
          >
            Change Label
          </button>
          <button
            data-testid="change-help-link-again"
            onClick={() =>
              onItemChange({ ...menuItem, helpLink: "/another-help-link" })
            }
          >
            Change Help Link Again
          </button>
        </div>
      ) : null,
  }),
);

vi.mock("./components/add-menu-item-dialog/add-menu-item-dialog", () => ({
  AddMenuItemDialog: ({ open }: any) =>
    open ? <div data-testid="add-dialog">Add Dialog</div> : null,
}));

// Mock other dependencies
vi.mock("../../components/view-bar/view-bar", () => ({
  ViewBar: ({ children, title, showSave, saveEnabled, onSaveClick }: any) => (
    <div data-testid="view-bar">
      <h1>{title}</h1>
      {showSave && (
        <button
          onClick={onSaveClick}
          disabled={!saveEnabled}
          data-testid="save-button"
        >
          Save
        </button>
      )}
      {children}
    </div>
  ),
}));

vi.mock("../../components/full-page-container/full-page-container", () => ({
  FullPageContainer: ({ children }: any) => (
    <div data-testid="full-page-container">{children}</div>
  ),
}));

const mockUseNotificationSuccess = vi.fn();
const mockUseNotificationError = vi.fn();

vi.mock("../../components/toast/use-notification", () => ({
  useNotification: () => ({
    success: mockUseNotificationSuccess,
    error: mockUseNotificationError,
  }),
}));

describe("MenuManager - Unsaved Changes", () => {
  const mockUseConfigSetting = vi.mocked(useConfigSetting);
  const mockUpdateConfigSetting = vi.mocked(updateConfigSetting);

  const mockMenuItems: MenuItem[] = [
    {
      id: "outbound-overview",
      label: "Outbound Overview",
      link: "/outbound-overview",
      viewType: "dashboard",
      viewConfigId: "dashboard-outbound-overview",
      visible: true,
      icon: "Dashboard",
      custom: true,
      roles: ["ct_engineers"],
      isTop: true,
      helpLink: "/original-help-link",
    },
    {
      id: "inventory",
      label: "Inventory",
      link: "/inventory",
      viewType: "dashboard",
      viewConfigId: "dashboard-inventory",
      visible: true,
      icon: "Inventory",
      custom: true,
      roles: ["ct_operators"],
      isTop: true,
    },
  ];

  const mockMenuSetting = {
    id: "menu-config",
    name: "Menu Configuration",
    group: null,
    dataType: "json" as const,
    value: mockMenuItems,
    source: "facility" as const,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseConfigSetting.mockReturnValue({
      setting: mockMenuSetting,
      isLoading: false,
      error: null,
    });
    mockUpdateConfigSetting.mockResolvedValue(mockMenuSetting);

    const mockedMenuTree = vi.mocked(MenuTree);
    mockedMenuTree.mockImplementation(({ menuItems, onItemSelect }: any) => (
      <div data-testid="menu-tree">
        {menuItems.map((item: MenuItem) => (
          <button
            key={item.id}
            data-testid={`menu-item-${item.id}`}
            onClick={() => onItemSelect(item)}
          >
            {item.label}
          </button>
        ))}
      </div>
    ));
  });

  it("tracks helpLink changes in unsaved changes list", async () => {
    const user = userEvent.setup();
    render(<MenuManager />);

    // Select a menu item
    await user.click(screen.getByTestId("menu-item-outbound-overview"));

    // Change the help link
    await user.click(screen.getByTestId("change-help-link"));

    // Check that unsaved changes are displayed
    await waitFor(() => {
      expect(screen.getByText(/Unsaved Changes/)).toBeInTheDocument();
    });

    // Check that helpLink change is tracked
    expect(screen.getByText("helpLink")).toBeInTheDocument();
    expect(screen.getByText("/original-help-link")).toBeInTheDocument();
    expect(screen.getByText("/new-help-link")).toBeInTheDocument();
  });

  it("consolidates multiple changes to the same property", async () => {
    const user = userEvent.setup();
    render(<MenuManager />);

    // Select a menu item
    await user.click(screen.getByTestId("menu-item-outbound-overview"));

    // Change the help link multiple times
    await user.click(screen.getByTestId("change-help-link"));
    await user.click(screen.getByTestId("change-help-link-again"));

    // Check that only one change is shown for helpLink
    await waitFor(() => {
      expect(screen.getByText(/Unsaved Changes \(1\)/)).toBeInTheDocument();
    });

    // Check that it shows the original value to the final value
    expect(screen.getByText("/original-help-link")).toBeInTheDocument();
    expect(screen.getByText("/another-help-link")).toBeInTheDocument();
    expect(screen.queryByText("/new-help-link")).not.toBeInTheDocument();
  });

  it("preserves original beforeValue when updating existing change record", async () => {
    const user = userEvent.setup();
    render(<MenuManager />);

    // Select a menu item
    await user.click(screen.getByTestId("menu-item-outbound-overview"));

    // Make first change
    await user.click(screen.getByTestId("change-help-link"));

    // Verify first change shows original value
    await waitFor(() => {
      expect(screen.getByText("/original-help-link")).toBeInTheDocument();
      expect(screen.getByText("/new-help-link")).toBeInTheDocument();
    });

    // Make second change to same property
    await user.click(screen.getByTestId("change-help-link-again"));

    // Verify it still shows original value, not the intermediate value
    await waitFor(() => {
      expect(screen.getByText("/original-help-link")).toBeInTheDocument();
      expect(screen.getByText("/another-help-link")).toBeInTheDocument();
      expect(screen.queryByText("/new-help-link")).not.toBeInTheDocument();
    });
  });

  it("tracks multiple different properties separately", async () => {
    const user = userEvent.setup();
    render(<MenuManager />);

    // Select a menu item
    await user.click(screen.getByTestId("menu-item-outbound-overview"));

    // Change different properties
    await user.click(screen.getByTestId("change-help-link"));
    await user.click(screen.getByTestId("change-label"));

    // Check that both changes are tracked separately
    await waitFor(() => {
      expect(screen.getByText(/Unsaved Changes \(2\)/)).toBeInTheDocument();
    });

    expect(screen.getByText("helpLink")).toBeInTheDocument();
    expect(screen.getByText("label")).toBeInTheDocument();
  });

  it("shows correct before and after values for different property types", async () => {
    const user = userEvent.setup();
    render(<MenuManager />);

    // Select a menu item
    await user.click(screen.getByTestId("menu-item-outbound-overview"));

    // Change label (string property)
    await user.click(screen.getByTestId("change-label"));

    // Check string values are displayed correctly in the table
    await waitFor(() => {
      // Check that the old value is in the beforeValue column
      const beforeValueCell = document.querySelector("._beforeValue_192e10");
      expect(beforeValueCell).toHaveTextContent("Outbound Overview");

      // Check that the new value is in the afterValue column
      const afterValueCell = document.querySelector("._afterValue_192e10");
      expect(afterValueCell).toHaveTextContent("New Label");
    });
  });

  it("clears unsaved changes after successful save", async () => {
    const user = userEvent.setup();
    mockUpdateConfigSetting.mockResolvedValue(mockMenuSetting);

    render(<MenuManager />);

    // Make a change
    await user.click(screen.getByTestId("menu-item-outbound-overview"));
    await user.click(screen.getByTestId("change-help-link"));

    // Verify changes are shown
    await waitFor(() => {
      expect(screen.getByText(/Unsaved Changes/)).toBeInTheDocument();
    });

    // Find and click save button (it should be in the ViewBar)
    const saveButton = screen.getByTestId("save-button");
    expect(saveButton).toBeInTheDocument();
    await user.click(saveButton);

    // Verify changes are cleared after save
    await waitFor(() => {
      expect(screen.queryByText(/Unsaved Changes/)).not.toBeInTheDocument();
    });
  });

  it("shows 'Not Implemented' for null/undefined values", () => {
    const itemWithNullValues: MenuItem = {
      id: "test-item",
      label: "Test Item",
      link: "/test",
      viewType: "dashboard",
      visible: true,
      helpLink: undefined,
    };

    mockUseConfigSetting.mockReturnValue({
      setting: { ...mockMenuSetting, value: [itemWithNullValues] },
      isLoading: false,
      error: null,
    });

    render(<MenuManager />);

    // The formatValue function should handle null/undefined values
    // This is tested indirectly through the component behavior
    expect(screen.getByTestId("menu-tree")).toBeInTheDocument();
  });
});

describe("Menu setting validation on save", () => {
  const mockUseConfigSetting = vi.mocked(useConfigSetting);
  const mockUpdateConfigSetting = vi.mocked(updateConfigSetting);

  const mockMenuItems: MenuItem[] = [
    {
      id: "outbound-overview",
      label: "Outbound Overview",
      children: [
        {
          id: "example-1",
          label: "Example 1",
          allowedParentIds: ["outbound-overview"],
        },
      ],
    },
    {
      id: "inventory",
      label: "Inventory",
      allowedParentIds: [],
    },
  ];

  const mockMenuSetting = {
    id: "menu-config",
    name: "Menu Configuration",
    group: null,
    dataType: "json" as const,
    value: mockMenuItems,
    source: "facility" as const,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseConfigSetting.mockReturnValue({
      setting: mockMenuSetting,
      isLoading: false,
      error: null,
    });
    mockUpdateConfigSetting.mockResolvedValue(mockMenuSetting);
  });

  it("should not allow item to be moved to another parent not in allowedParentIds", async () => {
    const invalidMenuStructure: MenuItem[] = [
      {
        id: "outbound-overview",
        label: "Outbound Overview",
      },
      {
        id: "inventory",
        label: "Inventory",
        allowedParentIds: [],
        children: [
          {
            id: "example-1",
            label: "Example 1",
            allowedParentIds: ["outbound-overview"],
          },
        ],
      },
    ];

    const mockedMenuTree = vi.mocked(MenuTree);
    mockedMenuTree.mockImplementation(
      ({ menuItems, onItemSelect, onMenuReorder }: any) => (
        <div data-testid="menu-tree">
          {menuItems.map((item: MenuItem) => (
            <button
              key={item.id}
              data-testid={`menu-item-${item.id}`}
              onClick={() => onItemSelect(item)}
            >
              {item.label}
            </button>
          ))}
          <button
            data-testid="menu-reorder-button"
            onClick={() => {
              onMenuReorder(invalidMenuStructure);
            }}
          >
            Reorder
          </button>
        </div>
      ),
    );

    const user = userEvent.setup();
    render(<MenuManager />);

    await user.click(screen.getByTestId("menu-reorder-button"));
    await waitFor(() => {
      const saveButton = screen.getByTestId("save-button");
      expect(saveButton).toBeInTheDocument();
    });

    const reoderButton = screen.getByTestId("menu-reorder-button");
    await user.click(reoderButton);

    const saveButton = screen.getByTestId("save-button");
    await user.click(saveButton as HTMLElement);

    await waitFor(() => {
      expect(mockUseNotificationError).toBeCalled();
      expect(mockUseNotificationSuccess).not.toBeCalled();
    });
  });

  it("should not allow item to be nested if allowedParentIds property is empty array", async () => {
    const invalidMenuStructure: MenuItem[] = [
      {
        id: "outbound-overview",
        label: "Outbound Overview",
        children: [
          {
            id: "example-1",
            label: "Example 1",
            allowedParentIds: ["outbound-overview"],
          },
          {
            id: "inventory",
            label: "Inventory",
            allowedParentIds: [],
          },
        ],
      },
    ];

    const mockedMenuTree = vi.mocked(MenuTree);
    mockedMenuTree.mockImplementation(
      ({ menuItems, onItemSelect, onMenuReorder }: any) => (
        <div data-testid="menu-tree">
          {menuItems.map((item: MenuItem) => (
            <button
              key={item.id}
              data-testid={`menu-item-${item.id}`}
              onClick={() => onItemSelect(item)}
            >
              {item.label}
            </button>
          ))}
          <button
            data-testid="menu-reorder-button"
            onClick={() => {
              onMenuReorder(invalidMenuStructure);
            }}
          >
            Reorder
          </button>
        </div>
      ),
    );

    const user = userEvent.setup();
    render(<MenuManager />);

    await user.click(screen.getByTestId("menu-reorder-button"));
    await waitFor(() => {
      const saveButton = screen.getByTestId("save-button");
      expect(saveButton).toBeInTheDocument();
    });

    const reoderButton = screen.getByTestId("menu-reorder-button");
    await user.click(reoderButton);

    const saveButton = screen.getByTestId("save-button");
    await user.click(saveButton as HTMLElement);

    await waitFor(() => {
      expect(mockUseNotificationError).toBeCalled();
      expect(mockUseNotificationSuccess).not.toBeCalled();
    });
  });
  it("should not allow item to be at root level if allowedParentIds specified with non-empty array", async () => {
    const invalidMenuStructure: MenuItem[] = [
      {
        id: "outbound-overview",
        label: "Outbound Overview",
      },
      {
        id: "inventory",
        label: "Inventory",
        allowedParentIds: [],
      },
      {
        id: "example-1",
        label: "Example 1",
        allowedParentIds: ["outbound-overview"],
      },
    ];

    const mockedMenuTree = vi.mocked(MenuTree);
    mockedMenuTree.mockImplementation(
      ({ menuItems, onItemSelect, onMenuReorder }: any) => (
        <div data-testid="menu-tree">
          {menuItems.map((item: MenuItem) => (
            <button
              key={item.id}
              data-testid={`menu-item-${item.id}`}
              onClick={() => onItemSelect(item)}
            >
              {item.label}
            </button>
          ))}
          <button
            data-testid="menu-reorder-button"
            onClick={() => {
              onMenuReorder(invalidMenuStructure);
            }}
          >
            Reorder
          </button>
        </div>
      ),
    );

    const user = userEvent.setup();
    render(<MenuManager />);

    await user.click(screen.getByTestId("menu-reorder-button"));
    await waitFor(() => {
      const saveButton = screen.getByTestId("save-button");
      expect(saveButton).toBeInTheDocument();
    });

    const reoderButton = screen.getByTestId("menu-reorder-button");
    await user.click(reoderButton);

    const saveButton = screen.getByTestId("save-button");
    await user.click(saveButton as HTMLElement);

    await waitFor(() => {
      expect(mockUseNotificationError).toBeCalled();
      expect(mockUseNotificationSuccess).not.toBeCalled();
    });
  });
});
