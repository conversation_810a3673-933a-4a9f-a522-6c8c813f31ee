import { vi } from "vitest";
import { fireEvent, render, screen, waitFor } from "../../../test-utils";
import { ictApi } from "../../api/ict-api";
import { DatePeriod } from "../../types";
import { DailyPerformanceReport } from "./daily-performance-report";

// Mock the config hooks
vi.mock("../../config/hooks/use-config", () => ({
  useConfig: () => ({
    data: [],
    isLoading: false,
    error: null,
  }),
  useConfigSetting: () => ({
    setting: null,
    isLoading: false,
    error: null,
  }),
  useFeatureFlag: () => ({
    enabled: false,
    isLoading: false,
    error: null,
  }),
  useFeatureFlags: () => ({
    featureFlags: [],
    isLoading: false,
    error: null,
  }),
}));

// Mock the auth hooks
vi.mock("../../auth/hooks/use-roles", () => ({
  useRoles: () => ({
    roles: [],
    hasRole: () => false,
    hasAnyRole: () => false,
    isLoading: false,
  }),
}));

// Mock the auth0 client
vi.mock("@auth0/auth0-react", () => ({
  useAuth0: () => ({
    isAuthenticated: true,
    user: { name: "Test User" },
    getIdTokenClaims: vi.fn().mockResolvedValue({
      "https://ict.dematic.cloud/roles": [],
    }),
  }),
}));

// Mock the useSafeLocation hook
vi.mock("../../hooks/use-safe-location", () => ({
  useSafeLocation: () => ({
    pathname: "/daily-performance-report",
    search: "",
    hash: "",
    state: null,
  }),
}));

// Mock the use-favorites hook
vi.mock("../../config/hooks/use-favorites", () => ({
  useFavorites: () => ({
    favorites: [],
    isLoading: false,
    error: null,
    addFavorite: vi.fn(),
    removeFavorite: vi.fn(),
    isFavorite: () => false,
  }),
}));

// Mock the use-menu hook
vi.mock("../../config/menu/use-menu", () => ({
  useMenu: () => ({
    menuItems: [],
    isLoading: false,
  }),
}));

// Mock the use-dates hook
vi.mock("../../hooks/use-dates", () => ({
  useDates: () => ({
    startDate: new Date("2023-01-01"),
    endDate: new Date("2023-01-07"),
  }),
}));

// Mock the use-api-error-state hook
vi.mock("../../hooks/use-api-error-state", () => ({
  useApiErrorState: () => false,
}));

// Mock dependencies
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

vi.mock("xlsx", () => ({
  utils: {
    json_to_sheet: vi.fn(),
    book_new: vi.fn(),
    book_append_sheet: vi.fn(),
  },
  writeFile: vi.fn(),
}));

vi.mock("../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: vi.fn(),
    },
  },
}));

vi.mock("./components/workstation-filter/workstation-filter", () => ({
  WorkstationFilter: ({
    onChange,
  }: {
    onChange: (workstations: string[]) => void;
  }) => (
    <button
      data-testid="workstation-filter"
      onClick={() => onChange(["WS1", "WS2"])}
      type="button"
    >
      Select Workstations
    </button>
  ),
}));

// Mock the daily performance report table
vi.mock(
  "./components/daily-performance-report-table/daily-performance-report-table",
  () => ({
    DailyPerformanceReportTable: ({
      data,
      loading,
      error,
      onRefresh,
    }: {
      data: any[];
      loading: boolean;
      error: Error | null;
      onRefresh?: () => void;
    }) => (
      <div data-testid="daily-performance-report-table">
        <div data-testid="table-data">{JSON.stringify(data)}</div>
        <div data-testid="table-loading">
          {loading ? "Loading" : "Not Loading"}
        </div>
        <div data-testid="table-error">
          {error ? error.message : "No Error"}
        </div>
        {onRefresh && (
          <button
            data-testid="refresh-button"
            onClick={onRefresh}
            type="button"
          >
            Refresh
          </button>
        )}
      </div>
    ),
  }),
);

describe("DailyPerformanceReport", () => {
  const mockData = [
    { workstation_id: "WS1", date: "2023-01-01", performance: 95 },
    { workstation_id: "WS2", date: "2023-01-01", performance: 87 },
  ];

  const mockRefetch = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup default mock implementation
    const useQueryMock = vi.mocked(ictApi.client.useQuery);
    // @ts-ignore
    useQueryMock.mockReturnValue({
      data: mockData,
      error: null,
      isLoading: false,
      refetch: mockRefetch,
    });
  });

  it("renders the component with correct title", () => {
    render(<DailyPerformanceReport />);
    expect(screen.getByText("Daily Performance Report")).toBeInTheDocument();
  });

  it("displays date period selector with default value", () => {
    render(<DailyPerformanceReport />);
    expect(screen.getByText("Last 7 days")).toBeInTheDocument();
  });

  it("changes date period when selector is changed", async () => {
    render(<DailyPerformanceReport />);

    // Find the select element and change its value
    const select = screen.getByLabelText("Date Period");
    fireEvent.change(select, { target: { value: DatePeriod.last30days } });

    await waitFor(() => {
      expect(select).toHaveValue(DatePeriod.last30days);
    });
  });

  it("updates workstations when filter is applied", async () => {
    const useQueryMock = vi.mocked(ictApi.client.useQuery);

    render(<DailyPerformanceReport />);

    // Click the workstation filter button to select workstations
    fireEvent.click(screen.getByTestId("workstation-filter"));

    await waitFor(() => {
      // Verify that the API was called with the correct workstations
      expect(useQueryMock).toHaveBeenCalledWith(
        "post",
        "/workstation/daily-performance/list",
        expect.objectContaining({
          body: { workstations: ["WS1", "WS2"] },
        }),
        expect.anything(),
      );
    });
  });

  it("handles loading state correctly", () => {
    const useQueryMock = vi.mocked(ictApi.client.useQuery);
    useQueryMock.mockReturnValue({
      data: undefined,
      error: null,
      isLoading: true,
    });

    render(<DailyPerformanceReport />);

    // The table should not be rendered when data is undefined
    expect(screen.queryByTestId("export-button")).not.toBeInTheDocument();
  });

  it("passes refetch function to DailyPerformanceReportTable", () => {
    render(<DailyPerformanceReport />);

    // Verify that the refresh button exists
    expect(screen.getByTestId("refresh-button")).toBeInTheDocument();
  });

  it("calls refetch when refresh button is clicked", () => {
    render(<DailyPerformanceReport />);

    // Click the refresh button
    fireEvent.click(screen.getByTestId("refresh-button"));

    // Verify the refetch function was called
    expect(mockRefetch).toHaveBeenCalledTimes(1);
  });
});
