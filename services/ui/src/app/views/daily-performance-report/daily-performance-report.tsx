import { Select, SelectItem } from "@carbon/react";
import { useState } from "react";
import { ictApi } from "../../api/ict-api";
import { ViewBar } from "../../components/view-bar/view-bar";
import { DatePeriod } from "../../types";
import { DailyPerformanceReportTable } from "./components/daily-performance-report-table/daily-performance-report-table";
import { WorkstationFilter } from "./components/workstation-filter/workstation-filter";
import { useTranslation } from "react-i18next";
import { useApiErrorState } from "../../hooks/use-api-error-state";
import { useDates } from "../../hooks/use-dates";
import { FullPageContainer } from "../../components/full-page-container/full-page-container";
import DebugInfoTooltip from "../../components/debug-info/debug-info-tooltip";

export function DailyPerformanceReport() {
  const [workstations, setWorkstations] = useState<string[]>([]);
  const [datePeriod, setDatePeriod] = useState<DatePeriod>(
    DatePeriod.last7days,
  );
  const { startDate, endDate } = useDates(datePeriod);
  const endpoint = "/workstation/daily-performance/list";

  const { data, error, isLoading, isRefetching, refetch } =
    ictApi.client.useQuery(
      "post",
      endpoint,
      {
        body: { workstations },
        params: {
          query: {
            start_date: startDate.toISOString(),
            end_date: endDate.toISOString(),
          },
        },
      },
      { enabled: !!workstations && !!startDate && !!endDate },
    );

  const { t } = useTranslation();
  const isNoDataAvailable = useApiErrorState(error);

  const handleRefresh = () => {
    refetch();
  };

  return (
    <div style={{ flex: 1, width: "100%" }}>
      <ViewBar title={"Daily Performance Report"} showDatePeriodRange={false}>
        <Select
          id="date-period-select"
          labelText="Date Period"
          hideLabel
          value={datePeriod}
          onChange={(e) => setDatePeriod(e.target.value as DatePeriod)}
          size="md"
        >
          <SelectItem
            value={DatePeriod.last7days}
            text={t("last7Days", "Last 7 days")}
          />
          <SelectItem
            value={DatePeriod.last30days}
            text={t("last30Days", "Last 30 days")}
          />
          <SelectItem
            value={DatePeriod.last60days}
            text={t("last60Days", "Last 60 days")}
          />
        </Select>

        <WorkstationFilter onChange={setWorkstations} />
      </ViewBar>
      <FullPageContainer>
        <div style={{ float: "right" }}>
          <DebugInfoTooltip endpointKey={endpoint} />
        </div>
        <DailyPerformanceReportTable
          error={isNoDataAvailable ? null : error}
          loading={isLoading || isRefetching}
          data={data || []}
          onRefresh={handleRefresh}
        />
      </FullPageContainer>
    </div>
  );
}

export default DailyPerformanceReport;
