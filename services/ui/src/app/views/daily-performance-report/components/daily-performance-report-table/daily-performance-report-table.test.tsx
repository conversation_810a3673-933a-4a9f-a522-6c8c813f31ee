import { vi } from "vitest";
import { fireEvent, render, screen } from "../../../../../test-utils";
import { DailyPerformanceReportTable } from "./daily-performance-report-table";

// Mock the Datagrid component
vi.mock("../../../../components/datagrid", () => ({
  Datagrid: ({
    columns,
    data,
    isLoading,
    error,
    onExport,
    onRefreshClick,
  }: {
    columns: any[];
    data: any[];
    isLoading: boolean;
    error: Error | null;
    onExport: () => void;
    onRefreshClick?: () => void;
    mode: string;
    enableSelection: boolean;
    initialPagination: { pageIndex: number; pageSize: number };
    initialDensity: string;
  }) => (
    <div data-testid="datagrid">
      <div data-testid="columns">
        {JSON.stringify(columns.map((c) => c.header))}
      </div>
      <div data-testid="data">{JSON.stringify(data)}</div>
      <div data-testid="loading">{isLoading ? "Loading" : "Not Loading"}</div>
      <div data-testid="error">{error ? error.message : "No Error"}</div>
      <button type="button" data-testid="export-button" onClick={onExport}>
        Export
      </button>
      {onRefreshClick && (
        <button
          type="button"
          data-testid="refresh-button"
          onClick={onRefreshClick}
        >
          Refresh
        </button>
      )}
    </div>
  ),
}));

// Mock Carbon React
vi.mock("@carbon/react", () => ({
  GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
}));

describe("DailyPerformanceReportTable", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render the table with correct data", () => {
    const mockData = [
      {
        date: "2023-01-01",
        totalLoggedInHours: 8.5,
        idlePercentage: 10,
        starvedPercentage: 5,
        starvedHours: 0.5,
        donorContainers: 100,
        gtpContainers: 50,
        linesPicked: 200,
        qtyPerLine: 2.5,
        pickLineQty: 500,
        linesPerHour: 25,
        avgLinesPickedPerHr1stShiftPercentage: 60,
        avgLinesPickedPerHr2ndShiftPercentage: 40,
        retrievalFromDMS: 30,
        storageToDMS: 20,
        retrievalFromASRS: 15,
        storageToASRS: 10,
      },
    ];

    render(
      <DailyPerformanceReportTable
        data={mockData}
        loading={false}
        error={null}
      />,
    );

    expect(screen.getByTestId("datagrid")).toBeInTheDocument();

    // Check that data was transformed correctly
    const dataElement = screen.getByTestId("data");
    const parsedData = JSON.parse(dataElement.textContent || "[]");

    expect(parsedData).toHaveLength(1);
    expect(parsedData[0]).toHaveProperty("date");
    expect(parsedData[0]).toHaveProperty("totalLoggedInHours", "8.5");
    expect(parsedData[0]).toHaveProperty("idlePercentage", 10);
    expect(parsedData[0]).toHaveProperty("starvedHours", "0.5");
  });

  it("should handle null values in data", () => {
    const mockData = [
      {
        date: "2023-01-01",
        totalLoggedInHours: 8.5,
        idlePercentage: 10,
        starvedPercentage: 5,
        starvedHours: 0.5,
        donorContainers: null,
        gtpContainers: null,
        linesPicked: null,
        qtyPerLine: null,
        pickLineQty: null,
        linesPerHour: null,
        avgLinesPickedPerHr1stShiftPercentage: null,
        avgLinesPickedPerHr2ndShiftPercentage: null,
        retrievalFromDMS: null,
        storageToDMS: null,
        retrievalFromASRS: null,
        storageToASRS: null,
      },
    ];

    render(
      <DailyPerformanceReportTable
        data={mockData as any}
        loading={false}
        error={null}
      />,
    );

    // Check that null values are handled correctly
    const dataElement = screen.getByTestId("data");
    const parsedData = JSON.parse(dataElement.textContent || "[]");

    expect(parsedData[0]).toHaveProperty("donorContainers", "-");
    expect(parsedData[0]).toHaveProperty("gtpContainers", "-");
    expect(parsedData[0]).toHaveProperty("linesPicked", "-");
  });

  it("should pass loading state to datagrid", () => {
    const mockData = [{ date: "2023-01-01" }];

    render(
      <DailyPerformanceReportTable
        data={mockData}
        loading={true}
        error={null}
      />,
    );

    expect(screen.getByTestId("loading")).toHaveTextContent("Loading");
  });

  it("should generate all expected columns", () => {
    const mockData = [{ date: "2023-01-01" }];

    render(
      <DailyPerformanceReportTable
        data={mockData}
        loading={false}
        error={null}
      />,
    );

    const columnsElement = screen.getByTestId("columns");
    const columnHeaders = JSON.parse(columnsElement.textContent || "[]");

    // Check that all expected columns are present
    expect(columnHeaders).toContain("Date");
    expect(columnHeaders).toContain("Total Hours");
    expect(columnHeaders).toContain("Idle %");
    expect(columnHeaders).toContain("Starved %");
    expect(columnHeaders).toContain("Starved Hours");
    expect(columnHeaders).toContain("Donor Containers");
    expect(columnHeaders).toContain("GTP Containers");
    expect(columnHeaders).toContain("Lines Picked");
    expect(columnHeaders).toContain("Qty/Line");
    expect(columnHeaders).toContain("Pick Line Qty");
    expect(columnHeaders).toContain("Lines/Hour");
    expect(columnHeaders).toContain("1st Shift %");
    expect(columnHeaders).toContain("2nd Shift %");
    expect(columnHeaders).toContain("DMS Retrieval");
    expect(columnHeaders).toContain("DMS Storage");
    expect(columnHeaders).toContain("ASRS Retrieval");
    expect(columnHeaders).toContain("ASRS Storage");
  });

  it("should call onRefresh when refresh button is clicked", () => {
    const mockData = [{ date: "2023-01-01" }];
    const onRefreshMock = vi.fn();

    render(
      <DailyPerformanceReportTable
        data={mockData}
        loading={false}
        error={null}
        onRefresh={onRefreshMock}
      />,
    );

    const refreshButton = screen.getByTestId("refresh-button");
    fireEvent.click(refreshButton);

    expect(onRefreshMock).toHaveBeenCalledTimes(1);
  });

  it("should not show refresh button when onRefresh prop is not provided", () => {
    const mockData = [{ date: "2023-01-01" }];

    render(
      <DailyPerformanceReportTable
        data={mockData}
        loading={false}
        error={null}
      />,
    );

    expect(screen.queryByTestId("refresh-button")).not.toBeInTheDocument();
  });
});
