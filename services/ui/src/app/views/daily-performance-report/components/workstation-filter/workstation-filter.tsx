import { MultiSelect, Stack } from "@carbon/react";
import { ictApi } from "../../../../api/ict-api";
import classes from "./workstation-filter.module.css";
import { useTranslation } from "react-i18next";

type WorkstationFilterProps = {
  onChange: (workstation: string[]) => void;
};

export function WorkstationFilter({ onChange }: WorkstationFilterProps) {
  const { data } = ictApi.client.useQuery(
    "get",
    "/workstation/workstations",
    {},
  );

  // Transform the data for Carbon's MultiSelect
  const items: {
    id: string;
    text: string;
    label: string;
    isSelectAll?: boolean;
  }[] = [
    {
      id: "select-all",
      text: "Select All",
      label: "Select All",
      isSelectAll: true,
    },
    ...(data?.map((workstation) => ({
      id: workstation,
      text: workstation,
      label: workstation,
    })) || []),
  ];

  const { t } = useTranslation();

  return (
    <Stack orientation="horizontal" gap={2} className={classes.container}>
      <span className={classes.text}>Workstation Filter:</span>
      <MultiSelect
        id="workstation-filter"
        titleText=""
        hideLabel
        label={t("Select workstations")}
        items={items}
        itemToString={(item) => item?.text || ""}
        onChange={(data) =>
          onChange(data.selectedItems?.map((item) => item.id) ?? [])
        }
        selectionFeedback="top"
        size="md"
        className={classes.workstationDropdown} // Add a class for additional styling if needed
      />
    </Stack>
  );
}
