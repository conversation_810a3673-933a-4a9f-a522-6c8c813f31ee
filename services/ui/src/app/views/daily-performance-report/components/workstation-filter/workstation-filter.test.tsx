import { vi } from "vitest";
import { fireEvent, render, screen, waitFor } from "../../../../../test-utils";
import { ictApi } from "../../../../api/ict-api";
import { WorkstationFilter } from "./workstation-filter";

// Mock dependencies
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

vi.mock("../../../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: vi.fn(),
    },
  },
}));

describe("WorkstationFilter", () => {
  const mockWorkstations = ["WS1", "WS2", "WS3"];
  const onChangeMock = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup default mock implementation for the API call
    const useQueryMock = vi.mocked(ictApi.client.useQuery);
    useQueryMock.mockReturnValue({
      data: mockWorkstations,
      error: null,
      isLoading: false,
    });
  });

  it("renders the component with correct label", () => {
    render(<WorkstationFilter onChange={onChangeMock} />);
    expect(screen.getByText("Workstation Filter:")).toBeInTheDocument();
  });

  it("renders MultiSelect with workstation data", async () => {
    render(<WorkstationFilter onChange={onChangeMock} />);

    // Open the MultiSelect dropdown
    const multiSelect = await screen.findByText("Select workstations");

    fireEvent.click(multiSelect);

    // Check if workstation options are rendered
    expect(screen.getByText("WS1")).toBeInTheDocument();
    expect(screen.getByText("WS2")).toBeInTheDocument();
    expect(screen.getByText("WS3")).toBeInTheDocument();
  });

  it("calls onChange with selected workstations", async () => {
    render(<WorkstationFilter onChange={onChangeMock} />);

    // Open the MultiSelect dropdown
    const multiSelect = await screen.findByText("Select workstations");
    fireEvent.click(multiSelect);

    // Select a workstation
    const option = screen.getByText("WS1");
    fireEvent.click(option);

    await waitFor(() => {
      expect(onChangeMock).toHaveBeenCalledWith(["WS1"]);
    });
  });

  it("handles multiple workstation selections", async () => {
    render(<WorkstationFilter onChange={onChangeMock} />);

    // Open the MultiSelect dropdown
    const multiSelect = await screen.findByText("Select workstations");
    fireEvent.click(multiSelect);

    // Select multiple workstations
    fireEvent.click(screen.getByText("WS1"));
    fireEvent.click(screen.getByText("WS2"));

    await waitFor(() => {
      expect(onChangeMock).toHaveBeenCalledWith(
        expect.arrayContaining(["WS1", "WS2"]),
      );
    });
  });

  it("handles API loading state", async () => {
    const useQueryMock = vi.mocked(ictApi.client.useQuery);
    useQueryMock.mockReturnValue({
      data: undefined,
      error: null,
      isLoading: true,
    });

    render(<WorkstationFilter onChange={onChangeMock} />);

    // MultiSelect should be rendered but with empty items
    const multiSelect = await screen.findByText("Select workstations");
    fireEvent.click(multiSelect);

    // No workstation options should be available
    expect(screen.queryByText("WS1")).not.toBeInTheDocument();
  });

  it("handles API error state", async () => {
    const useQueryMock = vi.mocked(ictApi.client.useQuery);
    useQueryMock.mockReturnValue({
      data: undefined,
      error: new Error("API Error"),
      isLoading: false,
    });

    render(<WorkstationFilter onChange={onChangeMock} />);

    // Component should still render with empty items
    expect(screen.getByText("Workstation Filter:")).toBeInTheDocument();
  });

  it("handles empty workstation data", async () => {
    const useQueryMock = vi.mocked(ictApi.client.useQuery);
    useQueryMock.mockReturnValue({
      data: [],
      error: null,
      isLoading: false,
    });

    render(<WorkstationFilter onChange={onChangeMock} />);

    // MultiSelect should be rendered but with empty items
    const multiSelect = await screen.findByText("Select workstations");
    fireEvent.click(multiSelect);

    // No workstation options should be available
    expect(screen.queryByText("WS1")).not.toBeInTheDocument();
  });

  it("calls API with correct parameters", () => {
    render(<WorkstationFilter onChange={onChangeMock} />);

    // Verify API was called correctly
    expect(ictApi.client.useQuery).toHaveBeenCalledWith(
      "get",
      "/workstation/workstations",
      {},
    );
  });
});
