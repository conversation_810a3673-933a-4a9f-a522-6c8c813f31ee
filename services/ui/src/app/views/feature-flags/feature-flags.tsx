import {
  Column,
  Grid,
  <PERSON><PERSON>,
  InlineNotification,
  Tab,
  Tab<PERSON>ist,
  Tabs,
  Tile,
  Toggle,
} from "@carbon/react";
import { createColumnHelper } from "@tanstack/react-table";
import { useMemo, useState } from "react";
import { useRoles } from "../../auth/hooks/use-roles";
import { Datagrid } from "../../components/datagrid";
import {
  toggleFeatureFlag,
  useFeatureFlags,
} from "../../config/hooks/use-config";
import classes from "./feature-flags.module.css";
import type { FeatureFlag, FeatureFlagType } from "./types";

export const FeatureFlagsView = () => {
  const { featureFlags = [], isLoading, error } = useFeatureFlags();
  const [selectedType, setSelectedType] = useState<FeatureFlagType>("All");
  const { hasConfiguratorAccess } = useRoles();

  // Parse feature flags to extract additional metadata
  const parsedFeatureFlags = useMemo(() => {
    return featureFlags.map((flag) => {
      return {
        name: flag.name,
        description: flag.description ?? " - ",
        enabled: Boolean(flag.value),
        source: flag.source || "default",
      } as FeatureFlag;
    });
  }, [featureFlags]);

  // Filter feature flags based on selected type
  const filteredFeatureFlags = useMemo(() => {
    return selectedType === "All"
      ? parsedFeatureFlags
      : parsedFeatureFlags.filter((flag) => flag.type === selectedType);
  }, [parsedFeatureFlags, selectedType]);

  // Get unique types for the segmented control
  const featureFlagTypes = useMemo(() => {
    const types = ["All"];
    const typesSet = new Set<string>();

    for (const flag of parsedFeatureFlags) {
      if (flag.type && flag.type !== " - " && !typesSet.has(flag.type)) {
        typesSet.add(flag.type);
        types.push(flag.type);
      }
    }

    return types;
  }, [parsedFeatureFlags]);

  // Define column helper for type safety
  const columnHelper = createColumnHelper<FeatureFlag>();

  // Define columns for the table
  const columns = useMemo(
    () => [
      columnHelper.display({
        id: "enabled",
        header: "Enabled",
        size: 50,
        cell: ({ row }) => (
          <Toggle
            id={`toggle-${row.original.name}`}
            disabled={!hasConfiguratorAccess}
            labelText=""
            toggled={row.original.enabled}
            style={{ marginLeft: "1rem" }}
            onToggle={(toggled) => {
              toggleFeatureFlag(row.original.name, toggled);
            }}
          />
        ),
      }),
      columnHelper.accessor("name", {
        header: "Name",
        size: 130,
      }),
      columnHelper.accessor("description", {
        header: "Description",
        size: 250,
      }),
    ],
    [columnHelper],
  );

  // Handle error state
  if (error) {
    return (
      <InlineNotification
        kind="error"
        title="Error"
        subtitle={`Error loading feature flags: ${
          (error as Error)?.message || String(error)
        }`}
      />
    );
  }

  return (
    <div className={classes.container}>
      <Grid>
        <Column lg={16} md={8} sm={4}>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "1rem",
            }}
          >
            <Heading>Feature Flags</Heading>

            <Tile style={{ padding: "1rem" }}>
              <div className={classes.controls}>
                <span
                  className="cds--type-body-compact-01"
                  style={{ fontWeight: 600, marginRight: "1rem" }}
                >
                  Type:
                </span>
                <Tabs
                  selectedIndex={featureFlagTypes.indexOf(selectedType)}
                  onChange={(state: { selectedIndex: number }) =>
                    setSelectedType(
                      featureFlagTypes[state.selectedIndex] as FeatureFlagType,
                    )
                  }
                >
                  <TabList contained aria-label="Feature flag types">
                    {featureFlagTypes.map((type) => (
                      <Tab key={type}>{type}</Tab>
                    ))}
                  </TabList>
                </Tabs>
              </div>
            </Tile>
          </div>
          <div className={classes.tableContainer}>
            <div style={{ height: "calc(100vh - 180px)", width: "100%" }}>
              <Datagrid
                columns={columns}
                data={filteredFeatureFlags}
                mode="client"
                enableSelection={false}
                initialPagination={{ pageIndex: 0, pageSize: 100 }}
                initialDensity="compact"
                initialSorting={[{ id: "name", desc: false }]}
                initialColumnVisibility={{ source: false }}
                isLoading={isLoading}
              />
            </div>
          </div>
        </Column>
      </Grid>
    </div>
  );
};

export default FeatureFlagsView;
