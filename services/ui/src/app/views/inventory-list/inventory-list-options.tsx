import { Dropdown } from "@carbon/react";
import { OptionsAccordion } from "../../components/options/options-accordion/options-accordion";
import { OptionsAccordionGroup } from "../../components/options/options-accordion-group/options-accordion-group";
import { DataBase } from "@carbon/icons-react";
import { OptionsContainer } from "../../components/options/options-container/options-container";

export interface InventoryListOptions {
  dataSource: string;
  [key: string]: unknown;
}

interface InventoryListOptionsFormProps {
  options: InventoryListOptions;
  onChange: (updatedOptions: InventoryListOptions) => void;
  onSave: () => void;
  onClose: () => void;
}

const defaultDataSources = [
  {
    id: "wms",
    text: "WMS",
  },
  {
    id: "facility",
    text: "Facility",
  },
];

export function InventoryListOptionsForm({
  options,
  onChange,
  onSave,
  onClose,
}: InventoryListOptionsFormProps) {
  const handleDataSourceChange = (selectedId: string | null) => {
    if (!selectedId) {
      return;
    }

    onChange({
      ...options,
      dataSource: selectedId,
    });
  };

  return (
    <OptionsContainer onClose={onClose} onSave={onSave}>
      <OptionsAccordion>
        <OptionsAccordionGroup
          id="data"
          title="Data"
          icon={<DataBase size="24" />}
        >
          <Dropdown
            titleText="Data Source"
            label=""
            id="data-source"
            items={[...defaultDataSources]}
            selectedItem={
              options.dataSource
                ? {
                    id: options.dataSource,
                    text:
                      defaultDataSources.find(
                        (info) => info.id === options.dataSource,
                      )?.text || "",
                  }
                : null
            }
            onChange={({ selectedItem }) => {
              if (selectedItem) {
                handleDataSourceChange(selectedItem.id);
              }
            }}
            itemToString={(item) => (item ? item.text : "")}
          />
        </OptionsAccordionGroup>
      </OptionsAccordion>
    </OptionsContainer>
  );
}

export default InventoryListOptions;
