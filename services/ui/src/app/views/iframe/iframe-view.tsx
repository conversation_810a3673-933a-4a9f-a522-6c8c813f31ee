import { Heading, Section, Stack } from "@carbon/react";
import { useRoles } from "../../auth/hooks/use-roles";
import { ViewBar } from "../../components/view-bar/view-bar";
import type { AppConfigSetting } from "../../config/hooks/use-config";
import { useViewOptions } from "../../hooks/use-view-options";
import type { BaseViewProps } from "../view-registry.types";
import { IframeViewOptions } from "./iframe-view-options";
import classes from "./iframe-view.module.css";

type IframeViewProps = BaseViewProps & {
  id: string;
  setting: AppConfigSetting;
};

/**
 * Iframe View displays an iframe with a configurable URL. Features include:
 *  - URL is loaded from the configuration API and passed into the view
 *  - An options panel is toggled when the user clicks the Settings button
 *  - Options have a "draft" mode which allow live editing
 *  - A View Bar with Save / Settings button
 *  - The View Bar only allows editing if the user has configurator access
 *  - Options are saved back to the configuration API via "Save"
 * @param setting - The view setting
 * @returns The iframe view component
 */
export function IframeView({ id, setting }: IframeViewProps) {
  /**
   * Use the useRoles hook to check if the user has configurator access
   */
  const { hasConfiguratorAccess } = useRoles();

  const defaultOptions = {
    url: "",
    title: "Iframe View",
  };

  // Extract initial values from setting or use defaults
  const initialOptions: IframeViewOptions =
    (setting?.value as IframeViewOptions) || {
      url: "",
      title: "Iframe View",
    };

  const initialSettings = setting || {
    name: id,
    dataType: "json",
    value: initialOptions,
  };

  /**
   * The "useViewOptions" hook, which manages the view options including
   *  - draft / live editing
   *  - saving options back to the configuration API
   *  - dirty state
   */
  const { draftOptions, handleShowSettings, handleSaveOptions, isDirty } =
    useViewOptions<IframeViewOptions>({
      setting: initialSettings,
      defaultOptions,
      optionsComponent: IframeViewOptions,
    });

  return (
    <div className={classes.container}>
      <ViewBar
        title={draftOptions.title}
        showDatePeriodRange={false}
        showSettings={true}
        saveEnabled={isDirty}
        showSave={true}
        hasConfiguratorAccess={hasConfiguratorAccess}
        onSettingsClick={handleShowSettings}
        onSaveClick={handleSaveOptions}
      />

      <div className={classes.contentWrapper}>
        <div className={classes.content}>
          {draftOptions.url ? (
            <iframe
              src={draftOptions.url}
              className={classes.iframe}
              title={draftOptions.title}
              frameBorder="0"
              sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-downloads allow-presentation allow-top-navigation"
              allow="fullscreen"
              referrerPolicy="strict-origin-when-cross-origin"
              loading="lazy"
            />
          ) : (
            <Stack gap={5}>
              <Heading>Iframe View</Heading>
              <Section>
                <p>
                  Please configure a URL in the settings to display content in
                  the iframe.
                </p>
              </Section>
            </Stack>
          )}
        </div>
      </div>
    </div>
  );
}

export default IframeView;
