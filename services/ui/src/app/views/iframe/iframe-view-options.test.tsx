import { vi } from "vitest";
import { fireEvent, render, screen } from "../../../test-utils";
import { IframeViewOptions } from "./iframe-view-options";
import type { IframeViewOptions as IframeViewOptionsType } from "./iframe-view-options";

// Mock Carbon components
vi.mock("@carbon/react", () => ({
  TextInput: ({
    labelText,
    id,
    value,
    onChange,
    helperText,
    placeholder,
  }: {
    labelText: string;
    id: string;
    value: string;
    onChange: (e: { target: { value: string } }) => void;
    helperText?: string;
    placeholder?: string;
  }) => (
    <div data-testid={id}>
      <label htmlFor={id}>{labelText}</label>
      <input
        id={id}
        type="text"
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        data-testid={`${id}-input`}
      />
      {helperText && <div data-testid={`${id}-helper`}>{helperText}</div>}
    </div>
  ),
  GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock Carbon icons
vi.mock("@carbon/icons-react", () => ({
  Analytics: () => <div data-testid="analytics-icon" />,
}));

// Mock options components
vi.mock("../../components/options/options-container/options-container", () => ({
  OptionsContainer: ({
    children,
    onClose,
    onSave,
  }: {
    children: React.ReactNode;
    onClose: () => void;
    onSave: () => void;
  }) => (
    <div data-testid="options-container">
      <div data-testid="options-content">{children}</div>
      <button data-testid="options-close" onClick={onClose}>
        Close
      </button>
      <button data-testid="options-save" onClick={onSave}>
        Save
      </button>
    </div>
  ),
}));

vi.mock("../../components/options/options-accordion/options-accordion", () => ({
  OptionsAccordion: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="options-accordion">{children}</div>
  ),
}));

vi.mock(
  "../../components/options/options-accordion-group/options-accordion-group",
  () => ({
    OptionsAccordionGroup: ({
      children,
      title,
      id,
      icon,
    }: {
      children: React.ReactNode;
      title: string;
      id: string;
      icon: React.ReactNode;
    }) => (
      <div data-testid={`options-accordion-group-${id}`}>
        <div data-testid="accordion-icon">{icon}</div>
        <h3 data-testid="accordion-title">{title}</h3>
        <div data-testid="accordion-content">{children}</div>
      </div>
    ),
  }),
);

describe("IframeViewOptions", () => {
  const defaultOptions: IframeViewOptionsType = {
    url: "https://example.com",
    title: "Test Iframe",
  };

  const mockProps = {
    options: defaultOptions,
    onChange: vi.fn(),
    onClose: vi.fn(),
    onSave: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders correctly with provided options", () => {
    render(<IframeViewOptions {...mockProps} />);

    // Check if the accordion group is rendered with the correct title
    expect(screen.getByTestId("accordion-title")).toHaveTextContent(
      "Iframe Settings",
    );

    // Check if the analytics icon is rendered
    expect(screen.getByTestId("analytics-icon")).toBeInTheDocument();

    // Check if text inputs are rendered with correct labels and values
    expect(screen.getByLabelText("Title")).toBeInTheDocument();
    expect(screen.getByTestId("iframe-title-input")).toHaveValue("Test Iframe");
    expect(screen.getByTestId("iframe-title-helper")).toHaveTextContent(
      "The title for this iframe view",
    );

    expect(screen.getByLabelText("URL")).toBeInTheDocument();
    expect(screen.getByTestId("iframe-url-input")).toHaveValue(
      "https://example.com",
    );
    expect(screen.getByTestId("iframe-url-helper")).toHaveTextContent(
      "The URL to display in the iframe",
    );
    expect(screen.getByTestId("iframe-url-input")).toHaveAttribute(
      "placeholder",
      "https://example.com",
    );

    // Check if buttons are rendered
    expect(screen.getByTestId("options-close")).toBeInTheDocument();
    expect(screen.getByTestId("options-save")).toBeInTheDocument();
  });

  it("renders with empty options", () => {
    const emptyOptions: IframeViewOptionsType = {
      url: "",
      title: "",
    };

    render(<IframeViewOptions {...mockProps} options={emptyOptions} />);

    expect(screen.getByTestId("iframe-title-input")).toHaveValue("");
    expect(screen.getByTestId("iframe-url-input")).toHaveValue("");
  });

  it("calls onChange when title input changes", () => {
    render(<IframeViewOptions {...mockProps} />);

    const titleInput = screen.getByTestId("iframe-title-input");
    fireEvent.change(titleInput, { target: { value: "New Title" } });

    expect(mockProps.onChange).toHaveBeenCalledWith({
      ...defaultOptions,
      title: "New Title",
    });
  });

  it("calls onChange when URL input changes", () => {
    render(<IframeViewOptions {...mockProps} />);

    const urlInput = screen.getByTestId("iframe-url-input");
    fireEvent.change(urlInput, { target: { value: "https://newurl.com" } });

    expect(mockProps.onChange).toHaveBeenCalledWith({
      ...defaultOptions,
      url: "https://newurl.com",
    });
  });

  it("calls onClose when close button is clicked", () => {
    render(<IframeViewOptions {...mockProps} />);

    fireEvent.click(screen.getByTestId("options-close"));
    expect(mockProps.onClose).toHaveBeenCalledTimes(1);
  });

  it("calls onSave when save button is clicked", () => {
    render(<IframeViewOptions {...mockProps} />);

    fireEvent.click(screen.getByTestId("options-save"));
    expect(mockProps.onSave).toHaveBeenCalledTimes(1);
  });

  it("handles multiple input changes correctly", () => {
    render(<IframeViewOptions {...mockProps} />);

    // Change title first
    const titleInput = screen.getByTestId("iframe-title-input");
    fireEvent.change(titleInput, { target: { value: "First Change" } });

    expect(mockProps.onChange).toHaveBeenCalledWith({
      ...defaultOptions,
      title: "First Change",
    });

    // Clear mock and change URL
    vi.clearAllMocks();
    const urlInput = screen.getByTestId("iframe-url-input");
    fireEvent.change(urlInput, { target: { value: "https://second.com" } });

    expect(mockProps.onChange).toHaveBeenCalledWith({
      ...defaultOptions,
      url: "https://second.com",
    });
  });

  it("renders all components in correct structure", () => {
    render(<IframeViewOptions {...mockProps} />);

    // Check if all parent components are rendered
    expect(screen.getByTestId("options-container")).toBeInTheDocument();
    expect(screen.getByTestId("options-accordion")).toBeInTheDocument();
    expect(
      screen.getByTestId("options-accordion-group-settings"),
    ).toBeInTheDocument();

    // Check if content is nested properly
    const container = screen.getByTestId("options-container");
    const accordion = screen.getByTestId("options-accordion");
    const accordionGroup = screen.getByTestId(
      "options-accordion-group-settings",
    );

    expect(container).toContainElement(accordion);
    expect(accordion).toContainElement(accordionGroup);
  });
});
