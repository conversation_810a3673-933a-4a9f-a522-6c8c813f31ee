import { vi } from "vitest";
import { fireEvent, render, screen } from "../../../test-utils";
import { IframeView } from "./iframe-view";
import type { AppConfigSetting } from "../../config/hooks/use-config";

// Mock Carbon components
vi.mock("@carbon/react", () => ({
  Heading: ({ children }: { children: React.ReactNode }) => (
    <h2 data-testid="carbon-heading">{children}</h2>
  ),
  Section: ({ children }: { children: React.ReactNode }) => (
    <section data-testid="carbon-section">{children}</section>
  ),
  Stack: ({ children, gap }: { children: React.ReactNode; gap?: number }) => (
    <div data-testid="carbon-stack" data-gap={gap}>
      {children}
    </div>
  ),
  GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock the ViewBar component
vi.mock("../../components/view-bar/view-bar", () => ({
  ViewBar: ({
    title,
    showDatePeriodRange,
    showSettings,
    saveEnabled,
    showSave,
    hasConfiguratorAccess,
    onSettingsClick,
    onSaveClick,
  }: {
    title: string;
    showDatePeriodRange: boolean;
    showSettings: boolean;
    saveEnabled: boolean;
    showSave: boolean;
    hasConfiguratorAccess: boolean;
    onSettingsClick: () => void;
    onSaveClick: () => void;
  }) => (
    <div data-testid="view-bar">
      <div data-testid="view-bar-title">{title}</div>
      <div data-testid="show-date-period-range">
        {showDatePeriodRange ? "true" : "false"}
      </div>
      <div data-testid="show-settings">{showSettings ? "true" : "false"}</div>
      <div data-testid="save-enabled">{saveEnabled ? "true" : "false"}</div>
      <div data-testid="show-save">{showSave ? "true" : "false"}</div>
      <div data-testid="has-configurator-access">
        {hasConfiguratorAccess ? "true" : "false"}
      </div>
      <button data-testid="settings-button" onClick={onSettingsClick}>
        Settings
      </button>
      <button data-testid="save-button" onClick={onSaveClick}>
        Save
      </button>
    </div>
  ),
}));

// Mock the IframeViewOptions component
vi.mock("./iframe-view-options", () => ({
  IframeViewOptions: ({ options, onSave, onClose }: any) => (
    <div data-testid="iframe-view-options">
      <div data-testid="options-url">{options.url}</div>
      <div data-testid="options-title">{options.title}</div>
      <button data-testid="options-save" onClick={onSave}>
        Save Options
      </button>
      <button data-testid="options-close" onClick={onClose}>
        Close Options
      </button>
    </div>
  ),
}));

// Mock the use-roles hook
const mockUseRoles = vi.fn();
vi.mock("../../auth/hooks/use-roles", () => ({
  useRoles: () => mockUseRoles(),
}));

// Mock the use-view-options hook
const mockUseViewOptions = vi.fn();
vi.mock("../../hooks/use-view-options", () => ({
  useViewOptions: () => mockUseViewOptions(),
}));

describe("IframeView", () => {
  const mockId = "test-iframe-view";
  const mockSettingWithUrl: AppConfigSetting = {
    id: "test",
    name: "test-iframe",
    dataType: "json",
    group: "views",
    value: {
      url: "https://example.com",
      title: "Test Iframe",
    },
  };

  const mockSettingWithoutUrl: AppConfigSetting = {
    id: "test",
    name: "test-iframe",
    dataType: "json",
    group: "views",
    value: {
      url: "",
      title: "Empty Iframe",
    },
  };

  const mockViewOptions = {
    draftOptions: {
      url: "https://example.com",
      title: "Test Iframe",
    },
    handleShowSettings: vi.fn(),
    handleSaveOptions: vi.fn(),
    handleOptionsChange: vi.fn(),
    handleCancelOptions: vi.fn(),
    handleApplyOptions: vi.fn(),
    isDirty: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup default mocks
    mockUseRoles.mockReturnValue({
      roles: [],
      actualRoles: [],
      isInternalUser: false,
      hasTableauAccess: false,
      hasConfiguratorAccess: true,
      isActualInternalUser: false,
    });

    mockUseViewOptions.mockReturnValue(mockViewOptions);
  });

  it("renders iframe when URL is provided", () => {
    render(
      <IframeView id={mockId} setting={mockSettingWithUrl} options={{}} />,
    );

    // Check if iframe is rendered
    const iframe = screen.getByTitle("Test Iframe");
    expect(iframe).toBeInTheDocument();
    expect(iframe).toHaveAttribute("src", "https://example.com");
    expect(iframe).toHaveAttribute("frameBorder", "0");
    expect(iframe).toHaveAttribute("sandbox");

    // Check if view bar is rendered with correct props
    expect(screen.getByTestId("view-bar-title")).toHaveTextContent(
      "Test Iframe",
    );
    expect(screen.getByTestId("show-date-period-range")).toHaveTextContent(
      "false",
    );
    expect(screen.getByTestId("show-settings")).toHaveTextContent("true");
    expect(screen.getByTestId("show-save")).toHaveTextContent("true");
    expect(screen.getByTestId("has-configurator-access")).toHaveTextContent(
      "true",
    );
  });

  it("renders placeholder content when URL is not provided", () => {
    mockUseViewOptions.mockReturnValue({
      ...mockViewOptions,
      draftOptions: {
        url: "",
        title: "Empty Iframe",
      },
    });

    render(
      <IframeView id={mockId} setting={mockSettingWithoutUrl} options={{}} />,
    );

    // Check if placeholder content is rendered
    expect(screen.getByTestId("carbon-heading")).toHaveTextContent(
      "Iframe View",
    );
    expect(
      screen.getByText(
        "Please configure a URL in the settings to display content in the iframe.",
      ),
    ).toBeInTheDocument();

    // Check if iframe is not rendered
    expect(screen.queryByTitle("Empty Iframe")).not.toBeInTheDocument();
  });

  it("handles settings button click", () => {
    render(
      <IframeView id={mockId} setting={mockSettingWithUrl} options={{}} />,
    );

    fireEvent.click(screen.getByTestId("settings-button"));
    expect(mockViewOptions.handleShowSettings).toHaveBeenCalledTimes(1);
  });

  it("handles save button click", () => {
    render(
      <IframeView id={mockId} setting={mockSettingWithUrl} options={{}} />,
    );

    fireEvent.click(screen.getByTestId("save-button"));
    expect(mockViewOptions.handleSaveOptions).toHaveBeenCalledTimes(1);
  });

  it("shows correct save enabled state when dirty", () => {
    mockUseViewOptions.mockReturnValue({
      ...mockViewOptions,
      isDirty: true,
    });

    render(
      <IframeView id={mockId} setting={mockSettingWithUrl} options={{}} />,
    );

    expect(screen.getByTestId("save-enabled")).toHaveTextContent("true");
  });

  it("shows correct configurator access state", () => {
    mockUseRoles.mockReturnValue({
      roles: [],
      actualRoles: [],
      isInternalUser: false,
      hasTableauAccess: false,
      hasConfiguratorAccess: false,
      isActualInternalUser: false,
    });

    render(
      <IframeView id={mockId} setting={mockSettingWithUrl} options={{}} />,
    );

    expect(screen.getByTestId("has-configurator-access")).toHaveTextContent(
      "false",
    );
  });

  it("handles missing setting prop gracefully", () => {
    render(<IframeView id={mockId} setting={undefined as any} options={{}} />);

    // Should still render without errors
    expect(screen.getByTestId("view-bar")).toBeInTheDocument();
  });
});
