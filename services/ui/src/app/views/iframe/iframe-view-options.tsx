import { Analytics } from "@carbon/icons-react";
import { TextInput } from "@carbon/react";
import { OptionsAccordionGroup } from "../../components/options/options-accordion-group/options-accordion-group";
import { OptionsAccordion } from "../../components/options/options-accordion/options-accordion";
import { OptionsContainer } from "../../components/options/options-container/options-container";

export interface IframeViewOptions {
  url: string;
  title: string;
  [key: string]: unknown;
}

interface IframeViewOptionsProps {
  options: IframeViewOptions;
  onChange: (updatedOptions: IframeViewOptions) => void;
  onSave: () => void;
  onClose: () => void;
}

export function IframeViewOptions({
  options,
  onChange,
  onSave,
  onClose,
}: IframeViewOptionsProps) {
  return (
    <OptionsContainer onClose={onClose} onSave={onSave}>
      <OptionsAccordion>
        <OptionsAccordionGroup
          id="settings"
          title="Iframe Settings"
          icon={<Analytics size={20} />}
        >
          <TextInput
            labelText="Title"
            id="iframe-title"
            value={options.title}
            onChange={(e) => onChange({ ...options, title: e.target.value })}
            helperText="The title for this iframe view"
          />
          <TextInput
            labelText="URL"
            id="iframe-url"
            value={options.url}
            onChange={(e) => onChange({ ...options, url: e.target.value })}
            helperText="The URL to display in the iframe"
            placeholder="https://example.com"
          />
        </OptionsAccordionGroup>
      </OptionsAccordion>
    </OptionsContainer>
  );
}

export default IframeViewOptions;
