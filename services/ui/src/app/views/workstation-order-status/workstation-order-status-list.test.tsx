import { vi } from "vitest";
import { render, screen } from "../../../test-utils";
import { WorkstationOrderStatus } from "./workstation-order-status-list";

// Mock the useLocation hook from react-router
let mockLocation = {
  search: "",
  pathname: "/ict-workstation-active-orders",
};

// Mock navigate function
const mockNavigate = vi.fn();

vi.mock("react-router", () => ({
  useLocation: () => mockLocation,
  useNavigate: () => mockNavigate,
  Link: ({
    to,
    children,
    className,
  }: {
    to: string;
    children: React.ReactNode;
    className?: string;
  }) => (
    <a href={to} className={className} data-testid="router-link">
      {children}
    </a>
  ),
}));

// Mock ictApi.client.useQuery hook
const mockUseQuery = vi.fn();
vi.mock("../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: (...args: unknown[]) => mockUseQuery(...args),
    },
  },
}));

// Mock the filter transform utility
vi.mock("../../api/util/filter-transform-util", () => ({
  transformFilters: vi.fn(() => []),
}));

// Mock the dates hook
vi.mock("../../hooks/use-dates", () => ({
  useDates: vi.fn(() => ({
    startDate: new Date("2023-05-15T00:00:00Z"),
    endDate: new Date("2023-05-15T23:59:59Z"),
  })),
}));

// Mock the API error state hook
vi.mock("../../hooks/use-api-error-state", () => ({
  useApiErrorState: vi.fn(() => false),
}));

// Mock the ViewBar component
vi.mock("../../components/view-bar/view-bar", () => ({
  ViewBar: ({
    title,
    children,
  }: {
    title: React.ReactNode;
    children?: React.ReactNode;
  }) => (
    <div data-testid="view-bar">
      {title}
      {children}
    </div>
  ),
}));

vi.mock("../../auth/hooks/use-roles", () => ({
  useRoles: () => ({
    hasConfiguratorAccess: true,
  }),
}));

// Mock the WorkstationOrderStatusTable component
vi.mock(
  "./components/workstation-order-status-table/workstation-order-status-table",
  () => ({
    WorkstationOrderStatusTable: vi
      .fn()
      .mockReturnValue(
        <div data-testid="workstation-order-status-table">
          Workstation Order Status Table
        </div>,
      ),
  }),
);

describe("WorkstationOrderStatus", () => {
  const mockData = {
    data: [
      {
        station: "Station-001",
        position: "A1",
        dwellTime: 15,
        orderStatus: "In Progress",
        pickTask: "PICK-001",
        orderId: "ORDER-001",
        container: "CONT-001",
        completedPicks: 5,
        arrivalTime: "2023-05-15T14:30:00Z",
      },
    ],
    metadata: {
      totalResults: 1,
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockLocation = {
      search: "",
      pathname: "/ict-workstation-overview/order-status",
    };

    // Setup default useQuery response
    mockUseQuery.mockReturnValue({
      data: mockData,
      dataUpdatedAt: Date.now(),
      error: null,
      isLoading: false,
      isFetching: false,
      refetch: vi.fn(),
    });
  });

  test("renders the component with ViewBar and WorkstationOrderStatusTable", async () => {
    render(<WorkstationOrderStatus />);

    expect(screen.getByTestId("view-bar")).toBeInTheDocument();
    expect(
      screen.getByTestId("workstation-order-status-table"),
    ).toBeInTheDocument();
  });

  test("renders breadcrumb navigation correctly", () => {
    render(<WorkstationOrderStatus />);

    const viewBar = screen.getByTestId("view-bar");
    expect(viewBar).toBeInTheDocument();

    // Check for the link back to workstation overview
    const overviewLink = screen.getByTestId("router-link");
    expect(overviewLink).toHaveAttribute("href", "/ict-workstation-overview");
    expect(overviewLink).toHaveTextContent("Workstation Overview");

    // Check for the current page title
    expect(screen.getByText("Order Status")).toBeInTheDocument();
    expect(screen.getByText("/")).toBeInTheDocument();
  });

  test("calls ictApi.client.useQuery with correct parameters", () => {
    render(<WorkstationOrderStatus />);

    expect(mockUseQuery).toHaveBeenCalledWith(
      "post",
      "/workstation/orders/list",
      {
        params: {
          query: {
            start_date: "2023-05-15T00:00:00.000Z",
            end_date: "2023-05-15T23:59:59.000Z",
          },
        },
        body: {
          page: 0,
          limit: 50,
          sortFields: [
            {
              columnName: "dwell_time",
              isDescending: true,
            },
          ],
          filters: [],
        },
      },
      {
        enabled: true,
        keepPreviousData: true,
        placeholderData: expect.any(Function),
        retry: false,
        refetchOnWindowFocus: false,
      },
    );
  });

  test("handles loading state", () => {
    mockUseQuery.mockReturnValue({
      data: null,
      dataUpdatedAt: null,
      error: null,
      isLoading: true,
      isFetching: false,
      refetch: vi.fn(),
    });

    render(<WorkstationOrderStatus />);

    expect(
      screen.getByTestId("workstation-order-status-table"),
    ).toBeInTheDocument();
  });

  test("handles error state", () => {
    const mockError = new Error("API Error");
    mockUseQuery.mockReturnValue({
      data: null,
      dataUpdatedAt: null,
      error: mockError,
      isLoading: false,
      isFetching: false,
      refetch: vi.fn(),
    });

    render(<WorkstationOrderStatus />);

    expect(
      screen.getByTestId("workstation-order-status-table"),
    ).toBeInTheDocument();
  });

  test("maps column IDs to API field names correctly", () => {
    render(<WorkstationOrderStatus />);

    const useQueryCall = mockUseQuery.mock.calls[0];
    const requestBody = useQueryCall[2].body;

    expect(requestBody.sortFields).toEqual([
      {
        columnName: "dwell_time", // mapped from "arrivalTime"
        isDescending: true,
      },
    ]);
  });

  test("calls refetch when handleRefresh is triggered", () => {
    const mockRefetch = vi.fn();
    mockUseQuery.mockReturnValue({
      data: mockData,
      dataUpdatedAt: Date.now(),
      error: null,
      isLoading: false,
      isFetching: false,
      refetch: mockRefetch,
    });

    render(<WorkstationOrderStatus />);

    // The refetch function should be available to the table component
    expect(mockRefetch).toBeDefined();
  });
});
