import { Route, Routes } from "react-router";
import type { BaseViewProps } from "../view-registry.types";
import WorkstationOrderStatusList from "./workstation-order-status-list";
import WorkstationOrderDetail from "./components/workstation-order-detail/workstation-order-detail";
export function WorkstationView(_props: BaseViewProps) {
  return (
    <Routes>
      <Route index element={<WorkstationOrderStatusList />} />
      <Route path=":orderId" element={<WorkstationOrderDetail />} />
    </Routes>
  );
}
export default WorkstationView;
