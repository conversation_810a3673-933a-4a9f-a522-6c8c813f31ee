import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { useMemo, useState } from "react";
import { Link } from "react-router";
import { ictApi } from "../../api/ict-api";
import { transformFilters } from "../../api/util/filter-transform-util";
import { ViewBar } from "../../components/view-bar/view-bar";
import { WorkstationOrderStatusTable } from "./components/workstation-order-status-table/workstation-order-status-table";
import type { WorkstationOrdersListItem } from "./types";
import styles from "./workstation-order-status-list.module.css";
import { useTranslation } from "react-i18next";
import { useDates } from "../../hooks/use-dates";
import { DatePeriod } from "../../types";
import { useApiErrorState } from "../../hooks/use-api-error-state";
import { ExportModal } from "../../components/export-modal/export-modal";
import DebugInfoTooltip from "../../components/debug-info/debug-info-tooltip";

/**
 * Workstation Order Status Page
 *
 * Displays detailed order status information with:
 * - Breadcrumbs back to Workstation Overview
 * - Data grid table with order details
 * - Pagination, sorting, and filtering capabilities
 */
export function WorkstationOrderStatus() {
  const { t } = useTranslation();
  const [globalFilter, setGlobalFilter] = useState<string>("");
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 50,
  });

  const [sorting, setSorting] = useState<SortingState>([
    { id: "dwellTime", desc: true },
  ]);

  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);

  // Use date range (you can make this configurable later)
  const { startDate, endDate } = useDates(DatePeriod.today);

  // Map frontend column IDs to API field names
  const mapColumnIdToApiField = (columnId: string): string => {
    const mapping: Record<string, string> = {
      arrivalTime: "arrival_time",
      dwellTime: "dwell_time",
      orderStatus: "order_status",
      pickTask: "pick_task",
      orderId: "order_id",
      completedPicks: "completed_picks",
      totalPicks: "total_picks",
      station: "station",
      position: "position",
      container: "container",
    };
    return mapping[columnId] || columnId;
  };

  const sortFields = useMemo(
    () =>
      sorting.map((sort) => ({
        columnName: mapColumnIdToApiField(sort.id),
        isDescending: sort.desc,
      })),
    [sorting],
  );

  const apiFilters = useMemo(
    () => transformFilters(columnFilters),
    [columnFilters],
  );

  const endpoint = "/workstation/orders/list";

  const { data, error, isLoading, isFetching, refetch } =
    ictApi.client.useQuery(
      "post",
      endpoint,
      {
        params: {
          query: {
            start_date: startDate.toISOString(),
            end_date: endDate.toISOString(),
          },
        },
        body: {
          page: pagination.pageIndex,
          limit: pagination.pageSize,
          sortFields,
          filters: apiFilters,
          ...(globalFilter && { searchString: globalFilter }),
        },
      },
      {
        enabled: true,
        keepPreviousData: true,
        placeholderData: (prev) => prev,
        retry: false,
        refetchOnWindowFocus: false,
      },
    );

  const isNoDataAvailable = useApiErrorState(error);

  const workstationData = useMemo(
    () => (data?.data ?? []) as unknown as WorkstationOrdersListItem[],
    [data],
  );

  const handleRefresh = () => {
    refetch();
  };

  // Function to generate column definitions for the export dialog
  const getColumnDefinitions = () => {
    return [
      {
        id: "orderId",
        header: t("workstationOrderStatusTable.orderId", "Order ID"),
      },
      {
        id: "station",
        header: t("workstationOrderStatusTable.station", "Station"),
      },
      {
        id: "position",
        header: t("workstationOrderStatusTable.position", "Position"),
      },
      {
        id: "dwellTime",
        header: t("workstationOrderStatusTable.dwellTime", "Dwell Time"),
      },
      {
        id: "orderStatus",
        header: t("workstationOrderStatusTable.orderStatus", "Order Status"),
      },
      {
        id: "pickTask",
        header: t("workstationOrderStatusTable.pickTask", "Pick Task"),
      },
      {
        id: "container",
        header: t("workstationOrderStatusTable.container", "Container"),
      },
      {
        id: "completedPicks",
        header: t(
          "workstationOrderStatusTable.completedPicks",
          "Completed Picks",
        ),
      },
      {
        id: "arrivalTime",
        header: t("workstationOrderStatusTable.arrivalTime", "Arrival Time"),
      },
    ];
  };

  // Export handler to use selected columns
  const handleExport = async (
    fileName: string,
    selectedColumns: Record<string, boolean>,
    signal: AbortSignal,
  ) => {
    console.log("Starting export process...");

    const { error, data: blob } = await ictApi.fetchClient.POST(
      "/workstation/orders/list/export",
      {
        params: {
          query: {
            start_date: startDate.toISOString(),
            end_date: endDate.toISOString(),
          },
        },
        body: {
          page: pagination.pageIndex,
          limit: pagination.pageSize,
          columns: selectedColumns,
          filters: apiFilters,
          sortFields,
          ...(globalFilter !== "" && { searchString: globalFilter }),
        },
        parseAs: "blob",
        signal,
      },
    );

    if (error) {
      throw new Error("Export failed");
    }
    if (!blob) {
      throw new Error("No data to export");
    }

    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    URL.revokeObjectURL(url);
    document.body.removeChild(a);
    console.log("Export completed successfully!");
  };

  // Function to handle modal close
  const handleExportModalClose = () => {
    setExportDialogOpen(false);
  };

  /**
   * Renders the breadcrumb-style title with link back to Workstation Overview
   */
  const renderTitleContent = () => (
    <div
      data-testid="workstation-order-status"
      className={styles.titleContainer}
    >
      <Link to="/ict-workstation-overview" className={styles.titleLink}>
        <span
          className="cds--type-productive-heading-03"
          style={{ fontWeight: "bold" }}
        >
          {t(
            "workstationOrderStatus.workstationOverview",
            "Workstation Overview",
          )}
        </span>
      </Link>
      <span
        className="cds--type-productive-heading-03"
        style={{ margin: "0 8px" }}
      >
        /
      </span>
      <span className="cds--type-productive-heading-03">
        {t("workstationOrderStatus.title", "Order Status")}
      </span>
    </div>
  );

  return (
    <div data-testid="workstation-order-status" className={styles.container}>
      <ViewBar title={renderTitleContent()} />

      <div className={styles.content}>
        <div style={{ marginLeft: "auto" }}>
          <DebugInfoTooltip endpointKey={endpoint} />
        </div>
        <WorkstationOrderStatusTable
          data={workstationData}
          pagination={pagination}
          setPagination={setPagination}
          sorting={sorting}
          setSorting={setSorting}
          columnFilters={columnFilters}
          setColumnFilters={setColumnFilters}
          isLoading={isLoading}
          isFetching={isFetching}
          error={isNoDataAvailable ? null : error}
          rowCount={data?.metadata.totalResults ?? 0}
          onRefresh={handleRefresh}
          setGlobalFilter={setGlobalFilter}
          onExport={() => setExportDialogOpen(true)}
        />
      </div>
      <ExportModal
        open={exportDialogOpen}
        onClose={handleExportModalClose}
        onExport={handleExport}
        filters={columnFilters}
        columnDefs={getColumnDefinitions()}
        globalFilter={globalFilter}
        baseFileName="Workstation_Order_Status_"
      />
    </div>
  );
}

export default WorkstationOrderStatus;
