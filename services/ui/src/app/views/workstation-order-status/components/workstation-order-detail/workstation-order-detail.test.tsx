import { vi } from "vitest";
import { render, screen } from "../../../../../test-utils";
import { WorkstationOrderDetail } from "./workstation-order-detail";

// Mock React Router
const mockUseParams = vi.fn();

vi.mock("react-router", () => ({
  useParams: () => mockUseParams(),
  Link: ({
    to,
    children,
    className,
  }: {
    to: string;
    children: React.ReactNode;
    className?: string;
  }) => (
    <a href={to} className={className} data-testid="router-link" data-to={to}>
      {children}
    </a>
  ),
}));

// Mock the ICT API
const mockUseQuery = vi.fn();
vi.mock("../../../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: (...args: any[]) => mockUseQuery(...args),
    },
  },
}));

// Mock the ViewBar component
vi.mock("../../../../components/view-bar/view-bar", () => ({
  ViewBar: ({ title }: { title: React.ReactNode }) => (
    <div data-testid="view-bar">
      <div data-testid="view-bar-title">{title}</div>
    </div>
  ),
}));

// Mock the useDates hook
vi.mock("../../../../hooks/use-dates", () => ({
  useDates: () => ({
    startDate: new Date("2023-05-15T00:00:00Z"),
    endDate: new Date("2023-05-15T23:59:59Z"),
  }),
}));

describe("WorkstationOrderDetail", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockUseParams.mockReturnValue({ orderId: "0008168811" });
    // Set default mock return value
    mockUseQuery.mockReturnValue({
      data: {
        data: {
          deliveryNumber: "DEL-123456",
        },
        metadata: {
          totalResults: 25,
        },
      },
      isLoading: false,
      error: null,
    });
  });

  test("renders the component with ViewBar and metrics", () => {
    render(<WorkstationOrderDetail />);

    expect(screen.getByTestId("view-bar")).toBeInTheDocument();
    expect(screen.getByTestId("workstation-order-detail")).toBeInTheDocument();
    expect(screen.getAllByTestId("widget-heading")).toHaveLength(2);
    expect(screen.getAllByTestId("widget-value")).toHaveLength(2);
  });

  test("renders breadcrumb navigation correctly", () => {
    render(<WorkstationOrderDetail />);

    const links = screen.getAllByTestId("router-link");
    expect(links).toHaveLength(2);

    // Check Workstation Overview link
    expect(links[0]).toHaveAttribute("data-to", "/ict-workstation-overview");
    expect(links[0]).toHaveTextContent("Workstation Overview");

    // Check Order Status link
    expect(links[1]).toHaveAttribute(
      "data-to",
      "/ict-workstation-active-orders",
    );
    expect(links[1]).toHaveTextContent("Order Status");
  });

  test("displays order ID in breadcrumb", () => {
    render(<WorkstationOrderDetail />);

    expect(screen.getByText("0008168811")).toBeInTheDocument();
  });

  test("renders delivery number metric with correct data", () => {
    render(<WorkstationOrderDetail />);

    const metricHeadings = screen.getAllByTestId("widget-heading");
    const metricValues = screen.getAllByTestId("widget-value");

    expect(metricHeadings[0]).toHaveTextContent("Delivery Number");
    expect(metricValues[0]).toHaveTextContent("DEL-123456");
  });

  test("renders pick lines metric with correct data", () => {
    render(<WorkstationOrderDetail />);

    const metricHeadings = screen.getAllByTestId("widget-heading");
    const metricValues = screen.getAllByTestId("widget-value");

    expect(metricHeadings[1]).toHaveTextContent("Pick Lines");
    expect(metricValues[1]).toHaveTextContent("25");
  });

  test("calls API with correct parameters", () => {
    render(<WorkstationOrderDetail />);

    expect(mockUseQuery).toHaveBeenCalledWith(
      "post",
      "/workstation/orders/{orderId}/picks/list",
      {
        params: {
          path: { orderId: "0008168811" },
          query: {
            start_date: "2023-05-15T00:00:00.000Z",
            end_date: "2023-05-15T23:59:59.000Z",
            zone: "ASRS",
          },
        },
        body: {
          page: 0,
          limit: 100,
          sortFields: [],
        },
      },
      { enabled: true },
    );
  });

  test("shows N/A for delivery number when data is not available", () => {
    mockUseQuery.mockReturnValue({
      data: {
        data: {
          deliveryNumber: null,
        },
        metadata: {
          totalResults: 0,
        },
      },
      isLoading: false,
      error: null,
    });

    render(<WorkstationOrderDetail />);

    const metricValues = screen.getAllByTestId("widget-value");
    expect(metricValues[0]).toHaveTextContent("N/A");
  });

  test("shows 0 for pick lines when metadata is not available", () => {
    mockUseQuery.mockReturnValue({
      data: {
        data: {
          deliveryNumber: "DEL-123456",
        },
        metadata: null,
      },
      isLoading: false,
      error: null,
    });

    render(<WorkstationOrderDetail />);

    const metricValues = screen.getAllByTestId("widget-value");
    expect(metricValues[1]).toHaveTextContent("0");
  });

  test("handles missing orderId parameter", () => {
    mockUseParams.mockReturnValue({ orderId: undefined });

    render(<WorkstationOrderDetail />);

    expect(mockUseQuery).toHaveBeenCalledWith(
      "post",
      "/workstation/orders/{orderId}/picks/list",
      expect.objectContaining({
        params: {
          path: { orderId: "undefined" },
          query: expect.any(Object),
        },
      }),
      { enabled: false },
    );
  });
});
