.titleContainer {
  display: flex;
  align-items: center;
}

.titleLink {
  text-decoration: none;
  color: inherit;
  cursor: pointer;
}

.titleLink:hover {
  color: var(--cds-link-primary-hover);
  text-decoration: underline;
}

.container {
  padding: 1rem;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.metricsContainer {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.metricItem {
  flex: 1;
  min-width: 0;
}
.tableSection {
  flex: 1;
  min-height: 400px;
  display: flex;
  flex-direction: column;
}
