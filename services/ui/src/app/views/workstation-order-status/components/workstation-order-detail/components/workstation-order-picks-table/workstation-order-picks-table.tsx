import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { createColumnHelper } from "@tanstack/react-table";
import { useState, useCallback } from "react";
import styles from "./workstation-order-picks-table.module.css";
import { useTranslation } from "react-i18next";
import { Datagrid, FilterState } from "../../../../../../components/datagrid";
import { ictApi } from "../../../../../../api/ict-api";
import { useDates } from "../../../../../../hooks/use-dates";
import { DatePeriod } from "../../../../../../types";
import { logger } from "../../../../../../utils";

interface OrderPicksTableData {
  container: string;
  status: string;
  orderLine: string;
  style: string;
  size: string;
  qtyOrdered: number;
  qtyPicked: number;
  containers: number;
}

interface ContainerItem {
  container: string;
  status: string;
  transport: string;
  quantity: number;
  lastLocation: string;
  eventTime: string;
}

interface WorkstationOrderPicksTableProps {
  data: OrderPicksTableData[];
  pagination: PaginationState;
  setPagination: (pagination: PaginationState) => void;
  sorting: SortingState;
  _setSorting: (sorting: SortingState) => void;
  _columnFilters: ColumnFiltersState;
  _setColumnFilters: (filters: ColumnFiltersState) => void;
  isLoading: boolean;
  isFetching: boolean;
  error: Error | null;
  _rowCount: number;
  onRefresh?: () => void;
  _setGlobalFilter: (globalFilter: string) => void;
  zone: string;
  onExport?: () => void;
  onFilter?: (filters: FilterState) => void;
}

function ExpandedOrderPickRow({
  style,
  size,
  zone,
  containerData,
  isLoading,
  error,
  isNoDataAvailable,
}: {
  style: string;
  size: string;
  zone: string;
  containerData?: ContainerItem[];
  isLoading?: boolean;
  error?: string | null;
  isNoDataAvailable?: boolean;
}) {
  const { t } = useTranslation();

  return (
    <div>
      <div className={styles.expandedSummary}>
        {`${style}${size} containers in the ${zone} zone`}
      </div>
      {isLoading ? (
        <div>{t("workstationOrderPicks.loading", "Loading...")}</div>
      ) : error ? (
        <div>{error}</div>
      ) : isNoDataAvailable ? (
        <div className={styles.noDataAvailable}>
          {t("workstationOrderPicks.noData", "No data available")}
        </div>
      ) : (
        <table>
          <thead>
            <tr>
              <td>{t("workstationOrderPicks.containerHeader", "Container")}</td>
              <td>{t("workstationOrderPicks.statusHeader", "Status")}</td>
              <td>{t("workstationOrderPicks.transportHeader", "Transport")}</td>
              <td>{t("workstationOrderPicks.quantityHeader", "Quantity")}</td>
              <td>
                {t("workstationOrderPicks.lastLocationHeader", "Last Location")}
              </td>
              <td>
                {t("workstationOrderPicks.eventTimeHeader", "Event Time")}
              </td>
            </tr>
          </thead>
          <tbody>
            {containerData?.map((container, idx) => (
              <tr key={idx}>
                <td>{container.container}</td>
                <td>{container.status}</td>
                <td>{container.transport}</td>
                <td>{container.quantity}</td>
                <td>{container.lastLocation}</td>
                <td>{new Date(container.eventTime).toLocaleString()}</td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
}

export function WorkstationOrderPicksTable({
  data,
  pagination,
  setPagination,
  sorting,
  _setSorting,
  _columnFilters,
  _setColumnFilters,
  isLoading,
  isFetching,
  error,
  _rowCount,
  onRefresh,
  _setGlobalFilter,
  zone,
  onExport,
  onFilter,
}: WorkstationOrderPicksTableProps) {
  const { t } = useTranslation();
  const { startDate, endDate } = useDates(DatePeriod.today);
  const columnHelper = createColumnHelper<OrderPicksTableData>();

  // Per-row expanded state, data, loading, error
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});
  const [expandedData, setExpandedData] = useState<
    Record<number, ContainerItem[]>
  >({});
  const [expandedLoading, setExpandedLoading] = useState<
    Record<number, boolean>
  >({});
  const [expandedError, setExpandedError] = useState<
    Record<number, string | null>
  >({});
  const [expandedNoDataAvailable, setExpandedNoDataAvailable] = useState<
    Record<number, boolean>
  >({});

  const fetchContainerData = async (
    rowIndex: number,
    pickData: OrderPicksTableData,
  ) => {
    setExpandedLoading((prev) => ({ ...prev, [rowIndex]: true }));
    setExpandedError((prev) => ({ ...prev, [rowIndex]: null }));
    setExpandedNoDataAvailable((prev) => ({ ...prev, [rowIndex]: false }));

    try {
      const response = await ictApi.queryClient.fetchQuery(
        ictApi.client.queryOptions("post", "/workstation/containers/list", {
          params: {
            query: {
              start_date: startDate.toISOString(),
              end_date: endDate.toISOString(),
              size: pickData.size,
              style: pickData.style,
              zone: zone || "ASRS",
            },
          },
          body: {
            page: 0,
            limit: 50,
            sortFields: [],
          },
        }),
      );

      const rawResponseData = (response as { data?: ContainerItem[] })?.data;

      if (
        rawResponseData == null ||
        (Array.isArray(rawResponseData) && rawResponseData.length === 0)
      ) {
        setExpandedData((prev) => ({ ...prev, [rowIndex]: [] }));
        setExpandedNoDataAvailable((prev) => ({ ...prev, [rowIndex]: true }));
      } else {
        setExpandedData((prev) => ({
          ...prev,
          [rowIndex]: rawResponseData as ContainerItem[],
        }));
        setExpandedNoDataAvailable((prev) => ({ ...prev, [rowIndex]: false }));
      }
    } catch (err: unknown) {
      const error = err as Error;
      const isNoDataErrorFromCatch =
        error.message?.search("data is undefined") > -1;

      if (isNoDataErrorFromCatch) {
        setExpandedData((prev) => ({ ...prev, [rowIndex]: [] }));
        setExpandedNoDataAvailable((prev) => ({ ...prev, [rowIndex]: true }));
        setExpandedError((prev) => ({ ...prev, [rowIndex]: null }));
      } else {
        let message = "Failed to fetch containers";
        if (err instanceof Error) {
          message = err.message;
        }
        setExpandedError((prev) => ({ ...prev, [rowIndex]: message }));
        setExpandedNoDataAvailable((prev) => ({ ...prev, [rowIndex]: false }));
        logger.error("Failed to fetch containers:", err);
      }
    } finally {
      setExpandedLoading((prev) => ({ ...prev, [rowIndex]: false }));
    }
  };

  const renderStatusShape = (status: string) => {
    const normalized = status.toLowerCase();
    if (normalized.includes("picked")) {
      return <span className={styles.circle}>●</span>;
    }
    if (normalized.includes("allocated")) {
      return <span className={styles.triangle}>▲</span>;
    }
    return null;
  };

  // Only data columns, no expand column
  const columns = [
    columnHelper.accessor("container", {
      header: t("workstationOrderPicks.container", "Container"),
      size: 120,
    }),
    columnHelper.accessor("status", {
      header: t("workstationOrderPicks.status", "Status"),
      size: 140,
      cell: (info) => {
        const rawStatus = info.getValue();
        return (
          <div className={styles.statusContainer}>
            {renderStatusShape(rawStatus)}
            <span className={styles.statusText}>{rawStatus}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("orderLine", {
      header: t("workstationOrderPicks.orderLine", "Order Line"),
      size: 120,
    }),
    columnHelper.accessor("style", {
      header: t("workstationOrderPicks.style", "Style"),
      size: 140,
    }),
    columnHelper.accessor("size", {
      header: t("workstationOrderPicks.size", "Size"),
      size: 100,
    }),
    columnHelper.accessor("qtyOrdered", {
      header: t("workstationOrderPicks.qtyOrdered", "Qty Ordered"),
      size: 120,
      cell: (info) => info.getValue().toLocaleString(),
    }),
    columnHelper.accessor("qtyPicked", {
      header: t("workstationOrderPicks.qtyPicked", "Qty Picked"),
      size: 120,
      cell: (info) => info.getValue().toLocaleString(),
    }),
    columnHelper.accessor("containers", {
      header: t("workstationOrderPicks.containers", "Containers"),
      size: 120,
      cell: (info) => info.getValue().toLocaleString(),
    }),
  ];

  const handleExport = useCallback(() => {
    if (onExport) {
      onExport();
    }
  }, [onExport]);

  return (
    <div className={styles.tableContainer}>
      <div className={styles.datagridWrapper}>
        <Datagrid
          columns={columns}
          data={data}
          mode="client"
          isLoading={isLoading || isFetching}
          error={error ? error.message : undefined}
          onPageChange={setPagination}
          onRefreshClick={onRefresh}
          onFilter={onFilter}
          showRefreshButton={!!onRefresh}
          initialPagination={pagination}
          initialSorting={sorting}
          enableExpansion={true}
          expandedRows={expandedRows}
          onExpandedRowsChange={(newExpandedRows) => {
            // Find which row was just expanded by comparing with previous state
            const previouslyExpandedKeys = Object.keys(expandedRows).filter(
              (key) => expandedRows[key],
            );
            const newlyExpandedKeys = Object.keys(newExpandedRows).filter(
              (key) => newExpandedRows[key],
            );

            // Find the key that exists in new but not in previous (newly expanded)
            const justExpandedKey = newlyExpandedKeys.find(
              (key) => !previouslyExpandedKeys.includes(key),
            );

            setExpandedRows(newExpandedRows);

            // If we found a newly expanded row, fetch its data
            if (justExpandedKey !== undefined) {
              const expandedRowIndex = Number(justExpandedKey);
              if (
                !expandedData[expandedRowIndex] &&
                !expandedLoading[expandedRowIndex] &&
                !expandedError[expandedRowIndex] &&
                data[expandedRowIndex]
              ) {
                fetchContainerData(expandedRowIndex, data[expandedRowIndex]);
              }
            }
          }}
          renderExpandedRow={(row) => {
            const rowIndex = row.index;
            const isLoading = expandedLoading[rowIndex];
            const errorMsg = expandedError[rowIndex];
            const containerData = expandedData[rowIndex];
            const noData = expandedNoDataAvailable[rowIndex];
            return (
              <ExpandedOrderPickRow
                style={row.original.style}
                size={row.original.size}
                zone={zone}
                containerData={containerData}
                isLoading={isLoading}
                error={errorMsg}
                isNoDataAvailable={noData}
              />
            );
          }}
          onExport={handleExport}
        />
      </div>
    </div>
  );
}
