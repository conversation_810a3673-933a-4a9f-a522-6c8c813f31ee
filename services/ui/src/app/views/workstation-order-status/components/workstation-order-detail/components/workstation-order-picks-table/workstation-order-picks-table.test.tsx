import { vi } from "vitest";
import {
  fireEvent,
  render,
  screen,
  waitFor,
} from "../../../../../../../test-utils";
import { cleanup } from "@testing-library/react";
import { WorkstationOrderPicksTable } from "./workstation-order-picks-table";
import React from "react";

// Mock the ICT API
const mockFetchQuery = vi.fn();
vi.mock("../../../../../../api/ict-api", () => ({
  ictApi: {
    queryClient: {
      fetchQuery: (...args: any[]) => mockFetchQuery(...args),
    },
    client: {
      queryOptions: vi.fn((method, endpoint, options) => ({
        method,
        endpoint,
        ...options,
      })),
    },
  },
}));

// Mock the useDates hook
vi.mock("../../../../../../hooks/use-dates", () => ({
  useDates: () => ({
    startDate: new Date("2023-05-15T00:00:00Z"),
    endDate: new Date("2023-05-15T23:59:59Z"),
  }),
}));

// Mock Carbon icons
vi.mock("@carbon/icons-react", () => ({
  ChevronDown: ({ size }: { size: number }) => (
    <div data-testid="chevron-down" data-size={size}>
      ChevronDown
    </div>
  ),
  ChevronUp: ({ size }: { size: number }) => (
    <div data-testid="chevron-up" data-size={size}>
      ChevronUp
    </div>
  ),
}));

// Mock the Datagrid component
vi.mock("../../../../../../components/datagrid", () => ({
  Datagrid: ({
    columns,
    data,
    isLoading,
    error,
    onPageChange,
    onRefreshClick,
    initialPagination,
    _initialSorting,
    className,
    mode,
    showRefreshButton,
    enableExpansion,
    expandedRows,
    onExpandedRowsChange,
    renderExpandedRow,
  }: any) => {
    const [localExpandedRows, setLocalExpandedRows] = React.useState(
      expandedRows || {},
    );
    const handleExpandedRowsChange = (
      newExpandedRows: Record<string, boolean>,
    ) => {
      setLocalExpandedRows(newExpandedRows);
      if (onExpandedRowsChange) {
        onExpandedRowsChange(newExpandedRows);
      }
    };

    return (
      <div
        data-testid="datagrid"
        data-loading={isLoading}
        data-error={error}
        data-page-index={initialPagination.pageIndex}
        data-page-size={initialPagination.pageSize}
        data-mode={mode}
        data-show-refresh={showRefreshButton}
        className={className}
        data-on-expanded-rows-change={onExpandedRowsChange ? "true" : "false"}
      >
        <div data-testid="column-count">
          {columns.length + (enableExpansion ? 1 : 0)}
        </div>
        <div data-testid="row-count">{data.length}</div>
        <button
          data-testid="page-change-button"
          type="button"
          onClick={() => onPageChange({ pageIndex: 1, pageSize: 50 })}
        >
          Change Page
        </button>
        {onRefreshClick && (
          <button
            data-testid="refresh-button"
            type="button"
            onClick={() => onRefreshClick()}
          >
            Refresh
          </button>
        )}
        {/* Always render expand column if enableExpansion is true */}
        {enableExpansion && (
          <div data-testid="expand-column">
            {data.map((_: unknown, rowIndex: number) => (
              <div key={rowIndex} data-testid={`expand-cell-${rowIndex}`}>
                <button
                  type="button"
                  onClick={() => {
                    handleExpandedRowsChange({
                      ...localExpandedRows,
                      [rowIndex]: !localExpandedRows[rowIndex],
                    });
                  }}
                >
                  {localExpandedRows[rowIndex] ? "▼" : "▶"}
                </button>
              </div>
            ))}
          </div>
        )}
        {/* Render columns except expand column */}
        {columns.map((column: any, colIndex: number) => (
          <div key={colIndex} data-testid={`column-${colIndex}`}>
            {column.accessor === "container" && (
              <div data-testid="container-column">
                {data.map((row: any, rowIndex: number) => (
                  <div
                    key={rowIndex}
                    data-testid={`container-cell-${rowIndex}`}
                  >
                    {typeof column.cell === "function"
                      ? column.cell({
                          row: { original: row },
                          getValue: () => row.container,
                        })
                      : row.container}
                  </div>
                ))}
              </div>
            )}
            {column.accessor === "status" && (
              <div data-testid="status-column">
                {data.map((row: any, rowIndex: number) => (
                  <div key={rowIndex} data-testid={`status-cell-${rowIndex}`}>
                    {typeof column.cell === "function"
                      ? column.cell({
                          row: { original: row },
                          getValue: () => row.status,
                        })
                      : row.status}
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
        {/* Render expanded rows */}
        {enableExpansion &&
          Object.entries(localExpandedRows).map(([rowId, isExpanded]) => {
            if (isExpanded) {
              const rowIndex = parseInt(rowId);
              const row = data[rowIndex];
              return (
                <div
                  key={`expanded-${rowId}`}
                  data-testid={`expanded-row-${rowId}`}
                >
                  {renderExpandedRow({ original: row, index: rowIndex })}
                </div>
              );
            }
            return null;
          })}
      </div>
    );
  },
}));

// Mock createColumnHelper
vi.mock("@tanstack/react-table", () => ({
  createColumnHelper: () => ({
    accessor: (id: string, config: any) => ({
      accessor: id,
      ...config,
    }),
  }),
}));

describe("WorkstationOrderPicksTable", () => {
  const mockContainerData = [
    {
      container: "AAZ27662",
      status: "Forward Pick",
      transport: "T2024",
      quantity: 12,
      lastLocation: "MS012040150222",
      eventTime: "2025-01-06T14:23:15.000Z",
    },
    {
      container: "AAZ27663",
      status: "Reserve Storage",
      transport: "--",
      quantity: 8,
      lastLocation: "RS-A12-B04",
      eventTime: "2025-01-06T13:45:32.000Z",
    },
  ];

  const mockData = [
    {
      container: "AAZ27662",
      status: "Picked",
      orderLine: "00003",
      style: "TBN2807",
      size: "M",
      qtyOrdered: 1,
      qtyPicked: 0,
      containers: 1,
    },
    {
      container: "AAZ27663",
      status: "Allocated",
      orderLine: "00004",
      style: "PKT3456",
      size: "L",
      qtyOrdered: 2,
      qtyPicked: 1,
      containers: 2,
    },
  ];

  const mockOnRefresh = vi.fn();

  const defaultProps = {
    data: mockData,
    pagination: { pageIndex: 0, pageSize: 10 },
    setPagination: vi.fn(),
    sorting: [],
    _setSorting: vi.fn(),
    _columnFilters: [],
    _setColumnFilters: vi.fn(),
    isLoading: false,
    isFetching: false,
    error: null,
    _rowCount: 2,
    _setGlobalFilter: vi.fn(),
    zone: "ASRS",
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockFetchQuery.mockResolvedValue({
      data: mockContainerData,
    });
  });

  it("renders table with correct data", () => {
    render(<WorkstationOrderPicksTable {...defaultProps} />);

    expect(screen.getByTestId("datagrid")).toBeInTheDocument();
    expect(screen.getByTestId("column-count")).toHaveTextContent("9");
    expect(screen.getByTestId("row-count")).toHaveTextContent("2");
  });

  it("renders loading state correctly", () => {
    render(<WorkstationOrderPicksTable {...defaultProps} isLoading={true} />);

    expect(screen.getByTestId("datagrid")).toHaveAttribute(
      "data-loading",
      "true",
    );
  });

  it("renders fetching state correctly", () => {
    render(<WorkstationOrderPicksTable {...defaultProps} isFetching={true} />);

    expect(screen.getByTestId("datagrid")).toHaveAttribute(
      "data-loading",
      "true",
    );
  });

  it("renders error state correctly", () => {
    const error = new Error("Test error");
    render(<WorkstationOrderPicksTable {...defaultProps} error={error} />);

    expect(screen.getByTestId("datagrid")).toHaveAttribute(
      "data-error",
      "Test error",
    );
  });

  it("uses client mode for datagrid", () => {
    render(<WorkstationOrderPicksTable {...defaultProps} />);

    expect(screen.getByTestId("datagrid")).toHaveAttribute(
      "data-mode",
      "client",
    );
  });

  it("handles pagination change", () => {
    render(<WorkstationOrderPicksTable {...defaultProps} />);

    fireEvent.click(screen.getByTestId("page-change-button"));
    expect(defaultProps.setPagination).toHaveBeenCalledWith({
      pageIndex: 1,
      pageSize: 50,
    });
  });

  it("handles refresh when onRefresh is provided", () => {
    render(
      <WorkstationOrderPicksTable
        {...defaultProps}
        onRefresh={mockOnRefresh}
      />,
    );

    fireEvent.click(screen.getByTestId("refresh-button"));
    expect(mockOnRefresh).toHaveBeenCalled();
  });

  it("doesn't show refresh button when onRefresh is not provided", () => {
    render(<WorkstationOrderPicksTable {...defaultProps} />);

    expect(screen.queryByTestId("refresh-button")).not.toBeInTheDocument();
  });

  it("shows refresh button when onRefresh is provided", () => {
    render(
      <WorkstationOrderPicksTable
        {...defaultProps}
        onRefresh={mockOnRefresh}
      />,
    );

    expect(screen.getByTestId("datagrid")).toHaveAttribute(
      "data-show-refresh",
      "true",
    );
  });

  it("renders expand column header", () => {
    render(<WorkstationOrderPicksTable {...defaultProps} />);

    expect(screen.getByTestId("expand-column")).toBeInTheDocument();
  });

  it("renders individual expand buttons for each row", () => {
    render(<WorkstationOrderPicksTable {...defaultProps} />);

    expect(screen.getByTestId("expand-cell-0")).toBeInTheDocument();
    expect(screen.getByTestId("expand-cell-1")).toBeInTheDocument();
  });

  it("renders status shapes correctly for picked status", () => {
    const testData = [
      {
        container: "TEST001",
        status: "Picked",
        orderLine: "00001",
        style: "TEST",
        size: "M",
        qtyOrdered: 1,
        qtyPicked: 1,
        containers: 1,
      },
    ];

    render(<WorkstationOrderPicksTable {...defaultProps} data={testData} />);

    // The status column should render the status with appropriate styling
    expect(screen.getByTestId("status-column")).toBeInTheDocument();
  });

  it("renders status shapes correctly for allocated status", () => {
    const testData = [
      {
        container: "TEST001",
        status: "Allocated",
        orderLine: "00001",
        style: "TEST",
        size: "M",
        qtyOrdered: 1,
        qtyPicked: 0,
        containers: 1,
      },
    ];

    render(<WorkstationOrderPicksTable {...defaultProps} data={testData} />);

    expect(screen.getByTestId("status-column")).toBeInTheDocument();
  });

  it("fetches container data when row is expanded", async () => {
    render(<WorkstationOrderPicksTable {...defaultProps} />);

    // Simulate expanding the first row
    const expandButton = screen
      .getByTestId("expand-cell-0")
      .querySelector("button");
    if (expandButton) {
      fireEvent.click(expandButton);
    }

    await waitFor(() => {
      expect(mockFetchQuery).toHaveBeenCalledWith(
        expect.objectContaining({
          method: "post",
          endpoint: "/workstation/containers/list",
        }),
      );
    });
  });

  it("handles API error when fetching container data", async () => {
    mockFetchQuery.mockRejectedValue(new Error("API Error"));

    render(<WorkstationOrderPicksTable {...defaultProps} />);

    // Simulate expanding the first row
    const expandButton = screen
      .getByTestId("expand-cell-0")
      .querySelector("button");
    if (expandButton) {
      fireEvent.click(expandButton);
    }

    await waitFor(() => {
      expect(screen.getByTestId("expanded-row-0")).toHaveTextContent(
        "API Error",
      );
    });
  });

  it("handles empty API response", async () => {
    mockFetchQuery.mockResolvedValue(null);

    render(<WorkstationOrderPicksTable {...defaultProps} />);

    // Simulate expanding the first row
    const expandButton = screen
      .getByTestId("expand-cell-0")
      .querySelector("button");
    if (expandButton) {
      fireEvent.click(expandButton);
    }

    await waitFor(() => {
      expect(screen.getByTestId("expanded-row-0")).toBeInTheDocument();
    });
  });

  it("handles API response with different data structures", async () => {
    // Test different response structures the component handles
    const responses = [
      { data: mockContainerData },
      mockContainerData,
      { items: mockContainerData },
      { results: mockContainerData },
    ];

    for (const response of responses) {
      mockFetchQuery.mockResolvedValue(response);
      render(<WorkstationOrderPicksTable {...defaultProps} />);

      // Use getAllByTestId to avoid multiple elements error
      const expandCells = screen.getAllByTestId("expand-cell-0");
      const expandButton =
        expandCells[expandCells.length - 1].querySelector("button");
      if (expandButton) {
        fireEvent.click(expandButton);
      }

      await waitFor(() => {
        expect(screen.getByTestId("expanded-row-0")).toBeInTheDocument();
      });

      cleanup();
    }
  });

  it("formats quantities with locale string", () => {
    const testData = [
      {
        container: "TEST001",
        status: "Picked",
        orderLine: "00001",
        style: "TEST",
        size: "M",
        qtyOrdered: 1000,
        qtyPicked: 500,
        containers: 10,
      },
    ];

    render(<WorkstationOrderPicksTable {...defaultProps} data={testData} />);

    // Numbers should be formatted with toLocaleString()
    expect(screen.getByTestId("datagrid")).toBeInTheDocument();
  });

  it("formats event time correctly", () => {
    // Event time formatting is handled in the expanded content
    // It uses new Date(container.eventTime).toLocaleString()
    const testDate = "2025-01-06T14:23:15.000Z";
    const expectedFormat = new Date(testDate).toLocaleString();

    expect(expectedFormat).toBeTruthy();
  });

  it("passes correct initial pagination", () => {
    render(<WorkstationOrderPicksTable {...defaultProps} />);

    expect(screen.getByTestId("datagrid")).toHaveAttribute(
      "data-page-index",
      "0",
    );
    expect(screen.getByTestId("datagrid")).toHaveAttribute(
      "data-page-size",
      "10",
    );
  });

  it("handles missing container data gracefully", () => {
    const testData = [
      {
        container: "",
        status: "",
        orderLine: "",
        style: "",
        size: "",
        qtyOrdered: 0,
        qtyPicked: 0,
        containers: 0,
      },
    ];

    render(<WorkstationOrderPicksTable {...defaultProps} data={testData} />);

    expect(screen.getByTestId("datagrid")).toBeInTheDocument();
  });

  it("handles expanded row loading state", async () => {
    render(<WorkstationOrderPicksTable {...defaultProps} />);

    // Simulate expanding the first row
    const expandButton = screen
      .getByTestId("expand-cell-0")
      .querySelector("button");
    if (expandButton) {
      fireEvent.click(expandButton);
    }

    await waitFor(() => {
      expect(screen.getByTestId("expanded-row-0")).toHaveTextContent(
        "Loading...",
      );
    });
  });

  it("handles expanded row error state", async () => {
    mockFetchQuery.mockRejectedValue(new Error("Failed to fetch containers"));

    render(<WorkstationOrderPicksTable {...defaultProps} />);

    // Simulate expanding the first row
    const expandButton = screen
      .getByTestId("expand-cell-0")
      .querySelector("button");
    if (expandButton) {
      fireEvent.click(expandButton);
    }

    await waitFor(() => {
      expect(screen.getByTestId("expanded-row-0")).toHaveTextContent(
        "Failed to fetch containers",
      );
    });
  });
});
