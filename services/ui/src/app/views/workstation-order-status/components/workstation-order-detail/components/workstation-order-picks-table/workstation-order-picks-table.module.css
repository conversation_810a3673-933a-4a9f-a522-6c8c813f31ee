.tableContainer {
  height: calc(100vh - 170px);
  display: flex;
  flex-direction: column;
}
.tableWrapper {
  height: 100%;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  background-color: var(--cds-layer-01);
  border: 1px solid var(--cds-border-subtle-01);
  border-radius: 4px;
}

.datagridWrapper {
  flex: 1;
  overflow: hidden;
}
.datagridContainer {
  border: none;
}
.circle {
  color: var(--cds-status-green);
  font-size: 16px;
  line-height: 1;
  flex-shrink: 0;
}
.triangle {
  color: var(--cds-status-yellow);
  font-size: 14px;
  line-height: 1;
  flex-shrink: 0;
}
.statusContainer {
  display: flex;
  align-items: center;
  gap: 8px;
}
.statusText {
  color: var(--cds-text-primary);
  font-size: 14px;
  font-weight: 400;
}

/* Expansion controls */
.expandButton,
.batchExpandButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  padding: 0;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  color: var(--cds-icon-primary);
  transition: background-color 0.11s cubic-bezier(0.2, 0, 0.38, 0.9);
}

.expandButton:hover,
.batchExpandButton:hover {
  background-color: var(--cds-background-hover);
}

.expandButton:active,
.batchExpandButton:active {
  background-color: var(--cds-background-active);
}

.expandButton:focus,
.batchExpandButton:focus {
  outline: 2px solid var(--cds-focus);
  outline-offset: -2px;
}

/* Expanded content styles */
.expandedContent {
  display: flex;
  flex-direction: column;
}

.expandedHeader {
  font-weight: 600;
  position: relative;
}

.expandedHeader::before {
  content: "";
  position: absolute;
  left: -1000px;
  right: -1000px;
  bottom: 0;
  height: 1px;
  background-color: var(--cds-border-subtle);
  z-index: 1;
}

.expandedCell {
  position: relative;
}

.expandedCell::before {
  content: "";
  position: absolute;
  left: -1000px;
  right: -1000px;
  bottom: 0;
  height: 1px;
  background-color: var(--cds-border-subtle-02);
  z-index: 1;
}

.expandedCell:last-child {
  border-bottom: none;
}

.expandedCell:last-child::before {
  display: none;
}

/* Add background to expanded rows to ensure continuity */
.expandedContent::after {
  content: "";
  position: absolute;
  top: 0;
  left: -1000px;
  right: -1000px;
  bottom: 0;
  background-color: var(--cds-background);
  z-index: -1;
}

.expandedLoading {
  padding: 16px;
  text-align: center;
  color: var(--cds-text-secondary);
  font-style: italic;
  position: relative;
}

.expandedLoading::after {
  content: "";
  position: absolute;
  top: 0;
  left: -1000px;
  right: -1000px;
  bottom: 0;
  background-color: var(--cds-background);
  z-index: -1;
}

.expandedError {
  padding: 16px;
  text-align: center;
  color: var(--cds-status-danger);
  font-size: 14px;
  position: relative;
}

.expandedError::after {
  content: "";
  position: absolute;
  top: 0;
  left: -1000px;
  right: -1000px;
  bottom: 0;
  background-color: var(--cds-background);
  z-index: -1;
}

.expandedEmpty {
  padding: 16px;
  text-align: center;
  color: var(--cds-text-secondary);
  font-style: italic;
  position: relative;
}

.expandedEmpty::after {
  content: "";
  position: absolute;
  top: 0;
  left: -1000px;
  right: -1000px;
  bottom: 0;
  background-color: var(--cds-background);
  z-index: -1;
}
