import { Link, useParams } from "react-router";
import { ViewBar } from "../../../../components/view-bar/view-bar";
import { useTranslation } from "react-i18next";
import MetricWidget from "../../../../widgets/container-list/container-list-widget/components/container-detail/components/metric-widget";
import styles from "./workstation-order-detail.module.css";
import { ictApi } from "../../../../api/ict-api";
import { useDates } from "../../../../hooks/use-dates";
import { DatePeriod } from "../../../../types";
import { WorkstationOrderPicksTable } from "./components/workstation-order-picks-table";
import { useState, useMemo } from "react";
import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { ExportModal } from "../../../../components/export-modal/export-modal";
import { transformFilters } from "../../../../api/util/filter-transform-util";

/**
 * Workstation Order Detail View
 *
 * Displays detailed information about a specific workstation order.
 * This will be populated with order-specific components later.
 */
export function WorkstationOrderDetail() {
  const { orderId } = useParams<{ orderId: string }>();
  const { t } = useTranslation();
  const { startDate, endDate } = useDates(DatePeriod.today);
  // Table state management
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>("");
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const zone = "ASRS";

  // Memoized sort fields and filters like the working order status list
  const sortFields = useMemo(
    () =>
      sorting.map((sort) => ({
        columnName: sort.id,
        isDescending: sort.desc,
      })),
    [sorting],
  );

  // Fetch workstation order details
  const {
    data: orderData,
    isLoading: isOrderLoading,
    error: orderError,
    refetch: refetchOrderData,
  } = ictApi.client.useQuery(
    "post",
    "/workstation/orders/{orderId}/picks/list",
    {
      params: {
        path: { orderId: String(orderId) },
        query: {
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
          zone,
        },
      },
      body: {
        page: 0,
        limit: 100,
        sortFields,
      },
    },
    { enabled: !!orderId },
  );

  /**
   * Renders the breadcrumb-style title with link back to Order Status List
   */
  const renderTitleContent = () => (
    <div
      data-testid="workstation-order-detail"
      className={styles.titleContainer}
    >
      <Link to="/ict-workstation-overview" className={styles.titleLink}>
        <span
          className="cds--type-productive-heading-03"
          style={{ fontWeight: "bold" }}
        >
          {t(
            "workstationOrderDetail.workstationOverview",
            "Workstation Overview",
          )}
        </span>
      </Link>
      <span
        className="cds--type-productive-heading-03"
        style={{ margin: "0 8px" }}
      >
        /
      </span>
      <Link to="/ict-workstation-active-orders" className={styles.titleLink}>
        <span
          className="cds--type-productive-heading-03"
          style={{ fontWeight: "bold" }}
        >
          {t("workstationOrderDetail.orderStatus", "Order Status")}
        </span>
      </Link>
      <span
        className="cds--type-productive-heading-03"
        style={{ margin: "0 8px" }}
      >
        /
      </span>
      <span className="cds--type-productive-heading-03">{orderId}</span>
    </div>
  );
  // Handle table refresh
  const handleTableRefresh = () => {
    refetchOrderData();
  };

  // Function to generate column definitions for the export dialog
  const getColumnDefinitions = () => {
    return [
      {
        id: "container",
        header: t("workstationOrderPicks.container", "Container"),
      },
      { id: "status", header: t("workstationOrderPicks.status", "Status") },
      {
        id: "orderLine",
        header: t("workstationOrderPicks.orderLine", "Order Line"),
      },
      { id: "style", header: t("workstationOrderPicks.style", "Style") },
      { id: "size", header: t("workstationOrderPicks.size", "Size") },
      {
        id: "qtyOrdered",
        header: t("workstationOrderPicks.qtyOrdered", "Qty Ordered"),
      },
      {
        id: "qtyPicked",
        header: t("workstationOrderPicks.qtyPicked", "Qty Picked"),
      },
      {
        id: "containers",
        header: t("workstationOrderPicks.containers", "Containers"),
      },
    ];
  };

  // Export handler to use selected columns
  const handleExport = async (
    fileName: string,
    selectedColumns: Record<string, boolean>,
    signal: AbortSignal,
  ) => {
    console.log("Starting export process...");

    if (!orderId) {
      throw new Error("Order ID is required for export");
    }

    const { error, data: blob } = await ictApi.fetchClient.POST(
      "/workstation/orders/{orderId}/picks/list/export",
      {
        params: {
          path: { orderId },
          query: {
            start_date: startDate.toISOString(),
            end_date: endDate.toISOString(),
            zone,
          },
        },
        body: {
          page: pagination.pageIndex,
          limit: pagination.pageSize,
          filters: transformFilters(columnFilters),
          sortFields: sorting.map((sort) => ({
            columnName: sort.id,
            isDescending: sort.desc,
          })),
          columns: selectedColumns,
          ...(globalFilter !== "" && { searchString: globalFilter }),
        },
        parseAs: "blob",
        signal,
      },
    );

    if (error) {
      throw new Error("Export failed");
    }
    if (!blob) {
      throw new Error("No data to export");
    }

    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    URL.revokeObjectURL(url);
    document.body.removeChild(a);
    console.log("Export completed successfully!");
  };

  // Function to handle modal close
  const handleExportModalClose = () => {
    setExportDialogOpen(false);
  };

  return (
    <div>
      <ViewBar title={renderTitleContent()} />

      <div className={styles.container}>
        <div className={styles.content}>
          {/* Metrics Section */}
          <div className={styles.metricsContainer}>
            <div className={styles.metricItem}>
              <MetricWidget
                title={t(
                  "workstationOrderDetail.deliveryNumber",
                  "Delivery Number",
                )}
                value={orderData?.data?.deliveryNumber || "N/A"}
                isLoading={isOrderLoading}
              />
            </div>
            <div className={styles.metricItem}>
              <MetricWidget
                title={t("workstationOrderDetail.pickLines", "Pick Lines")}
                value={orderData?.metadata?.totalResults ?? 0}
                isLoading={isOrderLoading}
              />
            </div>
          </div>
          {/* Picks Table Section */}
          <div className={styles.tableSection}>
            <WorkstationOrderPicksTable
              data={orderData?.data?.tableData || []}
              pagination={pagination}
              setPagination={setPagination}
              sorting={sorting}
              _setSorting={setSorting}
              _columnFilters={columnFilters}
              _setColumnFilters={setColumnFilters}
              isLoading={isOrderLoading}
              isFetching={false}
              error={orderError}
              _rowCount={orderData?.metadata?.totalResults || 0}
              onRefresh={handleTableRefresh}
              _setGlobalFilter={setGlobalFilter}
              zone={zone}
              onExport={() => setExportDialogOpen(true)}
            />
          </div>
        </div>
      </div>
      <ExportModal
        open={exportDialogOpen}
        onClose={handleExportModalClose}
        onExport={handleExport}
        filters={columnFilters}
        columnDefs={getColumnDefinitions()}
        baseFileName="Workstation_Order_Picks_"
      />
    </div>
  );
}

export default WorkstationOrderDetail;
