import { vi } from "vitest";
import { fireEvent, render, screen } from "../../../../../test-utils";
import { WorkstationOrderStatusTable } from "./workstation-order-status-table";

// Mock React Router
const mockNavigate = vi.fn();
vi.mock("react-router", () => ({
  useNavigate: () => mockNavigate,
}));

// Mock Carbon components
vi.mock("@carbon/react", () => ({
  GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  Link: ({
    onClick,
    children,
  }: {
    onClick: any;
    children: React.ReactNode;
  }) => (
    <button type="button" onClick={onClick} data-testid="carbon-link">
      {children}
    </button>
  ),
}));

// Mock the Datagrid component
vi.mock("../../../../components/datagrid", () => ({
  Datagrid: ({
    columns,
    data,
    isLoading,
    error,
    onPageChange,
    onRefreshClick,
    initialPagination,
    initialSorting,
    className,
    mode,
    showRefreshButton,
  }: any) => (
    <div
      data-testid="datagrid"
      data-loading={isLoading}
      data-error={error}
      data-page-index={initialPagination.pageIndex}
      data-page-size={initialPagination.pageSize}
      data-sort-id={initialSorting[0]?.id}
      data-sort-desc={initialSorting[0]?.desc}
      data-mode={mode}
      data-show-refresh={showRefreshButton}
      className={className}
    >
      <div data-testid="column-count">{columns.length}</div>
      <div data-testid="row-count">{data.length}</div>
      <button
        data-testid="page-change-button"
        type="button"
        onClick={() => onPageChange({ pageIndex: 1, pageSize: 50 })}
      >
        Change Page
      </button>
      {onRefreshClick && (
        <button
          data-testid="refresh-button"
          type="button"
          onClick={() => onRefreshClick()}
        >
          Refresh
        </button>
      )}
      {data.map((item: any, index: number) => (
        <div key={index} data-testid={`row-${index}`}>
          {item.orderId}
        </div>
      ))}
    </div>
  ),
}));

// Mock createColumnHelper
vi.mock("@tanstack/react-table", () => ({
  createColumnHelper: () => ({
    accessor: (id: string, config: any) => ({
      id,
      ...config,
    }),
  }),
}));

describe("WorkstationOrderStatusTable", () => {
  const mockData = [
    {
      station: "Station-001",
      position: "A1",
      dwellTime: 15,
      orderStatus: "In Progress",
      pickTask: "PICK-001",
      orderId: "ORDER-001",
      container: "CONT-001",
      completedPicks: 5,
      totalPicks: 10,
      arrivalTime: "2023-05-15T14:30:00Z",
    },
    {
      station: "Station-002",
      position: "B2",
      dwellTime: 25,
      orderStatus: "Completed",
      pickTask: "PICK-002",
      orderId: "ORDER-002",
      container: "CONT-002",
      completedPicks: 10,
      totalPicks: 10,
      arrivalTime: "2023-05-14T10:15:00Z",
    },
  ];

  const mockOnRefresh = vi.fn();

  const defaultProps = {
    data: mockData,
    pagination: { pageIndex: 0, pageSize: 50 },
    setPagination: vi.fn(),
    sorting: [{ id: "arrivalTime", desc: true }],
    setSorting: vi.fn(),
    columnFilters: [],
    setColumnFilters: vi.fn(),
    isLoading: false,
    isFetching: false,
    error: null,
    rowCount: 2,
    setGlobalFilter: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders table with correct data", () => {
    render(<WorkstationOrderStatusTable {...defaultProps} />);

    expect(screen.getByTestId("datagrid")).toBeInTheDocument();
    expect(screen.getByTestId("row-count")).toHaveTextContent("2");
    expect(screen.getByTestId("column-count")).toHaveTextContent("10");
    expect(screen.getByTestId("row-0")).toHaveTextContent("ORDER-001");
    expect(screen.getByTestId("row-1")).toHaveTextContent("ORDER-002");
  });

  it("renders Active Orders title", () => {
    render(<WorkstationOrderStatusTable {...defaultProps} />);

    expect(screen.getByText("Active Orders")).toBeInTheDocument();
  });

  it("renders loading state correctly", () => {
    render(<WorkstationOrderStatusTable {...defaultProps} isLoading={true} />);

    expect(screen.getByTestId("datagrid")).toHaveAttribute(
      "data-loading",
      "true",
    );
  });

  it("renders fetching state correctly", () => {
    render(<WorkstationOrderStatusTable {...defaultProps} isFetching={true} />);

    expect(screen.getByTestId("datagrid")).toHaveAttribute(
      "data-loading",
      "true",
    );
  });

  it("renders error state correctly", () => {
    const error = new Error("Test error");
    render(<WorkstationOrderStatusTable {...defaultProps} error={error} />);

    expect(screen.getByTestId("datagrid")).toHaveAttribute(
      "data-error",
      "Test error",
    );
  });

  it("uses server mode for datagrid", () => {
    render(<WorkstationOrderStatusTable {...defaultProps} />);

    expect(screen.getByTestId("datagrid")).toHaveAttribute(
      "data-mode",
      "server",
    );
  });

  it("handles pagination change", () => {
    render(<WorkstationOrderStatusTable {...defaultProps} />);

    fireEvent.click(screen.getByTestId("page-change-button"));
    expect(defaultProps.setPagination).toHaveBeenCalledWith({
      pageIndex: 1,
      pageSize: 50,
    });
  });

  it("handles refresh when onRefresh is provided", () => {
    render(
      <WorkstationOrderStatusTable
        {...defaultProps}
        onRefresh={mockOnRefresh}
      />,
    );

    fireEvent.click(screen.getByTestId("refresh-button"));
    expect(mockOnRefresh).toHaveBeenCalled();
  });

  it("doesn't show refresh button when onRefresh is not provided", () => {
    render(<WorkstationOrderStatusTable {...defaultProps} />);

    expect(screen.queryByTestId("refresh-button")).not.toBeInTheDocument();
  });

  it("shows refresh button when onRefresh is provided", () => {
    render(
      <WorkstationOrderStatusTable
        {...defaultProps}
        onRefresh={mockOnRefresh}
      />,
    );

    expect(screen.getByTestId("datagrid")).toHaveAttribute(
      "data-show-refresh",
      "true",
    );
  });

  it("handles Order ID click with console log", () => {
    const consoleSpy = vi.spyOn(console, "log").mockImplementation(() => {});

    // We need to mock the cell renderer for orderId
    // Get the first column's cell renderer function
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const columnHelper = require("@tanstack/react-table").createColumnHelper();
    const columns = [
      columnHelper.accessor("orderId", {
        header: "Order ID",
        size: 140,
        cell: (info: any) => (
          <button
            type="button"
            onClick={(e: any) => {
              e.stopPropagation();
              console.log("Order ID clicked:", info.getValue());
            }}
          >
            {info.getValue()}
          </button>
        ),
      }),
    ];

    // Call the cell renderer with mock info
    const cellRenderer = columns[0].cell;
    const mockInfo = { getValue: () => "ORDER-001" };
    const linkElement = cellRenderer(mockInfo);

    // Create a mock event
    const mockEvent = { stopPropagation: vi.fn() };

    // Simulate clicking the link
    linkElement.props.onClick(mockEvent);

    expect(mockEvent.stopPropagation).toHaveBeenCalled();
    expect(consoleSpy).toHaveBeenCalledWith("Order ID clicked:", "ORDER-001");

    consoleSpy.mockRestore();
  });

  it("formats dwell time correctly", () => {
    // Test the dwell time cell formatter
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const columnHelper = require("@tanstack/react-table").createColumnHelper();
    const columns = [
      columnHelper.accessor("dwellTime", {
        header: "Dwell Time",
        size: 120,
        cell: (info: any) => `${info.getValue()} min`,
      }),
    ];

    const cellRenderer = columns[0].cell;
    const mockInfo = { getValue: () => 15 };
    const result = cellRenderer(mockInfo);

    expect(result).toBe("15 min");
  });

  it("formats arrival time correctly", () => {
    // Test the arrival time cell formatter
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const columnHelper = require("@tanstack/react-table").createColumnHelper();
    const columns = [
      columnHelper.accessor("arrivalTime", {
        header: "Arrival Time",
        size: 180,
        cell: (info: any) => {
          const value = info.getValue();
          return value ? new Date(value).toLocaleString() : "";
        },
      }),
    ];

    const cellRenderer = columns[0].cell;
    const mockInfo = { getValue: () => "2023-05-15T14:30:00Z" };
    const result = cellRenderer(mockInfo);

    expect(result).toBe(new Date("2023-05-15T14:30:00Z").toLocaleString());
  });

  it("handles empty arrival time", () => {
    // Test the arrival time cell formatter with null value
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const columnHelper = require("@tanstack/react-table").createColumnHelper();
    const columns = [
      columnHelper.accessor("arrivalTime", {
        header: "Arrival Time",
        size: 180,
        cell: (info: any) => {
          const value = info.getValue();
          return value ? new Date(value).toLocaleString() : "";
        },
      }),
    ];

    const cellRenderer = columns[0].cell;
    const mockInfo = { getValue: () => null };
    const result = cellRenderer(mockInfo);

    expect(result).toBe("");
  });

  it("passes correct initial pagination", () => {
    render(<WorkstationOrderStatusTable {...defaultProps} />);

    expect(screen.getByTestId("datagrid")).toHaveAttribute(
      "data-page-index",
      "0",
    );
    expect(screen.getByTestId("datagrid")).toHaveAttribute(
      "data-page-size",
      "50",
    );
  });

  it("passes correct initial sorting", () => {
    render(<WorkstationOrderStatusTable {...defaultProps} />);

    expect(screen.getByTestId("datagrid")).toHaveAttribute(
      "data-sort-id",
      "arrivalTime",
    );
    expect(screen.getByTestId("datagrid")).toHaveAttribute(
      "data-sort-desc",
      "true",
    );
  });
});
