.tableContainer {
  height: calc(100vh - 170px);
  display: flex;
  flex-direction: column;
}

.tableWrapper {
  height: 100%;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  background-color: var(--cds-layer-01);
  border: 1px solid var(--cds-border-subtle-01);
  border-radius: 4px;
}

.tableTitle {
  padding: 1rem 0.5rem;
  font-size: 1.125rem;
  background-color: var(--cds-layer-01);
}

.datagridWrapper {
  flex: 1;
  overflow: hidden;
}
.emptyCell {
  color: #8d8d8d;
}

.statusContainer {
  display: flex;
  align-items: center;
}

.statusIconDelayed {
  fill: #f1c21b;
  margin-right: 8px;
}

.statusIconActive {
  fill: #0f62fe;
  margin-right: 8px;
}

.statusIconAssigned {
  fill: #6f6f6f;
  margin-right: 8px;
}

.dwellTimeExceeded {
  color: #da1e28;
  font-weight: 600;
}
