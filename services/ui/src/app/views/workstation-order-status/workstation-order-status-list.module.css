.container {
  flex: 1;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.content {
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.filterTag {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.titleContainer {
  display: flex;
  align-items: center;
}

.titleLink {
  color: inherit;
  text-decoration: none;
}

.tableSection {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.tableTitle {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--cds-text-primary);
}
