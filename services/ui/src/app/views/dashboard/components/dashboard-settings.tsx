import { <PERSON><PERSON><PERSON>, Timer } from "@carbon/icons-react";
import {
  ComboBox,
  TextInput,
  Toggle,
  FilterableMultiSelect,
  NumberInput,
} from "@carbon/react";
import { OptionsAccordionGroup } from "../../../components/options/options-accordion-group/options-accordion-group";
import { OptionsAccordion } from "../../../components/options/options-accordion/options-accordion";
import { OptionsContainer } from "../../../components/options/options-container/options-container";
import { DatePeriod, DatePeriodOption } from "../../../types";
import type { BaseViewOptionsProps } from "../../view-registry.types";
import type { DashboardOptions } from "../dashboard";
import { TFunction } from "i18next";
import { useTranslation } from "react-i18next";
import { useEffect } from "react";

interface DashboardSettingsProps
  extends BaseViewOptionsProps<DashboardOptions> {
  options: DashboardOptions;
}

// Date period options for the ComboBox
export const datePeriodOptions: (t: TFunction) => DatePeriodOption[] = (t) => [
  { id: DatePeriod.today, text: "Today", label: t("Today", "Today") },
  {
    id: DatePeriod.yesterday,
    text: "Yesterday",
    label: t("Yesterday", "Yesterday"),
  },
  {
    id: DatePeriod.thisWeek,
    text: "This Week",
    label: t("This Week", "This Week"),
  },
  {
    id: DatePeriod.lastWeek,
    text: "Last Week",
    label: t("Last Week", "Last Week"),
  },
  {
    id: DatePeriod.thisMonth,
    text: "This Month",
    label: t("This Month", "This Month"),
  },
  {
    id: DatePeriod.lastMonth,
    text: "Last Month",
    label: t("Last Month", "Last Month"),
  },
  {
    id: DatePeriod.last7days,
    text: "Last 7 Days",
    label: t("Last 7 Days", "Last 7 Days"),
  },
  {
    id: DatePeriod.last14days,
    text: "Last 14 Days",
    label: t("Last 14 Days", "Last 14 Days"),
  },
  {
    id: DatePeriod.last30days,
    text: "Last 30 Days",
    label: t("Last 30 Days", "Last 30 Days"),
  },
];

type ComboBoxChangeData = {
  selectedItem?: DatePeriodOption | null;
};

export const DashboardSettings = ({
  options,
  onChange,
  onClose,
  onSave,
}: DashboardSettingsProps) => {
  const { t } = useTranslation();

  // Ensure default date range is included in available ranges when component loads
  useEffect(() => {
    if (
      options.showDateRange &&
      options.defaultDatePeriodRange &&
      typeof options.defaultDatePeriodRange === "string" &&
      options.availableDateRanges &&
      !options.availableDateRanges.includes(options.defaultDatePeriodRange)
    ) {
      const newAvailableRanges = [
        ...options.availableDateRanges,
        options.defaultDatePeriodRange,
      ];
      const newOptions = {
        ...options,
        availableDateRanges: newAvailableRanges,
      };
      onChange(newOptions);
    }
  }, []); // Empty dependency array - only run on mount

  const handleDefaultDateRangeChange = (data: ComboBoxChangeData) => {
    if (data.selectedItem) {
      const newDefaultRange = data.selectedItem.id;
      let newAvailableRanges = options.availableDateRanges || [];

      // If show date range is enabled and the new default range is not already in available ranges, add it
      if (
        options.showDateRange &&
        newDefaultRange &&
        typeof newDefaultRange === "string" &&
        !newAvailableRanges.includes(newDefaultRange)
      ) {
        newAvailableRanges = [...newAvailableRanges, newDefaultRange];
      }

      const newOptions = {
        ...options,
        defaultDatePeriodRange: newDefaultRange,
        availableDateRanges: newAvailableRanges,
      };
      onChange(newOptions);
    }
  };

  const handleShowDateRangeChange = (checked: boolean) => {
    let newAvailableRanges = options.availableDateRanges || [];

    // If turning on show date range and there's a valid default range that is a DatePeriod, ensure it's included in available ranges
    if (
      checked &&
      options.defaultDatePeriodRange &&
      typeof options.defaultDatePeriodRange === "string" &&
      !newAvailableRanges.includes(options.defaultDatePeriodRange)
    ) {
      newAvailableRanges = [
        ...newAvailableRanges,
        options.defaultDatePeriodRange,
      ];
    }

    const newOptions = {
      ...options,
      showDateRange: checked,
      availableDateRanges: newAvailableRanges,
    };
    onChange(newOptions);
  };

  const handleAvailableDateRangesChange = ({
    selectedItems,
  }: {
    selectedItems: DatePeriodOption[];
  }) => {
    const selectedRanges = selectedItems.map((item) => item.id);

    const newOptions = {
      ...options,
      availableDateRanges: selectedRanges,
    };
    onChange(newOptions);
  };

  // Calculate selected available date ranges, ensuring default is always included for actual usage
  let effectiveAvailableRanges = options.availableDateRanges || [];

  // Only ensure the default date range is included if we have a valid configuration
  // (i.e., if the user has selected at least one range OR if showDateRange is false)
  if (
    (!options.showDateRange || effectiveAvailableRanges.length > 0) &&
    options.defaultDatePeriodRange &&
    typeof options.defaultDatePeriodRange === "string" &&
    !effectiveAvailableRanges.includes(options.defaultDatePeriodRange)
  ) {
    effectiveAvailableRanges = [
      ...effectiveAvailableRanges,
      options.defaultDatePeriodRange,
    ];
  }

  // Use actual user selections for the FilterableMultiSelect, not effective ranges
  const userSelectedRanges = options.availableDateRanges || [];
  const selectedAvailableDateRanges =
    userSelectedRanges.length > 0
      ? datePeriodOptions(t).filter((option) =>
          userSelectedRanges.includes(option.id),
        )
      : [];

  const defaultDateRangeOptions =
    effectiveAvailableRanges.length > 0
      ? datePeriodOptions(t).filter((option) =>
          effectiveAvailableRanges.includes(option.id),
        )
      : datePeriodOptions(t);

  const selectedDatePeriod = defaultDateRangeOptions.find(
    (option) => option.id === options.defaultDatePeriodRange,
  );

  const handleAutoRefreshToggle = (checked: boolean) => {
    const newOptions = {
      ...options,
      autoRefreshEnabled: checked,
    };
    onChange(newOptions);
  };

  const handleAutoRefreshIntervalChange = (value: number) => {
    const newOptions = {
      ...options,
      autoRefreshInterval: value,
    };
    onChange(newOptions);
  };

  const MIN_INTERVAL = 30; // 30 seconds
  const MAX_INTERVAL = 86400; // 24 hours in seconds

  // Validation: Check if date range picker is enabled but no available ranges are selected
  const isInvalidDateRangeConfig =
    options.showDateRange &&
    (!options.availableDateRanges || options.availableDateRanges.length === 0);

  return (
    <OptionsContainer
      onClose={onClose}
      onSave={onSave}
      saveButtonDisabled={isInvalidDateRangeConfig}
    >
      <OptionsAccordion>
        <OptionsAccordionGroup
          id="display"
          title="Display"
          icon={<Analytics size={20} />}
        >
          <TextInput
            id="dashboard-name"
            labelText="Dashboard Name"
            value={options.title}
            onChange={(e) => onChange({ ...options, title: e.target.value })}
          />
          <ComboBox
            id="default-date-range"
            titleText="Default Date Range"
            helperText="The default date range to use for the dashboard"
            items={defaultDateRangeOptions}
            initialSelectedItem={selectedDatePeriod}
            onChange={handleDefaultDateRangeChange}
            placeholder="Select a default date range"
          />
          <Toggle
            id="show-date-range-toggle"
            labelText="Show Date Range Picker"
            toggled={options.showDateRange}
            onToggle={handleShowDateRangeChange}
            labelA=""
            labelB=""
          />
          {options.showDateRange && (
            <FilterableMultiSelect
              id="available-date-ranges"
              titleText="Available Date Ranges"
              helperText={
                isInvalidDateRangeConfig
                  ? "You must select at least one date range when the date range picker is enabled"
                  : "Select which date ranges are available in the dropdown"
              }
              invalid={isInvalidDateRangeConfig}
              invalidText={
                isInvalidDateRangeConfig
                  ? "At least one date range must be selected"
                  : ""
              }
              items={datePeriodOptions(t)}
              initialSelectedItems={selectedAvailableDateRanges}
              onChange={handleAvailableDateRangesChange}
              placeholder="Select available date ranges"
              itemToString={(item) => (item ? item.label : "")}
            />
          )}
        </OptionsAccordionGroup>

        <OptionsAccordionGroup
          id="auto-refresh"
          title="Auto Refresh"
          icon={<Timer size={20} />}
        >
          <Toggle
            id="auto-refresh-toggle"
            labelText="Enable Auto Refresh"
            toggled={options.autoRefreshEnabled ?? false}
            onToggle={handleAutoRefreshToggle}
            labelA=""
            labelB=""
          />
          {(options.autoRefreshEnabled ?? false) && (
            <NumberInput
              id="auto-refresh-interval"
              label="Refresh Interval (seconds)"
              helperText={`Min: ${MIN_INTERVAL}s, Max: ${MAX_INTERVAL}s (24 hours)`}
              value={options.autoRefreshInterval ?? 60}
              min={MIN_INTERVAL}
              max={MAX_INTERVAL}
              step={1}
              onChange={(_e, { value }) => {
                const numValue = parseInt(value.toString(), 10);
                if (!isNaN(numValue)) {
                  handleAutoRefreshIntervalChange(numValue);
                }
              }}
            />
          )}
        </OptionsAccordionGroup>
      </OptionsAccordion>
    </OptionsContainer>
  );
};
