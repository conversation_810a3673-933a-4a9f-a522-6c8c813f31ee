/* Container Layout */

.layoutWrapper {
  display: flex;
  height: calc(100vh - 48px);
}

.chatContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: linear-gradient(
    to top,
    var(--cds-ai-aura-start, #4589ff1a),
    var(--cds-ai-aura-end, #00000000)
  );
  overflow-x: hidden;
  flex-grow: 1;
  transition: margin-right 0.3s ease;
}

.chatShifted {
  margin-right: 320px; /* width of sidePanel */
}

.chatMessages {
  flex: 1;
  overflow-y: auto;
  padding: 2em;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  scrollbar-gutter: stable;
  scrollbar-color: var(--cds-border-subtle) transparent;
}

.chatRow {
  margin: 0 auto;
  width: 100%;
  max-width: 980px;
  display: flex;
  gap: 0.75rem;
  align-items: flex-start;
}

/* Message Wrappers */

.agentMessageWrapper {
  justify-content: flex-start;
}

.userMessageWrapper {
  justify-content: flex-end;
}

.userMessageWrapper .messageActions {
  justify-content: flex-end;
}

/* Message Bubbles */

.agentMessage,
.userMessage {
  animation: fadeIn 0.3s ease-out;
}

.agentMessage {
  background: transparent;
  padding: 0.5em 0 0.5em 1rem;
  color: var(--cds-text-primary, #161616);
  font-weight: 400;
  border: none;
  box-shadow: none;
}

.userMessage {
  background: var(--cds-ai-aura-start, #262626);
  color: var(--cds-text-primary, #f4f4f4);
  padding: 0.75rem 1rem;
  max-width: 600px;
  line-height: normal;
  word-break: break-word;
  text-align: left;
}

.timestamp {
  font-size: 12px;
  color: var(--cds-text-helper, #8d8d8d);
  margin-top: 0.5rem;
  text-align: right;
}

/* Input Bar */

.chatInput {
  flex: 1;
  resize: none;
  overflow-y: auto;
  max-height: 300px;
  font-size: 1rem;
  padding: 0.75rem 1rem;
  border: none;
  background: var(--cds-layer, #262626);
  color: var(--cds-text-primary, #f4f4f4);
  line-height: 1.5;
  outline: none;
  overflow-y: auto;
  scrollbar-color: var(--cds-border-subtle) transparent;
}

.chatInput:focus {
  outline: 2px solid var(--cds-focus, #0f62fe); /* CDS default focus color */
  border-color: var(--cds-focus, #0f62fe);
  box-shadow: 0 0 0 2px var(--cds-focus, #0f62fe); /* Carbon-like halo */
}

.chatInputBar {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  width: 980px;
  margin: 0 auto;
  background: var(--cds-field);
  border: 1px solid var(--cds-border-subtle);
}

/* Centered on first load */
.chatInputBar.centered {
  position: absolute;
  top: calc(50% - 48px);
  left: 50%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05), 0 0 1px rgba(0, 0, 0, 0.1);
  transform: translate(-50%, -50%);
}

/* Docked after input */
.chatInputBar.docked {
  position: sticky;
  bottom: 0;
  left: 0;
  width: calc(100% - 2rem);
  max-width: 980px;
  transform: none;
}

.chatSendButton {
  padding-top: 0.95rem;
}

.verifyHelperText {
  text-align: center;
  padding: 0.75rem 0;
}

.verifyHelperText p {
  font-size: 14px;
  color: var(--cds-text-helper);
}

/* Action Buttons */

.messageActions {
  display: flex;
  gap: 0.5rem;
  padding: 0 0 0 0.5em;
  margin-top: 0.5rem;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.actionBtn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  color: var(--cds-icon-secondary, #6f6f6f);
  transform: translateY(4px);
  opacity: 0;
  transition:
    opacity 0.3s ease,
    transform 0.3s ease,
    color 0.2s ease,
    background-color 0.2s ease;
}

.actionBtn:hover {
  transform: scale(1.1);
  color: var(--cds-icon-primary, #161616);
  background-color: var(--cds-layer-hover, #e0e0e0);
}

/* Hover/Active Visibility */
.latestMessage .messageActions,
.hoverableMessage:hover .messageActions {
  opacity: 1;
  pointer-events: auto;
}

.latestMessage .actionBtn,
.hoverableMessage:hover .actionBtn {
  opacity: 1;
  transform: translateY(0);
}

/* Loading */
.agentLoadingRow {
  display: flex;
  padding-left: 1rem;
  align-items: center;
  gap: 1rem;
  min-height: 2rem;
  animation: fadeIn 0.4s ease-out;
}

.agentSpinner {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.agentLoadingText {
  font-weight: 500;
  font-size: 1rem;
  color: var(--cds-text-secondary, #6f6f6f);
  animation: fadeText 0.6s ease;
  transition: opacity 0.4s ease;
}

@keyframes fadeText {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animations */

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.toastWrapper {
  position: fixed;
  top: calc(1rem + 48px);
  right: 1rem;
  z-index: 9999;
}
