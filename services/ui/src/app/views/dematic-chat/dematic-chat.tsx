import { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, ToastNotification } from "@carbon/react";
import AgentMessage from "./agent-message";
import UserMessage from "./user-message";
import AgentLoading from "./agent-loading";
import { ViewBar } from "../../components/view-bar/view-bar";
import styles from "./dematic-chat.module.scss";
import { ictApi } from "../../api/ict-api";
import { InputBar } from "./input-bar";
import { useConfigSetting } from "../../config/hooks/use-config";
import type { components } from "@ict/sdk/openapi-react-query";

/**
 * dematic-chat.tsx
 *
 * Main UI shell for the AI agent chat interface in Control Tower.
 *
 * Features:
 * - Accepts user input and displays messages in a conversational thread layout.
 * - Simulates agent "thinking" via animated steps using `AgentLoading`.
 * - Displays agent reply using `AgentMessage` and user input using `UserMessage`.
 * - Allows thumbs up/down feedback per message and shows toasts for feedback confirmation.
 * - Input field is centered initially, then docks after first use.
 * - Fully responsive and styled with Carbon tokens and custom theming (see `dematic-chat.module.scss`).
 */

// Message object type for both user and agent
type Message = {
  id: number;
  type: "user" | "agent";
  text: string;
  timestamp: string;
};

// Type for gold questions API response
type GoldQuestionsResponse = {
  goldQuestions: components["schemas"]["DataExplorerRecommendationsData"][];
};

export default function DematicChat() {
  // Tracks if the user has interacted with the input at all (for initial centering transition)
  const [inputHasBeenUsed, setInputHasBeenUsed] = useState(false);

  // Stores the current value of the user's input
  const [input, setInput] = useState("");

  // Message history array, storing user and agent messages in order
  const [messages, setMessages] = useState<Message[]>([]);

  // Indicates whether the agent is currently "thinking" (disables input, shows animation)
  const [isThinking, setIsThinking] = useState(false);

  // Controls which toast notification is shown (used for thumbs up/down feedback)
  const [toast, setToast] = useState<null | "up" | "down">(null);

  // Forces remount of AgentLoading component to replay its animation
  const [agentLoadingKey, setAgentLoadingKey] = useState(0);

  // Stores the current session ID for the chat
  const [sessionId, setSessionId] = useState<string | null>(null);

  // Ref used to scroll the view to the bottom after new messages
  const messageEndRef = useRef<HTMLDivElement | null>(null);

  // Ref for the textarea, so it can auto-resize based on content
  const inputRef = useRef<HTMLTextAreaElement | null>(null);

  const isMountedRef = useRef(true);

  // Get the config setting for number of recommendations to display
  const { setting: recommendationCountSetting } = useConfigSetting(
    "data-explorer-default-recommendations",
  );
  const maxRecommendations = (recommendationCountSetting?.value as number) || 4;

  // Gold questions fetched from API
  const [goldQuestions, setGoldQuestions] = useState<string[]>([]);
  const [isLoadingGoldQuestions, setIsLoadingGoldQuestions] = useState(true);
  const [showAllQuestions, setShowAllQuestions] = useState(false);

  // Fallback reference bank in case API fails
  const fallbackReferenceBank = [
    "What are the top 3 busiest locations in the last 6 months?",
    "Who are the top 5 best operators in the last year based on the number of orders?",
    "Give me a monthly breakdown of the number of completed pick orders for the last 3 months.",
    "What were the top 5 SKUs by quantity picked last year?",
    "How much inventory was assigned in each day of this week?",
    "Give me a daily breakdown of the number of orders active each day last month.",
  ];

  // Fetch gold questions on component mount
  useEffect(() => {
    const fetchGoldQuestions = async () => {
      try {
        const response = await ictApi.fetchClient.GET(
          "/data-explorer/gold-questions",
          {
            params: {},
          },
        );

        const data = response.data as GoldQuestionsResponse;
        if (data?.goldQuestions) {
          const questions = data.goldQuestions
            .map((item: { prompt: string }) => item.prompt)
            .filter((prompt: string) => {
              // More robust filtering to catch edge cases
              if (!prompt || typeof prompt !== "string") return false;
              const trimmed = prompt.trim();
              return (
                trimmed.length > 0 && trimmed !== "" && !/^\s*$/.test(trimmed)
              );
            });

          if (isMountedRef.current) {
            setGoldQuestions(questions);
          }
        } else {
          if (isMountedRef.current) {
            setGoldQuestions(fallbackReferenceBank);
          }
        }
      } catch (_error) {
        if (isMountedRef.current) {
          setGoldQuestions(fallbackReferenceBank);
        }
      } finally {
        if (isMountedRef.current) {
          setIsLoadingGoldQuestions(false);
        }
      }
    };

    fetchGoldQuestions();
  }, []);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Auto-scroll to the latest message
  useEffect(() => {
    messageEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, isThinking]);

  // Auto-resize the input field as the user types
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.style.height = "auto";
      inputRef.current.style.height = `${Math.min(inputRef.current.scrollHeight, 300)}px`;
    }
  }, [input]);

  // Called when the user hits enter or clicks the send button
  const sendMessage = async () => {
    const messageText = input.trim();

    if (!messageText || isThinking) return;

    // Triggers input bar to dock
    if (!inputHasBeenUsed) {
      setInputHasBeenUsed(true);
    }

    // Timestamp to display below user's message
    const timestamp = new Date().toLocaleTimeString([], {
      hour: "numeric",
      minute: "2-digit",
    });

    const userMessage: Message = {
      id: Date.now(),
      type: "user",
      text: messageText,
      timestamp,
    };

    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setIsThinking(true);
    setAgentLoadingKey((k) => k + 1);

    try {
      const response = await ictApi.fetchClient.GET(
        "/data-explorer/agentsearch",
        {
          params: {
            query: {
              searchText: messageText,
              sessionId: sessionId || undefined,
            },
          },
        },
      );

      if (!response.data) {
        throw new Error("No data received from API");
      }

      // Update session ID if this is the first message
      if (!sessionId && response.data.sessionId) {
        setSessionId(response.data.sessionId);
      }

      if (!response.data.messages || response.data.messages.length === 0) {
        // Handle case when no messages are returned
        const agentMessage = {
          id: Date.now(),
          type: "agent" as const,
          text: "I apologize, but I wasn't able to generate a response to your question. Please try rephrasing your question or contact support if the issue persists.",
          timestamp: "",
        };

        if (isMountedRef.current) {
          setMessages((prev) => [...prev, agentMessage]);
        }
        return;
      }

      // Only use the last message from the response
      const lastMessage =
        response.data.messages[response.data.messages.length - 1];

      // Check if the last message is empty or undefined
      const agentResponseText =
        lastMessage && lastMessage.trim()
          ? lastMessage
          : "I received your question but wasn't able to provide a meaningful response. Please try rephrasing your question or ask something else.";

      const agentMessage = {
        id: Date.now(),
        type: "agent" as const,
        text: agentResponseText,
        timestamp: "",
      };

      if (isMountedRef.current) {
        setMessages((prev) => [...prev, agentMessage]);
      }
    } catch (_error) {
      // Add error message to chat
      if (isMountedRef.current) {
        setMessages((prev) => [
          ...prev,
          {
            id: Date.now(),
            type: "agent",
            text: "Sorry, I encountered an error while processing your request. Please try again.",
            timestamp: "",
          },
        ]);
      }
    } finally {
      if (isMountedRef.current) {
        setIsThinking(false);
      }
    }
  };

  // Trigger toast for feedback (up/down). Auto-dismisses after 3s
  const showToast = (type: "up" | "down") => {
    if (!isMountedRef.current) return;
    setToast(type);
    setTimeout(() => {
      if (isMountedRef.current) {
        setToast(null);
      }
    }, 3000);
  };

  // Cancels the agent loading state
  const cancelThinking = () => {
    if (!isMountedRef.current) return;
    setIsThinking(false);

    // Add a cancellation message to the chat
    setMessages((prev) => [
      ...prev,
      {
        id: Date.now(),
        type: "agent",
        text: "⚠️ Request canceled by the user.",
        timestamp: "",
      },
    ]);
  };

  const centeredView = () => (
    <div data-testid="centered-view" className={styles.centeredContainer}>
      <div className={styles.centeredTitle}>
        <h3>How can I assist with your warehouse data today?</h3>
      </div>
      <InputBar
        inputRef={inputRef}
        inputValue={input}
        onInputChange={setInput}
        onSendMessage={sendMessage}
        onCancelThinking={cancelThinking}
        isThinking={isThinking}
      />
      <div
        data-testid="reference-bank-container"
        className={styles.referenceBankContainer}
      >
        <div className={styles.referenceContainer}>
          {isLoadingGoldQuestions ? (
            <div>Loading questions...</div>
          ) : (
            <>
              {(showAllQuestions
                ? goldQuestions
                : goldQuestions.slice(0, maxRecommendations)
              ).map((reference: string, index: number) => (
                <Button
                  data-testid="reference-bank-button"
                  key={`${reference}-${index}`}
                  className={styles.reference}
                  kind="tertiary"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setInput(reference);
                  }}
                >
                  {reference}
                </Button>
              ))}
              {goldQuestions.length > maxRecommendations && (
                <Button
                  data-testid="show-more-questions-button"
                  kind="tertiary"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setShowAllQuestions(!showAllQuestions);
                  }}
                >
                  {showAllQuestions
                    ? "Show less"
                    : `Show more (${goldQuestions.length - maxRecommendations} more)`}
                </Button>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );

  const dockedView = () => (
    <div data-testid="docked-view" className={styles.dockedContainer}>
      {/* Main chat area */}
      <div className={styles.chatMessages}>
        {messages.map((msg, i) => {
          const isLast = i === messages.length - 1;
          return msg.type === "agent" ? (
            <AgentMessage
              key={msg.id}
              text={msg.text}
              isLatest={isLast}
              onFeedback={showToast}
            />
          ) : (
            <UserMessage
              key={msg.id}
              text={msg.text}
              timestamp={msg.timestamp}
            />
          );
        })}

        {isThinking && <AgentLoading key={agentLoadingKey} onDone={() => {}} />}

        <div ref={messageEndRef} />
      </div>

      <InputBar
        inputRef={inputRef}
        inputValue={input}
        onInputChange={setInput}
        onSendMessage={sendMessage}
        onCancelThinking={cancelThinking}
        isThinking={isThinking}
      />
      <div className={styles.verifyHelperText}>
        <p>Always verify critical data independently.</p>
      </div>
    </div>
  );

  return (
    <div className={styles.layoutWrapper}>
      <div className={styles.chatContainer}>
        {/* Top bar */}
        <ViewBar title={"Dematic Chat"} />

        {(() => {
          return messages.length > 0 ? dockedView() : centeredView();
        })()}
      </div>

      {/* Optional feedback toast in upper-right */}
      {toast && (
        <div className={styles.toastWrapper}>
          <ToastNotification
            kind="success"
            lowContrast
            title="Feedback submitted"
            timeout={3000}
          />
        </div>
      )}
    </div>
  );
}
