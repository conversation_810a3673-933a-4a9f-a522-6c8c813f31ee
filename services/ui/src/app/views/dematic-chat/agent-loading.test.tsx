import { vi } from "vitest";
import { render, screen, act } from "../../../test-utils";
import AgentLoading from "./agent-loading";
import styles from "./dematic-chat.module.scss";

// Mock window timer functions
vi.spyOn(window, "setInterval");
vi.spyOn(window, "clearInterval");
vi.spyOn(window, "setTimeout");
vi.spyOn(window, "clearTimeout");

// Mock Carbon React components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    Loading: ({ description }: any) => (
      <div data-testid="loading-spinner" data-description={description}>
        Loading...
      </div>
    ),
  };
});

describe("AgentLoading", () => {
  beforeAll(() => {
    vi.useFakeTimers();
  });

  afterAll(() => {
    vi.useRealTimers();
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render initial loading state", () => {
    render(<AgentLoading onDone={() => {}} />);
    expect(screen.getByTestId("loading-spinner")).toBeInTheDocument();
    expect(screen.getByText("Thinking...")).toBeInTheDocument();
  });

  it("should progress through loading steps", () => {
    render(<AgentLoading onDone={() => {}} />);

    // Initial state
    expect(screen.getByText("Thinking...")).toBeInTheDocument();

    // Since there's only one step, advancing time shouldn't change the text
    act(() => {
      vi.advanceTimersByTime(2400);
    });
    expect(screen.getByText("Thinking...")).toBeInTheDocument();
  });

  it("should call onDone after timeout", () => {
    const mockOnDone = vi.fn();
    render(<AgentLoading onDone={mockOnDone} />);

    // Advance through the timeout (1 step * 2400ms + 500ms buffer)
    act(() => {
      vi.advanceTimersByTime(2900);
    });

    expect(mockOnDone).toHaveBeenCalled();
  });

  it("should stay at the same step", () => {
    render(<AgentLoading onDone={() => {}} />);

    // Since there's only one step, it should stay the same
    act(() => {
      vi.advanceTimersByTime(2400);
    });

    expect(screen.getByText("Thinking...")).toBeInTheDocument();
  });

  it("should clean up timers on unmount", () => {
    const clearIntervalSpy = vi.spyOn(window, "clearInterval");
    const clearTimeoutSpy = vi.spyOn(window, "clearTimeout");

    const { unmount } = render(<AgentLoading onDone={() => {}} />);
    unmount();

    expect(clearIntervalSpy).toHaveBeenCalled();
    expect(clearTimeoutSpy).toHaveBeenCalled();
  });

  it("should have correct CSS classes", () => {
    render(<AgentLoading onDone={() => {}} />);
    const wrapper = screen.getByTestId("loading-spinner").closest("div");
    const loadingRow = wrapper?.parentElement;
    expect(loadingRow).toHaveClass(styles.agentLoadingRow);
    expect(loadingRow?.parentElement).toHaveClass(styles.agentMessageWrapper);
  });
});
