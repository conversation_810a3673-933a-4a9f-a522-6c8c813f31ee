import { Button } from "@carbon/react";
import styles from "./dematic-chat.module.scss";
import { Send, StopFilled } from "@carbon/icons-react";

export interface InputBarProps {
  inputRef: React.RefObject<HTMLTextAreaElement | null>;
  inputValue: string;
  onInputChange: (value: string) => void;
  onSendMessage: () => void;
  onCancelThinking: () => void;
  isThinking: boolean;
}

export const InputBar: React.FC<InputBarProps> = ({
  inputRef,
  inputValue,
  onInputChange,
  onSendMessage,
  onCancelThinking,
  isThinking,
}) => (
  <div className={styles.chatInputBar}>
    <textarea
      ref={inputRef}
      className={styles.chatInput}
      placeholder="Type a warehouse question or command..."
      value={inputValue}
      onChange={(e) => onInputChange(e.target.value)}
      onKeyDown={(e) => {
        if (e.key === "Enter" && !e.shiftKey) {
          e.preventDefault();
          onSendMessage();
        }
      }}
      rows={1}
      disabled={isThinking}
    />
    <Button
      kind="ghost"
      hasIconOnly
      renderIcon={isThinking ? StopFilled : Send}
      iconDescription={isThinking ? "Stop" : "Send"}
      tooltipAlignment="center"
      size="lg"
      className={styles.chatSendButton}
      onClick={isThinking ? onCancelThinking : onSendMessage}
      disabled={!inputValue.trim() && !isThinking}
    />
  </div>
);
