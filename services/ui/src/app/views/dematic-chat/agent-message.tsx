import React, { useState } from "react";
import styles from "./dematic-chat.module.scss";
import {
  Copy,
  CopyFile,
  ThumbsUp,
  ThumbsDown,
  ThumbsUpFilled,
  ThumbsDownFilled,
} from "@carbon/icons-react";
import { Button } from "@carbon/react";

type Props = {
  text: string;
  isLatest: boolean;
  onFeedback?: (type: "up" | "down") => void;
};

export default function AgentMessage({ text, isLatest, onFeedback }: Props) {
  const [copied, setCopied] = useState(false);
  const [feedback, setFeedback] = useState<null | "up" | "down">(null);

  const handleCopy = () => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 3000);
  };

  const handleFeedback = (type: "up" | "down") => {
    const newType = feedback === type ? null : type; // Toggle off if clicked again
    setFeedback(newType);
    if (newType) onFeedback?.(newType);
  };

  return (
    <div
      className={`${styles.chatRow} ${styles.agentMessageWrapper} ${
        isLatest ? styles.latestMessage : styles.hoverableMessage
      }`}
    >
      <div>
        <div className={styles.agentMessage}>{text}</div>

        <div className={styles.messageActions}>
          <Button
            kind="ghost"
            hasIconOnly
            size="sm"
            tooltipPosition="bottom"
            iconDescription="Copy"
            renderIcon={copied ? CopyFile : Copy}
            onClick={handleCopy}
          />
          <Button
            kind="ghost"
            hasIconOnly
            size="sm"
            tooltipPosition="bottom"
            iconDescription={
              feedback === "up" ? "Remove feedback" : "Mark as helpful"
            }
            renderIcon={feedback === "up" ? ThumbsUpFilled : ThumbsUp}
            onClick={() => handleFeedback("up")}
          />

          <Button
            kind="ghost"
            hasIconOnly
            size="sm"
            tooltipPosition="bottom"
            iconDescription={
              feedback === "down" ? "Remove feedback" : "Mark as unhelpful"
            }
            renderIcon={feedback === "down" ? ThumbsDownFilled : ThumbsDown}
            onClick={() => handleFeedback("down")}
          />
        </div>
      </div>
    </div>
  );
}
