import { vi } from "vitest";
import { fireEvent, render, screen, act } from "../../../test-utils";
import AgentMessage from "./agent-message";
import styles from "./dematic-chat.module.scss";

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn(),
  },
});

// Mock Carbon icons
vi.mock("@carbon/icons-react", () => ({
  Copy: { name: "Copy" },
  CopyFile: { name: "CopyFile" },
  ThumbsUp: { name: "ThumbsUp" },
  ThumbsDown: { name: "ThumbsDown" },
  ThumbsUpFilled: { name: "ThumbsUpFilled" },
  ThumbsDownFilled: { name: "ThumbsDownFilled" },
}));

// Mock Carbon React components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    Button: ({ onClick, iconDescription, renderIcon }: any) => (
      <button
        data-testid={`button-${iconDescription}`}
        onClick={onClick}
        data-icon={renderIcon?.name}
      >
        {iconDescription}
      </button>
    ),
  };
});

describe("AgentMessage", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render message text", () => {
    render(<AgentMessage text="Test message" isLatest={false} />);
    expect(screen.getByText("Test message")).toBeInTheDocument();
  });

  it("should have correct classes based on isLatest prop", () => {
    const { rerender } = render(<AgentMessage text="Test" isLatest={true} />);
    expect(screen.getByText("Test").closest("div")).toHaveClass(
      styles.agentMessage,
    );

    rerender(<AgentMessage text="Test" isLatest={false} />);
    expect(screen.getByText("Test").closest("div")).toHaveClass(
      styles.agentMessage,
    );
  });

  it("should copy text to clipboard when copy button is clicked", async () => {
    render(<AgentMessage text="Test message" isLatest={false} />);
    const copyButton = screen.getByTestId("button-Copy");
    fireEvent.click(copyButton);
    expect(navigator.clipboard.writeText).toHaveBeenCalledWith("Test message");
  });

  it("should show copied state temporarily after copying", async () => {
    vi.useFakeTimers();
    render(<AgentMessage text="Test message" isLatest={false} />);
    const copyButton = screen.getByTestId("button-Copy");

    // Click copy button
    fireEvent.click(copyButton);
    expect(copyButton).toHaveAttribute("data-icon", "CopyFile");

    // Advance timers
    act(() => {
      vi.advanceTimersByTime(3000);
    });

    // Should revert back to copy icon
    expect(copyButton).toHaveAttribute("data-icon", "Copy");
    vi.useRealTimers();
  });

  it("should handle thumbs up feedback", () => {
    const mockOnFeedback = vi.fn();
    render(
      <AgentMessage text="Test" onFeedback={mockOnFeedback} isLatest={false} />,
    );

    const thumbsUpButton = screen.getByTestId("button-Mark as helpful");
    fireEvent.click(thumbsUpButton);

    expect(mockOnFeedback).toHaveBeenCalledWith("up");
    expect(thumbsUpButton).toHaveAttribute("data-icon", "ThumbsUpFilled");
  });

  it("should handle thumbs down feedback", () => {
    const mockOnFeedback = vi.fn();
    render(
      <AgentMessage text="Test" onFeedback={mockOnFeedback} isLatest={false} />,
    );

    const thumbsDownButton = screen.getByTestId("button-Mark as unhelpful");
    fireEvent.click(thumbsDownButton);

    expect(mockOnFeedback).toHaveBeenCalledWith("down");
    expect(thumbsDownButton).toHaveAttribute("data-icon", "ThumbsDownFilled");
  });

  it("should toggle off feedback when clicked again", () => {
    const mockOnFeedback = vi.fn();
    render(
      <AgentMessage text="Test" onFeedback={mockOnFeedback} isLatest={false} />,
    );

    const thumbsUpButton = screen.getByTestId("button-Mark as helpful");

    // Click first time
    fireEvent.click(thumbsUpButton);
    expect(mockOnFeedback).toHaveBeenCalledWith("up");

    // Click second time
    fireEvent.click(thumbsUpButton);
    expect(mockOnFeedback).toHaveBeenCalledWith("up"); // Should still be "up" since we're toggling
  });

  it("should not call onFeedback if not provided", () => {
    render(<AgentMessage text="Test" isLatest={false} />);

    const thumbsUpButton = screen.getByTestId("button-Mark as helpful");
    fireEvent.click(thumbsUpButton);

    // Should not throw error and should still update the UI
    expect(thumbsUpButton).toHaveAttribute("data-icon", "ThumbsUpFilled");
  });
});
