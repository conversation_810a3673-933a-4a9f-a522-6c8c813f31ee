import { vi } from "vitest";
import { fireEvent, render, screen, waitFor } from "../../../test-utils";
import { ictApi } from "../../api/ict-api";
import DematicChat from "./dematic-chat";

// Mock scrollIntoView
const mockScrollIntoView = vi.fn();
Element.prototype.scrollIntoView = mockScrollIntoView;

// Mock the ictApi
vi.mock("../../api/ict-api", () => ({
  ictApi: {
    fetchClient: {
      GET: vi.fn(),
    },
    client: {
      useQuery: vi.fn(),
    },
  },
}));

// Mock the useConfigSetting hook
vi.mock("../../config/hooks/use-config", () => ({
  useConfigSetting: vi.fn(() => ({
    setting: { value: 4 },
    isLoading: false,
    error: null,
  })),
}));

// Mock the ViewBar component
vi.mock("../../components/view-bar/view-bar", () => ({
  ViewBar: ({
    title,
    children,
  }: {
    title: string;
    children: React.ReactNode;
  }) => (
    <div data-testid="view-bar">
      <h1>{title}</h1>
      <div data-testid="view-bar-children">{children}</div>
    </div>
  ),
}));

// Mock Carbon React components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    Button: ({ onClick, disabled, iconDescription }: any) => (
      <button
        data-testid="carbon-button"
        onClick={onClick}
        disabled={disabled}
        data-icon-description={iconDescription}
      >
        {iconDescription}
      </button>
    ),
    ToastNotification: ({ kind, title, subtitle }: any) => (
      <div data-testid="toast-notification" data-kind={kind}>
        <h3>{title}</h3>
        <p>{subtitle}</p>
      </div>
    ),
  };
});

// Mock the child components
vi.mock("./agent-message", () => ({
  default: ({ text, isLatest, onFeedback }: any) => (
    <div data-testid="agent-message" data-is-latest={isLatest}>
      {text}
      <button onClick={() => onFeedback?.("up")}>Feedback</button>
    </div>
  ),
}));

vi.mock("./user-message", () => ({
  default: ({ text, timestamp }: any) => (
    <div data-testid="user-message">
      {text}
      <span data-testid="timestamp">{timestamp}</span>
    </div>
  ),
}));

vi.mock("./agent-loading", () => ({
  default: ({ onDone }: any) => (
    <div data-testid="agent-loading">
      Loading...
      <button onClick={onDone}>Done</button>
    </div>
  ),
}));

describe("DematicChat", () => {
  const mockGet = ictApi.fetchClient.GET as any;

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock different responses for different endpoints
    mockGet.mockImplementation((url: string) => {
      if (url === "/data-explorer/gold-questions") {
        return Promise.resolve({
          data: {
            goldQuestions: [
              {
                prompt:
                  "What are the most efficient pick locations in our warehouse?",
              },
              {
                prompt:
                  "How can we optimize our inventory distribution across zones?",
              },
              {
                prompt:
                  "What patterns do you see in our peak operational hours?",
              },
              {
                prompt: "Which equipment shows the highest utilization rates?",
              },
              {
                prompt:
                  "What are the top 3 busiest locations in the last 6 months?",
              },
              {
                prompt:
                  "Who are the top 5 best operators in the last year based on the number of orders?",
              },
            ],
          },
        });
      }

      // Default response for agentsearch
      return Promise.resolve({
        data: {
          sessionId: "test-session-123",
          messages: ["Test response from agent"],
        },
      });
    });
  });

  it("should render the view bar with correct title", () => {
    render(<DematicChat />);
    expect(screen.getByText("Dematic Chat")).toBeInTheDocument();
  });

  it("should show centered input initially", () => {
    render(<DematicChat />);
    const inputBar = screen.getByPlaceholderText(
      "Type a warehouse question or command...",
    );
    expect(inputBar).toBeInTheDocument();
    waitFor(() => {
      expect(screen.getByTestId("centered-view")).toBeVisible();
    });
  });

  it("should dock input after first use", async () => {
    render(<DematicChat />);
    const input = screen.getByPlaceholderText(
      "Type a warehouse question or command...",
    );

    // Type and send a message
    fireEvent.change(input, { target: { value: "Test message" } });
    fireEvent.click(screen.getByText("Send"));

    // Wait for the message to be sent
    await waitFor(() => {
      expect(screen.getByTestId("docked-view")).toBeVisible();
    });
  });

  it("should send message and display response", async () => {
    render(<DematicChat />);
    const input = screen.getByPlaceholderText(
      "Type a warehouse question or command...",
    );

    // Type and send a message
    fireEvent.change(input, { target: { value: "Test message" } });
    fireEvent.click(screen.getByText("Send"));

    // Check if API was called
    await waitFor(() => {
      expect(mockGet).toHaveBeenCalledWith(
        "/data-explorer/agentsearch",
        expect.objectContaining({
          params: {
            query: {
              searchText: "Test message",
            },
          },
        }),
      );
    });

    // Check if messages are displayed
    expect(screen.getByText("Test message")).toBeInTheDocument();
    expect(screen.getByText("Test response from agent")).toBeInTheDocument();
  });

  it("should handle API errors gracefully", async () => {
    mockGet.mockRejectedValue(new Error("API Error"));
    render(<DematicChat />);

    const input = screen.getByPlaceholderText(
      "Type a warehouse question or command...",
    );
    fireEvent.change(input, { target: { value: "Test message" } });
    fireEvent.click(screen.getByText("Send"));

    // Check if error message is displayed
    await waitFor(() => {
      expect(
        screen.getByText(/Sorry, I encountered an error/),
      ).toBeInTheDocument();
    });
  });

  it("should allow canceling a request", async () => {
    // Mock a slow response
    mockGet.mockImplementation(
      () => new Promise((resolve) => setTimeout(resolve, 1000)),
    );

    render(<DematicChat />);
    const input = screen.getByPlaceholderText(
      "Type a warehouse question or command...",
    );

    // Start a request
    fireEvent.change(input, { target: { value: "Test message" } });
    fireEvent.click(screen.getByText("Send"));

    // Cancel the request
    fireEvent.click(screen.getByText("Stop"));

    // Check if cancellation message is displayed
    await waitFor(() => {
      expect(
        screen.getByText("⚠️ Request canceled by the user."),
      ).toBeInTheDocument();
    });
  });

  it("should not send empty messages", () => {
    render(<DematicChat />);

    // Clear any calls from component mount (gold questions fetch)
    mockGet.mockClear();

    // Try to send empty message
    fireEvent.click(screen.getByText("Send"));

    expect(mockGet).not.toHaveBeenCalled();
  });

  it("should handle Enter key to send message", async () => {
    render(<DematicChat />);

    // Clear any calls from component mount (gold questions fetch)
    mockGet.mockClear();

    const input = screen.getByPlaceholderText(
      "Type a warehouse question or command...",
    );

    // Type and press Enter
    fireEvent.change(input, { target: { value: "Test message" } });
    fireEvent.keyDown(input, { key: "Enter" });

    await waitFor(() => {
      expect(mockGet).toHaveBeenCalled();
    });
  });

  it("should not send message on Shift+Enter", () => {
    render(<DematicChat />);

    // Clear any calls from component mount (gold questions fetch)
    mockGet.mockClear();

    const input = screen.getByPlaceholderText(
      "Type a warehouse question or command...",
    );

    // Type and press Shift+Enter
    fireEvent.change(input, { target: { value: "Test message" } });
    fireEvent.keyDown(input, { key: "Enter", shiftKey: true });

    expect(mockGet).not.toHaveBeenCalled();
  });

  it("should load and display gold questions from API", async () => {
    render(<DematicChat />);

    // Wait for gold questions to load
    await waitFor(() => {
      expect(mockGet).toHaveBeenCalledWith(
        "/data-explorer/gold-questions",
        expect.any(Object),
      );
    });

    const referenceBankContainer = screen.getByTestId(
      "reference-bank-container",
    );

    // Should have reference bank buttons (4 visible + show more button)
    const buttons = referenceBankContainer.querySelectorAll("button");
    expect(buttons.length).toBeGreaterThanOrEqual(4);

    // Since we're mocking the buttons, we can't check for the exact text content
    // but we can verify the structure is there
    expect(referenceBankContainer).toBeInTheDocument();
  });

  it("should render the title text", () => {
    render(<DematicChat />);
    const title = screen.getByText(
      "How can I assist with your warehouse data today?",
    );
    expect(title).toBeInTheDocument();
  });

  it("should use fallback questions when API fails", async () => {
    // Mock API failure for gold questions
    mockGet.mockImplementation((url: string) => {
      if (url === "/data-explorer/gold-questions") {
        return Promise.reject(new Error("API Error"));
      }

      // Default response for agentsearch
      return Promise.resolve({
        data: {
          sessionId: "test-session-123",
          messages: ["Test response from agent"],
        },
      });
    });

    render(<DematicChat />);

    // Wait for component to handle the API failure and use fallbacks
    await waitFor(() => {
      const referenceBankContainer = screen.getByTestId(
        "reference-bank-container",
      );
      expect(referenceBankContainer).toBeInTheDocument();
    });

    // Since we're using mocked buttons, we can't check for exact text
    // but we can verify the fallback handling worked
    const referenceBankContainer = screen.getByTestId(
      "reference-bank-container",
    );
    const buttons = referenceBankContainer.querySelectorAll("button");
    expect(buttons.length).toBeGreaterThan(0);
  });
});
