import React, { useEffect, useState } from "react";
import { Loading } from "@carbon/react";
import styles from "./dematic-chat.module.scss";

/**
 * agent-loading.tsx
 *
 * Displays a simulated multi-step loading animation while the AI agent is "thinking".
 *
 * Features:
 * - Animates through a list of fake loading messages to simulate complex reasoning.
 * - Plays one step every 2.4 seconds, totaling ~6.4 seconds before `onDone()` is called.
 *
 * Used in: `AiChat.tsx` as a placeholder while waiting for an agent response.
 */

// Sequence of loading steps
const thinkingSteps = ["Thinking..."];

export default function AgentLoading({ onDone }: { onDone: () => void }) {
  const [stepIndex, setStepIndex] = useState(0);

  useEffect(() => {
    const interval = window.setInterval(() => {
      setStepIndex((i) => Math.min(i + 1, thinkingSteps.length - 1));
    }, 2400);

    const timeout = window.setTimeout(
      () => {
        window.clearInterval(interval);
        onDone();
      },
      thinkingSteps.length * 2400 + 500,
    );

    return () => {
      window.clearInterval(interval);
      window.clearTimeout(timeout);
    };
  }, [onDone]);

  return (
    <div className={`${styles.chatRow} ${styles.agentMessageWrapper}`}>
      <div className={styles.agentLoadingRow}>
        <Loading
          withOverlay={false}
          small
          description="Loading"
          className={styles.agentSpinner}
        />
        <span key={stepIndex} className={styles.agentLoadingText}>
          {thinkingSteps[stepIndex]}
        </span>
      </div>
    </div>
  );
}
