import { vi } from "vitest";
import { fireEvent, render, screen, act } from "../../../test-utils";
import UserMessage from "./user-message";
import styles from "./dematic-chat.module.scss";

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn(),
  },
});

// Mock Carbon icons
vi.mock("@carbon/icons-react", () => ({
  Copy: { name: "Copy" },
  CopyFile: { name: "CopyFile" },
}));

// Mock Carbon React components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    Button: ({ onClick, iconDescription, renderIcon }: any) => (
      <button
        data-testid={`button-${iconDescription}`}
        onClick={onClick}
        data-icon={renderIcon?.name}
      >
        {iconDescription}
      </button>
    ),
  };
});

describe("UserMessage", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render message text and timestamp", () => {
    render(<UserMessage text="Test message" timestamp="10:30 AM" />);
    expect(screen.getByText("Test message")).toBeInTheDocument();
    expect(screen.getByText("10:30 AM")).toBeInTheDocument();
  });

  it("should have correct CSS classes", () => {
    render(<UserMessage text="Test" timestamp="10:30 AM" />);
    const messageWrapper = screen.getByText("Test").closest("div")
      ?.parentElement?.parentElement?.parentElement;
    expect(messageWrapper).toHaveClass(styles.userMessageWrapper);
    expect(messageWrapper).toHaveClass(styles.hoverableMessage);
  });

  it("should copy text to clipboard when copy button is clicked", async () => {
    render(<UserMessage text="Test message" timestamp="10:30 AM" />);
    const copyButton = screen.getByTestId("button-Copy");
    fireEvent.click(copyButton);
    expect(navigator.clipboard.writeText).toHaveBeenCalledWith("Test message");
  });

  it("should show copied state temporarily after copying", async () => {
    vi.useFakeTimers();
    render(<UserMessage text="Test message" timestamp="10:30 AM" />);
    const copyButton = screen.getByTestId("button-Copy");

    // Click copy button
    fireEvent.click(copyButton);
    expect(copyButton).toHaveAttribute("data-icon", "CopyFile");

    // Advance timers
    act(() => {
      vi.advanceTimersByTime(3000);
    });

    // Should revert back to copy icon
    expect(copyButton).toHaveAttribute("data-icon", "Copy");
    vi.useRealTimers();
  });

  it("should handle empty message text", () => {
    render(<UserMessage text="" timestamp="10:30 AM" />);
    const messageText = screen.getByTestId("user-message-text");
    expect(messageText).toBeInTheDocument();
    expect(messageText.parentElement).toHaveClass(styles.userMessage);
  });

  it("should handle empty timestamp", () => {
    render(<UserMessage text="Test message" timestamp="" />);
    const timestamp = screen.getByTestId("user-message-timestamp");
    expect(timestamp).toBeInTheDocument();
    expect(timestamp).toHaveClass(styles.timestamp);
  });
});
