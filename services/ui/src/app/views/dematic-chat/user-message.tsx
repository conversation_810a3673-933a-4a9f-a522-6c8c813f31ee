import React, { useState } from "react";
import styles from "./dematic-chat.module.scss";
import { Copy, CopyFile } from "@carbon/icons-react";
import { Button } from "@carbon/react";

type Props = {
  text: string; // The content of the user's message
  timestamp: string; // The time the message was sent (formatted in parent)
};

// Renders a single user message bubble in the chat interface.
// Includes a timestamp and a button to copy the message to clipboard.
export default function UserMessage({ text, timestamp }: Props) {
  const [copied, setCopied] = useState(false); // Whether the message has just been copied

  // Handles the copy-to-clipboard action
  const handleCopy = () => {
    navigator.clipboard.writeText(text); // Copies the message text to clipboard
    setCopied(true); // Sets `copied` to true to show confirmation icon
    setTimeout(() => setCopied(false), 3000); // Reverts icon back after 3 seconds
  };

  return (
    <div
      className={`${styles.chatRow} ${styles.userMessageWrapper} ${styles.hoverableMessage}`}
    >
      <div>
        <div className={styles.userMessage}>
          {/* The user's message text */}
          <div data-testid="user-message-text">{text}</div>

          {/* Timestamp shown below the message in smaller, muted style */}
          <div
            className={styles.timestamp}
            data-testid="user-message-timestamp"
          >
            {timestamp}
          </div>
        </div>

        {/* Message action buttons (like copy) fade in on hover */}
        <div className={styles.messageActions}>
          <Button
            kind="ghost"
            hasIconOnly
            size="sm"
            renderIcon={copied ? CopyFile : Copy} // Show "copied" icon if recently clicked
            iconDescription="Copy"
            tooltipPosition="bottom"
            onClick={handleCopy}
          />
        </div>
      </div>
    </div>
  );
}
