.contentContainer {
  position: relative;
  width: 100%;
  height: calc(100vh - 3rem); /* Adjust based on your ViewBar height */
}

.playerContainer {
  position: relative;
  width: 100%;
  height: 100%;
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 100;
}

/* Target the iframe directly */
.playerContainer iframe {
  height: 80vh;
  width: 100%;
  margin: 1rem;
  box-sizing: border-box;
  border: none;
}
