import { vi } from "vitest";
import { render, screen } from "../../../../../test-utils";
import SimulationPlayerView from "./simulation-player-view";
import React from "react";

// Mock components with minimal implementations
vi.mock("../../../../components/sim-player/sim-player", () => {
  const MockPlayer = () => <div data-testid="sim-player">Sim Player</div>;
  MockPlayer.displayName = "SimPlayer";
  return { __esModule: true, default: MockPlayer };
});

// Mock the react-router hooks
vi.mock("react-router", () => ({
  useParams: () => ({ jobId: "test-job-123" }),
  useSearchParams: () => [new URLSearchParams({ taskIndex: "0" })],
}));

// Mock the ViewBar component
vi.mock("../../../../components/view-bar/view-bar", () => ({
  ViewBar: ({ title }: { title: string }) => (
    <div data-testid="view-bar">{title}</div>
  ),
}));

// Mock the WidgetContainer component
vi.mock("../../../../components/widget-container/widget-container", () => ({
  WidgetContainer: ({ error }: { title: string; error?: Error }) => (
    <div data-testid="widget-container">
      {error ? error.message : "Content"}
    </div>
  ),
}));

// Mock the auth service
vi.mock("../../../../auth/auth-service", () => ({
  authService: {
    getAccessToken: vi.fn().mockResolvedValue("mock-token"),
  },
}));

// Mock fetch
global.fetch = vi.fn().mockResolvedValue({
  status: 200,
  ok: true,
  blob: () => Promise.resolve(new Blob(["test"])),
  text: () => Promise.resolve("OK"),
});

// Mock environment variables
vi.mock("import.meta", () => ({
  env: {
    VITE_LOCALHOST: false,
    VITE_API_BASE_URL: "https://api.example.com",
    VITE_API_LOCAL_BASE_URL: "http://localhost:3000",
  },
}));

describe("SimulationPlayerView", () => {
  it("renders the view bar with correct title", () => {
    render(<SimulationPlayerView />);
    expect(screen.getByTestId("view-bar")).toHaveTextContent("test-job-123");
  });
});
