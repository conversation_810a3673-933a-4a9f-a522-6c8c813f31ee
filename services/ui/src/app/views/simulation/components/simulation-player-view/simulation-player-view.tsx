import { useEffect, useRef, useState } from "react";
import { useParams, useSearchParams } from "react-router";
import { ViewBar } from "../../../../components/view-bar/view-bar";
import SimPlayer from "../../../../components/sim-player/sim-player";
import { WidgetContainer } from "../../../../components/widget-container/widget-container";
import { authService } from "../../../../auth/auth-service";
import { Loading } from "@carbon/react";
import styles from "./simulation-player-view.module.css";

export default function SimulationPlayerView() {
  const { jobId } = useParams();
  const [searchParams] = useSearchParams();
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Extract params from URL
  const taskIndex = searchParams.get("taskIndex") || "0";
  const simFileName = "sim_events.zip";
  const simFileType = "sim-events";

  const [error, setError] = useState<Error | undefined>(undefined);
  const [simData, setSimData] = useState<ArrayBuffer | null>(null);
  const [authToken, setAuthToken] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [playerLoading, setPlayerLoading] = useState(true);

  // Get API base URL from environment
  const getApiUrl = () =>
    import.meta.env.VITE_LOCALHOST
      ? import.meta.env.VITE_API_LOCAL_BASE_URL
      : import.meta.env.VITE_API_BASE_URL;

  // Compute API endpoint for SimPlayer
  const apiEndpoint = `${getApiUrl()}/simulation/job/${jobId}/${taskIndex}/${simFileName}/${simFileType}/output`;

  // Direct fetch is preferred for binary data retrieval since ictApi.client.useQuery
  // is optimized for JSON responses and may cause issues with streaming binary content
  useEffect(() => {
    async function loadSimulationData() {
      if (!jobId || !authToken) return;

      setIsLoading(true);
      setError(undefined);

      try {
        console.log("Fetching simulation data with parameters:", {
          jobId,
          taskIndex,
          fileName: simFileName,
          fileType: simFileType,
          apiEndpoint,
        });

        // Fetch the simulation data
        const response = await fetch(apiEndpoint, {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        });

        // Handle API response based on status
        if (response.status === 200) {
          // Get binary data and validate
          const blob = await response.blob();

          if (!blob || blob.size === 0) {
            throw new Error("Received empty simulation data from server");
          }

          console.log(
            `Successfully fetched simulation data: Status ${response.status}, Size: ${blob.size} bytes`,
          );

          // Convert to ArrayBuffer for postMessage
          const arrayBuffer = await blob.arrayBuffer();
          setSimData(arrayBuffer);
        } else {
          // Simple error handling for non-200 status codes
          const errorMessage = `Error loading simulation data: ${response.status} ${response.statusText}`;
          throw new Error(errorMessage);
        }
      } catch (err: unknown) {
        console.error("Error fetching simulation data:", err);
        setError(
          err instanceof Error ? err : new Error("Unknown error occurred"),
        );
      } finally {
        setIsLoading(false);
      }
    }

    loadSimulationData();
  }, [jobId, taskIndex, apiEndpoint, authToken]);

  // Get auth token when component mounts
  useEffect(() => {
    async function getToken() {
      try {
        const token = await authService.getAccessToken();
        setAuthToken(token);
      } catch (err: unknown) {
        console.error("Error getting auth token:", err);
        setError(
          err instanceof Error
            ? err
            : new Error("Failed to get authentication token"),
        );
      }
    }

    getToken();
  }, []);

  // Effect to send data to iframe once we have the data
  useEffect(() => {
    if (!simData || !iframeRef.current) return;

    // Function to send data to iframe
    const sendDataToIframe = async () => {
      console.log("Setting up iframe with simulation data");

      if (!iframeRef.current?.contentWindow) {
        console.error("Iframe or contentWindow not available");
        return;
      }

      try {
        // Create transferable copy of data
        const dataClone = simData.slice(0);
        // Send the data to the iframe
        iframeRef.current.contentWindow.postMessage(
          {
            type: "SIM_DATA",
            data: dataClone,
            apiEndpoint,
            token: authToken,
          },
          "*",
        );
        console.log("Sent simulation data to iframe", {
          dataSize: dataClone.byteLength,
          apiEndpoint,
        });
      } catch (err) {
        console.error("Error sending data to iframe:", err);
        const playerError =
          err instanceof Error
            ? err
            : new Error("Failed to send data to simulation player");

        setError(playerError);
        setPlayerLoading(false);
      }
    };

    // Set up onload handler for iframe
    if (iframeRef.current) {
      iframeRef.current.onload = () => {
        console.log("Iframe loaded, sending simulation data");
        setTimeout(sendDataToIframe, 500); // Small delay to ensure iframe is fully initialized
      };

      // If iframe is already loaded, send data directly
      if (iframeRef.current.contentDocument?.readyState === "complete") {
        console.log("Iframe already loaded, sending data directly");
        setTimeout(sendDataToIframe, 500);
      }
    }
  }, [simData, apiEndpoint, authToken]);

  // Handle message from iframe
  useEffect(() => {
    const handleIframeMessage = (event: MessageEvent) => {
      if (event.data && event.data.type === "SIM_PLAYER_READY") {
        console.log("Sim player reported ready");
        setPlayerLoading(false);
      } else if (event.data && event.data.type === "SIM_PLAYER_ERROR") {
        console.error("Sim player reported error:", event.data.error);
        setError(
          new Error(
            event.data.error || "Simulation player encountered an error",
          ),
        );
        setPlayerLoading(false);
      } else if (event.data && event.data.type === "ANIMATION_LOADED") {
        console.log("Animation data loaded:", event.data.message);
        setPlayerLoading(false);
      } else if (event.data && event.data.type === "LOG_MESSAGE") {
        console.log("Message from sim player:", event.data.message);
      }
    };

    window.addEventListener("message", handleIframeMessage);
    return () => {
      window.removeEventListener("message", handleIframeMessage);
    };
  }, []);

  return (
    <div>
      <ViewBar title={`Simulation View: ${jobId}`} />
      <div className={styles.contentContainer}>
        {error ? (
          <WidgetContainer title="Simulation Player" error={error} />
        ) : (
          <div className={styles.playerContainer}>
            {(isLoading || playerLoading) && (
              <div
                className={styles.loadingOverlay}
                data-testid="loading-overlay"
              >
                <Loading
                  description="Loading simulation..."
                  withOverlay={false}
                />
              </div>
            )}
            <SimPlayer ref={iframeRef} />
          </div>
        )}
      </div>
    </div>
  );
}
