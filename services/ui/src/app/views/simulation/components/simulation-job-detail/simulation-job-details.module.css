.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}
.content {
  flex: 1;
  padding: 1rem;
  overflow: auto;
}
.tableContainer {
  height: 100%;
  width: 100%;
  position: relative;
  height: calc(100vh - 170px);
}
.errorTile {
  background-color: #f4f4f4;
  border-left: 4px solid #da1e28;
  padding: 1rem;
  margin-bottom: 1rem;
  color: inherit;
}
.actionsContainer {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 1rem;
  z-index: 1;
  position: relative;
}
.loadingContainer {
  padding: 2rem;
  display: flex;
  justify-content: center;
}
.outputSelector {
  margin-bottom: 1.5rem;
  max-width: 400px;
}
.widgetHeading {
  margin-bottom: 0;
}

.datagrid {
  position: absolute;
  top: 3rem;
  left: 0;
  right: 0;
  bottom: 0;
}

.datagridContainer {
  height: 0;
  width: 0;
}
