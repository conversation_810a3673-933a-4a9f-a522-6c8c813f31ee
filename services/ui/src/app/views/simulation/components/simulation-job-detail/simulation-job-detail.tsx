import { Tile, But<PERSON>, Loading } from "@carbon/react";
import { useEffect, useState, useMemo } from "react";
import { useParams, useLocation, useNavigate } from "react-router";
import { ViewBar } from "../../../../components/view-bar/view-bar";
import styles from "./simulation-job-details.module.css";
import { ictApi } from "../../../../api/ict-api";
import { WidgetContainer } from "../../../../components/widget-container/widget-container";
import { SimulationJob } from "../../../simulation/types";
import { Datagrid } from "../../../../components/datagrid";
import { createColumnHelper } from "@tanstack/react-table";

// Define a type for the summary data
interface SummaryData {
  [key: string]: string | number | boolean | null;
}

// Define a type for formatted summary data to display in the datagrid
interface FormattedSummaryData {
  statistic: string;
  value: string | number | boolean | null;
}

export default function SimulationJobDetail() {
  const { jobId } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const searchParams = new URLSearchParams(location.search);

  // Extract params directly
  const taskIndex = searchParams.get("taskIndex") || "0";
  const fileName = searchParams.get("fileName") || "summary.csv";
  const fileType = searchParams.get("fileType") || "summary";

  const [error] = useState<Error | undefined>(undefined);
  const [summaryData, setSummaryData] = useState<SummaryData[]>([]);
  const [formattedSummaryData, setFormattedSummaryData] = useState<
    FormattedSummaryData[]
  >([]);

  // Get the job data from the navigation state if available
  const jobFromNavigation = location.state?.job as SimulationJob | undefined;

  // Determine if we need to fetch job data
  const shouldFetchJobData = Boolean(!jobFromNavigation && jobId);

  // Fetch job details only if not available from navigation
  const { data: jobListData, isLoading: isJobLoading } = ictApi.client.useQuery(
    "get",
    "/simulation/jobs/list",
    {},
    {
      // Only enable this query if we need to fetch job data
      enabled: shouldFetchJobData,
      select: (data) => {
        // Find the specific job
        const job = data.data?.find(
          (job: SimulationJob) => job.jobId === jobId,
        );
        return job;
      },
    },
  );

  // Combine job data from navigation or from API
  const jobData = jobFromNavigation || jobListData;

  // Fetch job output data
  const { data: outputData, isLoading: isOutputLoading } =
    ictApi.client.useQuery(
      "get",
      "/simulation/job/{jobId}/{taskIndex}/{fileName}/{fileType}/output",
      {
        params: {
          path: {
            jobId: String(jobId),
            taskIndex: Number(taskIndex),
            fileName,
            fileType,
          },
        },
      },
      {
        enabled: !!jobId && fileType === "summary",
        select: (data) => data as SummaryData[],
      },
    );

  // Update summary data when output data changes
  useEffect(() => {
    if (outputData) {
      setSummaryData(outputData as SummaryData[]);

      // Format the data for datagrid display
      if (outputData.length > 0) {
        const formattedData: FormattedSummaryData[] = [];

        // Extract all keys from the first summary data object
        const firstDataObject = outputData[0];

        // Transform the data into rows with statistic and value columns
        Object.entries(firstDataObject).forEach(([key, value]) => {
          formattedData.push({
            statistic: key,
            value: value,
          });
        });

        setFormattedSummaryData(formattedData);
      }
    }
  }, [outputData]);

  // Function to navigate to simulation player view
  const goToSimPlayer = () => {
    if (!jobId) return;

    // Navigate to the SimulationPlayerView with the necessary params
    navigate(`/ict-simulation/job/${jobId}/player?taskIndex=${taskIndex}`);
  };

  // Create column helper for the datagrid
  const columnHelper = createColumnHelper<FormattedSummaryData>();

  // Define columns for the datagrid
  const columns = useMemo(
    () => [
      columnHelper.accessor("statistic", {
        header: "Statistic",
        cell: (info) => info.getValue(),
      }),
      columnHelper.accessor("value", {
        header: "Value",
        cell: (info) => {
          const value = info.getValue();
          if (value === null) return "N/A";
          if (typeof value === "boolean") return value ? "Yes" : "No";
          return String(value);
        },
      }),
    ],
    [columnHelper],
  );

  return (
    <div className={styles.container}>
      <ViewBar title={`Simulation Job: ${jobId}`} />
      <div className={styles.content}>
        <WidgetContainer title="Job Summary" error={error}>
          {!error && (
            <div className={styles.tableContainer}>
              {isJobLoading || isOutputLoading ? (
                <div className={styles.loadingContainer}>
                  <Loading
                    description="Loading job data..."
                    withOverlay={false}
                  />
                </div>
              ) : summaryData.length === 0 ? (
                <Tile>
                  <p>No summary data available for this job.</p>
                </Tile>
              ) : (
                <>
                  {jobData && jobData.outputReady && (
                    <div className={styles.actionsContainer}>
                      <Button
                        kind="primary"
                        onClick={goToSimPlayer}
                        disabled={!summaryData.length || !jobData}
                        data-testid="view-simulation-button"
                      >
                        View Simulation
                      </Button>
                    </div>
                  )}
                  <Datagrid
                    columns={columns}
                    data={formattedSummaryData}
                    mode="client"
                    enableSelection={false}
                    showRefreshButton={false}
                    showExportButton={false}
                    initialPagination={{ pageIndex: 0, pageSize: 50 }}
                    className={styles.datagrid}
                  />
                </>
              )}
            </div>
          )}
        </WidgetContainer>
      </div>
    </div>
  );
}
