import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { vi } from "vitest";
import { fireEvent, render, screen } from "../../../../../test-utils";
import { SimulationTable } from "./simulation-table";
import { SimulationJob } from "../../types";

// Mock react-router
const mockNavigate = vi.fn();
vi.mock("react-router", () => ({
  useNavigate: () => mockNavigate,
}));

// Mock Carbon React
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
    Tag: ({ children }: { children: React.ReactNode }) => (
      <span data-testid="mock-tag">{children}</span>
    ),
    Link: ({ children, onClick }: any) => (
      <a data-testid="mock-link" onClick={onClick}>
        {children}
      </a>
    ),
  };
});

// Mock the Datagrid component
vi.mock("../../../../components/datagrid", () => ({
  Datagrid: ({
    columns,
    data,
    onPageChange,
    onSort,
    onFilter,
    onRefreshClick,
  }: any) => (
    <div data-testid="datagrid">
      <div data-testid="columns">
        {JSON.stringify(columns.map((c: any) => c.header))}
      </div>
      <div data-testid="data">{JSON.stringify(data)}</div>
      <div data-testid="job-id-cell">
        {data.length > 0 &&
          columns
            .find((c: any) => c.id === "jobId")
            ?.cell({
              getValue: () => data[0].jobId,
              row: { original: data[0] },
            })}
      </div>
      <div data-testid="created-cell">
        {data.length > 0 ? new Date(data[0].createTime).toLocaleString() : ""}
      </div>
      <div data-testid="time-taken-cell">
        {data.length > 0
          ? data[0].timeTaken !== null
            ? data[0].timeTaken?.toString()
            : "N/A"
          : ""}
      </div>
      <div data-testid="tags-cell">
        {data.length > 0 && data[0].tags.length > 0
          ? data[0].tags.join(", ")
          : ""}
      </div>
      <div data-testid="output-ready-cell">
        {data.length > 0 ? (data[0].outputReady ? "Yes" : "No") : ""}
      </div>
      <button
        data-testid="page-change"
        onClick={() => onPageChange({ pageIndex: 1, pageSize: 10 })}
      >
        Change Page
      </button>
      <button
        data-testid="sort"
        onClick={() => onSort([{ id: "jobId", desc: true }])}
      >
        Sort
      </button>
      <button
        data-testid="filter"
        onClick={() =>
          onFilter({ filters: { state: "COMPLETED" }, globalFilter: "test" })
        }
      >
        Filter
      </button>
      {onRefreshClick && (
        <button data-testid="refresh" onClick={onRefreshClick}>
          Refresh
        </button>
      )}
    </div>
  ),
}));

// Mock @tanstack/react-table
vi.mock("@tanstack/react-table", () => ({
  createColumnHelper: () => ({
    accessor: (key: string, config: any) => ({
      id: key,
      header: config.header,
      cell: config.cell,
    }),
  }),
}));

const mockSetPagination = vi.fn();
const mockSetSorting = vi.fn();
const mockSetColumnFilters = vi.fn();
const mockSetGlobalFilter = vi.fn();
const mockOnRefresh = vi.fn();

describe("SimulationTable", () => {
  const mockData: SimulationJob[] = [
    {
      jobId: "job-123",
      events: [
        {
          eventType: "JOB_CREATED",
          state: "CREATED",
          timestamp: "2023-05-01T12:00:00Z",
          taskIndex: null,
          jobId: "job-123",
        },
      ],
      tags: ["test-tag"],
      lastUpdate: "2023-05-01T12:30:00Z",
      tasks: {
        "0": {
          taskIndex: 0,
          lastUpdate: "2023-05-01T12:30:00Z",
          state: "COMPLETED",
          timeTaken: 300,
          visState: "VISIBLE",
          exitCode: 0,
        },
      },
      createTime: "2023-05-01T12:00:00Z",
      userId: "user-123",
      taskCount: 1,
      state: "SUCCEEDED",
      outputReady: true,
      attributes: {
        randomSeeds: [12345],
        uploadId: "upload-123",
        runnerBackend: "test-backend",
      },
      timeTaken: 1800,
      uploadId: "upload-123",
    },
    {
      jobId: "job-456",
      events: [],
      tags: ["another-tag", "test-tag"],
      lastUpdate: "2023-05-02T14:30:00Z",
      tasks: {},
      createTime: "2023-05-02T14:00:00Z",
      userId: "user-456",
      taskCount: 0,
      state: "PENDING",
      outputReady: false,
      attributes: {
        randomSeeds: [67890],
        uploadId: "upload-456",
        runnerBackend: "test-backend",
      },
      timeTaken: null,
      uploadId: "upload-456",
    },
  ];

  const defaultProps = {
    data: mockData,
    pagination: { pageIndex: 0, pageSize: 10 } as PaginationState,
    setPagination: mockSetPagination,
    sorting: [] as SortingState,
    setSorting: mockSetSorting,
    columnFilters: [] as ColumnFiltersState,
    setColumnFilters: mockSetColumnFilters,
    isLoading: false,
    isFetching: false,
    error: null,
    rowCount: 2,
    setGlobalFilter: mockSetGlobalFilter,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render the datagrid with correct data", () => {
    render(<SimulationTable {...defaultProps} />);

    expect(screen.getByTestId("datagrid")).toBeInTheDocument();

    const dataElement = screen.getByTestId("data");
    const parsedData = JSON.parse(dataElement.textContent || "[]");

    expect(parsedData).toHaveLength(2);
    expect(parsedData[0].jobId).toBe("job-123");
    expect(parsedData[1].jobId).toBe("job-456");
  });

  it("should render all expected columns", () => {
    render(<SimulationTable {...defaultProps} />);

    const columnsElement = screen.getByTestId("columns");
    const columnHeaders = JSON.parse(columnsElement.textContent || "[]");

    const expectedHeaders = [
      "Job ID",
      "Created",
      "Last Updated",
      "State",
      "Tasks",
      "Output Ready",
      "Time Taken (s)",
      "Tags",
      "User ID",
    ];

    expect(columnHeaders).toEqual(expectedHeaders);
  });

  it("should handle pagination changes", () => {
    render(<SimulationTable {...defaultProps} />);

    fireEvent.click(screen.getByTestId("page-change"));

    expect(mockSetPagination).toHaveBeenCalledWith({
      pageIndex: 1,
      pageSize: 10,
    });
  });

  it("should handle sorting changes", () => {
    render(<SimulationTable {...defaultProps} />);

    fireEvent.click(screen.getByTestId("sort"));

    expect(mockSetSorting).toHaveBeenCalledWith([{ id: "jobId", desc: true }]);
  });

  it("should handle filter changes", () => {
    render(<SimulationTable {...defaultProps} />);

    fireEvent.click(screen.getByTestId("filter"));

    expect(mockSetColumnFilters).toHaveBeenCalledWith([
      { id: "state", value: "COMPLETED" },
    ]);
    expect(mockSetGlobalFilter).toHaveBeenCalledWith("test");
  });

  it("should render job ID as link for SUCCEEDED state", () => {
    render(<SimulationTable {...defaultProps} />);

    // First job has SUCCEEDED state, so it should be rendered as a link
    const link = screen.getByTestId("mock-link");
    expect(link).toBeInTheDocument();
    expect(link.textContent).toBe("job-123");

    // Test click navigation
    fireEvent.click(link);
    expect(mockNavigate).toHaveBeenCalledWith("/ict-simulation/job/job-123", {
      state: { job: mockData[0] },
    });
  });

  it("should format date cells correctly", () => {
    render(<SimulationTable {...defaultProps} />);

    const cell = screen.getByTestId("created-cell");
    expect(cell).toHaveTextContent(
      new Date("2023-05-01T12:00:00Z").toLocaleString(),
    );
  });

  it("should format time taken cells correctly", () => {
    render(<SimulationTable {...defaultProps} />);

    const cell = screen.getByTestId("time-taken-cell");
    expect(cell).toHaveTextContent("1800");
  });

  it("should handle null time taken values", () => {
    const newData = [...mockData];
    newData[0].timeTaken = null;

    render(<SimulationTable {...defaultProps} data={newData} />);

    const cell = screen.getByTestId("time-taken-cell");
    expect(cell).toHaveTextContent("N/A");
  });

  it("should format outputReady cells correctly", () => {
    render(<SimulationTable {...defaultProps} />);

    const cell = screen.getByTestId("output-ready-cell");
    expect(cell).toHaveTextContent("Yes");
  });

  it("should handle tags rendering", () => {
    render(<SimulationTable {...defaultProps} />);

    const cell = screen.getByTestId("tags-cell");
    expect(cell).toHaveTextContent("test-tag");
  });

  it("should handle empty tags", () => {
    const newData = [...mockData];
    newData[0].tags = [];

    render(<SimulationTable {...defaultProps} data={newData} />);

    const cell = screen.getByTestId("tags-cell");
    expect(cell).toHaveTextContent("");
  });

  it("should show loading state", () => {
    render(<SimulationTable {...defaultProps} isLoading={true} />);

    const datagrid = screen.getByTestId("datagrid");
    expect(datagrid).toBeInTheDocument();
  });

  it("should show error state", () => {
    render(<SimulationTable {...defaultProps} error="Test error" />);

    const datagrid = screen.getByTestId("datagrid");
    expect(datagrid).toBeInTheDocument();
  });

  it("should handle refresh button click when onRefresh is provided", () => {
    render(<SimulationTable {...defaultProps} onRefresh={mockOnRefresh} />);

    fireEvent.click(screen.getByTestId("refresh"));

    expect(mockOnRefresh).toHaveBeenCalled();
  });
});
