import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { useCallback, useMemo } from "react";
import { createColumnHelper } from "@tanstack/react-table";
import { Datagrid } from "../../../../components/datagrid";
import type { FilterState } from "../../../../components/datagrid/types";
import { Link, Tag } from "@carbon/react";
import { SimulationJob } from "../../types";
import { useNavigate } from "react-router";

interface SimulationTableProps {
  data: SimulationJob[];
  pagination: PaginationState;
  setPagination: (pagination: PaginationState) => void;
  sorting: SortingState;
  setSorting: (sorting: SortingState) => void;
  columnFilters: ColumnFiltersState;
  setColumnFilters: (filters: ColumnFiltersState) => void;
  isLoading: boolean;
  isFetching: boolean;
  error: unknown;
  rowCount: number;
  onRefresh?: () => void;
  setGlobalFilter: (globalFilter: string) => void;
}
export function SimulationTable({
  data,
  pagination,
  setPagination,
  sorting,
  setSorting,
  setColumnFilters,
  isLoading,
  isFetching,
  error,
  rowCount,
  onRefresh,
  setGlobalFilter,
}: SimulationTableProps) {
  const navigate = useNavigate();
  // Handle job ID click to navigate to job details
  const handleJobIdClick = useCallback(
    (jobId: string, job: SimulationJob) => {
      navigate(`/ict-simulation/job/${jobId}`, { state: { job } });
    },
    [navigate],
  );
  // Define column helper for the SimulationJob type
  const columnHelper = createColumnHelper<SimulationJob>();
  // Define the columns for the simulation job table
  const columns = useMemo(
    () => [
      columnHelper.accessor("jobId", {
        header: "Job ID",
        cell: (info) => {
          // Get the job state from the row data
          const row = info.row.original;
          const isSucceeded = row.state === "SUCCEEDED";
          // Only show hyperlink for SUCCEEDED jobs
          if (isSucceeded) {
            return (
              <Link
                onClick={(e) => {
                  e.stopPropagation();
                  handleJobIdClick(info.getValue(), row);
                }}
                style={{ cursor: "pointer" }}
              >
                {info.getValue()}
              </Link>
            );
          }
          // For other states, just display the job ID as text
          return info.getValue();
        },
      }),
      columnHelper.accessor("createTime", {
        header: "Created",
        cell: (info) => new Date(info.getValue()).toLocaleString(),
      }),
      columnHelper.accessor("lastUpdate", {
        header: "Last Updated",
        cell: (info) => new Date(info.getValue()).toLocaleString(),
      }),
      columnHelper.accessor("state", {
        header: "State",
        cell: (info) => info.getValue(),
      }),
      columnHelper.accessor("taskCount", {
        header: "Tasks",
        cell: (info) => info.getValue(),
      }),
      columnHelper.accessor("outputReady", {
        header: "Output Ready",
        cell: (info) => (info.getValue() ? "Yes" : "No"),
      }),
      columnHelper.accessor("timeTaken", {
        header: "Time Taken (s)",
        cell: (info) => {
          const timeTaken = info.getValue();
          return timeTaken !== null ? timeTaken.toString() : "N/A";
        },
      }),
      columnHelper.accessor("tags", {
        header: "Tags",
        cell: (info) => {
          // Filter out empty tags before rendering
          const validTags = info
            .getValue()
            .filter((tag) => tag && tag.trim() !== "");
          // If no valid tags, return nothing
          if (validTags.length === 0) {
            return null;
          }
          return (
            <div>
              {validTags.map((tag, index) => (
                <Tag key={index} type="blue" style={{ display: "inherit" }}>
                  {tag}
                </Tag>
              ))}
            </div>
          );
        },
      }),
      columnHelper.accessor("userId", {
        header: "User ID",
        cell: (info) => info.getValue(),
      }),
    ],
    [columnHelper, handleJobIdClick],
  );
  // Handlers for Datagrid callbacks
  const handlePageChange = useCallback(
    (newPagination: PaginationState) => {
      setPagination(newPagination);
    },
    [setPagination],
  );
  const handleSort = useCallback(
    (newSorting: SortingState) => {
      setSorting(newSorting);
    },
    [setSorting],
  );
  const handleFilter = useCallback(
    (newFilters: FilterState) => {
      // Convert FilterState to ColumnFiltersState
      const newColumnFilters: ColumnFiltersState = Object.entries(
        newFilters.filters,
      ).map(([id, value]) => ({
        id,
        value,
      }));
      setColumnFilters(newColumnFilters);
      setGlobalFilter(newFilters.globalFilter);
    },
    [setColumnFilters, setGlobalFilter],
  );
  return (
    <Datagrid
      columns={columns}
      data={data}
      mode="server"
      totalRows={rowCount}
      isLoading={isLoading || isFetching}
      error={error ? String(error) : undefined}
      onPageChange={handlePageChange}
      onSort={handleSort}
      onFilter={handleFilter}
      initialPagination={pagination}
      initialSorting={sorting}
      enableSelection={false}
      onRefreshClick={onRefresh}
      showRefreshButton={!!onRefresh}
      showExportButton={false}
    />
  );
}
