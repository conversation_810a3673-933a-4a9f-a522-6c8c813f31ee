export interface SimulationJob {
  jobId: string;
  events: Array<{
    eventType: string;
    state: string;
    timestamp: string;
    taskIndex: number | null;
    jobId: string;
    [key: string]: unknown;
  }>;
  tags: string[];
  lastUpdate: string;
  tasks: Record<
    string,
    {
      taskIndex: number;
      lastUpdate: string;
      state: string;
      timeTaken: number | null;
      visState: string;
      exitCode: number;
    }
  >;
  createTime: string;
  userId: string;
  taskCount: number;
  state: string;
  outputReady: boolean;
  attributes: {
    randomSeeds: number[];
    uploadId: string;
    runnerBackend: string;
  };
  timeTaken: number | null;
  uploadId: string;
}

export interface SimulationResponse {
  data: SimulationJob[];
  metadata: {
    totalResults: number;
  };
}

export interface SortField {
  columnName: string;
  isDescending: boolean;
}
