import { vi } from "vitest";
import { fireEvent, render, screen } from "../../../test-utils";
import { SimulationView } from "./simulation";
import { MemoryRouter, Route, Routes } from "react-router";
import type { BaseViewProps } from "../view-registry.types";

// Mock dependencies
vi.mock("../../components/view-bar/view-bar", () => ({
  ViewBar: ({ title, children }: any) => (
    <div data-testid="view-bar">
      <h1>{title}</h1>
      {children && <div data-testid="view-bar-children">{children}</div>}
    </div>
  ),
}));

vi.mock("./components/simulation-table/simulation-table", () => ({
  SimulationTable: () => (
    <div data-testid="simulation-table">Mock Simulation Table</div>
  ),
}));

// Mock Carbon React with GlobalTheme
vi.mock("@carbon/react", async () => {
  // Get the actual module implementations
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => (
      <>{children}</>
    ),
    Button: ({ children, onClick }: any) => (
      <button data-testid="carbon-button" onClick={onClick}>
        {children}
      </button>
    ),
  };
});

// Mock the API client
const mockUseQuery = vi.fn();
vi.mock("../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: (...args: any[]) => mockUseQuery(...args),
    },
  },
}));

// Mock the useApiErrorState hook
vi.mock("../../hooks/use-api-error-state", () => ({
  useApiErrorState: () => false,
}));

// Mock child components
vi.mock("./components/simulation-job-detail/simulation-job-detail", () => ({
  __esModule: true,
  default: () => <div data-testid="simulation-job-detail">Job Detail</div>,
}));

vi.mock("../../components/sim-player/sim-player", () => ({
  __esModule: true,
  default: () => <div data-testid="sim-player">Sim Player</div>,
}));

describe("SimulationView", () => {
  const mockViewProps: BaseViewProps = {
    id: "simulation-view",
    options: {},
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup mock data
    mockUseQuery.mockReturnValue({
      data: {
        data: [
          {
            jobId: "test-job-123",
            state: "COMPLETED",
            createTime: "2023-05-01T12:00:00Z",
            lastUpdate: "2023-05-01T12:30:00Z",
            taskCount: 1,
            outputReady: true,
            tags: ["test"],
            tasks: {},
            events: [],
            userId: "user-123",
            timeTaken: 1800,
            uploadId: "upload-123",
            attributes: {},
          },
        ],
        metadata: { totalResults: 1 },
      },
      isLoading: false,
      isFetching: false,
      error: null,
      refetch: vi.fn(),
    });
  });

  // Directly test the SimulationView component wrapped in a MemoryRouter
  it("renders the simulation list view", () => {
    render(
      <MemoryRouter>
        <SimulationView {...mockViewProps} />
      </MemoryRouter>,
    );

    // Check main components are rendered
    expect(screen.getByTestId("view-bar")).toBeInTheDocument();
    expect(screen.getByText("Simulation")).toBeInTheDocument();
    expect(screen.getByTestId("carbon-button")).toBeInTheDocument();
    expect(screen.getByTestId("simulation-table")).toBeInTheDocument();
  });

  it("handles the simulation button click", () => {
    const consoleLogSpy = vi.spyOn(console, "log");

    render(
      <MemoryRouter>
        <SimulationView {...mockViewProps} />
      </MemoryRouter>,
    );

    fireEvent.click(screen.getByTestId("carbon-button"));

    expect(consoleLogSpy).toHaveBeenCalledWith("Starting simulation...");
  });

  it("makes a request for simulation jobs", () => {
    render(
      <MemoryRouter>
        <SimulationView {...mockViewProps} />
      </MemoryRouter>,
    );

    expect(mockUseQuery).toHaveBeenCalledWith(
      "get",
      "/simulation/jobs/list",
      {},
      expect.objectContaining({ enabled: true, retry: false }),
    );
  });

  // Test route navigation
  it("navigates to job details page", () => {
    render(
      <MemoryRouter initialEntries={["/job/test-123"]}>
        <Routes>
          <Route path="/" element={<SimulationView {...mockViewProps} />} />
          <Route path="/job/:jobId" element={<div>Job Details Route</div>} />
        </Routes>
      </MemoryRouter>,
    );

    // Should be on job details page
    expect(screen.getByText("Job Details Route")).toBeInTheDocument();
  });
});
