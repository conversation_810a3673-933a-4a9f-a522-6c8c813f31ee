import { RecentlyViewed } from "@carbon/icons-react";
import { Loading, Search, Stack } from "@carbon/react";
import { format } from "date-fns";
import { useEffect, useState } from "react";
import { OptionsAccordionGroup } from "../../../components/options/options-accordion-group/options-accordion-group";
import { OptionsAccordion } from "../../../components/options/options-accordion/options-accordion";
import { OptionsContainer } from "../../../components/options/options-container/options-container";
import {
  CardContent,
  SelectableCard,
  SelectableCardGroup,
} from "../../../components/selectable-card";
import { useTranslation } from "react-i18next";
import {
  useConfigSettingLogs,
  useDefaultConfigSetting,
} from "../../../config/hooks/use-config";
import styles from "./view-history.module.css";

export interface ViewHistoryProps {
  settingId: string;
  onClose: () => void;
  onSave: () => void;
  onChange: (newValue: Record<string, unknown>) => void;
}

export const ViewHistory = ({
  settingId,
  onClose,
  onSave,
  onChange,
}: ViewHistoryProps) => {
  const { data, isLoading, error } = useConfigSettingLogs(settingId);
  const { data: defaultSetting } = useDefaultConfigSetting(settingId);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedId, setSelectedId] = useState<string | null>(null);
  const { t } = useTranslation();

  // Format the data for the cards
  const historyItems = !data?.data
    ? []
    : data.data.map((item, index) => ({
        id: String(index),
        changedBy: item.changedBy.split("|")[0],
        timestamp: format(new Date(item.timestamp), "MMM d, yyyy h:mm a"),
        source: item.source,
        rawTimestamp: item.timestamp,
        newValue: item.newValue,
      }));

  // Handle filtering based on search term
  const filteredItems = !searchTerm
    ? historyItems
    : historyItems.filter(
        (item) =>
          item.changedBy.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.timestamp.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.source.toLowerCase().includes(searchTerm.toLowerCase()),
      );

  const emptyData =
    error && (error as Error)?.message?.includes("data is undefined");

  // When a card is selected, call onChange with the parsed newValue
  const handleCardSelect = (id: string | null) => {
    setSelectedId(id);
    if (id === null) return;

    if (id === "default") {
      // Default setting is structured differently than the other history items
      onChange(defaultSetting?.value as Record<string, unknown>);
      return;
    }

    const selectedItem = historyItems.find((item) => item.id === id);

    if (selectedItem?.newValue) {
      try {
        const parsedValue = JSON.parse(selectedItem.newValue);
        onChange(parsedValue);
      } catch (e) {
        console.error("Error parsing history value:", e);
      }
    }
  };

  // Clear selection when the data changes
  useEffect(() => {
    setSelectedId(null);
  }, [data]);

  const renderContent = () => {
    if (emptyData) {
      return (
        <div className={styles.emptyData}>
          {t("viewHistory.noHistoryAvailable", "No history available")}
        </div>
      );
    }

    if (isLoading) {
      return <Loading />;
    }

    return (
      <>
        <Stack gap={4}>
          <div className={styles.searchContainer}>
            <Search
              labelText={t("viewHistory.searchHistory", "Search history")}
              placeholder={t("viewHistory.searchHistory", "Search history")}
              size="sm"
              value={searchTerm}
              onChange={(e) => {
                if (typeof e === "string") {
                  setSearchTerm(e);
                } else if (e?.target?.value !== undefined) {
                  setSearchTerm(e.target.value);
                }
              }}
            />
          </div>

          {filteredItems.length === 0 ? (
            <p className={styles.noResults}>
              {t(
                "viewHistory.noHistoryEntriesFound",
                "No history entries found.",
              )}
            </p>
          ) : (
            <div className={styles.cardsContainer}>
              <SelectableCardGroup
                selectedId={selectedId}
                onChange={handleCardSelect}
                ariaLabel={t(
                  "viewHistory.viewHistoryEntries",
                  "View history entries",
                )}
              >
                {defaultSetting && (
                  <SelectableCard
                    id="default"
                    selected={selectedId === "default"}
                    className={`${styles.historyCard} ${styles.defaultCard}`}
                  >
                    <CardContent
                      title={t("viewHistory.defaultValue", "Default Value")}
                      description={t(
                        "viewHistory.defaultValueDescription",
                        "The default value for the view",
                      )}
                      icon={<RecentlyViewed size={24} />}
                    />
                  </SelectableCard>
                )}
                {filteredItems.map((item) => (
                  <SelectableCard
                    key={item.id}
                    id={item.id}
                    selected={item.id === selectedId}
                    className={styles.historyCard}
                  >
                    <CardContent
                      title={item.timestamp}
                      description={`${t("viewHistory.changedBy", "Changed by")} ${item.changedBy} (${item.source})`}
                    />
                  </SelectableCard>
                ))}
              </SelectableCardGroup>
            </div>
          )}
        </Stack>
      </>
    );
  };

  return (
    <OptionsContainer
      onClose={onClose}
      onSave={onSave}
      saveButtonText={t("viewHistory.apply", "Apply")}
      saveButtonDisabled={!selectedId}
    >
      <OptionsAccordion defaultValues={["history"]}>
        <OptionsAccordionGroup
          id="history"
          title={t("viewHistory.title", "View History")}
          icon={<RecentlyViewed size={20} />}
        >
          {renderContent()}
        </OptionsAccordionGroup>
      </OptionsAccordion>
    </OptionsContainer>
  );
};
