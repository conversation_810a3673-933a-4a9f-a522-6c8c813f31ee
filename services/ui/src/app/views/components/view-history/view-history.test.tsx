import { render, screen, userEvent } from "../../../../test-utils";
import { ViewHistory } from "./view-history";
import { vi } from "vitest";

// Mock the config hooks
vi.mock("../../../config/hooks/use-config", () => ({
  useConfigSettingLogs: vi.fn(),
  useDefaultConfigSetting: vi.fn(),
}));

// Mock Carbon React components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

// Import the mocked hooks
import {
  useConfigSettingLogs,
  useDefaultConfigSetting,
} from "../../../config/hooks/use-config";

const mockUseConfigSettingLogs = vi.mocked(useConfigSettingLogs);
const mockUseDefaultConfigSetting = vi.mocked(useDefaultConfigSetting);

describe("ViewHistory", () => {
  const defaultProps = {
    settingId: "test-setting",
    onClose: vi.fn(),
    onSave: vi.fn(),
    onChange: vi.fn(),
  };

  const mockHistoryData = {
    data: [
      {
        changedBy: "user1|full-name",
        timestamp: "2024-01-01T10:00:00Z",
        source: "manual",
        newValue: '{"test": "value1"}',
      },
      {
        changedBy: "user2|full-name",
        timestamp: "2024-01-02T11:00:00Z",
        source: "automatic",
        newValue: '{"test": "value2"}',
      },
    ],
  };

  const mockDefaultSetting = {
    id: "default-id",
    name: "test-setting",
    group: "test-group",
    dataType: "json" as const,
    value: { test: "default" },
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseConfigSettingLogs.mockReturnValue({
      data: mockHistoryData,
      isLoading: false,
      error: null,
    } as any);
    mockUseDefaultConfigSetting.mockReturnValue({
      data: mockDefaultSetting,
      isLoading: false,
      error: null,
    } as any);
  });

  it("renders the view history component", () => {
    render(<ViewHistory {...defaultProps} />);

    expect(screen.getByText("View History")).toBeInTheDocument();
  });

  it("displays loading state", () => {
    mockUseConfigSettingLogs.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
    } as any);

    render(<ViewHistory {...defaultProps} />);

    expect(screen.getByTitle("loading")).toBeInTheDocument();
  });

  it("displays empty data message when no history is available", () => {
    const errorWithUndefinedData = new Error("data is undefined");
    mockUseConfigSettingLogs.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: errorWithUndefinedData,
    } as any);

    render(<ViewHistory {...defaultProps} />);

    expect(screen.getByText("No history available")).toBeInTheDocument();
  });

  it("displays history items", () => {
    render(<ViewHistory {...defaultProps} />);

    expect(screen.getByText("Default Value")).toBeInTheDocument();
    expect(screen.getByText(/Jan 1, 2024/)).toBeInTheDocument();
    expect(screen.getByText(/Jan 2, 2024/)).toBeInTheDocument();
    expect(screen.getByText(/Changed by user1/)).toBeInTheDocument();
    expect(screen.getByText(/Changed by user2/)).toBeInTheDocument();
  });

  it("filters history items based on search term", async () => {
    render(<ViewHistory {...defaultProps} />);

    const searchInput = screen.getByRole("searchbox");
    await userEvent.type(searchInput, "user1");

    expect(screen.getByText(/Changed by user1/)).toBeInTheDocument();
    expect(screen.queryByText(/Changed by user2/)).not.toBeInTheDocument();
  });

  it("calls onChange when a history item is selected", async () => {
    render(<ViewHistory {...defaultProps} />);

    const historyCard = screen.getByText(/Jan 1, 2024/);
    await userEvent.click(historyCard);

    expect(defaultProps.onChange).toHaveBeenCalledWith({ test: "value1" });
  });

  it("calls onChange with default value when default card is selected", async () => {
    render(<ViewHistory {...defaultProps} />);

    const defaultCard = screen.getByText("Default Value");
    await userEvent.click(defaultCard);

    expect(defaultProps.onChange).toHaveBeenCalledWith({ test: "default" });
  });

  it("disables save button when no item is selected", () => {
    render(<ViewHistory {...defaultProps} />);

    const applyButton = screen.getByText("Apply");
    expect(applyButton).toBeDisabled();
  });

  it("enables save button when an item is selected", async () => {
    render(<ViewHistory {...defaultProps} />);

    const historyCard = screen.getByText(/Jan 1, 2024/);
    await userEvent.click(historyCard);

    const applyButton = screen.getByText("Apply");
    expect(applyButton).not.toBeDisabled();
  });

  it("displays no results message when search returns empty", async () => {
    render(<ViewHistory {...defaultProps} />);

    const searchInput = screen.getByRole("searchbox");
    await userEvent.type(searchInput, "nonexistent");

    expect(screen.getByText("No history entries found.")).toBeInTheDocument();
  });
});
