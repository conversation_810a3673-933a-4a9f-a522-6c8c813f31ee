import { Node, <PERSON>, Position, XYPosition } from "reactflow";
import dagre from "@dagrejs/dagre";

// this helper function returns the intersection point
// of the line between the center of the intersectionNode and the target node
// https://math.stackexchange.com/questions/1724792/an-algorithm-for-finding-the-intersection-point-between-a-center-of-vision-and-a
function getNodeIntersection(intersectionNode: Node, targetNode: Node) {
  let x = 0;
  let y = 0;

  if (
    intersectionNode.width &&
    intersectionNode.height &&
    intersectionNode.positionAbsolute &&
    targetNode.positionAbsolute &&
    targetNode.height &&
    targetNode.width
  ) {
    const w = intersectionNode.width / 2;
    const h = intersectionNode.height / 2;

    const x2 = intersectionNode.positionAbsolute.x + w;
    const y2 = intersectionNode.positionAbsolute.y + h;
    const x1 = targetNode.positionAbsolute.x + targetNode.width / 2;
    const y1 = targetNode.positionAbsolute.y + targetNode.height / 2;

    const xx1 = (x1 - x2) / (2 * w) - (y1 - y2) / (2 * h);
    const yy1 = (x1 - x2) / (2 * w) + (y1 - y2) / (2 * h);
    const a = 1 / (Math.abs(xx1) + Math.abs(yy1));
    const xx3 = a * xx1;
    const yy3 = a * yy1;
    x = w * (xx3 + yy3) + x2;
    y = h * (-xx3 + yy3) + y2;
  }

  return { x, y };
}

function getEdgePosition(node: Node, intersectionPoint: XYPosition) {
  // Given a node and an intersection point, this function returns the position (top,right,bottom,left)
  // of the intersection point on that node.
  const n = { ...node.position, ...node };

  if (n.width && n.height) {
    const nx = Math.round(n.x);
    const ny = Math.round(n.y);
    const px = Math.round(intersectionPoint.x);
    const py = Math.round(intersectionPoint.y);

    if (px <= nx + 1) {
      return Position.Left;
    }
    if (px >= nx + n.width - 1) {
      return Position.Right;
    }
    if (py <= ny + 1) {
      return Position.Top;
    }
    if (py >= n.y + n.height - 1) {
      return Position.Bottom;
    }
  }

  return Position.Top;
}

export function getEdgeParams(source: Node, target: Node) {
  // Given two nodes, this function returns the parameters (sx, sy, tx, ty, sourcePos, targetPos)
  // that are needed to create an edge that connects the two nodes.
  const sourceIntersectionPoint = getNodeIntersection(source, target);
  const targetIntersectionPoint = getNodeIntersection(target, source);
  const sourcePos = getEdgePosition(source, sourceIntersectionPoint);
  const targetPos = getEdgePosition(target, targetIntersectionPoint);

  return {
    sx: sourceIntersectionPoint.x,
    sy: sourceIntersectionPoint.y,
    tx: targetIntersectionPoint.x,
    ty: targetIntersectionPoint.y,
    sourcePos,
    targetPos,
  };
}

/**
 * Calculate the angle of the arrow based on the source and target nodes.
 * This function is used to determine the angle of the arrow that connects the source and target nodes.
 * The arrow starts as an arrow pointing right (slope=0) and then calculates the angle of rotation
 * needed to point the arrow at the target node.
 * @param sx - The x coordinate of the source node.
 * @param sy - The y coordinate of the source node.
 * @param tx - The x coordinate of the target node.
 * @param ty - The y coordinate of the target node.
 */
export function calculateArrowAngle(
  sx: number,
  sy: number,
  tx: number,
  ty: number,
) {
  // overly verbose and inversion of the y coordinates since this isn't a real xy plot (y increases as you go down)
  const slope = (-ty - -sy) / (tx - sx);
  // calculate the angle in radians based on the slope
  const angle = Math.atan(Number.isNaN(slope) ? 0 : slope);
  // convert radians to degrees. This will only ever be -90<x<90 degrees in quadrants 1 and 2
  let angleDegrees = angle * (180 / Math.PI);

  // This is the tricky part.  We need to determine how much to rotate the arrow
  // based on where the target node is in relation to the source node, along with the
  // number of degrees the angle is from the x axis.  Comments below are based on
  // North, East, South, West directions for clarity
  if (angleDegrees < 0 && ty > sy) {
    // slope is negative and the target is pointing SE (quadrant 2). just absolute the degrees
    angleDegrees = Math.abs(angleDegrees);
  } else if (angleDegrees <= 0 && ty <= sy && tx < sx) {
    // slope is negative and the target is pointing NW (quadrant 4). abs the degrees and rotate another 180
    angleDegrees = Math.abs(angleDegrees) + 180;
  } else if (angleDegrees >= 0 && ty <= sy) {
    // slope is positive and target is NE (quadrant 1). Make the degrees negative to point this direction
    angleDegrees = -angleDegrees;
  } else if (angleDegrees > 0 && ty > sy) {
    // slope is positive and target is SW (quadrant 3).
    angleDegrees = 180 - angleDegrees;
  }

  return angleDegrees;
}

export function getAutoLayoutedElements(nodes: Node[], edges: Edge[]) {
  const dagreGraph = new dagre.graphlib.Graph().setDefaultEdgeLabel(() => ({}));
  dagreGraph.setGraph({ rankdir: "TB" });

  nodes.forEach((node) => {
    // giving aisle nodes extra height by default due to their design
    const manualHeight = node.data.nodeType === "ict-aisle-node" ? 750 : 200;
    dagreGraph.setNode(node.id, {
      width: node.width || 500,
      height: node.height || manualHeight,
    });
  });

  edges.forEach((edge) => {
    dagreGraph.setEdge(edge.source, edge.target);
  });

  dagre.layout(dagreGraph);

  const newNodes = nodes.map((node) => {
    const nodeWithPosition = dagreGraph.node(node.id);
    const newNode: Node = {
      ...node,
      targetPosition: Position.Top,
      sourcePosition: Position.Bottom,
      // We are shifting the dagre node position (anchor=center center) to the top left
      // so it matches the React Flow node anchor point (top left).
      position: {
        x: nodeWithPosition.x - nodeWithPosition.width / 2,
        y: nodeWithPosition.y - nodeWithPosition.height / 2,
      },
    };

    return newNode;
  });

  return { nodes: newNodes, edges };
}

export const formatMetricValue = (
  metricValue: number | string | null,
): string => {
  if (metricValue === null) return "N/A";
  if (typeof metricValue === "number") {
    const formatter = new Intl.NumberFormat(undefined, {
      minimumFractionDigits: 0,
      maximumFractionDigits: 1,
    });
    return formatter.format(metricValue);
  }
  return metricValue;
};
