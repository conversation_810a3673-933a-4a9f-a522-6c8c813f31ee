import { vi } from "vitest";
import { render, screen } from "../../../../../../test-utils";
import { EdgeDetailPanel } from "../edge-detail-panel";
import { ictApi } from "../../../../../api/ict-api";
import { useApiErrorState } from "../../../../../hooks/use-api-error-state";

// Mock Carbon components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

// Mock the ICT API
vi.mock("../../../../../api/ict-api", () => {
  return {
    ictApi: {
      client: {
        useQuery: vi.fn(),
      },
    },
  };
});

// Mock the useApiErrorState hook
vi.mock("../../../../../hooks/use-api-error-state", () => ({
  useApiErrorState: vi.fn(),
}));

describe("EdgeDetailPanel", () => {
  const onClearSelectionMock = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup the mocks for each test
    const mockUseQuery = ictApi.client.useQuery as any;
    mockUseQuery.mockReturnValue({
      data: {
        metricGroups: [
          {
            title: "default",
            metrics: [
              {
                id: "edge:throughput:outbound:inbound",
                value: 120,
                units: "units/hr",
              },
            ],
          },
        ],
      },
      isLoading: false,
      error: null,
    });

    (useApiErrorState as any).mockReturnValue(false);
  });

  it("renders loading state correctly", () => {
    (ictApi.client.useQuery as any).mockReturnValueOnce({
      data: undefined,
      isLoading: true,
      error: null,
    });

    render(
      <EdgeDetailPanel
        edgeId="edge-1"
        view="facility"
        onClearSelection={onClearSelectionMock}
      />,
    );

    expect(screen.getByTestId("loading-view")).toBeInTheDocument();
  });

  it("renders error state correctly", () => {
    (ictApi.client.useQuery as any).mockReturnValueOnce({
      data: undefined,
      isLoading: false,
      error: new Error("Failed to fetch data"),
    });

    (useApiErrorState as any).mockReturnValueOnce(true);

    render(
      <EdgeDetailPanel
        edgeId="edge-1"
        view="facility"
        onClearSelection={onClearSelectionMock}
      />,
    );

    expect(screen.getByText("No data available")).toBeInTheDocument();
  });

  it("renders empty state when no metrics are available", () => {
    // Override the mock for this test
    (ictApi.client.useQuery as any).mockReturnValueOnce({
      data: { metricGroups: [] },
      isLoading: false,
      error: null,
    });

    render(
      <EdgeDetailPanel
        edgeId="edge-1"
        view="facility"
        onClearSelection={onClearSelectionMock}
      />,
    );

    expect(
      screen.getByText("No metrics available for this element"),
    ).toBeInTheDocument();
  });

  it("renders metrics correctly", () => {
    render(
      <EdgeDetailPanel
        edgeId="edge-1"
        view="facility"
        onClearSelection={onClearSelectionMock}
      />,
    );

    // Check that the edge title is rendered correctly
    expect(screen.getByText("Outbound to Inbound")).toBeInTheDocument();

    // Check that metrics are rendered
    expect(screen.getByText("Units per Hour:")).toBeInTheDocument();
    expect(screen.getByText("120")).toBeInTheDocument();
  });

  it("formats metric values correctly", () => {
    (ictApi.client.useQuery as any).mockReturnValueOnce({
      data: {
        metricGroups: [
          {
            title: "default",
            metrics: [
              {
                id: "edge:throughput:outbound:inbound",
                value: 120.5,
                units: "units/hr",
              },
            ],
          },
        ],
      },
      isLoading: false,
      error: null,
    });

    render(
      <EdgeDetailPanel
        edgeId="edge-1"
        view="facility"
        onClearSelection={onClearSelectionMock}
      />,
    );

    expect(screen.getByText("120.5")).toBeInTheDocument();
  });

  it("handles null metric values correctly", () => {
    (ictApi.client.useQuery as any).mockReturnValueOnce({
      data: {
        metricGroups: [
          {
            title: "default",
            metrics: [
              {
                id: "edge:throughput:outbound:inbound",
                value: null,
                units: "units/hr",
              },
            ],
          },
        ],
      },
      isLoading: false,
      error: null,
    });

    render(
      <EdgeDetailPanel
        edgeId="edge-1"
        view="facility"
        onClearSelection={onClearSelectionMock}
      />,
    );

    expect(screen.getByText("N/A")).toBeInTheDocument();
  });

  it("calls onClearSelection when close button is clicked", () => {
    render(
      <EdgeDetailPanel
        edgeId="edge-1"
        view="facility"
        onClearSelection={onClearSelectionMock}
      />,
    );

    const closeButton = screen.getByTestId("close-button");
    closeButton.click();

    expect(onClearSelectionMock).toHaveBeenCalledTimes(1);
  });

  it("parses node names correctly from edge metric id", () => {
    (ictApi.client.useQuery as any).mockReturnValueOnce({
      data: {
        metricGroups: [
          {
            title: "default",
            metrics: [
              {
                id: "edge:throughput:dms:miniload",
                value: 150,
                units: "units/hr",
              },
            ],
          },
        ],
      },
      isLoading: false,
      error: null,
    });

    render(
      <EdgeDetailPanel
        edgeId="edge-1"
        view="facility"
        onClearSelection={onClearSelectionMock}
      />,
    );

    expect(screen.getByText("Dms to Miniload")).toBeInTheDocument();
  });
});
