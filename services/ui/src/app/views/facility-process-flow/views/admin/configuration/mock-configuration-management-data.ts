import { FactConfigurationData, MetricConfiguration } from "./types";

interface MockFactData {
  data: FactConfigurationData[];
}

export const mockFactData: MockFactData = {
  data: [
    {
      active: true,
      enabled: true,
      fact_name: "bin_utilization",
      num_configurations: 4,
    },
    {
      active: true,
      enabled: true,
      fact_name: "connection_movement",
      num_configurations: 4,
    },
    {
      active: true,
      enabled: true,
      fact_name: "fault_event",
      num_configurations: 4,
    },
    {
      active: true,
      enabled: true,
      fact_name: "multishuttle_movement",
      num_configurations: 4,
    },
    {
      active: true,
      enabled: true,
      fact_name: "pick",
      num_configurations: 4,
    },
    {
      active: true,
      enabled: true,
      fact_name: "pick_activity",
      num_configurations: 4,
    },
    {
      active: true,
      enabled: true,
      fact_name: "vehicle_movement",
      num_configurations: 4,
    },
    {
      active: true,
      enabled: true,
      fact_name: "viz_event_log",
      num_configurations: 4,
    },
  ],
};

interface MockMetricConfigurationData {
  data: MetricConfiguration[];
}

export const mockMetricData: MockMetricConfigurationData = {
  data: [
    {
      active: true,
      config_type: "node",
      enabled: true,
      fact_name: "multishuttle_movement",
      graph_operation: "node",
      hu_id: "lift_id",
      label: "Lift",
      match_conditions: { event_type: "^lift_move$" },
      metric_name: "multishuttle_total_storage_movements",
      metric_units: "/hr",
      name_formula: "",
      node_name: "multishuttle",
      parent_nodes: ["Zone A"],
      redis_operation: "incr",
      views: ["operations", "dashboard"],
    },
    {
      active: true,
      config_type: "node",
      enabled: true,
      fact_name: "multishuttle_movement",
      graph_operation: "node",
      hu_id: "lift_id",
      label: "Lift",
      match_conditions: { event_type: "^lift_move$" },
      metric_name: "multishuttle_total_storage_movements",
      metric_units: "/hr",
      name_formula: "",
      node_name: "multishuttle",
      parent_nodes: ["Zone A"],
      redis_operation: "incr",
      views: ["operations", "dashboard"],
    },
    {
      active: true,
      config_type: "node",
      enabled: true,
      fact_name: "multishuttle_movement",
      graph_operation: "node",
      hu_id: "lift_id",
      label: "Lift",
      match_conditions: { event_type: "^lift_move$" },
      metric_name: "multishuttle_total_storage_movements",
      metric_units: "/hr",
      name_formula: "",
      node_name: "multishuttle",
      parent_nodes: ["Zone A"],
      redis_operation: "incr",
      views: ["operations", "dashboard"],
    },
    {
      active: true,
      config_type: "node",
      enabled: true,
      fact_name: "multishuttle_movement",
      graph_operation: "node",
      hu_id: "lift_id",
      label: "Lift",
      match_conditions: { event_type: "^lift_move$" },
      metric_name: "multishuttle_total_storage_movements",
      metric_units: "/hr",
      name_formula: "",
      node_name: "multishuttle",
      parent_nodes: ["Zone A"],
      redis_operation: "incr",
      views: ["operations", "dashboard"],
    },
  ],
};

export const mockMetricConfigDetailData: MetricConfiguration = {
  active: true,
  config_type: "node",
  enabled: true,
  fact_name: "multishuttle_movement",
  graph_operation: "node",
  hu_id: "lift_id",
  label: "Lift",
  match_conditions: { event_type: "^lift_move$" },
  metric_name: "multishuttle_total_storage_movements",
  metric_units: "/hr",
  name_formula: "",
  node_name: "multishuttle",
  parent_nodes: ["Zone A"],
  redis_operation: "incr",
  views: ["operations", "dashboard"],
};
