import { Breadcrumb, BreadcrumbItem } from "@carbon/react";

export interface ConfigBreadcrumb {
  label: string;
  isCurrentPage: boolean;
  onClick: () => void;
}

interface ConfigBreadcrumbsProps {
  breadcrumbs: ConfigBreadcrumb[];
}

export function ConfigBreadcrumbs(props: ConfigBreadcrumbsProps) {
  const { breadcrumbs } = props;

  const handleCrumbClick = (crumb: ConfigBreadcrumb) => {
    if (!crumb.isCurrentPage) {
      crumb.onClick();
    }
  };

  return (
    <div
      data-testid="process-flow-config-breadcrumbs"
      className={"flowChartBreadcrumbs"}
    >
      <Breadcrumb noTrailingSlash={true}>
        {breadcrumbs.map((crumb: ConfigBreadcrumb) => (
          <BreadcrumbItem
            key={crumb.label}
            className={`${crumb.isCurrentPage ? "breadcrumb" : ""}`}
            onClick={() => handleCrumbClick(crumb)}
            isCurrentPage={crumb.isCurrentPage}
          >
            {crumb.label}
          </BreadcrumbItem>
        ))}
      </Breadcrumb>
    </div>
  );
}
