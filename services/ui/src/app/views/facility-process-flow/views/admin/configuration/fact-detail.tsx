import { Dispatch, SetStateAction, useEffect } from "react";
import { ConfigBreadcrumb } from "./configuration-breadcrumbs";
import { SelectedItem } from "./configuration-management";

interface FactDetailProps {
  name: string;
  setBreadcrumbs: Dispatch<SetStateAction<ConfigBreadcrumb[]>>;
  setSelectedItem: Dispatch<SetStateAction<SelectedItem | null>>;
}

export function FactDetail({
  name,
  setBreadcrumbs,
  setSelectedItem,
}: FactDetailProps) {
  useEffect(() => {
    setBreadcrumbs([
      {
        label: "Configuration Management",
        isCurrentPage: false,
        onClick: () => setSelectedItem(null),
      },
      {
        label: "View Sample Data",
        isCurrentPage: true,
        onClick: () => {},
      },
    ]);
  }, []);
  return (
    <div>
      <h3>Fact Details for: {name}</h3>
      {/* Replace below with actual detail rendering */}
      <p>
        This is where detailed data for <strong>{name}</strong> will go.
      </p>
    </div>
  );
}
