import { calculateArrowAngle } from "./utils";

describe("calculateArrowAngle", () => {
  // Test cases for different quadrants
  it("should calculate correct angle for points in first quadrant (NE)", () => {
    // Source at (0,0), target at (1,-1) - pointing NE
    const angle = calculateArrowAngle(0, 0, 1, -1);
    expect(angle).toBeCloseTo(-45);
  });

  it("should calculate correct angle for points in second quadrant (NW)", () => {
    // Source at (0,0), target at (-1,-1) - pointing NW
    const angle = calculateArrowAngle(0, 0, -1, -1);
    expect(angle).toBeCloseTo(225);
  });

  it("should calculate correct angle for points in third quadrant (SW)", () => {
    // Source at (0,0), target at (-1,1) - pointing SW
    const angle = calculateArrowAngle(0, 0, -1, 1);
    expect(angle).toBeCloseTo(135);
  });

  it("should calculate correct angle for points in fourth quadrant (SE)", () => {
    // Source at (0,0), target at (1,1) - pointing SE
    const angle = calculateArrowAngle(0, 0, 1, 1);
    expect(angle).toBeCloseTo(45);
  });

  // Test cases for horizontal and vertical lines
  it("should calculate correct angle for horizontal line right", () => {
    const angle = calculateArrowAngle(0, 0, 1, 0);
    expect(angle).toBeCloseTo(0);
  });

  it("should calculate correct angle for horizontal line left", () => {
    const angle = calculateArrowAngle(0, 0, -1, 0);
    expect(angle).toBeCloseTo(180);
  });

  it("should calculate correct angle for vertical line up", () => {
    const angle = calculateArrowAngle(0, 0, 0, -1);
    expect(angle).toBeCloseTo(-90);
  });

  it("should calculate correct angle for vertical line down", () => {
    const angle = calculateArrowAngle(0, 0, 0, 1);
    expect(angle).toBeCloseTo(90);
  });

  // Test cases for diagonal lines with different slopes
  it("should calculate correct angle for steep positive slope in quadrant 1", () => {
    const angle = calculateArrowAngle(0, 0, 1, -2);
    expect(angle).toBeCloseTo(-63.43, 1);
  });

  it("should calculate correct angle for shallow positive slope in quadrant 1", () => {
    const angle = calculateArrowAngle(0, 0, 2, -1);
    expect(angle).toBeCloseTo(-26.57, 1);
  });

  it("should calculate correct angle for steep negative slope in quadrant 4", () => {
    const angle = calculateArrowAngle(0, 0, -1, -2);
    expect(angle).toBeCloseTo(243.4, 1);
  });

  it("should calculate correct angle for shallow negative slope in quadrant 4", () => {
    const angle = calculateArrowAngle(0, 0, -2, -1);
    expect(angle).toBeCloseTo(206.6, 1);
  });

  // Test cases for points with negative coordinates
  it("should handle negative coordinates correctly", () => {
    const angle = calculateArrowAngle(-5, -5, -3, -7);
    expect(angle).toBeCloseTo(-45);
  });

  // Edge case: same point
  it("should handle same point coordinates", () => {
    const angle = calculateArrowAngle(0, 0, 0, 0);
    expect(Math.abs(angle)).toBe(0); // Default to 0 when points are the same
  });
});
