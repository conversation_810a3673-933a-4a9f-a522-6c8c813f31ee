.react-flow__node {
    
      background-color: var(--cds-chat-bubble-agent);
      border-color: var(--cds-border-strong);
      border-radius: 3px;
      border-style: solid;
      border-width: 1px;
      border-radius: 8px;
      box-shadow: 0px 4px 4px 0px var(--cds-shadow);
      font-size: 12px;
      overflow: hidden;
      text-transform: capitalize;

      .react-flow__handle {
        visibility: hidden;
      }
    
}

.contentWrapper {
    flex: 1;
    padding: 1rem;
    overflow: auto;
    display: flex;
    flex-direction: row;
    gap: 1rem;
  }

  .mainPanel {
    padding: 1rem;
    background-color: var(--cds-layer);
    border-radius: 4px;
    height: 100%;
    flex: 1;
  }

.detail-panel.hidden,
.detail-panel.hidden .detail-content {
  width: 0px;
  padding: 0px;
}
  
  .detail-panel {
    width: 400px;
    transition: width 250ms ease-in;
    background-color: var(--cds-layer);
    border-radius: 4px;
  }

  .detail-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding: 25px 35px;
    overflow: hidden;
    position: relative;
    height: 100%;
    text-transform: capitalize;

  }

  .aisle-node {
    padding: 8px 12px;
  }
  
  .aisle-node-content {
    display: flex;

    align-items: center;
    font-size: 14px;
  }
