import { Node, <PERSON> } from "reactflow";
import { EquipmentSectionData, AisleData } from "./types";

export const initialNodes: Node<EquipmentSectionData | AisleData>[] = [
  {
    id: "1",
    type: "accordion",
    data: { sectionName: "Section 1", kiValue: "0.5", isParent: true },
    position: { x: 250, y: 25 },
  },
  {
    id: "2",
    type: "accordion",
    data: {
      sectionName: "Section 2",
      unitCount: 3,
      bufferTime: "6 min",
      units: [
        {
          name: "Name 1",
          type: "Parallel",
          subUnits: ["1.1", "1.2", "1.3"],
          extraCapacity: 0.25,
          extraCapacityKiValues: [0.1, 0.2, 0.233],
        },
        { name: "Name 2", kiValue: 0.5 },
        { name: "Name 3", kiValue: 0.5 },
      ],
    },
    position: { x: 100, y: 125 },
  },
  {
    id: "3",
    type: "accordion",
    data: { sectionName: "Section 3", kiValue: "0.5" },
    position: { x: 250, y: 250 },
  },
  {
    id: "4",
    type: "aisle",
    data: { aisleName: "Aisle A1" },
    position: { x: 400, y: 125 },
  },
];

export const initialEdges: Edge[] = [
  { id: "e1-2", source: "1", target: "2" },
  { id: "e2-3", source: "2", target: "3", animated: true },
  { id: "e1-4", source: "1", target: "4" },
];
