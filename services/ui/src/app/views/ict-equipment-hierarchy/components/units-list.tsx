import { FC } from "react";
import { OrderedList, ListItem } from "@carbon/react";
import { EquipmentUnit } from "../types";
import { ExtraCapacity } from "./extra-capacity";

export const UnitsList: FC<{ units: EquipmentUnit[] }> = ({ units }) => (
  <OrderedList style={{ paddingLeft: "1.5rem", margin: 0 }}>
    {units.map((unit, index) => (
      <ListItem key={index} style={{ listStylePosition: "inside" }}>
        {unit.type} Unit {unit.name}
        {unit.kiValue && <span> | Ki - {unit.kiValue}</span>}
        {unit.subUnits && (
          <OrderedList
            nested
            style={{ paddingLeft: "1rem", marginTop: "0.25rem" }}
          >
            {unit.subUnits.map((subUnit, subIndex) => (
              <ListItem key={subIndex} style={{ listStyleType: "lower-alpha" }}>
                {subUnit}
              </ListItem>
            ))}
          </OrderedList>
        )}
        {unit.extraCapacity && (
          <ExtraCapacity
            extraCapacity={unit.extraCapacity}
            extraCapacityKiValues={unit.extraCapacityKiValues}
          />
        )}
      </ListItem>
    ))}
  </OrderedList>
);
