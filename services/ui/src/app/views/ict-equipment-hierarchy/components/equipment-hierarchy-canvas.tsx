import { useState, useCallback, useMemo, useEffect, FC } from "react";
import {
  NodeChange,
  EdgeChange,
  ReactFlow,
  applyEdgeChanges,
  applyNodeChanges,
  Node,
  Edge,
  useReactFlow,
} from "reactflow";
import { EquipmentSectionData, AisleData } from "../types";
import { initialNodes, initialEdges } from "../initial-data";
import { EquipmentSection } from "./equipment-section";
import { AisleNode } from "./aisle-node";

interface EquipmentHierarchyCanvasProps {
  onToggleDetailPanel: () => void;
  isDetailPanelOpen: boolean;
}

export const EquipmentHierarchyCanvas: FC<EquipmentHierarchyCanvasProps> = ({
  onToggleDetailPanel,
  isDetailPanelOpen,
}) => {
  const [nodes, setNodes] =
    useState<Node<EquipmentSectionData | AisleData>[]>(initialNodes);
  const [edges, setEdges] = useState<Edge[]>(initialEdges);
  const { fitView } = useReactFlow();

  const onNodesChange = useCallback(
    (changes: NodeChange[]) =>
      setNodes((nds) => applyNodeChanges(changes, nds)),
    [],
  );

  const onEdgesChange = useCallback(
    (changes: EdgeChange[]) =>
      setEdges((eds) => applyEdgeChanges(changes, eds)),
    [],
  );

  useEffect(() => {
    const handleAutoFit = () => {
      fitView({ duration: 600 });
    };

    window.addEventListener("triggerAutoFit", handleAutoFit);
    return () => {
      window.removeEventListener("triggerAutoFit", handleAutoFit);
    };
  }, [fitView]);

  const enrichAisleNode = useCallback(
    (node: Node<EquipmentSectionData | AisleData>) =>
      node.type === "aisle"
        ? {
            ...node,
            data: {
              ...node.data,
              onToggleDetailPanel,
              isDetailPanelOpen,
            },
          }
        : node,
    [onToggleDetailPanel, isDetailPanelOpen],
  );

  const updatedNodes = useMemo(
    () => nodes.map(enrichAisleNode),
    [nodes, enrichAisleNode],
  );

  const nodeTypes = useMemo(
    () => ({
      accordion: EquipmentSection,
      aisle: AisleNode,
    }),
    [],
  );

  return (
    <ReactFlow
      nodes={updatedNodes}
      edges={edges}
      nodeTypes={nodeTypes}
      onNodesChange={onNodesChange}
      onEdgesChange={onEdgesChange}
      fitView
      proOptions={{ hideAttribution: true }}
      zoomOnScroll
      panOnDrag={true}
      nodesFocusable={false}
      zoomOnDoubleClick={false}
      selectionKeyCode={"Shift"}
    />
  );
};
