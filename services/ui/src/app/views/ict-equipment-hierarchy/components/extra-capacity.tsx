import { FC } from "react";
import { OrderedList, Layer, ListItem } from "@carbon/react";

export const ExtraCapacity: FC<{
  extraCapacity: number;
  extraCapacityKiValues?: number[];
}> = ({ extraCapacity, extraCapacityKiValues }) => (
  <Layer withBackground level={2} style={{ padding: ".5rem" }}>
    <div>Extra Capacity - {extraCapacity * 100}%</div>
    <div>Ki based on failed parallel paths</div>
    {extraCapacityKiValues && (
      <OrderedList nested>
        {extraCapacityKiValues.map((ki, index) => (
          <ListItem key={index}>{ki.toFixed(3)}</ListItem>
        ))}
      </OrderedList>
    )}
  </Layer>
);
