import { FC } from "react";
import { NodeProps, Handle, Position, useReactFlow } from "reactflow";
import { Accordion, AccordionItem } from "@carbon/react";
import { EquipmentSectionData } from "../types";
import { FITVIEW_DELAY_MS } from "../constants";
import { SectionHeader } from "./section-header";
import { UnitsList } from "./units-list";

export const EquipmentSection: FC<NodeProps<EquipmentSectionData>> = ({
  data,
}) => {
  const { sectionName, kiValue, unitCount, bufferTime, units, isParent } = data;
  const { fitView } = useReactFlow();

  const handleAccordionToggle = () => {
    setTimeout(() => {
      fitView({ duration: 600 });
    }, FITVIEW_DELAY_MS);
  };

  return (
    <>
      {!isParent && <Handle type="target" position={Position.Top} />}
      <Accordion>
        <AccordionItem
          title={<SectionHeader sectionName={sectionName} kiValue={kiValue} />}
          onHeadingClick={handleAccordionToggle}
        >
          {unitCount && (
            <div>
              <strong>{unitCount} Units</strong>
            </div>
          )}
          {bufferTime && (
            <div>
              <strong>Buffer time - {bufferTime}</strong>
            </div>
          )}
          {units && units.length > 0 && (
            <div style={{ paddingTop: "1rem" }}>
              <UnitsList units={units} />
            </div>
          )}
        </AccordionItem>
      </Accordion>
      <Handle type="source" position={Position.Bottom} />
    </>
  );
};
