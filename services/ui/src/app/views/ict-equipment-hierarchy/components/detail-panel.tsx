import { FC } from "react";
import { Button } from "@carbon/react";
import { Close } from "@carbon/react/icons";

interface DetailPanelProps {
  isDetailPanelOpen: boolean;
  toggleDetailPanel: () => void;
}

export const DetailPanel: FC<DetailPanelProps> = ({
  isDetailPanelOpen,
  toggleDetailPanel,
}) => {
  return (
    <div
      className={`detail-panel ${isDetailPanelOpen ? "visible" : "hidden"}`}
      data-testid="detail-panel"
    >
      <div className="detail-content">
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <h4>TITLE</h4>
          <Button
            data-testid="close-button"
            hasIconOnly
            renderIcon={() => <Close />}
            size="sm"
            onClick={toggleDetailPanel}
            tooltipPosition="left"
            iconDescription="Close detail panel"
          />
        </div>
        <h1>Detail Panel</h1>
      </div>
    </div>
  );
};
