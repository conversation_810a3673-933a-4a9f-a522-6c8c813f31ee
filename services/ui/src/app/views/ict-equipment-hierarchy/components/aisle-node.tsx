import { FC } from "react";
import { NodeProps, <PERSON>le, Position, useReactFlow } from "reactflow";
import { Button, Stack } from "@carbon/react";
import { ChevronDown, ChevronRight } from "@carbon/react/icons";
import { AisleData } from "../types";
import { FITVIEW_DELAY_MS } from "../constants";

export const AisleNode: FC<NodeProps<AisleData>> = ({ data }) => {
  const { aisleName, onToggleDetailPanel, isDetailPanelOpen } = data;
  const { fitView } = useReactFlow();

  const handleToggle = () => {
    onToggleDetailPanel?.();
    setTimeout(() => {
      fitView({ duration: 600 });
    }, FITVIEW_DELAY_MS);
  };

  return (
    <>
      <Handle type="target" position={Position.Top} />
      <div className="aisle-node">
        <Stack orientation="horizontal" gap={2} className="aisle-node-content">
          <span>{aisleName}</span>
          <Button
            kind="ghost"
            size="sm"
            onClick={handleToggle}
            renderIcon={
              isDetailPanelOpen
                ? () => <ChevronRight size={16} />
                : () => <ChevronDown size={16} />
            }
          >
            Details
          </Button>
        </Stack>
      </div>
      <Handle type="source" position={Position.Bottom} />
    </>
  );
};
