import "./equipment-hierarchy.css";
import { ViewBar } from "../../components/view-bar/view-bar";
import { ReactFlowProvider } from "reactflow";
import { useState, useCallback } from "react";
import "reactflow/dist/style.css";
import { FITVIEW_DELAY_MS } from "./constants";
import { EquipmentHierarchyCanvas, DetailPanel } from "./components";

export default function IctEquipmentHierarchyView() {
  const [isDetailPanelOpen, setIsDetailPanelOpen] = useState(false);

  const toggleDetailPanel = useCallback(() => {
    setIsDetailPanelOpen((prev) => {
      const newState = !prev;

      setTimeout(() => {
        window.dispatchEvent(new CustomEvent("triggerAutoFit"));
      }, FITVIEW_DELAY_MS);

      return newState;
    });
  }, []);

  return (
    <div
      data-testid="equipment-hierarchy"
      style={{ height: "calc(100vh - 48px)" }}
    >
      <ViewBar title="Equipment Hierarchy" />
      <div
        data-testid="equipment-hierarchy-chart-container"
        className="contentWrapper"
      >
        <div className="mainPanel">
          <div style={{ height: "calc(100vh - 260px)" }}>
            <ReactFlowProvider>
              <EquipmentHierarchyCanvas
                onToggleDetailPanel={toggleDetailPanel}
                isDetailPanelOpen={isDetailPanelOpen}
              />
            </ReactFlowProvider>
          </div>
        </div>
        <DetailPanel
          isDetailPanelOpen={isDetailPanelOpen}
          toggleDetailPanel={toggleDetailPanel}
        />
      </div>
    </div>
  );
}
