export type UnitType = "Parallel" | "Serial";

export type EquipmentUnit = {
  name: string;
  type?: UnitType;
  kiValue?: number;
  subUnits?: string[];
  extraCapacity?: number;
  extraCapacityKiValues?: number[];
};

export type EquipmentSectionData = {
  sectionName: string;
  kiValue?: string;
  unitCount?: number;
  units?: EquipmentUnit[];
  bufferTime?: string;
  isParent?: boolean;
};

export type AisleData = {
  aisleName: string;
  onToggleDetailPanel?: () => void;
  isDetailPanelOpen?: boolean;
};
