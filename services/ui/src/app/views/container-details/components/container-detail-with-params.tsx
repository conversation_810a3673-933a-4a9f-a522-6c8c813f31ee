import { useParams, useNavigate, useSearchParams } from "react-router";
import { ContainerDetail } from "../../../widgets/container-list/container-list-widget/components/container-detail/container-detail";
import { ContainerListEndpointType } from "../../../widgets/container-list/container-list-widget/container-list-widget";

/**
 * Container Detail With Params
 *
 * A wrapper component that extracts the container ID from URL parameters
 * and datasource from query parameters, then passes them to the ContainerDetail component.
 */
export function ContainerDetailWithParams() {
  const { containerId } = useParams<{ containerId: string }>();
  const [searchParams] = useSearchParams();
  const datasource = searchParams.get(
    "datasource",
  ) as ContainerListEndpointType;
  const navigate = useNavigate();

  const handleBack = () => {
    // Navigate back to the previous page
    navigate(-1);
  };

  if (!containerId) {
    return <div>Container ID not found</div>;
  }

  if (!datasource) {
    return <div>Datasource not found</div>;
  }

  return (
    <ContainerDetail
      datasource={datasource}
      containerId={containerId}
      onBack={handleBack}
    />
  );
}
