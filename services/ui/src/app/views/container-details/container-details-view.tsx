import { Route, Routes } from "react-router";
import { BaseViewProps } from "../view-registry.types";
import { ContainerDetailWithParams } from "./components/container-detail-with-params";

/**
 * Containers View
 *
 * A view that handles the container detail routes.
 * Route to the container detail view when a container is selected from the container list.
 */
export function ContainerDetailView(_props: BaseViewProps) {
  return (
    <Routes>
      <Route path=":containerId" element={<ContainerDetailWithParams />} />
    </Routes>
  );
}

export default ContainerDetailView;
