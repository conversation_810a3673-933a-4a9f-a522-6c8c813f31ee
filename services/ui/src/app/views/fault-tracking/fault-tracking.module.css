.tabsContainer {
    :global(.cds--tabs) {
        position: relative;
        top: 2px;
    }
}

.manualEntryWrapper {
  display: flex;
  flex-direction: row;
  gap: 16px;
  width: 100%;
  align-items: stretch;
  padding-top: var(--cds-layout-density-padding-inline-local);
}

.manualEntryLeft {
  flex: 0 0 420px;
  padding-bottom: 0;
}

.manualEntryRight {
  flex: 1;
  min-height: 100px;
}

.manualEntryForm {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.inputWrapper {
  margin-bottom: 1rem;
}

.timePickerWrapper {
  display: flex;
  gap: 1rem;
}

.submitWrapper {
  display: flex;
  justify-content: flex-end;
  margin-right: calc(-1 * var(--cds-layout-density-padding-inline-local));
  margin-top: 48px;
}

.submitButton {
  width: calc(50% + var(--cds-layout-density-padding-inline-local));
}

.fullWidth {
  width: 100%;
}

.fullWidthDatePicker {
  width: 100%;
}

.fullWidthDatePicker :global(.cds--date-picker) {
  width: 100%;
}

.fullWidthDatePicker :global(.cds--date-picker__input) {
  width: 100%;
}

.manualEntryDatagrid {
  height: 300px;
  overflow: auto;
}

.modalCalculationText {
  max-width: calc(100% - 35px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  margin-bottom: 0.25rem;
}

.radioGroup {
  margin-top: 1rem;
}

.modalHeader {
  display: flex;
  flex-direction: column-reverse;
}

.hint {
  font-size: 14px;
  margin-top: 1rem;
}

.commentBox {
  margin-top: 1rem;
}

.datePickerContainer {
  margin-top: 1rem;
  display: flex;
  justify-content: space-between;
  gap: 1rem;
}

.timeGroup {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 1rem;
  flex: 1;
}

.timeGroup :global(.cds--form-item) {
  flex-grow: 0;
}

.timeGroup > div {
  width: 100%;
}

.flexRow {
  display: flex;
}

.dateTimeRow {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  gap: 0.75rem;
}

.timebox {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  max-width: 185px;
}

.millisInput {
  max-width: 150px;

  :global(.cds--text-input__field-outer-wrapper) {
    max-width: 114px;
  }

  label {
    white-space: nowrap;
  }
}

.ampmInput {
  margin-top: 1rem;
  min-width: 85px;
}

.durationField {
  margin-top: 2rem;
}

.initialValueSection {
  margin-top: 1rem;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.initialValueSection .hint {
  margin-top: 0;
  flex: 1;
}

.durationField:first-of-type {
  margin-top: 2rem;
}

.dropdownRow {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.dropdownHalf {
  flex: 1;
  min-width: 0; /* Prevents flex items from overflowing */
}

.actionButton {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}