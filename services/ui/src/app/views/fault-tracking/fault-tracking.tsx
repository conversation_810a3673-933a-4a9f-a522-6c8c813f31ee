import { ViewBar } from "../../components/view-bar/view-bar";
import { FaultTrackingTable } from "./components/fault-tracking-table";
import { useFaultTrackingData } from "./hooks/use-fault-tracking-data";
import { useFaultTrackingTable } from "./hooks/use-fault-tracking-table";

export default function FaultTrackingView() {
  const faultData = useFaultTrackingData();
  const tableConfig = useFaultTrackingTable(faultData.data?.data);

  return (
    <>
      <ViewBar title="Fault Tracking" />
      <FaultTrackingTable
        tableKey={tableConfig.tableKey}
        columns={tableConfig.columns}
        data={tableConfig.tableData}
        rowCount={faultData.data?.metadata.totalResults ?? 0}
        isLoading={faultData.isLoading}
        isFetching={faultData.isFetching}
        error={faultData.error}
        pagination={faultData.pagination}
        setPagination={faultData.setPagination}
        sorting={faultData.sorting}
        setSorting={faultData.setSorting}
        setColumnFilters={faultData.setColumnFilters}
        setGlobalFilter={faultData.setGlobalFilter}
        rowSelection={tableConfig.rowSelection}
        onRowSelectionChange={tableConfig.setRowSelection}
        onRefreshClick={() => {
          tableConfig.handleRefresh();
          faultData.refetch();
        }}
      />
    </>
  );
}
