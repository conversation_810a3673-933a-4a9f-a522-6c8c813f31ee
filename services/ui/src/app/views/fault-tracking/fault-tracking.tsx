import { ViewBar } from "../../components/view-bar/view-bar";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@carbon/react";
import { useLocation, useNavigate } from "react-router";
import { useMemo, useState } from "react";
// import { useCallback } from "react";
// import type {
//   ColumnFiltersState,
//   PaginationState,
//   SortingState,
// } from "@tanstack/react-table";
// import { ictApi } from "../../api/ict-api";
// import { transformFilters } from "../../api/util/filter-transform-util";
// import { formatISOTimeToReadableDateTime } from "../../utils/date-util";
// import { useConfigSetting } from "../../config/hooks/use-config";
import { FaultTrackingTable } from "./components/fault-tracking-table";
import { EditTimingsModal } from "./components/edit-timings-modal";
import { ManualEntryModal } from "./components/manual-entry-modal";
// import type { FilterState } from "../../components/datagrid/types";
// import type { FaultAlarm, SortField } from "./types";
import styles from "./fault-tracking.module.css";
import UpdateCalculationModal from "./update-calculation-modal";
import {
  faultTrackingMockedData,
  createFaultTrackingColumns,
  FaultTrackingData,
} from "./mock-fault-tracking-data";
import { useNotification } from "../../components/toast/use-notification";

export default function FaultTrackingView() {
  const location = useLocation();
  const navigate = useNavigate();
  const { success: showSuccessToast } = useNotification();

  // const { setting: timezoneConfig } = useConfigSetting("site-time-zone");

  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});
  const [tableKey, setTableKey] = useState(0);
  const [tableData, setTableData] = useState(faultTrackingMockedData);
  const [isManualEntryModalOpen, setIsManualEntryModalOpen] = useState(false);
  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);
  const [isEditTimingsModalOpen, setIsEditTimingsModalOpen] = useState(false);
  const [resetTrigger, setResetTrigger] = useState(0);
  const [timingData, setTimingData] = useState({
    startDate: "",
    endDate: "",
    startTime: "",
    endTime: "",
    startAmPm: "AM",
    endAmPm: "AM",
    startMillis: "",
    endMillis: "",
    duration: "00:00:00:000",
    hasChanges: false,
    isValid: false,
  });
  const [editingFault, setEditingFault] = useState<FaultTrackingData | null>(
    null,
  );

  // Get selected rows data
  const selectedFaults = useMemo(() => {
    return tableData.filter((_row, index) => rowSelection[index]);
  }, [rowSelection, tableData]);

  // Handle timing changes from TimingCalculation component
  const handleTimingChange = (timing: {
    startDate: string;
    endDate: string;
    startTime: string;
    endTime: string;
    startAmPm: string;
    endAmPm: string;
    startMillis: string;
    endMillis: string;
    duration: string;
    hasChanges: boolean;
    isValid: boolean;
  }) => {
    setTimingData(timing);
  };

  // const [pagination, setPagination] = useState<PaginationState>({
  //   pageIndex: 0,
  //   pageSize: 50,
  // });

  // const [sorting, setSorting] = useState<SortingState>([
  //   // default sort by most recent start time
  //   { id: "timing.startTime", desc: true },
  // ]);

  // const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  // const [globalFilter, setGlobalFilter] = useState<string>("");

  // const [dateRange, _setDateRange] = useState({
  //   start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
  //   end_date: new Date(),
  // });

  const tabs = [
    { route: "system-availability", label: "System Availability" },
    { route: "equipment-hierarchy", label: "Equipment Hierarchy" },
    { route: "fault-tracking", label: "Fault Tracking" },
  ];

  const selectedIndex = useMemo(() => {
    return tabs.findIndex((tab) => location.pathname.endsWith(tab.route));
  }, [location.pathname]);

  function handleTabChange(state: { selectedIndex: number }) {
    navigate(`/${tabs[state.selectedIndex].route}`);
  }

  // Table columns
  const columns = useMemo(
    () =>
      createFaultTrackingColumns({
        onEditTimings: (faultId) => {
          const fault = tableData.find((row) => row.faultId === faultId);
          if (fault) {
            setEditingFault(fault);
            // Open the modal
            setIsEditTimingsModalOpen(true);
          }
        },
        onUpdateCalculation: (faultId) => {
          const fault = tableData.find((row) => row.faultId === faultId);
          if (fault) {
            // Set the selected fault
            setRowSelection({ [tableData.indexOf(fault)]: true });

            // Open the modal
            setIsUpdateModalOpen(true);
          }
        },
      }),
    [tableData],
  );
  // transform data for API
  // const sortFields: SortField[] = useMemo(() => {
  //   const fields = sorting.map((sort) => {
  //     // Map UI column names to API field names
  //     // Note: Datagrid converts dots to underscores, but initial sorting uses dots
  //     let columnName = sort.id;
  //     if (sort.id === "timing.startTime" || sort.id === "timing_startTime") {
  //       columnName = "startTime";
  //     } else if (sort.id === "timing.endTime" || sort.id === "timing_endTime") {
  //       columnName = "endTime";
  //     } else if (sort.id === "location.area" || sort.id === "location_area") {
  //       columnName = "area";
  //     } else if (
  //       sort.id === "location.section" ||
  //       sort.id === "location_section"
  //     ) {
  //       columnName = "section";
  //     } else if (
  //       sort.id === "location.equipment" ||
  //       sort.id === "location_equipment"
  //     ) {
  //       columnName = "equipment";
  //     } else if (sort.id === "id") {
  //       columnName = "faultId";
  //     } else if (sort.id === "status") {
  //       columnName = "status";
  //     }

  //     return {
  //       columnName,
  //       isDescending: sort.desc,
  //     };
  //   });
  //   return fields;
  // }, [sorting]);

  // const apiFilters = useMemo(() => {
  //   const filters = transformFilters(columnFilters);
  //   const finalFilters = filters || { type: "multiple" };
  //   return finalFilters;
  // }, [columnFilters]);

  function handleAddManualEntry() {
    setIsManualEntryModalOpen(true);
  }

  function handleCloseManualEntry() {
    setIsManualEntryModalOpen(false);
  }

  function handleManualEntrySubmit(entry: {
    faultId: string;
    faultStartTime: string;
    faultEndTime: string;
    faultDuration: string;
    notes: string;
    sectionName: string;
    equipmentName: string;
    faultDescription: string;
    faultTag: string;
  }) {
    // Create the new fault entry
    const newFault = {
      faultId: entry.faultId,
      sectionArea: "",
      sectionName: entry.sectionName,
      equipmentName: entry.equipmentName,
      faultDescription: entry.faultDescription,
      faultTag: "",
      faultStartTime: entry.faultStartTime,
      faultEndTime: entry.faultEndTime,
      faultDuration: entry.faultDuration,
      systemAffected: "",
      reportedBy: "",
      errorCode: "",
      notes: "",
      removalStatus: "Included",
      removalReason: "",
      severity: "600",
      comments: entry.notes,
      actions: "",
      user: "",
      updatedStartTime: "",
      updatedEndTime: "",
      updatedDuration: "",
    };

    // Add the new fault to the table data
    setTableData([newFault, ...tableData]);
  }

  function handleUpdateCalculation() {
    setIsUpdateModalOpen(true);
  }

  function handleUpdateCalculationSave(
    updates: Array<{
      faultId: string;
      removalStatus: string;
      removalReason: string;
      comments: string;
    }>,
  ) {
    // Update the table data
    setTableData((prevData) => {
      return prevData.map((row) => {
        const update = updates.find((u) => u.faultId === row.faultId);
        if (update) {
          return {
            ...row,
            removalStatus: update.removalStatus,
            removalReason: update.removalReason,
            comments: update.comments,
          };
        }
        return row;
      });
    });

    // Show success toast with fault IDs and status
    if (updates.length > 0) {
      const faultIds = updates.map((update) => update.faultId).join(", ");
      const status = updates[0].removalStatus.toLowerCase(); // All updates have same status
      const isPlural = updates.length > 1;
      const areIs = isPlural ? "are" : "is";
      const idText = isPlural ? "IDs" : "ID";

      showSuccessToast(
        `Alarm ${idText} ${faultIds} ${areIs} now ${status} from the availability calculation.`,
      );
    }

    setRowSelection({});
  }

  function handleEditFaultTimings() {
    if (selectedFaults.length === 1) {
      setEditingFault(selectedFaults[0]);
      setIsEditTimingsModalOpen(true);
    }
  }

  // Create batch actions for fault tracking
  const faultBatchActions = useMemo(() => {
    // Type guard for removalStatus
    function hasRemovalStatus(
      row: FaultTrackingData,
    ): row is FaultTrackingData & { removalStatus: string } {
      return (
        typeof row === "object" &&
        row !== null &&
        "removalStatus" in row &&
        typeof row.removalStatus === "string"
      );
    }

    const hasIncluded = selectedFaults.some(
      (row) => hasRemovalStatus(row) && row.removalStatus === "Included",
    );
    const hasExcluded = selectedFaults.some(
      (row) => hasRemovalStatus(row) && row.removalStatus === "Excluded",
    );
    const disableUpdateCalculation = hasIncluded && hasExcluded;
    const selectedRowCount = Object.values(rowSelection).filter(Boolean).length;

    const actions = [];

    // Always add Edit Fault Timings action, but disable when multiple rows selected
    actions.push({
      label: "Edit Fault Timings",
      onClick: handleEditFaultTimings,
      disabled: selectedRowCount !== 1,
      tooltip:
        selectedRowCount !== 1
          ? "Fault Timings can only be edited for individual faults"
          : undefined,
    });

    // Always add Update Calculation action, but disable when mixed statuses
    actions.push({
      label: "Update Calculation Status",
      onClick: handleUpdateCalculation,
      disabled: disableUpdateCalculation,
      tooltip: disableUpdateCalculation
        ? "Please select faults that are either all included or all excluded to update the calculation."
        : undefined,
    });

    return actions;
  }, [
    selectedFaults,
    rowSelection,
    handleEditFaultTimings,
    handleUpdateCalculation,
  ]);

  function handleCloseEditTimings() {
    // Reset timing data state
    setTimingData({
      startDate: "",
      endDate: "",
      startTime: "",
      endTime: "",
      startAmPm: "AM",
      endAmPm: "AM",
      startMillis: "",
      endMillis: "",
      duration: "00:00:00:000",
      hasChanges: false,
      isValid: false,
    });
    // Clear editing fault and row selection
    setEditingFault(null);
    setRowSelection({});
    // Trigger TimingCalculation reset
    setResetTrigger((prev) => prev + 1);
    setIsEditTimingsModalOpen(false);
  }

  function handleSaveTimings() {
    if (editingFault) {
      const fault = editingFault;
      const faultId = fault.faultId;

      // Capture original values for comparison
      const originalStartTime = fault.faultStartTime;
      const originalEndTime = fault.faultEndTime;

      // Parse original dates and times for detailed comparison
      const [originalStartDate, ...originalStartTimeParts] =
        originalStartTime.split(" ");
      const [originalEndDate, ...originalEndTimeParts] =
        originalEndTime.split(" ");
      const originalStartTimeOnly = originalStartTimeParts.join(" ");
      const originalEndTimeOnly = originalEndTimeParts.join(" ");

      // Format the new times with seconds and milliseconds
      const [sSec = "00", sMs = "000"] = (
        timingData.startMillis || "00:000"
      ).split(":");
      const [eSec = "00", eMs = "000"] = (
        timingData.endMillis || "00:000"
      ).split(":");
      const updatedStartTime = `${timingData.startDate} ${timingData.startTime}:${sSec}.${sMs} ${timingData.startAmPm}`;
      const updatedEndTime = `${timingData.endDate} ${timingData.endTime}:${eSec}.${eMs} ${timingData.endAmPm}`;
      const newStartTimeOnly = `${timingData.startTime} ${timingData.startAmPm}`;
      const newEndTimeOnly = `${timingData.endTime} ${timingData.endAmPm}`;

      // Determine what changed
      const startDateChanged = originalStartDate !== timingData.startDate;
      const endDateChanged = originalEndDate !== timingData.endDate;
      const startTimeChanged = originalStartTimeOnly !== newStartTimeOnly;
      const endTimeChanged = originalEndTimeOnly !== newEndTimeOnly;

      setTableData((prevData) => {
        return prevData.map((row) => {
          if (row.faultId === editingFault.faultId) {
            return {
              ...row,
              updatedStartTime,
              updatedEndTime,
              updatedDuration: (() => {
                try {
                  const parts = timingData.duration.split(":");
                  if (parts.length >= 3) {
                    const hours = parseInt(parts[0], 10) || 0;
                    const minutes = parseInt(parts[1], 10) || 0;

                    if (hours > 0) {
                      if (minutes > 0) {
                        return `${hours} hrs ${minutes} mins`;
                      } else {
                        return `${hours} hrs`;
                      }
                    } else {
                      return `${minutes} mins`;
                    }
                  }
                } catch (e) {
                  console.error("Duration formatting error:", e);
                }
                return timingData.duration;
              })(),
            };
          }
          return row;
        });
      });

      // Show success toast based on what changed
      const changedFields = [];
      if (startDateChanged) changedFields.push("Start date");
      if (endDateChanged) changedFields.push("End date");
      if (startTimeChanged) changedFields.push("Start time");
      if (endTimeChanged) changedFields.push("End time");

      if (changedFields.length > 0) {
        let changesMessage = "";
        if (changedFields.length === 1) {
          changesMessage = `${changedFields[0]} is`;
        } else if (changedFields.length === 2) {
          changesMessage = `${changedFields[0]} and ${changedFields[1]} are`;
        } else {
          // For 3 or more changes, use comma-separated list with "and" before the last item
          const lastField = changedFields.pop();
          changesMessage = `${changedFields.join(", ")} and ${lastField} are`;
        }

        showSuccessToast(
          `Alarm ID ${faultId} - ${changesMessage} successfully updated.`,
        );
      }

      setEditingFault(null);
      setRowSelection({});
      setIsEditTimingsModalOpen(false);
    }
  }

  function handleRefresh() {
    setTableKey((prev) => prev + 1);
  }
  // // api request body for logging
  // const apiRequestBody = useMemo(() => {
  //   const body = {
  //     start_date: dateRange.start_date.toISOString(),
  //     end_date: dateRange.end_date.toISOString(),
  //     limit: pagination.pageSize,
  //     offset: pagination.pageIndex * pagination.pageSize, // Note: offset, not page
  //     filters: apiFilters,
  //     sortFields,
  //     ...(globalFilter !== "" && { searchString: globalFilter }),
  //   };
  //   return body;
  // }, [
  //   dateRange.start_date.getTime(), // Use timestamp to avoid Date object reference issues
  //   dateRange.end_date.getTime(),
  //   pagination.pageSize,
  //   pagination.pageIndex,
  //   apiFilters,
  //   sortFields,
  //   globalFilter,
  // ]);

  // // API Query Hook
  // const { data, error, isLoading, isFetching, refetch } =
  //   ictApi.client.useQuery(
  //     "post",
  //     "/availability/alarms/list",
  //     {
  //       body: apiRequestBody,
  //     },
  //     {
  //       enabled: true,
  //       keepPreviousData: true,
  //       placeholderData: (prev) => prev,
  //       retry: false,
  //       refetchOnWindowFocus: false,
  //       staleTime: 30000,
  //       cacheTime: 300000,
  //     },
  //   );

  // const alarmData = useMemo(() => {
  //   const transformedData = (data?.data ?? []) as unknown as FaultAlarm[];
  //   return transformedData;
  // }, [data]);

  // // table columns (updated to match Alarm interface)
  // const columns = useMemo(
  //   () => [
  //     { accessorKey: "location.area", header: "Area" },
  //     { accessorKey: "location.section", header: "Section" },
  //     { accessorKey: "location.equipment", header: "Equipment" },
  //     { accessorKey: "id", header: "Alarm ID", enableSorting: false },
  //     { accessorKey: "title", header: "Title", enableSorting: false },
  //     {
  //       accessorKey: "description",
  //       header: "Description",
  //       enableSorting: false,
  //     },
  //     { accessorKey: "tag", header: "Tag", enableSorting: false },
  //     {
  //       accessorKey: "timing.startTime",
  //       header: "Start Time",
  //       cell: ({ getValue }: { getValue: () => Date | string | undefined }) => {
  //         const value = getValue();
  //         return value
  //           ? formatISOTimeToReadableDateTime(
  //               value as string,
  //               timezoneConfig?.value as string,
  //             )
  //           : "";
  //       },
  //     },
  //     {
  //       accessorKey: "timing.endTime",
  //       header: "End Time",
  //       cell: ({ getValue }: { getValue: () => Date | string | undefined }) => {
  //         const value = getValue();
  //         return value
  //           ? formatISOTimeToReadableDateTime(
  //               value as string,
  //               timezoneConfig?.value as string,
  //             )
  //           : "";
  //       },
  //     },
  //     {
  //       accessorKey: "timing.duration",
  //       header: "Duration",
  //       enableSorting: false,
  //     },
  //     { accessorKey: "status", header: "Status" },
  //     { accessorKey: "reason", header: "Reason", enableSorting: false },
  //   ],
  //   [timezoneConfig?.value],
  // );

  // const handleRefresh = useCallback(() => {
  //   refetch();
  // }, [refetch]);

  // const handleAddManualEntry = useCallback(() => {
  //   setIsAddingManualEntry(true);
  // }, []);

  // const handleCancelManualEntry = useCallback(() => {
  //   setIsAddingManualEntry(false);
  // }, []);

  // const handleUpdateCalculation = useCallback(() => {
  //   alert("Update Calculation clicked");
  // }, []);

  // const handlePageChange = useCallback((newPagination: PaginationState) => {
  //   setPagination(newPagination);
  // }, []);

  // const handleSort = useCallback((newSorting: SortingState) => {
  //   setSorting(newSorting);
  // }, []);

  // const handleFilter = useCallback((newFilters: FilterState) => {
  //   // Convert FilterState to ColumnFiltersState
  //   const newColumnFilters = Object.entries(newFilters.filters).map(
  //     ([id, value]) => ({
  //       id,
  //       value,
  //     }),
  //   );
  //   setColumnFilters(newColumnFilters);
  //   setGlobalFilter(newFilters.globalFilter);
  // }, []);

  // const handleRowSelectionChange = useCallback(
  //   (
  //     updaterOrValue:
  //       | Record<string, boolean>
  //       | ((prev: Record<string, boolean>) => Record<string, boolean>),
  //   ) => {
  //     setRowSelection(updaterOrValue);
  //   },
  //   [],
  // );

  function renderTabs() {
    return tabs.map((tab, idx) => (
      <Tab key={`tab-${tab.route}-${idx}`}>{tab.label}</Tab>
    ));
  }

  return (
    <>
      <Tabs selectedIndex={selectedIndex} onChange={handleTabChange}>
        <div className={styles.tabsContainer}>
          <ViewBar title="Availability" isExtended>
            <TabList aria-label="Fault Tracking Tabs">{renderTabs()}</TabList>
          </ViewBar>
        </div>
        <div className={styles.tabsContent}>
          <TabPanels>
            <TabPanel>System Availability content goes here.</TabPanel>
            <TabPanel>Equipment Hierarchy content goes here.</TabPanel>
            <TabPanel>
              <FaultTrackingTable
                tableKey={tableKey}
                columns={columns}
                data={tableData}
                rowSelection={rowSelection}
                onRowSelectionChange={setRowSelection}
                onRefreshClick={handleRefresh}
                onAddManualEntry={handleAddManualEntry}
                batchActions={faultBatchActions}
              />
              <ManualEntryModal
                isOpen={isManualEntryModalOpen}
                onClose={handleCloseManualEntry}
                onSubmit={handleManualEntrySubmit}
              />
              <UpdateCalculationModal
                isOpen={isUpdateModalOpen}
                selectedFaults={selectedFaults}
                onClose={() => setIsUpdateModalOpen(false)}
                onSave={handleUpdateCalculationSave}
              />
            </TabPanel>
          </TabPanels>
        </div>
      </Tabs>

      {/* Edit Timings Modal */}
      <EditTimingsModal
        isOpen={isEditTimingsModalOpen}
        fault={editingFault}
        resetTrigger={resetTrigger}
        onClose={handleCloseEditTimings}
        onSave={handleSaveTimings}
        onTimingChange={handleTimingChange}
        isSaveDisabled={!timingData.hasChanges || !timingData.isValid}
      />
    </>
  );
}
