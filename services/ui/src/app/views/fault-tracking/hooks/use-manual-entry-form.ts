import { useState } from "react";

export type TimingState = {
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  startAmPm: string;
  endAmPm: string;
  startMillis: string;
  endMillis: string;
  duration: string;
  hasChanges: boolean;
  isValid: boolean;
};

export type ManualEntryValues = {
  notes: string;
  alarmDescription: string;
  sectionImpacted: string;
  equipmentName: string;
};

export type ManualEntryErrors = {
  sectionImpacted?: string;
  equipmentName?: string;
  alarmDescription?: string;
};

export function useManualEntryForm() {
  const INITIAL_VALUES: ManualEntryValues = {
    notes: "",
    alarmDescription: "",
    sectionImpacted: "",
    equipmentName: "",
  };

  const INITIAL_TIMING: TimingState = {
    startDate: "",
    endDate: "",
    startTime: "",
    endTime: "",
    startAmPm: "AM",
    endAmPm: "AM",
    startMillis: "",
    endMillis: "",
    duration: "00:00:00:000",
    hasChanges: false,
    isValid: false,
  };

  const [values, setValues] = useState<ManualEntryValues>(INITIAL_VALUES);
  const [timing, setTiming] = useState<TimingState>(INITIAL_TIMING);
  const [errors, setErrors] = useState<ManualEntryErrors>({});
  const [touched, setTouched] = useState({
    sectionImpacted: false,
    equipmentName: false,
    alarmDescription: false,
    submitted: false,
  });
  const [resetTrigger, setResetTrigger] = useState(0);

  function validate(overrides?: Partial<ManualEntryValues>): {
    errors: ManualEntryErrors;
    isValid: boolean;
  } {
    const v = { ...values, ...overrides };
    const nextErrors: ManualEntryErrors = {};
    if (!v.sectionImpacted)
      nextErrors.sectionImpacted = "Section Impacted is required.";
    if (!v.equipmentName)
      nextErrors.equipmentName = "Equipment Name is required.";
    if (!v.alarmDescription?.trim())
      nextErrors.alarmDescription = "Alarm Description is required.";

    const timingValid =
      !!timing.startDate &&
      !!timing.endDate &&
      !!timing.startTime &&
      !!timing.endTime &&
      !!timing.isValid;

    return {
      errors: nextErrors,
      isValid: Object.keys(nextErrors).length === 0 && timingValid,
    };
  }

  function setField<K extends keyof ManualEntryValues>(
    key: K,
    value: ManualEntryValues[K],
  ) {
    setValues((prev) => ({ ...prev, [key]: value }));
    setTouched((prev) => ({ ...prev, [key]: true }));
    const { errors: nextErrors } = validate({
      [key]: value,
    } as Partial<ManualEntryValues>);
    setErrors(nextErrors);
  }

  function handleTimingChange(next: TimingState) {
    setTiming(next);
    if (touched.submitted) {
      const { errors: nextErrors } = validate();
      setErrors(nextErrors);
    }
  }

  function resetForm() {
    setValues(INITIAL_VALUES);
    setTiming(INITIAL_TIMING);
    setErrors({});
    setTouched({
      sectionImpacted: false,
      equipmentName: false,
      alarmDescription: false,
      submitted: false,
    });
    setResetTrigger((prev) => prev + 1);
  }

  function buildEntry() {
    const { errors: validationErrors, isValid } = validate();
    setErrors(validationErrors);
    if (!isValid) return null;

    const [startSec = "00", startMs = "000"] = (
      timing.startMillis || "00:000"
    ).split(":");
    const [endSec = "00", endMs = "000"] = (timing.endMillis || "00:000").split(
      ":",
    );
    const faultStartTime = `${timing.startDate} ${timing.startTime}:${startSec}.${startMs} ${timing.startAmPm}`;
    const faultEndTime = `${timing.endDate} ${timing.endTime}:${endSec}.${endMs} ${timing.endAmPm}`;

    const newFaultId = `F${Math.floor(Math.random() * 10000)}`;

    return {
      faultId: newFaultId,
      faultStartTime,
      faultEndTime,
      faultDuration: timing.duration,
      notes: values.notes || "",
      sectionName: values.sectionImpacted,
      equipmentName: values.equipmentName,
      faultDescription: values.alarmDescription,
      faultTag: "",
    } as const;
  }

  function submit():
    | { entry: ReturnType<typeof buildEntry> }
    | { entry: null } {
    setTouched((prev) => ({ ...prev, submitted: true }));
    const entry = buildEntry();
    return { entry } as const;
  }

  return {
    values,
    errors,
    touched,
    timing,
    resetTrigger,
    setNotes: (v: string) => setField("notes", v),
    setAlarmDescription: (v: string) => setField("alarmDescription", v),
    setSectionImpacted: (v: string) => setField("sectionImpacted", v),
    setEquipmentName: (v: string) => setField("equipmentName", v),
    handleTimingChange,
    resetForm,
    validate,
    submit,
    isSubmitDisabled:
      !timing.isValid ||
      !values.sectionImpacted ||
      !values.equipmentName ||
      !values.alarmDescription.trim(),
  };
}
