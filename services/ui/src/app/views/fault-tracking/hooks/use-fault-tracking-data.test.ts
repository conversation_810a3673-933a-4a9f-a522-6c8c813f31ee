import { renderHook, act } from "@testing-library/react";
import { vi, beforeEach, describe, it, expect } from "vitest";
import { useFaultTrackingData } from "./use-fault-tracking-data";

// Mock the ictApi module
vi.mock("../../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: vi.fn(),
    },
  },
}));

// Mock the filter transform utility
vi.mock("../../../api/util/filter-transform-util", () => ({
  transformFilters: vi.fn((filters) =>
    filters.map((filter: any) => ({
      field: filter.id,
      value: filter.value,
    })),
  ),
}));

describe("useFaultTrackingData", () => {
  let mockUseQuery: any;
  let mockRefetch: any;

  beforeEach(async () => {
    vi.clearAllMocks();

    // Get the mocked function
    const { ictApi } = await import("../../../api/ict-api");
    mockUseQuery = ictApi.client.useQuery as any;
    mockRefetch = vi.fn();

    // Setup default mock return value
    mockUseQuery.mockReturnValue({
      data: {
        data: [
          {
            faultId: "FAULT-001",
            description: "Test fault",
            status: "Included",
            location: { area: "A", section: "S1", equipment: "EQ1" },
            timing: {
              origStartTime: "2024-01-01 10:00:00 AM",
              origEndTime: "2024-01-01 11:00:00 AM",
              origDuration: "01:00:00",
            },
          },
        ],
        metadata: { totalResults: 1 },
      },
      error: null,
      isLoading: false,
      isFetching: false,
      refetch: mockRefetch,
    });
  });

  it("should initialize with correct default values", () => {
    const { result } = renderHook(() => useFaultTrackingData());

    expect(result.current.columnFilters).toEqual([]);
    expect(result.current.globalFilter).toBe("");
    expect(result.current.sorting).toEqual([
      { id: "faultStartTime", desc: true },
    ]);
    expect(result.current.pagination).toEqual({ pageIndex: 0, pageSize: 50 });
  });

  it("should return API data and states correctly", () => {
    const { result } = renderHook(() => useFaultTrackingData());

    expect(result.current.data).toBeDefined();
    expect(result.current.data?.data).toHaveLength(1);
    expect(result.current.data?.data[0].faultId).toBe("FAULT-001");
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isFetching).toBe(false);
    expect(result.current.error).toBeNull();
    expect(result.current.refetch).toBe(mockRefetch);
  });

  it("should update column filters", () => {
    const { result } = renderHook(() => useFaultTrackingData());

    act(() => {
      result.current.setColumnFilters([{ id: "status", value: "Included" }]);
    });

    expect(result.current.columnFilters).toEqual([
      { id: "status", value: "Included" },
    ]);
  });

  it("should update global filter", () => {
    const { result } = renderHook(() => useFaultTrackingData());

    act(() => {
      result.current.setGlobalFilter("test search");
    });

    expect(result.current.globalFilter).toBe("test search");
  });

  it("should update sorting state", () => {
    const { result } = renderHook(() => useFaultTrackingData());

    act(() => {
      result.current.setSorting([{ id: "faultEndTime", desc: false }]);
    });

    expect(result.current.sorting).toEqual([
      { id: "faultEndTime", desc: false },
    ]);
  });

  it("should update pagination state", () => {
    const { result } = renderHook(() => useFaultTrackingData());

    act(() => {
      result.current.setPagination({ pageIndex: 2, pageSize: 25 });
    });

    expect(result.current.pagination).toEqual({ pageIndex: 2, pageSize: 25 });
  });

  it("should call useQuery with correct API endpoint", () => {
    renderHook(() => useFaultTrackingData());

    expect(mockUseQuery).toHaveBeenCalledWith(
      "post",
      "/availability/alarms/list",
      expect.objectContaining({
        body: expect.any(Object),
      }),
      expect.objectContaining({
        enabled: true,
        keepPreviousData: true,
        retry: false,
        refetchOnWindowFocus: false,
        staleTime: 30000,
        cacheTime: 300000,
      }),
    );
  });

  it("should build API request body with correct structure", () => {
    renderHook(() => useFaultTrackingData());

    const callArgs = mockUseQuery.mock.calls[0];
    const requestConfig = callArgs[2];
    const requestBody = requestConfig.body;

    expect(requestBody).toEqual(
      expect.objectContaining({
        start_date: expect.any(String),
        end_date: expect.any(String),
        limit: 50,
        offset: 0,
        filters: [],
        sortFields: expect.arrayContaining([
          expect.objectContaining({
            columnName: "startTime",
            isDescending: true,
          }),
        ]),
      }),
    );
  });

  it("should include search string when global filter is set", () => {
    const { result } = renderHook(() => useFaultTrackingData());

    act(() => {
      result.current.setGlobalFilter("test search");
    });

    // Check the latest call to useQuery
    const latestCall =
      mockUseQuery.mock.calls[mockUseQuery.mock.calls.length - 1];
    const requestBody = latestCall[2].body;

    expect(requestBody).toEqual(
      expect.objectContaining({
        searchString: "test search",
      }),
    );
  });

  it("should map sorting field names correctly", () => {
    const { result } = renderHook(() => useFaultTrackingData());

    act(() => {
      result.current.setSorting([
        { id: "sectionArea", desc: true },
        { id: "sectionName", desc: false },
        { id: "equipmentName", desc: true },
        { id: "faultStartTime", desc: false },
        { id: "faultId", desc: true },
      ]);
    });

    const latestCall =
      mockUseQuery.mock.calls[mockUseQuery.mock.calls.length - 1];
    const requestBody = latestCall[2].body;

    expect(requestBody.sortFields).toEqual([
      { columnName: "area", isDescending: true },
      { columnName: "section", isDescending: false },
      { columnName: "equipment", isDescending: true },
      { columnName: "startTime", isDescending: false },
      { columnName: "faultId", isDescending: true },
    ]);
  });

  it("should map location field names correctly with dot and underscore notation", () => {
    const { result } = renderHook(() => useFaultTrackingData());

    act(() => {
      result.current.setSorting([
        { id: "location.area", desc: true },
        { id: "location_area", desc: false },
        { id: "location.section", desc: true },
        { id: "location_section", desc: false },
        { id: "location.equipment", desc: true },
        { id: "location_equipment", desc: false },
      ]);
    });

    const latestCall =
      mockUseQuery.mock.calls[mockUseQuery.mock.calls.length - 1];
    const requestBody = latestCall[2].body;

    expect(requestBody.sortFields).toEqual([
      { columnName: "area", isDescending: true },
      { columnName: "area", isDescending: false },
      { columnName: "section", isDescending: true },
      { columnName: "section", isDescending: false },
      { columnName: "equipment", isDescending: true },
      { columnName: "equipment", isDescending: false },
    ]);
  });

  it("should map timing field names correctly with dot and underscore notation", () => {
    const { result } = renderHook(() => useFaultTrackingData());

    act(() => {
      result.current.setSorting([
        { id: "timing.updatedDuration", desc: true },
        { id: "timing_updatedDuration", desc: false },
        { id: "timing.origStartTime", desc: true },
        { id: "timing_origStartTime", desc: false },
        { id: "comments", desc: true },
      ]);
    });

    const latestCall =
      mockUseQuery.mock.calls[mockUseQuery.mock.calls.length - 1];
    const requestBody = latestCall[2].body;

    expect(requestBody.sortFields).toEqual([
      { columnName: "updatedDuration", isDescending: true },
      { columnName: "updatedDuration", isDescending: false },
      { columnName: "origStartTime", isDescending: true },
      { columnName: "origStartTime", isDescending: false },
      { columnName: "comments", isDescending: true },
    ]);
  });

  it("should calculate pagination offset correctly", () => {
    const { result } = renderHook(() => useFaultTrackingData());

    act(() => {
      result.current.setPagination({ pageIndex: 3, pageSize: 25 });
    });

    const latestCall =
      mockUseQuery.mock.calls[mockUseQuery.mock.calls.length - 1];
    const requestBody = latestCall[2].body;

    expect(requestBody.limit).toBe(25);
    expect(requestBody.offset).toBe(75); // pageIndex 3 * pageSize 25
  });

  it("should handle date range correctly", () => {
    renderHook(() => useFaultTrackingData());

    const callArgs = mockUseQuery.mock.calls[0];
    const requestBody = callArgs[2].body;

    expect(requestBody.start_date).toMatch(
      /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/,
    );
    expect(requestBody.end_date).toMatch(
      /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/,
    );

    // Verify date range is approximately 30 days
    const startDate = new Date(requestBody.start_date);
    const endDate = new Date(requestBody.end_date);
    const daysDiff = Math.round(
      (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24),
    );
    expect(daysDiff).toBe(30);
  });

  it("should transform filters using transformFilters utility", () => {
    const { result } = renderHook(() => useFaultTrackingData());

    act(() => {
      result.current.setColumnFilters([
        { id: "status", value: "Included" },
        { id: "location", value: "Area A" },
      ]);
    });

    const latestCall =
      mockUseQuery.mock.calls[mockUseQuery.mock.calls.length - 1];
    const requestBody = latestCall[2].body;

    // Based on our mock transformFilters implementation
    expect(requestBody.filters).toEqual([
      { field: "status", value: "Included" },
      { field: "location", value: "Area A" },
    ]);
  });

  it("should handle loading and error states from useQuery", () => {
    mockUseQuery.mockReturnValue({
      data: null,
      error: new Error("API Error"),
      isLoading: true,
      isFetching: true,
      refetch: mockRefetch,
    });

    const { result } = renderHook(() => useFaultTrackingData());

    expect(result.current.data).toBeNull();
    expect(result.current.error).toEqual(new Error("API Error"));
    expect(result.current.isLoading).toBe(true);
    expect(result.current.isFetching).toBe(true);
  });
});
