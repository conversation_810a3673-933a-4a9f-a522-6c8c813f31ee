import { describe, it, expect, vi } from "vitest";
import {
  formatMillisForDisplay,
  getFormattedMillis,
  formatTimeForDisplay,
  formatFaultTrackingDuration,
} from "./time-formatting";

// Mock the formatDuration utility from the main utils
vi.mock("../../../utils", () => ({
  formatDuration: vi.fn((ms) => {
    // Simple mock implementation for testing
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${Math.floor(ms / 1000)}s`;
    if (ms < 3600000)
      return `${Math.floor(ms / 60000)}m${Math.floor((ms % 60000) / 1000)}s`;
    return `${Math.floor(ms / 3600000)}hr${Math.floor((ms % 3600000) / 60000)}m`;
  }),
}));

describe("time-formatting", () => {
  describe("formatMillisForDisplay", () => {
    it("should return empty string for empty input", () => {
      expect(formatMillisForDisplay("")).toBe("");
      expect(formatMillisForDisplay(null as any)).toBe("");
      expect(formatMillisForDisplay(undefined as any)).toBe("");
    });

    it("should handle 1-2 digit input (seconds only)", () => {
      expect(formatMillisForDisplay("5")).toBe("05:000");
      expect(formatMillisForDisplay("15")).toBe("15:000");
    });

    it("should handle 3-5 digit input (seconds + milliseconds)", () => {
      expect(formatMillisForDisplay("123")).toBe("12:300");
      expect(formatMillisForDisplay("1234")).toBe("12:340");
      expect(formatMillisForDisplay("12345")).toBe("12:345");
    });

    it("should clean non-digit characters", () => {
      expect(formatMillisForDisplay("1a2b3")).toBe("12:300");
      expect(formatMillisForDisplay("5:")).toBe("05:000");
    });

    it("should return original value for longer input", () => {
      expect(formatMillisForDisplay("123456")).toBe("123456");
    });

    it("should handle edge cases", () => {
      expect(formatMillisForDisplay("0")).toBe("00:000");
      expect(formatMillisForDisplay("abc")).toBe("");
    });
  });

  describe("getFormattedMillis", () => {
    it("should return default for empty input", () => {
      expect(getFormattedMillis("")).toBe("00:000");
      expect(getFormattedMillis(null as any)).toBe("00:000");
      expect(getFormattedMillis(undefined as any)).toBe("00:000");
    });

    it("should return already formatted values unchanged", () => {
      expect(getFormattedMillis("15:500")).toBe("15:500");
      expect(getFormattedMillis("00:000")).toBe("00:000");
    });

    it("should format raw input values", () => {
      expect(getFormattedMillis("5")).toBe("05:000");
      expect(getFormattedMillis("123")).toBe("12:300");
    });

    it("should fallback to default for invalid input", () => {
      expect(getFormattedMillis("abc")).toBe("00:000");
    });
  });

  describe("formatTimeForDisplay", () => {
    it("should return empty string for empty input", () => {
      expect(formatTimeForDisplay("")).toBe("");
      expect(formatTimeForDisplay(null as any)).toBe("");
      expect(formatTimeForDisplay(undefined as any)).toBe("");
    });

    it("should handle 1-2 digit input (hours only)", () => {
      expect(formatTimeForDisplay("5")).toBe("5:00");
      expect(formatTimeForDisplay("12")).toBe("12:00");
    });

    it("should handle 3-4 digit input (hours + minutes)", () => {
      expect(formatTimeForDisplay("130")).toBe("1:30");
      expect(formatTimeForDisplay("1230")).toBe("12:30");
    });

    it("should clean non-digit characters", () => {
      expect(formatTimeForDisplay("1:2:3")).toBe("1:23");
      expect(formatTimeForDisplay("5h30m")).toBe("5:30");
    });

    it("should pad minutes correctly", () => {
      expect(formatTimeForDisplay("15")).toBe("15:00"); // 15 hours, 0 minutes
      expect(formatTimeForDisplay("105")).toBe("1:05"); // 1 hour, 5 minutes
    });

    it("should return original value for longer input", () => {
      expect(formatTimeForDisplay("12345")).toBe("12345");
    });

    it("should handle edge cases", () => {
      expect(formatTimeForDisplay("0")).toBe("0:00");
      expect(formatTimeForDisplay("abc")).toBe("");
    });
  });

  describe("formatFaultTrackingDuration", () => {
    it("should return empty string for undefined input", () => {
      expect(formatFaultTrackingDuration(undefined)).toBe("");
      expect(formatFaultTrackingDuration("")).toBe("");
    });

    it("should handle raw millisecond numbers", () => {
      expect(formatFaultTrackingDuration("4000")).toBe("4s");
      expect(formatFaultTrackingDuration("60000")).toBe("1m0s");
      expect(formatFaultTrackingDuration("3600000")).toBe("1hr0m");
    });

    it("should handle HH:MM:SS format with hours and minutes", () => {
      expect(formatFaultTrackingDuration("02:30:00")).toBe("2 hrs 30 mins");
      expect(formatFaultTrackingDuration("01:00:00")).toBe("1 hrs");
      expect(formatFaultTrackingDuration("00:45:00")).toBe("45 mins");
      expect(formatFaultTrackingDuration("00:00:00")).toBe("0 mins");
    });

    it("should handle hours only (no minutes)", () => {
      expect(formatFaultTrackingDuration("03:00:00")).toBe("3 hrs");
      expect(formatFaultTrackingDuration("1:00:00")).toBe("1 hrs");
    });

    it("should handle minutes only (no hours)", () => {
      expect(formatFaultTrackingDuration("00:15:00")).toBe("15 mins");
      expect(formatFaultTrackingDuration("0:05:30")).toBe("5 mins");
    });

    it("should handle HH:MM:SS:mmm format", () => {
      expect(formatFaultTrackingDuration("01:30:45:123")).toBe("1 hrs 30 mins");
      expect(formatFaultTrackingDuration("00:45:30:456")).toBe("45 mins");
    });

    it("should handle malformed input gracefully", () => {
      expect(formatFaultTrackingDuration("invalid")).toBe("invalid");
      expect(formatFaultTrackingDuration("1:2")).toBe("1:2");
    });

    it("should parse integer values correctly", () => {
      expect(formatFaultTrackingDuration("01:05:00")).toBe("1 hrs 5 mins");
      expect(formatFaultTrackingDuration("00:01:00")).toBe("1 mins");
      expect(formatFaultTrackingDuration("10:00:00")).toBe("10 hrs");
    });

    it("should handle edge case with leading zeros", () => {
      expect(formatFaultTrackingDuration("00:00:30")).toBe("0 mins");
      expect(formatFaultTrackingDuration("01:01:00")).toBe("1 hrs 1 mins");
    });

    it("should handle error cases and log errors", () => {
      const consoleSpy = vi
        .spyOn(console, "error")
        .mockImplementation(() => {});

      // This input has enough parts to be parsed as "1 hrs 2 mins"
      const malformedInput = "1:2:3:4:5:6";
      const result = formatFaultTrackingDuration(malformedInput);

      expect(result).toBe("1 hrs 2 mins"); // Parses first two parts as hours:minutes

      consoleSpy.mockRestore();
    });

    it("should distinguish between millisecond numbers and time formats", () => {
      // Pure numbers should use formatDuration
      expect(formatFaultTrackingDuration("500")).toBe("500ms");
      expect(formatFaultTrackingDuration("1500")).toBe("1s");

      // Time formats should use custom formatting
      expect(formatFaultTrackingDuration("00:01:30")).toBe("1 mins");
    });
  });
});
