import type { FaultAlarm } from "../types/types";

export function transformFaultData(rawData: FaultAlarm[]): FaultAlarm[] {
  return rawData.map((fault) => ({
    ...fault,
    // Transform status values for display
    status: transformStatusValue(fault.status),
    // Format timing values for display
    timing: {
      ...fault.timing,
      origStartTime: formatDateTime(fault.timing?.origStartTime),
      origEndTime: formatDateTime(fault.timing?.origEndTime),
      updatedStartTime: formatDateTime(fault.timing?.updatedStartTime),
      updatedEndTime: formatDateTime(fault.timing?.updatedEndTime),
    },
  }));
}

function transformStatusValue(status: string): string {
  let transformedStatus = status;

  // Transform "KEPT" to "Included"
  if (transformedStatus === "KEPT") {
    transformedStatus = "Included";
  }

  // Remove "[x]" prefix from values like "[4] Equipment Excluded"
  const bracketRegex = /^\[\d+\]\s*/;
  if (bracketRegex.test(transformedStatus)) {
    transformedStatus = transformedStatus.replace(bracketRegex, "");
  }

  return transformedStatus;
}

function formatDateTime(dateTime?: string): string {
  if (!dateTime) return "";
  return dateTime.replace(/:\d{2}\.\d{3}/, "");
}

export function getStatusColor(status: string): "blue" | "outline" {
  return status.toLowerCase() === "included" ? "blue" : "outline";
}
