import { describe, it, expect } from "vitest";
import { transformFaultData, getStatusColor } from "./cell-data-transforms";
import type { FaultAlarm } from "../types/types";

describe("data-transforms", () => {
  const mockRawData: FaultAlarm[] = [
    {
      id: "1",
      faultId: "FAULT-001",
      title: "Test fault 1",
      description: "Test fault 1",
      tag: "TAG001",
      status: "KEPT", // Should transform to "Included"
      reason: "Test reason",
      location: { area: "A", section: "S1", equipment: "EQ1" },
      timing: {
        startTime: "01/01/2024 10:00:15.123 AM",
        duration: "01:00:00",
        origStartTime: "01/01/2024 10:00:15.123 AM", // Should remove milliseconds
        origEndTime: "01/01/2024 11:00:30.456 AM",
        origDuration: "01:00:00",
        updatedStartTime: "01/01/2024 10:05:45.789 AM",
        updatedEndTime: "01/01/2024 11:05:20.012 AM",
        updatedDuration: "01:00:00",
      },
    },
    {
      id: "2",
      faultId: "FAULT-002",
      title: "Test fault 2",
      description: "Test fault 2",
      tag: "TAG002",
      status: "[4] Equipment Excluded", // Should remove bracket prefix
      reason: "Test reason",
      location: { area: "B", section: "S2", equipment: "EQ2" },
      timing: {
        startTime: "01/02/2024 12:00:00.999 PM",
        duration: "01:00:00",
        origStartTime: "01/02/2024 12:00:00.999 PM",
        origEndTime: "01/02/2024 01:00:00.000 PM",
        origDuration: "01:00:00",
      },
    },
  ];

  describe("transformFaultData", () => {
    it("should transform KEPT status to Included", () => {
      const result = transformFaultData(mockRawData);

      expect(result[0].status).toBe("Included");
    });

    it("should remove bracket prefixes from status", () => {
      const result = transformFaultData(mockRawData);

      expect(result[1].status).toBe("Equipment Excluded");
    });

    it("should format datetime strings by removing milliseconds", () => {
      const result = transformFaultData(mockRawData);

      expect(result[0].timing.origStartTime).toBe("01/01/2024 10:00 AM");
      expect(result[0].timing.origEndTime).toBe("01/01/2024 11:00 AM");
      expect(result[0].timing.updatedStartTime).toBe("01/01/2024 10:05 AM");
      expect(result[0].timing.updatedEndTime).toBe("01/01/2024 11:05 AM");
    });

    it("should handle missing datetime values", () => {
      const result = transformFaultData(mockRawData);

      expect(result[1].timing.updatedStartTime).toBe("");
      expect(result[1].timing.updatedEndTime).toBe("");
    });

    it("should preserve other fields unchanged", () => {
      const result = transformFaultData(mockRawData);

      expect(result[0].faultId).toBe("FAULT-001");
      expect(result[0].description).toBe("Test fault 1");
      expect(result[0].tag).toBe("TAG001");
      expect(result[0].location).toEqual({
        area: "A",
        section: "S1",
        equipment: "EQ1",
      });
      expect(result[0].timing.origDuration).toBe("01:00:00");
    });

    it("should handle empty array", () => {
      const result = transformFaultData([]);

      expect(result).toEqual([]);
    });

    it("should handle multiple status transformations", () => {
      const testData: FaultAlarm[] = [
        {
          ...mockRawData[0],
          status: "KEPT",
        },
        {
          ...mockRawData[0],
          status: "[2] Manual Override",
        },
        {
          ...mockRawData[0],
          status: "Normal Status",
        },
      ];

      const result = transformFaultData(testData);

      expect(result[0].status).toBe("Included");
      expect(result[1].status).toBe("Manual Override");
      expect(result[2].status).toBe("Normal Status");
    });
  });

  describe("getStatusColor", () => {
    it("should return blue for included status", () => {
      expect(getStatusColor("Included")).toBe("blue");
      expect(getStatusColor("included")).toBe("blue");
      expect(getStatusColor("INCLUDED")).toBe("blue");
    });

    it("should return outline for other statuses", () => {
      expect(getStatusColor("Excluded")).toBe("outline");
      expect(getStatusColor("Equipment Excluded")).toBe("outline");
      expect(getStatusColor("Manual Override")).toBe("outline");
      expect(getStatusColor("")).toBe("outline");
    });
  });
});
