import {
  <PERSON><PERSON>,
  <PERSON>mposed<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Toggle,
} from "@carbon/react";
import styles from "./fault-tracking.module.css";
import { FormEvent } from "react";
import { useNotification } from "../../components/toast/use-notification";
import TimingCalculation from "./timing-calculation";
import { useManualEntryForm } from "./hooks/use-manual-entry-form";
import { ManualEntryFields } from "./manual-entry-fields";

export default function ManualEntryForm({
  isOpen,
  onClose,
  onSubmit,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (entry: {
    faultId: string;
    faultStartTime: string;
    faultEndTime: string;
    faultDuration: string;
    notes: string;
    sectionName: string;
    equipmentName: string;
    faultDescription: string;
    faultTag: string;
  }) => void;
}) {
  // Using custom hook for all form state, validation and submit

  const { success: showSuccessToast, error: showErrorToast } =
    useNotification();
  const {
    values,
    errors,
    touched,
    resetTrigger,
    setNotes,
    setAlarmDescription,
    setSectionImpacted,
    setEquipmentName,
    handleTimingChange,
    resetForm,
    submit,
    isSubmitDisabled,
  } = useManualEntryForm();

  // timing change is provided by the hook as handleTimingChange

  // Submit handler
  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    try {
      const { entry } = submit();
      if (!entry) return;
      onSubmit(entry);
      showSuccessToast(`Alarm ID ${entry.faultId} added successfully.`);

      // Reset form
      resetFormFields();
      onClose();
    } catch (err) {
      showErrorToast(`Error: ${err}`);
    }
  };

  const resetFormFields = () => {
    resetForm();
  };

  const handleClose = () => {
    // Reset all form state
    resetFormFields();
    onClose();
  };

  return (
    <ComposedModal open={isOpen} onClose={handleClose} size="md">
      <ModalHeader title="Create a Manual Alarm">
        <br />
        <p className={styles.modalCalculationText}>
          Adjust the date range, start and end time, the duration will update
          automatically.
        </p>
      </ModalHeader>
      <ModalBody>
        <form
          id="manual-entry-form"
          onSubmit={handleSubmit}
          className={styles.manualEntryForm}
        >
          <Toggle
            labelText="System Availability Calculation"
            labelA="Excluded"
            labelB="Included"
            toggled={true}
            readOnly
            id="calculation-toggle-manual-entry"
          />
          <TimingCalculation
            onTimingChange={handleTimingChange}
            showRestore={false}
            resetTrigger={resetTrigger}
          />
          <ManualEntryFields
            values={values}
            errors={errors}
            touched={touched}
            onChange={{
              setNotes,
              setAlarmDescription,
              setSectionImpacted,
              setEquipmentName,
            }}
          />
        </form>
      </ModalBody>
      <ModalFooter>
        <Button kind="secondary" onClick={handleClose}>
          Cancel
        </Button>
        <Button
          type="submit"
          form="manual-entry-form"
          disabled={isSubmitDisabled}
        >
          Submit
        </Button>
      </ModalFooter>
    </ComposedModal>
  );
}
