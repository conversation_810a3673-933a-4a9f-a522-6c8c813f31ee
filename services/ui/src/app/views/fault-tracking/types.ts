export interface FaultAlarm {
  id: string;
  title: string;
  description: string;
  tag: string;
  location: {
    area: string;
    section: string;
    equipment: string;
  };
  timing: {
    startTime: Date;
    endTime?: Date;
    duration: string;
    updatedStartTime?: Date;
    updatedEndTime?: Date;
    updatedDuration?: string;
  };
  status: string;
  reason: string;
}

export interface SortField {
  columnName: string;
  isDescending: boolean;
}

export interface FaultTrackingResponse {
  data: FaultAlarm[];
  metadata: {
    page: number;
    limit: number;
    totalResults: number;
  };
}
