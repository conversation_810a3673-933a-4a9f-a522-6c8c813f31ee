export const mockedManualEntryLogData = [
  {
    id: "1",
    date: "01/05/2024",
    timestamp: "10:15:23",
    changeMade: "System 1",
    user: "Operator",
    reason: "Correction",
    notes: "Adjusted downtime value.",
  },
  {
    id: "2",
    date: "02/05/2024",
    timestamp: "14:22:10",
    changeMade: "System 2",
    user: "Technician",
    reason: "System Update",
    notes: "Auto-calculated after import.",
  },
  {
    id: "3",
    date: "03/05/2024",
    timestamp: "09:05:45",
    changeMade: "System 3",
    user: "Supervisor",
    reason: "Data Entry",
    notes: "Initial entry.",
  },
  {
    id: "4",
    date: "04/05/2024",
    timestamp: "11:30:12",
    changeMade: "System 2",
    user: "System",
    reason: "Correction",
    notes: "Fixed typo in fault description.",
  },
  {
    id: "5",
    date: "05/05/2024",
    timestamp: "13:45:00",
    changeMade: "System 5",
    user: "Operator",
    reason: "System Update",
    notes: "Updated after scheduled maintenance.",
  },
  {
    id: "6",
    date: "06/05/2024",
    timestamp: "08:10:33",
    changeMade: "System 1",
    user: "Technician",
    reason: "Correction",
    notes: "Adjusted weighted downtime.",
  },
  {
    id: "7",
    date: "07/05/2024",
    timestamp: "16:05:27",
    changeMade: "System 1",
    user: "Supervisor",
    reason: "Data Entry",
    notes: "Added missing entry for Unit 4.",
  },
  {
    id: "8",
    date: "08/05/2024",
    timestamp: "12:55:19",
    changeMade: "System 2",
    user: "System",
    reason: "System Update",
    notes: "Auto-calculated after import.",
  },
  {
    id: "9",
    date: "09/05/2024",
    timestamp: "10:40:50",
    changeMade: "System 1",
    user: "Operator",
    reason: "Correction",
    notes: "Corrected calculation status.",
  },
  {
    id: "10",
    date: "10/05/2024",
    timestamp: "15:20:05",
    changeMade: "System 1",
    user: "Technician",
    reason: "Data Entry",
    notes: "Initial entry for new subsystem.",
  },
];

export const manualEntryLogColumns = [
  { accessorKey: "date", header: "Date" },
  { accessorKey: "timestamp", header: "Timestamp" },
  { accessorKey: "changeMade", header: "Change Made" },
  { accessorKey: "user", header: "User" },
  { accessorKey: "reason", header: "Reason" },
  { accessorKey: "notes", header: "Notes" },
];

export const systemAffectedOptions = [
  { text: "System 1" },
  { text: "System 2" },
  { text: "System 3" },
  { text: "System 4" },
];

export const reportedByRoleOptions = [
  { text: "Operator" },
  { text: "Technician" },
  { text: "Supervisor" },
  { text: "System" },
];

export const errorCodeOptions = [
  { text: "E01" },
  { text: "E02" },
  { text: "E03" },
  { text: "E04" },
];

export const sectionImpactedOptions = [
  { text: "Production Line 1" },
  { text: "Production Line 2" },
  { text: "Assembly Area" },
  { text: "Quality Control" },
  { text: "Packaging" },
];

export const equipmentNameOptions = [
  { text: "Conveyor Belt A" },
  { text: "Robotic Arm B" },
  { text: "Inspection Station C" },
  { text: "Packaging Unit D" },
  { text: "Control Panel E" },
];
