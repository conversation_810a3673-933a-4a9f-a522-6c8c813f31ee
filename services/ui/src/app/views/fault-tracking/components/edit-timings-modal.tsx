import {
  Composed<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@carbon/react";
import styles from "../fault-tracking.module.css";
import { FaultAlarm } from "../types/types";
import TimingCalculation from "./timing-calculation";

type TimingChangePayload = {
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  startAmPm: string;
  endAmPm: string;
  startMillis: string;
  endMillis: string;
  duration: string;
  hasChanges: boolean;
  isValid: boolean;
};

type Props = {
  isOpen: boolean;
  fault: FaultAlarm | null;
  resetTrigger: number;
  onClose: () => void;
  onSave: () => void;
  onTimingChange: (timing: TimingChangePayload) => void;
  isSaveDisabled?: boolean;
};

export function EditTimingsModal({
  isOpen,
  fault,
  resetTrigger,
  onClose,
  onSave,
  onTimingChange,
  isSaveDisabled,
}: Props) {
  if (!fault) return null;

  const startTimeToParse =
    fault.timing.updatedStartTime || fault.timing.origStartTime;
  const endTimeToParse =
    fault.timing.updatedEndTime || fault.timing.origEndTime;

  const [startDate, ...startTimeParts] = (startTimeToParse || "").split(" ");
  // startTimeToParse.split(" ");
  const [endDate, ...endTimeParts] = (endTimeToParse || "").split(" ");

  const startTimeWithAmPm = startTimeParts.join(" ");
  const endTimeWithAmPm = endTimeParts.join(" ");

  let startTime,
    startAmPm = "AM",
    startMillis = "00:000";
  let endTime,
    endAmPm = "AM",
    endMillis = "00:000";

  if (startTimeWithAmPm.includes(".")) {
    const [timeWithSeconds, amPmPart] = startTimeWithAmPm.split(" ");
    const [time, millisecondsOnly] = timeWithSeconds.split(".");
    const [hours, minutes, seconds] = time.split(":");
    startTime = `${hours}:${minutes}`;
    startMillis = `${seconds}:${millisecondsOnly}`;
    startAmPm = amPmPart || "AM";
  } else {
    [startTime, startAmPm = "AM"] = startTimeWithAmPm.split(" ");
  }

  if (endTimeWithAmPm.includes(".")) {
    const [timeWithSeconds, amPmPart] = endTimeWithAmPm.split(" ");
    const [time, millisecondsOnly] = timeWithSeconds.split(".");
    const [hours, minutes, seconds] = time.split(":");
    endTime = `${hours}:${minutes}`;
    endMillis = `${seconds}:${millisecondsOnly}`;
    endAmPm = amPmPart || "AM";
  } else {
    [endTime, endAmPm = "AM"] = endTimeWithAmPm.split(" ");
  }

  const [originalStartDate, ...originalStartTimeParts] = (
    fault.timing.origStartTime || ""
  ).split(" ");
  const [originalEndDate, ...originalEndTimeParts] = (
    fault.timing.origEndTime || ""
  ).split(" ");
  const originalStartTimeWithAmPm = originalStartTimeParts.join(" ");
  const originalEndTimeWithAmPm = originalEndTimeParts.join(" ");

  let originalStartTime,
    originalStartAmPm = "AM",
    originalStartMillis = "00:000";
  let originalEndTime,
    originalEndAmPm = "AM",
    originalEndMillis = "00:000";

  if (originalStartTimeWithAmPm.includes(".")) {
    const [timeWithSeconds, amPmPart] = originalStartTimeWithAmPm.split(" ");
    const [time, millisecondsOnly] = timeWithSeconds.split(".");
    const [hours, minutes, seconds] = time.split(":");
    originalStartTime = `${hours}:${minutes}`;
    originalStartMillis = `${seconds}:${millisecondsOnly}`;
    originalStartAmPm = amPmPart || "AM";
  } else {
    [originalStartTime, originalStartAmPm = "AM"] =
      originalStartTimeWithAmPm.split(" ");
  }

  if (originalEndTimeWithAmPm.includes(".")) {
    const [timeWithSeconds, amPmPart] = originalEndTimeWithAmPm.split(" ");
    const [time, millisecondsOnly] = timeWithSeconds.split(".");
    const [hours, minutes, seconds] = time.split(":");
    originalEndTime = `${hours}:${minutes}`;
    originalEndMillis = `${seconds}:${millisecondsOnly}`;
    originalEndAmPm = amPmPart || "AM";
  } else {
    [originalEndTime, originalEndAmPm = "AM"] =
      originalEndTimeWithAmPm.split(" ");
  }

  return (
    <ComposedModal open={isOpen} onClose={onClose}>
      <ModalHeader className={styles.modalHeader} title="Edit Fault Timings">
        <p className={styles.modalCalculationText}>
          Selected Fault ID {fault?.faultId}
        </p>
      </ModalHeader>
      <ModalBody>
        <p className={styles.hint}>
          Adjust the date range, start and end time, the duration will update
          automatically.
        </p>
        <TimingCalculation
          initial={{
            start: {
              date: originalStartDate,
              time: originalStartTime,
              ampm: originalStartAmPm,
              millis: originalStartMillis,
            },
            end: {
              date: originalEndDate,
              time: originalEndTime,
              ampm: originalEndAmPm,
              millis: originalEndMillis,
            },
          }}
          current={{
            start: {
              date: startDate,
              time: startTime,
              ampm: startAmPm,
              millis: startMillis,
            },
            end: {
              date: endDate,
              time: endTime,
              ampm: endAmPm,
              millis: endMillis,
            },
          }}
          onTimingChange={onTimingChange}
          showRestore={true}
          resetTrigger={resetTrigger}
          allowPastDates={true}
        />
      </ModalBody>
      <ModalFooter>
        <Button kind="secondary" onClick={onClose}>
          Cancel
        </Button>
        <Button onClick={onSave} disabled={!!isSaveDisabled}>
          Save
        </Button>
      </ModalFooter>
    </ComposedModal>
  );
}
