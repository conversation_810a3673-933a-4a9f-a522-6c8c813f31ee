import { Datagrid } from "../../../components/datagrid/datagrid";
import type {
  RowSelectionState,
  OnChangeFn,
  ColumnDef,
} from "@tanstack/react-table";
import type { FaultTrackingData } from "../mock-fault-tracking-data";

type BatchAction = {
  label: string;
  onClick: () => void;
  disabled?: boolean;
  tooltip?: string;
};

type FaultTrackingTableProps = {
  tableKey: number;
  columns: ColumnDef<FaultTrackingData, string>[];
  data: FaultTrackingData[];
  rowSelection: RowSelectionState;
  onRowSelectionChange: OnChangeFn<RowSelectionState>;
  onRefreshClick: () => void;
  onAddManualEntry: () => void;
  batchActions?: BatchAction[];
};

export function FaultTrackingTable({
  tableKey,
  columns,
  data,
  rowSelection,
  onRowSelectionChange,
  onRefreshClick,
  onAddManualEntry,
  batchActions,
}: FaultTrackingTableProps) {
  return (
    <Datagrid
      key={tableKey}
      columns={columns}
      data={data}
      enableSelection={true}
      rowSelection={rowSelection}
      onRowSelectionChange={onRowSelectionChange}
      showRefreshButton={true}
      onRefreshClick={onRefreshClick}
      onAddManualEntry={onAddManualEntry}
      batchActions={batchActions}
    />
  );
}
