import {
  Composed<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ggle,
  RadioButtonGroup,
  RadioButton,
  TextArea,
} from "@carbon/react";
import { useState, useEffect } from "react";
import styles from "./fault-tracking.module.css";
import { FaultAlarm } from "../types/types";

interface UpdateCalculationModalProps {
  isOpen: boolean;
  selectedFaults: FaultAlarm[];
  onClose: () => void;
  onSave: (
    updates: Array<{
      faultId: string;
      removalStatus: string;
      removalReason: string;
      comments: string;
    }>,
  ) => void;
}

export default function UpdateCalculationModal({
  isOpen,
  selectedFaults,
  onClose,
  onSave,
}: UpdateCalculationModalProps) {
  const [isToggleChecked, setIsToggleChecked] = useState(true);
  const [removalReason, setRemovalReason] = useState("Equipment Excluded");
  const [comment, setComment] = useState("");

  // Track original values to detect changes
  const [originalValues, setOriginalValues] = useState<{
    status: string;
    reason: string;
    comment: string;
  }>({
    status: "Included",
    reason: "",
    comment: "",
  });

  // Update initial state when modal opens with selected faults
  useEffect(() => {
    if (isOpen && selectedFaults.length > 0) {
      // For simplicity, use the first fault's values as the original state
      // (since multiple faults are batch updated to the same values)
      const firstFault = selectedFaults[0];
      const originalStatus = firstFault.status;
      const originalReason = firstFault.reason || "";
      const originalComment = firstFault.comments || "";

      // Store original values
      setOriginalValues({
        status: originalStatus,
        reason: originalReason,
        comment: originalComment,
      });

      // Set initial comment from the first selected fault
      setComment(originalComment);

      // Show the opposite of the current status
      const areAllFaultsIncluded = selectedFaults.every(
        (fault) => fault.status === "Included",
      );
      // If all faults are included, default to excluded (toggle off)
      // If any fault is excluded, default to included (toggle on)
      setIsToggleChecked(!areAllFaultsIncluded);

      // Set removal reason if we're defaulting to exclude (when currently all are included)
      if (areAllFaultsIncluded) {
        // Currently all included, so we're suggesting to exclude
        setRemovalReason("Equipment Excluded");
      } else {
        // Some are excluded, so we're suggesting to include
        // But keep existing reason/comment if available
        const excludedFault = selectedFaults.find(
          (fault) => fault.status.toLowerCase() === "excluded",
        );
        if (excludedFault) {
          setRemovalReason(excludedFault.reason || "Equipment Excluded");
        } else {
          setRemovalReason("Equipment Excluded");
        }
      }
    }
  }, [isOpen, selectedFaults]);

  // Check if there are actual changes to enable/disable Submit button
  const hasChanges = () => {
    const currentStatus = isToggleChecked ? "Included" : "Excluded";

    // If status is changing, there are changes
    if (currentStatus !== originalValues.status) {
      return true;
    }

    // If status is "Excluded" and staying "Excluded", check reason and comment
    if (currentStatus === "Excluded") {
      return (
        removalReason !== originalValues.reason ||
        comment !== originalValues.comment
      );
    }

    // If status is "Included" and staying "Included", check only comment
    if (currentStatus === "Included") {
      return comment !== originalValues.comment;
    }

    return false;
  };

  const handleSave = () => {
    // Create updates for each selected fault
    const faultUpdates = selectedFaults.map((fault) => {
      const baseUpdate = {
        faultId: fault.faultId,
        removalStatus: isToggleChecked ? "Included" : "Excluded",
        removalReason: "",
        comments: comment || "",
      };

      // Only add reason if we're excluding the fault
      if (!isToggleChecked) {
        return {
          ...baseUpdate,
          removalReason,
        };
      }

      return baseUpdate;
    });

    onSave(faultUpdates);
    onClose();
  };

  const handleClose = () => {
    // Reset all modal state
    setIsToggleChecked(true);
    setRemovalReason("Equipment Excluded");
    setComment("");
    setOriginalValues({
      status: "Included",
      reason: "",
      comment: "",
    });
    onClose();
  };

  return (
    <ComposedModal open={isOpen} onClose={handleClose} size="sm">
      <ModalHeader
        className={styles.modalHeader}
        title="Update Calculation Status"
      >
        <p className={styles.modalCalculationText}>
          Selected {selectedFaults.length} fault
          {selectedFaults.length !== 1 ? "s" : ""}:{" "}
          {selectedFaults.map((fault) => fault.faultId).join(", ")}
        </p>
      </ModalHeader>
      <ModalBody>
        <div>
          <Toggle
            labelText="System Availability Calculation"
            labelA="Excluded"
            labelB="Included"
            id="calculation-toggle"
            toggled={isToggleChecked}
            onToggle={(checked) => setIsToggleChecked(checked)}
          />
          {!isToggleChecked && (
            <>
              <p className={styles.hint}>
                Faults which are removed from the System Availability
                calculation must provide a reason.
              </p>
              <div>
                <RadioButtonGroup
                  orientation="vertical"
                  className={styles.radioGroup}
                  legendText="Select Removal Reason"
                  name="removal-reason"
                  valueSelected={removalReason}
                  onChange={(value) =>
                    setRemovalReason(value?.toString() || "")
                  }
                >
                  <RadioButton
                    id="equipment-excluded"
                    labelText="Equipment Excluded"
                    value="Equipment Excluded"
                  />
                  <RadioButton
                    id="planned-maintenance"
                    labelText="Planned Maintenance Activity"
                    value="Planned Maintenance Activity"
                  />
                  <RadioButton
                    id="planned-software-update"
                    labelText="Planned Software Update"
                    value="Planned Software Update"
                  />
                  <RadioButton
                    id="material-quality-poor"
                    labelText="Material Quality Poor"
                    value="Material Quality Poor"
                  />
                  <RadioButton
                    id="customer-responsibility"
                    labelText="Customer Responsibility"
                    value="Customer Responsibility"
                  />
                  <RadioButton
                    id="external-outage"
                    labelText="External Outage"
                    value="External Outage"
                  />
                  <RadioButton
                    id="fault-excluded"
                    labelText="Fault Excluded"
                    value="Fault Excluded"
                  />
                  <RadioButton
                    id="other"
                    labelText="Other (please specify in the text box below)"
                    value="Other"
                  />
                </RadioButtonGroup>
              </div>
            </>
          )}
          <TextArea
            className={styles.commentBox}
            id="removal-comment"
            labelText="Comments"
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            enableCounter
            maxCount={100}
          />
        </div>
      </ModalBody>
      <ModalFooter>
        <Button kind="secondary" onClick={handleClose}>
          Cancel
        </Button>
        <Button onClick={handleSave} disabled={!hasChanges()}>
          Save
        </Button>
      </ModalFooter>
    </ComposedModal>
  );
}
