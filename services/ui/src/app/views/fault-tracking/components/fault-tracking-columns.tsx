import { createColumnHelper } from "@tanstack/react-table";
import { FaultAlarm } from "../types/types";
import { Tag } from "@carbon/react";
import { formatFaultTrackingDuration } from "../utils/time-formatting";
import { getStatusColor } from "../utils/cell-data-transforms";

const columnHelper = createColumnHelper<FaultAlarm>();

export const createFaultTrackingColumns = () => {
  const columns = [
    columnHelper.accessor("location.area", {
      header: "Section Area",
    }),
    columnHelper.accessor("location.section", {
      header: "Section Name",
    }),
    columnHelper.accessor("location.equipment", {
      header: "Equipment Name",
    }),
    columnHelper.accessor("faultId", {
      header: "Alarm ID",
    }),
    columnHelper.accessor("status", {
      header: "Removal Status",
      cell: (info) => {
        const status = info.getValue();
        const color = getStatusColor(status);
        return (
          <Tag size="md" title={status} type={color}>
            {status}
          </Tag>
        );
      },
    }),
    columnHelper.accessor("description", {
      header: "Alarm Description",
    }),
    columnHelper.accessor("tag", {
      header: "Alarm Tag",
    }),
    columnHelper.accessor("timing.origStartTime", {
      header: "Alarm Start Time",
    }),
    columnHelper.accessor("timing.origEndTime", {
      header: "Alarm End Time",
    }),
    columnHelper.accessor("timing.origDuration", {
      header: "Alarm Duration",
      cell: (info) => formatFaultTrackingDuration(info.getValue()),
    }),
    columnHelper.accessor("reason", {
      header: "Removal Reason",
    }),
    columnHelper.accessor("timing.updatedStartTime", {
      header: "Updated Start Time",
    }),
    columnHelper.accessor("timing.updatedEndTime", {
      header: "Updated End Time",
    }),
    columnHelper.accessor("timing.updatedDuration", {
      header: "Updated Duration",
      cell: (info) => formatFaultTrackingDuration(info.getValue()),
    }),
    columnHelper.accessor("comments", {
      header: "Comments",
      // Truncating long comments with tooltip
      cell: (info) => {
        const comment = info.getValue();
        return (
          <span
            title={comment}
            style={{
              maxWidth: "200px",
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
              display: "block",
            }}
          >
            {comment}
          </span>
        );
      },
    }),
  ];

  return columns;
};
