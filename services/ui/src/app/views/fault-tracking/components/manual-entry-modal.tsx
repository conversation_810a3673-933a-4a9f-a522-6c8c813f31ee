import ManualEntryForm from "../manual-entry-form";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (entry: {
    faultId: string;
    faultStartTime: string;
    faultEndTime: string;
    faultDuration: string;
    notes: string;
    sectionName: string;
    equipmentName: string;
    faultDescription: string;
    faultTag: string;
  }) => void;
};

export function ManualEntryModal({ isOpen, onClose, onSubmit }: Props) {
  return (
    <ManualEntryForm isOpen={isOpen} onClose={onClose} onSubmit={onSubmit} />
  );
}
