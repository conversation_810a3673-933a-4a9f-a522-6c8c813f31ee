import {
  DatePicker,
  DatePickerInput,
  TimePicker,
  TextInput,
  Select,
  SelectItem,
  Button,
} from "@carbon/react";
import { WatsonHealthRotate_360 } from "@carbon/icons-react";
import { useEffect, useState, useCallback, useRef } from "react";
import { DateTime } from "luxon";
import styles from "./fault-tracking.module.css";
import { FieldValidity } from "../types/types";
import {
  formatMillisForDisplay,
  getFormattedMillis,
  formatTimeForDisplay,
} from "../utils/time-formatting";

interface TimingData {
  date: string;
  time: string;
  ampm: string;
  millis: string;
}

interface TimingCalculationProps {
  initialStartDate?: string;
  initialEndDate?: string;
  initialStartTime?: string;
  initialEndTime?: string;
  initialStartAmPm?: string;
  initialEndAmPm?: string;
  initialStartMillis?: string;
  initialEndMillis?: string;
  currentStartDate?: string;
  currentEndDate?: string;
  currentStartTime?: string;
  currentEndTime?: string;
  currentStartAmPm?: string;
  currentEndAmPm?: string;
  currentStartMillis?: string;
  currentEndMillis?: string;
  initial?: { start: TimingData; end: TimingData };
  current?: { start?: Partial<TimingData>; end?: Partial<TimingData> };
  onTimingChange: (timing: {
    startDate: string;
    endDate: string;
    startTime: string;
    endTime: string;
    startAmPm: string;
    endAmPm: string;
    startMillis: string;
    endMillis: string;
    duration: string;
    hasChanges: boolean;
    isValid: boolean;
  }) => void;
  showRestore?: boolean;
  resetTrigger?: number;
  allowPastDates?: boolean;
}

type Errors = {
  startDate?: string;
  endDate?: string;
  startTime?: string;
  endTime?: string;
  startMillis?: string;
  endMillis?: string;
};

export default function TimingCalculation(props: TimingCalculationProps) {
  const {
    onTimingChange,
    showRestore = false,
    initialStartDate = "",
    initialEndDate = "",
    initialStartTime = "",
    initialEndTime = "",
    initialStartAmPm = "AM",
    initialEndAmPm = "AM",
    initialStartMillis = "",
    initialEndMillis = "",
    currentStartDate,
    currentEndDate,
    currentStartTime,
    currentEndTime,
    currentStartAmPm,
    currentEndAmPm,
    currentStartMillis,
    currentEndMillis,
    initial,
    current,
    resetTrigger = 0,
    allowPastDates = false,
  } = props;

  // State management
  const [startDate, setStartDate] = useState(
    (current?.start?.date ?? currentStartDate) ||
      initial?.start.date ||
      initialStartDate,
  );
  const [endDate, setEndDate] = useState(
    (current?.end?.date ?? currentEndDate) ||
      initial?.end.date ||
      initialEndDate,
  );
  const [startTime, setStartTime] = useState(
    (current?.start?.time ?? currentStartTime) ||
      initial?.start.time ||
      initialStartTime,
  );
  const [endTime, setEndTime] = useState(
    (current?.end?.time ?? currentEndTime) ||
      initial?.end.time ||
      initialEndTime,
  );
  const [startMillis, setStartMillis] = useState(
    (current?.start?.millis ?? currentStartMillis) ||
      initial?.start.millis ||
      initialStartMillis,
  );
  const [endMillis, setEndMillis] = useState(
    (current?.end?.millis ?? currentEndMillis) ||
      initial?.end.millis ||
      initialEndMillis,
  );
  const [startAmPm, setStartAmPm] = useState(
    (current?.start?.ampm ?? currentStartAmPm) ||
      initial?.start.ampm ||
      initialStartAmPm,
  );
  const [endAmPm, setEndAmPm] = useState(
    (current?.end?.ampm ?? currentEndAmPm) ||
      initial?.end.ampm ||
      initialEndAmPm,
  );
  const [duration, setDuration] = useState("00:00:00:000");
  const [errors, setErrors] = useState<Errors>({});
  const [fieldValidity, setFieldValidity] = useState<FieldValidity>({
    startDate: !!(
      (current?.start?.date ?? currentStartDate) ||
      initial?.start.date ||
      initialStartDate
    ),
    endDate: !!(
      (current?.end?.date ?? currentEndDate) ||
      initial?.end.date ||
      initialEndDate
    ),
    startTime: !!(
      (current?.start?.time ?? currentStartTime) ||
      initial?.start.time ||
      initialStartTime
    ),
    endTime: !!(
      (current?.end?.time ?? currentEndTime) ||
      initial?.end.time ||
      initialEndTime
    ),
    startMillis: true,
    endMillis: true,
  });
  const [hasInitialValueChanges, setHasInitialValueChanges] = useState(false);
  const [canRestore, setCanRestore] = useState(false);
  const [isTimeRangeValid, setIsTimeRangeValid] = useState(true);

  // Store the state of the form as it was when the modal was opened
  const asOpenedTiming = useRef({
    startDate: currentStartDate ?? initialStartDate,
    endDate: currentEndDate ?? initialEndDate,
    startTime: currentStartTime ?? initialStartTime,
    endTime: currentEndTime ?? initialEndTime,
    startAmPm: currentStartAmPm ?? initialStartAmPm,
    endAmPm: currentEndAmPm ?? initialEndAmPm,
    startMillis: currentStartMillis ?? initialStartMillis,
    endMillis: currentEndMillis ?? initialEndMillis,
  });

  // Calculate date restrictions using Luxon
  const dateRestrictions = useCallback(() => {
    const todayDt = DateTime.local().startOf("day");
    const today = todayDt.toJSDate();
    const fortyFiveDaysAgo = todayDt.minus({ days: 44 }).toJSDate();

    // Determine minimum date based on allowPastDates prop
    const minDate = (() => {
      if (allowPastDates) {
        const initialStartDateMerged = initial?.start?.date || initialStartDate;
        if (initialStartDateMerged) {
          const dt = DateTime.fromFormat(initialStartDateMerged, "MM/dd/yyyy");
          return dt.isValid ? dt.minus({ days: 7 }).toJSDate() : undefined;
        }
        return undefined;
      }
      return fortyFiveDaysAgo;
    })();

    // Calculate minimum date for end date picker
    const getEndDateMinDate = () => {
      if (startDate) {
        try {
          const dt = DateTime.fromFormat(startDate, "MM/dd/yyyy");
          const startDateObj = dt.isValid ? dt.toJSDate() : undefined;
          if (startDateObj) {
            if (allowPastDates) {
              return startDateObj;
            } else {
              const limit = minDate || fortyFiveDaysAgo;
              return startDateObj > (limit as Date) ? startDateObj : limit;
            }
          }
        } catch (e) {
          console.error("Error parsing start date:", e);
          return allowPastDates ? undefined : minDate || fortyFiveDaysAgo;
        }
      }
      return allowPastDates ? undefined : minDate || fortyFiveDaysAgo;
    };

    return {
      minDate,
      maxDate: today,
      endMinDate: getEndDateMinDate(),
    };
  }, [startDate, allowPastDates, initial, initialStartDate]);

  // Event handlers
  const handleTimeChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    fieldName: "startTime" | "endTime",
  ) => {
    let cleanValue = e.target.value.replace(/[^\d:]/g, "");
    if (cleanValue.length > 5) {
      cleanValue = cleanValue.slice(0, 5);
    }
    if (fieldName === "startTime") {
      setStartTime(cleanValue);
    } else {
      setEndTime(cleanValue);
    }
  };

  const handleTimeBlur = (
    e: React.FocusEvent<HTMLInputElement>,
    fieldName: "startTime" | "endTime",
  ) => {
    const value = e.target.value;
    if (value) {
      if (value.length === 5 && !/^\d{1,2}:\d{2}$/.test(value)) {
        setErrors((prev) => ({
          ...prev,
          [fieldName]:
            "Incorrect format. The time must be in the format HH:MM.",
        }));
        setFieldValidity((prev) => ({ ...prev, [fieldName]: false }));
        return;
      }

      const formatted = formatTimeForDisplay(value);
      if (fieldName === "startTime") {
        setStartTime(formatted);
      } else {
        setEndTime(formatted);
      }

      if (formatted.includes(":")) {
        const [hours, minutes] = formatted.split(":");
        const hoursNum = parseInt(hours, 10);
        const minutesNum = parseInt(minutes, 10);

        if (hoursNum < 1 || hoursNum > 12 || minutesNum > 59) {
          setErrors((prev) => ({
            ...prev,
            [fieldName]:
              "Incorrect format. Hours must be 1-12, minutes must be 00-59.",
          }));
          setFieldValidity((prev) => ({ ...prev, [fieldName]: false }));
        } else {
          setErrors((prev) => ({ ...prev, [fieldName]: undefined }));
          setFieldValidity((prev) => ({ ...prev, [fieldName]: true }));
        }
      } else {
        setFieldValidity((prev) => ({ ...prev, [fieldName]: false }));
      }
    } else {
      setErrors((prev) => ({ ...prev, [fieldName]: "Time is required." }));
      setFieldValidity((prev) => ({ ...prev, [fieldName]: false }));
    }
  };

  const handleMillisChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    fieldName: "startMillis" | "endMillis",
  ) => {
    const value = e.target.value;
    if (!value) {
      if (fieldName === "startMillis") {
        setStartMillis("");
      } else {
        setEndMillis("");
      }
      return;
    }

    try {
      let cleanValue = value.replace(/\D/g, "");
      if (cleanValue.length > 5) {
        cleanValue = cleanValue.slice(0, 5);
      }
      if (cleanValue.length <= 5) {
        if (fieldName === "startMillis") {
          setStartMillis(cleanValue);
        } else {
          setEndMillis(cleanValue);
        }
      }
    } catch (e) {
      console.error("Milliseconds format validation error:", e);
      if (fieldName === "startMillis") {
        setStartMillis(value);
      } else {
        setEndMillis(value);
      }
    }
  };

  const handleMillisBlur = (
    e: React.FocusEvent<HTMLInputElement>,
    fieldName: "startMillis" | "endMillis",
  ) => {
    const value = e.target.value;
    if (value) {
      const formatted = formatMillisForDisplay(value);
      if (fieldName === "startMillis") {
        setStartMillis(formatted);
      } else {
        setEndMillis(formatted);
      }

      if (formatted.includes(":")) {
        const [seconds] = formatted.split(":");
        const secondsNum = parseInt(seconds, 10);
        if (secondsNum > 59) {
          setErrors((prev) => ({
            ...prev,
            [fieldName]: "Incorrect format. Seconds must be 59 or less.",
          }));
          setFieldValidity((prev) => ({ ...prev, [fieldName]: false }));
        } else {
          setErrors((prev) => ({ ...prev, [fieldName]: undefined }));
          setFieldValidity((prev) => ({ ...prev, [fieldName]: true }));
        }
      } else {
        setFieldValidity((prev) => ({ ...prev, [fieldName]: false }));
      }
    } else {
      setErrors((prev) => ({ ...prev, [fieldName]: undefined }));
      setFieldValidity((prev) => ({ ...prev, [fieldName]: true }));
    }
  };

  const handleDateChange = (
    dates: Date[],
    fieldName: "startDate" | "endDate",
  ) => {
    if (dates.length > 0) {
      const formattedDate = DateTime.fromJSDate(dates[0]).toFormat(
        "MM/dd/yyyy",
      );
      if (fieldName === "startDate") {
        setStartDate(formattedDate);
      } else {
        setEndDate(formattedDate);
      }

      if (formattedDate) {
        setErrors((prev) => ({ ...prev, [fieldName]: undefined }));
        setFieldValidity((prev) => ({ ...prev, [fieldName]: true }));
      } else {
        setErrors((prev) => ({ ...prev, [fieldName]: "Date is required." }));
        setFieldValidity((prev) => ({ ...prev, [fieldName]: false }));
      }
    }
  };

  const handleAmPmChange = (
    e: React.ChangeEvent<HTMLSelectElement>,
    fieldName: "startAmPm" | "endAmPm",
  ) => {
    if (fieldName === "startAmPm") {
      setStartAmPm(e.target.value);
    } else {
      setEndAmPm(e.target.value);
    }
  };

  const handleRestore = () => {
    if (showRestore) {
      const initStartDate = initial?.start?.date ?? initialStartDate;
      const initEndDate = initial?.end?.date ?? initialEndDate;
      const initStartTime = initial?.start?.time ?? initialStartTime;
      const initEndTime = initial?.end?.time ?? initialEndTime;
      const initStartAmPm = initial?.start?.ampm ?? initialStartAmPm;
      const initEndAmPm = initial?.end?.ampm ?? initialEndAmPm;
      const initStartMillis = initial?.start?.millis ?? initialStartMillis;
      const initEndMillis = initial?.end?.millis ?? initialEndMillis;

      setStartDate(initStartDate);
      setEndDate(initEndDate);
      setStartTime(initStartTime);
      setEndTime(initEndTime);
      setStartAmPm(initStartAmPm);
      setEndAmPm(initEndAmPm);
      setStartMillis(initStartMillis || "");
      setEndMillis(initEndMillis || "");
    }
  };

  // Duration calculation effect
  useEffect(() => {
    if (startDate && endDate && startTime && endTime) {
      try {
        const [startSec = "00", startMs = "000"] =
          getFormattedMillis(startMillis).split(":");
        const [endSec = "00", endMs = "000"] =
          getFormattedMillis(endMillis).split(":");

        const [startHourStr, startMinuteStr] = startTime.split(":");
        const [endHourStr, endMinuteStr] = endTime.split(":");

        let startHour = parseInt(startHourStr, 10);
        const startMinute = parseInt(startMinuteStr, 10);
        let endHour = parseInt(endHourStr, 10);
        const endMinute = parseInt(endMinuteStr, 10);

        startHour = (startHour % 12) + (startAmPm === "PM" ? 12 : 0);
        endHour = (endHour % 12) + (endAmPm === "PM" ? 12 : 0);

        const startDtBase = DateTime.fromFormat(startDate, "MM/dd/yyyy");
        const endDtBase = DateTime.fromFormat(endDate, "MM/dd/yyyy");

        const startDt = startDtBase.set({
          hour: startHour,
          minute: startMinute,
          second: parseInt(startSec, 10),
          millisecond: parseInt(startMs, 10),
        });
        const endDt = endDtBase.set({
          hour: endHour,
          minute: endMinute,
          second: parseInt(endSec, 10),
          millisecond: parseInt(endMs, 10),
        });

        const diffInMs = endDt.toMillis() - startDt.toMillis();

        if (diffInMs === 0) {
          setErrors((prev) => ({
            ...prev,
            endTime:
              "Fault duration cannot be zero. Start and end times must be different.",
          }));
          setIsTimeRangeValid(false);
          setDuration("00:00:00:000");
        } else if (startDate === endDate && diffInMs < 0) {
          setErrors((prev) => ({
            ...prev,
            endTime:
              "End time cannot be earlier than start time on the same day.",
          }));
          setIsTimeRangeValid(false);
          setDuration("00:00:00:000");
        } else {
          setIsTimeRangeValid(true);

          if (errors.endTime) {
            setErrors((prev) => ({ ...prev, endTime: undefined }));
          }

          if (!isNaN(diffInMs) && diffInMs >= 0) {
            const hours = Math.floor(diffInMs / (1000 * 60 * 60));
            const mins = Math.floor(
              (diffInMs % (1000 * 60 * 60)) / (1000 * 60),
            );
            const secs = Math.floor((diffInMs % (1000 * 60)) / 1000);
            const ms = diffInMs % 1000;
            const newDuration =
              `${hours.toString().padStart(2, "0")}:` +
              `${mins.toString().padStart(2, "0")}:` +
              `${secs.toString().padStart(2, "0")}:` +
              `${ms.toString().padStart(3, "0")}`;
            setDuration(newDuration);
          }
        }
      } catch (e) {
        console.error("Duration calculation error:", e);
      }
    }
  }, [
    startDate,
    endDate,
    startTime,
    endTime,
    startMillis,
    endMillis,
    startAmPm,
    endAmPm,
    errors.endTime,
  ]);

  // Check if current values differ from the "as opened" state
  useEffect(() => {
    if (showRestore) {
      const isChanged =
        startDate !== asOpenedTiming.current.startDate ||
        endDate !== asOpenedTiming.current.endDate ||
        startTime !== asOpenedTiming.current.startTime ||
        endTime !== asOpenedTiming.current.endTime ||
        startAmPm !== asOpenedTiming.current.startAmPm ||
        endAmPm !== asOpenedTiming.current.endAmPm ||
        getFormattedMillis(startMillis) !==
          getFormattedMillis(asOpenedTiming.current.startMillis) ||
        getFormattedMillis(endMillis) !==
          getFormattedMillis(asOpenedTiming.current.endMillis);

      setHasInitialValueChanges(isChanged);
    }
  }, [
    startDate,
    endDate,
    startTime,
    endTime,
    startAmPm,
    endAmPm,
    startMillis,
    endMillis,
    showRestore,
  ]);

  // Check if current values differ from initial values for Restore button state
  useEffect(() => {
    if (showRestore) {
      const initStartDate = initial?.start?.date || initialStartDate;
      const initEndDate = initial?.end?.date || initialEndDate;
      const initStartTime = initial?.start?.time || initialStartTime;
      const initEndTime = initial?.end?.time || initialEndTime;
      const initStartAmPm = initial?.start?.ampm || initialStartAmPm;
      const initEndAmPm = initial?.end?.ampm || initialEndAmPm;
      const initStartMillis = initial?.start?.millis || initialStartMillis;
      const initEndMillis = initial?.end?.millis || initialEndMillis;

      const isDifferentFromInitial =
        startDate !== initStartDate ||
        endDate !== initEndDate ||
        startTime !== initStartTime ||
        endTime !== initEndTime ||
        startAmPm !== initStartAmPm ||
        endAmPm !== initEndAmPm ||
        getFormattedMillis(startMillis) !==
          getFormattedMillis(initStartMillis) ||
        getFormattedMillis(endMillis) !== getFormattedMillis(initEndMillis);

      setCanRestore(isDifferentFromInitial);
    }
  }, [
    startDate,
    endDate,
    startTime,
    endTime,
    startAmPm,
    endAmPm,
    startMillis,
    endMillis,
    showRestore,
    initialStartDate,
    initialEndDate,
    initialStartTime,
    initialEndTime,
    initialStartAmPm,
    initialEndAmPm,
    initialStartMillis,
    initialEndMillis,
    initial,
  ]);

  // Initialize on mount and when resetTrigger changes
  useEffect(() => {
    const openedStartDate =
      (current?.start?.date ?? currentStartDate) ||
      initial?.start?.date ||
      initialStartDate;
    const openedEndDate =
      (current?.end?.date ?? currentEndDate) ||
      initial?.end?.date ||
      initialEndDate;
    const openedStartTime =
      (current?.start?.time ?? currentStartTime) ||
      initial?.start?.time ||
      initialStartTime;
    const openedEndTime =
      (current?.end?.time ?? currentEndTime) ||
      initial?.end?.time ||
      initialEndTime;
    const openedStartAmPm =
      (current?.start?.ampm ?? currentStartAmPm) ||
      initial?.start?.ampm ||
      initialStartAmPm;
    const openedEndAmPm =
      (current?.end?.ampm ?? currentEndAmPm) ||
      initial?.end?.ampm ||
      initialEndAmPm;
    const openedStartMillis =
      (current?.start?.millis ?? currentStartMillis) ||
      initial?.start?.millis ||
      initialStartMillis;
    const openedEndMillis =
      (current?.end?.millis ?? currentEndMillis) ||
      initial?.end?.millis ||
      initialEndMillis;

    setStartDate(openedStartDate);
    setEndDate(openedEndDate);
    setStartTime(openedStartTime);
    setEndTime(openedEndTime);
    setStartAmPm(openedStartAmPm);
    setEndAmPm(openedEndAmPm);
    setStartMillis(openedStartMillis || "");
    setEndMillis(openedEndMillis || "");

    // Capture the "as opened" state
    asOpenedTiming.current = {
      startDate: openedStartDate,
      endDate: openedEndDate,
      startTime: openedStartTime,
      endTime: openedEndTime,
      startAmPm: openedStartAmPm,
      endAmPm: openedEndAmPm,
      startMillis: openedStartMillis,
      endMillis: openedEndMillis,
    };
  }, [resetTrigger]);

  // Notify parent of timing changes
  useEffect(() => {
    if (onTimingChange && startDate && endDate && startTime && endTime) {
      const timingData = {
        startDate,
        endDate,
        startTime,
        endTime,
        startAmPm,
        endAmPm,
        startMillis: getFormattedMillis(startMillis),
        endMillis: getFormattedMillis(endMillis),
        duration,
        hasChanges: hasInitialValueChanges,
        isValid:
          Object.values(fieldValidity).every((valid) => valid) &&
          isTimeRangeValid,
      };
      onTimingChange(timingData);
    }
  }, [
    startDate,
    endDate,
    startTime,
    endTime,
    startAmPm,
    endAmPm,
    startMillis,
    endMillis,
    duration,
    hasInitialValueChanges,
    fieldValidity,
    isTimeRangeValid,
    onTimingChange,
  ]);

  // Get initial display values for restore section
  const initialDisplayValues = {
    startDate: initial?.start?.date ?? initialStartDate,
    endDate: initial?.end?.date ?? initialEndDate,
    startTime: initial?.start?.time ?? initialStartTime,
    endTime: initial?.end?.time ?? initialEndTime,
    startMillis: initial?.start?.millis ?? initialStartMillis,
    endMillis: initial?.end?.millis ?? initialEndMillis,
  };

  // Pure UI rendering
  return (
    <>
      <div className={styles.datePickerContainer}>
        <div className={styles.timeGroup}>
          <DatePicker
            datePickerType="single"
            value={
              startDate
                ? ([
                    (() => {
                      const dt = DateTime.fromFormat(startDate, "MM/dd/yyyy");
                      return dt.isValid ? dt.toJSDate() : undefined;
                    })(),
                  ].filter(Boolean) as Date[])
                : []
            }
            onChange={(dates) => handleDateChange(dates, "startDate")}
            minDate={dateRestrictions().minDate}
            maxDate={dateRestrictions().maxDate}
            invalid={!!errors.startDate}
            invalidText={errors.startDate}
          >
            <DatePickerInput
              id="start-date"
              placeholder="mm/dd/yyyy"
              labelText="Start Date"
              size="md"
              {...({ autoComplete: "off" } as Record<string, unknown>)}
            />
          </DatePicker>
          <div className={styles.dateTimeRow}>
            <div className={styles.timebox}>
              <TimePicker
                id="start-time"
                labelText="Start Time"
                value={startTime}
                onChange={(e) => handleTimeChange(e, "startTime")}
                onBlur={(e) => handleTimeBlur(e, "startTime")}
                pattern="[0-9]{1,2}:[0-5][0-9]"
                placeholder="H:MM or HH:MM"
                invalid={!!errors.startTime}
                invalidText={errors.startTime}
                size="md"
                {...({ autoComplete: "off" } as Record<string, unknown>)}
              />
              <Select
                className={styles.ampmInput}
                id="start-ampm"
                labelText=" "
                value={startAmPm}
                onChange={(e) => handleAmPmChange(e, "startAmPm")}
                size="md"
              >
                <SelectItem value="AM" text="AM" />
                <SelectItem value="PM" text="PM" />
              </Select>
            </div>
            <TextInput
              className={styles.millisInput}
              id="start-millis"
              labelText="Seconds & Milliseconds"
              value={startMillis}
              onChange={(e) => handleMillisChange(e, "startMillis")}
              onBlur={(e) => handleMillisBlur(e, "startMillis")}
              placeholder="SS:mmm (e.g., 05:250)"
              size="md"
              invalid={!!errors.startMillis}
              invalidText={errors.startMillis}
              autoComplete="off"
            />
          </div>
        </div>

        <div className={styles.timeGroup}>
          <DatePicker
            datePickerType="single"
            value={
              endDate
                ? ([
                    (() => {
                      const dt = DateTime.fromFormat(endDate, "MM/dd/yyyy");
                      return dt.isValid ? dt.toJSDate() : undefined;
                    })(),
                  ].filter(Boolean) as Date[])
                : []
            }
            onChange={(dates) => handleDateChange(dates, "endDate")}
            minDate={dateRestrictions().endMinDate}
            maxDate={dateRestrictions().maxDate}
            invalid={!!errors.endDate}
            invalidText={errors.endDate}
          >
            <DatePickerInput
              id="end-date"
              placeholder="mm/dd/yyyy"
              labelText="End Date"
              size="md"
              {...({ autoComplete: "off" } as Record<string, unknown>)}
            />
          </DatePicker>
          <div className={styles.dateTimeRow}>
            <div className={styles.timebox}>
              <TimePicker
                id="end-time-picker"
                labelText="End Time"
                value={endTime}
                onChange={(e) => handleTimeChange(e, "endTime")}
                onBlur={(e) => handleTimeBlur(e, "endTime")}
                pattern="[0-9]{1,2}:[0-5][0-9]"
                placeholder="H:MM or HH:MM"
                invalid={!!errors.endTime}
                invalidText={errors.endTime}
                size="md"
                {...({ autoComplete: "off" } as Record<string, unknown>)}
              />
              <Select
                className={styles.ampmInput}
                id="end-ampm"
                labelText=" "
                value={endAmPm}
                onChange={(e) => handleAmPmChange(e, "endAmPm")}
                size="md"
              >
                <SelectItem value="AM" text="AM" />
                <SelectItem value="PM" text="PM" />
              </Select>
            </div>
            <TextInput
              className={styles.millisInput}
              id="end-millis"
              labelText="Seconds & Milliseconds"
              value={endMillis}
              onChange={(e) => handleMillisChange(e, "endMillis")}
              onBlur={(e) => handleMillisBlur(e, "endMillis")}
              placeholder="SS:mmm (e.g., 05:250)"
              size="md"
              invalid={!!errors.endMillis}
              invalidText={errors.endMillis}
              autoComplete="off"
            />
          </div>
        </div>
      </div>
      <div className={styles.durationField}>
        <TextInput
          id="duration"
          labelText="Duration"
          value={duration}
          placeholder="00:00:00:000"
          size="md"
          readOnly
        />
      </div>
      {showRestore && (
        <div className={styles.initialValueSection}>
          <p className={styles.hint}>
            Initial Value <br />
            Date Range - {initialDisplayValues.startDate} to{" "}
            {initialDisplayValues.endDate} <br />
            Time Range - {initialDisplayValues.startTime}:
            {initialDisplayValues.startMillis} to {initialDisplayValues.endTime}
            :{initialDisplayValues.endMillis}
          </p>
          <Button
            kind="tertiary"
            onClick={handleRestore}
            disabled={!canRestore}
            renderIcon={WatsonHealthRotate_360}
            iconDescription="Reset to initial values"
          >
            Restore to Initial Value
          </Button>
        </div>
      )}
    </>
  );
}
