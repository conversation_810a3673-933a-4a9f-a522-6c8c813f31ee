import {
  DatePicker,
  DatePickerInput,
  TimePicker,
  TextInput,
  Select,
  SelectItem,
  Button,
} from "@carbon/react";
import { WatsonHealthRotate_360 } from "@carbon/icons-react";
import { useEffect, useRef, useReducer } from "react";
import { DateTime } from "luxon";
import styles from "./fault-tracking.module.css";

interface TimingData {
  date: string;
  time: string;
  ampm: string;
  millis: string;
}

interface TimingCalculationProps {
  initialStartDate?: string;
  initialEndDate?: string;
  initialStartTime?: string;
  initialEndTime?: string;
  initialStartAmPm?: string;
  initialEndAmPm?: string;
  initialStartMillis?: string;
  initialEndMillis?: string;
  currentStartDate?: string;
  currentEndDate?: string;
  currentStartTime?: string;
  currentEndTime?: string;
  currentStartAmPm?: string;
  currentEndAmPm?: string;
  currentStartMillis?: string;
  currentEndMillis?: string;
  initial?: { start: TimingData; end: TimingData };
  current?: { start?: Partial<TimingData>; end?: Partial<TimingData> };
  onTimingChange: (timing: {
    startDate: string;
    endDate: string;
    startTime: string;
    endTime: string;
    startAmPm: string;
    endAmPm: string;
    startMillis: string;
    endMillis: string;
    duration: string;
    hasChanges: boolean;
    isValid: boolean;
  }) => void;
  showRestore?: boolean;
  resetTrigger?: number;
  allowPastDates?: boolean;
}

type Errors = {
  startDate?: string;
  endDate?: string;
  startTime?: string;
  endTime?: string;
  startMillis?: string;
  endMillis?: string;
};

type FieldValidity = {
  startDate: boolean;
  endDate: boolean;
  startTime: boolean;
  endTime: boolean;
  startMillis: boolean;
  endMillis: boolean;
};

type TimingState = {
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  startMillis: string;
  endMillis: string;
  startAmPm: string;
  endAmPm: string;
  startDtISO?: string;
  endDtISO?: string;
  duration: string;
  errors: Errors;
  fieldValidity: FieldValidity;
  hasInitialValueChanges: boolean;
  canRestore: boolean;
  isTimeRangeValid: boolean;
};

type StateField = keyof Omit<TimingState, "errors" | "fieldValidity">;

type Action =
  | { type: "SET_FIELD"; field: StateField; value: string | boolean }
  | { type: "SET_ERRORS"; errors: Partial<Errors> }
  | { type: "SET_FIELD_VALIDITY"; validity: Partial<FieldValidity> }
  | { type: "PATCH"; patch: Partial<TimingState> }
  | {
      type: "RESET";
      values: {
        startDate: string;
        endDate: string;
        startTime: string;
        endTime: string;
        startAmPm: string;
        endAmPm: string;
        startMillis: string;
        endMillis: string;
      };
    };

const reducer = (state: TimingState, action: Action): TimingState => {
  switch (action.type) {
    case "SET_FIELD":
      return { ...state, [action.field]: action.value } as TimingState;
    case "SET_ERRORS":
      return { ...state, errors: { ...state.errors, ...action.errors } };
    case "SET_FIELD_VALIDITY":
      return {
        ...state,
        fieldValidity: { ...state.fieldValidity, ...action.validity },
      };
    case "PATCH":
      return { ...state, ...action.patch } as TimingState;
    case "RESET":
      return {
        ...state,
        startDate: action.values.startDate,
        endDate: action.values.endDate,
        startTime: action.values.startTime,
        endTime: action.values.endTime,
        startAmPm: action.values.startAmPm,
        endAmPm: action.values.endAmPm,
        startMillis: action.values.startMillis,
        endMillis: action.values.endMillis,
        startDtISO:
          action.values.startDate && action.values.startTime
            ? DateTime.fromFormat(
                `${action.values.startDate} ${action.values.startTime}:${(action.values.startMillis || "00:000").split(":")[0]}`,
                "MM/dd/yyyy h:mm:ss",
              )
                .set({
                  millisecond:
                    parseInt(
                      (action.values.startMillis || "00:000").split(":")[1],
                      10,
                    ) || 0,
                })
                .toISO() || undefined
            : undefined,
        endDtISO:
          action.values.endDate && action.values.endTime
            ? DateTime.fromFormat(
                `${action.values.endDate} ${action.values.endTime}:${(action.values.endMillis || "00:000").split(":")[0]}`,
                "MM/dd/yyyy h:mm:ss",
              )
                .set({
                  millisecond:
                    parseInt(
                      (action.values.endMillis || "00:000").split(":")[1],
                      10,
                    ) || 0,
                })
                .toISO() || undefined
            : undefined,
        duration: "00:00:00:000",
        errors: {},
        fieldValidity: {
          startDate: !!action.values.startDate,
          endDate: !!action.values.endDate,
          startTime: !!action.values.startTime,
          endTime: !!action.values.endTime,
          startMillis: true,
          endMillis: true,
        },
        hasInitialValueChanges: false,
        isTimeRangeValid: true,
      };
    default:
      return state;
  }
};

export default function TimingCalculation({
  initialStartDate = "",
  initialEndDate = "",
  initialStartTime = "",
  initialEndTime = "",
  initialStartAmPm = "AM",
  initialEndAmPm = "AM",
  initialStartMillis = "",
  initialEndMillis = "",
  currentStartDate,
  currentEndDate,
  currentStartTime,
  currentEndTime,
  currentStartAmPm,
  currentEndAmPm,
  currentStartMillis,
  currentEndMillis,
  initial,
  current,
  onTimingChange,
  showRestore = false,
  resetTrigger = 0,
  allowPastDates = false,
}: TimingCalculationProps) {
  const [state, dispatch] = useReducer(reducer, {
    startDate:
      (current?.start?.date ?? currentStartDate) ||
      initial?.start.date ||
      initialStartDate,
    endDate:
      (current?.end?.date ?? currentEndDate) ||
      initial?.end.date ||
      initialEndDate,
    startTime:
      (current?.start?.time ?? currentStartTime) ||
      initial?.start.time ||
      initialStartTime,
    endTime:
      (current?.end?.time ?? currentEndTime) ||
      initial?.end.time ||
      initialEndTime,
    startMillis:
      (current?.start?.millis ?? currentStartMillis) ||
      initial?.start.millis ||
      initialStartMillis,
    endMillis:
      (current?.end?.millis ?? currentEndMillis) ||
      initial?.end.millis ||
      initialEndMillis,
    startAmPm:
      (current?.start?.ampm ?? currentStartAmPm) ||
      initial?.start.ampm ||
      initialStartAmPm,
    endAmPm:
      (current?.end?.ampm ?? currentEndAmPm) ||
      initial?.end.ampm ||
      initialEndAmPm,
    startDtISO: undefined,
    endDtISO: undefined,
    duration: "00:00:00:000",
    errors: {},
    fieldValidity: {
      startDate: !!(
        (current?.start?.date ?? currentStartDate) ||
        initial?.start.date ||
        initialStartDate
      ),
      endDate: !!(
        (current?.end?.date ?? currentEndDate) ||
        initial?.end.date ||
        initialEndDate
      ),
      startTime: !!(
        (current?.start?.time ?? currentStartTime) ||
        initial?.start.time ||
        initialStartTime
      ),
      endTime: !!(
        (current?.end?.time ?? currentEndTime) ||
        initial?.end.time ||
        initialEndTime
      ),
      startMillis: true,
      endMillis: true,
    },
    hasInitialValueChanges: false,
    canRestore: false,
    isTimeRangeValid: true,
  });

  // Consolidated reset routine used by both reset trigger and restore
  const resetTimingState = (values: {
    startDate: string;
    endDate: string;
    startTime: string;
    endTime: string;
    startAmPm: string;
    endAmPm: string;
    startMillis: string;
    endMillis: string;
  }) => {
    dispatch({ type: "RESET", values });
  };

  // Store the state of the form as it was when the modal was opened
  const asOpenedTiming = useRef({
    startDate: currentStartDate ?? initialStartDate,
    endDate: currentEndDate ?? initialEndDate,
    startTime: currentStartTime ?? initialStartTime,
    endTime: currentEndTime ?? initialEndTime,
    startAmPm: currentStartAmPm ?? initialStartAmPm,
    endAmPm: currentEndAmPm ?? initialEndAmPm,
    startMillis: currentStartMillis ?? initialStartMillis,
    endMillis: currentEndMillis ?? initialEndMillis,
  });

  // errors and field validity are managed by useReducer in `state`

  // Calculate date restrictions using Luxon
  const todayDt = DateTime.local().startOf("day");
  const today = todayDt.toJSDate();
  const fortyFiveDaysAgo = todayDt.minus({ days: 44 }).toJSDate();

  // Determine minimum date based on allowPastDates prop
  const minDate = (() => {
    if (allowPastDates) {
      const initialStartDateMerged = initial?.start?.date || initialStartDate;
      if (initialStartDateMerged) {
        const dt = DateTime.fromFormat(initialStartDateMerged, "MM/dd/yyyy");
        return dt.isValid ? dt.minus({ days: 7 }).toJSDate() : undefined;
      }
      return undefined;
    }
    return fortyFiveDaysAgo;
  })();

  // Calculate minimum date for end date picker
  const getEndDateMinDate = () => {
    if (state.startDate) {
      try {
        const dt = DateTime.fromFormat(state.startDate, "MM/dd/yyyy");
        const startDateObj = dt.isValid ? dt.toJSDate() : undefined;
        if (startDateObj) {
          if (allowPastDates) {
            // If past dates are allowed, end date can't be before start date
            return startDateObj;
          } else {
            // End date should not be before start date, but also not before fortyFiveDaysAgo
            const limit = minDate || fortyFiveDaysAgo;
            return startDateObj > (limit as Date) ? startDateObj : limit;
          }
        }
      } catch (e) {
        console.error("Error parsing start date:", e);
        return allowPastDates ? undefined : minDate || fortyFiveDaysAgo;
      }
    }
    return allowPastDates ? undefined : minDate || fortyFiveDaysAgo;
  };

  // Initialize on mount and when resetTrigger changes
  useEffect(() => {
    const openedStartDate =
      (current?.start?.date ?? currentStartDate) ||
      initial?.start?.date ||
      initialStartDate;
    const openedEndDate =
      (current?.end?.date ?? currentEndDate) ||
      initial?.end?.date ||
      initialEndDate;
    const openedStartTime =
      (current?.start?.time ?? currentStartTime) ||
      initial?.start?.time ||
      initialStartTime;
    const openedEndTime =
      (current?.end?.time ?? currentEndTime) ||
      initial?.end?.time ||
      initialEndTime;
    const openedStartAmPm =
      (current?.start?.ampm ?? currentStartAmPm) ||
      initial?.start?.ampm ||
      initialStartAmPm;
    const openedEndAmPm =
      (current?.end?.ampm ?? currentEndAmPm) ||
      initial?.end?.ampm ||
      initialEndAmPm;
    const openedStartMillis =
      (current?.start?.millis ?? currentStartMillis) ||
      initial?.start?.millis ||
      initialStartMillis;
    const openedEndMillis =
      (current?.end?.millis ?? currentEndMillis) ||
      initial?.end?.millis ||
      initialEndMillis;

    resetTimingState({
      startDate: openedStartDate,
      endDate: openedEndDate,
      startTime: openedStartTime,
      endTime: openedEndTime,
      startAmPm: openedStartAmPm,
      endAmPm: openedEndAmPm,
      startMillis: openedStartMillis || "",
      endMillis: openedEndMillis || "",
    });

    // Capture the "as opened" state
    asOpenedTiming.current = {
      startDate: openedStartDate,
      endDate: openedEndDate,
      startTime: openedStartTime,
      endTime: openedEndTime,
      startAmPm: openedStartAmPm,
      endAmPm: openedEndAmPm,
      startMillis: openedStartMillis,
      endMillis: openedEndMillis,
    };
  }, [resetTrigger]);

  // Format milliseconds input to maintain SS:mmm format with auto-formatting
  const handleMillisChange = (
    value: string,
    fieldName: "startMillis" | "endMillis",
  ) => {
    // If empty, allow it to stay empty
    if (!value) {
      dispatch({ type: "SET_FIELD", field: fieldName, value: "" });
      return;
    }

    try {
      // Remove any non-digit characters
      let cleanValue = value.replace(/\D/g, "");

      // Limit to 5 digits maximum (SS:mmm = 2 seconds + 3 milliseconds)
      if (cleanValue.length > 5) {
        cleanValue = cleanValue.slice(0, 5);
      }

      // Allow raw input during typing - only format when complete or on blur
      if (cleanValue.length <= 5) {
        dispatch({ type: "SET_FIELD", field: fieldName, value: cleanValue });
      }
    } catch (e) {
      console.error("Milliseconds format validation error:", e);
      dispatch({ type: "SET_FIELD", field: fieldName, value });
    }
  };

  // Format milliseconds for display and calculations
  const formatMillisForDisplay = (rawValue: string) => {
    if (!rawValue) return "";

    const cleanValue = rawValue.replace(/\D/g, "");

    if (cleanValue.length === 0) {
      return "";
    } else if (cleanValue.length <= 2) {
      // Just seconds: pad and add :000
      const seconds = cleanValue.padStart(2, "0");
      return `${seconds}:000`;
    } else if (cleanValue.length <= 5) {
      // Seconds + milliseconds
      const seconds = cleanValue.slice(0, 2).padStart(2, "0");
      const milliseconds = cleanValue.slice(2).padEnd(3, "0");
      return `${seconds}:${milliseconds}`;
    }
    return rawValue;
  };

  // Handle blur to format the input
  const handleMillisBlur = (
    value: string,
    fieldName: "startMillis" | "endMillis",
  ) => {
    if (value) {
      const formatted = formatMillisForDisplay(value);
      dispatch({ type: "SET_FIELD", field: fieldName, value: formatted });

      // Validate seconds after formatting
      if (formatted.includes(":")) {
        const [seconds] = formatted.split(":");
        const secondsNum = parseInt(seconds, 10);
        if (secondsNum > 59) {
          // Set error for invalid seconds
          dispatch({
            type: "SET_ERRORS",
            errors: {
              [fieldName]: "Incorrect format. Seconds must be 59 or less.",
            },
          });
          dispatch({
            type: "SET_FIELD_VALIDITY",
            validity: { [fieldName]: false } as Partial<FieldValidity>,
          });
        } else {
          // Clear error if format is correct
          dispatch({
            type: "SET_ERRORS",
            errors: { [fieldName]: undefined },
          });
          dispatch({
            type: "SET_FIELD_VALIDITY",
            validity: { [fieldName]: true } as Partial<FieldValidity>,
          });
        }
      } else {
        dispatch({
          type: "SET_FIELD_VALIDITY",
          validity: { [fieldName]: false } as Partial<FieldValidity>,
        });
      }
    } else {
      // Empty field is OK - milliseconds are optional
      dispatch({ type: "SET_ERRORS", errors: { [fieldName]: undefined } });
      dispatch({
        type: "SET_FIELD_VALIDITY",
        validity: { [fieldName]: true } as Partial<FieldValidity>,
      });
    }
  };

  // Get formatted milliseconds value for calculations (returns 00:000 if empty)
  const getFormattedMillis = (millisValue: string) => {
    if (!millisValue) return "00:000";

    // If it's already formatted (contains colon), return as is
    if (millisValue.includes(":")) {
      return millisValue;
    }

    // If it's raw input, format it
    return formatMillisForDisplay(millisValue) || "00:000";
  };

  // Handle time input changes - allow free input during typing
  const handleTimeChange = (
    value: string,
    fieldName: "startTime" | "endTime",
  ) => {
    // Remove any non-digit characters except colon
    let cleanValue = value.replace(/[^\d:]/g, "");

    // Limit input length to prevent excessive typing
    if (cleanValue.length > 5) {
      cleanValue = cleanValue.slice(0, 5);
    }

    dispatch({ type: "SET_FIELD", field: fieldName, value: cleanValue });
  };

  // Format time for display and calculations
  const formatTimeForDisplay = (rawValue: string) => {
    if (!rawValue) return "";

    // Remove any non-digit characters
    const cleanValue = rawValue.replace(/\D/g, "");

    if (cleanValue.length === 0) {
      return "";
    } else if (cleanValue.length <= 2) {
      // Just hours: add :00
      const hours = cleanValue;
      return `${hours}:00`;
    } else if (cleanValue.length <= 4) {
      // Hours + minutes
      const hours = cleanValue.slice(0, cleanValue.length - 2) || "0";
      const minutes = cleanValue.slice(-2).padStart(2, "0");
      return `${hours}:${minutes}`;
    }
    return rawValue;
  };

  // Handle blur to format the time input
  const handleTimeBlur = (
    value: string,
    fieldName: "startTime" | "endTime",
  ) => {
    if (value) {
      // Check for 5 symbols that don't match HH:mm format
      if (value.length === 5 && !/^\d{1,2}:\d{2}$/.test(value)) {
        dispatch({
          type: "SET_ERRORS",
          errors: {
            [fieldName]:
              "Incorrect format. The time must be in the format HH:MM.",
          },
        });
        dispatch({
          type: "SET_FIELD_VALIDITY",
          validity: { [fieldName]: false } as Partial<FieldValidity>,
        });
        return;
      }

      const formatted = formatTimeForDisplay(value);
      dispatch({ type: "SET_FIELD", field: fieldName, value: formatted });

      // Validate time after formatting
      if (formatted.includes(":")) {
        const [hours, minutes] = formatted.split(":");
        const hoursNum = parseInt(hours, 10);
        const minutesNum = parseInt(minutes, 10);

        if (hoursNum < 1 || hoursNum > 12 || minutesNum > 59) {
          // Set error for invalid time
          dispatch({
            type: "SET_ERRORS",
            errors: {
              [fieldName]:
                "Incorrect format. Hours must be 1-12, minutes must be 00-59.",
            },
          });
          dispatch({
            type: "SET_FIELD_VALIDITY",
            validity: { [fieldName]: false } as Partial<FieldValidity>,
          });
        } else {
          // Clear error if format is correct
          dispatch({ type: "SET_ERRORS", errors: { [fieldName]: undefined } });
          dispatch({
            type: "SET_FIELD_VALIDITY",
            validity: { [fieldName]: true } as Partial<FieldValidity>,
          });
        }
      } else {
        dispatch({
          type: "SET_FIELD_VALIDITY",
          validity: { [fieldName]: false } as Partial<FieldValidity>,
        });
      }
    } else {
      // Empty field - required field error
      dispatch({
        type: "SET_ERRORS",
        errors: { [fieldName]: "Time is required." },
      });
      dispatch({
        type: "SET_FIELD_VALIDITY",
        validity: { [fieldName]: false } as Partial<FieldValidity>,
      });
    }
  };

  // Handle date blur validation
  const handleDateBlur = (
    value: string,
    fieldName: "startDate" | "endDate",
  ) => {
    if (value) {
      dispatch({ type: "SET_ERRORS", errors: { [fieldName]: undefined } });
      dispatch({
        type: "SET_FIELD_VALIDITY",
        validity: { [fieldName]: true } as Partial<FieldValidity>,
      });
    } else {
      dispatch({
        type: "SET_ERRORS",
        errors: { [fieldName]: "Date is required." },
      });
      dispatch({
        type: "SET_FIELD_VALIDITY",
        validity: { [fieldName]: false } as Partial<FieldValidity>,
      });
    }
  };

  // Calculate duration whenever dates/times change
  useEffect(() => {
    if (state.startDate && state.endDate && state.startTime && state.endTime) {
      try {
        // Parse milliseconds from startMillis and endMillis (format: SS:mmm)
        // Use getFormattedMillis to handle empty values
        const [startSec = "00", startMs = "000"] = getFormattedMillis(
          state.startMillis,
        ).split(":");
        const [endSec = "00", endMs = "000"] = getFormattedMillis(
          state.endMillis,
        ).split(":");

        // Parse using Luxon for reliable date/time handling
        const [startHourStr, startMinuteStr] = state.startTime.split(":");
        const [endHourStr, endMinuteStr] = state.endTime.split(":");

        let startHour = parseInt(startHourStr, 10);
        const startMinute = parseInt(startMinuteStr, 10);
        let endHour = parseInt(endHourStr, 10);
        const endMinute = parseInt(endMinuteStr, 10);

        // Convert to 24-hour based on AM/PM
        startHour = (startHour % 12) + (state.startAmPm === "PM" ? 12 : 0);
        endHour = (endHour % 12) + (state.endAmPm === "PM" ? 12 : 0);

        const startDtBase = DateTime.fromFormat(state.startDate, "MM/dd/yyyy");
        const endDtBase = DateTime.fromFormat(state.endDate, "MM/dd/yyyy");

        const startDt = startDtBase.set({
          hour: startHour,
          minute: startMinute,
          second: parseInt(startSec, 10),
          millisecond: parseInt(startMs, 10),
        });
        const endDt = endDtBase.set({
          hour: endHour,
          minute: endMinute,
          second: parseInt(endSec, 10),
          millisecond: parseInt(endMs, 10),
        });

        // Persist ISO forms as single sources of truth
        const startISO = startDt.toISO();
        const endISO = endDt.toISO();
        if (startISO) {
          dispatch({ type: "SET_FIELD", field: "startDtISO", value: startISO });
        }
        if (endISO) {
          dispatch({ type: "SET_FIELD", field: "endDtISO", value: endISO });
        }

        const diffInMs = endDt.toMillis() - startDt.toMillis();

        if (diffInMs === 0) {
          dispatch({
            type: "SET_ERRORS",
            errors: {
              endTime:
                "Fault duration cannot be zero. Start and end times must be different.",
            },
          });
          dispatch({
            type: "SET_FIELD",
            field: "isTimeRangeValid",
            value: false,
          });
          dispatch({
            type: "SET_FIELD",
            field: "duration",
            value: "00:00:00:000",
          });
        } else if (state.startDate === state.endDate && diffInMs < 0) {
          dispatch({
            type: "SET_ERRORS",
            errors: {
              endTime:
                "End time cannot be earlier than start time on the same day.",
            },
          });
          dispatch({
            type: "SET_FIELD",
            field: "isTimeRangeValid",
            value: false,
          });
          dispatch({
            type: "SET_FIELD",
            field: "duration",
            value: "00:00:00:000",
          });
        } else {
          dispatch({
            type: "SET_FIELD",
            field: "isTimeRangeValid",
            value: true,
          });

          // Clear any previous time-related error if the range is now valid
          if (state.errors.endTime) {
            dispatch({ type: "SET_ERRORS", errors: { endTime: undefined } });
          }

          if (!isNaN(diffInMs) && diffInMs >= 0) {
            // Convert ms to HH:mm:ss:SSS
            const hours = Math.floor(diffInMs / (1000 * 60 * 60));
            const mins = Math.floor(
              (diffInMs % (1000 * 60 * 60)) / (1000 * 60),
            );
            const secs = Math.floor((diffInMs % (1000 * 60)) / 1000);
            const ms = diffInMs % 1000;
            const newDuration =
              `${hours.toString().padStart(2, "0")}:` +
              `${mins.toString().padStart(2, "0")}:` +
              `${secs.toString().padStart(2, "0")}:` +
              `${ms.toString().padStart(3, "0")}`;
            dispatch({
              type: "SET_FIELD",
              field: "duration",
              value: newDuration,
            });
          }
        }
      } catch (e) {
        console.error("Duration calculation error:", e);
      }
    }
  }, [
    state.startDate,
    state.endDate,
    state.startTime,
    state.endTime,
    state.startMillis,
    state.endMillis,
    state.startAmPm,
    state.endAmPm,
    state.errors.endTime,
  ]);

  // Check if current values differ from the "as opened" state
  useEffect(() => {
    if (showRestore) {
      const isChanged =
        state.startDate !== asOpenedTiming.current.startDate ||
        state.endDate !== asOpenedTiming.current.endDate ||
        state.startTime !== asOpenedTiming.current.startTime ||
        state.endTime !== asOpenedTiming.current.endTime ||
        state.startAmPm !== asOpenedTiming.current.startAmPm ||
        state.endAmPm !== asOpenedTiming.current.endAmPm ||
        getFormattedMillis(state.startMillis) !==
          getFormattedMillis(asOpenedTiming.current.startMillis) ||
        getFormattedMillis(state.endMillis) !==
          getFormattedMillis(asOpenedTiming.current.endMillis);

      dispatch({
        type: "SET_FIELD",
        field: "hasInitialValueChanges",
        value: isChanged,
      });
    }
  }, [
    state.startDate,
    state.endDate,
    state.startTime,
    state.endTime,
    state.startAmPm,
    state.endAmPm,
    state.startMillis,
    state.endMillis,
    showRestore,
  ]);

  // Check if current values differ from initial values for Restore button state
  useEffect(() => {
    if (showRestore) {
      const initStartDate = initial?.start?.date || initialStartDate;
      const initEndDate = initial?.end?.date || initialEndDate;
      const initStartTime = initial?.start?.time || initialStartTime;
      const initEndTime = initial?.end?.time || initialEndTime;
      const initStartAmPm = initial?.start?.ampm || initialStartAmPm;
      const initEndAmPm = initial?.end?.ampm || initialEndAmPm;
      const initStartMillis = initial?.start?.millis || initialStartMillis;
      const initEndMillis = initial?.end?.millis || initialEndMillis;

      const isDifferentFromInitial =
        state.startDate !== initStartDate ||
        state.endDate !== initEndDate ||
        state.startTime !== initStartTime ||
        state.endTime !== initEndTime ||
        state.startAmPm !== initStartAmPm ||
        state.endAmPm !== initEndAmPm ||
        getFormattedMillis(state.startMillis) !==
          getFormattedMillis(initStartMillis) ||
        getFormattedMillis(state.endMillis) !==
          getFormattedMillis(initEndMillis);

      dispatch({
        type: "SET_FIELD",
        field: "canRestore",
        value: isDifferentFromInitial,
      });
    }
  }, [
    state.startDate,
    state.endDate,
    state.startTime,
    state.endTime,
    state.startAmPm,
    state.endAmPm,
    state.startMillis,
    state.endMillis,
    showRestore,
    initialStartDate,
    initialEndDate,
    initialStartTime,
    initialEndTime,
    initialStartAmPm,
    initialEndAmPm,
    initialStartMillis,
    initialEndMillis,
    initial,
  ]);

  // Notify parent component of timing changes and validity
  useEffect(() => {
    // Guard against transient resets: only notify when basic fields are populated
    if (
      !state.startDate ||
      !state.endDate ||
      !state.startTime ||
      !state.endTime
    ) {
      return;
    }
    if (onTimingChange) {
      const timingData = {
        startDate: state.startDate,
        endDate: state.endDate,
        startTime: state.startTime,
        endTime: state.endTime,
        startAmPm: state.startAmPm,
        endAmPm: state.endAmPm,
        startMillis: getFormattedMillis(state.startMillis),
        endMillis: getFormattedMillis(state.endMillis),
        duration: state.duration,
        hasChanges: state.hasInitialValueChanges,
        isValid:
          Object.values(state.fieldValidity).every((valid) => valid) &&
          state.isTimeRangeValid,
      };

      onTimingChange(timingData);
    }
  }, [
    state.startDate,
    state.endDate,
    state.startTime,
    state.endTime,
    state.startAmPm,
    state.endAmPm,
    state.duration,
    state.hasInitialValueChanges,
    state.startMillis,
    state.endMillis,
    state.fieldValidity,
    state.isTimeRangeValid,
  ]);

  const handleRestore = () => {
    if (showRestore) {
      const initStartDate = initial?.start?.date ?? initialStartDate;
      const initEndDate = initial?.end?.date ?? initialEndDate;
      const initStartTime = initial?.start?.time ?? initialStartTime;
      const initEndTime = initial?.end?.time ?? initialEndTime;
      const initStartAmPm = initial?.start?.ampm ?? initialStartAmPm;
      const initEndAmPm = initial?.end?.ampm ?? initialEndAmPm;
      const initStartMillis = initial?.start?.millis ?? initialStartMillis;
      const initEndMillis = initial?.end?.millis ?? initialEndMillis;

      resetTimingState({
        startDate: initStartDate,
        endDate: initEndDate,
        startTime: initStartTime,
        endTime: initEndTime,
        startAmPm: initStartAmPm,
        endAmPm: initEndAmPm,
        startMillis: initStartMillis || "",
        endMillis: initEndMillis || "",
      });

      // Also compute and set duration immediately to avoid any race with effects
      try {
        const [sSec = "00", sMs = "000"] = (initStartMillis || "00:000").split(
          ":",
        );
        const [eSec = "00", eMs = "000"] = (initEndMillis || "00:000").split(
          ":",
        );

        const [sHourStr, sMinStr] = initStartTime.split(":");
        const [eHourStr, eMinStr] = initEndTime.split(":");

        const sHour =
          (parseInt(sHourStr, 10) % 12) + (initStartAmPm === "PM" ? 12 : 0);
        const eHour =
          (parseInt(eHourStr, 10) % 12) + (initEndAmPm === "PM" ? 12 : 0);
        const sMin = parseInt(sMinStr, 10);
        const eMin = parseInt(eMinStr, 10);

        const sDate = DateTime.fromFormat(initStartDate, "MM/dd/yyyy").set({
          hour: sHour,
          minute: sMin,
          second: parseInt(sSec, 10),
          millisecond: parseInt(sMs, 10),
        });
        const eDate = DateTime.fromFormat(initEndDate, "MM/dd/yyyy").set({
          hour: eHour,
          minute: eMin,
          second: parseInt(eSec, 10),
          millisecond: parseInt(eMs, 10),
        });

        const diff = eDate.toMillis() - sDate.toMillis();
        if (!isNaN(diff) && diff >= 0) {
          const h = Math.floor(diff / (1000 * 60 * 60));
          const m = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
          const sec = Math.floor((diff % (1000 * 60)) / 1000);
          const ms = diff % 1000;
          const dur = `${h.toString().padStart(2, "0")}:${m
            .toString()
            .padStart(2, "0")}:${sec.toString().padStart(2, "0")}:${ms
            .toString()
            .padStart(3, "0")}`;
          dispatch({ type: "SET_FIELD", field: "duration", value: dur });
          dispatch({ type: "SET_ERRORS", errors: { endTime: undefined } });
          dispatch({
            type: "SET_FIELD",
            field: "isTimeRangeValid",
            value: true,
          });
          const sISO = sDate.toISO();
          const eISO = eDate.toISO();
          if (sISO)
            dispatch({ type: "SET_FIELD", field: "startDtISO", value: sISO });
          if (eISO)
            dispatch({ type: "SET_FIELD", field: "endDtISO", value: eISO });
        }
      } catch (e) {
        console.error("Duration calculation error:", e);
        // ignore; effect will recalc
      }
    }
  };

  // Derive initial display values (prefer grouped props, fallback to legacy)
  const initStartDateDisp = initial?.start?.date ?? initialStartDate;
  const initEndDateDisp = initial?.end?.date ?? initialEndDate;
  const initStartTimeDisp = initial?.start?.time ?? initialStartTime;
  const initEndTimeDisp = initial?.end?.time ?? initialEndTime;
  const initStartMillisDisp = initial?.start?.millis ?? initialStartMillis;
  const initEndMillisDisp = initial?.end?.millis ?? initialEndMillis;

  return (
    <>
      <div className={styles.datePickerContainer}>
        <div className={styles.timeGroup}>
          <DatePicker
            datePickerType="single"
            value={
              state.startDate
                ? ([
                    (() => {
                      const dt = DateTime.fromFormat(
                        state.startDate,
                        "MM/dd/yyyy",
                      );
                      return dt.isValid ? dt.toJSDate() : undefined;
                    })(),
                  ].filter(Boolean) as Date[])
                : []
            }
            onChange={(dates) => {
              if (dates.length > 0) {
                const date = dates[0];
                const formattedDate =
                  DateTime.fromJSDate(date).toFormat("MM/dd/yyyy");
                dispatch({
                  type: "SET_FIELD",
                  field: "startDate",
                  value: formattedDate,
                });
                // Validate immediately when date is selected
                handleDateBlur(formattedDate, "startDate");
              }
            }}
            minDate={minDate}
            maxDate={today}
            invalid={!!state.errors.startDate}
            invalidText={state.errors.startDate}
          >
            <DatePickerInput
              id="start-date"
              placeholder="mm/dd/yyyy"
              labelText="Start Date"
              size="md"
              {...({ autoComplete: "off" } as Record<string, unknown>)}
            />
          </DatePicker>
          <div className={styles.dateTimeRow}>
            <div className={styles.timebox}>
              <TimePicker
                id="start-time"
                labelText="Start Time"
                value={state.startTime}
                onChange={(e) => handleTimeChange(e.target.value, "startTime")}
                onBlur={(e) => handleTimeBlur(e.target.value, "startTime")}
                pattern="[0-9]{1,2}:[0-5][0-9]"
                placeholder="H:MM or HH:MM"
                invalid={!!state.errors.startTime}
                invalidText={state.errors.startTime}
                size="md"
                {...({ autoComplete: "off" } as Record<string, unknown>)}
              />
              <Select
                className={styles.ampmInput}
                id="start-ampm"
                labelText=" "
                value={state.startAmPm}
                onChange={(e) =>
                  dispatch({
                    type: "SET_FIELD",
                    field: "startAmPm",
                    value: e.target.value,
                  })
                }
                size="md"
              >
                <SelectItem value="AM" text="AM" />
                <SelectItem value="PM" text="PM" />
              </Select>
            </div>
            <TextInput
              className={styles.millisInput}
              id="start-millis"
              labelText="Seconds & Milliseconds"
              value={state.startMillis}
              onChange={(e) =>
                handleMillisChange(e.target.value, "startMillis")
              }
              onBlur={(e) => handleMillisBlur(e.target.value, "startMillis")}
              placeholder="SS:mmm (e.g., 05:250)"
              size="md"
              invalid={!!state.errors.startMillis}
              invalidText={state.errors.startMillis}
              autoComplete="off"
            />
          </div>
        </div>

        <div className={styles.timeGroup}>
          <DatePicker
            datePickerType="single"
            value={
              state.endDate
                ? ([
                    (() => {
                      const dt = DateTime.fromFormat(
                        state.endDate,
                        "MM/dd/yyyy",
                      );
                      return dt.isValid ? dt.toJSDate() : undefined;
                    })(),
                  ].filter(Boolean) as Date[])
                : []
            }
            onChange={(dates) => {
              if (dates.length > 0) {
                const date = dates[0];
                const formattedDate =
                  DateTime.fromJSDate(date).toFormat("MM/dd/yyyy");
                dispatch({
                  type: "SET_FIELD",
                  field: "endDate",
                  value: formattedDate,
                });
                // Validate immediately when date is selected
                handleDateBlur(formattedDate, "endDate");
              }
            }}
            minDate={getEndDateMinDate()}
            maxDate={today}
            invalid={!!state.errors.endDate}
            invalidText={state.errors.endDate}
          >
            <DatePickerInput
              id="end-date"
              placeholder="mm/dd/yyyy"
              labelText="End Date"
              size="md"
              {...({ autoComplete: "off" } as Record<string, unknown>)}
            />
          </DatePicker>
          <div className={styles.dateTimeRow}>
            <div className={styles.timebox}>
              <TimePicker
                id="end-time-picker"
                labelText="End Time"
                value={state.endTime}
                onChange={(e) => handleTimeChange(e.target.value, "endTime")}
                onBlur={(e) => handleTimeBlur(e.target.value, "endTime")}
                pattern="[0-9]{1,2}:[0-5][0-9]"
                placeholder="H:MM or HH:MM"
                invalid={!!state.errors.endTime}
                invalidText={state.errors.endTime}
                size="md"
                {...({ autoComplete: "off" } as Record<string, unknown>)}
              />
              <Select
                className={styles.ampmInput}
                id="end-ampm"
                labelText=" "
                value={state.endAmPm}
                onChange={(e) =>
                  dispatch({
                    type: "SET_FIELD",
                    field: "endAmPm",
                    value: e.target.value,
                  })
                }
                size="md"
              >
                <SelectItem value="AM" text="AM" />
                <SelectItem value="PM" text="PM" />
              </Select>
            </div>
            <TextInput
              className={styles.millisInput}
              id="end-millis"
              labelText="Seconds & Milliseconds"
              value={state.endMillis}
              onChange={(e) => handleMillisChange(e.target.value, "endMillis")}
              onBlur={(e) => handleMillisBlur(e.target.value, "endMillis")}
              placeholder="SS:mmm (e.g., 05:250)"
              size="md"
              invalid={!!state.errors.endMillis}
              invalidText={state.errors.endMillis}
              autoComplete="off"
            />
          </div>
        </div>
      </div>
      <div className={styles.durationField}>
        <TextInput
          id="duration"
          labelText="Duration"
          value={state.duration}
          placeholder="00:00:00:000"
          size="md"
          readOnly
        />
      </div>
      {showRestore && (
        <div className={styles.initialValueSection}>
          <p className={styles.hint}>
            Initial Value <br />
            Date Range - {initStartDateDisp} to {initEndDateDisp} <br />
            Time Range - {initStartTimeDisp}:{initStartMillisDisp} to{" "}
            {initEndTimeDisp}:{initEndMillisDisp}
          </p>
          <Button
            kind="tertiary"
            onClick={handleRestore}
            disabled={!state.canRestore}
            renderIcon={WatsonHealthRotate_360}
            iconDescription="Reset to initial values"
          >
            Restore to Initial Value
          </Button>
        </div>
      )}
    </>
  );
}
