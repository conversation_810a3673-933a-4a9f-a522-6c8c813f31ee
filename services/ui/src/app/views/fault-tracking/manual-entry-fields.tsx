import { Dropdown, TextArea } from "@carbon/react";
import styles from "./fault-tracking.module.css";
import {
  sectionImpactedOptions,
  equipmentNameOptions,
} from "./mock-manual-entry-log-data";

type Props = {
  values: {
    notes: string;
    alarmDescription: string;
    sectionImpacted: string;
    equipmentName: string;
  };
  errors: {
    sectionImpacted?: string;
    equipmentName?: string;
    alarmDescription?: string;
  };
  touched: {
    sectionImpacted: boolean;
    equipmentName: boolean;
    alarmDescription: boolean;
    submitted: boolean;
  };
  onChange: {
    setNotes: (v: string) => void;
    setAlarmDescription: (v: string) => void;
    setSectionImpacted: (v: string) => void;
    setEquipmentName: (v: string) => void;
  };
};

export function ManualEntryFields({
  values,
  errors,
  touched,
  onChange,
}: Props) {
  return (
    <>
      <div className={styles.dropdownRow}>
        <div className={styles.dropdownHalf}>
          <Dropdown
            id="section-impacted-dropdown"
            titleText="Section Impacted"
            label="Select section"
            items={sectionImpactedOptions}
            itemToString={(item) => (item ? item.text : "")}
            selectedItem={
              values.sectionImpacted ? { text: values.sectionImpacted } : null
            }
            onChange={({
              selectedItem,
            }: {
              selectedItem?: { text: string } | null;
            }) => onChange.setSectionImpacted(selectedItem?.text || "")}
            invalid={
              (touched.sectionImpacted || touched.submitted) &&
              !!errors.sectionImpacted
            }
            invalidText={
              (touched.sectionImpacted || touched.submitted) &&
              errors.sectionImpacted
            }
          />
        </div>
        <div className={styles.dropdownHalf}>
          <Dropdown
            id="equipment-name-dropdown"
            titleText="Equipment Name"
            label="Select equipment"
            items={equipmentNameOptions}
            itemToString={(item) => (item ? item.text : "")}
            selectedItem={
              values.equipmentName ? { text: values.equipmentName } : null
            }
            onChange={({
              selectedItem,
            }: {
              selectedItem?: { text: string } | null;
            }) => onChange.setEquipmentName(selectedItem?.text || "")}
            invalid={
              (touched.equipmentName || touched.submitted) &&
              !!errors.equipmentName
            }
            invalidText={
              (touched.equipmentName || touched.submitted) &&
              errors.equipmentName
            }
          />
        </div>
      </div>
      <div className={styles.inputWrapper}>
        <TextArea
          labelText="Alarm Description"
          enableCounter
          id="alarm-description"
          maxCount={100}
          rows={4}
          value={values.alarmDescription}
          onChange={(e) => onChange.setAlarmDescription(e.target.value)}
          invalid={
            (touched.alarmDescription || touched.submitted) &&
            !!errors.alarmDescription
          }
          invalidText={
            (touched.alarmDescription || touched.submitted) &&
            errors.alarmDescription
          }
        />
      </div>
      <div className={styles.inputWrapper}>
        <TextArea
          labelText="Comments"
          enableCounter
          id="notes-comments"
          maxCount={100}
          rows={4}
          value={values.notes}
          onChange={(e) => onChange.setNotes(e.target.value)}
        />
      </div>
    </>
  );
}
