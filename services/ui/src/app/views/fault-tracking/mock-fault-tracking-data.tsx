import { But<PERSON>, <PERSON>, Tooltip } from "@carbon/react";
import { createColumnHelper } from "@tanstack/react-table";
import styles from "./fault-tracking.module.css";
import { Edit, WatsonHealthStatusChange } from "@carbon/icons-react";

export interface FaultTrackingData {
  sectionArea: string;
  sectionName: string;
  equipmentName: string;
  faultId: string;
  faultDescription: string;
  faultTag: string;
  faultStartTime: string;
  faultEndTime: string;
  faultDuration: string;
  removalStatus: string;
  removalReason: string;
  severity: string;
  user: string;
  updatedStartTime: string;
  updatedEndTime: string;
  updatedDuration: string;
  comments: string;
  actions: string;
}

interface ColumnConfig {
  onEditTimings: (faultId: string) => void;
  onUpdateCalculation: (faultId: string) => void;
}

const columnHelper = createColumnHelper<FaultTrackingData>();

export const createFaultTrackingColumns = ({
  onEditTimings,
  onUpdateCalculation,
}: ColumnConfig) => [
  columnHelper.accessor("sectionArea", {
    header: "Section Area",
  }),
  columnHelper.accessor("sectionName", {
    header: "Section Name",
  }),
  columnHelper.accessor("equipmentName", {
    header: "Equipment Name",
  }),
  columnHelper.accessor("faultId", {
    header: "Alarm ID",
  }),
  columnHelper.accessor("removalStatus", {
    header: "Removal Status",
    cell: (info) => {
      const status = info.getValue();
      const color = status.toLowerCase() === "included" ? "blue" : "outline";
      return (
        <Tag size="md" title={status} type={color}>
          {status}
        </Tag>
      );
    },
  }),
  columnHelper.accessor("severity", {
    header: "Severity",
  }),
  columnHelper.accessor("actions", {
    header: "Actions",
    cell: (info) => (
      <>
        <Button
          size="sm"
          kind="ghost"
          className={styles.actionButton}
          onClick={() => onEditTimings(info.row.original.faultId)}
        >
          <Tooltip label="Edit Fault Timings" align="top">
            <Edit />
          </Tooltip>
        </Button>
        <Button
          size="sm"
          kind="ghost"
          className={styles.actionButton}
          onClick={() => onUpdateCalculation(info.row.original.faultId)}
        >
          <Tooltip label="Update Calculation Status" align="top">
            <WatsonHealthStatusChange />
          </Tooltip>
        </Button>
      </>
    ),
  }),
  columnHelper.accessor("faultDescription", {
    header: "Alarm Description",
  }),
  columnHelper.accessor("faultTag", {
    header: "Alarm Tag",
  }),
  columnHelper.accessor("faultStartTime", {
    header: "Alarm Start Time",
    cell: (info) => {
      const fullDateTime = info.getValue();
      // Format: "MM/DD/YYYY HH:MM:SS.mmm AM/PM" -> "MM/DD/YYYY HH:MM AM/PM"
      const formatted = fullDateTime.replace(/:\d{2}\.\d{3}/, "");
      return <span>{formatted}</span>;
    },
  }),
  columnHelper.accessor("faultEndTime", {
    header: "Alarm End Time",
    cell: (info) => {
      const fullDateTime = info.getValue();
      // Format: "MM/DD/YYYY HH:MM:SS.mmm AM/PM" -> "MM/DD/YYYY HH:MM AM/PM"
      const formatted = fullDateTime.replace(/:\d{2}\.\d{3}/, "");
      return <span>{formatted}</span>;
    },
  }),
  columnHelper.accessor("faultDuration", {
    header: "Alarm Duration",
    cell: (info) => {
      const duration = info.getValue();
      // Convert "HH:MM:SS:mmm" to hours and minutes format
      try {
        const parts = duration.split(":");
        if (parts.length >= 3) {
          const hours = parseInt(parts[0], 10) || 0;
          const minutes = parseInt(parts[1], 10) || 0;

          if (hours > 0) {
            // Show hours and minutes if there are hours
            if (minutes > 0) {
              return (
                <span>
                  {hours} hrs {minutes} mins
                </span>
              );
            } else {
              return <span>{hours} hrs</span>;
            }
          } else {
            // Show only minutes if less than 1 hour
            return <span>{minutes} mins</span>;
          }
        }
      } catch (e) {
        console.error("Duration parsing error:", e);
      }
      return <span>{duration}</span>;
    },
  }),
  columnHelper.accessor("removalReason", {
    header: "Removal Reason",
  }),
  columnHelper.accessor("user", {
    header: "User",
  }),
  columnHelper.accessor("updatedStartTime", {
    header: "Updated Start Time",
    cell: (info) => {
      const fullDateTime = info.getValue();
      if (!fullDateTime) return "";
      // Format: "MM/DD/YYYY HH:MM:SS.mmm AM/PM" -> "MM/DD/YYYY HH:MM AM/PM"
      const formatted = fullDateTime.replace(/:\d{2}\.\d{3}/, "");
      return <span>{formatted}</span>;
    },
  }),
  columnHelper.accessor("updatedEndTime", {
    header: "Updated End Time",
    cell: (info) => {
      const fullDateTime = info.getValue();
      if (!fullDateTime) return "";
      // Format: "MM/DD/YYYY HH:MM:SS.mmm AM/PM" -> "MM/DD/YYYY HH:MM AM/PM"
      const formatted = fullDateTime.replace(/:\d{2}\.\d{3}/, "");
      return <span>{formatted}</span>;
    },
  }),
  columnHelper.accessor("updatedDuration", {
    header: "Updated Duration",
    cell: (info) => {
      const duration = info.getValue();
      if (!duration) return "";
      // Convert "HH:MM:SS:mmm" to hours and minutes format
      try {
        const parts = duration.split(":");
        if (parts.length >= 3) {
          const hours = parseInt(parts[0], 10) || 0;
          const minutes = parseInt(parts[1], 10) || 0;

          if (hours > 0) {
            // Show hours and minutes if there are hours
            if (minutes > 0) {
              return (
                <span>
                  {hours} hrs {minutes} mins
                </span>
              );
            } else {
              return <span>{hours} hrs</span>;
            }
          } else {
            // Show only minutes if less than 1 hour
            return <span>{minutes} mins</span>;
          }
        }
      } catch (e) {
        console.error("Duration parsing error:", e);
      }
      return <span>{duration}</span>;
    },
  }),
  columnHelper.accessor("comments", {
    header: "Comments",
    // Truncating long comments with tooltip
    cell: (info) => {
      const comment = info.getValue();
      return (
        <span
          title={comment}
          style={{
            maxWidth: "200px",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
            display: "block",
          }}
        >
          {comment}
        </span>
      );
    },
  }),
];

export const faultTrackingMockedData = [
  {
    sectionArea: "Area Name",
    sectionName: "Name",
    equipmentName: "Print & Apply",
    faultId: "F1000",
    faultDescription: "Sensor Failure",
    faultTag: "T2000",
    faultStartTime: "01/10/2025 01:00:00.000 PM",
    faultEndTime: "01/10/2025 01:30:00.000 PM",
    faultDuration: "00:30:00:000",
    removalStatus: "Included",
    removalReason: "N/A",
    severity: "600",
    user: "",
    updatedStartTime: "",
    updatedEndTime: "",
    updatedDuration: "",
    comments: "",
    actions: ".",
  },
  {
    sectionArea: "Packing",
    sectionName: "Section 2",
    equipmentName: "Conveyor Belt",
    faultId: "F1001",
    faultDescription: "Belt Jam",
    faultTag: "T2001",
    faultStartTime: "01/15/2025 02:00:00.000 PM",
    faultEndTime: "01/15/2025 02:20:00.000 PM",
    faultDuration: "00:20:00:000",
    removalStatus: "Included",
    removalReason: "Cleared Jam",
    severity: "600",
    user: "Operator",
    updatedStartTime: "01/15/2025 02:05:00.000 PM",
    updatedEndTime: "01/15/2025 02:25:00.000 PM",
    updatedDuration: "00:20:00:000",
    comments: "Manual intervention required.",
    actions: ".",
  },
  {
    sectionArea: "Inspection",
    sectionName: "Section 3",
    equipmentName: "Scanner",
    faultId: "F1002",
    faultDescription: "Camera Error",
    faultTag: "T2002",
    faultStartTime: "02/15/2025 09:00:00.000 AM",
    faultEndTime: "02/15/2025 09:15:00.000 AM",
    faultDuration: "00:15:00:000",
    removalStatus: "Excluded",
    removalReason: "Auto Reset",
    severity: "600",
    user: "Technician",
    updatedStartTime: "",
    updatedEndTime: "",
    updatedDuration: "",
    comments: "",
    actions: ".",
  },
  {
    sectionArea: "Labeling",
    sectionName: "Section 4",
    equipmentName: "Label Printer",
    faultId: "F1003",
    faultDescription: "Ribbon Out",
    faultTag: "T2003",
    faultStartTime: "03/05/2025 10:00:00.000 AM",
    faultEndTime: "03/05/2025 10:10:00.000 AM",
    faultDuration: "00:10:00:000",
    removalStatus: "Included",
    removalReason: "Replaced Ribbon",
    severity: "600",
    user: "",
    updatedStartTime: "",
    updatedEndTime: "",
    updatedDuration: "",
    comments: "",
    actions: ".",
  },
  {
    sectionArea: "Sorting",
    sectionName: "Section 5",
    equipmentName: "Sorter Arm",
    faultId: "F1004",
    faultDescription: "Motor Overheat",
    faultTag: "T2004",
    faultStartTime: "04/12/2025 03:00:00.000 PM",
    faultEndTime: "04/12/2025 03:45:00.000 PM",
    faultDuration: "00:45:00:000",
    removalStatus: "Included",
    removalReason: "Cooled Down",
    severity: "600",
    user: "Supervisor",
    updatedStartTime: "04/12/2025 03:05:00.000 PM",
    updatedEndTime: "04/12/2025 03:50:00.000 PM",
    updatedDuration: "00:45:00:000",
    comments: "Temperature spike detected.",
    actions: ".",
  },
  {
    sectionArea: "Packing",
    sectionName: "Section 6",
    equipmentName: "Box Erector",
    faultId: "F1005",
    faultDescription: "Box Jam",
    faultTag: "T2005",
    faultStartTime: "05/22/2025 11:00:00.000 AM",
    faultEndTime: "05/22/2025 11:25:00.000 AM",
    faultDuration: "00:25:00:000",
    removalStatus: "Excluded",
    removalReason: "Cleared Jam",
    severity: "600",
    user: "Operator",
    updatedStartTime: "",
    updatedEndTime: "",
    updatedDuration: "",
    comments: "",
    actions: ".",
  },
];
