import {
  <PERSON><PERSON>,
  <PERSON>umn,
  Grid,
  Heading,
  InlineNotification,
} from "@carbon/react";
import { createColumnHelper } from "@tanstack/react-table";
import { useMemo, useState } from "react";
import { Datagrid } from "../../components/datagrid";
import { useConfig } from "../../config/hooks/use-config";
import classes from "./app-config-settings.module.css";
import { AppConfigSetting } from "@ict/sdk/types";
import { ViewAside } from "../../components/view-aside/view-aside";
import { AppConfigSettingDetail } from "./app-config-setting-detail";

export const AppConfigSettingsView = () => {
  const { data: appConfigSettings, isLoading, error } = useConfig();
  // const { hasConfiguratorAccess } = useRoles();
  const [selectedSettingId, setSelectedSettingId] = useState<
    string | undefined
  >(undefined);

  // determines if the expanded view is open for a new setting value
  const [isNewSettingOpen, setIsNewSettingOpen] = useState(false);

  // filter config settings down to remove feature flags since there's a dedicated view
  const filteredConfigSettings: AppConfigSetting[] = useMemo(() => {
    return (appConfigSettings as AppConfigSetting[]).filter((setting) => {
      return setting.group !== "feature-flags";
    });
  }, [appConfigSettings]);

  // Define column helper for type safety
  const columnHelper = createColumnHelper<AppConfigSetting>();

  // Define columns for the table
  const columns = useMemo(
    () => [
      columnHelper.accessor("name", {
        header: "Name",
        minSize: 90,
      }),
      columnHelper.accessor("description", {
        header: "Description",
        minSize: 180,
      }),
      columnHelper.accessor("group", {
        header: "Group",
        maxSize: 100,
        cell: (value) => value.getValue(),
      }),
      columnHelper.accessor("value", {
        id: "setting-value",
        header: "Value",
        cell: (props) => (
          <span>
            {`${props.row.original.dataType === "json" ? "View JSON in details" : props.getValue()}`}
          </span>
        ),
      }),
      columnHelper.accessor((row) => `${row.source}`, {
        id: "display-source",
        header: "Source",
        maxSize: 50,
      }),
      // columnHelper.display({
      //   id: "delete-setting",
      //   header: "",
      //   size: 50,
      //   cell: ({ row }) => {
      //     function deleteSetting(id: string) {
      //       console.log("TODO deleteSetting", id);
      //     }
      //     if (!hasConfiguratorAccess) {
      //       return null;
      //     }
      //     return (
      //       <Button
      //         kind="danger--ghost"
      //         size="sm"
      //         onClick={() => {
      //           deleteSetting(row.original.id);
      //         }}
      //       >
      //         <RowDelete />
      //       </Button>
      //     );
      //   },
      // }),
      columnHelper.display({
        id: "details-button",
        cell: ({ row }) => {
          return (
            <Button
              size="sm"
              onClick={(e) => {
                e.preventDefault();
                setSelectedSettingId(row.original.id);
              }}
            >
              Details
            </Button>
          );
        },
      }),
    ],
    [columnHelper],
  );

  // Handle error state
  if (error) {
    return (
      <InlineNotification
        kind="error"
        title="Error"
        subtitle={`Error loading application config settings: ${
          (error as Error)?.message || String(error)
        }`}
      />
    );
  }

  return (
    <div className={classes.container}>
      <Grid>
        <Column lg={16} md={8} sm={4}>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "1rem",
            }}
          >
            <Heading>Application Config Settings</Heading>
            <Button onClick={() => setIsNewSettingOpen(true)}>
              Add Setting
            </Button>
          </div>
          <div className={classes.tableContainer}>
            <div style={{ height: "calc(100vh - 180px)", width: "100%" }}>
              <Datagrid
                columns={columns}
                data={filteredConfigSettings}
                mode="client"
                enableSelection={false}
                initialPagination={{ pageIndex: 0, pageSize: 100 }}
                initialDensity="compact"
                initialSorting={[{ id: "name", desc: false }]}
                initialColumnVisibility={{ source: false }}
                isLoading={isLoading}
              />
            </div>
          </div>
        </Column>
      </Grid>
      <ViewAside
        style={{
          width: "35vw",
          position: "fixed",
          top: "var(--header-height, 48px)",
          right: "0",
        }}
        isVisible={!!selectedSettingId || isNewSettingOpen}
      >
        {(selectedSettingId || isNewSettingOpen) && (
          <AppConfigSettingDetail
            settingId={selectedSettingId}
            onClose={() => {
              setSelectedSettingId(undefined);
              setIsNewSettingOpen(false);
            }}
          />
        )}
      </ViewAside>
    </div>
  );
};

export default AppConfigSettingsView;
