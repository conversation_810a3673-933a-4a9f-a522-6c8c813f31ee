import { vi } from "vitest";
import { fireEvent, render, screen, waitFor } from "../../../../test-utils";
import { AppConfigSettingsView } from "../app-config-settings";
import { AppConfigSettingSource } from "@ict/sdk/types";

// Mock Carbon components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

vi.mock("../../../auth/hooks/use-roles", () => ({
  useRoles: vi.fn().mockReturnValue({
    hasConfiguratorAccess: true,
  }),
}));

// Mock the useConfig hook
const mockUseConfig = vi.fn();
vi.mock("../../../config/hooks/use-config", () => ({
  useConfig: () => ({
    data: mockUseConfig(),
    isLoading: false,
    error: null,
  }),
}));

// Mock the AppConfigSettingDetail component
vi.mock("../app-config-setting-detail", () => ({
  AppConfigSettingDetail: vi
    .fn()
    .mockReturnValue(<div>AppConfigSettingDetail</div>),
}));

const mockSettings = [
  {
    id: "test-setting-1",
    name: "Test Setting 1",
    group: "Test Group",
    description: "Test Description 1",
    dataType: "string",
    source: AppConfigSettingSource.default,
    value: "test-value-1",
  },
  {
    id: "test-setting-2",
    name: "Test Setting 2",
    group: "Test Group",
    description: "Test Description 2",
    dataType: "number",
    source: AppConfigSettingSource.default,
    value: 123,
  },
];

describe("AppConfigSettingsView", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockUseConfig.mockReturnValue(mockSettings);
  });

  it("renders correctly with settings data", () => {
    render(<AppConfigSettingsView />);

    // Check that the heading is displayed
    expect(screen.getByText("Application Config Settings")).toBeInTheDocument();

    // Check that the "Add Setting" button is displayed
    expect(screen.getByText("Add Setting")).toBeInTheDocument();

    // Check that the settings data is displayed in the table
    expect(screen.getByText("Test Setting 1")).toBeInTheDocument();
    expect(screen.getByText("Test Description 1")).toBeInTheDocument();
    expect(screen.getByText("Test Setting 2")).toBeInTheDocument();
    expect(screen.getByText("Test Description 2")).toBeInTheDocument();
  });

  it("opens the detail view when a setting is selected", async () => {
    render(<AppConfigSettingsView />);

    // Click the "Details" button for the first setting
    const detailsButton = screen.getAllByText("Details")[0];
    fireEvent.click(detailsButton);

    // Wait for the detail view to open
    await waitFor(() => {
      expect(screen.getByText("AppConfigSettingDetail")).toBeInTheDocument();
    });
  });

  it("opens the detail view for a new setting when 'Add Setting' is clicked", async () => {
    render(<AppConfigSettingsView />);

    // Click the "Add Setting" button
    const addSettingButton = screen.getByText("Add Setting");
    fireEvent.click(addSettingButton);

    // Wait for the detail view to open
    await waitFor(() => {
      expect(screen.getByText("AppConfigSettingDetail")).toBeInTheDocument();
    });
  });

  it("filters out feature flags", () => {
    const settingsWithFeatureFlags = [
      ...mockSettings,
      {
        id: "feature-flag-1",
        name: "Feature Flag 1",
        group: "feature-flags",
        description: "Feature Flag 1 Description",
        dataType: "boolean",
        source: AppConfigSettingSource.default,
        value: true,
      },
    ];
    mockUseConfig.mockReturnValue(settingsWithFeatureFlags);

    render(<AppConfigSettingsView />);

    // Check that the feature flag is not displayed in the table
    expect(screen.queryByText("Feature Flag 1")).toBeNull();
  });

  it("displays 'View JSON in details' for JSON data type", () => {
    const settingsWithJson = [
      ...mockSettings,
      {
        id: "json-setting",
        name: "JSON Setting",
        group: "Test Group",
        description: "JSON Setting Description",
        dataType: "json",
        source: AppConfigSettingSource.default,
        value: { key: "value" },
      },
    ];
    mockUseConfig.mockReturnValue(settingsWithJson);

    render(<AppConfigSettingsView />);

    // Check that "View JSON in details" is displayed for the JSON setting
    expect(screen.getByText("View JSON in details")).toBeInTheDocument();
  });
});
