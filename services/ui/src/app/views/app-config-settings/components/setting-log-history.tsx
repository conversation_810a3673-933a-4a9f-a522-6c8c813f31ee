import {
  Accordion,
  AccordionItem,
  Modal,
  ModalBody,
  TextArea,
} from "@carbon/react";
import { ictApi } from "../../../api/ict-api";
import { Datagrid } from "../../../components/datagrid";
import { useTranslation } from "react-i18next";
import { createColumnHelper } from "@tanstack/react-table";
import { components } from "../../../../../../../libs/shared/ict-sdk/generated/openapi-react-query";
import { Link } from "@carbon/react";
import { useMemo, useState } from "react";
import { useApiErrorState } from "../../../hooks/use-api-error-state";
import ReactDOM from "react-dom";

interface SettingLogHistoryProps {
  settingId: string;
  settingType: string;
}

export type SettingLogHistoryItem =
  components["schemas"]["AppConfigSettingLog"];

export const SettingLogHistory = ({
  settingId,
  settingType,
}: SettingLogHistoryProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalContent, setModalContent] =
    useState<SettingLogHistoryItem | null>();

  const { data, isLoading, error } = ictApi.client.useQuery(
    "get",
    `/config/setting-logs`,
    {
      params: {
        query: {
          setting_id: settingId,
          limit: 10,
        },
      },
    },
  );

  const isNoDataAvailable = useApiErrorState(error);

  // Define columns using createColumnHelper
  const columnHelper = createColumnHelper<SettingLogHistoryItem>();

  const { t } = useTranslation();

  const columns = useMemo(
    () => [
      columnHelper.accessor("timestamp", {
        header: t("Time Changed"),
        size: 110,
      }),
      columnHelper.accessor("source", {
        header: t("Level"),
        size: 60,
      }),
      columnHelper.accessor("newValue", {
        header: t("New Value"),
        size: 90,
        cell: (info) => (
          // JSON looks bad in the table, so send an event to open a modal with the json value
          <span>
            {settingType === "json" ? (
              <Link
                onClick={(e) => {
                  e.stopPropagation();
                  setIsModalOpen(true);
                  setModalContent(info.row.original);
                }}
                style={{ cursor: "pointer" }}
              >
                {"View JSON"}
              </Link>
            ) : (
              info.getValue()
            )}
          </span>
        ),
      }),
      columnHelper.accessor("changedBy", {
        header: t("Changed By"),
        size: 120,
      }),
    ],
    [columnHelper],
  );

  return (
    <>
      <Accordion>
        <AccordionItem title={"Setting History"}>
          <Datagrid
            columns={columns}
            data={data?.data || []}
            isLoading={isLoading}
            showPagination={false}
            error={
              isNoDataAvailable
                ? undefined
                : error
                  ? "Error fetching setting history"
                  : ""
            }
            initialSorting={[{ id: "timestamp", desc: true }]}
          />
        </AccordionItem>
      </Accordion>
      {isModalOpen &&
        modalContent &&
        ReactDOM.createPortal(
          <Modal
            open={isModalOpen}
            onRequestClose={() => {
              setIsModalOpen(false);
              setModalContent(null);
            }}
            passiveModal={true}
          >
            <ModalBody>
              <span>
                JSON value of setting changed at {modalContent?.timestamp}:
              </span>
              <TextArea
                labelText={""}
                value={JSON.stringify(
                  JSON.parse(modalContent?.newValue || ""),
                  undefined,
                  4,
                )}
                readOnly={true}
                rows={30}
              />
            </ModalBody>
          </Modal>,
          document.body,
        )}
    </>
  );
};
