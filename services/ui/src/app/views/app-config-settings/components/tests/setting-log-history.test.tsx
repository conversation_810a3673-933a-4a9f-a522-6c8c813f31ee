import { vi } from "vitest";
import { fireEvent, render, screen } from "../../../../../test-utils";
import { SettingLogHistory } from "../../components/setting-log-history";
import { AppConfigSettingSource } from "@ict/sdk/types";

// Mock Carbon components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

// Mock the useConfig hook
const mockUseQuery = vi.fn();
vi.mock("../../../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: (...args: any[]) => {
        mockUseQuery(...args);
        return {
          data: {
            data: [
              {
                timestamp: "2024-07-25T12:00:00.000Z",
                source: AppConfigSettingSource.default,
                newValue: '{"firstTest":"firstTest"}',
                changedBy: "test-user",
              },
              {
                timestamp: "2024-07-26T12:00:00.000Z",
                source: AppConfigSettingSource.tenant,
                newValue: '{"secondTest":"secondTest"}',
                changedBy: "another-user",
              },
            ],
          },
          isLoading: false,
          error: null,
        };
      },
    },
  },
}));

describe("SettingLogHistory", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders correctly with log history data", () => {
    render(<SettingLogHistory settingId="test-setting" settingType="string" />);

    // Check that the accordion title is displayed
    expect(screen.getByText("Setting History")).toBeInTheDocument();

    // Check that the log history data is displayed in the table
    expect(screen.getByText("2024-07-25T12:00:00.000Z")).toBeInTheDocument();
    expect(screen.getByText("default")).toBeInTheDocument();
    expect(screen.getByText('{"firstTest":"firstTest"}')).toBeInTheDocument();
    expect(screen.getByText("test-user")).toBeInTheDocument();

    expect(screen.getByText("2024-07-26T12:00:00.000Z")).toBeInTheDocument();
    expect(screen.getByText("tenant")).toBeInTheDocument();
    expect(screen.getByText('{"firstTest":"firstTest"}')).toBeInTheDocument();
    expect(screen.getByText("another-user")).toBeInTheDocument();
  });

  it("opens a modal with JSON content when 'View JSON' is clicked", async () => {
    render(<SettingLogHistory settingId="test-setting" settingType="json" />);

    // Click the "View JSON" link for the first log entry
    const viewJsonLink = screen.getAllByText("View JSON")[0];
    fireEvent.click(viewJsonLink);

    // modal is rendered in document.body with react portal
    expect(document.body.innerHTML).to.contain(
      "JSON value of setting changed at 2024-07-26T12:00:00.000Z:",
    );
  });
});
