.optionContainer {
  padding-top: 1rem;
  height: 100%;
}

.bottomPadding {
  padding-bottom: 1rem;
}

.bottomMargin {
  margin-bottom: 1rem;
}

.topMargin {
  margin-top: 2rem;
}

.drawerHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.accordionItemHeader {
  display: flex;
  justify-content: space-between;
}

.actionButton {
  display: flex;
  gap: 1rem;
  justify-content: center;
  padding: 10px;
}

.valueConfigContainer {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
  margin-bottom: 1rem;
}
