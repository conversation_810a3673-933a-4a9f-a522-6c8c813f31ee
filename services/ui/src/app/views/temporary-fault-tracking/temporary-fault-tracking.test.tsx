import { vi } from "vitest";
import { fireEvent, render, screen, waitFor } from "../../../test-utils";
import { ictApi } from "../../api/ict-api";
import TemporaryFaultTrackingView from "./temporary-fault-tracking";

// Mock the ictApi
vi.mock("../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: vi.fn(),
    },
  },
}));

// Mock the ViewBar component
vi.mock("../../components/view-bar/view-bar", () => ({
  ViewBar: ({
    title,
    children,
  }: {
    title: string;
    children?: React.ReactNode;
  }) => (
    <div data-testid="view-bar">
      <h1>{title}</h1>
      <div data-testid="view-bar-children">{children}</div>
    </div>
  ),
}));

// Mock the table component to observe props and trigger callbacks
vi.mock(
  "./components/temporary-fault-tracking-table/temporary-fault-tracking-table",
  () => ({
    TemporaryFaultTrackingTable: ({
      data,
      pagination,
      setPagination,
      sorting,
      setSorting,
      columnFilters,
      setColumnFilters,
      isLoading,
      isFetching,
      error,
      rowCount,
      onRefresh,
    }: any) => (
      <div data-testid="temporary-fault-tracking-table">
        <div data-testid="table-data">{JSON.stringify(data)}</div>
        <div data-testid="table-pagination">{JSON.stringify(pagination)}</div>
        <div data-testid="table-sorting">{JSON.stringify(sorting)}</div>
        <div data-testid="table-filters">{JSON.stringify(columnFilters)}</div>
        <div data-testid="table-loading">{String(isLoading)}</div>
        <div data-testid="table-fetching">{String(isFetching)}</div>
        <div data-testid="table-error">{error ? String(error) : "null"}</div>
        <div data-testid="table-row-count">{rowCount}</div>
        <button data-testid="refresh-button" type="button" onClick={onRefresh}>
          Refresh
        </button>
        <button
          data-testid="change-pagination"
          type="button"
          onClick={() => setPagination({ pageIndex: 1, pageSize: 25 })}
        >
          Change Page
        </button>
        <button
          data-testid="change-sorting"
          type="button"
          onClick={() => setSorting([{ id: "timing.startTime", desc: false }])}
        >
          Change Sorting
        </button>
        <button
          data-testid="change-filters"
          type="button"
          onClick={() =>
            setColumnFilters([{ id: "timing.duration", value: 1000 }])
          }
        >
          Change Filters
        </button>
      </div>
    ),
  }),
);

describe("TemporaryFaultTrackingView", () => {
  const mockUseQuery = ictApi.client.useQuery as any;

  const mockApiResponse = {
    data: [
      {
        id: "A1",
        description: "Test alarm",
        tag: "TAG001",
        status: "ACTIVE",
        reason: "TEST",
        location: { area: "A", section: "S", equipment: "EQ1" },
        timing: {
          startTime: "2024-01-01T00:00:00Z",
          endTime: "2024-01-01T00:05:00Z",
          duration: 300000,
        },
      },
    ],
    metadata: {
      totalResults: 42,
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Default mock implementation
    mockUseQuery.mockReturnValue({
      data: mockApiResponse,
      dataUpdatedAt: Date.now(),
      error: null,
      isLoading: false,
      isFetching: false,
      refetch: vi.fn(),
    });
  });

  it("should render the view bar with correct title", () => {
    render(<TemporaryFaultTrackingView />);

    const viewBar = screen.getByTestId("view-bar");
    expect(viewBar).toBeInTheDocument();
    expect(screen.getByText("Fault Tracking")).toBeInTheDocument();
  });

  it("should render the table with correct props", () => {
    render(<TemporaryFaultTrackingView />);

    const table = screen.getByTestId("temporary-fault-tracking-table");
    expect(table).toBeInTheDocument();

    // Data passed correctly
    const tableData = screen.getByTestId("table-data");
    const parsedData = JSON.parse(tableData.textContent || "[]");
    expect(parsedData).toEqual(mockApiResponse.data);

    // Pagination initialized correctly
    const tablePagination = screen.getByTestId("table-pagination");
    const parsedPagination = JSON.parse(tablePagination.textContent || "{}");
    expect(parsedPagination).toEqual({ pageIndex: 0, pageSize: 50 });

    // Row count passed correctly
    const tableRowCount = screen.getByTestId("table-row-count");
    expect(tableRowCount.textContent).toBe("42");
  });

  it("should update pagination when table triggers change", async () => {
    render(<TemporaryFaultTrackingView />);

    fireEvent.click(screen.getByTestId("change-pagination"));

    await waitFor(() => {
      expect(mockUseQuery).toHaveBeenCalledWith(
        "post",
        "/availability/alarms/list",
        expect.objectContaining({
          body: expect.objectContaining({
            limit: 25,
            offset: 25,
          }),
        }),
        expect.anything(),
      );
    });
  });

  it("should update sorting and map fields when table triggers change", async () => {
    render(<TemporaryFaultTrackingView />);

    fireEvent.click(screen.getByTestId("change-sorting"));

    await waitFor(() => {
      expect(mockUseQuery).toHaveBeenCalledWith(
        "post",
        "/availability/alarms/list",
        expect.objectContaining({
          body: expect.objectContaining({
            sortFields: [{ columnName: "startTime", isDescending: false }],
          }),
        }),
        expect.anything(),
      );
    });
  });

  it("should update filters when table triggers change", async () => {
    render(<TemporaryFaultTrackingView />);

    fireEvent.click(screen.getByTestId("change-filters"));

    await waitFor(() => {
      expect(mockUseQuery).toHaveBeenCalledWith(
        "post",
        "/availability/alarms/list",
        expect.objectContaining({
          body: expect.objectContaining({
            filters: expect.anything(),
          }),
        }),
        expect.anything(),
      );
    });
  });
});
