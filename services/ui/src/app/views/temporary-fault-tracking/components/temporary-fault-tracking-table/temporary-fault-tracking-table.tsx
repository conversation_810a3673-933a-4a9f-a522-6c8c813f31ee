import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import type { ColumnDef } from "@tanstack/react-table";
import { createColumnHelper } from "@tanstack/react-table";
import { useCallback, useEffect, useMemo, useState } from "react";
import { Datagrid } from "../../../../components/datagrid";
import type { FilterState } from "../../../../components/datagrid/types";
import { useTranslation } from "react-i18next";
import {
  formatISOTimeToReadableDateTime,
  formatTimeValue,
} from "../../../../utils/date-util";
import { useConfigSetting } from "../../../../config/hooks/use-config";
import type { FaultAlarm } from "../../types";

const MS_IN_SECOND = 1000;

interface TemporaryFaultTrackingTableProps {
  data: FaultAlarm[];
  pagination: PaginationState;
  setPagination: (pagination: PaginationState) => void;
  sorting: SortingState;
  setSorting: (sorting: SortingState) => void;
  columnFilters: ColumnFiltersState;
  setColumnFilters: (filters: ColumnFiltersState) => void;
  isLoading: boolean;
  isFetching: boolean;
  error: unknown;
  rowCount: number;
  onRefresh?: () => void;
  setGlobalFilter: (globalFilter: string) => void;
}

export function TemporaryFaultTrackingTable({
  data,
  pagination,
  setPagination,
  sorting,
  setSorting,
  setColumnFilters,
  isLoading,
  isFetching,
  error,
  rowCount,
  onRefresh,
  setGlobalFilter,
}: TemporaryFaultTrackingTableProps) {
  const { t } = useTranslation();
  const { setting: timezoneConfig } = useConfigSetting("site-time-zone");

  const columnHelper = createColumnHelper<FaultAlarm>();

  const columnDefinitions = useMemo(
    () => [
      columnHelper.accessor("location.area", {
        header: t("temporaryFaultTracking.columns.area", "Area"),
      }),
      columnHelper.accessor("location.section", {
        header: t("temporaryFaultTracking.columns.section", "Section"),
      }),
      columnHelper.accessor("location.equipment", {
        header: t("temporaryFaultTracking.columns.equipment", "Equipment"),
      }),
      columnHelper.accessor("id", {
        header: t("temporaryFaultTracking.columns.alarmId", "Alarm ID"),
      }),
      columnHelper.accessor("description", {
        header: t("temporaryFaultTracking.columns.description", "Description"),
      }),
      columnHelper.accessor("tag", {
        header: t("temporaryFaultTracking.columns.tag", "Tag"),
      }),
      columnHelper.accessor("timing.startTime", {
        header: t("temporaryFaultTracking.columns.startTime", "Start Time"),
        cell: (info) => {
          const value = info.getValue() as string | Date | undefined;
          return value
            ? formatISOTimeToReadableDateTime(
                value as string,
                (timezoneConfig?.value as string) ?? undefined,
              )
            : "";
        },
      }),
      columnHelper.accessor("timing.endTime", {
        header: t("temporaryFaultTracking.columns.endTime", "End Time"),
        cell: (info) => {
          const value = info.getValue() as string | Date | undefined;
          return value
            ? formatISOTimeToReadableDateTime(
                value as string,
                (timezoneConfig?.value as string) ?? undefined,
              )
            : "";
        },
      }),
      columnHelper.accessor("timing.duration", {
        header: t("temporaryFaultTracking.columns.duration", "Duration"),
        cell: (info) => {
          const raw = info.getValue() as string | number | undefined;
          const num = typeof raw === "string" ? Number(raw) : raw;
          const seconds = num && !Number.isNaN(num) ? num / MS_IN_SECOND : 0;
          return formatTimeValue(seconds, "seconds");
        },
      }),
      columnHelper.accessor("status", {
        header: t("temporaryFaultTracking.columns.status", "Status"),
      }),
      columnHelper.accessor("reason", {
        header: t("temporaryFaultTracking.columns.reason", "Reason"),
      }),
    ],
    [t, timezoneConfig?.value],
  ) as ColumnDef<FaultAlarm>[];

  const [columns, setColumns] =
    useState<ColumnDef<FaultAlarm>[]>(columnDefinitions);

  useEffect(() => {
    setColumns(columnDefinitions);
  }, [columnDefinitions]);

  const handlePageChange = useCallback(
    (newPagination: PaginationState) => {
      setPagination(newPagination);
    },
    [setPagination],
  );

  const handleSort = useCallback(
    (newSorting: SortingState) => {
      setSorting(newSorting);
    },
    [setSorting],
  );

  const handleFilter = useCallback(
    (newFilters: FilterState) => {
      const newColumnFilters: ColumnFiltersState = Object.entries(
        newFilters.filters,
      ).map(([id, value]) => ({ id, value }));
      setColumnFilters(newColumnFilters);
      setGlobalFilter(newFilters.globalFilter);
    },
    [setColumnFilters, setGlobalFilter],
  );

  return (
    <Datagrid
      columns={columns}
      data={data}
      mode="server"
      totalRows={rowCount}
      isLoading={isLoading || isFetching}
      error={error ? String(error) : undefined}
      onPageChange={handlePageChange}
      onSort={handleSort}
      onFilter={handleFilter}
      onRefreshClick={onRefresh}
      showRefreshButton={!!onRefresh}
      initialPagination={pagination}
      initialSorting={sorting}
      enableSelection={false}
      showExportButton={false}
    />
  );
}
