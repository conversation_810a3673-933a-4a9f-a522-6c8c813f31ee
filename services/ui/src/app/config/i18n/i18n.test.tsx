import i18n from "../../../test-utils/i18n-testing";
import translationEn from "../../../../public/locales/en/translation.json";
import translationFr from "../../../../public/locales/fr/translation.json";

function isNonEmptyString(value: any): value is string {
  return typeof value === "string" && value !== "";
}

// vi.mock('i18next-browser-languagedetector', async (importOriginal) => {
//   const actual = await importOriginal() as any; // Use type assertion here
//   return {
//     __esModule: true, // Important for ES modules
//     ...actual,
//     detect: () => 'en',
//     init: () => { }, // It's safer to add empty mocks for other functions if they are used
//     cacheUserLanguage: () => { }, // Mock if it's used by i18next during init
//   };
// });

describe("i18n", () => {
  it("should be initialised", () => {
    expect(i18n.isInitialized).toBeTruthy();
  });

  it("should have default language set to `en`", () => {
    expect(i18n.languages[0]).toBe("en");
  });

  it("should have translation files available for English and French", async () => {
    const translationEnglish = i18n.getResourceBundle("en", "translation");
    expect(translationEnglish).toEqual(translationEn);

    await i18n.changeLanguage("fr");

    const translationFrench = i18n.getResourceBundle("fr", "translation");
    expect(translationFrench).toEqual(translationFr);

    await i18n.changeLanguage("en");
  });

  it("should fallback to `en` if translation is missing", async () => {
    await i18n.changeLanguage("de");

    for (const key in translationEn) {
      const translationValue = translationEn[key as keyof typeof translationEn];

      if (isNonEmptyString(translationValue)) {
        const translatedText = i18n.t(key);
        const englishValue = translationValue;
        expect(translatedText).toBe(englishValue);
      }
    }
  });
});
