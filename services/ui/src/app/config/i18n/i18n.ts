/* eslint-disable @typescript-eslint/no-unused-vars */
import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import Backend from "i18next-http-backend";
import LanguageDetector from "i18next-browser-languagedetector";

// Initialize i18n with a promise that can be awaited
const i18nInitPromise = i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    fallbackLng: "en",
    debug: false,
    interpolation: {
      escapeValue: false, // not needed for react as it escapes by default
    },
    detection: {
      // This may need to be changed if we implement language selection into CT.
      order: ["navigator", "localStorage", "cookie", "htmlTag"],
    },
  });

// Add formatters
i18n.services.formatter?.add(
  "formatNumber",
  (value: number, lng?, options?) => {
    return value.toLocaleString(options);
  },
);

i18n.services.formatter?.add("formatDate", (value: Date, lng?, options?) => {
  return value.toLocaleString(options);
});

export { i18nInitPromise };
export default i18n;
