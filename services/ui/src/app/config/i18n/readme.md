# Control Tower i18n

i18n is the process of designing and developing software to be adapted for users of different languages. As Control Tower may be used by users of different tongues, this i18n implementation aims to make our software accessible in different languages.

Three functions are available for use to translate strings in the codebase. The first is recommended where possible, but can only be used within functional components. Keys for the strings may be manually added to the `translation.json` files for each locale, or by using the installed parser (details given below).

## The react-i18next translate hook

This hook can only be used within functional components. It must be imported as shown below, and acquires the i18n context from the context provider. Keys should usually be the component name, with a more specific identifier for the particular value. A fallback may be given, and is recommended for the parser to give a default value in the `translation.json` files. If no fallback is given, the value will equal the key name. The advantage of this method of translation is due to its automatic update upon language change.

** Special characters should be avoided in the key given to the `t` function. Best practice is to use the name of the component as the first part
of the key, with more specific subsequent identifiers **. For example:

```js
<div>
  {t(
    "confirmationDialog.errorMessage.noDataFound",
    "No data is available for the requested resource: {{resource}}",
    { resource: resource },
  )}
</div>
```

Options may be passed to the `t` hook, after the fallback value. This invokes interpolation to insert dynamic values into the string.

Formatters may also be used. Currently, two formatters are available: `formatDate` and `formatNumber` (defined in the i18n.ts file in the `config/i18n` directory. Formatting options, which must follow the `toLocaleString` formatting options, may be given to customise the value further. The name of the formatter should be given after the variable to be formatted, as shown below.

```js
import { useTranslation } from 'react-i18next';

const { t } = useTranslation();

return (
  <div>{t("componentName.title", "fallback Value")}</div>
  <div>{t("componentName.welcomeUser", "Welcome {{name}}!", {name: userName})}</div>
  <div>{t(
    "componentName.numberOfViews",
    "Number of Views: {{number, formatNumber}}", {
      number: noOfViews,
      maximumSignificantDigits: 3
  })}</div>
)
```

## Other functions

As the other methods of calling i18n should be used rarely, see the [react-i18next documentation](https://react.i18next.com/) for more details. The other two methods you may come across are the base `i18n.t()` function (used outside of a functional component) and the `<Trans>` component (used where React / HTML nodes are integrated into a sentence). It is worth noting that the base `i18n.t()` function does not ensure translations are loaded before performing translations. Therefore, a promise is given in the i18n config that can be awaited to prevent race conditions.

## Using the parser to extract keys and values

From the root of the `ui` directory, run the command:

```bash
yarn run extract
```

This will utilise the `i18next-parser.config.js` file to create `translation.json` files for each locale listed in the config file. Keys with no fallback value will default to the key name. Upon calling the `t()` hook or other i18n functions, these generated files are used to locale and insert the appropriate string. These files can be manually translated, or by using the `translate_keys.py` script in the `localise` directory. See the README in the `localise` directory for further details. Keys no longer found in the codebase will be removed from the json files. Otherwise, existing key value pairs will remain unchanged if defined.
