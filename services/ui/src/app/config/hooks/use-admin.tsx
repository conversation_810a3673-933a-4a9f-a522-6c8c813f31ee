import { logger } from "../../utils/logger";
import { ictApi } from "../../api/ict-api";
import { components } from "@ict/sdk/openapi-react-query";
import { useNotification } from "../../components/toast/use-notification";
import { useTranslation } from "react-i18next";

export type ResendEmailVerificationResponse =
  components["schemas"]["ResendEmailVerificationResponse"];

export const useResendEmailVerification = () => {
  const { success, error: showError, info } = useNotification();
  const { t } = useTranslation();

  const mutation = ictApi.client.useMutation(
    "post",
    "/admin/auth/resend-verification",
    {
      onSuccess: (data) => {
        if (data && typeof data === "object" && "message" in data) {
          const response = data as ResendEmailVerificationResponse;
          logger.info(
            "Email verification resent successfully:",
            response.message,
          );

          // Check if this is a social auth case (if the response has isSocialAuth property)
          const isSocialAuth =
            "isSocialAuth" in response && response.isSocialAuth;

          if (isSocialAuth) {
            info(response.message, {
              title: t("emailVerification.emailAlreadyVerified"),
              duration: 8000, // Longer duration for info messages
            });
          } else {
            success(response.message, {
              title: t("emailVerification.emailSent"),
            });
          }
        } else {
          logger.info("Email verification resent successfully");
          success(t("emailVerification.emailSentSuccessfully"), {
            title: t("emailVerification.emailSent"),
          });
        }
      },
      onError: (error: Error) => {
        logger.error("Failed to resend email verification:", error);

        // Extract user-friendly error message
        let errorMessage = t("emailVerification.genericError");
        let errorTitle = t("emailVerification.error");

        // Check if it's an API error with a specific message
        if (error && typeof error === "object" && "message" in error) {
          errorMessage = error.message as string;
        }

        // Handle specific error cases for better user experience
        if (errorMessage.includes("Too many verification email requests")) {
          errorMessage = t("emailVerification.rateLimitMessage");
          errorTitle = t("emailVerification.rateLimitExceeded");
        } else if (errorMessage.includes("User not found")) {
          errorMessage = t("emailVerification.userNotFoundMessage");
          errorTitle = t("emailVerification.userNotFound");
        } else if (errorMessage.includes("Invalid Auth0 credentials")) {
          errorMessage = t("emailVerification.authErrorMessage");
          errorTitle = t("emailVerification.authenticationError");
        }

        showError(errorMessage, {
          title: errorTitle,
          duration: 10000, // Longer duration for errors
        });
      },
    },
  );

  const resendVerification =
    async (): Promise<ResendEmailVerificationResponse | void> => {
      try {
        const result = await mutation.mutateAsync({});
        if (result && typeof result === "object" && "success" in result) {
          return result as ResendEmailVerificationResponse;
        }
        return;
      } catch (error) {
        logger.error("Error resending email verification:", error);
        throw error;
      }
    };

  return {
    resendVerification,
    isLoading: mutation.isPending,
    error: mutation.error,
    isSuccess: mutation.isSuccess,
    isError: mutation.isError,
  };
};
