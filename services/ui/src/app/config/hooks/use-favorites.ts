import { useState, useCallback, useEffect } from "react";
import { useConfigSetting, updateUserWritableSetting } from "./use-config";
import type { AppConfigSetting } from "./use-config";
import { logger } from "../../utils/logger";

interface FavoritesData {
  favorites: string[];
}

// Hook to manage user favorites
export const useFavorites = () => {
  // Get the user favorites setting from the config API
  const { setting, isLoading, error } = useConfigSetting("user-favorites");

  // Keep a local state of favorites for immediate UI updates
  const [localFavorites, setLocalFavorites] = useState<string[]>(
    (setting?.value as FavoritesData)?.favorites || [],
  );

  // Update local favorites when setting changes
  useEffect(() => {
    if (setting?.value) {
      const favorites = (setting.value as FavoritesData)?.favorites || [];
      setLocalFavorites(favorites);
    }
  }, [setting]);

  // Initialize the favorites setting if it doesn't exist
  useEffect(() => {
    const initializeFavorites = async () => {
      if (!isLoading && !setting && !error) {
        try {
          const newSetting: AppConfigSetting = {
            id: "",
            name: "user-favorites",
            group: "user-writable",
            dataType: "json",
            source: "user",
            description: "User-specific favorite pages stored as route IDs",
            value: { favorites: [] },
          };

          await updateUserWritableSetting(newSetting);
        } catch (error) {
          logger.error("Failed to initialize favorites setting", {
            error,
            context: "useFavorites.initializeFavorites",
          });
        }
      }
    };

    initializeFavorites();
  }, [isLoading, setting, error]);

  // Check if a menu item is in the favorites list
  const isFavorite = useCallback(
    (menuItemId: string) => {
      const result = localFavorites.includes(menuItemId);
      return result;
    },
    [localFavorites],
  );

  // Helper function to update favorites in both local state and API
  const updateFavoritesList = useCallback(
    async (newFavorites: string[]) => {
      if (!setting) return;

      // Store the original favorites before updating
      const originalFavorites = [...localFavorites];

      // Update local state immediately for responsive UI
      setLocalFavorites(newFavorites);

      // Prepare the updated setting
      const updatedSetting: AppConfigSetting = {
        ...setting,
        value: { favorites: newFavorites },
        source: "user",
        group: "user-writable",
      };

      // Update the setting in the API
      try {
        await updateUserWritableSetting(updatedSetting);
      } catch (error) {
        logger.error("Failed to update favorites", { error });
        // Revert the local state if the API call fails
        setLocalFavorites(() => originalFavorites);
      }
    },
    [setting, localFavorites],
  );

  // Add a menu item to favorites
  const addFavorite = useCallback(
    async (menuItemId: string) => {
      if (!setting || isFavorite(menuItemId)) return;

      const updatedFavorites = [...localFavorites, menuItemId];
      await updateFavoritesList(updatedFavorites);
    },
    [setting, localFavorites, isFavorite, updateFavoritesList],
  );

  // Remove a menu item from favorites
  const removeFavorite = useCallback(
    async (menuItemId: string) => {
      if (!setting || !isFavorite(menuItemId)) return;

      const updatedFavorites = localFavorites.filter((id) => id !== menuItemId);
      await updateFavoritesList(updatedFavorites);
    },
    [setting, localFavorites, isFavorite, updateFavoritesList],
  );

  return {
    favorites: localFavorites,
    isFavorite,
    addFavorite,
    removeFavorite,
    isLoading,
    error,
  };
};
