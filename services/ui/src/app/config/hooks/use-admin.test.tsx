import { renderHook } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { ictApi } from "../../api/ict-api";
import { logger } from "../../utils/logger";
import {
  useResendEmailVerification,
  type ResendEmailVerificationResponse,
} from "./use-admin";

vi.mock("../../api/ict-api", () => ({
  ictApi: {
    client: {
      useMutation: vi.fn(),
    },
  },
}));

vi.mock("../../utils/logger", () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
  },
}));

describe("Admin Hooks", () => {
  const mockSuccessResponse: ResendEmailVerificationResponse = {
    success: true,
    message: "Email verification resent successfully",
  };

  const mockMutation = {
    mutateAsync: vi.fn(),
    isPending: false,
    error: null,
    isSuccess: false,
    isError: false,
  };

  beforeEach(() => {
    vi.resetAllMocks();
    ictApi.client.useMutation = vi.fn().mockReturnValue(mockMutation);
  });

  describe("useResendEmailVerification", () => {
    it("should return mutation functions and initial states", () => {
      const { result } = renderHook(() => useResendEmailVerification());

      expect(result.current.resendVerification).toBeInstanceOf(Function);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
      expect(result.current.isSuccess).toBe(false);
      expect(result.current.isError).toBe(false);
    });

    it("should configure mutation with correct parameters", () => {
      renderHook(() => useResendEmailVerification());

      expect(ictApi.client.useMutation).toHaveBeenCalledWith(
        "post",
        "/admin/auth/resend-verification",
        expect.objectContaining({
          onSuccess: expect.any(Function),
          onError: expect.any(Function),
        }),
      );
    });

    it("should handle successful response with message", async () => {
      ictApi.client.useMutation = vi
        .fn()
        .mockImplementation((_method, _path, options) => {
          // Simulate successful response
          options.onSuccess(mockSuccessResponse);
          return {
            ...mockMutation,
            mutateAsync: vi.fn().mockResolvedValue(mockSuccessResponse),
            isSuccess: true,
          };
        });

      renderHook(() => useResendEmailVerification());

      expect(logger.info).toHaveBeenCalledWith(
        "Email verification resent successfully:",
        "Email verification resent successfully",
      );
    });

    it("should handle successful response without message", async () => {
      ictApi.client.useMutation = vi
        .fn()
        .mockImplementation((_method, _path, options) => {
          // Simulate successful response without message
          options.onSuccess(undefined);
          return {
            ...mockMutation,
            mutateAsync: vi.fn().mockResolvedValue(undefined),
            isSuccess: true,
          };
        });

      renderHook(() => useResendEmailVerification());

      expect(logger.info).toHaveBeenCalledWith(
        "Email verification resent successfully",
      );
    });

    it("should handle error response", async () => {
      const mockError = new Error("API error");

      ictApi.client.useMutation = vi
        .fn()
        .mockImplementation((_method, _path, options) => {
          // Simulate error response
          options.onError(mockError);
          return {
            ...mockMutation,
            error: mockError,
            isError: true,
          };
        });

      renderHook(() => useResendEmailVerification());

      expect(logger.error).toHaveBeenCalledWith(
        "Failed to resend email verification:",
        mockError,
      );
    });

    it("should handle loading state", () => {
      ictApi.client.useMutation = vi.fn().mockReturnValue({
        ...mockMutation,
        isPending: true,
      });

      const { result } = renderHook(() => useResendEmailVerification());

      expect(result.current.isLoading).toBe(true);
    });

    describe("resendVerification function", () => {
      it("should successfully resend verification and return response", async () => {
        const mockMutateAsync = vi.fn().mockResolvedValue(mockSuccessResponse);

        ictApi.client.useMutation = vi.fn().mockReturnValue({
          ...mockMutation,
          mutateAsync: mockMutateAsync,
        });

        const { result } = renderHook(() => useResendEmailVerification());

        const response = await result.current.resendVerification();

        expect(mockMutateAsync).toHaveBeenCalledWith({});
        expect(response).toEqual(mockSuccessResponse);
      });

      it("should return undefined for response without success property", async () => {
        const mockMutateAsync = vi
          .fn()
          .mockResolvedValue({ someOtherProperty: true });

        ictApi.client.useMutation = vi.fn().mockReturnValue({
          ...mockMutation,
          mutateAsync: mockMutateAsync,
        });

        const { result } = renderHook(() => useResendEmailVerification());

        const response = await result.current.resendVerification();

        expect(response).toBeUndefined();
      });

      it("should return undefined for null response", async () => {
        const mockMutateAsync = vi.fn().mockResolvedValue(null);

        ictApi.client.useMutation = vi.fn().mockReturnValue({
          ...mockMutation,
          mutateAsync: mockMutateAsync,
        });

        const { result } = renderHook(() => useResendEmailVerification());

        const response = await result.current.resendVerification();

        expect(response).toBeUndefined();
      });

      it("should handle and re-throw errors", async () => {
        const mockError = new Error("Network error");
        const mockMutateAsync = vi.fn().mockRejectedValue(mockError);

        ictApi.client.useMutation = vi.fn().mockReturnValue({
          ...mockMutation,
          mutateAsync: mockMutateAsync,
        });

        const { result } = renderHook(() => useResendEmailVerification());

        await expect(result.current.resendVerification()).rejects.toThrow(
          "Network error",
        );

        expect(logger.error).toHaveBeenCalledWith(
          "Error resending email verification:",
          mockError,
        );
      });
    });

    describe("state mapping", () => {
      it("should map isPending to isLoading", () => {
        ictApi.client.useMutation = vi.fn().mockReturnValue({
          ...mockMutation,
          isPending: true,
        });

        const { result } = renderHook(() => useResendEmailVerification());

        expect(result.current.isLoading).toBe(true);
      });

      it("should map error state correctly", () => {
        const mockError = new Error("Test error");
        ictApi.client.useMutation = vi.fn().mockReturnValue({
          ...mockMutation,
          error: mockError,
          isError: true,
        });

        const { result } = renderHook(() => useResendEmailVerification());

        expect(result.current.error).toBe(mockError);
        expect(result.current.isError).toBe(true);
      });

      it("should map success state correctly", () => {
        ictApi.client.useMutation = vi.fn().mockReturnValue({
          ...mockMutation,
          isSuccess: true,
        });

        const { result } = renderHook(() => useResendEmailVerification());

        expect(result.current.isSuccess).toBe(true);
      });
    });
  });
});
