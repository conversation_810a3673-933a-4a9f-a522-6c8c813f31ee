import { renderHook, act } from "@testing-library/react";
import { describe, expect, it, beforeEach, vi } from "vitest";
import { useFavorites } from "./use-favorites";
import { useConfigSetting, updateUserWritableSetting } from "./use-config";

// Mock the useConfigSetting and updateUserWritableSetting functions
vi.mock("./use-config", () => ({
  useConfigSetting: vi.fn(),
  updateUserWritableSetting: vi.fn(),
}));

describe("useFavorites", () => {
  const mockSetting = {
    id: "favorites-id",
    name: "user-favorites",
    value: { favorites: ["menu-item-1", "menu-item-2"] },
    dataType: "json",
    source: "user",
    description: "User favorites",
    group: "user-writable",
  };

  beforeEach(() => {
    vi.resetAllMocks();
    (useConfigSetting as ReturnType<typeof vi.fn>).mockReturnValue({
      setting: mockSetting,
      isLoading: false,
      error: null,
    });
    (updateUserWritableSetting as ReturnType<typeof vi.fn>).mockResolvedValue({
      success: true,
    });
  });

  it("should return the current favorites", () => {
    const { result } = renderHook(() => useFavorites());
    expect(result.current.favorites).toEqual(["menu-item-1", "menu-item-2"]);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it("should check if an item is favorited", () => {
    const { result } = renderHook(() => useFavorites());
    expect(result.current.isFavorite("menu-item-1")).toBe(true);
    expect(result.current.isFavorite("menu-item-3")).toBe(false);
  });

  it("should add a favorite", async () => {
    const { result } = renderHook(() => useFavorites());
    await act(async () => {
      await result.current.addFavorite("menu-item-3");
    });
    expect(result.current.favorites).toContain("menu-item-3");
    expect(updateUserWritableSetting).toHaveBeenCalledWith({
      ...mockSetting,
      value: { favorites: ["menu-item-1", "menu-item-2", "menu-item-3"] },
      group: "user-writable",
    });
  });

  it("should not add a duplicate favorite", async () => {
    const { result } = renderHook(() => useFavorites());
    await act(async () => {
      await result.current.addFavorite("menu-item-1");
    });
    expect(result.current.favorites).toEqual(["menu-item-1", "menu-item-2"]);
    expect(updateUserWritableSetting).not.toHaveBeenCalled();
  });

  it("should remove a favorite", async () => {
    const { result } = renderHook(() => useFavorites());
    await act(async () => {
      await result.current.removeFavorite("menu-item-1");
    });
    expect(result.current.favorites).not.toContain("menu-item-1");
    expect(updateUserWritableSetting).toHaveBeenCalledWith({
      ...mockSetting,
      value: { favorites: ["menu-item-2"] },
      group: "user-writable",
    });
  });

  it("should not attempt to remove a non-favorited item", async () => {
    const { result } = renderHook(() => useFavorites());
    await act(async () => {
      await result.current.removeFavorite("menu-item-3");
    });
    expect(result.current.favorites).toEqual(["menu-item-1", "menu-item-2"]);
    expect(updateUserWritableSetting).not.toHaveBeenCalled();
  });

  it("should handle empty favorites", () => {
    (useConfigSetting as ReturnType<typeof vi.fn>).mockReturnValue({
      setting: { ...mockSetting, value: { favorites: [] } },
      isLoading: false,
      error: null,
    });
    const { result } = renderHook(() => useFavorites());
    expect(result.current.favorites).toEqual([]);
  });

  it("should handle missing favorites data", () => {
    (useConfigSetting as ReturnType<typeof vi.fn>).mockReturnValue({
      setting: { ...mockSetting, value: null },
      isLoading: false,
      error: null,
    });
    const { result } = renderHook(() => useFavorites());
    expect(result.current.favorites).toEqual([]);
  });

  // Moved from use-favorites.user-setting.spec.tsx
  it("should use the default value if no user value exists", () => {
    const defaultSetting = {
      id: "default-id",
      name: "user-favorites",
      value: { favorites: ["default-favorite"] },
      dataType: "json",
      source: "default",
      description: "Default favorites",
      group: "user-writable",
    };
    (useConfigSetting as ReturnType<typeof vi.fn>).mockReturnValue({
      setting: defaultSetting,
      isLoading: false,
      error: null,
    });
    const { result } = renderHook(() => useFavorites());
    expect(result.current.favorites).toEqual(["default-favorite"]);
  });

  it("should use the user value if it exists", () => {
    const userSetting = {
      id: "user-id",
      name: "user-favorites",
      value: { favorites: ["user-favorite"] },
      dataType: "json",
      source: "user",
      description: "User favorites",
      group: "user-writable",
    };
    (useConfigSetting as ReturnType<typeof vi.fn>).mockReturnValue({
      setting: userSetting,
      isLoading: false,
      error: null,
    });
    const { result } = renderHook(() => useFavorites());
    expect(result.current.favorites).toEqual(["user-favorite"]);
  });

  it("should update only the user value and not the default", async () => {
    const userSetting = {
      id: "user-id",
      name: "user-favorites",
      value: { favorites: ["user-favorite"] },
      dataType: "json",
      source: "user",
      description: "User favorites",
      group: "user-writable",
    };
    (useConfigSetting as ReturnType<typeof vi.fn>).mockReturnValue({
      setting: userSetting,
      isLoading: false,
      error: null,
    });
    const { result } = renderHook(() => useFavorites());
    await act(async () => {
      await result.current.addFavorite("another-favorite");
    });
    expect(updateUserWritableSetting).toHaveBeenCalledWith({
      ...userSetting,
      value: { favorites: ["user-favorite", "another-favorite"] },
      source: "user",
      group: "user-writable",
    });
  });

  it("should not update the default value when user updates favorites", async () => {
    const defaultSetting = {
      id: "default-id",
      name: "user-favorites",
      value: { favorites: ["default-favorite"] },
      dataType: "json",
      source: "default",
      description: "Default favorites",
      group: "user-writable",
    };
    (useConfigSetting as ReturnType<typeof vi.fn>).mockReturnValue({
      setting: defaultSetting,
      isLoading: false,
      error: null,
    });
    const { result } = renderHook(() => useFavorites());
    await act(async () => {
      await result.current.addFavorite("new-user-favorite");
    });
    expect(updateUserWritableSetting).toHaveBeenCalledWith(
      expect.objectContaining({
        source: "user",
        value: { favorites: ["default-favorite", "new-user-favorite"] },
        group: "user-writable",
      }),
    );
  });

  it("returns user favorites if set", () => {
    (useConfigSetting as ReturnType<typeof vi.fn>).mockReturnValue({
      setting: {
        id: "user-id",
        name: "user-favorites",
        value: {
          favorites: ["picking-buffer-area-details", "inbound-overview"],
        },
        dataType: "json",
        source: "user",
        description: "User favorites",
        group: "user-writable",
      },
      isLoading: false,
      error: null,
    });
    const { result } = renderHook(() => useFavorites());
    expect(result.current.favorites).toEqual([
      "picking-buffer-area-details",
      "inbound-overview",
    ]);
  });

  it("returns default favorites if user has not set any", () => {
    (useConfigSetting as ReturnType<typeof vi.fn>).mockReturnValue({
      setting: {
        id: "default-id",
        name: "user-favorites",
        value: {
          favorites: ["picking-buffer-area-details", "inbound-overview"],
        },
        dataType: "json",
        source: "default",
        description: "Default favorites",
        group: "user-writable",
      },
      isLoading: false,
      error: null,
    });
    const { result } = renderHook(() => useFavorites());
    expect(result.current.favorites).toEqual([
      "picking-buffer-area-details",
      "inbound-overview",
    ]);
  });

  it("returns empty array if default is empty and user has not set any", () => {
    (useConfigSetting as ReturnType<typeof vi.fn>).mockReturnValue({
      setting: {
        id: "default-id",
        name: "user-favorites",
        value: { favorites: [] },
        dataType: "json",
        source: "default",
        description: "Default favorites",
        group: "user-writable",
      },
      isLoading: false,
      error: null,
    });
    const { result } = renderHook(() => useFavorites());
    expect(result.current.favorites).toEqual([]);
  });

  it("returns user favorites if set, and default remains empty", () => {
    (useConfigSetting as ReturnType<typeof vi.fn>).mockReturnValue({
      setting: {
        id: "user-id",
        name: "user-favorites",
        value: { favorites: ["menu-item-1", "menu-item-2"] },
        dataType: "json",
        source: "user",
        description: "User favorites",
        group: "user-writable",
      },
      isLoading: false,
      error: null,
    });
    const { result } = renderHook(() => useFavorites());
    expect(result.current.favorites).toEqual(["menu-item-1", "menu-item-2"]);
    // Simulate a backend check for default (optional, for completeness)
    const defaultSetting = {
      id: "default-id",
      name: "user-favorites",
      value: { favorites: [] },
      dataType: "json",
      source: "default",
      description: "Default favorites",
      group: "user-writable",
    };
    expect(defaultSetting.value.favorites).toEqual([]);
  });
});
