import type { MenuItem } from "@ict/sdk/types";
import { TFunction } from "i18next";
import { useEffect, useState } from "react";
import { useRoles } from "../../auth/hooks/use-roles";
import { type AppConfigSetting, useConfig } from "../hooks/use-config";
import { applyAllMenuFilters } from "./filters/menu-filters";
import { applyMenuTransformers } from "./menu-transformers";
import type { MenuHookResult } from "./types";
import { useNavigate } from "react-router";

/**
 * Hook to manage menu items based on user authentication, permissions, and feature flags
 * @returns Object containing menu items and loading state
 */
export const useMenu = (t: TFunction): MenuHookResult => {
  const { roles, isLoading: rolesLoading } = useRoles();
  const { data: configSettings, isLoading: configLoading } = useConfig();
  const navigate = useNavigate();
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadMenuItems = async () => {
      if (configLoading || rolesLoading) return;

      try {
        const baseMenuItems = getFilteredMenuItems(configSettings, roles, t);

        const transformedItems = await applyMenuTransformers(baseMenuItems, {
          configSettings,
          roles,
        });

        // If we have a "homePage" and our current location is "/", route to it
        const homePageSetting = configSettings?.find(
          (setting) => setting.name === "home-page-url",
        );

        if (window.location.pathname === "/") {
          navigate(
            (homePageSetting?.value as string) || "/ict-outbound-overview",
          );
        }

        setMenuItems(transformedItems);
      } catch (error) {
        console.error("Error loading menu items:", error);
        setMenuItems([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadMenuItems();
  }, [configSettings, roles, configLoading]);

  const defaultRoute = findDefaultRoute(menuItems);
  return {
    menuItems,
    isLoading: isLoading || configLoading,
    defaultRoute,
  };
};

/**
 * Finds the first default route in the menu items hierarchy
 * @param items Menu items to search through
 * @returns The link of the first item marked as defaultRoute, or undefined if none found
 */
const findDefaultRoute = (items: MenuItem[]): string | undefined => {
  for (const item of items) {
    if (item.defaultRoute) {
      return item.link;
    }
    if (item.children) {
      const childDefaultRoute = findDefaultRoute(item.children);
      if (childDefaultRoute) {
        return childDefaultRoute;
      }
    }
  }
  return undefined;
};

const getFilteredMenuItems = (
  configSettings: AppConfigSetting[] | null,
  roles: string[],
  t: TFunction,
): MenuItem[] => {
  if (!configSettings) return [];

  console.log(
    "useMenu -> getFilteredMenuItems - configSettings",
    configSettings,
  );

  const menu = configSettings.find((setting) => setting.name === "menu");
  if (!menu) return [];

  const menuItems = menu.value as MenuItem[];

  // Recursive function to translate menu items and their children
  const translateMenuItems = (items: MenuItem[]): MenuItem[] => {
    return items.map((item) => ({
      ...item,
      label: t(item.label),
      children: item.children ? translateMenuItems(item.children) : undefined,
    }));
  };

  const translatedMenuItems = translateMenuItems(menuItems);

  return applyAllMenuFilters(translatedMenuItems, configSettings, roles);
};
