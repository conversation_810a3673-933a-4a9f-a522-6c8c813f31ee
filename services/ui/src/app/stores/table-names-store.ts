import { create } from "zustand";

interface TableNamesState {
  tableNamesMap: Map<string, string>;
  setTableNames: (queryKey: string, tableNames: string | null) => void;
  getTableNames: (queryKey: string) => string | null;
}

export const useTableNamesStore = create<TableNamesState>((set, get) => ({
  tableNamesMap: new Map(),
  setTableNames: (queryKey, tableNames) => {
    console.debug(
      "Setting table names for key:",
      queryKey,
      "value:",
      tableNames,
    );
    set((state) => {
      const newMap = new Map(state.tableNamesMap);
      if (tableNames) {
        newMap.set(queryKey, tableNames);
      } else {
        newMap.delete(queryKey);
      }
      return { tableNamesMap: newMap };
    });
  },
  getTableNames: (queryKey) => {
    const currentMap = get().tableNamesMap;
    const result = currentMap.get(queryKey) || null;

    return result;
  },
}));
