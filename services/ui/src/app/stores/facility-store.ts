import { create } from "zustand";
import type { FacilityMap } from "@ict/sdk/types";

interface FacilityState {
  selectedFacility: FacilityMap | null;
  userKey: string | null; // Track current user to detect context changes
  setSelectedFacility: (facility: FacilityMap) => void;
  setUserKey: (key: string) => void;
}

/**
 * This facility store is used to globally store the selected facility and the user's key.
 * It is mainly used by the facility API middleware to pass the selected facility header
 */
export const useFacilityStore = create<FacilityState>()((set, get) => ({
  selectedFacility: null,
  userKey: null,
  facilitySwitcherEnabled: false,

  setSelectedFacility: (facility: FacilityMap) => {
    // Update local state
    set({ selectedFacility: facility });
  },

  setUserKey: (key: string) => {
    const currentUserKey = get().userKey;

    // If user key changes, clear the facility selection (organization switch)
    if (currentUserKey && currentUserKey !== key) {
      set({ selectedFacility: null, userKey: key });
    } else {
      set({ userKey: key });
    }
  },
}));
