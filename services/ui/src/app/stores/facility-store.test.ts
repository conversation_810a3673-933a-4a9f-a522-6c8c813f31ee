import { describe, expect, it, vi, beforeEach } from "vitest";
import { act } from "../../test-utils";
import { useFacilityStore } from "./facility-store";
import type { FacilityMap } from "@ict/sdk/types";

describe("useFacilityStore", () => {
  const mockFacility: FacilityMap = {
    id: "facility1",
    name: "Test Facility",
    dataset: "test-dataset",
    default: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Reset store to initial state before each test
    useFacilityStore.setState({
      selectedFacility: null,
      userKey: null,
    });
  });

  it("initializes with default state", () => {
    const state = useFacilityStore.getState();

    expect(state.selectedFacility).toBeNull();
    expect(state.userKey).toBeNull();
  });

  it("successfully sets selected facility", () => {
    const { setSelectedFacility } = useFacilityStore.getState();

    act(() => {
      setSelectedFacility(mockFacility);
    });

    const state = useFacilityStore.getState();
    expect(state.selectedFacility).toEqual(mockFacility);
  });

  it("handles facility selection synchronously", () => {
    const { setSelectedFacility } = useFacilityStore.getState();

    act(() => {
      setSelectedFacility(mockFacility);
    });

    const state = useFacilityStore.getState();
    expect(state.selectedFacility).toEqual(mockFacility);
  });

  it("sets userKey and preserves existing state", () => {
    // Set initial facility
    act(() => {
      useFacilityStore.setState({ selectedFacility: mockFacility });
    });

    const { setUserKey } = useFacilityStore.getState();

    act(() => {
      setUserKey("user123");
    });

    const state = useFacilityStore.getState();
    expect(state.userKey).toBe("user123");
    expect(state.selectedFacility).toEqual(mockFacility); // Should preserve existing facility
  });

  it("clears facility when user key changes (organization switch)", () => {
    // Set initial state
    act(() => {
      useFacilityStore.setState({
        selectedFacility: mockFacility,
        userKey: "user123",
      });
    });

    const { setUserKey } = useFacilityStore.getState();

    // Change user key (simulate organization switch)
    act(() => {
      setUserKey("user456");
    });

    const state = useFacilityStore.getState();
    expect(state.userKey).toBe("user456");
    expect(state.selectedFacility).toBeNull(); // Should clear facility
  });

  it("does not clear facility when setting same user key", () => {
    // Set initial state
    act(() => {
      useFacilityStore.setState({
        selectedFacility: mockFacility,
        userKey: "user123",
      });
    });

    const { setUserKey } = useFacilityStore.getState();

    // Set same user key
    act(() => {
      setUserKey("user123");
    });

    const state = useFacilityStore.getState();
    expect(state.userKey).toBe("user123");
    expect(state.selectedFacility).toEqual(mockFacility); // Should preserve facility
  });

  it("handles setUserKey when no current userKey is set", () => {
    // Set initial facility but no user key
    act(() => {
      useFacilityStore.setState({ selectedFacility: mockFacility });
    });

    const { setUserKey } = useFacilityStore.getState();

    act(() => {
      setUserKey("user123");
    });

    const state = useFacilityStore.getState();
    expect(state.userKey).toBe("user123");
    expect(state.selectedFacility).toEqual(mockFacility); // Should preserve facility
  });

  it("handles multiple facility selections", () => {
    const { setSelectedFacility } = useFacilityStore.getState();

    const facility1: FacilityMap = {
      id: "facility1",
      name: "Facility 1",
      dataset: "dataset1",
      default: false,
    };

    const facility2: FacilityMap = {
      id: "facility2",
      name: "Facility 2",
      dataset: "dataset2",
      default: true,
    };

    act(() => {
      setSelectedFacility(facility1);
    });

    expect(useFacilityStore.getState().selectedFacility).toEqual(facility1);

    act(() => {
      setSelectedFacility(facility2);
    });

    expect(useFacilityStore.getState().selectedFacility).toEqual(facility2);
  });

  it("preserves facility data integrity", () => {
    const complexFacility: FacilityMap = {
      id: "complex-facility",
      name: "Complex Facility Name",
      dataset: "complex-dataset",
      default: true,
    };

    const { setSelectedFacility } = useFacilityStore.getState();

    act(() => {
      setSelectedFacility(complexFacility);
    });

    expect(useFacilityStore.getState().selectedFacility).toEqual(
      complexFacility,
    );
  });
});
