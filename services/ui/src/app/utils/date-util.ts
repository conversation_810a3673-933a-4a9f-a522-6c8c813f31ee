import { DateTime } from "luxon";
import * as i18n from "i18next";
import { DatePeriod } from "../types/date-types";
import { DateRange } from "../types/date-types";

/**
 * Utility functions for date operations using Luxon
 */

/**
 * Converts a Date or DateTime object to a DateTime with the specified timezone
 * @param date The date to convert
 * @param timezone The timezone to set (e.g., 'America/New_York')
 * @returns Luxon DateTime object in the specified timezone
 */
export function toSiteDate(
  date: Date | DateTime | string,
  timezone: string,
): DateTime {
  // Parse date strings directly in the site timezone to avoid browser UTC
  // conversions that would shift dates in some timezones.
  if (typeof date === "string") {
    let dt = DateTime.fromISO(date, { zone: timezone });
    if (!dt.isValid) {
      // Try common BigQuery string format: "2025-05-05 04:00:00 UTC"
      dt = DateTime.fromFormat(date, "yyyy-MM-dd HH:mm:ss 'UTC'", {
        zone: "utc",
      }).setZone(timezone);
    }
    if (!dt.isValid) {
      // Try generic SQL timestamp without timezone
      dt = DateTime.fromSQL(date, { zone: timezone });
    }
    return dt;
  }

  return date instanceof DateTime
    ? date.setZone(timezone)
    : DateTime.fromJSDate(date).setZone(timezone);
}

/**
 * Formats a time value into a human-readable string based on the unit
 * // TODO: this will need to be improved
 * @param value The time value to format
 * @param unit The unit of the time value ('seconds', 'minutes', or 'hours')
 * @returns Formatted string (e.g., "45 mins", "2 hrs 5 mins", "1 min 30 secs")
 */
export function formatTimeValue(value: number, unit: string): string {
  const t = i18n.t;
  if (Number.isNaN(value) || value < 0) {
    return t("formatTime.nan", "0 {{unit}}", { value: 0, unit: unit });
    // return `0 ${unit}`;
  }

  // Convert everything to seconds for consistent handling
  let totalSeconds = value;
  if (unit === "minutes") {
    totalSeconds = value * 60;
  } else if (unit === "hours") {
    totalSeconds = value * 3600;
  }

  // Calculate hours, minutes, seconds
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = Math.floor(totalSeconds % 60);

  // Format the output based on the values
  if (hours > 0) {
    if (minutes > 0) {
      return t("formatTime.hasHours", "{{hours}} hrs {{minutes}} mins", {
        hours: hours,
        minutes: minutes,
      });
    } else {
      return t("formatTime.hasHoursNoMinutes", "{{hours}} hrs", {
        hours: hours,
      });
    }
  }

  // return minutes > 0 ? `${hours} hrs ${minutes} mins` : `${hours} hrs`;
  if (minutes > 0) {
    return seconds > 0 && minutes < 10
      ? t("formatTime.hasMinutes", "{{minutes}} mins {{seconds}} seconds", {
          minutes: minutes,
          seconds: seconds,
        })
      : t("formatTime.hasMinutesNoSeconds", "{{minutes}} mins", {
          minutes: minutes,
        });
    // ? `${minutes} mins ${seconds} secs`
    // : `${minutes} mins`;
  }
  return t("formatTime.hasSeconds", "{{seconds}} secs", { seconds: seconds });
  // return `${seconds} secs`;
}

/**
 * Formats a number of minutes into a human-readable string
 * @param minutes The number of minutes to format
 * @returns Formatted string (e.g., "45 mins", "2 hrs 5 mins", "2 hrs")
 */
export function formatMinutes(minutes: number): string {
  return formatTimeValue(minutes, "minutes");
}

/**
 * Formats an ISO string to a specified human readable date string.
 * @param isoString ISO 8601 datetime string
 * @param format format string or Intl.DateTimeFormatOptions
 * @param timezone timezone to use, defaults to browser timezone
 * @returns formatted date string
 */
export function formatISOTime(
  isoString: string,
  format: string | Intl.DateTimeFormatOptions,
  timezone = Intl.DateTimeFormat().resolvedOptions().timeZone,
): string {
  const siteDate = DateTime.fromISO(isoString).setZone(timezone);
  if (!siteDate.isValid) {
    return "Invalid Date";
  }
  if (typeof format === "string") {
    return siteDate.toFormat(format);
  }
  return siteDate.toLocaleString(format);
}

/**
 * Formats the UTC timestamp to a human readable date and time format
 * This example is the preferred ISO standard.
 * example: 2024-06-05T09:00:00.000Z -> 2024-06-05 9:00:00 AM
 */
export function formatISOTimeToReadableDateTime(
  isoString: string,
  timezone?: string,
): string {
  return formatISOTime(isoString, "yyyy-LL-dd h:mm:ss a", timezone);
}

/**
 * Gets the start date for a specific date period
 * @param datePeriod The date period enum value
 * @param timezone The timezone to use for the date period
 * @returns Luxon DateTime object representing the start of the period
 * @throws Error if an invalid date period is provided
 */
export function getStartDateOfPeriod(
  datePeriod: DatePeriod,
  timezone: string,
): DateTime {
  const now = DateTime.now().setZone(timezone);

  switch (datePeriod) {
    case DatePeriod.today:
      return now.startOf("day");
    case DatePeriod.yesterday:
      return now.minus({ days: 1 }).startOf("day");
    case DatePeriod.thisWeek:
      return now.startOf("week");
    case DatePeriod.lastWeek:
      return now.minus({ weeks: 1 }).startOf("week");
    case DatePeriod.thisMonth:
      return now.startOf("month");
    case DatePeriod.lastMonth:
      return now.minus({ months: 1 }).startOf("month");
    case DatePeriod.thisYear:
      return now.startOf("year");
    case DatePeriod.last7days:
      return now.minus({ days: 7 }).startOf("day");
    case DatePeriod.last14days:
      return now.minus({ days: 14 }).startOf("day");
    case DatePeriod.last30days:
      return now.minus({ days: 30 }).startOf("day");
    case DatePeriod.last60days:
      return now.minus({ days: 60 }).startOf("day");
    default:
      throw new Error(`Invalid date period: ${datePeriod}`);
  }
}

/**
 * Gets the end date for a specific date period
 * @param datePeriod The date period enum value
 * @param timezone The timezone to use for the date period
 * @returns Luxon DateTime object representing the end of the period
 * @throws Error if an invalid date period is provided
 */
export function getEndDateOfPeriod(
  datePeriod: DatePeriod,
  timezone: string,
): DateTime {
  const now = DateTime.now().setZone(timezone);

  switch (datePeriod) {
    case DatePeriod.today:
      return now.endOf("day");
    case DatePeriod.yesterday:
      return now.minus({ days: 1 }).endOf("day");
    case DatePeriod.thisWeek:
      return now;
    case DatePeriod.lastWeek:
      return now.minus({ weeks: 1 }).endOf("week");
    case DatePeriod.thisMonth:
      return now;
    case DatePeriod.lastMonth:
      return now.minus({ months: 1 }).endOf("month");
    case DatePeriod.thisYear:
    case DatePeriod.last7days:
    case DatePeriod.last14days:
    case DatePeriod.last30days:
    case DatePeriod.last60days:
      return now;
    default:
      throw new Error(`Invalid date period: ${datePeriod}`);
  }
}

/**
 * Determines the appropriate date format based on the range between dates
 * @param dateRange The date range to analyze
 * @returns DateTimeFormatOptions appropriate for the range:
 *   - TIME_SIMPLE for ranges less than 2 days
 *   - DATE_SHORT for ranges less than 40 days
 *   - Month/day format for longer ranges
 */
export function dateRangeToDateFormat(
  dateRange: DateRange,
): Intl.DateTimeFormatOptions {
  // Calculate the difference in days
  const diffInDays = Math.floor(
    DateTime.fromJSDate(dateRange.endDate).diff(
      DateTime.fromJSDate(dateRange.startDate),
      "days",
    ).days,
  );

  // Return appropriate format based on the date range
  if (diffInDays < 2) {
    // For very short ranges (less than 2 days), use time format
    return DateTime.TIME_SIMPLE;
  }
  if (diffInDays < 40) {
    // For medium ranges (less than 40 days), use short date format
    return DateTime.DATE_SHORT;
  }

  // For longer ranges, use month and day format
  return { month: "short", day: "numeric" };
}
