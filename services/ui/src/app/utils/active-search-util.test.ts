import { describe, it, expect } from "vitest";
import { filterHierarchicalItems } from "./active-search-util";

interface TestItem {
  id: string;
  label: string;
  link?: string;
  children?: TestItem[];
  [key: string]: unknown;
}

describe("filterHierarchicalItems", () => {
  const mockItems: TestItem[] = [
    {
      id: "1",
      label: "Operations Visibility",
      children: [
        {
          id: "1.1",
          label: "Outbound Overview",
          link: "/outbound",
          children: [
            { id: "1.1.1", label: "Daily Report", link: "/daily-report" },
            { id: "1.1.2", label: "Weekly Report", link: "/weekly-report" },
          ],
        },
        {
          id: "1.2",
          label: "Inventory",
          link: "/inventory",
          children: [
            { id: "1.2.1", label: "Inventory List", link: "/inventory-list" },
            { id: "1.2.2", label: "Container List", link: "/container-list" },
          ],
        },
      ],
    },
    {
      id: "2",
      label: "Operational Alerting",
      children: [{ id: "2.1", label: "Configured Alerts", link: "/alerts" }],
    },
    {
      id: "3",
      label: "Dematic Internal",
      children: [
        {
          id: "3.1",
          label: "Examples",
          children: [
            { id: "3.1.1", label: "Options View", link: "/options" },
            { id: "3.1.2", label: "Static View", link: "/static" },
          ],
        },
      ],
    },
  ];

  it("should return empty array when search query is empty", () => {
    const result = filterHierarchicalItems(
      mockItems,
      "",
      (item) => item.children,
      (item) => item.label,
      (item) => item.link !== undefined,
    );
    expect(result).toEqual([]);
  });

  it("should return empty array when no items match", () => {
    const result = filterHierarchicalItems(
      mockItems,
      "nonexistent",
      (item) => item.children,
      (item) => item.label,
      (item) => item.link !== undefined,
    );
    expect(result).toEqual([]);
  });

  it("should return only matching routable items", () => {
    const result = filterHierarchicalItems(
      mockItems,
      "Report",
      (item) => item.children,
      (item) => item.label,
      (item) => item.link !== undefined,
    );
    expect(result).toHaveLength(2);
    expect(result.map((item) => item.label)).toEqual([
      "Daily Report",
      "Weekly Report",
    ]);
  });

  it("should not include non-routable items even if they match", () => {
    const result = filterHierarchicalItems(
      mockItems,
      "Operations",
      (item) => item.children,
      (item) => item.label,
      (item) => item.link !== undefined,
    );
    expect(result).toHaveLength(0);
  });

  it("should handle case-insensitive search", () => {
    const result = filterHierarchicalItems(
      mockItems,
      "REPORT",
      (item) => item.children,
      (item) => item.label,
      (item) => item.link !== undefined,
    );
    expect(result).toHaveLength(2);
    expect(result.map((item) => item.label)).toEqual([
      "Daily Report",
      "Weekly Report",
    ]);
  });

  it("should handle partial matches", () => {
    const result = filterHierarchicalItems(
      mockItems,
      "List",
      (item) => item.children,
      (item) => item.label,
      (item) => item.link !== undefined,
    );
    expect(result).toHaveLength(2);
    expect(result.map((item) => item.label)).toEqual([
      "Inventory List",
      "Container List",
    ]);
  });

  it("should handle items with no children", () => {
    const items: TestItem[] = [
      { id: "1", label: "Single Item", link: "/single" },
      { id: "2", label: "Another Item" },
    ];
    const result = filterHierarchicalItems(
      items,
      "Single",
      (item) => item.children,
      (item) => item.label,
      (item) => item.link !== undefined,
    );
    expect(result).toHaveLength(1);
    expect(result[0].label).toBe("Single Item");
  });

  it("should handle empty input array", () => {
    const emptyItems: TestItem[] = [];
    const result = filterHierarchicalItems(
      emptyItems,
      "test",
      (item) => item.children,
      (item) => item.label,
      (item) => item.link !== undefined,
    );
    expect(result).toEqual([]);
  });
});
