export interface HierarchicalItem<T> {
  children?: T[];
  [key: string]: unknown;
}

/**
 * Generic function to filter hierarchical data based on a search query
 * @param items Array of items to filter
 * @param searchQuery Search query string
 * @param getChildren Function to get children of an item
 * @param getSearchableText Function to get the text to search against
 * @param isRoutable Function to check if an item is routable
 * @returns Filtered array of items maintaining the hierarchy
 */
export function filterHierarchicalItems<T extends HierarchicalItem<T>>(
  items: T[],
  searchQuery: string,
  getChildren: (item: T) => T[] | undefined,
  getSearchableText: (item: T) => string,
  isRoutable: (item: T) => boolean,
): T[] {
  if (!searchQuery) return [];

  const searchLower = searchQuery.toLowerCase();
  const results: T[] = [];

  const searchItems = (items: T[]) => {
    for (const item of items) {
      const text = getSearchableText(item);
      if (text.toLowerCase().includes(searchLower) && isRoutable(item)) {
        const { _children, ...itemWithoutChildren } = item;
        results.push(itemWithoutChildren as T);
      }

      const children = getChildren(item);
      if (children) {
        searchItems(children);
      }
    }
  };

  searchItems(items);
  return results;
}
