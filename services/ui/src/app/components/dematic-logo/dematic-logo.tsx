import React from "react";
import logoDark from "../../../assets/brand/logo-dark.png";
import logoLight from "../../../assets/brand/logo-light.png";
import styles from "./dematic-logo.module.scss";
import { ThemeMode, useTheme } from "../../layout/theme";

interface LogoProps {
  className?: string;
  alt?: string;
}

const Logo: React.FC<LogoProps> = ({ className, alt = "Dematic Logo" }) => {
  const { theme } = useTheme();
  const isDark = theme === ThemeMode.DARK;
  return (
    <img
      src={isDark ? logoDark : logoLight}
      alt={alt}
      className={className || styles.dematicLogo}
      width={150}
      height={54}
      draggable={false}
    />
  );
};

export default Logo;
