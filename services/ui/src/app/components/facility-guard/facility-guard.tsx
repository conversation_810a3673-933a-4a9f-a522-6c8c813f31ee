import { useEffect, useState } from "react";
import { <PERSON>ack, Loading, Tile, <PERSON><PERSON>, But<PERSON> } from "@carbon/react";
import { Logout } from "@carbon/icons-react";
import { useTranslation } from "react-i18next";
import { useFacilityStore } from "../../stores/facility-store";
import {
  updateUserWritableSetting,
  useConfigSetting,
} from "../../config/hooks/use-config";
import { useRoles } from "../../auth/hooks/use-roles";
import { useAuth } from "../../auth/hooks/use-auth";
import type { FacilityMap } from "@ict/sdk/types";
import styles from "./facility-guard.module.css";

interface FacilityGuardProps {
  children: React.ReactNode;
}

/**
 * This component is used to wrap the application and ensure that the user has selected a facility (or at least has
 * access to a facility) before rendering the menu or any pages.  Logic for this guard is:
 *   - If a user has selected a facility and it's persisted to the config setting `selected-facility-id` and it's in the list of available facilities, use that
 *   - If a user has selected a facility but it's not in the list of available facilities, continue to select default (this really shouldn't happen unless the user's rights are revoked)
 *   - If a user has not selected a facility but has access to a facility, auto-select the default facility
 *   - If a user has not selected a facility and there is not default facility, select the first one
 *   - If a user has not selected a facility and has no access to any facilities, show an error
 */
export function FacilityGuard({ children }: FacilityGuardProps) {
  const { t } = useTranslation();
  const { logout } = useAuth();
  const { selectedFacility, setSelectedFacility } = useFacilityStore();
  const { roles, isInternalUser } = useRoles();
  const [hasTriedAutoSelect, setHasTriedAutoSelect] = useState(false);
  const [autoSelectError, setAutoSelectError] = useState<string | null>(null);
  // Get facility maps
  const { setting: facilityMap, isLoading: isLoadingFacilities } =
    useConfigSetting("facility-maps");
  // Get the user's selected facility from preferences
  const { setting: selectedFacilityPreference } = useConfigSetting(
    "selected-facility-id",
  );

  const handleLogout = () => {
    logout({ logoutParams: { returnTo: window.location.origin } });
  };

  // Auto-select facility when data is available
  useEffect(() => {
    console.debug("FacilityGuard - main useEffect inputs:", {
      selectedFacility,
      isLoadingFacilities,
      hasTriedAutoSelect,
      facilityMap: facilityMap?.value,
      roles,
    });
    if (
      !selectedFacility &&
      !isLoadingFacilities &&
      !hasTriedAutoSelect &&
      facilityMap?.value &&
      roles.length > 0
    ) {
      setHasTriedAutoSelect(true);

      // Get available facilities based on user roles
      const availableFacilities = (facilityMap.value as FacilityMap[]).filter(
        (facility) => roles.some((role) => role === facility.id),
      );

      if (availableFacilities.length === 0) {
        console.debug(
          `FacilityGuard - no available facilities for user with roles ${roles} and facilities ${JSON.stringify(facilityMap.value)}`,
        );
        setAutoSelectError(
          t(
            "facilityGuard.noFacilitiesError",
            "You do not have access to any facilities. Please contact your administrator.",
          ),
        );
        return;
      }

      // if a facility is already selected, check if it's valid, then use that
      if (selectedFacilityPreference?.value) {
        console.debug(
          "user persisted facility id selection is",
          selectedFacilityPreference.value,
        );
        // check if the selected facility is in the list of available facilities
        const selectedFacilityMap = availableFacilities.find(
          (facility) => facility.id === selectedFacilityPreference.value,
        );
        if (selectedFacilityMap) {
          // the selected facility is valid, so we can use it
          setSelectedFacility(selectedFacilityMap);
          return;
        } else {
          console.debug(
            "user persisted facility selection is not valid, auto-selecting from the list",
          );
        }
      }
      // the user has no persisted facility selection or it's invalid, so we need to auto-select
      const defaultFacility = availableFacilities.find(
        (facility) => facility.default === true,
      );
      const facilityToSelect = defaultFacility || availableFacilities[0];
      setSelectedFacility(facilityToSelect);
      // update the user's selected facility preference
      updateUserWritableSetting({
        id: selectedFacilityPreference?.id || "",
        name: "selected-facility-id",
        group: selectedFacilityPreference?.group || "user-writable",
        dataType: "string",
        value: facilityToSelect.id,
        source: "user",
      });
    }
  }, [
    selectedFacility,
    isLoadingFacilities,
    hasTriedAutoSelect,
    facilityMap,
    roles,
    setSelectedFacility,
    t,
    selectedFacilityPreference,
  ]);

  // Show loading while store is initializing or facilities are loading
  if (isLoadingFacilities) {
    return (
      <div className={styles.container}>
        <Loading
          description={t(
            "facilityGuard.loadingFacilities",
            "Loading facilities...",
          )}
        />
      </div>
    );
  }

  if (!isInternalUser) {
    // Show error if auto-select failed
    if (autoSelectError) {
      return (
        <div className={styles.container}>
          <div className={styles.content}>
            <Tile className={styles.tile}>
              <Stack gap="2rem">
                <Heading className={styles.heading}>
                  {t("facilityGuard.errorTitle", "Facility Access Error")}
                </Heading>
                <p className={styles.errorDescription}>{autoSelectError}</p>
                <Button
                  kind="tertiary"
                  onClick={handleLogout}
                  renderIcon={Logout}
                >
                  {t("facilityGuard.logout", "Log Out")}
                </Button>
              </Stack>
            </Tile>
          </div>
        </div>
      );
    }

    // Show loading while facility is being set or auto-selected
    if (!selectedFacility && !hasTriedAutoSelect) {
      return (
        <div className={styles.container}>
          <Loading
            data-testid="facility-guard-loading"
            description={t(
              "facilityGuard.settingUpFacility",
              "Setting up facility access...",
            )}
          />
        </div>
      );
    }
  }

  // Render children if facility is selected
  return <>{children}</>;
}
