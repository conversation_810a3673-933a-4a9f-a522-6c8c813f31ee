import { describe, expect, it, vi, beforeEach, afterEach } from "vitest";
import { render, screen, act } from "../../../test-utils";
import { FacilityGuard } from "./facility-guard";
import { useFacilityStore } from "../../stores/facility-store";
import {
  useConfigSetting,
  useFeatureFlag,
} from "../../config/hooks/use-config";
import { useRoles } from "../../auth/hooks/use-roles";
import { useAuth } from "../../auth/hooks/use-auth";
import type { FacilityMap } from "@ict/sdk/types";

// Mock the dependencies
vi.mock("../../stores/facility-store", () => ({
  useFacilityStore: vi.fn(),
}));

vi.mock("../../config/hooks/use-config", () => ({
  useConfigSetting: vi.fn(),
  useFeatureFlag: vi.fn(),
  updateUserWritableSetting: vi.fn(),
}));

vi.mock("../../auth/hooks/use-roles", () => ({
  useRoles: vi.fn(),
}));

vi.mock("../../auth/hooks/use-auth", () => ({
  useAuth: vi.fn(),
}));

vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

describe("FacilityGuard", () => {
  const mockSetSelectedFacility = vi.fn();

  const mockFacilities: FacilityMap[] = [
    {
      id: "facility1",
      name: "Facility 1",
      dataset: "dataset1",
      default: false,
    },
    { id: "facility2", name: "Facility 2", dataset: "dataset2", default: true },
    {
      id: "facility3",
      name: "Facility 3",
      dataset: "dataset3",
      default: false,
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();

    // Default mock implementations
    (useFacilityStore as any).mockReturnValue({
      selectedFacility: null,
      setSelectedFacility: mockSetSelectedFacility,
    });

    (useFeatureFlag as any).mockReturnValue({
      enabled: true,
    });

    // Mock useConfigSetting to return different values based on the setting name
    (useConfigSetting as any).mockImplementation((settingName: string) => {
      if (settingName === "facility-maps") {
        return {
          setting: {
            value: mockFacilities,
          },
          isLoading: false,
        };
      } else if (settingName === "selected-facility-id") {
        return {
          setting: null, // No persisted facility selection by default
        };
      }
      return { setting: null, isLoading: false };
    });

    (useRoles as any).mockReturnValue({
      roles: ["facility1", "facility2"],
    });

    (useAuth as any).mockReturnValue({
      user: {
        organization: {
          id: "org123",
        },
      },
    });

    mockSetSelectedFacility.mockResolvedValue(undefined);
  });

  afterEach(() => {
    vi.runOnlyPendingTimers();
    vi.useRealTimers();
  });

  it("renders children when facility switcher is disabled", () => {
    (useFeatureFlag as any).mockReturnValue({
      enabled: false,
    });

    render(
      <FacilityGuard>
        <div data-testid="child-component">Child Content</div>
      </FacilityGuard>,
    );

    expect(screen.getByTestId("child-component")).toBeInTheDocument();
  });

  it("shows loading when store is initializing", () => {
    // Mock the scenario where no facilities are loaded yet
    (useConfigSetting as any).mockImplementation((settingName: string) => {
      if (settingName === "facility-maps") {
        return {
          setting: null, // No facilities loaded yet
          isLoading: false,
        };
      } else if (settingName === "selected-facility-id") {
        return {
          setting: null,
        };
      }
      return { setting: null, isLoading: false };
    });

    render(
      <FacilityGuard>
        <div data-testid="child-component">Child Content</div>
      </FacilityGuard>,
    );

    expect(
      screen.getByText("Setting up facility access..."),
    ).toBeInTheDocument();
  });

  it("shows loading when facilities are loading", () => {
    (useConfigSetting as any).mockImplementation((settingName: string) => {
      if (settingName === "facility-maps") {
        return {
          setting: null,
          isLoading: true,
        };
      } else if (settingName === "selected-facility-id") {
        return {
          setting: null,
        };
      }
      return { setting: null, isLoading: false };
    });

    render(
      <FacilityGuard>
        <div data-testid="child-component">Child Content</div>
      </FacilityGuard>,
    );

    expect(screen.getByText("Loading facilities...")).toBeInTheDocument();
  });

  it("renders children when facility is selected", async () => {
    (useFacilityStore as any).mockReturnValue({
      selectedFacility: mockFacilities[0],
      setSelectedFacility: mockSetSelectedFacility,
    });

    render(
      <FacilityGuard>
        <div data-testid="child-component">Child Content</div>
      </FacilityGuard>,
    );

    // Advance timers to trigger isInitialized and wait for re-render
    await act(async () => {
      vi.advanceTimersByTime(100);
    });

    expect(screen.getByTestId("child-component")).toBeInTheDocument();
  });

  it("does not render children when loading", () => {
    // Mock the scenario where no facilities are loaded yet
    (useConfigSetting as any).mockImplementation((settingName: string) => {
      if (settingName === "facility-maps") {
        return {
          setting: null, // No facilities loaded yet
          isLoading: false,
        };
      } else if (settingName === "selected-facility-id") {
        return {
          setting: null,
        };
      }
      return { setting: null, isLoading: false };
    });

    render(
      <FacilityGuard>
        <div data-testid="child-component">Child Content</div>
      </FacilityGuard>,
    );

    expect(screen.queryByTestId("child-component")).not.toBeInTheDocument();
    expect(
      screen.getByText("Setting up facility access..."),
    ).toBeInTheDocument();
  });

  it("does not render children when facility is loading", async () => {
    (useFacilityStore as any).mockReturnValue({
      selectedFacility: null,
      setSelectedFacility: mockSetSelectedFacility,
    });

    // Mock no roles initially to prevent auto-selection
    (useRoles as any).mockReturnValue({
      roles: [],
    });

    render(
      <FacilityGuard>
        <div data-testid="child-component">Child Content</div>
      </FacilityGuard>,
    );

    // Should show loading state when no facility is selected and no roles
    expect(screen.queryByTestId("child-component")).not.toBeInTheDocument();
    expect(screen.getByTestId("facility-guard-loading")).toBeInTheDocument();
  });

  it("respects facility loading state from config", () => {
    (useConfigSetting as any).mockImplementation((settingName: string) => {
      if (settingName === "facility-maps") {
        return {
          setting: {
            value: mockFacilities,
          },
          isLoading: true,
        };
      } else if (settingName === "selected-facility-id") {
        return {
          setting: null,
        };
      }
      return { setting: null, isLoading: false };
    });

    render(
      <FacilityGuard>
        <div data-testid="child-component">Child Content</div>
      </FacilityGuard>,
    );

    expect(screen.getByText("Loading facilities...")).toBeInTheDocument();
  });

  it("uses persisted facility selection when valid", () => {
    (useConfigSetting as any).mockImplementation((settingName: string) => {
      if (settingName === "facility-maps") {
        return {
          setting: {
            value: mockFacilities,
          },
          isLoading: false,
        };
      } else if (settingName === "selected-facility-id") {
        return {
          setting: {
            value: "facility2", // Valid persisted facility ID
          },
        };
      }
      return { setting: null, isLoading: false };
    });

    render(
      <FacilityGuard>
        <div data-testid="child-component">Child Content</div>
      </FacilityGuard>,
    );

    expect(mockSetSelectedFacility).toHaveBeenCalledWith(mockFacilities[1]); // facility2
  });

  it("ignores invalid persisted facility selection", () => {
    (useConfigSetting as any).mockImplementation((settingName: string) => {
      if (settingName === "facility-maps") {
        return {
          setting: {
            value: mockFacilities,
          },
          isLoading: false,
        };
      } else if (settingName === "selected-facility-id") {
        return {
          setting: {
            value: "invalid-facility-id", // Invalid persisted facility ID
          },
        };
      }
      return { setting: null, isLoading: false };
    });

    render(
      <FacilityGuard>
        <div data-testid="child-component">Child Content</div>
      </FacilityGuard>,
    );

    // Should auto-select default facility (facility2) instead
    expect(mockSetSelectedFacility).toHaveBeenCalledWith(mockFacilities[1]); // facility2 (default)
  });

  it("renders children for internal users even when there would be errors for external users", () => {
    // Mock as internal user
    (useRoles as any).mockReturnValue({
      roles: [], // No roles - would normally cause error for external users
      isInternalUser: true,
    });

    // Mock no available facilities (would normally show error)
    (useConfigSetting as any).mockImplementation((settingName: string) => {
      if (settingName === "facility-maps") {
        return {
          setting: {
            value: mockFacilities,
          },
          isLoading: false,
        };
      } else if (settingName === "selected-facility-id") {
        return {
          setting: null,
        };
      }
      return { setting: null, isLoading: false };
    });

    // Mock no selected facility
    (useFacilityStore as any).mockReturnValue({
      selectedFacility: null,
      setSelectedFacility: mockSetSelectedFacility,
    });

    render(
      <FacilityGuard>
        <div data-testid="child-component">Child Content</div>
      </FacilityGuard>,
    );

    // Internal users should see children even when external users would see errors
    expect(screen.getByTestId("child-component")).toBeInTheDocument();
    expect(screen.queryByText("Facility Access Error")).not.toBeInTheDocument();
  });
});
