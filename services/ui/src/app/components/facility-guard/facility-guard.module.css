.container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: var(--cds-background);
}

.content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 2rem;
}

.tile {
  max-width: 500px;
  width: 100%;
  padding: 3rem;
  text-align: center;
}

.heading {
  color: var(--cds-text-primary);
  margin-bottom: 1rem;
}

.description {
  color: var(--cds-text-secondary);
  margin-bottom: 2rem;
  font-size: 1rem;
  line-height: 1.5;
}

.errorDescription {
  color: var(--cds-text-error);
  margin-bottom: 2rem;
  font-size: 1rem;
  line-height: 1.5;
  font-weight: 500;
}
