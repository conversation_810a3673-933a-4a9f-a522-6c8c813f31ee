import { render, screen } from "../../../test-utils";
import { LabeledValue } from "./labeled-value";

describe("LabeledValue", () => {
  it("should render the value and label correctly", () => {
    const value = "100";
    const label = "Units";
    render(<LabeledValue value={value} label={label} />);

    expect(screen.getByTestId("labeled-value-Units-value")).toHaveTextContent(
      value,
    );
    expect(screen.getByTestId("labeled-value-Units-label")).toHaveTextContent(
      label,
    );
  });

  it("should handle number values", () => {
    const value = 123;
    const label = "Count";
    render(<LabeledValue value={value} label={label} />);

    expect(screen.getByTestId("labeled-value-Count-value")).toHaveTextContent(
      value.toString(),
    );
    expect(screen.getByTestId("labeled-value-Count-label")).toHaveTextContent(
      label,
    );
  });
});
