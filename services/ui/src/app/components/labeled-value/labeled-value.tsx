import styles from "./labeled-value.module.scss";

export interface LabeledValueProps {
  /**
   * Value to be displayed
   */
  value: number | string;
  /**
   * Font size for the value. Using rem units, defaults to 2rem
   */
  size?: number;
  /**
   * Label for the value
   */
  label: string;
}

export function LabeledValue(props: LabeledValueProps) {
  const { value, label, size: fontSize = "2rem" } = props;
  return (
    <div className={styles.labeledValue} data-testid={`labeled-value-${label}`}>
      <span
        className={styles.value}
        data-testid={`labeled-value-${label}-value`}
        style={{ fontSize: `${fontSize}rem` }}
      >
        {value}
      </span>
      <span
        className={styles.label}
        data-testid={`labeled-value-${label}-label`}
      >
        {label}
      </span>
    </div>
  );
}
