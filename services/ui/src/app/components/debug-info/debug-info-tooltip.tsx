import { Toggletip, ToggletipButton, ToggletipContent } from "@carbon/react";
import { Information } from "@carbon/icons-react";
import { useTableNamesStore } from "../../stores/table-names-store";
import { useRoles } from "../../auth/hooks/use-roles";

export default function DebugInfoTooltip({
  endpointKey,
}: {
  endpointKey: string;
}) {
  const { getTableNames } = useTableNamesStore();
  const tableNames = getTableNames(endpointKey);
  const { hasConfiguratorAccess } = useRoles();

  if (!tableNames || !hasConfiguratorAccess) {
    return null;
  }

  return (
    <>
      <Toggletip align="left">
        <ToggletipButton
          label="Additional information"
          style={{ padding: "5px" }}
        >
          <Information />
        </ToggletipButton>
        <ToggletipContent>
          <div
            style={{
              overflowWrap: "anywhere",
            }}
          >
            <span>
              <strong>Endpoint:</strong> <br />
              {endpointKey}
            </span>
            <br />
            <span>
              <strong>Table names:</strong> <br />
              {tableNames}
            </span>
          </div>
        </ToggletipContent>
      </Toggletip>
    </>
  );
}
