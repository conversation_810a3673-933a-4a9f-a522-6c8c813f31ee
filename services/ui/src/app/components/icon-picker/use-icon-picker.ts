import { useState, useMemo } from "react";
import { AvailableIcon, AvailableIcons } from "../dynamic-icon/dynamic-icon";

export interface UseIconPickerReturn {
  /** Current search term */
  search: string;
  /** Function to update search term */
  setSearch: React.Dispatch<React.SetStateAction<string>>;
  /** Filtered array of available icons */
  icons: AvailableIcon[];
  /** All available icons (unfiltered) */
  allIcons: AvailableIcon[];
}

/**
 * Hook for managing icon picker state and search functionality
 * Similar to the useIconPicker from modall.ca but adapted for Carbon icons
 */
export function useIconPicker(): UseIconPickerReturn {
  const [search, setSearch] = useState("");

  // Memoize the search functionality for performance
  const filteredIcons = useMemo(() => {
    if (search === "") {
      return AvailableIcons;
    }

    return AvailableIcons.filter((icon) => {
      return icon.name.toLowerCase().includes(search.toLowerCase());
    });
  }, [search]);

  return {
    search,
    setSearch,
    icons: filteredIcons,
    allIcons: AvailableIcons,
  };
}
