# Icon Picker Component

A React icon picker component built with Carbon Design System, inspired by the [modall.ca icon picker example](https://modall.ca/lab/shadcn-icon-picker-component). This component provides a searchable, customizable interface for selecting icons from the available Carbon icons.

## Features

- 🔍 **Built-in search functionality** - Filter icons by name
- 🎨 **Carbon Design System** - Consistent with your design system
- 📱 **Responsive design** - Works on all screen sizes
- ⚡ **Performance optimized** - Memoized search and efficient rendering
- 🎯 **TypeScript support** - Fully typed for better developer experience
- 🔧 **Flexible API** - Multiple usage patterns supported

## Components

### IconPicker

The main icon picker component that displays a searchable grid of icons.

```tsx
import { IconPicker } from "@/components/icon-picker";

function MyComponent() {
  const [selectedIcon, setSelectedIcon] = useState<string>("Dashboard");

  return (
    <IconPicker
      selectedIcon={selectedIcon}
      onIconSelect={setSelectedIcon}
      iconSize={24}
      showPreview={true}
      searchPlaceholder="Search for an icon..."
    />
  );
}
```

### IconPickerModal

A modal version that opens the icon picker in a Carbon Modal dialog.

```tsx
import { IconPickerModal } from "@/components/icon-picker";

function MyComponent() {
  const [selectedIcon, setSelectedIcon] = useState<string>();

  return (
    <IconPickerModal
      selectedIcon={selectedIcon}
      onIconSelect={setSelectedIcon}
      placeholder="Choose an icon"
      modalTitle="Select Icon"
      buttonKind="secondary"
    />
  );
}
```

### IconRenderer

A utility component for rendering icons by name.

```tsx
import { IconRenderer } from "@/components/icon-picker";

function MyComponent() {
  return (
    <div>
      <IconRenderer icon="Dashboard" size={24} />
      <IconRenderer icon="Analytics" size={32} />
    </div>
  );
}
```

### useIconPicker Hook

A hook for managing icon picker state and search functionality.

```tsx
import { useIconPicker } from "@/components/icon-picker";

function CustomIconPicker() {
  const { search, setSearch, icons } = useIconPicker();

  return (
    <div>
      <input
        value={search}
        onChange={(e) => setSearch(e.target.value)}
        placeholder="Search icons..."
      />
      <div>
        {icons.map((icon) => (
          <button key={icon.name}>
            <icon.icon size={24} />
            {icon.name}
          </button>
        ))}
      </div>
    </div>
  );
}
```

## Props

### IconPickerProps

| Prop                | Type                         | Default             | Description                          |
| ------------------- | ---------------------------- | ------------------- | ------------------------------------ |
| `selectedIcon`      | `string`                     | -                   | Currently selected icon name         |
| `onIconSelect`      | `(iconName: string) => void` | -                   | Callback when icon selection changes |
| `iconSize`          | `16 \| 20 \| 24 \| 32`       | `24`                | Size of the icons to display         |
| `className`         | `string`                     | `""`                | Optional className for the container |
| `showPreview`       | `boolean`                    | `true`              | Show selected icon preview           |
| `searchPlaceholder` | `string`                     | `"Search icons..."` | Placeholder text for search input    |

### IconPickerModalProps

| Prop           | Type                                                            | Default            | Description                          |
| -------------- | --------------------------------------------------------------- | ------------------ | ------------------------------------ |
| `selectedIcon` | `string`                                                        | -                  | Currently selected icon name         |
| `onIconSelect` | `(iconName: string) => void`                                    | -                  | Callback when icon selection changes |
| `iconSize`     | `16 \| 20 \| 24 \| 32`                                          | `24`               | Size of the icons to display         |
| `placeholder`  | `string`                                                        | `"Select an icon"` | Button text when no icon is selected |
| `selectedText` | `string`                                                        | `"Change icon"`    | Button text when icon is selected    |
| `modalTitle`   | `string`                                                        | `"Select Icon"`    | Modal title                          |
| `buttonSize`   | `"sm" \| "md" \| "lg"`                                          | `"md"`             | Button size                          |
| `buttonKind`   | `"primary" \| "secondary" \| "tertiary" \| "ghost" \| "danger"` | `"secondary"`      | Button kind                          |

### IconRendererProps

| Prop      | Type                   | Default | Description                                    |
| --------- | ---------------------- | ------- | ---------------------------------------------- |
| `icon`    | `string`               | -       | Name of the icon to render                     |
| `size`    | `16 \| 20 \| 24 \| 32` | `24`    | Size of the icon                               |
| `...rest` | `any`                  | -       | Additional props to pass to the icon component |

## Available Icons

The component uses the icons from your existing `AvailableIcons` array in the `dynamic-icon` component. Current available icons include:

- Analytics
- BatteryCharging
- ChartBar
- CloudMonitoring
- ColumnDependency
- Dashboard
- Enterprise
- Flow
- IbmWatsonDiscovery
- Information
- Keyboard
- Notification
- Password
- Rule
- Search
- SearchLocate
- Settings
- Template
- User

## Styling

The component uses SCSS modules with Carbon Design System tokens. You can customize the appearance by:

1. **Overriding CSS custom properties** - Use Carbon design tokens
2. **Adding custom className** - Pass additional styles via the `className` prop
3. **Modifying the SCSS file** - Edit `icon-picker.module.scss` directly

## Examples

### Basic Usage

```tsx
function App() {
  const [icon, setIcon] = useState("Dashboard");

  return (
    <div>
      <h2>Select an Icon</h2>
      <IconPicker selectedIcon={icon} onIconSelect={setIcon} />

      <div style={{ marginTop: "1rem" }}>
        Selected: <IconRenderer icon={icon} size={32} />
      </div>
    </div>
  );
}
```

### Modal Usage

```tsx
function App() {
  const [icon, setIcon] = useState<string>();

  return (
    <div>
      <IconPickerModal
        selectedIcon={icon}
        onIconSelect={setIcon}
        modalTitle="Choose Your Icon"
        placeholder="No icon selected"
      />

      {icon && (
        <div>
          You selected: <IconRenderer icon={icon} size={24} />
        </div>
      )}
    </div>
  );
}
```

## Dependencies

- `@carbon/react` - Carbon Design System React components
- `@carbon/icons-react` - Carbon icons
- `react` - React library

## Inspiration

This component is inspired by the excellent [React Icon Picker example](https://modall.ca/lab/shadcn-icon-picker-component) from Modall, adapted to work with Carbon Design System instead of ShadCN/UI.
