import React from "react";
import { Button, TextInput } from "@carbon/react";
import { Search } from "@carbon/icons-react";
import { useIconPicker } from "./use-icon-picker";
import { IconRenderer } from "./icon-renderer";
import { AvailableIcon } from "../dynamic-icon/dynamic-icon";
import styles from "./icon-picker.module.scss";

export interface IconPickerProps {
  /** Currently selected icon */
  selectedIcon?: string;
  /** Callback when icon selection changes */
  onIconSelect?: (iconName: string) => void;
  /** Size of the icons to display */
  iconSize?: 16 | 20 | 24 | 32;
  /** Optional className for the container */
  className?: string;
  /** Show selected icon preview */
  showPreview?: boolean;
  /** Placeholder text for search input */
  searchPlaceholder?: string;
}

/**
 * Icon Picker component similar to the modall.ca example but using Carbon components
 * Provides a searchable grid of icons with selection functionality
 */
export function IconPicker({
  selectedIcon,
  onIconSelect,
  iconSize = 24,
  className = "",
  showPreview = true,
  searchPlaceholder = "Search icons...",
}: IconPickerProps) {
  const { search, setSearch, icons } = useIconPicker();

  const handleIconClick = (icon: AvailableIcon) => {
    onIconSelect?.(icon.name);
  };

  return (
    <div className={`${styles.iconPicker} ${className}`}>
      {/* Search Input */}
      <div className={styles.searchContainer}>
        <TextInput
          id="icon-search"
          labelText="Search icons"
          placeholder={searchPlaceholder}
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          size="md"
        />

        {/* Selected Icon Preview */}
        {showPreview && selectedIcon && (
          <div className={styles.selectedIconPreview}>
            <IconRenderer icon={selectedIcon} size={iconSize} />
            <span className={styles.selectedIconName}>
              Selected: {selectedIcon}
            </span>
          </div>
        )}
      </div>

      {/* Icon Grid */}
      <div className={styles.iconGrid}>
        {icons.length === 0 ? (
          <div className={styles.noResults}>
            <Search size={32} />
            <p>No icons found for &quot;{search}&quot;</p>
            <p>Try adjusting your search terms</p>
          </div>
        ) : (
          icons.map((icon) => {
            const IconComponent = icon.icon;
            const isSelected = selectedIcon === icon.name;

            return (
              <Button
                key={icon.name}
                kind="ghost"
                className={`${styles.iconButton} ${isSelected ? styles.selected : ""}`}
                onClick={() => handleIconClick(icon)}
                title={icon.name}
              >
                <IconComponent size={iconSize} />
                <span className={styles.iconName}>{icon.name}</span>
              </Button>
            );
          })
        )}
      </div>
    </div>
  );
}
