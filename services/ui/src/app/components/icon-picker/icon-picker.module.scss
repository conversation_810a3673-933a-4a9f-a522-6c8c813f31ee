.iconPicker {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  background: var(--cds-background);
  border: 1px solid var(--cds-border-subtle);
  border-radius: 0.5rem;
  min-height: 300px;
  max-height: 500px;
}

.searchContainer {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.iconGrid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 0.5rem;
  overflow-y: auto;
  flex: 1;
  padding: 0.5rem 0;
}

.iconButton {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.25rem !important;
  padding: 0.75rem 0.5rem !important;
  min-height: 70px !important;
  width: 100% !important;
  border-radius: 0.375rem !important;
  transition: all 0.2s ease !important;

  &:hover {
    background-color: var(--cds-background-hover) !important;
  }

  &.selected {
    background-color: var(--cds-background-selected) !important;
    border-color: var(--cds-border-interactive) !important;
  }
}

.iconName {
  font-size: 0.75rem;
  color: var(--cds-text-secondary);
  text-align: center;
  word-wrap: break-word;
  max-width: 100%;
  line-height: 1.2;
}

.noResults {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem;
  color: var(--cds-text-secondary);
  text-align: center;
}

.selectedIconPreview {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: var(--cds-background-selected);
  border-radius: 0.375rem;
  border: 1px solid var(--cds-border-subtle);
}

.selectedIconName {
  font-weight: 600;
  color: var(--cds-text-primary);
}

/* Modal overlay styles to ensure proper stacking */
:global(.icon-picker-modal-overlay) {
  z-index: 10000 !important;
}

:global(.icon-picker-modal-overlay .cds--modal-container) {
  z-index: 10001 !important;
}

:global(.icon-picker-modal-overlay .cds--modal-overlay) {
  z-index: 9999 !important;
} 