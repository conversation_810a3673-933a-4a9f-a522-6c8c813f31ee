import React, { useState } from "react";
import { createPortal } from "react-dom";
import { Button, Modal } from "@carbon/react";
import { Add } from "@carbon/icons-react";
import { IconPicker } from "./icon-picker";
import { IconRenderer } from "./icon-renderer";

export interface IconPickerModalProps {
  /** Currently selected icon */
  selectedIcon?: string;
  /** Callback when icon selection changes */
  onIconSelect?: (iconName: string) => void;
  /** Size of the icons to display */
  iconSize?: 16 | 20 | 24 | 32;
  /** Button text when no icon is selected */
  placeholder?: string;
  /** Button text when icon is selected */
  selectedText?: string;
  /** Modal title */
  modalTitle?: string;
  /** Button size */
  buttonSize?: "sm" | "md" | "lg";
  /** Button kind */
  buttonKind?: "primary" | "secondary" | "tertiary" | "ghost" | "danger";
}

/**
 * Icon Picker Modal component - similar to the ShadCN Dialog example from modall.ca
 * Opens an icon picker in a modal dialog
 */
export function IconPickerModal({
  selectedIcon,
  onIconSelect,
  iconSize = 24,
  placeholder = "Select an icon",
  selectedText = "Change icon",
  modalTitle = "Select Icon",
  buttonSize = "md",
  buttonKind = "secondary",
}: IconPickerModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [tempSelectedIcon, setTempSelectedIcon] = useState(selectedIcon);

  const handleOpen = () => {
    setTempSelectedIcon(selectedIcon);
    setIsOpen(true);
  };

  const handleClose = () => {
    setIsOpen(false);
    setTempSelectedIcon(selectedIcon); // Reset to original selection
  };

  const handleConfirm = () => {
    if (tempSelectedIcon) {
      onIconSelect?.(tempSelectedIcon);
    }
    setIsOpen(false);
  };

  const handleIconSelect = (iconName: string) => {
    setTempSelectedIcon(iconName);
  };

  return (
    <>
      {/* Trigger Button */}
      <Button
        kind={buttonKind}
        size={buttonSize}
        onClick={handleOpen}
        renderIcon={selectedIcon ? undefined : Add}
      >
        {selectedIcon ? (
          <>
            <IconRenderer icon={selectedIcon} size={16} />
            {selectedText}
          </>
        ) : (
          placeholder
        )}
      </Button>

      {/* Modal - Rendered via Portal to ensure proper stacking */}
      {isOpen &&
        createPortal(
          <Modal
            open={isOpen}
            onRequestClose={handleClose}
            modalHeading={modalTitle}
            primaryButtonText="Select"
            secondaryButtonText="Cancel"
            onRequestSubmit={handleConfirm}
            onSecondarySubmit={handleClose}
            size="md"
            primaryButtonDisabled={!tempSelectedIcon}
            style={{ zIndex: 10000 }}
            className="icon-picker-modal-overlay"
          >
            <IconPicker
              selectedIcon={tempSelectedIcon}
              onIconSelect={handleIconSelect}
              iconSize={iconSize}
              showPreview={true}
            />
          </Modal>,
          document.body,
        )}
    </>
  );
}
