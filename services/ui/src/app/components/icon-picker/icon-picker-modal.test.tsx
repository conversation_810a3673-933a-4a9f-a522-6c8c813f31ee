import { render, screen, userEvent } from "../../../test-utils";
import { IconPickerModal } from "./icon-picker-modal";
import { vi } from "vitest";

// Mock react-dom createPortal
vi.mock("react-dom", () => ({
  createPortal: (node: React.ReactNode) => node,
}));

// Mock Carbon React components
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    Modal: ({
      children,
      open,
      onRequestClose,
      onRequestSubmit,
      onSecondarySubmit,
      modalHeading,
      primaryButtonText,
      secondaryButtonText,
      primaryButtonDisabled,
    }: any) =>
      open ? (
        <div data-testid="modal">
          <div data-testid="modal-heading">{modalHeading}</div>
          <div data-testid="modal-content">{children}</div>
          <button
            data-testid="primary-button"
            onClick={onRequestSubmit}
            disabled={primaryButtonDisabled}
          >
            {primaryButtonText}
          </button>
          <button data-testid="secondary-button" onClick={onSecondarySubmit}>
            {secondaryButtonText}
          </button>
          <button data-testid="close-button" onClick={onRequestClose}>
            Close
          </button>
        </div>
      ) : null,
  };
});

// Mock IconPicker
vi.mock("./icon-picker", () => ({
  IconPicker: ({ selectedIcon, onIconSelect, iconSize, showPreview }: any) => (
    <div data-testid="icon-picker">
      <div data-testid="selected-icon">{selectedIcon || "none"}</div>
      <div data-testid="icon-size">{iconSize}</div>
      <div data-testid="show-preview">{showPreview ? "true" : "false"}</div>
      <button
        data-testid="mock-icon-select"
        onClick={() => onIconSelect("Dashboard")}
      >
        Select Dashboard
      </button>
    </div>
  ),
}));

// Mock IconRenderer
vi.mock("./icon-renderer", () => ({
  IconRenderer: ({ icon, size }: { icon: string; size: number }) => (
    <div data-testid={`icon-renderer-${icon}`} data-size={size}>
      {icon}
    </div>
  ),
}));

describe("IconPickerModal", () => {
  const mockOnIconSelect = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render trigger button with default placeholder when no icon is selected", () => {
    render(<IconPickerModal onIconSelect={mockOnIconSelect} />);

    expect(screen.getByText("Select an icon")).toBeInTheDocument();
  });

  it("should render trigger button with custom placeholder", () => {
    render(
      <IconPickerModal
        onIconSelect={mockOnIconSelect}
        placeholder="Choose icon"
      />,
    );

    expect(screen.getByText("Choose icon")).toBeInTheDocument();
  });

  it("should render trigger button with selected icon and text when icon is selected", () => {
    render(
      <IconPickerModal
        onIconSelect={mockOnIconSelect}
        selectedIcon="Dashboard"
        selectedText="Update icon"
      />,
    );

    expect(screen.getByTestId("icon-renderer-Dashboard")).toBeInTheDocument();
    expect(screen.getByText("Update icon")).toBeInTheDocument();
  });

  it("should render trigger button with default selected text when icon is selected", () => {
    render(
      <IconPickerModal
        onIconSelect={mockOnIconSelect}
        selectedIcon="Dashboard"
      />,
    );

    expect(screen.getByText("Change icon")).toBeInTheDocument();
  });

  it("should open modal when trigger button is clicked", async () => {
    render(<IconPickerModal onIconSelect={mockOnIconSelect} />);

    const triggerButton = screen.getByText("Select an icon");
    await userEvent.click(triggerButton);

    expect(screen.getByTestId("modal")).toBeInTheDocument();
    expect(screen.getByTestId("modal-heading")).toHaveTextContent(
      "Select Icon",
    );
  });

  it("should render modal with custom title", async () => {
    render(
      <IconPickerModal
        onIconSelect={mockOnIconSelect}
        modalTitle="Pick an Icon"
      />,
    );

    const triggerButton = screen.getByText("Select an icon");
    await userEvent.click(triggerButton);

    expect(screen.getByTestId("modal-heading")).toHaveTextContent(
      "Pick an Icon",
    );
  });

  it("should close modal when close button is clicked", async () => {
    render(<IconPickerModal onIconSelect={mockOnIconSelect} />);

    // Open modal
    const triggerButton = screen.getByText("Select an icon");
    await userEvent.click(triggerButton);
    expect(screen.getByTestId("modal")).toBeInTheDocument();

    // Close modal
    const closeButton = screen.getByTestId("close-button");
    await userEvent.click(closeButton);
    expect(screen.queryByTestId("modal")).not.toBeInTheDocument();
  });

  it("should close modal when secondary button is clicked", async () => {
    render(<IconPickerModal onIconSelect={mockOnIconSelect} />);

    // Open modal
    const triggerButton = screen.getByText("Select an icon");
    await userEvent.click(triggerButton);
    expect(screen.getByTestId("modal")).toBeInTheDocument();

    // Click cancel
    const cancelButton = screen.getByTestId("secondary-button");
    await userEvent.click(cancelButton);
    expect(screen.queryByTestId("modal")).not.toBeInTheDocument();
  });

  it("should call onIconSelect and close modal when primary button is clicked with selection", async () => {
    render(<IconPickerModal onIconSelect={mockOnIconSelect} />);

    // Open modal
    const triggerButton = screen.getByText("Select an icon");
    await userEvent.click(triggerButton);

    // Select an icon
    const mockIconSelect = screen.getByTestId("mock-icon-select");
    await userEvent.click(mockIconSelect);

    // Click primary button
    const primaryButton = screen.getByTestId("primary-button");
    await userEvent.click(primaryButton);

    expect(mockOnIconSelect).toHaveBeenCalledWith("Dashboard");
    expect(screen.queryByTestId("modal")).not.toBeInTheDocument();
  });

  it("should have primary button disabled when no icon is selected", async () => {
    render(<IconPickerModal onIconSelect={mockOnIconSelect} />);

    // Open modal
    const triggerButton = screen.getByText("Select an icon");
    await userEvent.click(triggerButton);

    const primaryButton = screen.getByTestId("primary-button");
    expect(primaryButton).toBeDisabled();
  });

  it("should pass correct props to IconPicker", async () => {
    render(
      <IconPickerModal
        onIconSelect={mockOnIconSelect}
        selectedIcon="Settings"
        iconSize={32}
      />,
    );

    // Open modal
    const triggerButton = screen.getByText("Change icon");
    await userEvent.click(triggerButton);

    expect(screen.getByTestId("selected-icon")).toHaveTextContent("Settings");
    expect(screen.getByTestId("icon-size")).toHaveTextContent("32");
    expect(screen.getByTestId("show-preview")).toHaveTextContent("true");
  });

  it("should use default icon size when not specified", async () => {
    render(<IconPickerModal onIconSelect={mockOnIconSelect} />);

    // Open modal
    const triggerButton = screen.getByText("Select an icon");
    await userEvent.click(triggerButton);

    expect(screen.getByTestId("icon-size")).toHaveTextContent("24");
  });

  it("should reset temp selection when modal is closed without confirming", async () => {
    render(
      <IconPickerModal
        onIconSelect={mockOnIconSelect}
        selectedIcon="Settings"
      />,
    );

    // Open modal
    const triggerButton = screen.getByText("Change icon");
    await userEvent.click(triggerButton);

    // Verify initial selection
    expect(screen.getByTestId("selected-icon")).toHaveTextContent("Settings");

    // Select a different icon
    const mockIconSelect = screen.getByTestId("mock-icon-select");
    await userEvent.click(mockIconSelect);

    // Close without confirming
    const cancelButton = screen.getByTestId("secondary-button");
    await userEvent.click(cancelButton);

    // Reopen modal
    await userEvent.click(triggerButton);

    // Should be back to original selection
    expect(screen.getByTestId("selected-icon")).toHaveTextContent("Settings");
  });

  it("should render with custom button styling", () => {
    render(
      <IconPickerModal
        onIconSelect={mockOnIconSelect}
        buttonSize="lg"
        buttonKind="primary"
      />,
    );

    const button = screen.getByText("Select an icon");
    expect(button).toBeInTheDocument();
    // Note: We can't easily test the button props without a more sophisticated mock
  });

  it("should render icon with correct size in button when selected", () => {
    render(
      <IconPickerModal
        onIconSelect={mockOnIconSelect}
        selectedIcon="Dashboard"
      />,
    );

    const iconRenderer = screen.getByTestId("icon-renderer-Dashboard");
    expect(iconRenderer).toHaveAttribute("data-size", "16");
  });
});
