import { render, screen } from "../../../test-utils";
import { IconRenderer } from "./icon-renderer";
import { vi } from "vitest";

// Mock the dynamic-icon module
vi.mock("../dynamic-icon/dynamic-icon", () => ({
  AvailableIcons: [
    {
      name: "Dashboard",
      icon: vi.fn(({ size, ...props }) => (
        <div data-testid="dashboard-icon" data-size={size} {...props}>
          Dashboard Icon
        </div>
      )),
    },
    {
      name: "Settings",
      icon: vi.fn(({ size, ...props }) => (
        <div data-testid="settings-icon" data-size={size} {...props}>
          Settings Icon
        </div>
      )),
    },
  ],
}));

describe("IconRenderer", () => {
  it("should render icon when valid icon name is provided", () => {
    render(<IconRenderer icon="Dashboard" />);

    expect(screen.getByTestId("dashboard-icon")).toBeInTheDocument();
    expect(screen.getByText("Dashboard Icon")).toBeInTheDocument();
  });

  it("should render icon with default size when size is not specified", () => {
    render(<IconRenderer icon="Dashboard" />);

    const iconElement = screen.getByTestId("dashboard-icon");
    expect(iconElement).toHaveAttribute("data-size", "24");
  });

  it("should render icon with custom size when specified", () => {
    render(<IconRenderer icon="Dashboard" size={32} />);

    const iconElement = screen.getByTestId("dashboard-icon");
    expect(iconElement).toHaveAttribute("data-size", "32");
  });

  it("should render different icons based on icon name", () => {
    const { rerender } = render(<IconRenderer icon="Dashboard" />);

    expect(screen.getByTestId("dashboard-icon")).toBeInTheDocument();
    expect(screen.getByText("Dashboard Icon")).toBeInTheDocument();

    rerender(<IconRenderer icon="Settings" />);

    expect(screen.getByTestId("settings-icon")).toBeInTheDocument();
    expect(screen.getByText("Settings Icon")).toBeInTheDocument();
    expect(screen.queryByTestId("dashboard-icon")).not.toBeInTheDocument();
  });

  it("should return null when icon is not found", () => {
    const { container } = render(<IconRenderer icon="NonExistentIcon" />);

    expect(container.firstChild).toBeNull();
  });

  it("should pass additional props to the icon component", () => {
    render(
      <IconRenderer
        icon="Dashboard"
        className="custom-class"
        data-custom="test"
      />,
    );

    const iconElement = screen.getByTestId("dashboard-icon");
    expect(iconElement).toHaveClass("custom-class");
    expect(iconElement).toHaveAttribute("data-custom", "test");
  });

  it("should handle different icon sizes", () => {
    const sizes = [16, 20, 24, 32] as const;

    sizes.forEach((size) => {
      const { rerender } = render(
        <IconRenderer icon="Dashboard" size={size} />,
      );

      const iconElement = screen.getByTestId("dashboard-icon");
      expect(iconElement).toHaveAttribute("data-size", size.toString());

      // Clean up for next iteration
      rerender(<div />);
    });
  });

  it("should not break when icon name is empty string", () => {
    const { container } = render(<IconRenderer icon="" />);

    expect(container.firstChild).toBeNull();
  });

  it("should be case sensitive for icon names", () => {
    const { container } = render(<IconRenderer icon="dashboard" />);

    expect(container.firstChild).toBeNull();
  });
});
