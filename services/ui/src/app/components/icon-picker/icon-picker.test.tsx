import { render, screen, userEvent } from "../../../test-utils";
import { IconPicker } from "./icon-picker";
import { vi } from "vitest";
import { useIconPicker } from "./use-icon-picker";

// Mock the useIconPicker hook
vi.mock("./use-icon-picker", () => ({
  useIconPicker: vi.fn(() => ({
    search: "",
    setSearch: vi.fn(),
    icons: [
      {
        name: "Dashboard",
        icon: vi.fn(() => <div data-testid="dashboard-icon">Dashboard</div>),
      },
      {
        name: "Settings",
        icon: vi.fn(() => <div data-testid="settings-icon">Settings</div>),
      },
    ],
    allIcons: [
      {
        name: "Dashboard",
        icon: vi.fn(() => <div data-testid="dashboard-icon">Dashboard</div>),
      },
      {
        name: "Settings",
        icon: vi.fn(() => <div data-testid="settings-icon">Settings</div>),
      },
    ],
  })),
}));

// Mock IconRenderer
vi.mock("./icon-renderer", () => ({
  IconRenderer: ({ icon, size }: { icon: string; size: number }) => (
    <div data-testid={`icon-renderer-${icon}`} data-size={size}>
      {icon}
    </div>
  ),
}));

describe("IconPicker", () => {
  const mockOnIconSelect = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render search input with default placeholder", () => {
    render(<IconPicker onIconSelect={mockOnIconSelect} />);

    expect(screen.getByPlaceholderText("Search icons...")).toBeInTheDocument();
  });

  it("should render search input with custom placeholder", () => {
    render(
      <IconPicker
        onIconSelect={mockOnIconSelect}
        searchPlaceholder="Find an icon"
      />,
    );

    expect(screen.getByPlaceholderText("Find an icon")).toBeInTheDocument();
  });

  it("should render icon grid with available icons", () => {
    render(<IconPicker onIconSelect={mockOnIconSelect} />);

    expect(screen.getByTestId("dashboard-icon")).toBeInTheDocument();
    expect(screen.getByTestId("settings-icon")).toBeInTheDocument();
    expect(screen.getAllByText("Dashboard")).toHaveLength(2); // Icon mock + label
    expect(screen.getAllByText("Settings")).toHaveLength(2); // Icon mock + label
  });

  it("should call onIconSelect when an icon is clicked", async () => {
    render(<IconPicker onIconSelect={mockOnIconSelect} />);

    const dashboardButton = screen.getByTitle("Dashboard");
    await userEvent.click(dashboardButton);

    expect(mockOnIconSelect).toHaveBeenCalledWith("Dashboard");
  });

  it("should show selected icon preview when showPreview is true and icon is selected", () => {
    render(
      <IconPicker
        onIconSelect={mockOnIconSelect}
        selectedIcon="Dashboard"
        showPreview={true}
      />,
    );

    expect(screen.getByTestId("icon-renderer-Dashboard")).toBeInTheDocument();
    expect(screen.getByText("Selected: Dashboard")).toBeInTheDocument();
  });

  it("should not show selected icon preview when showPreview is false", () => {
    render(
      <IconPicker
        onIconSelect={mockOnIconSelect}
        selectedIcon="Dashboard"
        showPreview={false}
      />,
    );

    expect(
      screen.queryByTestId("icon-renderer-Dashboard"),
    ).not.toBeInTheDocument();
    expect(screen.queryByText("Selected: Dashboard")).not.toBeInTheDocument();
  });

  it("should not show preview when no icon is selected", () => {
    render(<IconPicker onIconSelect={mockOnIconSelect} showPreview={true} />);

    expect(screen.queryByText(/Selected:/)).not.toBeInTheDocument();
  });

  it("should apply selected styling to selected icon", () => {
    render(
      <IconPicker onIconSelect={mockOnIconSelect} selectedIcon="Dashboard" />,
    );

    const dashboardButton = screen.getByTitle("Dashboard");
    expect(dashboardButton).toHaveClass("_selected_da2f7a");
  });

  it("should render with custom className", () => {
    const { container } = render(
      <IconPicker onIconSelect={mockOnIconSelect} className="custom-class" />,
    );

    const iconPickerContainer = container.querySelector("._iconPicker_da2f7a");
    expect(iconPickerContainer).toHaveClass("custom-class");
  });

  it("should render no results message when no icons are found", () => {
    // Temporarily override the mock to return no icons
    const originalMock = vi.mocked(useIconPicker);
    originalMock.mockReturnValueOnce({
      search: "nonexistent",
      setSearch: vi.fn(),
      icons: [],
      allIcons: [],
    });

    render(<IconPicker onIconSelect={mockOnIconSelect} />);

    expect(
      screen.getByText('No icons found for "nonexistent"'),
    ).toBeInTheDocument();
    expect(
      screen.getByText("Try adjusting your search terms"),
    ).toBeInTheDocument();
  });

  it("should update search when typing in search input", async () => {
    const mockSetSearch = vi.fn();
    const originalMock = vi.mocked(useIconPicker);
    originalMock.mockReturnValueOnce({
      search: "",
      setSearch: mockSetSearch,
      icons: [],
      allIcons: [],
    });

    render(<IconPicker onIconSelect={mockOnIconSelect} />);

    const searchInput = screen.getByPlaceholderText("Search icons...");
    await userEvent.type(searchInput, "dashboard");

    expect(mockSetSearch).toHaveBeenCalledWith("d");
    expect(mockSetSearch).toHaveBeenCalledWith("a");
    // And so on for each character
  });

  it("should pass correct icon size to rendered icons", () => {
    render(
      <IconPicker
        onIconSelect={mockOnIconSelect}
        iconSize={32}
        selectedIcon="Dashboard"
        showPreview={true}
      />,
    );

    const iconRenderer = screen.getByTestId("icon-renderer-Dashboard");
    expect(iconRenderer).toHaveAttribute("data-size", "32");
  });

  it("should use default icon size when not specified", () => {
    render(
      <IconPicker
        onIconSelect={mockOnIconSelect}
        selectedIcon="Dashboard"
        showPreview={true}
      />,
    );

    const iconRenderer = screen.getByTestId("icon-renderer-Dashboard");
    expect(iconRenderer).toHaveAttribute("data-size", "24");
  });
});
