import { renderHook, act } from "../../../test-utils";
import { useIconPicker } from "./use-icon-picker";
import { vi } from "vitest";

// Mock the dynamic-icon module
vi.mock("../dynamic-icon/dynamic-icon", () => ({
  AvailableIcons: [
    { name: "Dashboard", icon: vi.fn() },
    { name: "Settings", icon: vi.fn() },
    { name: "User", icon: vi.fn() },
    { name: "Search", icon: vi.fn() },
    { name: "Analytics", icon: vi.fn() },
  ],
}));

describe("useIconPicker", () => {
  it("should initialize with empty search and all icons", () => {
    const { result } = renderHook(() => useIconPicker());

    expect(result.current.search).toBe("");
    expect(result.current.icons).toHaveLength(5);
    expect(result.current.allIcons).toHaveLength(5);
    expect(result.current.icons[0].name).toBe("Dashboard");
  });

  it("should update search term when setSearch is called", () => {
    const { result } = renderHook(() => useIconPicker());

    act(() => {
      result.current.setSearch("dash");
    });

    expect(result.current.search).toBe("dash");
  });

  it("should filter icons based on search term", () => {
    const { result } = renderHook(() => useIconPicker());

    act(() => {
      result.current.setSearch("dash");
    });

    expect(result.current.icons).toHaveLength(1);
    expect(result.current.icons[0].name).toBe("Dashboard");
  });

  it("should filter icons case-insensitively", () => {
    const { result } = renderHook(() => useIconPicker());

    act(() => {
      result.current.setSearch("DASH");
    });

    expect(result.current.icons).toHaveLength(1);
    expect(result.current.icons[0].name).toBe("Dashboard");
  });

  it("should return multiple matching icons", () => {
    const { result } = renderHook(() => useIconPicker());

    act(() => {
      result.current.setSearch("s");
    });

    const matchingIcons = result.current.icons;
    const expectedNames = ["Settings", "User", "Search", "Analytics"];

    // Should include Dashboard, Settings, User, Search, Analytics (all containing 's')
    expect(matchingIcons).toHaveLength(5);
    expectedNames.forEach((name) => {
      expect(matchingIcons.some((icon) => icon.name === name)).toBe(true);
    });
  });

  it("should return empty array when no icons match search", () => {
    const { result } = renderHook(() => useIconPicker());

    act(() => {
      result.current.setSearch("nonexistent");
    });

    expect(result.current.icons).toHaveLength(0);
  });

  it("should return all icons when search is reset to empty string", () => {
    const { result } = renderHook(() => useIconPicker());

    // First, search for something
    act(() => {
      result.current.setSearch("dash");
    });

    expect(result.current.icons).toHaveLength(1);

    // Then reset search
    act(() => {
      result.current.setSearch("");
    });

    expect(result.current.icons).toHaveLength(5);
  });

  it("should handle partial matches", () => {
    const { result } = renderHook(() => useIconPicker());

    act(() => {
      result.current.setSearch("user");
    });

    expect(result.current.icons).toHaveLength(1);
    expect(result.current.icons[0].name).toBe("User");
  });

  it("should maintain allIcons array regardless of search", () => {
    const { result } = renderHook(() => useIconPicker());

    // Initial state
    expect(result.current.allIcons).toHaveLength(5);

    // After search
    act(() => {
      result.current.setSearch("dash");
    });

    expect(result.current.allIcons).toHaveLength(5);
    expect(result.current.icons).toHaveLength(1);
  });

  it("should handle whitespace in search terms", () => {
    const { result } = renderHook(() => useIconPicker());

    act(() => {
      result.current.setSearch("dash");
    });

    expect(result.current.icons).toHaveLength(1);
    expect(result.current.icons[0].name).toBe("Dashboard");
  });

  it("should provide a function to update search", () => {
    const { result } = renderHook(() => useIconPicker());

    expect(typeof result.current.setSearch).toBe("function");
  });
});
