import { AvailableIcons } from "../dynamic-icon/dynamic-icon";

export interface IconRendererProps {
  /** Name of the icon to render */
  icon: string;
  /** Size of the icon (16, 20, 24, 32) */
  size?: 16 | 20 | 24 | 32;
  /** Additional props to pass to the icon component */
  [key: string]: unknown;
}

/**
 * Component for rendering icons by name
 * Similar to the IconRenderer from modall.ca but adapted for Carbon icons
 */
export function IconRenderer({ icon, size = 24, ...rest }: IconRendererProps) {
  // Find the icon component by name
  const iconData = AvailableIcons.find(
    (availableIcon) => availableIcon.name === icon,
  );

  if (!iconData) {
    return null;
  }

  const IconComponent = iconData.icon;

  return <IconComponent size={size} {...rest} />;
}
