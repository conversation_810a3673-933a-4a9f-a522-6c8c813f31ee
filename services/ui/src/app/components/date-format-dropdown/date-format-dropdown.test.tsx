import { vi } from "vitest";
import { fireEvent, render, screen } from "../../../test-utils";
import { DateFormatDropdown, dateFormatOptions } from "./date-format-dropdown";
import { ThemeMode } from "../../layout/theme";

// Mock the Carbon components
vi.mock("@carbon/react", () => {
  return {
    ComboBox: ({
      id,
      titleText,
      items,
      selectedItem,
      onChange,
      itemToString,
    }: {
      id: string;
      titleText: string;
      items: Array<{
        id: string;
        text: string;
        options?: Intl.DateTimeFormatOptions;
      }>;
      selectedItem?: {
        id: string;
        text: string;
        options?: Intl.DateTimeFormatOptions;
      } | null;
      onChange: (arg: {
        selectedItem: {
          id: string;
          text: string;
          options?: Intl.DateTimeFormatOptions;
        } | null;
      }) => void;
      itemToString?: (
        item: {
          id: string;
          text: string;
          options?: Intl.DateTimeFormatOptions;
        } | null,
      ) => string;
    }) => (
      <div data-testid="mock-combo-box">
        <label htmlFor={id}>{titleText}</label>
        <select
          id={id}
          data-testid={id}
          value={selectedItem?.id || ""}
          onChange={(e) => {
            const selectedId = e.target.value;
            const item = items.find((i) => i.id === selectedId);
            onChange({ selectedItem: item || null });
          }}
        >
          {items.map((item) => (
            <option key={item.id} value={item.id}>
              {itemToString ? itemToString(item) : item.text}
            </option>
          ))}
        </select>
      </div>
    ),
    useTheme: vi.fn(() => ({ theme: ThemeMode.LIGHT })),
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

describe("DateFormatDropdown", () => {
  const mockOnChange = vi.fn();

  beforeEach(() => {
    mockOnChange.mockClear();
    vi.clearAllMocks();
  });

  it("renders with default props", () => {
    render(
      <DateFormatDropdown
        id="test-dropdown"
        titleText="Date Format"
        onChange={mockOnChange}
      />,
    );

    expect(screen.getByTestId("test-dropdown")).toBeInTheDocument();
    expect(screen.getByText("Date Format")).toBeInTheDocument();
  });

  it("selects the correct date format when selectedDateFormat is provided", () => {
    render(
      <DateFormatDropdown
        id="test-dropdown"
        titleText="Date Format"
        selectedDateFormat={dateFormatOptions[1].options}
        onChange={mockOnChange}
      />,
    );

    const dropdown = screen.getByTestId("test-dropdown");
    expect(dropdown).toHaveValue(dateFormatOptions[1].id);
  });

  it("defaults to the first option when no date format is selected", () => {
    render(
      <DateFormatDropdown
        id="test-dropdown"
        titleText="Date Format"
        onChange={mockOnChange}
      />,
    );

    const dropdown = screen.getByTestId("test-dropdown");
    expect(dropdown).toHaveValue(dateFormatOptions[0].id);
  });

  it("calls onChange with the selected date format when a format is selected", () => {
    render(
      <DateFormatDropdown
        id="test-dropdown"
        titleText="Date Format"
        onChange={mockOnChange}
      />,
    );

    const dropdown = screen.getByTestId("test-dropdown");
    fireEvent.change(dropdown, { target: { value: dateFormatOptions[2].id } });

    expect(mockOnChange).toHaveBeenCalledWith(dateFormatOptions[2].options);
  });

  it("renders with the correct options", () => {
    render(
      <DateFormatDropdown
        id="test-dropdown"
        titleText="Date Format"
        onChange={mockOnChange}
      />,
    );

    const dropdown = screen.getByTestId("test-dropdown");
    const options = dropdown.querySelectorAll("option");

    expect(options).toHaveLength(dateFormatOptions.length);
    dateFormatOptions.forEach((option, index) => {
      expect(options[index]).toHaveValue(option.id);
      expect(options[index]).toHaveTextContent(option.text);
    });
  });
});
