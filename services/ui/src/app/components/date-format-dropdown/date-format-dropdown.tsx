import { ComboBox } from "@carbon/react";
import { useTheme } from "@carbon/react";
import { GlobalTheme } from "@carbon/react";
import { DateTime } from "luxon";

export const dateFormatOptions: Array<{
  id: string;
  text: string;
  options?: Intl.DateTimeFormatOptions;
}> = [
  {
    // Default by useDateRangeFormatter logic
    id: "auto",
    text: "Auto (use default)",
    options: undefined,
  },
  // Otherwise, use the following options
  {
    id: "DATE_SIMPLE",
    text: "Date Simple",
    options: { month: "2-digit", day: "2-digit" },
  },
  {
    id: "DATE_SHORT",
    text: "Date Short",
    options: DateTime.DATE_SHORT,
  },
  {
    id: "DATE_MED",
    text: "Date Medium",
    options: DateTime.DATE_MED,
  },
];

/**
 * Props for the DateFormatDropdown component
 * @interface DateFormatDropdownProps
 */
interface DateFormatDropdownProps {
  /** Unique identifier for the dropdown */
  id: string;
  /** Text displayed above the dropdown */
  titleText: string;
  /** Currently selected date format */
  selectedDateFormat?: Intl.DateTimeFormatOptions;
  /** Callback function when a date format is selected */
  onChange: (format?: Intl.DateTimeFormatOptions) => void;
}

/**
 * Helper function to compare date format options
 */
const areDateFormatsEqual = (
  format1?: Intl.DateTimeFormatOptions,
  format2?: Intl.DateTimeFormatOptions,
): boolean => {
  if (!format1 && !format2) return true;
  if (!format1 || !format2) return false;

  // Compare specific properties
  const relevantKeys = ["month", "day", "year"] as const;
  return relevantKeys.every((key) => format1[key] === format2[key]);
};

/**
 * A dropdown component for selecting date formats
 * This component allows users to select a date format from predefined options.
 * @param {DateFormatDropdownProps} props - Component props
 * @returns {JSX.Element} The DateFormatDropdown component
 */
export const DateFormatDropdown = ({
  id,
  titleText,
  selectedDateFormat,
  onChange,
}: DateFormatDropdownProps) => {
  const { theme } = useTheme();

  // Find the selected item based on the provided format
  const selectedItem = selectedDateFormat
    ? dateFormatOptions.find(
        (option) =>
          option.options &&
          areDateFormatsEqual(option.options, selectedDateFormat),
      ) || dateFormatOptions[0]
    : dateFormatOptions[0];

  return (
    // Using Carbon Design theme
    <GlobalTheme theme={theme}>
      <ComboBox
        id={id}
        titleText={titleText}
        items={dateFormatOptions}
        selectedItem={selectedItem}
        onChange={({ selectedItem }) => {
          onChange(selectedItem?.options);
        }}
        itemToString={(item) => (item ? item.text : "")}
      />
    </GlobalTheme>
  );
};

export default DateFormatDropdown;
