import { expect } from "vitest";
import { render, screen } from "../../../test-utils";
import SimPlayer from "./sim-player";
import React from "react";

// Mock the CSS module is no longer needed since we've removed all styles

describe("SimPlayer", () => {
  // Simple test that verifies the component renders an iframe
  it("renders an iframe with correct attributes", () => {
    render(<SimPlayer />);

    // Check that the iframe is rendered with the correct attributes
    const iframe = screen.getByTestId("sim-player-iframe");
    expect(iframe).toBeInTheDocument();
    expect(iframe).toHaveAttribute("src", "/sim_player/player.html");
    expect(iframe).toHaveAttribute("title", "Sim Player");
  });

  // Test ref forwarding
  it("forwards ref to the iframe", () => {
    const ref = React.createRef<HTMLIFrameElement>();
    render(<SimPlayer ref={ref} />);

    // Check that the ref is correctly attached to the iframe
    expect(ref.current).not.toBeNull();
    expect(ref.current?.tagName).toBe("IFRAME");
  });
});
