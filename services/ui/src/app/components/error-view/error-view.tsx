import React from "react";
import { But<PERSON>, Heading } from "@carbon/react";
import cx from "classnames";
import styles from "./error-view.module.scss";
import { CarbonIconType } from "@carbon/icons-react";

interface ErrorViewAction {
  label: string;
  onClick?: () => void;
  href?: string;
  icon?: CarbonIconType;
  kind?: "primary" | "secondary" | "danger" | "ghost" | "tertiary";
  disabled?: boolean;
}

interface ErrorViewProps {
  icon: React.ReactNode;
  title: string;
  description: string | React.ReactNode;
  primaryAction?: ErrorViewAction;
  secondaryAction?: ErrorViewAction;
  tertiaryAction?: ErrorViewAction;
  className?: string;
}

const renderAction = (
  action?: ErrorViewAction,
  defaultKind:
    | "primary"
    | "secondary"
    | "danger"
    | "ghost"
    | "tertiary"
    | "danger--primary"
    | "danger--ghost"
    | "danger--tertiary" = "primary",
) => {
  if (!action) return null;
  return (
    <Button
      kind={action.kind || defaultKind}
      onClick={action.onClick}
      renderIcon={action.icon}
      href={action.href}
      target={
        action.href && action.href.startsWith("http") ? "_blank" : undefined
      }
      rel={
        action.href && action.href.startsWith("http")
          ? "noopener noreferrer"
          : undefined
      }
      disabled={action.disabled}
    >
      {action.label}
    </Button>
  );
};

export const ErrorView: React.FC<ErrorViewProps> = ({
  icon,
  title,
  description,
  primaryAction,
  secondaryAction,
  tertiaryAction,
  className,
}) => {
  return (
    <div className={cx(styles.errorViewContainer, className)}>
      <div className={styles.errorViewCard}>
        {icon && <div className={styles.errorViewIcon}>{icon}</div>}
        <Heading className={styles.errorViewTitle}>{title}</Heading>
        <div className={styles.errorViewDescription}>{description}</div>
        {(primaryAction || secondaryAction) && (
          <div className={styles.errorViewActions}>
            {renderAction(primaryAction, "primary")}
            {renderAction(secondaryAction, "tertiary")}
          </div>
        )}
      </div>
      {tertiaryAction && (
        <div className={styles.errorViewTertiary}>
          {renderAction(tertiaryAction, "ghost")}
        </div>
      )}
    </div>
  );
};
