@use "@carbon/react/scss/theme" as *;
@use "@carbon/react/scss/spacing" as *;
@use "@carbon/react/scss/type" as *;

.errorViewContainer {
  height: calc(100vh - 3rem);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 32px;
}

.errorViewCard {
  border: 1px solid $border-subtle-01;
  padding: $spacing-07;
  min-width: 350px;
  max-width: 480px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  background-color: $layer-01;
}

.errorViewIcon {
  width: 100%;
  color: $text-helper;
  & > svg {
    width: 48px;
    height: 48px;
    display: block;
  }
}

.errorViewTitle {
  margin: 0;
  font-weight: 600;
  width: 100%;
}

.errorViewDescription {
  margin: 0;
  color: $text-secondary;
}

.errorViewActions {
  display: flex;
  gap: 16px;
}

.errorViewTertiary {
  color: $link-primary
} 