import { Star, StarFilled } from "@carbon/icons-react";
import { Button } from "@carbon/react";
import { useFavorites } from "../../config/hooks/use-favorites";
import { logger } from "../../utils/logger";

interface FavoriteButtonProps {
  menuItemId?: string;
}

// A button component that allows users to favorite/unfavorite menu items
export function FavoriteButton({ menuItemId }: FavoriteButtonProps) {
  const { isFavorite, addFavorite, removeFavorite, isLoading } = useFavorites();

  const handleStarClick = async () => {
    if (!menuItemId || isLoading) {
      return;
    }

    try {
      if (isFavorite(menuItemId)) {
        // Remove from favorites
        await removeFavorite(menuItemId);
      } else {
        // Add to favorites
        await addFavorite(menuItemId);
      }
    } catch (error) {
      logger.error("Failed to update favorites", { error, menuItemId });
    }
  };

  const isFavorited = menuItemId ? isFavorite(menuItemId) : false;

  return (
    <Button
      kind="ghost"
      size="sm"
      onClick={handleStarClick}
      renderIcon={isFavorited ? StarFilled : Star}
      tooltipPosition="bottom"
      iconDescription={
        isFavorited ? "Remove From Favorites" : "Add to Favorites"
      }
      disabled={isLoading || !menuItemId}
      hasIconOnly
      data-testid="favorite-button"
    />
  );
}
