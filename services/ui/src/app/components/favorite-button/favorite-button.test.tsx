import { render, screen, userEvent } from "../../../test-utils";
import { FavoriteButton } from "./favorite-button";
import { useFavorites } from "../../config/hooks/use-favorites";
import { vi } from "vitest";

// Mock the useFavorites hook
vi.mock("../../config/hooks/use-favorites", () => ({
  useFavorites: vi.fn(),
}));

// Mock Carbon icons
vi.mock("@carbon/icons-react", () => ({
  Star: () => <div data-testid="star-icon" />,
  StarFilled: () => <div data-testid="star-filled-icon" />,
}));

describe("FavoriteButton", () => {
  const mockIsFavorite = vi.fn();
  const mockAddFavorite = vi.fn();
  const mockRemoveFavorite = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (useFavorites as ReturnType<typeof vi.fn>).mockReturnValue({
      isFavorite: mockIsFavorite,
      addFavorite: mockAddFavorite,
      removeFavorite: mockRemoveFavorite,
      isLoading: false,
      favorites: ["menu-item-1", "menu-item-2"],
    });
    mockIsFavorite.mockImplementation((id) => id === "menu-item-1");
  });

  it("should render with unfilled star when not favorited", () => {
    render(<FavoriteButton menuItemId="menu-item-3" />);

    expect(screen.getByRole("button")).toBeInTheDocument();
    expect(screen.getByTestId("star-icon")).toBeInTheDocument();
    expect(screen.queryByTestId("star-filled-icon")).not.toBeInTheDocument();
  });

  it("should render with filled star when favorited", () => {
    render(<FavoriteButton menuItemId="menu-item-1" />);

    expect(screen.getByRole("button")).toBeInTheDocument();
    expect(screen.getByTestId("star-filled-icon")).toBeInTheDocument();
    expect(screen.queryByTestId("star-icon")).not.toBeInTheDocument();
  });

  it("should call addFavorite when clicked and not favorited", async () => {
    render(<FavoriteButton menuItemId="menu-item-3" />);

    const button = screen.getByRole("button");
    await userEvent.click(button);

    expect(mockAddFavorite).toHaveBeenCalledWith("menu-item-3");
    expect(mockRemoveFavorite).not.toHaveBeenCalled();
  });

  it("should call removeFavorite when clicked and favorited", async () => {
    render(<FavoriteButton menuItemId="menu-item-1" />);

    const button = screen.getByRole("button");
    await userEvent.click(button);

    expect(mockRemoveFavorite).toHaveBeenCalledWith("menu-item-1");
    expect(mockAddFavorite).not.toHaveBeenCalled();
  });

  it("should be disabled when isLoading is true", () => {
    (useFavorites as ReturnType<typeof vi.fn>).mockReturnValue({
      isFavorite: mockIsFavorite,
      addFavorite: mockAddFavorite,
      removeFavorite: mockRemoveFavorite,
      isLoading: true,
      favorites: ["menu-item-1", "menu-item-2"],
    });

    render(<FavoriteButton menuItemId="menu-item-3" />);

    expect(screen.getByRole("button")).toBeDisabled();
  });

  it("should be disabled when menuItemId is not provided", () => {
    render(<FavoriteButton />);

    expect(screen.getByRole("button")).toBeDisabled();
  });
});
