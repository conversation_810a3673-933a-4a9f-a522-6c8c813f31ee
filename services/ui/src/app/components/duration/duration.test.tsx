import { render, screen } from "../../../test-utils";
import { Duration } from "./duration";

describe("Duration", () => {
  it("should render the default duration correctly", () => {
    render(<Duration duration={3760} />);
    expect(screen.getByTestId("labeled-value-Hour-value")).toHaveTextContent(
      "1",
    );
    expect(screen.getByTestId("labeled-value-Minutes-value")).toHaveTextContent(
      "2",
    );
    expect(
      screen.queryByTestId("labeled-value-Seconds-value"),
    ).not.toBeInTheDocument();
  });
  it("should display only hours when specified", () => {
    render(<Duration duration={3600} display={{ hours: true }} />);
    expect(screen.getByTestId("labeled-value-Hour-value")).toHaveTextContent(
      "1",
    );
    expect(
      screen.queryByTestId("labeled-value-Minutes-value"),
    ).not.toBeInTheDocument();
    expect(
      screen.queryByTestId("labeled-value-Seconds-value"),
    ).not.toBeInTheDocument();
  });

  it("should display only minutes when specified", () => {
    render(<Duration duration={120} display={{ minutes: true }} />);
    expect(
      screen.queryByTestId("labeled-value-Hour-value"),
    ).not.toBeInTheDocument();
    expect(screen.getByTestId("labeled-value-Minutes-value")).toHaveTextContent(
      "2",
    );
    expect(
      screen.queryByTestId("labeled-value-Seconds-value"),
    ).not.toBeInTheDocument();
  });

  it("should display only seconds when specified", () => {
    render(<Duration duration={30} display={{ seconds: true }} />);
    expect(
      screen.queryByTestId("labeled-value-Hour-value"),
    ).not.toBeInTheDocument();
    expect(
      screen.queryByTestId("labeled-value-Minutes-value"),
    ).not.toBeInTheDocument();
    expect(screen.getByTestId("labeled-value-Seconds-value")).toHaveTextContent(
      "30",
    );
  });

  it("should handle taking in minutes duration type", () => {
    render(<Duration duration={62} type="minutes" />);
    expect(screen.getByTestId("labeled-value-Hour-value")).toHaveTextContent(
      "1",
    );
    expect(screen.getByTestId("labeled-value-Minutes-value")).toHaveTextContent(
      "2",
    );
  });

  it("should handle taking in hours duration type", () => {
    render(<Duration duration={2} type="hours" />);
    expect(screen.getByTestId("labeled-value-Hours-value")).toHaveTextContent(
      "2",
    );
    expect(screen.getByTestId("labeled-value-Minutes-value")).toHaveTextContent(
      "0",
    );
  });

  it("should handle taking in seconds duration type", () => {
    render(
      <Duration duration={30} type="seconds" display={{ seconds: true }} />,
    );
    expect(screen.getByTestId("labeled-value-Seconds-value")).toHaveTextContent(
      "30",
    );
  });

  it("should handle decimal numbers", () => {
    render(<Duration duration={1.5} type="hours" />);
    expect(screen.getByTestId("labeled-value-Hour-value")).toHaveTextContent(
      "1",
    );
    expect(screen.getByTestId("labeled-value-Minutes-value")).toHaveTextContent(
      "30",
    );
  });

  it("should handle NaN duration", () => {
    render(<Duration duration={NaN} display={{ seconds: true }} />);
    expect(screen.getByTestId("labeled-value-Seconds-value")).toHaveTextContent(
      "0",
    );
  });

  it("should handle zero duration", () => {
    render(<Duration duration={0} />);
    expect(screen.getByTestId("labeled-value-Hours-value")).toHaveTextContent(
      "0",
    );
    expect(screen.getByTestId("labeled-value-Minutes-value")).toHaveTextContent(
      "0",
    );
  });

  it("should handle negative duration", () => {
    render(<Duration duration={-5} display={{ seconds: true }} />);
    expect(screen.getByTestId("labeled-value-Seconds-value")).toHaveTextContent(
      "0",
    );
  });
});
