import { LabeledValue } from "../labeled-value/labeled-value";
import { useTranslation } from "react-i18next";
import styles from "./duration.module.scss";

export interface DurationProps {
  /** Time duration */
  duration: number;
  /** Display options */
  display?: {
    hours?: boolean;
    minutes?: boolean;
    seconds?: boolean;
  };
  /** Time duration type */
  type?: "hours" | "minutes" | "seconds";
}

export function Duration(props: DurationProps) {
  const {
    duration,
    display = { hours: true, minutes: true, seconds: false },
    type = "seconds",
  } = props;
  const parts = splitDuration(duration, type);
  const { t } = useTranslation();

  const { hours, minutes, seconds } = parts;
  const hoursLabel =
    hours === 1
      ? t("duration.hours.singular", "Hour")
      : t("duration.hours.plural", "Hours");
  const minutesLabel =
    minutes === 1
      ? t("duration.minutes.singular", "Minute")
      : t("duration.minutes.plural", "Minutes");
  const secondsLabel =
    seconds === 1
      ? t("duration.seconds.singular", "Second")
      : t("duration.seconds.plural", "Seconds");

  return (
    <div className={styles.duration}>
      {display.hours && <LabeledValue value={hours} label={hoursLabel} />}
      {display.minutes && <LabeledValue value={minutes} label={minutesLabel} />}
      {display.seconds && <LabeledValue value={seconds} label={secondsLabel} />}
    </div>
  );
}

/**
 * Splits a duration into its component parts (hours, minutes, seconds).
 * The input duration is first converted to seconds based on the `type`
 * provided, and then split into a full h/m/s structure.
 * @param duration The duration value.
 * @param type The unit of the input duration ('hours', 'minutes', or 'seconds').
 * @returns An object with hours, minutes, and seconds components.
 */
function splitDuration(
  duration: number,
  type: "hours" | "minutes" | "seconds" = "seconds",
): { hours: number; minutes: number; seconds: number } {
  if (Number.isNaN(duration) || duration < 0) {
    return { hours: 0, minutes: 0, seconds: 0 };
  }

  let totalSeconds = 0;
  switch (type) {
    case "hours":
      totalSeconds = duration * 3600;
      break;
    case "minutes":
      totalSeconds = duration * 60;
      break;
    case "seconds":
      totalSeconds = duration;
      break;
  }

  const flooredTotalSeconds = Math.floor(totalSeconds);
  const hours = Math.floor(flooredTotalSeconds / 3600);
  const minutes = Math.floor((flooredTotalSeconds % 3600) / 60);
  const seconds = flooredTotalSeconds % 60;

  return { hours, minutes, seconds };
}
