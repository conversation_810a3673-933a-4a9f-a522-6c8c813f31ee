import { render, screen } from "../../../test-utils";
import { LoadingApp } from "./loading-app";
import styles from "./loading-app.module.scss";

describe("LoadingApp", () => {
  it("should render the loading image", () => {
    render(<LoadingApp />);

    const loadingImage = screen.getByAltText("Loading");
    expect(loadingImage).toBeInTheDocument();
    expect(loadingImage).toHaveAttribute(
      "src",
      expect.stringContaining("loading-warehouse.gif"),
    );
  });

  it("should apply the correct container class", () => {
    const { container } = render(<LoadingApp />);

    const loadingContainer = container.firstChild;
    expect(loadingContainer).toHaveClass(styles.loadingContainer);
  });

  it("should apply the correct image class", () => {
    render(<LoadingApp />);

    const loadingImage = screen.getByAltText("Loading");
    expect(loadingImage).toHaveClass(styles.loadingImage);
  });
});
