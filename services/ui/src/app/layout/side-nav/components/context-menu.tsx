import React, { useRef } from "react";
import { Menu, MenuItem, useContextMenu, MenuItemDivider } from "@carbon/react";
import type { MenuItemProps } from "./menu-item";
import { iconLookup } from "../../../components/dynamic-icon/dynamic-icon";
import styles from "./nested-menu.module.scss";
import contextMenuStyles from "./context-menu.module.scss";
import { useNotification } from "../../../components/toast/use-notification";

interface MenuItemWithContextMenuProps extends MenuItemProps {
  isFavorite: boolean;
  onAddFavorite?: (id: string) => void;
  onRemoveFavorite?: (id: string) => void;
}

// Helper function for link validation
function isValidLink(link: string | undefined): link is string {
  return !!link && link !== "/ict-page-not-implemented";
}

export const MenuItemWithContextMenu = ({
  isFavorite,
  onAddFavorite,
  onRemoveFavorite,
  ...rest
}: MenuItemWithContextMenuProps) => {
  const { item, isPathActive, onNavigate, depth = 0, hideIcon = false } = rest;
  // Reference to the menu item element for positioning the context menu
  const itemRef = useRef<HTMLDivElement>(null);
  // Hook from Carbon to handle context menu positioning and behavior
  const menuProps = useContextMenu(itemRef as React.RefObject<Element>);

  const itemPath = item.link || "/ict-page-not-implemented";
  const isItemActive = isPathActive(itemPath);
  const { error } = useNotification();

  const handleClick = () => {
    if (itemPath !== "/ict-page-not-implemented") {
      onNavigate(itemPath);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      handleClick();
    }
  };

  // Opens the item's link in a new browser tab
  const handleOpenInNewTab = (
    event: React.MouseEvent<HTMLLIElement> | React.KeyboardEvent<HTMLLIElement>,
  ) => {
    event.preventDefault();
    if (isValidLink(item.link)) {
      window.open(item.link, "_blank");
    }
  };

  // Opens the item's link in a new browser window
  const handleOpenInNewWindow = (
    event: React.MouseEvent<HTMLLIElement> | React.KeyboardEvent<HTMLLIElement>,
  ) => {
    event.preventDefault();

    const widthSize = Math.round(window.innerWidth * 1.0);
    const heightSize = Math.round(window.innerHeight * 1.0);

    if (isValidLink(item.link)) {
      window.open(
        item.link,
        "_blank",
        `noopener,noreferrer, width=${widthSize}, height=${heightSize}`,
      );
    }
  };

  // Copies the item's full URL to the clipboard
  const handleCopyLink = async (
    event: React.MouseEvent<HTMLLIElement> | React.KeyboardEvent<HTMLLIElement>,
  ) => {
    event.preventDefault();
    if (isValidLink(item.link)) {
      // For relative paths, prepend the current origin
      const fullUrl = item.link.startsWith("http")
        ? item.link
        : window.location.origin + item.link;
      try {
        await navigator.clipboard.writeText(fullUrl);
      } catch (err) {
        console.error("Failed to copy link to clipboard:", err);
        error("Failed to copy link to clipboard");
      }
    }
  };

  // Adds the current item to favorites
  const handleAddFavorite = (
    event: React.MouseEvent<HTMLLIElement> | React.KeyboardEvent<HTMLLIElement>,
  ) => {
    event.preventDefault();
    if (onAddFavorite) {
      onAddFavorite(item.id);
    }
  };

  // Removes the current item from favorites
  const handleRemoveFavorite = (
    event: React.MouseEvent<HTMLLIElement> | React.KeyboardEvent<HTMLLIElement>,
  ) => {
    event.preventDefault();
    if (onRemoveFavorite) {
      onRemoveFavorite(item.id);
    }
  };

  return (
    <div
      ref={itemRef}
      style={{ width: "100%" }}
      data-testid={item.id}
      className={styles.nestedMenu}
    >
      <button
        type="button"
        className={`${styles.menuItem} ${isItemActive ? styles.active : ""} ${styles[`depth${depth}`]}`}
        onClick={handleClick}
        onKeyDown={handleKeyDown}
      >
        <div className={styles.menuContent}>
          {item.icon && !(item.hideIcon ?? hideIcon) && (
            <div className={styles.icon}>{iconLookup(item.icon)}</div>
          )}
          <span className={styles.label}>{item.label}</span>
        </div>
      </button>
      {/* Context menu that appears on right-click */}
      <Menu
        {...menuProps}
        label="Context menu"
        className={contextMenuStyles.contextMenu}
      >
        {/* Navigation options */}
        <MenuItem label="Open in new tab" onClick={handleOpenInNewTab} />
        <MenuItem label="Open in new window" onClick={handleOpenInNewWindow} />
        <MenuItemDivider />
        {/* Link management */}
        <MenuItem label="Copy link address" onClick={handleCopyLink} />
        <MenuItemDivider />
        {/* Favorite management - conditionally show add/remove based on current state */}
        {isFavorite ? (
          <MenuItem
            label="Remove from favorites"
            onClick={handleRemoveFavorite}
          />
        ) : (
          <MenuItem label="Add to favorites" onClick={handleAddFavorite} />
        )}
      </Menu>
    </div>
  );
};
