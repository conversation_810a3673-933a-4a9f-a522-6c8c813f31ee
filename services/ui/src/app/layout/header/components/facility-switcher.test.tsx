import { describe, expect, it, vi, beforeEach } from "vitest";
import { render, screen, fireEvent, waitFor } from "../../../../test-utils";
import { FacilitySwitcher } from "./facility-switcher";
import { useFacilityStore } from "../../../stores/facility-store";
import {
  useConfigSetting,
  useUserPreference,
  updateUserWritableSetting,
} from "../../../config/hooks/use-config";
import { useRoles } from "../../../auth/hooks/use-roles";
import type { FacilityMap } from "@ict/sdk/types";

// Mock the dependencies
vi.mock("../../../stores/facility-store", () => ({
  useFacilityStore: vi.fn(),
}));

vi.mock("../../../config/hooks/use-config", () => ({
  useConfigSetting: vi.fn(),
  useUserPreference: vi.fn(),
  updateUserWritableSetting: vi.fn(),
}));

vi.mock("../../../auth/hooks/use-roles", () => ({
  useRoles: vi.fn(),
}));

vi.mock("../../../components/toast/use-notification", () => ({
  useNotification: vi.fn(() => ({
    error: vi.fn(),
  })),
}));

vi.mock("../../../api/ict-api", () => ({
  ictApi: {
    queryClient: {
      invalidateQueries: vi.fn(),
    },
  },
}));

const mockUseNavigate = vi.fn();
vi.mock("react-router", () => ({
  useNavigate: () => mockUseNavigate,
}));

vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    Dropdown: ({
      items,
      selectedItem,
      onChange,
      itemToString,
      disabled,
      titleText,
    }: any) => (
      <div data-testid="facility-dropdown">
        <label>{titleText}</label>
        <select
          data-testid="facility-select"
          disabled={disabled}
          value={selectedItem?.id || ""}
          onChange={(e) => {
            const selected = items.find(
              (item: any) => item.id === e.target.value,
            );
            onChange({ selectedItem: selected });
          }}
        >
          <option value="">Select Facility</option>
          {items.map((item: any) => (
            <option key={item.id} value={item.id}>
              {itemToString(item)}
            </option>
          ))}
        </select>
      </div>
    ),
    Button: ({ children, onClick, renderIcon: RenderIcon, ...props }: any) => (
      <button onClick={onClick} {...props}>
        {RenderIcon && <RenderIcon />}
        {children}
      </button>
    ),
    Stack: ({ children }: any) => <div data-testid="stack">{children}</div>,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

vi.mock("@carbon/icons-react", () => ({
  WarningFilled: () => <div data-testid="warning-icon">⚠️</div>,
}));

describe("FacilitySwitcher", () => {
  const mockSetSelectedFacility = vi.fn();

  const mockFacilities: FacilityMap[] = [
    {
      id: "facility1",
      name: "Facility 1",
      dataset: "dataset1",
      default: false,
    },
    { id: "facility2", name: "Facility 2", dataset: "dataset2", default: true },
    {
      id: "facility3",
      name: "Facility 3",
      dataset: "dataset3",
      default: false,
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();

    // Default mock implementations
    (useFacilityStore as any).mockReturnValue({
      selectedFacility: null,
      setSelectedFacility: mockSetSelectedFacility,
    });

    (useConfigSetting as any).mockReturnValue({
      setting: {
        value: mockFacilities,
      },
      isLoading: false,
    });

    (useUserPreference as any).mockReturnValue({
      preference: null,
    });

    (useRoles as any).mockReturnValue({
      roles: ["facility1", "facility2", "facility3"],
    });

    // Mock the setState method for useFacilityStore
    (useFacilityStore as any).setState = vi.fn();
  });

  it("renders dropdown when multiple facilities are available", () => {
    render(<FacilitySwitcher />);

    expect(screen.getByTestId("facility-dropdown")).toBeInTheDocument();
    expect(screen.getByText("Facility")).toBeInTheDocument();
    expect(screen.getByTestId("facility-select")).toBeInTheDocument();
  });

  it("renders selected facility name as span when only one facility is available", () => {
    (useRoles as any).mockReturnValue({
      roles: ["facility1"],
    });

    (useFacilityStore as any).mockReturnValue({
      selectedFacility: mockFacilities[0],
      setSelectedFacility: mockSetSelectedFacility,
    });

    render(<FacilitySwitcher />);

    expect(screen.getByText("Facility 1")).toBeInTheDocument();
    expect(screen.queryByTestId("facility-dropdown")).not.toBeInTheDocument();
  });

  it("filters facilities based on user roles", () => {
    (useRoles as any).mockReturnValue({
      roles: ["facility1", "facility2"], // User only has access to 2 facilities
    });

    render(<FacilitySwitcher />);

    const select = screen.getByTestId("facility-select");
    const options = select.querySelectorAll("option");

    // Should have 3 options: default empty option + 2 facilities
    expect(options).toHaveLength(3);
    expect(options[1]).toHaveTextContent("Facility 1");
    expect(options[2]).toHaveTextContent("Facility 2");
  });

  it("sorts facilities alphabetically", () => {
    const unsortedFacilities: FacilityMap[] = [
      {
        id: "facility3",
        name: "Zebra Facility",
        dataset: "dataset3",
        default: false,
      },
      {
        id: "facility1",
        name: "Alpha Facility",
        dataset: "dataset1",
        default: false,
      },
      {
        id: "facility2",
        name: "Beta Facility",
        dataset: "dataset2",
        default: false,
      },
    ];

    (useConfigSetting as any).mockReturnValue({
      setting: {
        value: unsortedFacilities,
      },
    });

    render(<FacilitySwitcher />);

    const select = screen.getByTestId("facility-select");
    const options = select.querySelectorAll("option");

    expect(options[1]).toHaveTextContent("Alpha Facility");
    expect(options[2]).toHaveTextContent("Beta Facility");
    expect(options[3]).toHaveTextContent("Zebra Facility");
  });

  it("calls setSelectedFacility when facility is changed", async () => {
    mockSetSelectedFacility.mockResolvedValue(undefined);

    render(<FacilitySwitcher />);

    const select = screen.getByTestId("facility-select");
    fireEvent.change(select, { target: { value: "facility2" } });

    await waitFor(() => {
      expect(mockSetSelectedFacility).toHaveBeenCalledWith({
        id: "facility2",
        name: "Facility 2",
        dataset: "dataset2",
        default: true,
      });
    });
  });

  it("handles setSelectedFacility errors gracefully", async () => {
    (updateUserWritableSetting as any).mockRejectedValue(
      new Error("Update failed"),
    );
    const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

    render(<FacilitySwitcher />);

    const select = screen.getByTestId("facility-select");
    fireEvent.change(select, { target: { value: "facility2" } });

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to update selected facility:",
        expect.any(Error),
      );
    });

    consoleSpy.mockRestore();
  });

  it("displays selected facility in dropdown", () => {
    (useFacilityStore as any).mockReturnValue({
      selectedFacility: mockFacilities[1],
      setSelectedFacility: mockSetSelectedFacility,
    });

    render(<FacilitySwitcher />);

    const select = screen.getByTestId("facility-select");
    expect(select).toHaveValue("facility2");
  });

  it("initializes selected facility from user preference when store is empty", async () => {
    const facilityFromPreference = "facility1";

    (useUserPreference as any).mockReturnValue({
      preference: {
        value: facilityFromPreference,
      },
    });

    // Make sure the facility exists in available facilities
    (useConfigSetting as any).mockReturnValue({
      setting: {
        value: mockFacilities,
      },
    });

    // Make sure the user has access to the facility
    (useRoles as any).mockReturnValue({
      roles: ["facility1", "facility2", "facility3"],
    });

    render(<FacilitySwitcher />);

    // The component properly initializes from user preference
    await waitFor(() => {
      expect(mockSetSelectedFacility).toHaveBeenCalledWith(mockFacilities[0]);
    });
  });

  it("does not override store selection with preference if already selected", () => {
    (useFacilityStore as any).mockReturnValue({
      selectedFacility: mockFacilities[1], // Already selected
      setSelectedFacility: mockSetSelectedFacility,
    });

    (useUserPreference as any).mockReturnValue({
      preference: {
        value: mockFacilities[0], // Different preference
      },
    });

    render(<FacilitySwitcher />);

    expect((useFacilityStore as any).setState).not.toHaveBeenCalled();
  });

  it("handles null facility selection gracefully", () => {
    render(<FacilitySwitcher />);

    const select = screen.getByTestId("facility-select");
    fireEvent.change(select, { target: { value: "" } });

    // Should not call setSelectedFacility for null/empty selection
    expect(mockSetSelectedFacility).not.toHaveBeenCalled();
  });

  it("handles missing facility data gracefully", () => {
    (useConfigSetting as any).mockReturnValue({
      setting: null,
    });

    render(<FacilitySwitcher />);

    // When no facility data is available, it should render a span with empty content
    // since availableFacilities.length will be 0, triggering the single facility display logic
    expect(screen.queryByTestId("facility-dropdown")).not.toBeInTheDocument();
  });

  it("shows no facilities when user has no matching roles", () => {
    (useRoles as any).mockReturnValue({
      roles: ["no-matching-role"],
    });

    render(<FacilitySwitcher />);

    // When user has no matching roles, availableFacilities will be empty
    // This triggers the single facility display logic (span instead of dropdown)
    expect(screen.queryByTestId("facility-dropdown")).not.toBeInTheDocument();
  });

  it("creates proper facility object with all required fields when changing selection", async () => {
    mockSetSelectedFacility.mockResolvedValue(undefined);

    render(<FacilitySwitcher />);

    const select = screen.getByTestId("facility-select");
    fireEvent.change(select, { target: { value: "facility1" } });

    await waitFor(() => {
      expect(mockSetSelectedFacility).toHaveBeenCalledWith({
        id: "facility1",
        name: "Facility 1",
        dataset: "dataset1",
        default: false,
      });
    });
  });

  it("handles facility change with missing dataset gracefully", async () => {
    const facilitiesWithMissingData: FacilityMap[] = [
      {
        id: "facility1",
        name: "Facility 1",
        dataset: undefined as any,
        default: false,
      },
      {
        id: "facility2",
        name: "Facility 2",
        dataset: "",
        default: false,
      },
    ];

    (useConfigSetting as any).mockReturnValue({
      setting: {
        value: facilitiesWithMissingData,
      },
      isLoading: false,
    });

    (updateUserWritableSetting as any).mockResolvedValue(undefined);

    render(<FacilitySwitcher />);

    const select = screen.getByTestId("facility-select");
    fireEvent.change(select, { target: { value: "facility1" } });

    await waitFor(() => {
      expect(mockSetSelectedFacility).toHaveBeenCalledWith({
        id: "facility1",
        name: "Facility 1",
        dataset: undefined,
        default: false,
      });
    });
  });

  it("shows no facilities warning when user has no roles and loading is complete", () => {
    (useRoles as any).mockReturnValue({
      roles: [],
    });

    (useConfigSetting as any).mockReturnValue({
      setting: {
        value: mockFacilities,
      },
      isLoading: false,
    });

    render(<FacilitySwitcher />);

    expect(screen.getByTestId("no-facilities-warning")).toBeInTheDocument();
    expect(screen.getByText("No facilities")).toBeInTheDocument();
    expect(screen.getByTestId("warning-icon")).toBeInTheDocument();
  });

  it("navigates to app config settings when no facilities warning is clicked", () => {
    (useRoles as any).mockReturnValue({
      roles: [],
    });

    (useConfigSetting as any).mockReturnValue({
      setting: {
        value: mockFacilities,
      },
      isLoading: false,
    });

    render(<FacilitySwitcher />);

    const warningButton = screen.getByTestId("no-facilities-warning");
    fireEvent.click(warningButton);

    expect(mockUseNavigate).toHaveBeenCalledWith("/app-config-settings");
  });

  it("does not show no facilities warning while loading", () => {
    (useRoles as any).mockReturnValue({
      roles: [],
    });

    (useConfigSetting as any).mockReturnValue({
      setting: {
        value: mockFacilities,
      },
      isLoading: true,
    });

    render(<FacilitySwitcher />);

    expect(
      screen.queryByTestId("no-facilities-warning"),
    ).not.toBeInTheDocument();
  });
});
