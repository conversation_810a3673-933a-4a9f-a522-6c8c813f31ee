import { <PERSON><PERSON>, Dropdown, <PERSON><PERSON>, Layer } from "@carbon/react";
import { WarningFilled } from "@carbon/icons-react";
import styles from "./facility-switcher.module.css";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router";
import {
  updateUserWritableSetting,
  useConfigSetting,
  useUserPreference,
} from "../../../config/hooks/use-config";
import { FacilityMap } from "@ict/sdk/types";
import { useEffect, useState } from "react";
import { useRoles } from "../../../auth/hooks/use-roles";
import { useFacilityStore } from "../../../stores/facility-store";
import { useNotification } from "../../../components/toast/use-notification";
import { AppConfigSetting } from "src/app/config/menu/types";
import { ictApi } from "../../../api/ict-api";

export function FacilitySwitcher() {
  const { t } = useTranslation();
  const { roles } = useRoles();
  const { selectedFacility, setSelectedFacility } = useFacilityStore();
  const { error } = useNotification();
  const navigate = useNavigate();
  const [availableFacilities, setAvailableFacilities] = useState<FacilityMap[]>(
    [],
  );

  // Get the user's available facilities
  const { setting: facilityMap, isLoading: isFacilityMapLoading } =
    useConfigSetting("facility-maps");

  // Get the user's selected facility from preferences
  const { preference: selectedFacilityPreference } = useUserPreference(
    "selected-facility-id",
  );

  // Initialize selected facility from preference if not already in store
  useEffect(() => {
    if (
      !selectedFacility &&
      selectedFacilityPreference?.value &&
      availableFacilities.length > 0
    ) {
      const facilityFromPreference = selectedFacilityPreference.value;
      // the user config setting is just the facility ID; we need to find the full facility map from the available facilities
      const facilityFromAvailableFacilities: FacilityMap | undefined =
        availableFacilities.find(
          (facility) => facility.id === facilityFromPreference,
        );

      if (facilityFromAvailableFacilities)
        setSelectedFacility(facilityFromAvailableFacilities);
    }
  }, [selectedFacility, selectedFacilityPreference, availableFacilities]);

  useEffect(() => {
    if (facilityMap && facilityMap.value) {
      // filter the facilities to only include those that the user has access to
      setAvailableFacilities(
        (facilityMap.value as FacilityMap[])
          .filter((facility) => {
            return roles.some((role) => role === facility.id);
          })
          .sort((a, b) => a.name.localeCompare(b.name)),
      );
    }
  }, [facilityMap, roles]);

  /**
   * Updates the user's selected facility in the config database
   * @param facility - The facility to update the user's selected facility to
   */
  const updateSelectedFacilityForUser = async (facility: FacilityMap) => {
    try {
      let updatedFacilityUserSetting: AppConfigSetting;
      if (selectedFacilityPreference) {
        updatedFacilityUserSetting = {
          ...selectedFacilityPreference,
          value: facility.id,
        };
      } else {
        updatedFacilityUserSetting = {
          id: "", // Will be set by the backend
          name: "selected-facility-id",
          group: "user-writable",
          dataType: "string",
          value: facility.id,
          source: "user",
        };
      }

      await updateUserWritableSetting(updatedFacilityUserSetting);

      // Navigate to "/" - the use-menu hook will handle routing to the appropriate home page
      navigate("/");

      // invalidate all queries that depend on facility context
      await ictApi.queryClient.invalidateQueries();
    } catch (e) {
      console.error("Failed to update selected facility:", e);
      // send a toast to the user
      error("Failed to set facility!");
    }
  };

  const handleFacilityChange = async (selectedItem: FacilityMap | null) => {
    if (selectedItem) {
      // set the facility in the store
      setSelectedFacility(selectedItem);
      // update the user's preference
      await updateSelectedFacilityForUser(selectedItem);
    }
  };

  // Only show "No facilities" if we've finished loading and there are actually no facilities
  const isLoadingFacilities = isFacilityMapLoading || !facilityMap;
  const hasNoFacilities =
    !isLoadingFacilities && availableFacilities.length === 0;

  return (
    <Stack orientation="horizontal" style={{ cursor: "default" }} gap="1.5rem">
      {hasNoFacilities ? (
        <Button
          kind="ghost"
          size="sm"
          renderIcon={WarningFilled}
          onClick={() => navigate("/app-config-settings")}
          data-testid="no-facilities-warning"
        >
          No facilities
        </Button>
      ) : availableFacilities.length > 1 ? (
        <Layer>
          <Dropdown
            size="sm"
            id="inline"
            data-testid="facility-select"
            hideLabel={true}
            label=""
            titleText={t("tenantFacilityDisplay.facility", "Facility")}
            selectedItem={selectedFacility}
            items={availableFacilities}
            itemToString={(item) => item?.name || ""}
            onChange={({ selectedItem }) => handleFacilityChange(selectedItem)}
            className={styles.roleDropdown}
          />
        </Layer>
      ) : (
        <span>{selectedFacility?.name}</span>
      )}
    </Stack>
  );
}
