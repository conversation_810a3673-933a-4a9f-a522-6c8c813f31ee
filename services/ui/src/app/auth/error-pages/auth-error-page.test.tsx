import { describe, expect, it, vi } from "vitest";
import { fireEvent, render, screen } from "../../../test-utils";
import { AuthErrorPage } from "./auth-error-page";

// Create mock functions before mocking modules
const mockLogout = vi.fn();

// Mock the hooks
vi.mock("../../layout/theme", () => ({
  ThemeMode: {
    DARK: "dark",
    LIGHT: "light",
  },
  useTheme: () => ({
    theme: "dark", // Default to dark theme for tests
  }),
}));

vi.mock("react-i18next", () => ({
  initReactI18next: {
    type: "3rdParty",
    init: () => {},
  },
  I18nextProvider: ({ children }: { children: React.ReactNode }) => children,
  useTranslation: () => ({
    t: (key: string, defaultValue: string) => {
      // Return specific translations for the keys we use
      const translations: { [key: string]: string } = {
        "accessRequestForm.submit": "Submit Access Request",
        "authErrorPage.refresh": "Refresh",
        "authErrorPage.logout": "Log Out",
        "authErrorPage.noOrganisations": "No Organizations",
        "authErrorPage.noOrganisationsDescription":
          "Your account isn't part of any organization yet. Please provide the organization and sites you need access to.",
        "authErrorPage.unverifiedEmail": "Email Not Verified",
        "authErrorPage.unverifiedEmailDescription":
          "Please verify your email address to continue. Check your inbox for a verification link.",
        "authErrorPage.unverifiedEmailResend": "Resend Verification Email",
        "authErrorPage.unknownError": "Error",
        "authErrorPage.unknownErrorDescription":
          "An unknown error occurred. Please contact your administrator.",
        "authErrorPage.contactAdministrator": "Contact Administrator",
        "authErrorPage.submitRequest": "Submit Request",
      };
      return translations[key] || defaultValue;
    },
  }),
}));

vi.mock("../../config/hooks/use-admin", () => ({
  useResendEmailVerification: () => ({
    resendVerification: vi.fn(),
    isLoading: false,
  }),
}));

vi.mock("../../components/toast/use-notification", () => ({
  useNotification: () => ({
    success: vi.fn(),
    error: vi.fn(),
  }),
}));

vi.mock("../hooks/use-auth", () => ({
  useAuth: () => ({
    logout: mockLogout,
    user: {
      name: "Test User",
      email: "<EMAIL>",
    },
    getAccessTokenSilently: vi.fn(),
  }),
}));

// Mock Carbon components
vi.mock("@carbon/react", () => ({
  Button: ({ children, onClick }: any) => (
    <button data-testid="carbon-button" type="button" onClick={onClick}>
      {children}
    </button>
  ),
  Heading: ({ children }: any) => (
    <h2 data-testid="carbon-heading">{children}</h2>
  ),
  GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  Form: ({ children, style }: any) => (
    <form data-testid="carbon-form" style={style}>
      {children}
    </form>
  ),
  Stack: ({ children, gap }: any) => (
    <div data-testid="carbon-stack" style={{ gap }}>
      {children}
    </div>
  ),
  TextInput: ({
    id,
    labelText,
    value,
    onChange,
    placeholder,
    type,
    invalid,
    invalidText,
    required,
  }: any) => (
    <div data-testid="carbon-text-input">
      <label htmlFor={id}>{labelText}</label>
      <input
        id={id}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        type={type}
        aria-invalid={invalid}
        required={required}
      />
      {invalid && <div>{invalidText}</div>}
    </div>
  ),
}));

describe("AuthErrorPage", () => {
  beforeEach(() => {
    // Reset mock function before each test
    mockLogout.mockReset();
  });

  it("renders the 'No Organizations Found' error when organization membership error occurs", () => {
    render(<AuthErrorPage errorDescription="organization membership error" />);

    expect(screen.getByTestId("carbon-heading")).toHaveTextContent(
      "No Organizations",
    );
    expect(
      screen.getByText(/Your account isn't part of any organization yet/i),
    ).toBeInTheDocument();

    // Check for form and input fields
    expect(screen.getByTestId("carbon-form")).toBeInTheDocument();

    const emailInput = screen.getByLabelText(/Email Address/i);
    expect(emailInput).toBeInTheDocument();
    expect(emailInput).toBeRequired();

    const organizationInput = screen.getByLabelText(/Organization/i);
    expect(organizationInput).toBeInTheDocument();
    expect(organizationInput).toBeRequired();

    const buttons = screen.getAllByTestId("carbon-button");
    expect(buttons[0]).toHaveTextContent("Submit Request");
    expect(buttons[1]).toHaveTextContent("Refresh");
    expect(buttons[2]).toHaveTextContent("Log Out");
  });

  it("renders the 'Email Not Verified' error when not-verified error occurs", () => {
    render(<AuthErrorPage errorDescription="not-verified error" />);

    expect(screen.getByTestId("carbon-heading")).toHaveTextContent(
      "Email Not Verified",
    );
    expect(
      screen.getByText(/Please verify your email address to continue/i),
    ).toBeInTheDocument();

    const buttons = screen.getAllByTestId("carbon-button");
    expect(buttons[0]).toHaveTextContent("Resend Verification Email");
    expect(buttons[1]).toHaveTextContent("Refresh");
    expect(buttons[2]).toHaveTextContent("Log Out");
  });

  it("renders the generic error page for unknown errors", () => {
    render(<AuthErrorPage errorDescription="some unknown error" />);

    expect(screen.getByTestId("carbon-heading")).toHaveTextContent("Error");
    expect(screen.getByText(/An unknown error occurred/i)).toBeInTheDocument();

    const buttons = screen.getAllByTestId("carbon-button");
    expect(buttons[0]).toHaveTextContent("Contact Administrator");
  });

  it("renders the generic error page when errorDescription is null", () => {
    render(<AuthErrorPage errorDescription={null} />);

    expect(screen.getByTestId("carbon-heading")).toHaveTextContent("Error");
    expect(screen.getByText(/An unknown error occurred/i)).toBeInTheDocument();

    const buttons = screen.getAllByTestId("carbon-button");
    expect(buttons[0]).toHaveTextContent("Contact Administrator");
  });

  it("calls logout function when logout button is clicked", () => {
    render(<AuthErrorPage errorDescription="some error" />);

    const buttons = screen.getAllByTestId("carbon-button");
    fireEvent.click(buttons[2]);

    expect(mockLogout).toHaveBeenCalledWith({
      logoutParams: { returnTo: window.location.origin },
    });
  });

  it("handles refresh button click correctly", () => {
    // Mock window.location and history methods
    const originalLocation = window.location;
    const originalHistory = window.history;

    // Make properties optional before deleting
    (window as any).location = undefined;
    (window as any).history = undefined;

    const mockLocation = {
      ...originalLocation,
      href: "http://localhost:3000?error=org&error_description=test",
      reload: vi.fn(),
      toString: () => "http://localhost:3000?error=org&error_description=test",
    };

    window.location = mockLocation as unknown as string & Location;

    window.history = {
      ...originalHistory,
      replaceState: vi.fn(),
    } as any;

    render(<AuthErrorPage errorDescription="organization membership error" />);

    // Get all buttons and find the refresh button
    const buttons = screen.getAllByTestId("carbon-button");
    const refreshButton = buttons[1]; // Second button is the refresh button
    expect(refreshButton).toHaveTextContent("Refresh");

    // Click the refresh button
    fireEvent.click(refreshButton);

    // Verify URL manipulation and page reload
    expect(window.history.replaceState).toHaveBeenCalledWith(
      {},
      document.title,
      "/",
    );
    expect(window.location.reload).toHaveBeenCalled();

    // Restore original window properties
    (window as any).location = originalLocation;
    window.history = originalHistory;
  });
});
