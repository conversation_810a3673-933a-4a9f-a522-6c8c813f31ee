<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      http-equiv="Content-Security-Policy"
      content="
      default-src 'self'; 
      script-src 'unsafe-eval' 'unsafe-inline' http://localhost:* https://*.dematic.cloud https://*.dematic.one https://*.dematic.dev https://*.dematic.net cdn.pendo.io pendo-io-static.storage.googleapis.com pendo-static-6327550373855232.storage.googleapis.com data.eu.pendo.io app.pendo.io app.eu.pendo.io public.tableau.com;
      connect-src https://*.auth0.com ws://localhost:* wss://*.dematic.cloud wss://*.dematic.dev http://localhost:* http://localhost:8080 https://*.dematic.dev https://*.dematic.net https://*.dematic.cloud https://*.dematic.one data.eu.pendo.io app.eu.pendo.io pendo-static-6327550373855232.storage.googleapis.com app.pendo.io https://*.webhook.office.com; 
      img-src 'self' cdn.pendo.io data.eu.pendo.io app.eu.pendo.io pendo-static-6327550373855232.storage.googleapis.com app.pendo.io data:; 
      frame-src https://*.auth0.com https://*.dematic.cloud https://*.dematic.one https://*.dematic.dev https://*.dematic.net http://localhost:* app.pendo.io app.eu.pendo.io; 
      style-src 'self' 'unsafe-inline' pendo-io-static.storage.googleapis.com pendo-static-6327550373855232.storage.googleapis.com app.pendo.io app.eu.pendo.io; 
      base-uri 'self'; 
      form-action 'self';
      font-src http://localhost:* https://*.dematic.cloud https://*.dematic.one https://*.dematic.dev https://*.dematic.net data:;
      worker-src blob:;
    "
    />
    <title>Dematic - Control Tower</title>
    <base href="/" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/png" href="/favicon.png" />
  </head>
  <body>
    <div id="root"></div>
    <div id="global-modal-portal"></div>
    <script>
      (function (apiKey) {
        (function (p, e, n, d, o) {
          var v, w, x, y, z;
          o = p[d] = p[d] || {};
          o._q = o._q || [];
          v = ["initialize", "identify", "updateOptions", "pageLoad", "track"];
          for (w = 0, x = v.length; w < x; ++w)
            (function (m) {
              o[m] =
                o[m] ||
                function () {
                  o._q[m === v[0] ? "unshift" : "push"](
                    [m].concat([].slice.call(arguments, 0)),
                  );
                };
            })(v[w]);
          y = e.createElement(n);
          y.async = !0;
          y.src = "https://cdn.pendo.io/agent/static/" + apiKey + "/pendo.js";
          z = e.getElementsByTagName(n)[0];
          z.parentNode.insertBefore(y, z);
        })(window, document, "script", "pendo");
      })("a3384442-f67f-4db7-4347-6116b2afe376");
    </script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
