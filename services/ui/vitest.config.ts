import { defineConfig } from "vitest/config";
import path from "path";

/**
 * Exclude paths from test coverage - these includes internal code and configuration files
 */
const excludedTestingPaths = [
  "vite.config.ts",
  "eslint.config.js",
  ".yarn/**",
  "./src/app/mfe/**",
  "./src/app/components/datagrid/examples/**",
  "./src/app/views/examples/**",
  "./src/app/views/debug-info",
  "./src/app/views/feature-flags",
  "./src/app/views/mfe-manager",
  "./src/app/views/playground",
  "./src/app/widgets/examples/**",
];

export default defineConfig({
  test: {
    watch: false,
    globals: true,
    environment: "jsdom",
    include: ["src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}"],
    reporters: ["default"],
    coverage: {
      reportsDirectory: "./coverage/apps/dashboard",
      provider: "v8",
      exclude: excludedTestingPaths,
    },
  },
  resolve: {
    alias: {
      "@ict/sdk/*": path.resolve(
        __dirname,
        "../../libs/shared/ict-sdk/generated/*",
      ),
      "@ict/sdk": path.resolve(
        __dirname,
        "../../libs/shared/ict-sdk/generated",
      ),
    },
  },
});
