import re
import html
from google.cloud import translate_v3

I18NEXT_PLACEHOLDER_RE = re.compile(r"(\{\{[^}]+?\}\})")
PROTECTION_SPAN_RE = re.compile(r'<span translate="no">(.*?)</span>')


def chunk_generator(texts_list: list[str], no_texts_in_chunk: int = 128):
    for i in range(0, len(texts_list), no_texts_in_chunk):
        yield texts_list[i : i + no_texts_in_chunk]


def decode_html_entities(text: str) -> str:
    """Replaces HTML entities with alphabetical characters"""
    return html.unescape(text)


def protect_placeholders(text: str) -> str:
    """Wraps i18next placeholders in <span translate='no'> tags."""
    return I18NEXT_PLACEHOLDER_RE.sub(r'<span translate="no">\1</span>', text)


def unprotect_placeholders(text: str) -> str:
    """Removes the <span translate='no'> wrapper tags."""
    return PROTECTION_SPAN_RE.sub(r"\1", text)


def translate_text(
    source_lng: str,
    target_lng: str,
    gcp_project: str,
    gcp_location: str,
    texts_to_translate: list[str],
):
    client = translate_v3.TranslationServiceClient()

    parent = f"projects/{gcp_project}/locations/{gcp_location}"
    mine_type = "text/html"

    results = []
    print("Translating with Google")

    protected_texts = [protect_placeholders(text) for text in texts_to_translate]

    translation_chunks = chunk_generator(protected_texts, 128)

    for translation_chunk in translation_chunks:
        response = client.translate_text(
            contents=translation_chunk,
            parent=parent,
            mime_type=mine_type,
            source_language_code=source_lng,
            target_language_code=target_lng,
        )

        for translation in response.translations:
            translated_text = translation.translated_text
            unprotected_text = unprotect_placeholders(translated_text)
            decoded_text = decode_html_entities(unprotected_text)
            results.append(decoded_text)

    return results
