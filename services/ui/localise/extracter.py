import copy


class UpdateValueByPathError(Exception):
    "Error updating dictionary by path"

    def __init__(self, message):
        self.message = message
        super().__init__(self.message)


class DataLengthMismatchError(Exception):
    "Length of extracted strings list does not match length of translated strings list"

    def __init__(self, message):
        self.message = message
        super().__init__(self.message)


def extract_dict_from_json(path: str):
    import json
    import os

    if not os.path.isfile(path):
        raise ValueError("Path is not of a file")

    try:
        with open(path, "r", encoding="utf-8") as f:
            data = json.load(f)
            return data
    except json.JSONDecodeError as e:
        raise ValueError(f"Error decoding json from file {path}: {e}")
    except Exception as e:
        raise IOError(f"Error reading file {path}: {e}")


def strings_with_paths_generator(data, current_path=()):
    """
    Recursively traverses a nested dictionary/list structure and yields
    tuples of (path, string_value) for every string found.

    Args:
    The dictionary or list to traverse.
    current_path: The path taken to reach the current data element (internal use).

    Yields:
    tuple: A tuple containing (path_tuple, string_value).
            path_tuple is a tuple of keys/indices.
    """
    if isinstance(data, str):
        yield (current_path, data)
    elif isinstance(data, dict):
        for key, value in data.items():
            yield from strings_with_paths_generator(value, current_path + (key,))
    elif isinstance(data, (list, tuple)):
        for index, item in enumerate(data):
            yield from strings_with_paths_generator(item, current_path + (index,))


def extract_strings_with_paths(data):
    strings_with_paths = []

    for string in strings_with_paths_generator(data):
        strings_with_paths.append(string)

    return strings_with_paths


def update_value_by_path(data_struct, path, new_value):
    """
    Navigates through a nested dictionary/list using the path
    and updates the value at the final destination.

    Args:
        data_struct: The dictionary or list to update (modified in-place).
        path (tuple): A tuple of keys/indices representing the location.
        new_value: The new value to set at the location.

    Raises:
    UpdateValueByPathError if path cannot be found or value cannot be set
    """
    current_element = data_struct
    # Navigate to the parent element
    for i in range(len(path) - 1):
        key_or_index = path[i]
        try:
            current_element = current_element[key_or_index]
        except (KeyError, IndexError, TypeError) as e:
            raise UpdateValueByPathError(
                f"Error accessing path element '{key_or_index}' in path {path}"
            ) from e

    # Get the final key/index and update the value
    last_key_or_index = path[-1]
    try:
        current_element[last_key_or_index] = new_value
    except (KeyError, IndexError, TypeError) as e:
        raise UpdateValueByPathError(
            f"Error setting value at final path element '{last_key_or_index}' in path {path}"
        ) from e


def reconstruct_dictionary(original_dict, extracted_data, translated_strings):
    """
    Reconstructs the dictionary with translated values.

    Args:
        original_dict (dict): The original dictionary structure.
        extracted_data (list): The list of (path, original_string) tuples
                                generated by extract_strings_with_paths.
        translated_strings (list): A list of translated strings, corresponding
                                    *exactly* in order to the strings in
                                    extracted_data.

    Returns:
        dict: A new dictionary with the translated values inserted.

    Raises:
        DataLengthMismatchError: If the number of extracted items and translated strings don't match.
    """
    if len(extracted_data) != len(translated_strings):
        raise DataLengthMismatchError(
            f"Length of extracted strings list does not match length of translated strings list. Extracted Data: {extracted_data}. Translated Strings: {translated_strings}"
        )

    modified_dict = copy.deepcopy(original_dict)

    for i, (path, _original_value) in enumerate(extracted_data):
        translated_value = translated_strings[i]
        if path:
            try:
                update_value_by_path(modified_dict, path, translated_value)
            except UpdateValueByPathError as e:
                print(e)

    return modified_dict
