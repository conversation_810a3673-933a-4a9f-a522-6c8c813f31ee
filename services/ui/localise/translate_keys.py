import os
import argparse
import json

from extracter import (
    DataLengthMismatchError,
    extract_dict_from_json,
    extract_strings_with_paths,
    reconstruct_dictionary,
)
from translator import translate_text

LOCALES_DIR = "./public/locales"
TRANSLATION_FILENAME = "translation.json"


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--first_language",
        help="The language used as a fallback in i18n. This should be the name of the directory containing the translation file.",
    )
    parser.add_argument(
        "--gcp_project",
        help="The GCP project to use for translation. This must have the translation API enabled",
    )
    parser.add_argument(
        "--gcp_location",
        help="The GCP location in which the translation API should be called (e.g. `us-central1`). Defaults to `global`",
        default="global",
    )

    args = parser.parse_args()
    first_language = args.first_language
    gcp_project = args.gcp_project
    gcp_location = args.gcp_location

    locales = []

    for entry in os.listdir(LOCALES_DIR):
        locales.append(entry)

    if not first_language:
        raise ValueError("First language must be given as an argument")
    locales.remove(first_language)

    first_language_json_filepath = os.path.join(
        LOCALES_DIR, first_language, TRANSLATION_FILENAME
    )

    for locale in locales:
        language_json_filepath = os.path.join(LOCALES_DIR, locale, TRANSLATION_FILENAME)
        try:
            original_dict = extract_dict_from_json(first_language_json_filepath)
            extracted_strings_with_paths = extract_strings_with_paths(original_dict)
            extracted_strings = [item[1] for item in extracted_strings_with_paths]
        except (ValueError, IOError) as e:
            print(f"Cannot extract dict from json in locale {locale}: {e}")
            continue
        try:
            translated_strings = translate_text(
                source_lng=first_language,
                target_lng=locale,
                gcp_project=gcp_project,
                gcp_location=gcp_location,
                texts_to_translate=extracted_strings,
            )
        except Exception as e:
            print(
                f"Translating strings with Google Translate API unsuccessful for locale {locale}: {e}"
            )
            continue

        try:
            reconstructed_dict = reconstruct_dictionary(
                original_dict, extracted_strings_with_paths, translated_strings
            )
        except DataLengthMismatchError as e:
            print(
                f"Cannot reconstruct dictionary of translated strings in locale {locale}: {e}"
            )
            continue

        try:
            with open(language_json_filepath, "w", encoding="utf-8") as f:
                json.dump(reconstructed_dict, f, ensure_ascii=False, indent=2)
        except TypeError as e:
            print(f"Failed to dump reconstructed dict to file for locale {locale}: {e}")
        except OSError as e:
            print(f"Failed to open file for locale {locale}: {e}")


if __name__ == "__main__":
    main()
