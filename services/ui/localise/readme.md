# Translation of i18n keys using Google Translate API

The `translate_keys` script takes in the `translation.json` file of the first language (likely `en`). It iterates through the other `translation.json` files for other locales and returns writes the translations to the relevant json file.

As this script uses the `translation.json` file for each locale, the extraction of keys and values should be performed prior to running this script. See the README for i18n for more details.

## How to use this script

Once the extraction of keys has been performed, there should be a directory for each relevant locale in the public folder, containing a `translation.json` file. The script uses the existence of the directory and json file to perform translation to the correct language.

First, navigate to the `ui/localise` folder. Activate the virtual environment and install the necessary Python packages by running the following commands:

```bash
python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
```

Before continuing, you may need to set up Google Cloud locally to authenticate to the correct project. See these links for more details: [Set up ADC for a local development environment](https://cloud.google.com/docs/authentication/set-up-adc-local-dev-environment), [Troubleshoot your ADC setup](https://cloud.google.com/docs/authentication/troubleshoot-adc#user-creds-client-based). Keep note of the GCP project you set up for use with the `translate_keys.py` script. You may wish to specify a GCP region when running the script, although this is optional (the default is `global`).

````
Then nagivate to the root of the ui project / repo, and choosing the correct first language (likely `en`), run the following:

```bash
python3 ./localise/translate_keys.py --first_language <first-language> --gcp_project <gcp-project>
````

This should iterate through the various `translation.json` files in the `locales` directories, and using the name of the directory, perform translation of the values in the file. The output will be written to the json file(s).

## Limitations of the script

The script performs a simple translation of strings, excluding any variable names (meaning the order of variable names will not be altered either). Consider the following example:

`translation.json`

```json
{
  "exampleDateStructure": "Date: {{day}}/{{month}}//{{year}}"
}
```

If we wished to translate this to Chinese, which uses a yy/mm/dd format, our script would not provide a satisfactory result. Only the word "date" would be translated, with no change to the variable names or ordering.
