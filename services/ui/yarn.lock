# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10c0

"@adobe/css-tools@npm:^4.4.0":
  version: 4.4.2
  resolution: "@adobe/css-tools@npm:4.4.2"
  checksum: 10c0/19433666ad18536b0ed05d4b53fbb3dd6ede266996796462023ec77a90b484890ad28a3e528cdf3ab8a65cb2fcdff5d8feb04db6bc6eed6ca307c40974239c94
  languageName: node
  linkType: hard

"@ampproject/remapping@npm:^2.2.0, @ampproject/remapping@npm:^2.3.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/81d63cca5443e0f0c72ae18b544cc28c7c0ec2cea46e7cb888bb0e0f411a1191d0d6b7af798d54e30777d8d1488b2ec0732aac2be342d3d7d3ffd271c6f489ed
  languageName: node
  linkType: hard

"@asamuzakjp/css-color@npm:^3.2.0":
  version: 3.2.0
  resolution: "@asamuzakjp/css-color@npm:3.2.0"
  dependencies:
    "@csstools/css-calc": "npm:^2.1.3"
    "@csstools/css-color-parser": "npm:^3.0.9"
    "@csstools/css-parser-algorithms": "npm:^3.0.4"
    "@csstools/css-tokenizer": "npm:^3.0.3"
    lru-cache: "npm:^10.4.3"
  checksum: 10c0/a4bf1c831751b1fae46b437e37e8a38c0b5bd58d23230157ae210bd1e905fe509b89b7c243e63d1522d852668a6292ed730a160e21342772b4e5b7b8ea14c092
  languageName: node
  linkType: hard

"@auth0/auth0-react@npm:^2.3.0":
  version: 2.4.0
  resolution: "@auth0/auth0-react@npm:2.4.0"
  dependencies:
    "@auth0/auth0-spa-js": "npm:^2.2.0"
  peerDependencies:
    react: ^16.11.0 || ^17 || ^18 || ^19
    react-dom: ^16.11.0 || ^17 || ^18 || ^19
  checksum: 10c0/41ffa32d5b1e5a950c463646641ef05dc0e41c0df837f7af3e3217cd0f46ded559935db6c2956f077b5c912b7882bedf5cdecf72ed031b8bb76d7df33d3aabca
  languageName: node
  linkType: hard

"@auth0/auth0-spa-js@npm:^2.2.0":
  version: 2.3.0
  resolution: "@auth0/auth0-spa-js@npm:2.3.0"
  checksum: 10c0/c7ac73e6bcec0e5b319b0a34756df7e0a00cf25628d5ebf625dc44aeda36abbabb30cdfbe217fbcc2161b8f133fbf93d5ec8f3cd41ccbda5a0713ca693d2194b
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.10.4":
  version: 7.26.2
  resolution: "@babel/code-frame@npm:7.26.2"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.25.9"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.0.0"
  checksum: 10c0/7d79621a6849183c415486af99b1a20b84737e8c11cd55b6544f688c51ce1fd710e6d869c3dd21232023da272a79b91efb3e83b5bc2dc65c1187c5fcd1b72ea8
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/code-frame@npm:7.27.1"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.1.1"
  checksum: 10c0/5dd9a18baa5fce4741ba729acc3a3272c49c25cb8736c4b18e113099520e7ef7b545a4096a26d600e4416157e63e87d66db46aa3fbf0a5f2286da2705c12da00
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.27.2":
  version: 7.27.5
  resolution: "@babel/compat-data@npm:7.27.5"
  checksum: 10c0/da2751fcd0b58eea958f2b2f7ff7d6de1280712b709fa1ad054b73dc7d31f589e353bb50479b9dc96007935f3ed3cada68ac5b45ce93086b7122ddc32e60dc00
  languageName: node
  linkType: hard

"@babel/core@npm:^7.27.4":
  version: 7.27.4
  resolution: "@babel/core@npm:7.27.4"
  dependencies:
    "@ampproject/remapping": "npm:^2.2.0"
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.3"
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-module-transforms": "npm:^7.27.3"
    "@babel/helpers": "npm:^7.27.4"
    "@babel/parser": "npm:^7.27.4"
    "@babel/template": "npm:^7.27.2"
    "@babel/traverse": "npm:^7.27.4"
    "@babel/types": "npm:^7.27.3"
    convert-source-map: "npm:^2.0.0"
    debug: "npm:^4.1.0"
    gensync: "npm:^1.0.0-beta.2"
    json5: "npm:^2.2.3"
    semver: "npm:^6.3.1"
  checksum: 10c0/d2d17b106a8d91d3eda754bb3f26b53a12eb7646df73c2b2d2e9b08d90529186bc69e3823f70a96ec6e5719dc2372fb54e14ad499da47ceeb172d2f7008787b5
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.27.3":
  version: 7.27.5
  resolution: "@babel/generator@npm:7.27.5"
  dependencies:
    "@babel/parser": "npm:^7.27.5"
    "@babel/types": "npm:^7.27.3"
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jsesc: "npm:^3.0.2"
  checksum: 10c0/8f649ef4cd81765c832bb11de4d6064b035ffebdecde668ba7abee68a7b0bce5c9feabb5dc5bb8aeba5bd9e5c2afa3899d852d2bd9ca77a711ba8c8379f416f0
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/helper-compilation-targets@npm:7.27.2"
  dependencies:
    "@babel/compat-data": "npm:^7.27.2"
    "@babel/helper-validator-option": "npm:^7.27.1"
    browserslist: "npm:^4.24.0"
    lru-cache: "npm:^5.1.1"
    semver: "npm:^6.3.1"
  checksum: 10c0/f338fa00dcfea931804a7c55d1a1c81b6f0a09787e528ec580d5c21b3ecb3913f6cb0f361368973ce953b824d910d3ac3e8a8ee15192710d3563826447193ad1
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-module-imports@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/e00aace096e4e29290ff8648455c2bc4ed982f0d61dbf2db1b5e750b9b98f318bf5788d75a4f974c151bd318fd549e81dbcab595f46b14b81c12eda3023f51e8
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/helper-module-transforms@npm:7.27.3"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.3"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/fccb4f512a13b4c069af51e1b56b20f54024bcf1591e31e978a30f3502567f34f90a80da6a19a6148c249216292a8074a0121f9e52602510ef0f32dbce95ca01
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-plugin-utils@npm:7.27.1"
  checksum: 10c0/94cf22c81a0c11a09b197b41ab488d416ff62254ce13c57e62912c85700dc2e99e555225787a4099ff6bae7a1812d622c80fbaeda824b79baa10a6c5ac4cf69b
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-string-parser@npm:7.25.9"
  checksum: 10c0/7244b45d8e65f6b4338a6a68a8556f2cb161b782343e97281a5f2b9b93e420cad0d9f5773a59d79f61d0c448913d06f6a2358a87f2e203cf112e3c5b53522ee6
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-string-parser@npm:7.27.1"
  checksum: 10c0/8bda3448e07b5583727c103560bcf9c4c24b3c1051a4c516d4050ef69df37bb9a4734a585fe12725b8c2763de0a265aa1e909b485a4e3270b7cfd3e4dbe4b602
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-validator-identifier@npm:7.25.9"
  checksum: 10c0/4fc6f830177b7b7e887ad3277ddb3b91d81e6c4a24151540d9d1023e8dc6b1c0505f0f0628ae653601eb4388a8db45c1c14b2c07a9173837aef7e4116456259d
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-identifier@npm:7.27.1"
  checksum: 10c0/c558f11c4871d526498e49d07a84752d1800bf72ac0d3dad100309a2eaba24efbf56ea59af5137ff15e3a00280ebe588560534b0e894a4750f8b1411d8f78b84
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-option@npm:7.27.1"
  checksum: 10c0/6fec5f006eba40001a20f26b1ef5dbbda377b7b68c8ad518c05baa9af3f396e780bdfded24c4eef95d14bb7b8fd56192a6ed38d5d439b97d10efc5f1a191d148
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.27.4":
  version: 7.27.6
  resolution: "@babel/helpers@npm:7.27.6"
  dependencies:
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.6"
  checksum: 10c0/448bac96ef8b0f21f2294a826df9de6bf4026fd023f8a6bb6c782fe3e61946801ca24381490b8e58d861fee75cd695a1882921afbf1f53b0275ee68c938bd6d3
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.1.0, @babel/parser@npm:^7.20.7, @babel/parser@npm:^7.25.4":
  version: 7.26.10
  resolution: "@babel/parser@npm:7.26.10"
  dependencies:
    "@babel/types": "npm:^7.26.10"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10c0/c47f5c0f63cd12a663e9dc94a635f9efbb5059d98086a92286d7764357c66bceba18ccbe79333e01e9be3bfb8caba34b3aaebfd8e62c3d5921c8cf907267be75
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.27.2, @babel/parser@npm:^7.27.4, @babel/parser@npm:^7.27.5":
  version: 7.27.5
  resolution: "@babel/parser@npm:7.27.5"
  dependencies:
    "@babel/types": "npm:^7.27.3"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10c0/f7faaebf21cc1f25d9ca8ac02c447ed38ef3460ea95be7ea760916dcf529476340d72a5a6010c6641d9ed9d12ad827c8424840277ec2295c5b082ba0f291220a
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-self@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-self@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/00a4f917b70a608f9aca2fb39aabe04a60aa33165a7e0105fd44b3a8531630eb85bf5572e9f242f51e6ad2fa38c2e7e780902176c863556c58b5ba6f6e164031
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-source@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-source@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5e67b56c39c4d03e59e03ba80692b24c5a921472079b63af711b1d250fc37c1733a17069b63537f750f3e937ec44a42b1ee6a46cd23b1a0df5163b17f741f7f2
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.12.5, @babel/runtime@npm:^7.23.2, @babel/runtime@npm:^7.24.5, @babel/runtime@npm:^7.25.0, @babel/runtime@npm:^7.26.10":
  version: 7.26.10
  resolution: "@babel/runtime@npm:7.26.10"
  dependencies:
    regenerator-runtime: "npm:^0.14.0"
  checksum: 10c0/6dc6d88c7908f505c4f7770fb4677dfa61f68f659b943c2be1f2a99cb6680343462867abf2d49822adc435932919b36c77ac60125793e719ea8745f2073d3745
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.26.7":
  version: 7.27.0
  resolution: "@babel/runtime@npm:7.27.0"
  dependencies:
    regenerator-runtime: "npm:^0.14.0"
  checksum: 10c0/35091ea9de48bd7fd26fb177693d64f4d195eb58ab2b142b893b7f3fa0f1d7c677604d36499ae0621a3703f35ba0c6a8f6c572cc8f7dc0317213841e493cf663
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.27.1, @babel/runtime@npm:^7.27.3, @babel/runtime@npm:^7.27.6":
  version: 7.27.6
  resolution: "@babel/runtime@npm:7.27.6"
  checksum: 10c0/89726be83f356f511dcdb74d3ea4d873a5f0cf0017d4530cb53aa27380c01ca102d573eff8b8b77815e624b1f8c24e7f0311834ad4fb632c90a770fda00bd4c8
  languageName: node
  linkType: hard

"@babel/template@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/template@npm:7.27.2"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/parser": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/ed9e9022651e463cc5f2cc21942f0e74544f1754d231add6348ff1b472985a3b3502041c0be62dc99ed2d12cfae0c51394bf827452b98a2f8769c03b87aadc81
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.27.1, @babel/traverse@npm:^7.27.3, @babel/traverse@npm:^7.27.4":
  version: 7.27.4
  resolution: "@babel/traverse@npm:7.27.4"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.3"
    "@babel/parser": "npm:^7.27.4"
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.3"
    debug: "npm:^4.3.1"
    globals: "npm:^11.1.0"
  checksum: 10c0/6de8aa2a0637a6ee6d205bf48b9e923928a02415771fdec60085ed754dcdf605e450bb3315c2552fa51c31a4662275b45d5ae4ad527ce55a7db9acebdbbbb8ed
  languageName: node
  linkType: hard

"@babel/types@npm:^7.0.0, @babel/types@npm:^7.20.7, @babel/types@npm:^7.25.4, @babel/types@npm:^7.26.10":
  version: 7.26.10
  resolution: "@babel/types@npm:7.26.10"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.25.9"
    "@babel/helper-validator-identifier": "npm:^7.25.9"
  checksum: 10c0/7a7f83f568bfc3dfabfaf9ae3a97ab5c061726c0afa7dcd94226d4f84a81559da368ed79671e3a8039d16f12476cf110381a377ebdea07587925f69628200dac
  languageName: node
  linkType: hard

"@babel/types@npm:^7.26.0, @babel/types@npm:^7.27.1, @babel/types@npm:^7.27.3, @babel/types@npm:^7.27.6":
  version: 7.27.6
  resolution: "@babel/types@npm:7.27.6"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
  checksum: 10c0/39d556be114f2a6d874ea25ad39826a9e3a0e98de0233ae6d932f6d09a4b222923a90a7274c635ed61f1ba49bbd345329226678800900ad1c8d11afabd573aaf
  languageName: node
  linkType: hard

"@bcoe/v8-coverage@npm:^1.0.2":
  version: 1.0.2
  resolution: "@bcoe/v8-coverage@npm:1.0.2"
  checksum: 10c0/1eb1dc93cc17fb7abdcef21a6e7b867d6aa99a7ec88ec8207402b23d9083ab22a8011213f04b2cf26d535f1d22dc26139b7929e6c2134c254bd1e14ba5e678c3
  languageName: node
  linkType: hard

"@bufbuild/protobuf@npm:^2.5.1":
  version: 2.5.2
  resolution: "@bufbuild/protobuf@npm:2.5.2"
  checksum: 10c0/30f0ede04b5318eda502759044329f44af27e0bebd9853d7d9baf5bcb4f4b17f813eb8904d98718d991bd56d33565ed18f8c9c65067626c3d4e55a4e039fe9b6
  languageName: node
  linkType: hard

"@carbon/charts-react@npm:^1.23.10":
  version: 1.23.15
  resolution: "@carbon/charts-react@npm:1.23.15"
  dependencies:
    "@carbon/charts": "npm:1.23.15"
    "@carbon/icons-react": "npm:^11.60.0"
    "@ibm/telemetry-js": "npm:^1.9.1"
  peerDependencies:
    react: ^16.8.6 || ^17.0.1 || ^18.2.0 || ^19.0.0
    react-dom: ^16.8.6 || ^17.0.1 || ^18.2.0 || ^19.0.0
  checksum: 10c0/0c3149cab193731e3ca2c8869081c7e794dcd304ef5405b2820f5410a8ef7468015ec6892a0ec1b52b9f86d97570914d3bbff1ea566d51f134588800e3ec8322
  languageName: node
  linkType: hard

"@carbon/charts@npm:1.23.15":
  version: 1.23.15
  resolution: "@carbon/charts@npm:1.23.15"
  dependencies:
    "@carbon/colors": "npm:^11.33.0"
    "@carbon/utils-position": "npm:^1.3.0"
    "@ibm/telemetry-js": "npm:^1.9.1"
    "@types/d3": "npm:^7.4.3"
    "@types/topojson": "npm:^3.2.6"
    d3: "npm:^7.9.0"
    d3-cloud: "npm:^1.2.7"
    d3-sankey: "npm:^0.12.3"
    date-fns: "npm:^4.1.0"
    dompurify: "npm:^3.2.6"
    html-to-image: "npm:1.11.11"
    lodash-es: "npm:^4.17.21"
    topojson-client: "npm:^3.1.0"
    tslib: "npm:^2.8.1"
  checksum: 10c0/7c73fd9d38614c0544c1fcce27beda5ed99ad52232cac950bb42de9d7d2cea2d5719d6b1ae44dc67ed56144775169e63ea02c5f3cf7a819758bf973a2d926c19
  languageName: node
  linkType: hard

"@carbon/colors@npm:^11.33.0":
  version: 11.34.0
  resolution: "@carbon/colors@npm:11.34.0"
  dependencies:
    "@ibm/telemetry-js": "npm:^1.5.0"
  checksum: 10c0/f7294c08991a509adbff6c86fec23105a033aece1160137382812e937bf8eb280eea71773445c71aabd4a500ee66c1f323d20585bce8c4442257898cc0a7eccd
  languageName: node
  linkType: hard

"@carbon/colors@npm:^11.37.0":
  version: 11.37.0
  resolution: "@carbon/colors@npm:11.37.0"
  dependencies:
    "@ibm/telemetry-js": "npm:^1.5.0"
  checksum: 10c0/2d8fa7c106d233e157ca0a2ce30da01266451f135cec35b9fb8467e79bb137d86b4110fb4265e7003c29e66f05d400f08762c2da0b46a871c1d40d81c22a6734
  languageName: node
  linkType: hard

"@carbon/feature-flags@npm:^0.29.0":
  version: 0.29.0
  resolution: "@carbon/feature-flags@npm:0.29.0"
  dependencies:
    "@ibm/telemetry-js": "npm:^1.5.0"
  checksum: 10c0/361f7e21255655ce3ec63159043756c61443f5c08985037d6232f1b35984bcebac762b49cf8f12898c8622ab3a836dead519cde22f243b1b7cda0ea64bf3285b
  languageName: node
  linkType: hard

"@carbon/grid@npm:^11.40.0":
  version: 11.40.0
  resolution: "@carbon/grid@npm:11.40.0"
  dependencies:
    "@carbon/layout": "npm:^11.38.0"
    "@ibm/telemetry-js": "npm:^1.5.0"
  checksum: 10c0/e941a312edc7e9213e2a1b9684d52fa5b3b5f9dff8b165ef6dd924249a219a3263120616074332df96f79bc7f4b34f585d8d0d79de0e22ef67797d9e2272e2e7
  languageName: node
  linkType: hard

"@carbon/icon-helpers@npm:^10.63.0":
  version: 10.63.0
  resolution: "@carbon/icon-helpers@npm:10.63.0"
  dependencies:
    "@ibm/telemetry-js": "npm:^1.5.0"
  checksum: 10c0/24c14d082715aa03981e1b81de2a1a408e2d9805e05c02ef909eb11e87414762e7177b741752fd2a50dca00246dfbb4fe6a8264fee60b19a3e677363643d9661
  languageName: node
  linkType: hard

"@carbon/icons-react@npm:^11.60.0, @carbon/icons-react@npm:^11.64.0":
  version: 11.64.0
  resolution: "@carbon/icons-react@npm:11.64.0"
  dependencies:
    "@carbon/icon-helpers": "npm:^10.63.0"
    "@ibm/telemetry-js": "npm:^1.5.0"
    prop-types: "npm:^15.8.1"
  peerDependencies:
    react: ">=16"
  checksum: 10c0/986b9bd8734fa04a2a02ae9984670a639f2e53a39b3a678ffec2c1efbc953d029d40762e75cd448f083b12dffb7152eb5b390dca1c0df742fae6c7991a88de30
  languageName: node
  linkType: hard

"@carbon/layout@npm:^11.38.0":
  version: 11.38.0
  resolution: "@carbon/layout@npm:11.38.0"
  dependencies:
    "@ibm/telemetry-js": "npm:^1.5.0"
  checksum: 10c0/c774b6fae5b2c2812348215fb49c81a0effe731be0a5986e677d1ae8f463f91c9f77e89620d47ef6b1b4de9d902ad20c4625b4a427cd47691043df84f80da44b
  languageName: node
  linkType: hard

"@carbon/motion@npm:^11.32.0":
  version: 11.32.0
  resolution: "@carbon/motion@npm:11.32.0"
  dependencies:
    "@ibm/telemetry-js": "npm:^1.5.0"
  checksum: 10c0/14d46a96ccd6361c7ab830cdaf3d9181d0b41a9b5837f80e68376f5dd32dd169e6ecaa9f8817ac01355bd2b82bad334c887a230f53575ad556e744cc1bb50bc5
  languageName: node
  linkType: hard

"@carbon/react@npm:^1.83.0":
  version: 1.88.0
  resolution: "@carbon/react@npm:1.88.0"
  dependencies:
    "@babel/runtime": "npm:^7.27.3"
    "@carbon/feature-flags": "npm:^0.29.0"
    "@carbon/icons-react": "npm:^11.64.0"
    "@carbon/layout": "npm:^11.38.0"
    "@carbon/styles": "npm:^1.87.0"
    "@carbon/utilities": "npm:^0.7.0"
    "@floating-ui/react": "npm:^0.27.4"
    "@ibm/telemetry-js": "npm:^1.5.0"
    classnames: "npm:2.5.1"
    copy-to-clipboard: "npm:^3.3.1"
    downshift: "npm:9.0.9"
    es-toolkit: "npm:^1.27.0"
    flatpickr: "npm:4.6.13"
    invariant: "npm:^2.2.3"
    prop-types: "npm:^15.8.1"
    react-fast-compare: "npm:^3.2.2"
    tabbable: "npm:^6.2.0"
    window-or-global: "npm:^1.0.1"
  peerDependencies:
    react: ^16.8.6 || ^17.0.1 || ^18.2.0 || ^19.0.0
    react-dom: ^16.8.6 || ^17.0.1 || ^18.2.0 || ^19.0.0
    react-is: ^16.13.1 || ^17.0.2 || ^18.3.1 || ^19.0.0
    sass: ^1.33.0
  checksum: 10c0/0c6d9058ef2fb1bc72febf11d82160571ef047efcac17d9f0ef61ed7553cbf3050689d934df1ab7dcd7b61532cc99e5025e16c68c33c5821492c3a8efaeafff1
  languageName: node
  linkType: hard

"@carbon/styles@npm:^1.87.0":
  version: 1.87.0
  resolution: "@carbon/styles@npm:1.87.0"
  dependencies:
    "@carbon/colors": "npm:^11.37.0"
    "@carbon/feature-flags": "npm:^0.29.0"
    "@carbon/grid": "npm:^11.40.0"
    "@carbon/layout": "npm:^11.38.0"
    "@carbon/motion": "npm:^11.32.0"
    "@carbon/themes": "npm:^11.57.0"
    "@carbon/type": "npm:^11.44.0"
    "@ibm/plex": "npm:6.0.0-next.6"
    "@ibm/plex-mono": "npm:0.0.3-alpha.0"
    "@ibm/plex-sans": "npm:0.0.3-alpha.0"
    "@ibm/plex-sans-arabic": "npm:0.0.3-alpha.0"
    "@ibm/plex-sans-devanagari": "npm:0.0.3-alpha.0"
    "@ibm/plex-sans-hebrew": "npm:0.0.3-alpha.0"
    "@ibm/plex-sans-thai": "npm:0.0.3-alpha.0"
    "@ibm/plex-sans-thai-looped": "npm:0.0.3-alpha.0"
    "@ibm/plex-serif": "npm:0.0.3-alpha.0"
    "@ibm/telemetry-js": "npm:^1.5.0"
  peerDependencies:
    sass: ^1.33.0
  peerDependenciesMeta:
    sass:
      optional: true
  checksum: 10c0/217b094a63474b8b6d64878a524c44cc1adc29faa5aec0f28730a6b7b7688783b7c546398db2e162c1ccf395a45794b1b8754a479530d68b14d571f4c0d62bfb
  languageName: node
  linkType: hard

"@carbon/themes@npm:^11.57.0":
  version: 11.57.0
  resolution: "@carbon/themes@npm:11.57.0"
  dependencies:
    "@carbon/colors": "npm:^11.37.0"
    "@carbon/layout": "npm:^11.38.0"
    "@carbon/type": "npm:^11.44.0"
    "@ibm/telemetry-js": "npm:^1.5.0"
    color: "npm:^4.0.0"
  checksum: 10c0/0f673ea1dde55dff2f03c24837aa7136e914b6f75d023359ce11d86ba222847ce348651ad541985a5cf12487848b3212d49837198be0e030c0393255484db57f
  languageName: node
  linkType: hard

"@carbon/type@npm:^11.44.0":
  version: 11.44.0
  resolution: "@carbon/type@npm:11.44.0"
  dependencies:
    "@carbon/grid": "npm:^11.40.0"
    "@carbon/layout": "npm:^11.38.0"
    "@ibm/telemetry-js": "npm:^1.5.0"
  checksum: 10c0/eafc4ff6d03f7be5f44ed4b65d0b6cb454ef5d72b31a5bb007bf979fe0424e5d0c50e0f4e2eddd6a2671c0a343077bd4a4474239b312d219380c90fb83a35135
  languageName: node
  linkType: hard

"@carbon/utilities@npm:^0.7.0":
  version: 0.7.0
  resolution: "@carbon/utilities@npm:0.7.0"
  dependencies:
    "@ibm/telemetry-js": "npm:^1.6.1"
    "@internationalized/number": "npm:^3.6.1"
  checksum: 10c0/29887942a42d319c631f7132de0981f06811a74ef6aba0ce6a8f3f040f1d627e0cb8393419cc7ac09ff27b877f43e28957fd8a407fe8272ff0ed46115fc8a008
  languageName: node
  linkType: hard

"@carbon/utils-position@npm:^1.3.0":
  version: 1.3.0
  resolution: "@carbon/utils-position@npm:1.3.0"
  dependencies:
    "@ibm/telemetry-js": "npm:^1.5.1"
  checksum: 10c0/dcce1c82d538a35ff42ba9b3e8b13960eb71ad013d92fa17ca4960d256f4d92cfe5f722781ae4cc33ae75c7207ec43cf1c6c7d70240004f6d649a3e1c95704bb
  languageName: node
  linkType: hard

"@csstools/color-helpers@npm:^5.0.2":
  version: 5.0.2
  resolution: "@csstools/color-helpers@npm:5.0.2"
  checksum: 10c0/bebaddb28b9eb58b0449edd5d0c0318fa88f3cb079602ee27e88c9118070d666dcc4e09a5aa936aba2fde6ba419922ade07b7b506af97dd7051abd08dfb2959b
  languageName: node
  linkType: hard

"@csstools/css-calc@npm:^2.1.3, @csstools/css-calc@npm:^2.1.4":
  version: 2.1.4
  resolution: "@csstools/css-calc@npm:2.1.4"
  peerDependencies:
    "@csstools/css-parser-algorithms": ^3.0.5
    "@csstools/css-tokenizer": ^3.0.4
  checksum: 10c0/42ce5793e55ec4d772083808a11e9fb2dfe36db3ec168713069a276b4c3882205b3507c4680224c28a5d35fe0bc2d308c77f8f2c39c7c09aad8747708eb8ddd8
  languageName: node
  linkType: hard

"@csstools/css-color-parser@npm:^3.0.9":
  version: 3.0.10
  resolution: "@csstools/css-color-parser@npm:3.0.10"
  dependencies:
    "@csstools/color-helpers": "npm:^5.0.2"
    "@csstools/css-calc": "npm:^2.1.4"
  peerDependencies:
    "@csstools/css-parser-algorithms": ^3.0.5
    "@csstools/css-tokenizer": ^3.0.4
  checksum: 10c0/8f8a2395b117c2f09366b5c9bf49bc740c92a65b6330fe3cc1e76abafd0d1000e42a657d7b0a3814846a66f1d69896142f7e36d7a4aca77de977e5cc5f944747
  languageName: node
  linkType: hard

"@csstools/css-parser-algorithms@npm:^3.0.4":
  version: 3.0.5
  resolution: "@csstools/css-parser-algorithms@npm:3.0.5"
  peerDependencies:
    "@csstools/css-tokenizer": ^3.0.4
  checksum: 10c0/d9a1c888bd43849ae3437ca39251d5c95d2c8fd6b5ccdb7c45491dfd2c1cbdc3075645e80901d120e4d2c1993db9a5b2d83793b779dbbabcfb132adb142eb7f7
  languageName: node
  linkType: hard

"@csstools/css-tokenizer@npm:^3.0.3":
  version: 3.0.4
  resolution: "@csstools/css-tokenizer@npm:3.0.4"
  checksum: 10c0/3b589f8e9942075a642213b389bab75a2d50d05d203727fcdac6827648a5572674caff07907eff3f9a2389d86a4ee47308fafe4f8588f4a77b7167c588d2559f
  languageName: node
  linkType: hard

"@dagrejs/dagre@npm:^1.1.4":
  version: 1.1.5
  resolution: "@dagrejs/dagre@npm:1.1.5"
  dependencies:
    "@dagrejs/graphlib": "npm:2.2.4"
  checksum: 10c0/738e2d86ee093b99ec96098f1a4d0b6290b6ddaef51db912885df0ab5d09291edb82ef875575a4b321bc13bab78b1cb00347b246aad4099ce4af3202a72a8586
  languageName: node
  linkType: hard

"@dagrejs/graphlib@npm:2.2.4":
  version: 2.2.4
  resolution: "@dagrejs/graphlib@npm:2.2.4"
  checksum: 10c0/14597ea9294c46b2571aee78bcaad3a24e3e5e0ebcdf198b6eae5b3805f99af727ac54a477dd9152e8b0a576efea0528fb7d4919c74801e9f669c90e5e6f5bd9
  languageName: node
  linkType: hard

"@esbuild/aix-ppc64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/aix-ppc64@npm:0.25.1"
  conditions: os=aix & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/android-arm64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/android-arm64@npm:0.25.1"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/android-arm@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/android-arm@npm:0.25.1"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/android-x64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/android-x64@npm:0.25.1"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/darwin-arm64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/darwin-arm64@npm:0.25.1"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/darwin-x64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/darwin-x64@npm:0.25.1"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/freebsd-arm64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/freebsd-arm64@npm:0.25.1"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/freebsd-x64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/freebsd-x64@npm:0.25.1"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/linux-arm64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/linux-arm64@npm:0.25.1"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/linux-arm@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/linux-arm@npm:0.25.1"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/linux-ia32@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/linux-ia32@npm:0.25.1"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/linux-loong64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/linux-loong64@npm:0.25.1"
  conditions: os=linux & cpu=loong64
  languageName: node
  linkType: hard

"@esbuild/linux-mips64el@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/linux-mips64el@npm:0.25.1"
  conditions: os=linux & cpu=mips64el
  languageName: node
  linkType: hard

"@esbuild/linux-ppc64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/linux-ppc64@npm:0.25.1"
  conditions: os=linux & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/linux-riscv64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/linux-riscv64@npm:0.25.1"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"@esbuild/linux-s390x@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/linux-s390x@npm:0.25.1"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@esbuild/linux-x64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/linux-x64@npm:0.25.1"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/netbsd-arm64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/netbsd-arm64@npm:0.25.1"
  conditions: os=netbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/netbsd-x64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/netbsd-x64@npm:0.25.1"
  conditions: os=netbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openbsd-arm64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/openbsd-arm64@npm:0.25.1"
  conditions: os=openbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/openbsd-x64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/openbsd-x64@npm:0.25.1"
  conditions: os=openbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/sunos-x64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/sunos-x64@npm:0.25.1"
  conditions: os=sunos & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/win32-arm64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/win32-arm64@npm:0.25.1"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/win32-ia32@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/win32-ia32@npm:0.25.1"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/win32-x64@npm:0.25.1":
  version: 0.25.1
  resolution: "@esbuild/win32-x64@npm:0.25.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0":
  version: 4.5.1
  resolution: "@eslint-community/eslint-utils@npm:4.5.1"
  dependencies:
    eslint-visitor-keys: "npm:^3.4.3"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: 10c0/b520ae1b7bd04531a5c5da2021071815df4717a9f7d13720e3a5ddccf5c9c619532039830811fcbae1c2f1c9d133e63af2435ee69e0fc0fabbd6d928c6800fb2
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.5.1, @eslint-community/eslint-utils@npm:^4.7.0":
  version: 4.7.0
  resolution: "@eslint-community/eslint-utils@npm:4.7.0"
  dependencies:
    eslint-visitor-keys: "npm:^3.4.3"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: 10c0/c0f4f2bd73b7b7a9de74b716a664873d08ab71ab439e51befe77d61915af41a81ecec93b408778b3a7856185244c34c2c8ee28912072ec14def84ba2dec70adf
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.10.0, @eslint-community/regexpp@npm:^4.12.1":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: 10c0/a03d98c246bcb9109aec2c08e4d10c8d010256538dcb3f56610191607214523d4fb1b00aa81df830b6dffb74c5fa0be03642513a289c567949d3e550ca11cdf6
  languageName: node
  linkType: hard

"@eslint/config-array@npm:^0.21.0":
  version: 0.21.0
  resolution: "@eslint/config-array@npm:0.21.0"
  dependencies:
    "@eslint/object-schema": "npm:^2.1.6"
    debug: "npm:^4.3.1"
    minimatch: "npm:^3.1.2"
  checksum: 10c0/0ea801139166c4aa56465b309af512ef9b2d3c68f9198751bbc3e21894fe70f25fbf26e1b0e9fffff41857bc21bfddeee58649ae6d79aadcd747db0c5dca771f
  languageName: node
  linkType: hard

"@eslint/config-helpers@npm:^0.3.1":
  version: 0.3.1
  resolution: "@eslint/config-helpers@npm:0.3.1"
  checksum: 10c0/f6c5b3a0b76a0d7d84cc93e310c259e6c3e0792ddd0a62c5fc0027796ffae44183432cb74b2c2b1162801ee1b1b34a6beb5d90a151632b4df7349f994146a856
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.12.0":
  version: 0.12.0
  resolution: "@eslint/core@npm:0.12.0"
  dependencies:
    "@types/json-schema": "npm:^7.0.15"
  checksum: 10c0/d032af81195bb28dd800c2b9617548c6c2a09b9490da3c5537fd2a1201501666d06492278bb92cfccac1f7ac249e58601dd87f813ec0d6a423ef0880434fa0c3
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.15.2":
  version: 0.15.2
  resolution: "@eslint/core@npm:0.15.2"
  dependencies:
    "@types/json-schema": "npm:^7.0.15"
  checksum: 10c0/c17a6dc4f5a6006ecb60165cc38bcd21fefb4a10c7a2578a0cfe5813bbd442531a87ed741da5adab5eb678e8e693fda2e2b14555b035355537e32bcec367ea17
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^3.3.1":
  version: 3.3.1
  resolution: "@eslint/eslintrc@npm:3.3.1"
  dependencies:
    ajv: "npm:^6.12.4"
    debug: "npm:^4.3.2"
    espree: "npm:^10.0.1"
    globals: "npm:^14.0.0"
    ignore: "npm:^5.2.0"
    import-fresh: "npm:^3.2.1"
    js-yaml: "npm:^4.1.0"
    minimatch: "npm:^3.1.2"
    strip-json-comments: "npm:^3.1.1"
  checksum: 10c0/b0e63f3bc5cce4555f791a4e487bf999173fcf27c65e1ab6e7d63634d8a43b33c3693e79f192cbff486d7df1be8ebb2bd2edc6e70ddd486cbfa84a359a3e3b41
  languageName: node
  linkType: hard

"@eslint/js@npm:9.33.0":
  version: 9.33.0
  resolution: "@eslint/js@npm:9.33.0"
  checksum: 10c0/4c42c9abde76a183b8e47205fd6c3116b058f82f07b6ad4de40de56cdb30a36e9ecd40efbea1b63a84d08c206aadbb0aa39a890197e1ad6455a8e542df98f186
  languageName: node
  linkType: hard

"@eslint/object-schema@npm:^2.1.6":
  version: 2.1.6
  resolution: "@eslint/object-schema@npm:2.1.6"
  checksum: 10c0/b8cdb7edea5bc5f6a96173f8d768d3554a628327af536da2fc6967a93b040f2557114d98dbcdbf389d5a7b290985ad6a9ce5babc547f36fc1fde42e674d11a56
  languageName: node
  linkType: hard

"@eslint/plugin-kit@npm:^0.2.7":
  version: 0.2.7
  resolution: "@eslint/plugin-kit@npm:0.2.7"
  dependencies:
    "@eslint/core": "npm:^0.12.0"
    levn: "npm:^0.4.1"
  checksum: 10c0/0a1aff1ad63e72aca923217e556c6dfd67d7cd121870eb7686355d7d1475d569773528a8b2111b9176f3d91d2ea81f7413c34600e8e5b73d59e005d70780b633
  languageName: node
  linkType: hard

"@eslint/plugin-kit@npm:^0.3.5":
  version: 0.3.5
  resolution: "@eslint/plugin-kit@npm:0.3.5"
  dependencies:
    "@eslint/core": "npm:^0.15.2"
    levn: "npm:^0.4.1"
  checksum: 10c0/c178c1b58c574200c0fd125af3e4bc775daba7ce434ba6d1eeaf9bcb64b2e9fea75efabffb3ed3ab28858e55a016a5efa95f509994ee4341b341199ca630b89e
  languageName: node
  linkType: hard

"@floating-ui/core@npm:^1.6.0":
  version: 1.6.9
  resolution: "@floating-ui/core@npm:1.6.9"
  dependencies:
    "@floating-ui/utils": "npm:^0.2.9"
  checksum: 10c0/77debdfc26bc36c6f5ae1f26ab3c15468215738b3f5682af4e1915602fa21ba33ad210273f31c9d2da1c531409929e1afb1138b1608c6b54a0f5853ee84c340d
  languageName: node
  linkType: hard

"@floating-ui/dom@npm:^1.0.0":
  version: 1.6.13
  resolution: "@floating-ui/dom@npm:1.6.13"
  dependencies:
    "@floating-ui/core": "npm:^1.6.0"
    "@floating-ui/utils": "npm:^0.2.9"
  checksum: 10c0/272242d2eb6238ffcee0cb1f3c66e0eafae804d5d7b449db5ecf904bc37d31ad96cf575a9e650b93c1190f64f49a684b1559d10e05ed3ec210628b19116991a9
  languageName: node
  linkType: hard

"@floating-ui/react-dom@npm:^2.1.2":
  version: 2.1.2
  resolution: "@floating-ui/react-dom@npm:2.1.2"
  dependencies:
    "@floating-ui/dom": "npm:^1.0.0"
  peerDependencies:
    react: ">=16.8.0"
    react-dom: ">=16.8.0"
  checksum: 10c0/e855131c74e68cab505f7f44f92cd4e2efab1c125796db3116c54c0859323adae4bf697bf292ee83ac77b9335a41ad67852193d7aeace90aa2e1c4a640cafa60
  languageName: node
  linkType: hard

"@floating-ui/react@npm:^0.27.4":
  version: 0.27.5
  resolution: "@floating-ui/react@npm:0.27.5"
  dependencies:
    "@floating-ui/react-dom": "npm:^2.1.2"
    "@floating-ui/utils": "npm:^0.2.9"
    tabbable: "npm:^6.0.0"
  peerDependencies:
    react: ">=17.0.0"
    react-dom: ">=17.0.0"
  checksum: 10c0/2da91779fddd3f368bfec0ceeb67b98e7b73f07b27f56f5ae339169de9c4145851ed432469d5dd81b99b9d6a5f0fefbb4ea8c881d2c97dd2dc0a0e96ad0945dd
  languageName: node
  linkType: hard

"@floating-ui/utils@npm:^0.2.9":
  version: 0.2.9
  resolution: "@floating-ui/utils@npm:0.2.9"
  checksum: 10c0/48bbed10f91cb7863a796cc0d0e917c78d11aeb89f98d03fc38d79e7eb792224a79f538ed8a2d5d5584511d4ca6354ef35f1712659fd569868e342df4398ad6f
  languageName: node
  linkType: hard

"@gulpjs/to-absolute-glob@npm:^4.0.0":
  version: 4.0.0
  resolution: "@gulpjs/to-absolute-glob@npm:4.0.0"
  dependencies:
    is-negated-glob: "npm:^1.0.0"
  checksum: 10c0/acddf10466bfff672e7d09d5b7d9fb2d9d50dff3bcf6d4cc3b3df364ea0ccad6e7a8d8ba0f474f880ff18a76ebbcc09b3f4d6d12d2913e3469361d5539a72110
  languageName: node
  linkType: hard

"@humanfs/core@npm:^0.19.1":
  version: 0.19.1
  resolution: "@humanfs/core@npm:0.19.1"
  checksum: 10c0/aa4e0152171c07879b458d0e8a704b8c3a89a8c0541726c6b65b81e84fd8b7564b5d6c633feadc6598307d34564bd53294b533491424e8e313d7ab6c7bc5dc67
  languageName: node
  linkType: hard

"@humanfs/node@npm:^0.16.6":
  version: 0.16.6
  resolution: "@humanfs/node@npm:0.16.6"
  dependencies:
    "@humanfs/core": "npm:^0.19.1"
    "@humanwhocodes/retry": "npm:^0.3.0"
  checksum: 10c0/8356359c9f60108ec204cbd249ecd0356667359b2524886b357617c4a7c3b6aace0fd5a369f63747b926a762a88f8a25bc066fa1778508d110195ce7686243e1
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 10c0/909b69c3b86d482c26b3359db16e46a32e0fb30bd306a3c176b8313b9e7313dba0f37f519de6aa8b0a1921349e505f259d19475e123182416a506d7f87e7f529
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.3.0":
  version: 0.3.1
  resolution: "@humanwhocodes/retry@npm:0.3.1"
  checksum: 10c0/f0da1282dfb45e8120480b9e2e275e2ac9bbe1cf016d046fdad8e27cc1285c45bb9e711681237944445157b430093412b4446c1ab3fc4bb037861b5904101d3b
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.4.2":
  version: 0.4.2
  resolution: "@humanwhocodes/retry@npm:0.4.2"
  checksum: 10c0/0235525d38f243bee3bf8b25ed395fbf957fb51c08adae52787e1325673071abe856c7e18e530922ed2dd3ce12ed82ba01b8cee0279ac52a3315fcdc3a69ef0c
  languageName: node
  linkType: hard

"@ibm/plex-mono@npm:0.0.3-alpha.0":
  version: 0.0.3-alpha.0
  resolution: "@ibm/plex-mono@npm:0.0.3-alpha.0"
  checksum: 10c0/75fa4dcaac8deef027004c7ed431a438e3584bc7a21e16446818ea576f2ed9af06a80980f3959d2ce0c640f30bdcc1b3c8583a858c4c7419320c70aa75e46b1e
  languageName: node
  linkType: hard

"@ibm/plex-sans-arabic@npm:0.0.3-alpha.0":
  version: 0.0.3-alpha.0
  resolution: "@ibm/plex-sans-arabic@npm:0.0.3-alpha.0"
  checksum: 10c0/be0ca3afe44241c0d429bb307540c72e3af81cc02bda9bb0027d00cd82e00b53ad2301d6e20f2b0434f260325d255edbddeab4addc5f32076dc7ee1988c51a3a
  languageName: node
  linkType: hard

"@ibm/plex-sans-devanagari@npm:0.0.3-alpha.0":
  version: 0.0.3-alpha.0
  resolution: "@ibm/plex-sans-devanagari@npm:0.0.3-alpha.0"
  checksum: 10c0/838c23d4e279f4b800594227f1b59edc291dc39c76551e0db988cf9641600683b885fb469de7ec8504204b8d7f76dddd04b5478ed995bdb9719465766d79f316
  languageName: node
  linkType: hard

"@ibm/plex-sans-hebrew@npm:0.0.3-alpha.0":
  version: 0.0.3-alpha.0
  resolution: "@ibm/plex-sans-hebrew@npm:0.0.3-alpha.0"
  checksum: 10c0/7737413091a9751b2e031ef2cf3adec4942c708896f3b3344973c03e94b947af176515717ba9ecc027e6f142633193ead4318ed65bf023c49618664aa892f96c
  languageName: node
  linkType: hard

"@ibm/plex-sans-thai-looped@npm:0.0.3-alpha.0":
  version: 0.0.3-alpha.0
  resolution: "@ibm/plex-sans-thai-looped@npm:0.0.3-alpha.0"
  checksum: 10c0/40596540e64e997bd65b860241764118df896cfb3426c081f75df1416c99f3a61a61881f1df066bd422ad0db934e8720e0f9e353ba385039cc437d89f30d8ee8
  languageName: node
  linkType: hard

"@ibm/plex-sans-thai@npm:0.0.3-alpha.0":
  version: 0.0.3-alpha.0
  resolution: "@ibm/plex-sans-thai@npm:0.0.3-alpha.0"
  checksum: 10c0/ef0877fcafdd99f6cd6c5aff3b96245de9dada9aee58bdcaa5da70256161ce7b67a7457e4a5952c9351b4eff0e06195c548ed0527eaf4259cc4e581a835af733
  languageName: node
  linkType: hard

"@ibm/plex-sans@npm:0.0.3-alpha.0":
  version: 0.0.3-alpha.0
  resolution: "@ibm/plex-sans@npm:0.0.3-alpha.0"
  checksum: 10c0/9178849fa798506d55d16cd09210825255952b8661243e933cb2e68874e94ce9a438f848adb6f1729044d899991ce75f4eb5ad4a018f52fa4d43c3b5d25628cc
  languageName: node
  linkType: hard

"@ibm/plex-serif@npm:0.0.3-alpha.0":
  version: 0.0.3-alpha.0
  resolution: "@ibm/plex-serif@npm:0.0.3-alpha.0"
  checksum: 10c0/215c3182b6b197045711bc351162f2084a9ed06a85f00a00099543f2ac789e2b1a0659f83ff9d2602cd30fde4d3225b549f82ce966f4d64bb124e17bd77962d0
  languageName: node
  linkType: hard

"@ibm/plex@npm:6.0.0-next.6":
  version: 6.0.0-next.6
  resolution: "@ibm/plex@npm:6.0.0-next.6"
  checksum: 10c0/7f47f9535def1047dff51703fd1041c2f2915df1d405f1d62e785cc55b56478ea91af2e95b838692ef4142835ea765217935e2e336bac5a4437a9092ec0cfb4f
  languageName: node
  linkType: hard

"@ibm/telemetry-js@npm:^1.5.0, @ibm/telemetry-js@npm:^1.5.1, @ibm/telemetry-js@npm:^1.6.1, @ibm/telemetry-js@npm:^1.9.1":
  version: 1.9.1
  resolution: "@ibm/telemetry-js@npm:1.9.1"
  bin:
    ibmtelemetry: dist/collect.js
  checksum: 10c0/fc7684c0051f76884ecc24409558b6162a11a1c80536afbe1f201b5f26f234f5ae7236ab13b260ce4faaaec0deacc0f2b702b2ae6b02765fef81c5bc4f3215e2
  languageName: node
  linkType: hard

"@internationalized/number@npm:^3.6.1":
  version: 3.6.3
  resolution: "@internationalized/number@npm:3.6.3"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
  checksum: 10c0/3173eaa59d78a4983bb9de7e6e61d4c6e1749805fd39b23f820fa3cafce7ed79a3a53fde2213bcb7fc8ab351ec1ffcd21956d41d19a7ae869002768b4a471510
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10c0/b1bf42535d49f11dc137f18d5e4e63a28c5569de438a221c369483731e9dac9fb797af554e8bf02b6192d1e5eba6e6402cf93900c3d0ac86391d00d04876789e
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10c0/c25b6dc1598790d5b55c0947a9b7d111cfa92594db5296c3b907e2f533c033666f692a3939eadac17b1c7c40d362d0b0635dc874cbfe3e70db7c2b07cc97a5d2
  languageName: node
  linkType: hard

"@istanbuljs/schema@npm:^0.1.2":
  version: 0.1.3
  resolution: "@istanbuljs/schema@npm:0.1.3"
  checksum: 10c0/61c5286771676c9ca3eb2bd8a7310a9c063fb6e0e9712225c8471c582d157392c88f5353581c8c9adbe0dff98892317d2fdfc56c3499aa42e0194405206a963a
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.8
  resolution: "@jridgewell/gen-mapping@npm:0.3.8"
  dependencies:
    "@jridgewell/set-array": "npm:^1.2.1"
    "@jridgewell/sourcemap-codec": "npm:^1.4.10"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/c668feaf86c501d7c804904a61c23c67447b2137b813b9ce03eca82cb9d65ac7006d766c218685d76e3d72828279b6ee26c347aa1119dab23fbaf36aed51585a
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 10c0/d502e6fb516b35032331406d4e962c21fe77cdf1cbdb49c6142bcbd9e30507094b18972778a6e27cbad756209cfe34b1a27729e6fa08a2eb92b33943f680cf1e
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 10c0/2a5aa7b4b5c3464c895c802d8ae3f3d2b92fcbe84ad12f8d0bfbb1f5ad006717e7577ee1fd2eac00c088abe486c7adb27976f45d2941ff6b0b92b2c3302c60f4
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14, @jridgewell/sourcemap-codec@npm:^1.5.0":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 10c0/2eb864f276eb1096c3c11da3e9bb518f6d9fc0023c78344cdc037abadc725172c70314bdb360f2d4b7bffec7f5d657ce006816bc5d4ecb35e61b66132db00c18
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.23, @jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.1.0"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
  checksum: 10c0/3d1ce6ebc69df9682a5a8896b414c6537e428a1d68b02fcc8363b04284a8ca0df04d0ee3013132252ab14f2527bc13bea6526a912ecb5658f0e39fd2860b4df4
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 10c0/732c3b6d1b1e967440e65f284bd06e5821fedf10a1bea9ed2bb75956ea1f30e08c44d3def9d6a230666574edbaf136f8cfd319c14fd1f87c66e6a44449afb2eb
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 10c0/88dafe5e3e29a388b07264680dc996c17f4bda48d163a9d4f5c1112979f0ce8ec72aa7116122c350b4e7976bc5566dc3ddb579be1ceaacc727872eb4ed93926d
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 10c0/db9de047c3bb9b51f9335a7bb46f4fcfb6829fb628318c12115fbaf7d369bfce71c15b103d1fc3b464812d936220ee9bc1c8f762d032c9f6be9acc99249095b1
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10c0/efe37b982f30740ee77696a80c196912c274ecd2cb243bc6ae7053a50c733ce0f6c09fda085145f33ecf453be19654acca74b69e81eaad4c90f00ccffe2f9271
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10c0/c90935d5ce670c87b6b14fab04a965a3b8137e585f8b2a6257263bd7f97756dd736cb165bb470e5156a9e718ecd99413dccc54b1138c1a46d6ec7cf325982fe5
  languageName: node
  linkType: hard

"@parcel/watcher-android-arm64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-android-arm64@npm:2.5.1"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@parcel/watcher-darwin-arm64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-darwin-arm64@npm:2.5.1"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@parcel/watcher-darwin-x64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-darwin-x64@npm:2.5.1"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@parcel/watcher-freebsd-x64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-freebsd-x64@npm:2.5.1"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm-glibc@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-arm-glibc@npm:2.5.1"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm-musl@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-arm-musl@npm:2.5.1"
  conditions: os=linux & cpu=arm & libc=musl
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm64-glibc@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-arm64-glibc@npm:2.5.1"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm64-musl@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-arm64-musl@npm:2.5.1"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@parcel/watcher-linux-x64-glibc@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-x64-glibc@npm:2.5.1"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@parcel/watcher-linux-x64-musl@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-x64-musl@npm:2.5.1"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@parcel/watcher-win32-arm64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-win32-arm64@npm:2.5.1"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@parcel/watcher-win32-ia32@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-win32-ia32@npm:2.5.1"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@parcel/watcher-win32-x64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-win32-x64@npm:2.5.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@parcel/watcher@npm:^2.4.1":
  version: 2.5.1
  resolution: "@parcel/watcher@npm:2.5.1"
  dependencies:
    "@parcel/watcher-android-arm64": "npm:2.5.1"
    "@parcel/watcher-darwin-arm64": "npm:2.5.1"
    "@parcel/watcher-darwin-x64": "npm:2.5.1"
    "@parcel/watcher-freebsd-x64": "npm:2.5.1"
    "@parcel/watcher-linux-arm-glibc": "npm:2.5.1"
    "@parcel/watcher-linux-arm-musl": "npm:2.5.1"
    "@parcel/watcher-linux-arm64-glibc": "npm:2.5.1"
    "@parcel/watcher-linux-arm64-musl": "npm:2.5.1"
    "@parcel/watcher-linux-x64-glibc": "npm:2.5.1"
    "@parcel/watcher-linux-x64-musl": "npm:2.5.1"
    "@parcel/watcher-win32-arm64": "npm:2.5.1"
    "@parcel/watcher-win32-ia32": "npm:2.5.1"
    "@parcel/watcher-win32-x64": "npm:2.5.1"
    detect-libc: "npm:^1.0.3"
    is-glob: "npm:^4.0.3"
    micromatch: "npm:^4.0.5"
    node-addon-api: "npm:^7.0.0"
    node-gyp: "npm:latest"
  dependenciesMeta:
    "@parcel/watcher-android-arm64":
      optional: true
    "@parcel/watcher-darwin-arm64":
      optional: true
    "@parcel/watcher-darwin-x64":
      optional: true
    "@parcel/watcher-freebsd-x64":
      optional: true
    "@parcel/watcher-linux-arm-glibc":
      optional: true
    "@parcel/watcher-linux-arm-musl":
      optional: true
    "@parcel/watcher-linux-arm64-glibc":
      optional: true
    "@parcel/watcher-linux-arm64-musl":
      optional: true
    "@parcel/watcher-linux-x64-glibc":
      optional: true
    "@parcel/watcher-linux-x64-musl":
      optional: true
    "@parcel/watcher-win32-arm64":
      optional: true
    "@parcel/watcher-win32-ia32":
      optional: true
    "@parcel/watcher-win32-x64":
      optional: true
  checksum: 10c0/8f35073d0c0b34a63d4c8d2213482f0ebc6a25de7b2cdd415d19cb929964a793cb285b68d1d50bfb732b070b3c82a2fdb4eb9c250eab709a1cd9d63345455a82
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10c0/5bd7576bb1b38a47a7fc7b51ac9f38748e772beebc56200450c4a817d712232b8f1d3ef70532c80840243c657d491cf6a6be1e3a214cff907645819fdc34aadd
  languageName: node
  linkType: hard

"@polka/url@npm:^1.0.0-next.24":
  version: 1.0.0-next.28
  resolution: "@polka/url@npm:1.0.0-next.28"
  checksum: 10c0/acc5ea62597e4da2fb42dbee02749d07f102ae7d6d2c966bf7e423c79cd65d1621da305af567e6e7c232f3b565e242d1ec932cbb3dcc0db1508d02e9a2cafa2e
  languageName: node
  linkType: hard

"@reactflow/background@npm:11.3.14":
  version: 11.3.14
  resolution: "@reactflow/background@npm:11.3.14"
  dependencies:
    "@reactflow/core": "npm:11.11.4"
    classcat: "npm:^5.0.3"
    zustand: "npm:^4.4.1"
  peerDependencies:
    react: ">=17"
    react-dom: ">=17"
  checksum: 10c0/a4ef8ca38f9cfd72d578dd2b2672d5bf841410f5a399e0c1d7bf4ea10e5d76003dec2fee15f927d4d5f121fc4414c811f2c4b9e702d28e31a4bd9399fc8cbe29
  languageName: node
  linkType: hard

"@reactflow/controls@npm:11.2.14":
  version: 11.2.14
  resolution: "@reactflow/controls@npm:11.2.14"
  dependencies:
    "@reactflow/core": "npm:11.11.4"
    classcat: "npm:^5.0.3"
    zustand: "npm:^4.4.1"
  peerDependencies:
    react: ">=17"
    react-dom: ">=17"
  checksum: 10c0/6cd79e66590e301e5b3034dccfa1dcfb01fe27bbd1b8aadf8afaa8a0f68e3abe1c3d4a90a6e1ea54eecf5acb019073ead7379fbbf1252af06a63e413f82a74dc
  languageName: node
  linkType: hard

"@reactflow/core@npm:11.11.4":
  version: 11.11.4
  resolution: "@reactflow/core@npm:11.11.4"
  dependencies:
    "@types/d3": "npm:^7.4.0"
    "@types/d3-drag": "npm:^3.0.1"
    "@types/d3-selection": "npm:^3.0.3"
    "@types/d3-zoom": "npm:^3.0.1"
    classcat: "npm:^5.0.3"
    d3-drag: "npm:^3.0.0"
    d3-selection: "npm:^3.0.0"
    d3-zoom: "npm:^3.0.0"
    zustand: "npm:^4.4.1"
  peerDependencies:
    react: ">=17"
    react-dom: ">=17"
  checksum: 10c0/895a0c1491ba81a6ed06ee9f2619870083753c6faf26ad6cb37a5c27f2f2fa80ab88d4be5c77fb7cad17ba6b20b212ea3171a976f3817056196cf85f6d90e86c
  languageName: node
  linkType: hard

"@reactflow/minimap@npm:11.7.14":
  version: 11.7.14
  resolution: "@reactflow/minimap@npm:11.7.14"
  dependencies:
    "@reactflow/core": "npm:11.11.4"
    "@types/d3-selection": "npm:^3.0.3"
    "@types/d3-zoom": "npm:^3.0.1"
    classcat: "npm:^5.0.3"
    d3-selection: "npm:^3.0.0"
    d3-zoom: "npm:^3.0.0"
    zustand: "npm:^4.4.1"
  peerDependencies:
    react: ">=17"
    react-dom: ">=17"
  checksum: 10c0/5bc99ab12e4daafacfb3d60e36c7d2253cfd7cd7ba5765a9ff7c35a3a6835c49cae9aa1acde181baf5b2de3e2c48226758f5a61125ecf7014a9d58fe6ef62bb6
  languageName: node
  linkType: hard

"@reactflow/node-resizer@npm:2.2.14":
  version: 2.2.14
  resolution: "@reactflow/node-resizer@npm:2.2.14"
  dependencies:
    "@reactflow/core": "npm:11.11.4"
    classcat: "npm:^5.0.4"
    d3-drag: "npm:^3.0.0"
    d3-selection: "npm:^3.0.0"
    zustand: "npm:^4.4.1"
  peerDependencies:
    react: ">=17"
    react-dom: ">=17"
  checksum: 10c0/094905bc9eb4ec9e20ebe732983a44895822f7fae56c8d348b478bbee776342b286f08578c57eeeb5408a931febced32a5fef68163755b37fc19ca523b78e0dd
  languageName: node
  linkType: hard

"@reactflow/node-toolbar@npm:1.3.14":
  version: 1.3.14
  resolution: "@reactflow/node-toolbar@npm:1.3.14"
  dependencies:
    "@reactflow/core": "npm:11.11.4"
    classcat: "npm:^5.0.3"
    zustand: "npm:^4.4.1"
  peerDependencies:
    react: ">=17"
    react-dom: ">=17"
  checksum: 10c0/cd29257969a11feb273849d3aa540d534513b292115ebf697db1357fae704426be899dcf882ea36e13a33d49073b6c2b44fc67c4ddbc779bf2dcd49ba87053a3
  languageName: node
  linkType: hard

"@rolldown/pluginutils@npm:1.0.0-beta.11":
  version: 1.0.0-beta.11
  resolution: "@rolldown/pluginutils@npm:1.0.0-beta.11"
  checksum: 10c0/140088e33a4dd3bc21d06fa0cbe79b52e95487c9737d425aa5729e52446dc70f066fbce632489a53e45bb567f1e86c19835677c98fe5d4123ae1e2fef53f8d97
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm-eabi@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-android-arm-eabi@npm:4.43.0"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm64@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-android-arm64@npm:4.43.0"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-arm64@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-darwin-arm64@npm:4.43.0"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-x64@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-darwin-x64@npm:4.43.0"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-arm64@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-freebsd-arm64@npm:4.43.0"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-x64@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-freebsd-x64@npm:4.43.0"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-gnueabihf@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-linux-arm-gnueabihf@npm:4.43.0"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-musleabihf@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-linux-arm-musleabihf@npm:4.43.0"
  conditions: os=linux & cpu=arm & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-gnu@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-linux-arm64-gnu@npm:4.43.0"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-musl@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-linux-arm64-musl@npm:4.43.0"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-loongarch64-gnu@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-linux-loongarch64-gnu@npm:4.43.0"
  conditions: os=linux & cpu=loong64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-powerpc64le-gnu@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-linux-powerpc64le-gnu@npm:4.43.0"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-riscv64-gnu@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-linux-riscv64-gnu@npm:4.43.0"
  conditions: os=linux & cpu=riscv64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-riscv64-musl@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-linux-riscv64-musl@npm:4.43.0"
  conditions: os=linux & cpu=riscv64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-s390x-gnu@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-linux-s390x-gnu@npm:4.43.0"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-gnu@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-linux-x64-gnu@npm:4.43.0"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-musl@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-linux-x64-musl@npm:4.43.0"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-win32-arm64-msvc@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-win32-arm64-msvc@npm:4.43.0"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-win32-ia32-msvc@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-win32-ia32-msvc@npm:4.43.0"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@rollup/rollup-win32-x64-msvc@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-win32-x64-msvc@npm:4.43.0"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@swc/helpers@npm:^0.5.0":
  version: 0.5.17
  resolution: "@swc/helpers@npm:0.5.17"
  dependencies:
    tslib: "npm:^2.8.0"
  checksum: 10c0/fe1f33ebb968558c5a0c595e54f2e479e4609bff844f9ca9a2d1ffd8dd8504c26f862a11b031f48f75c95b0381c2966c3dd156e25942f90089badd24341e7dbb
  languageName: node
  linkType: hard

"@tanstack/query-core@npm:5.80.7":
  version: 5.80.7
  resolution: "@tanstack/query-core@npm:5.80.7"
  checksum: 10c0/bd96393e1a94aebc4d10da05e03de89ddce09d7266d0fcff2785480c544425d732f9fb03ecb93c96520980f57655f143452b9701f9bcb620cd07a04077018583
  languageName: node
  linkType: hard

"@tanstack/react-query@npm:^5.79.0":
  version: 5.80.7
  resolution: "@tanstack/react-query@npm:5.80.7"
  dependencies:
    "@tanstack/query-core": "npm:5.80.7"
  peerDependencies:
    react: ^18 || ^19
  checksum: 10c0/2d2fb8a7c60fe94c90f3345050673e217366e968fd28565a92bf4097a320c7c73bc6ecaa1b20ca70a705accbf8618a9888016c5a1813bb57fe86b6c824973a21
  languageName: node
  linkType: hard

"@tanstack/react-table@npm:^8.21.3":
  version: 8.21.3
  resolution: "@tanstack/react-table@npm:8.21.3"
  dependencies:
    "@tanstack/table-core": "npm:8.21.3"
  peerDependencies:
    react: ">=16.8"
    react-dom: ">=16.8"
  checksum: 10c0/85d1d0fcb690ecc011f68a5a61c96f82142e31a0270dcf9cbc699a6f36715b1653fe6ff1518302a6d08b7093351fc4cabefd055a7db3cd8ac01e068956b0f944
  languageName: node
  linkType: hard

"@tanstack/table-core@npm:8.21.3":
  version: 8.21.3
  resolution: "@tanstack/table-core@npm:8.21.3"
  checksum: 10c0/40e3560e6d55e07cc047024aa7f83bd47a9323d21920d4adabba8071fd2d21230c48460b26cedf392588f8265b9edc133abb1b0d6d0adf4dae0970032900a8c9
  languageName: node
  linkType: hard

"@testing-library/dom@npm:10.4.1":
  version: 10.4.1
  resolution: "@testing-library/dom@npm:10.4.1"
  dependencies:
    "@babel/code-frame": "npm:^7.10.4"
    "@babel/runtime": "npm:^7.12.5"
    "@types/aria-query": "npm:^5.0.1"
    aria-query: "npm:5.3.0"
    dom-accessibility-api: "npm:^0.5.9"
    lz-string: "npm:^1.5.0"
    picocolors: "npm:1.1.1"
    pretty-format: "npm:^27.0.2"
  checksum: 10c0/19ce048012d395ad0468b0dbcc4d0911f6f9e39464d7a8464a587b29707eed5482000dad728f5acc4ed314d2f4d54f34982999a114d2404f36d048278db815b1
  languageName: node
  linkType: hard

"@testing-library/jest-dom@npm:6.6.4":
  version: 6.6.4
  resolution: "@testing-library/jest-dom@npm:6.6.4"
  dependencies:
    "@adobe/css-tools": "npm:^4.4.0"
    aria-query: "npm:^5.0.0"
    css.escape: "npm:^1.5.1"
    dom-accessibility-api: "npm:^0.6.3"
    lodash: "npm:^4.17.21"
    picocolors: "npm:^1.1.1"
    redent: "npm:^3.0.0"
  checksum: 10c0/cb73adf4910f654f6cc61cfb9a551efdffa04ef423bc7fbfd67a6d8aa31c6c6dc6363fe9db23a35fc7cb32ff1390e6e1c77575c2fa70d8b028a943af32bc214c
  languageName: node
  linkType: hard

"@testing-library/react@npm:16.3.0":
  version: 16.3.0
  resolution: "@testing-library/react@npm:16.3.0"
  dependencies:
    "@babel/runtime": "npm:^7.12.5"
  peerDependencies:
    "@testing-library/dom": ^10.0.0
    "@types/react": ^18.0.0 || ^19.0.0
    "@types/react-dom": ^18.0.0 || ^19.0.0
    react: ^18.0.0 || ^19.0.0
    react-dom: ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/3a2cb1f87c9a67e1ebbbcfd99b94b01e496fc35147be8bc5d8bf07a699c7d523a09d57ef2f7b1d91afccd1a28e21eda3b00d80187fbb51b1de01e422592d845e
  languageName: node
  linkType: hard

"@testing-library/user-event@npm:14.6.1":
  version: 14.6.1
  resolution: "@testing-library/user-event@npm:14.6.1"
  peerDependencies:
    "@testing-library/dom": ">=7.21.4"
  checksum: 10c0/75fea130a52bf320d35d46ed54f3eec77e71a56911b8b69a3fe29497b0b9947b2dc80d30f04054ad4ce7f577856ae3e5397ea7dff0ef14944d3909784c7a93fe
  languageName: node
  linkType: hard

"@types/aria-query@npm:^5.0.1":
  version: 5.0.4
  resolution: "@types/aria-query@npm:5.0.4"
  checksum: 10c0/dc667bc6a3acc7bba2bccf8c23d56cb1f2f4defaa704cfef595437107efaa972d3b3db9ec1d66bc2711bfc35086821edd32c302bffab36f2e79b97f312069f08
  languageName: node
  linkType: hard

"@types/babel__core@npm:^7.20.5":
  version: 7.20.5
  resolution: "@types/babel__core@npm:7.20.5"
  dependencies:
    "@babel/parser": "npm:^7.20.7"
    "@babel/types": "npm:^7.20.7"
    "@types/babel__generator": "npm:*"
    "@types/babel__template": "npm:*"
    "@types/babel__traverse": "npm:*"
  checksum: 10c0/bdee3bb69951e833a4b811b8ee9356b69a61ed5b7a23e1a081ec9249769117fa83aaaf023bb06562a038eb5845155ff663e2d5c75dd95c1d5ccc91db012868ff
  languageName: node
  linkType: hard

"@types/babel__generator@npm:*":
  version: 7.6.8
  resolution: "@types/babel__generator@npm:7.6.8"
  dependencies:
    "@babel/types": "npm:^7.0.0"
  checksum: 10c0/f0ba105e7d2296bf367d6e055bb22996886c114261e2cb70bf9359556d0076c7a57239d019dee42bb063f565bade5ccb46009bce2044b2952d964bf9a454d6d2
  languageName: node
  linkType: hard

"@types/babel__template@npm:*":
  version: 7.4.4
  resolution: "@types/babel__template@npm:7.4.4"
  dependencies:
    "@babel/parser": "npm:^7.1.0"
    "@babel/types": "npm:^7.0.0"
  checksum: 10c0/cc84f6c6ab1eab1427e90dd2b76ccee65ce940b778a9a67be2c8c39e1994e6f5bbc8efa309f6cea8dc6754994524cd4d2896558df76d92e7a1f46ecffee7112b
  languageName: node
  linkType: hard

"@types/babel__traverse@npm:*":
  version: 7.20.6
  resolution: "@types/babel__traverse@npm:7.20.6"
  dependencies:
    "@babel/types": "npm:^7.20.7"
  checksum: 10c0/7ba7db61a53e28cac955aa99af280d2600f15a8c056619c05b6fc911cbe02c61aa4f2823299221b23ce0cce00b294c0e5f618ec772aa3f247523c2e48cf7b888
  languageName: node
  linkType: hard

"@types/chai@npm:^5.2.2":
  version: 5.2.2
  resolution: "@types/chai@npm:5.2.2"
  dependencies:
    "@types/deep-eql": "npm:*"
  checksum: 10c0/49282bf0e8246800ebb36f17256f97bd3a8c4fb31f92ad3c0eaa7623518d7e87f1eaad4ad206960fcaf7175854bdff4cb167e4fe96811e0081b4ada83dd533ec
  languageName: node
  linkType: hard

"@types/d3-array@npm:*":
  version: 3.2.1
  resolution: "@types/d3-array@npm:3.2.1"
  checksum: 10c0/38bf2c778451f4b79ec81a2288cb4312fe3d6449ecdf562970cc339b60f280f31c93a024c7ff512607795e79d3beb0cbda123bb07010167bce32927f71364bca
  languageName: node
  linkType: hard

"@types/d3-axis@npm:*":
  version: 3.0.6
  resolution: "@types/d3-axis@npm:3.0.6"
  dependencies:
    "@types/d3-selection": "npm:*"
  checksum: 10c0/d756d42360261f44d8eefd0950c5bb0a4f67a46dd92069da3f723ac36a1e8cb2b9ce6347d836ef19d5b8aef725dbcf8fdbbd6cfbff676ca4b0642df2f78b599a
  languageName: node
  linkType: hard

"@types/d3-brush@npm:*":
  version: 3.0.6
  resolution: "@types/d3-brush@npm:3.0.6"
  dependencies:
    "@types/d3-selection": "npm:*"
  checksum: 10c0/fd6e2ac7657a354f269f6b9c58451ffae9d01b89ccb1eb6367fd36d635d2f1990967215ab498e0c0679ff269429c57fad6a2958b68f4d45bc9f81d81672edc01
  languageName: node
  linkType: hard

"@types/d3-chord@npm:*":
  version: 3.0.6
  resolution: "@types/d3-chord@npm:3.0.6"
  checksum: 10c0/c5a25eb5389db01e63faec0c5c2ec7cc41c494e9b3201630b494c4e862a60f1aa83fabbc33a829e7e1403941e3c30d206c741559b14406ac2a4239cfdf4b4c17
  languageName: node
  linkType: hard

"@types/d3-color@npm:*":
  version: 3.1.3
  resolution: "@types/d3-color@npm:3.1.3"
  checksum: 10c0/65eb0487de606eb5ad81735a9a5b3142d30bc5ea801ed9b14b77cb14c9b909f718c059f13af341264ee189acf171508053342142bdf99338667cea26a2d8d6ae
  languageName: node
  linkType: hard

"@types/d3-contour@npm:*":
  version: 3.0.6
  resolution: "@types/d3-contour@npm:3.0.6"
  dependencies:
    "@types/d3-array": "npm:*"
    "@types/geojson": "npm:*"
  checksum: 10c0/e7d83e94719af4576ceb5ac7f277c5806f83ba6c3631744ae391cffc3641f09dfa279470b83053cd0b2acd6784e8749c71141d05bdffa63ca58ffb5b31a0f27c
  languageName: node
  linkType: hard

"@types/d3-delaunay@npm:*":
  version: 6.0.4
  resolution: "@types/d3-delaunay@npm:6.0.4"
  checksum: 10c0/d154a8864f08c4ea23ecb9bdabcef1c406a25baa8895f0cb08a0ed2799de0d360e597552532ce7086ff0cdffa8f3563f9109d18f0191459d32bb620a36939123
  languageName: node
  linkType: hard

"@types/d3-dispatch@npm:*":
  version: 3.0.6
  resolution: "@types/d3-dispatch@npm:3.0.6"
  checksum: 10c0/405eb7d0ec139fbf72fa6a43b0f3ca8a1f913bb2cb38f607827e63fca8d4393f021f32f3b96b33c93ddbd37789453a0b3624f14f504add5308fd9aec8a46dda0
  languageName: node
  linkType: hard

"@types/d3-drag@npm:*, @types/d3-drag@npm:^3.0.1":
  version: 3.0.7
  resolution: "@types/d3-drag@npm:3.0.7"
  dependencies:
    "@types/d3-selection": "npm:*"
  checksum: 10c0/65e29fa32a87c72d26c44b5e2df3bf15af21cd128386bcc05bcacca255927c0397d0cd7e6062aed5f0abd623490544a9d061c195f5ed9f018fe0b698d99c079d
  languageName: node
  linkType: hard

"@types/d3-dsv@npm:*":
  version: 3.0.7
  resolution: "@types/d3-dsv@npm:3.0.7"
  checksum: 10c0/c0f01da862465594c8a28278b51c850af3b4239cc22b14fd1a19d7a98f93d94efa477bf59d8071beb285dca45bf614630811451e18e7c52add3a0abfee0a1871
  languageName: node
  linkType: hard

"@types/d3-ease@npm:*":
  version: 3.0.2
  resolution: "@types/d3-ease@npm:3.0.2"
  checksum: 10c0/aff5a1e572a937ee9bff6465225d7ba27d5e0c976bd9eacdac2e6f10700a7cb0c9ea2597aff6b43a6ed850a3210030870238894a77ec73e309b4a9d0333f099c
  languageName: node
  linkType: hard

"@types/d3-fetch@npm:*":
  version: 3.0.7
  resolution: "@types/d3-fetch@npm:3.0.7"
  dependencies:
    "@types/d3-dsv": "npm:*"
  checksum: 10c0/3d147efa52a26da1a5d40d4d73e6cebaaa964463c378068062999b93ea3731b27cc429104c21ecbba98c6090e58ef13429db6399238c5e3500162fb3015697a0
  languageName: node
  linkType: hard

"@types/d3-force@npm:*":
  version: 3.0.10
  resolution: "@types/d3-force@npm:3.0.10"
  checksum: 10c0/c82b459079a106b50e346c9b79b141f599f2fc4f598985a5211e72c7a2e20d35bd5dc6e91f306b323c8bfa325c02c629b1645f5243f1c6a55bd51bc85cccfa92
  languageName: node
  linkType: hard

"@types/d3-format@npm:*":
  version: 3.0.4
  resolution: "@types/d3-format@npm:3.0.4"
  checksum: 10c0/3ac1600bf9061a59a228998f7cd3f29e85cbf522997671ba18d4d84d10a2a1aff4f95aceb143fa9960501c3ec351e113fc75884e6a504ace44dc1744083035ee
  languageName: node
  linkType: hard

"@types/d3-geo@npm:*":
  version: 3.1.0
  resolution: "@types/d3-geo@npm:3.1.0"
  dependencies:
    "@types/geojson": "npm:*"
  checksum: 10c0/3745a93439038bb5b0b38facf435f7079812921d46406f5d38deaee59e90084ff742443c7ea0a8446df81a0d81eaf622fe7068cf4117a544bd4aa3b2dc182f88
  languageName: node
  linkType: hard

"@types/d3-hierarchy@npm:*":
  version: 3.1.7
  resolution: "@types/d3-hierarchy@npm:3.1.7"
  checksum: 10c0/873711737d6b8e7b6f1dda0bcd21294a48f75024909ae510c5d2c21fad2e72032e0958def4d9f68319d3aaac298ad09c49807f8bfc87a145a82693b5208613c7
  languageName: node
  linkType: hard

"@types/d3-interpolate@npm:*":
  version: 3.0.4
  resolution: "@types/d3-interpolate@npm:3.0.4"
  dependencies:
    "@types/d3-color": "npm:*"
  checksum: 10c0/066ebb8da570b518dd332df6b12ae3b1eaa0a7f4f0c702e3c57f812cf529cc3500ec2aac8dc094f31897790346c6b1ebd8cd7a077176727f4860c2b181a65ca4
  languageName: node
  linkType: hard

"@types/d3-path@npm:*":
  version: 3.1.1
  resolution: "@types/d3-path@npm:3.1.1"
  checksum: 10c0/2c36eb31ebaf2ce4712e793fd88087117976f7c4ed69cc2431825f999c8c77cca5cea286f3326432b770739ac6ccd5d04d851eb65e7a4dbcc10c982b49ad2c02
  languageName: node
  linkType: hard

"@types/d3-polygon@npm:*":
  version: 3.0.2
  resolution: "@types/d3-polygon@npm:3.0.2"
  checksum: 10c0/f46307bb32b6c2aef8c7624500e0f9b518de8f227ccc10170b869dc43e4c542560f6c8d62e9f087fac45e198d6e4b623e579c0422e34c85baf56717456d3f439
  languageName: node
  linkType: hard

"@types/d3-quadtree@npm:*":
  version: 3.0.6
  resolution: "@types/d3-quadtree@npm:3.0.6"
  checksum: 10c0/7eaa0a4d404adc856971c9285e1c4ab17e9135ea669d847d6db7e0066126a28ac751864e7ce99c65d526e130f56754a2e437a1617877098b3bdcc3ef23a23616
  languageName: node
  linkType: hard

"@types/d3-random@npm:*":
  version: 3.0.3
  resolution: "@types/d3-random@npm:3.0.3"
  checksum: 10c0/5f4fea40080cd6d4adfee05183d00374e73a10c530276a6455348983dda341003a251def28565a27c25d9cf5296a33e870e397c9d91ff83fb7495a21c96b6882
  languageName: node
  linkType: hard

"@types/d3-scale-chromatic@npm:*":
  version: 3.1.0
  resolution: "@types/d3-scale-chromatic@npm:3.1.0"
  checksum: 10c0/93c564e02d2e97a048e18fe8054e4a935335da6ab75a56c3df197beaa87e69122eef0dfbeb7794d4a444a00e52e3123514ee27cec084bd21f6425b7037828cc2
  languageName: node
  linkType: hard

"@types/d3-scale@npm:*":
  version: 4.0.9
  resolution: "@types/d3-scale@npm:4.0.9"
  dependencies:
    "@types/d3-time": "npm:*"
  checksum: 10c0/4ac44233c05cd50b65b33ecb35d99fdf07566bcdbc55bc1306b2f27d1c5134d8c560d356f2c8e76b096e9125ffb8d26d95f78d56e210d1c542cb255bdf31d6c8
  languageName: node
  linkType: hard

"@types/d3-selection@npm:*, @types/d3-selection@npm:^3.0.3":
  version: 3.0.11
  resolution: "@types/d3-selection@npm:3.0.11"
  checksum: 10c0/0c512956c7503ff5def4bb32e0c568cc757b9a2cc400a104fc0f4cfe5e56d83ebde2a97821b6f2cb26a7148079d3b86a2f28e11d68324ed311cf35c2ed980d1d
  languageName: node
  linkType: hard

"@types/d3-shape@npm:*":
  version: 3.1.7
  resolution: "@types/d3-shape@npm:3.1.7"
  dependencies:
    "@types/d3-path": "npm:*"
  checksum: 10c0/38e59771c1c4c83b67aa1f941ce350410522a149d2175832fdc06396b2bb3b2c1a2dd549e0f8230f9f24296ee5641a515eaf10f55ee1ef6c4f83749e2dd7dcfd
  languageName: node
  linkType: hard

"@types/d3-time-format@npm:*":
  version: 4.0.3
  resolution: "@types/d3-time-format@npm:4.0.3"
  checksum: 10c0/9ef5e8e2b96b94799b821eed5d61a3d432c7903247966d8ad951b8ce5797fe46554b425cb7888fa5bf604b4663c369d7628c0328ffe80892156671c58d1a7f90
  languageName: node
  linkType: hard

"@types/d3-time@npm:*":
  version: 3.0.4
  resolution: "@types/d3-time@npm:3.0.4"
  checksum: 10c0/6d9e2255d63f7a313a543113920c612e957d70da4fb0890931da6c2459010291b8b1f95e149a538500c1c99e7e6c89ffcce5554dd29a31ff134a38ea94b6d174
  languageName: node
  linkType: hard

"@types/d3-timer@npm:*":
  version: 3.0.2
  resolution: "@types/d3-timer@npm:3.0.2"
  checksum: 10c0/c644dd9571fcc62b1aa12c03bcad40571553020feeb5811f1d8a937ac1e65b8a04b759b4873aef610e28b8714ac71c9885a4d6c127a048d95118f7e5b506d9e1
  languageName: node
  linkType: hard

"@types/d3-transition@npm:*":
  version: 3.0.9
  resolution: "@types/d3-transition@npm:3.0.9"
  dependencies:
    "@types/d3-selection": "npm:*"
  checksum: 10c0/4f68b9df7ac745b3491216c54203cbbfa0f117ae4c60e2609cdef2db963582152035407fdff995b10ee383bae2f05b7743493f48e1b8e46df54faa836a8fb7b5
  languageName: node
  linkType: hard

"@types/d3-zoom@npm:*, @types/d3-zoom@npm:^3.0.1":
  version: 3.0.8
  resolution: "@types/d3-zoom@npm:3.0.8"
  dependencies:
    "@types/d3-interpolate": "npm:*"
    "@types/d3-selection": "npm:*"
  checksum: 10c0/1dbdbcafddcae12efb5beb6948546963f29599e18bc7f2a91fb69cc617c2299a65354f2d47e282dfb86fec0968406cd4fb7f76ba2d2fb67baa8e8d146eb4a547
  languageName: node
  linkType: hard

"@types/d3@npm:^7.4.0, @types/d3@npm:^7.4.3":
  version: 7.4.3
  resolution: "@types/d3@npm:7.4.3"
  dependencies:
    "@types/d3-array": "npm:*"
    "@types/d3-axis": "npm:*"
    "@types/d3-brush": "npm:*"
    "@types/d3-chord": "npm:*"
    "@types/d3-color": "npm:*"
    "@types/d3-contour": "npm:*"
    "@types/d3-delaunay": "npm:*"
    "@types/d3-dispatch": "npm:*"
    "@types/d3-drag": "npm:*"
    "@types/d3-dsv": "npm:*"
    "@types/d3-ease": "npm:*"
    "@types/d3-fetch": "npm:*"
    "@types/d3-force": "npm:*"
    "@types/d3-format": "npm:*"
    "@types/d3-geo": "npm:*"
    "@types/d3-hierarchy": "npm:*"
    "@types/d3-interpolate": "npm:*"
    "@types/d3-path": "npm:*"
    "@types/d3-polygon": "npm:*"
    "@types/d3-quadtree": "npm:*"
    "@types/d3-random": "npm:*"
    "@types/d3-scale": "npm:*"
    "@types/d3-scale-chromatic": "npm:*"
    "@types/d3-selection": "npm:*"
    "@types/d3-shape": "npm:*"
    "@types/d3-time": "npm:*"
    "@types/d3-time-format": "npm:*"
    "@types/d3-timer": "npm:*"
    "@types/d3-transition": "npm:*"
    "@types/d3-zoom": "npm:*"
  checksum: 10c0/a9c6d65b13ef3b42c87f2a89ea63a6d5640221869f97d0657b0cb2f1dac96a0f164bf5605643c0794e0de3aa2bf05df198519aaf15d24ca135eb0e8bd8a9d879
  languageName: node
  linkType: hard

"@types/deep-eql@npm:*":
  version: 4.0.2
  resolution: "@types/deep-eql@npm:4.0.2"
  checksum: 10c0/bf3f811843117900d7084b9d0c852da9a044d12eb40e6de73b552598a6843c21291a8a381b0532644574beecd5e3491c5ff3a0365ab86b15d59862c025384844
  languageName: node
  linkType: hard

"@types/estree@npm:1.0.7":
  version: 1.0.7
  resolution: "@types/estree@npm:1.0.7"
  checksum: 10c0/be815254316882f7c40847336cd484c3bc1c3e34f710d197160d455dc9d6d050ffbf4c3bc76585dba86f737f020ab20bdb137ebe0e9116b0c86c7c0342221b8c
  languageName: node
  linkType: hard

"@types/estree@npm:^1.0.0, @types/estree@npm:^1.0.6":
  version: 1.0.6
  resolution: "@types/estree@npm:1.0.6"
  checksum: 10c0/cdfd751f6f9065442cd40957c07fd80361c962869aa853c1c2fd03e101af8b9389d8ff4955a43a6fcfa223dd387a089937f95be0f3eec21ca527039fd2d9859a
  languageName: node
  linkType: hard

"@types/geojson@npm:*":
  version: 7946.0.16
  resolution: "@types/geojson@npm:7946.0.16"
  checksum: 10c0/1ff24a288bd5860b766b073ead337d31d73bdc715e5b50a2cee5cb0af57a1ed02cc04ef295f5fa68dc40fe3e4f104dd31282b2b818a5ba3231bc1001ba084e3c
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.15":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 10c0/a996a745e6c5d60292f36731dd41341339d4eeed8180bb09226e5c8d23759067692b1d88e5d91d72ee83dfc00d3aca8e7bd43ea120516c17922cbcb7c3e252db
  languageName: node
  linkType: hard

"@types/luxon@npm:3.7.1":
  version: 3.7.1
  resolution: "@types/luxon@npm:3.7.1"
  checksum: 10c0/2db30c13b58adcd86daa447faa3ba59515fe907ead8ee3e6bb716d662812af0619d712f6c1eb190cdd7f9d2c00444c3ecd80af0f36e8143eb0c5e7339d6b2aca
  languageName: node
  linkType: hard

"@types/minimatch@npm:^3.0.3":
  version: 3.0.5
  resolution: "@types/minimatch@npm:3.0.5"
  checksum: 10c0/a1a19ba342d6f39b569510f621ae4bbe972dc9378d15e9a5e47904c440ee60744f5b09225bc73be1c6490e3a9c938eee69eb53debf55ce1f15761201aa965f97
  languageName: node
  linkType: hard

"@types/node@npm:22.15.31":
  version: 22.15.31
  resolution: "@types/node@npm:22.15.31"
  dependencies:
    undici-types: "npm:~6.21.0"
  checksum: 10c0/ef7d5dc890da41cfd554d35ab8998bc18be9e3a0caa642e720599ac4410a94a4879766e52b3c9cafa06c66b7b8aebdc51f322cf67df23a6489927890196a316d
  languageName: node
  linkType: hard

"@types/raf@npm:^3.4.0":
  version: 3.4.3
  resolution: "@types/raf@npm:3.4.3"
  checksum: 10c0/dea835f0daa399c51db9137f5337dc08a2b4a5f61f645658966ecabaebbbd0fd59551f384a1141e14e22a1cc5a591da7d4d88c60a525ad1399108b6dd2641d75
  languageName: node
  linkType: hard

"@types/react-dom@npm:19.1.6":
  version: 19.1.6
  resolution: "@types/react-dom@npm:19.1.6"
  peerDependencies:
    "@types/react": ^19.0.0
  checksum: 10c0/7ba74eee2919e3f225e898b65fdaa16e54952aaf9e3472a080ddc82ca54585e46e60b3c52018d21d4b7053f09d27b8293e9f468b85f9932ff452cd290cc131e8
  languageName: node
  linkType: hard

"@types/react-grid-layout@npm:1.3.5":
  version: 1.3.5
  resolution: "@types/react-grid-layout@npm:1.3.5"
  dependencies:
    "@types/react": "npm:*"
  checksum: 10c0/abd2a1dda9625c753ff2571a10b69740b2fb9ed1d3141755d54d5814cc12a9701c7c5cd78e8797e945486b441303b82543be71043a32d6a988b57a14237f93c6
  languageName: node
  linkType: hard

"@types/react@npm:*":
  version: 19.0.11
  resolution: "@types/react@npm:19.0.11"
  dependencies:
    csstype: "npm:^3.0.2"
  checksum: 10c0/386cdde579b60e22160d6894a985655742703346bdc71fed9809f8f90ca87e2dd0252e86b81f92621a2c1de8d0fbf35e3963996db8c451cc670570061ed62f29
  languageName: node
  linkType: hard

"@types/react@npm:19.1.8":
  version: 19.1.8
  resolution: "@types/react@npm:19.1.8"
  dependencies:
    csstype: "npm:^3.0.2"
  checksum: 10c0/4908772be6dc941df276931efeb0e781777fa76e4d5d12ff9f75eb2dcc2db3065e0100efde16fde562c5bafa310cc8f50c1ee40a22640459e066e72cd342143e
  languageName: node
  linkType: hard

"@types/symlink-or-copy@npm:^1.2.0":
  version: 1.2.2
  resolution: "@types/symlink-or-copy@npm:1.2.2"
  checksum: 10c0/09ee39bd319f39c775c4e2049fc4018212b9b6043736bd2046537acbb59c98af7aa725c539f32672cd2ca27144073fa1f1101b4c0ed7db3fbddb305b638895b2
  languageName: node
  linkType: hard

"@types/topojson-client@npm:*":
  version: 3.1.5
  resolution: "@types/topojson-client@npm:3.1.5"
  dependencies:
    "@types/geojson": "npm:*"
    "@types/topojson-specification": "npm:*"
  checksum: 10c0/f60c71a79ec2d6387a9437a2d3d61d1a39928211c8b26e6a4572e4e65ff6d9ad283caa2207c447fe67cb20259c72f53ce21e222cd5c7af6f31b18cce0ab79b5d
  languageName: node
  linkType: hard

"@types/topojson-server@npm:*":
  version: 3.0.4
  resolution: "@types/topojson-server@npm:3.0.4"
  dependencies:
    "@types/geojson": "npm:*"
    "@types/topojson-specification": "npm:*"
  checksum: 10c0/671aae8b56b93e7f80c1dfcae78b6afe8a3d1d0c3753c2264d8502a2a2cdc677728e228219cb9ed4818a5c209e02d665778e6c57baf966e4da4292f998ea6f80
  languageName: node
  linkType: hard

"@types/topojson-simplify@npm:*":
  version: 3.0.3
  resolution: "@types/topojson-simplify@npm:3.0.3"
  dependencies:
    "@types/geojson": "npm:*"
    "@types/topojson-specification": "npm:*"
  checksum: 10c0/0eb6a3b6be0e324e668edf5af03c9813c031ff04f97ab957d35e7ef1b1b464d2cc61a323d89a70895a675473eb560436265815d9bccf8372f75253c27b0954f8
  languageName: node
  linkType: hard

"@types/topojson-specification@npm:*":
  version: 1.0.5
  resolution: "@types/topojson-specification@npm:1.0.5"
  dependencies:
    "@types/geojson": "npm:*"
  checksum: 10c0/015f5470b210cb2a4b9cc3fb5b411b2f774189663bdf70855a7ab8e56d55f2b23124d1f8bf345356407691f4a70a85e87d05512b0ffc90d4f5ee81ab96af180b
  languageName: node
  linkType: hard

"@types/topojson@npm:^3.2.6":
  version: 3.2.6
  resolution: "@types/topojson@npm:3.2.6"
  dependencies:
    "@types/geojson": "npm:*"
    "@types/topojson-client": "npm:*"
    "@types/topojson-server": "npm:*"
    "@types/topojson-simplify": "npm:*"
    "@types/topojson-specification": "npm:*"
  checksum: 10c0/98c42d89bfd3ac3d4b37dbf01113a2880389dba1e82237762d4dc12922afc923daf184c0a7c8554eff0ed72c98b46ea3219d0335107dc304caba203ca825f156
  languageName: node
  linkType: hard

"@types/trusted-types@npm:^2.0.7":
  version: 2.0.7
  resolution: "@types/trusted-types@npm:2.0.7"
  checksum: 10c0/4c4855f10de7c6c135e0d32ce462419d8abbbc33713b31d294596c0cc34ae1fa6112a2f9da729c8f7a20707782b0d69da3b1f8df6645b0366d08825ca1522e0c
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:8.34.0":
  version: 8.34.0
  resolution: "@typescript-eslint/eslint-plugin@npm:8.34.0"
  dependencies:
    "@eslint-community/regexpp": "npm:^4.10.0"
    "@typescript-eslint/scope-manager": "npm:8.34.0"
    "@typescript-eslint/type-utils": "npm:8.34.0"
    "@typescript-eslint/utils": "npm:8.34.0"
    "@typescript-eslint/visitor-keys": "npm:8.34.0"
    graphemer: "npm:^1.4.0"
    ignore: "npm:^7.0.0"
    natural-compare: "npm:^1.4.0"
    ts-api-utils: "npm:^2.1.0"
  peerDependencies:
    "@typescript-eslint/parser": ^8.34.0
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/905a05d15f4b0367838ec445f9890321d87470198bf7a589278fc0f38c82cf3ccc1efce4acd3c9c94ee6149d5579ef58606fb7c50f4db50c830de65af8c27c6d
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:8.34.0":
  version: 8.34.0
  resolution: "@typescript-eslint/parser@npm:8.34.0"
  dependencies:
    "@typescript-eslint/scope-manager": "npm:8.34.0"
    "@typescript-eslint/types": "npm:8.34.0"
    "@typescript-eslint/typescript-estree": "npm:8.34.0"
    "@typescript-eslint/visitor-keys": "npm:8.34.0"
    debug: "npm:^4.3.4"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/a829be00ea3455c1e50983c8b44476fbfc9329d019764e407c4d591a95dbd168f83f13e309751242bb4fdc02f89cb51ca5cdc912a12b10f69eebcb1c46dcc39b
  languageName: node
  linkType: hard

"@typescript-eslint/project-service@npm:8.34.0":
  version: 8.34.0
  resolution: "@typescript-eslint/project-service@npm:8.34.0"
  dependencies:
    "@typescript-eslint/tsconfig-utils": "npm:^8.34.0"
    "@typescript-eslint/types": "npm:^8.34.0"
    debug: "npm:^4.3.4"
  peerDependencies:
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/88e64b8daf7db9603277fcbeb9e585e70ec6d6e34fa10d4b60f421e48081cc7c1f6acb01e1ee9dd95e10c0601f164c1defbfe6c9d1edc9822089bb72dbb0fc80
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:8.34.0":
  version: 8.34.0
  resolution: "@typescript-eslint/scope-manager@npm:8.34.0"
  dependencies:
    "@typescript-eslint/types": "npm:8.34.0"
    "@typescript-eslint/visitor-keys": "npm:8.34.0"
  checksum: 10c0/35af36bddc4c227cb0bac42192c40b38179ced30866b6aac642781e21c3f3b1c72051eb4f685d7c99517c3296dd6ba83dd8360e4072e8dcf604aae266eece1b4
  languageName: node
  linkType: hard

"@typescript-eslint/tsconfig-utils@npm:8.34.0":
  version: 8.34.0
  resolution: "@typescript-eslint/tsconfig-utils@npm:8.34.0"
  peerDependencies:
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/98246f89d169d3feb453a6a8552c51d10225cb00c4ff1501549b7846e564ad0e218b644cd94ce779dceed07dcb9035c53fd32186b4c0223b7b2a1f7295b120c3
  languageName: node
  linkType: hard

"@typescript-eslint/tsconfig-utils@npm:^8.34.0":
  version: 8.34.1
  resolution: "@typescript-eslint/tsconfig-utils@npm:8.34.1"
  peerDependencies:
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/8d1ead8b7c279b48e2ed96f083ec119a9aeea1ca9cdd40576ec271b996b9fd8cfa0ddb0aafbb4e14bc27fc62c69c5be66d39b1de68eab9ddd7f1861da267423d
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:8.34.0":
  version: 8.34.0
  resolution: "@typescript-eslint/type-utils@npm:8.34.0"
  dependencies:
    "@typescript-eslint/typescript-estree": "npm:8.34.0"
    "@typescript-eslint/utils": "npm:8.34.0"
    debug: "npm:^4.3.4"
    ts-api-utils: "npm:^2.1.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/7c25d7f4186411190142390467160e81384d400cfb21183d8a305991c723da0a74e5528cdce30b5f2cb6d9d2f6af7c0981c20c18b45fc084b35632429270ae80
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:8.34.0":
  version: 8.34.0
  resolution: "@typescript-eslint/types@npm:8.34.0"
  checksum: 10c0/5d32b2ac03e4cbc1ac1777a53ee83d6d7887a783363bab4f0a6f7550a9e9df0254971cdf71e13b988e2215f2939e7592404856b8acb086ec63c4479c0225c742
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:^8.34.0":
  version: 8.34.1
  resolution: "@typescript-eslint/types@npm:8.34.1"
  checksum: 10c0/db1b3dce6a70b28ddb13c76fbb5983240d9395656df5f7cbd99bfd9905e39c0dab2132870f01dbc406b48739c437f7d344a879a824cedaba81b91a53110dc23a
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:8.34.0":
  version: 8.34.0
  resolution: "@typescript-eslint/typescript-estree@npm:8.34.0"
  dependencies:
    "@typescript-eslint/project-service": "npm:8.34.0"
    "@typescript-eslint/tsconfig-utils": "npm:8.34.0"
    "@typescript-eslint/types": "npm:8.34.0"
    "@typescript-eslint/visitor-keys": "npm:8.34.0"
    debug: "npm:^4.3.4"
    fast-glob: "npm:^3.3.2"
    is-glob: "npm:^4.0.3"
    minimatch: "npm:^9.0.4"
    semver: "npm:^7.6.0"
    ts-api-utils: "npm:^2.1.0"
  peerDependencies:
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/e678982b0009e895aee2b4ccc55bb9ea5473a32e846a97c63d0c6a978c72e1a29e506e6a5f9dda45e9b7803e6c3e3abcdf4c316af1c59146abef4e10e0e94129
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:8.34.0":
  version: 8.34.0
  resolution: "@typescript-eslint/utils@npm:8.34.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.7.0"
    "@typescript-eslint/scope-manager": "npm:8.34.0"
    "@typescript-eslint/types": "npm:8.34.0"
    "@typescript-eslint/typescript-estree": "npm:8.34.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/d759cf6f1b1b23d7d8ab922345e7b68b7c829f4bad841164312cfa3a3e8e818b962dd0d96c1aca7fd7c10248d56538d9714df5f3cfec9f159ca0a139feac60b9
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:8.34.0":
  version: 8.34.0
  resolution: "@typescript-eslint/visitor-keys@npm:8.34.0"
  dependencies:
    "@typescript-eslint/types": "npm:8.34.0"
    eslint-visitor-keys: "npm:^4.2.0"
  checksum: 10c0/d50997e921a178589913d08ffe14d02eba40666c90bdc0c9751f2b87ce500598f64027e2d866dfc975647b2f8b907158503d0722d6b1976c8f1cf5dd8e1d6d69
  languageName: node
  linkType: hard

"@uidotdev/usehooks@npm:^2.4.1":
  version: 2.4.1
  resolution: "@uidotdev/usehooks@npm:2.4.1"
  peerDependencies:
    react: ">=18.0.0"
    react-dom: ">=18.0.0"
  checksum: 10c0/181c43fb324dbe4fef9762c61ab4b8235efa48abedf39a9bfeab65872522c43dae789c4f85b82a1164ed7bb18ae7ff25c3a19e7c4e0eb944937ac7f8109cee9b
  languageName: node
  linkType: hard

"@vitejs/plugin-react@npm:4.5.2":
  version: 4.5.2
  resolution: "@vitejs/plugin-react@npm:4.5.2"
  dependencies:
    "@babel/core": "npm:^7.27.4"
    "@babel/plugin-transform-react-jsx-self": "npm:^7.27.1"
    "@babel/plugin-transform-react-jsx-source": "npm:^7.27.1"
    "@rolldown/pluginutils": "npm:1.0.0-beta.11"
    "@types/babel__core": "npm:^7.20.5"
    react-refresh: "npm:^0.17.0"
  peerDependencies:
    vite: ^4.2.0 || ^5.0.0 || ^6.0.0 || ^7.0.0-beta.0
  checksum: 10c0/37c58e6a9c953ab27eb6de42f0d317d26901117d4e4bec067b098c48353065888d240b819efc5b47e325f83532305d3cc51996fd3eb53f8649b199ecc4424746
  languageName: node
  linkType: hard

"@vitest/coverage-v8@npm:3.2.4":
  version: 3.2.4
  resolution: "@vitest/coverage-v8@npm:3.2.4"
  dependencies:
    "@ampproject/remapping": "npm:^2.3.0"
    "@bcoe/v8-coverage": "npm:^1.0.2"
    ast-v8-to-istanbul: "npm:^0.3.3"
    debug: "npm:^4.4.1"
    istanbul-lib-coverage: "npm:^3.2.2"
    istanbul-lib-report: "npm:^3.0.1"
    istanbul-lib-source-maps: "npm:^5.0.6"
    istanbul-reports: "npm:^3.1.7"
    magic-string: "npm:^0.30.17"
    magicast: "npm:^0.3.5"
    std-env: "npm:^3.9.0"
    test-exclude: "npm:^7.0.1"
    tinyrainbow: "npm:^2.0.0"
  peerDependencies:
    "@vitest/browser": 3.2.4
    vitest: 3.2.4
  peerDependenciesMeta:
    "@vitest/browser":
      optional: true
  checksum: 10c0/cae3e58d81d56e7e1cdecd7b5baab7edd0ad9dee8dec9353c52796e390e452377d3f04174d40b6986b17c73241a5e773e422931eaa8102dcba0605ff24b25193
  languageName: node
  linkType: hard

"@vitest/expect@npm:3.2.0":
  version: 3.2.0
  resolution: "@vitest/expect@npm:3.2.0"
  dependencies:
    "@types/chai": "npm:^5.2.2"
    "@vitest/spy": "npm:3.2.0"
    "@vitest/utils": "npm:3.2.0"
    chai: "npm:^5.2.0"
    tinyrainbow: "npm:^2.0.0"
  checksum: 10c0/2f02d7859cb6446c25ca9b01736df1cf344314f7a89ec00405f33ee38ff8c539ac52a5c2c21ef0c37e07089ee390f091aba6e6fe5cb77f12a93bb5088006f3a7
  languageName: node
  linkType: hard

"@vitest/mocker@npm:3.2.0":
  version: 3.2.0
  resolution: "@vitest/mocker@npm:3.2.0"
  dependencies:
    "@vitest/spy": "npm:3.2.0"
    estree-walker: "npm:^3.0.3"
    magic-string: "npm:^0.30.17"
  peerDependencies:
    msw: ^2.4.9
    vite: ^5.0.0 || ^6.0.0 || ^7.0.0-0
  peerDependenciesMeta:
    msw:
      optional: true
    vite:
      optional: true
  checksum: 10c0/1c36ec74b755364e624fbacbbd161ad6e989c17ce5e6161747f06ca63227f9f9ce9b64e6ff3235089fcda24b4138434385017de75265754ed5f000af50823a9d
  languageName: node
  linkType: hard

"@vitest/pretty-format@npm:3.2.0":
  version: 3.2.0
  resolution: "@vitest/pretty-format@npm:3.2.0"
  dependencies:
    tinyrainbow: "npm:^2.0.0"
  checksum: 10c0/3605a72bceb22fef318e5354a50df168baba634496763ef8ea8d8a3259aa8e727c3882bd40f2dbad309101bcf62be8d353443b835630039a4ce425b939cb9fbd
  languageName: node
  linkType: hard

"@vitest/pretty-format@npm:3.2.4, @vitest/pretty-format@npm:^3.2.0":
  version: 3.2.4
  resolution: "@vitest/pretty-format@npm:3.2.4"
  dependencies:
    tinyrainbow: "npm:^2.0.0"
  checksum: 10c0/5ad7d4278e067390d7d633e307fee8103958806a419ca380aec0e33fae71b44a64415f7a9b4bc11635d3c13d4a9186111c581d3cef9c65cc317e68f077456887
  languageName: node
  linkType: hard

"@vitest/runner@npm:3.2.0":
  version: 3.2.0
  resolution: "@vitest/runner@npm:3.2.0"
  dependencies:
    "@vitest/utils": "npm:3.2.0"
    pathe: "npm:^2.0.3"
  checksum: 10c0/899de249c3b620ccbce794121de36cabb49dfb015066cadeff615ad5514206fd5006474854cd9c1866a103c7484ca1de78cb8cb2402ad667de0f074ce9593ab9
  languageName: node
  linkType: hard

"@vitest/snapshot@npm:3.2.0":
  version: 3.2.0
  resolution: "@vitest/snapshot@npm:3.2.0"
  dependencies:
    "@vitest/pretty-format": "npm:3.2.0"
    magic-string: "npm:^0.30.17"
    pathe: "npm:^2.0.3"
  checksum: 10c0/ea90d2348589c321bcdd8b79f27ae3ef9d5ccbd3127ee373b7d56a12d33d6e07d47c8d372cde510a67bd48d7ca45f75f1c9fbc2c1193adc2884ea911b927853a
  languageName: node
  linkType: hard

"@vitest/spy@npm:3.2.0":
  version: 3.2.0
  resolution: "@vitest/spy@npm:3.2.0"
  dependencies:
    tinyspy: "npm:^4.0.3"
  checksum: 10c0/9fb3718bc0fd9b43992b7e1bcd1721b6fb64583d63a8d872bf019b32ba45628ca86a91adf25e328a669945ea4185725cdeb933485fe10ed8d6e06afe3bcc6c96
  languageName: node
  linkType: hard

"@vitest/ui@npm:3.2.4":
  version: 3.2.4
  resolution: "@vitest/ui@npm:3.2.4"
  dependencies:
    "@vitest/utils": "npm:3.2.4"
    fflate: "npm:^0.8.2"
    flatted: "npm:^3.3.3"
    pathe: "npm:^2.0.3"
    sirv: "npm:^3.0.1"
    tinyglobby: "npm:^0.2.14"
    tinyrainbow: "npm:^2.0.0"
  peerDependencies:
    vitest: 3.2.4
  checksum: 10c0/c3de1b757905d050706c7ab0199185dd8c7e115f2f348b8d5a7468528c6bf90c2c46096e8901602349ac04f5ba83ac23cd98c38827b104d5151cf8ba21739a0c
  languageName: node
  linkType: hard

"@vitest/utils@npm:3.2.0":
  version: 3.2.0
  resolution: "@vitest/utils@npm:3.2.0"
  dependencies:
    "@vitest/pretty-format": "npm:3.2.0"
    loupe: "npm:^3.1.3"
    tinyrainbow: "npm:^2.0.0"
  checksum: 10c0/7877a4e74c253125e6c731e1e243a2d429985a4907921569fbf7851b17c6ed0e3ef444aaba35e8518d70c00f837b9ec4726f2169b1ad59e7ec212b25742a982c
  languageName: node
  linkType: hard

"@vitest/utils@npm:3.2.4":
  version: 3.2.4
  resolution: "@vitest/utils@npm:3.2.4"
  dependencies:
    "@vitest/pretty-format": "npm:3.2.4"
    loupe: "npm:^3.1.4"
    tinyrainbow: "npm:^2.0.0"
  checksum: 10c0/024a9b8c8bcc12cf40183c246c244b52ecff861c6deb3477cbf487ac8781ad44c68a9c5fd69f8c1361878e55b97c10d99d511f2597f1f7244b5e5101d028ba64
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.0
  resolution: "abbrev@npm:3.0.0"
  checksum: 10c0/049704186396f571650eb7b22ed3627b77a5aedf98bb83caf2eac81ca2a3e25e795394b0464cfb2d6076df3db6a5312139eac5b6a126ca296ac53c5008069c28
  languageName: node
  linkType: hard

"acorn-class-fields@npm:^0.3.7":
  version: 0.3.7
  resolution: "acorn-class-fields@npm:0.3.7"
  dependencies:
    acorn-private-class-elements: "npm:^0.2.7"
  peerDependencies:
    acorn: ^6 || ^7 || ^8
  checksum: 10c0/bd31f8d06f177494a587c70b2e36fa20705ab8f75fbc0788d4f05b6cc580201b10bfd2bf9262ebcc9f31acb34485c2c62760de9507241cf293a91cc68ca85256
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.1, acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: 10c0/4c54868fbef3b8d58927d5e33f0a4de35f59012fe7b12cf9dfbb345fb8f46607709e1c4431be869a23fb63c151033d84c4198fa9f79385cec34fcb1dd53974c1
  languageName: node
  linkType: hard

"acorn-private-class-elements@npm:^0.2.7":
  version: 0.2.7
  resolution: "acorn-private-class-elements@npm:0.2.7"
  peerDependencies:
    acorn: ^6.1.0 || ^7 || ^8
  checksum: 10c0/83bf737e7c6b1ce7d8cee78ad4218d27494d4b84b2a28215c919e0dd111b0d595ac45c4e59d47b780482b0444b22647334e9830d2c4e85ac68469b568a79b4f3
  languageName: node
  linkType: hard

"acorn-private-methods@npm:^0.3.3":
  version: 0.3.3
  resolution: "acorn-private-methods@npm:0.3.3"
  dependencies:
    acorn-private-class-elements: "npm:^0.2.7"
  peerDependencies:
    acorn: ^6 || ^7 || ^8
  checksum: 10c0/c494c438b853dc84b23ce4e37919729c3c1bee1b2430bd15a0bbfd17ea0467089b8080e308fc709a0e5ef4ddf4fbc0fea85e934c157d1b2870edfdf6c60762a1
  languageName: node
  linkType: hard

"acorn-stage3@npm:^4.0.0":
  version: 4.0.0
  resolution: "acorn-stage3@npm:4.0.0"
  dependencies:
    acorn-class-fields: "npm:^0.3.7"
    acorn-private-methods: "npm:^0.3.3"
    acorn-static-class-features: "npm:^0.2.4"
  peerDependencies:
    acorn: ^7.4 || ^8
  checksum: 10c0/f545afded93809bb457a0fda91415518c544252c390f66b71d781457c5cdd2e2522ddd2da371def99b2e7b1671978323f633788c96d89c4e24cbea7a208e71b6
  languageName: node
  linkType: hard

"acorn-static-class-features@npm:^0.2.4":
  version: 0.2.4
  resolution: "acorn-static-class-features@npm:0.2.4"
  dependencies:
    acorn-private-class-elements: "npm:^0.2.7"
  peerDependencies:
    acorn: ^6.1.0 || ^7 || ^8
  checksum: 10c0/e81ada8a55257880ff85f6c47a2a0fbd077f19c072d5ed3b2971cc63f9e853de485d569d3e09e49f27a2f2ab85ad759d84773d1fc6ae3fe32ba96414b6ed2129
  languageName: node
  linkType: hard

"acorn-walk@npm:^8.0.0":
  version: 8.3.4
  resolution: "acorn-walk@npm:8.3.4"
  dependencies:
    acorn: "npm:^8.11.0"
  checksum: 10c0/76537ac5fb2c37a64560feaf3342023dadc086c46da57da363e64c6148dc21b57d49ace26f949e225063acb6fb441eabffd89f7a3066de5ad37ab3e328927c62
  languageName: node
  linkType: hard

"acorn@npm:^8.0.4, acorn@npm:^8.11.0, acorn@npm:^8.14.0":
  version: 8.14.1
  resolution: "acorn@npm:8.14.1"
  bin:
    acorn: bin/acorn
  checksum: 10c0/dbd36c1ed1d2fa3550140000371fcf721578095b18777b85a79df231ca093b08edc6858d75d6e48c73e431c174dcf9214edbd7e6fa5911b93bd8abfa54e47123
  languageName: node
  linkType: hard

"acorn@npm:^8.15.0":
  version: 8.15.0
  resolution: "acorn@npm:8.15.0"
  bin:
    acorn: bin/acorn
  checksum: 10c0/dec73ff59b7d6628a01eebaece7f2bdb8bb62b9b5926dcad0f8931f2b8b79c2be21f6c68ac095592adb5adb15831a3635d9343e6a91d028bbe85d564875ec3ec
  languageName: node
  linkType: hard

"adler-32@npm:~1.3.0":
  version: 1.3.1
  resolution: "adler-32@npm:1.3.1"
  checksum: 10c0/c1b7185526ee1bbe0eac8ed414d5226af4cd02a0540449a72ec1a75f198c5e93352ba4d7b9327231eea31fd83c2d080d13baf16d8ed5710fb183677beb85f612
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 10c0/6192b580c5b1d8fb399b9c62bf8343d76654c2dd62afcb9a52b2cf44a8b6ace1e3b704d3fe3547d91555c857d3df02603341ff2cb961b9cfe2b12f9f3c38ee11
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    fast-json-stable-stringify: "npm:^2.0.0"
    json-schema-traverse: "npm:^0.4.1"
    uri-js: "npm:^4.2.2"
  checksum: 10c0/41e23642cbe545889245b9d2a45854ebba51cda6c778ebced9649420d9205f2efb39cb43dbc41e358409223b1ea43303ae4839db682c848b891e4811da1a5a71
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10c0/9a64bb8627b434ba9327b60c027742e5d17ac69277960d041898596271d992d4d52ba7267a63ca10232e29f6107fc8a835f6ce8d719b88c5f8493f8254813737
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 10c0/a91daeddd54746338478eef88af3439a7edf30f8e23196e2d6ed182da9add559c601266dbef01c2efa46a958ad6f1f8b176799657616c702b5b02e799e7fd8dc
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10c0/895a23929da416f2bd3de7e9cb4eabd340949328ab85ddd6e484a637d8f6820d485f53933446f5291c3b760cbc488beb8e88573dd0f9c7daf83dccc8fe81b041
  languageName: node
  linkType: hard

"ansi-styles@npm:^5.0.0":
  version: 5.2.0
  resolution: "ansi-styles@npm:5.2.0"
  checksum: 10c0/9c4ca80eb3c2fb7b33841c210d2f20807f40865d27008d7c3f707b7f95cab7d67462a565e2388ac3285b71cb3d9bb2173de8da37c57692a362885ec34d6e27df
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10c0/5d1ec38c123984bcedd996eac680d548f31828bd679a66db2bdf11844634dde55fec3efa9c6bb1d89056a5e79c1ac540c4c784d592ea1d25028a92227d2f2d5c
  languageName: node
  linkType: hard

"anymatch@npm:^3.1.3":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: "npm:^3.0.0"
    picomatch: "npm:^2.0.4"
  checksum: 10c0/57b06ae984bc32a0d22592c87384cd88fe4511b1dd7581497831c56d41939c8a001b28e7b853e1450f2bf61992dfcaa8ae2d0d161a0a90c4fb631ef07098fbac
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 10c0/c5640c2d89045371c7cedd6a70212a04e360fd34d6edeae32f6952c63949e3525ea77dbec0289d8213a99bbaeab5abfa860b5c12cf88a2e6cf8106e90dd27a7e
  languageName: node
  linkType: hard

"aria-query@npm:5.3.0":
  version: 5.3.0
  resolution: "aria-query@npm:5.3.0"
  dependencies:
    dequal: "npm:^2.0.3"
  checksum: 10c0/2bff0d4eba5852a9dd578ecf47eaef0e82cc52569b48469b0aac2db5145db0b17b7a58d9e01237706d1e14b7a1b0ac9b78e9c97027ad97679dd8f91b85da1469
  languageName: node
  linkType: hard

"aria-query@npm:^5.0.0":
  version: 5.3.2
  resolution: "aria-query@npm:5.3.2"
  checksum: 10c0/003c7e3e2cff5540bf7a7893775fc614de82b0c5dde8ae823d47b7a28a9d4da1f7ed85f340bdb93d5649caa927755f0e31ecc7ab63edfdfc00c8ef07e505e03e
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.1, array-buffer-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "array-buffer-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    is-array-buffer: "npm:^3.0.5"
  checksum: 10c0/74e1d2d996941c7a1badda9cabb7caab8c449db9086407cad8a1b71d2604cc8abf105db8ca4e02c04579ec58b7be40279ddb09aea4784832984485499f48432d
  languageName: node
  linkType: hard

"array-includes@npm:^3.1.6, array-includes@npm:^3.1.8":
  version: 3.1.8
  resolution: "array-includes@npm:3.1.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.4"
    is-string: "npm:^1.0.7"
  checksum: 10c0/5b1004d203e85873b96ddc493f090c9672fd6c80d7a60b798da8a14bff8a670ff95db5aafc9abc14a211943f05220dacf8ea17638ae0af1a6a47b8c0b48ce370
  languageName: node
  linkType: hard

"array.prototype.findlast@npm:^1.2.5":
  version: 1.2.5
  resolution: "array.prototype.findlast@npm:1.2.5"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10c0/ddc952b829145ab45411b9d6adcb51a8c17c76bf89c9dd64b52d5dffa65d033da8c076ed2e17091779e83bc892b9848188d7b4b33453c5565e65a92863cb2775
  languageName: node
  linkType: hard

"array.prototype.flat@npm:^1.3.1":
  version: 1.3.3
  resolution: "array.prototype.flat@npm:1.3.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10c0/d90e04dfbc43bb96b3d2248576753d1fb2298d2d972e29ca7ad5ec621f0d9e16ff8074dae647eac4f31f4fb7d3f561a7ac005fb01a71f51705a13b5af06a7d8a
  languageName: node
  linkType: hard

"array.prototype.flatmap@npm:^1.3.3":
  version: 1.3.3
  resolution: "array.prototype.flatmap@npm:1.3.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10c0/ba899ea22b9dc9bf276e773e98ac84638ed5e0236de06f13d63a90b18ca9e0ec7c97d622d899796e3773930b946cd2413d098656c0c5d8cc58c6f25c21e6bd54
  languageName: node
  linkType: hard

"array.prototype.tosorted@npm:^1.1.4":
  version: 1.1.4
  resolution: "array.prototype.tosorted@npm:1.1.4"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.3"
    es-errors: "npm:^1.3.0"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10c0/eb3c4c4fc0381b0bf6dba2ea4d48d367c2827a0d4236a5718d97caaccc6b78f11f4cadf090736e86301d295a6aa4967ed45568f92ced51be8cbbacd9ca410943
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.4":
  version: 1.0.4
  resolution: "arraybuffer.prototype.slice@npm:1.0.4"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.1"
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    is-array-buffer: "npm:^3.0.4"
  checksum: 10c0/2f2459caa06ae0f7f615003f9104b01f6435cc803e11bd2a655107d52a1781dc040532dc44d93026b694cc18793993246237423e13a5337e86b43ed604932c06
  languageName: node
  linkType: hard

"assertion-error@npm:^2.0.1":
  version: 2.0.1
  resolution: "assertion-error@npm:2.0.1"
  checksum: 10c0/bbbcb117ac6480138f8c93cf7f535614282dea9dc828f540cdece85e3c665e8f78958b96afac52f29ff883c72638e6a87d469ecc9fe5bc902df03ed24a55dba8
  languageName: node
  linkType: hard

"ast-v8-to-istanbul@npm:^0.3.3":
  version: 0.3.3
  resolution: "ast-v8-to-istanbul@npm:0.3.3"
  dependencies:
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    estree-walker: "npm:^3.0.3"
    js-tokens: "npm:^9.0.1"
  checksum: 10c0/ffc39bc3ab4b8c1f7aea945960ce6b1e518bab3da7c800277eab2da07d397eeae4a2cb8a5a5f817225646c8ea495c1e4434fbe082c84bae8042abddef53f50b2
  languageName: node
  linkType: hard

"async-function@npm:^1.0.0":
  version: 1.0.0
  resolution: "async-function@npm:1.0.0"
  checksum: 10c0/669a32c2cb7e45091330c680e92eaeb791bc1d4132d827591e499cd1f776ff5a873e77e5f92d0ce795a8d60f10761dec9ddfe7225a5de680f5d357f67b1aac73
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 10c0/d73e2ddf20c4eb9337e1b3df1a0f6159481050a5de457c55b14ea2e5cb6d90bb69e004c9af54737a5ee0917fcf2c9e25de67777bbe58261847846066ba75bc9d
  languageName: node
  linkType: hard

"atob@npm:^2.1.2":
  version: 2.1.2
  resolution: "atob@npm:2.1.2"
  bin:
    atob: bin/atob.js
  checksum: 10c0/ada635b519dc0c576bb0b3ca63a73b50eefacf390abb3f062558342a8d68f2db91d0c8db54ce81b0d89de3b0f000de71f3ae7d761fd7d8cc624278fe443d6c7e
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.7":
  version: 1.0.7
  resolution: "available-typed-arrays@npm:1.0.7"
  dependencies:
    possible-typed-array-names: "npm:^1.0.0"
  checksum: 10c0/d07226ef4f87daa01bd0fe80f8f310982e345f372926da2e5296aecc25c41cab440916bbaa4c5e1034b453af3392f67df5961124e4b586df1e99793a1374bdb2
  languageName: node
  linkType: hard

"axios@npm:^1.9.0":
  version: 1.11.0
  resolution: "axios@npm:1.11.0"
  dependencies:
    follow-redirects: "npm:^1.15.6"
    form-data: "npm:^4.0.4"
    proxy-from-env: "npm:^1.1.0"
  checksum: 10c0/5de273d33d43058610e4d252f0963cc4f10714da0bfe872e8ef2cbc23c2c999acc300fd357b6bce0fc84a2ca9bd45740fa6bb28199ce2c1266c8b1a393f2b36e
  languageName: node
  linkType: hard

"b4a@npm:^1.6.4":
  version: 1.6.7
  resolution: "b4a@npm:1.6.7"
  checksum: 10c0/ec2f004d1daae04be8c5a1f8aeb7fea213c34025e279db4958eb0b82c1729ee25f7c6e89f92a5f65c8a9cf2d017ce27e3dda912403341d1781bd74528a4849d4
  languageName: node
  linkType: hard

"babel-plugin-react-compiler@npm:19.0.0-beta-ebf51a3-20250411":
  version: 19.0.0-beta-ebf51a3-20250411
  resolution: "babel-plugin-react-compiler@npm:19.0.0-beta-ebf51a3-20250411"
  dependencies:
    "@babel/types": "npm:^7.26.0"
  checksum: 10c0/351004a1c90c74f69ae69e4455ede1203d644d3dcaad385826544f84db9e1999e2df3c6b33b1c397979c7cfcab153c5edb47408aa8b9593125cd539cc45b1254
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10c0/9308baf0a7e4838a82bbfd11e01b1cb0f0cf2893bc1676c27c2a8c0e70cbae1c59120c3268517a8ae7fb6376b4639ef81ca22582611dbee4ed28df945134aaee
  languageName: node
  linkType: hard

"bare-events@npm:^2.2.0":
  version: 2.5.4
  resolution: "bare-events@npm:2.5.4"
  checksum: 10c0/877a9cea73d545e2588cdbd6fd01653e27dac48ad6b44985cdbae73e1f57f292d4ba52e25d1fba53674c1053c463d159f3d5c7bc36a2e6e192e389b499ddd627
  languageName: node
  linkType: hard

"base64-arraybuffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "base64-arraybuffer@npm:1.0.2"
  checksum: 10c0/3acac95c70f9406e87a41073558ba85b6be9dbffb013a3d2a710e3f2d534d506c911847d5d9be4de458af6362c676de0a5c4c2d7bdf4def502d00b313368e72f
  languageName: node
  linkType: hard

"base64-js@npm:^1.3.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 10c0/f23823513b63173a001030fae4f2dabe283b99a9d324ade3ad3d148e218134676f1ee8568c877cd79ec1c53158dcf2d2ba527a97c606618928ba99dd930102bf
  languageName: node
  linkType: hard

"bl@npm:^5.0.0":
  version: 5.1.0
  resolution: "bl@npm:5.1.0"
  dependencies:
    buffer: "npm:^6.0.3"
    inherits: "npm:^2.0.4"
    readable-stream: "npm:^3.4.0"
  checksum: 10c0/528a9c3d7d6b87af98c46f10a887654d027c28c503c7f7de87440e643f0056d7a2319a967762b8ec18150c64799d2825a277147a752a0570a7407c0b705b0d01
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 10c0/e4b53deb4f2b85c52be0e21a273f2045c7b6a6ea002b0e139c744cb6f95e9ec044439a52883b0d74dedd1ff3da55ed140cfdddfed7fb0cccbed373de5dce1bcf
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 10c0/695a56cd058096a7cb71fb09d9d6a7070113c7be516699ed361317aca2ec169f618e28b8af352e02ab4233fb54eb0168460a40dc320bab0034b36ab59aaad668
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10c0/b358f2fe060e2d7a87aa015979ecea07f3c37d4018f8d6deb5bd4c229ad3a0384fe6029bb76cd8be63c81e516ee52d1a0673edbe2023d53a5191732ae3c3e49f
  languageName: node
  linkType: hard

"braces@npm:^3.0.3":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: "npm:^7.1.1"
  checksum: 10c0/7c6dfd30c338d2997ba77500539227b9d1f85e388a5f43220865201e407e076783d0881f2d297b9f80951b4c957fcf0b51c1d2d24227631643c3f7c284b0aa04
  languageName: node
  linkType: hard

"broccoli-node-api@npm:^1.7.0":
  version: 1.7.0
  resolution: "broccoli-node-api@npm:1.7.0"
  checksum: 10c0/7ea3e32847c32c9017f7e9def8d80e02070b80dad3608a37d8472c0f535a768b9b1fe71afd2153a7c927fe59a724bc688e591b17666f86a8fc2bc4ec74a50532
  languageName: node
  linkType: hard

"broccoli-node-info@npm:^2.1.0":
  version: 2.2.0
  resolution: "broccoli-node-info@npm:2.2.0"
  checksum: 10c0/d904e31772e4f7a2af442dd3edc2232753d97ab68be5bd1b4f3b11f39e80b6f21e2b89204717a6d8815b530883a2c9054a6ce2c96a6e16f9aba9b0cb4eb2af7a
  languageName: node
  linkType: hard

"broccoli-output-wrapper@npm:^3.2.5":
  version: 3.2.5
  resolution: "broccoli-output-wrapper@npm:3.2.5"
  dependencies:
    fs-extra: "npm:^8.1.0"
    heimdalljs-logger: "npm:^0.1.10"
    symlink-or-copy: "npm:^1.2.0"
  checksum: 10c0/7e17524277cc9d1349967d329c2d7ec4a05eca5164e566948cf36043f5332581c7c76466b43d36b46e404403558f02930e89a7586f27e6312814e5ec4d130154
  languageName: node
  linkType: hard

"broccoli-plugin@npm:^4.0.7":
  version: 4.0.7
  resolution: "broccoli-plugin@npm:4.0.7"
  dependencies:
    broccoli-node-api: "npm:^1.7.0"
    broccoli-output-wrapper: "npm:^3.2.5"
    fs-merger: "npm:^3.2.1"
    promise-map-series: "npm:^0.3.0"
    quick-temp: "npm:^0.1.8"
    rimraf: "npm:^3.0.2"
    symlink-or-copy: "npm:^1.3.1"
  checksum: 10c0/5599a53ca43c18f2c763d05d8feda3fe0cb1f24194c6309fa316a53b67001d6215b7f99611011bb92843d2ff792345cec7b9a1947bfd728a4196a8057e3fe051
  languageName: node
  linkType: hard

"browserslist-config-single-spa@npm:^1.0.1":
  version: 1.0.1
  resolution: "browserslist-config-single-spa@npm:1.0.1"
  checksum: 10c0/62dffb6d962012d8ebbfc8042f9a1be51b8d8551f8c9ef502af3b6add7193fc4ab568f587c9482b65b85103111a85ce02085b7341063513c6f112d9b0e521f85
  languageName: node
  linkType: hard

"browserslist@npm:^4.24.0":
  version: 4.24.4
  resolution: "browserslist@npm:4.24.4"
  dependencies:
    caniuse-lite: "npm:^1.0.30001688"
    electron-to-chromium: "npm:^1.5.73"
    node-releases: "npm:^2.0.19"
    update-browserslist-db: "npm:^1.1.1"
  bin:
    browserslist: cli.js
  checksum: 10c0/db7ebc1733cf471e0b490b4f47e3e2ea2947ce417192c9246644e92c667dd56a71406cc58f62ca7587caf828364892e9952904a02b7aead752bc65b62a37cfe9
  languageName: node
  linkType: hard

"browserslist@npm:^4.25.0":
  version: 4.25.0
  resolution: "browserslist@npm:4.25.0"
  dependencies:
    caniuse-lite: "npm:^1.0.30001718"
    electron-to-chromium: "npm:^1.5.160"
    node-releases: "npm:^2.0.19"
    update-browserslist-db: "npm:^1.1.3"
  bin:
    browserslist: cli.js
  checksum: 10c0/cc16c55b4468b18684a0e1ca303592b38635b1155d6724f172407192737a2f405b8030d87a05813729592793445b3d15e737b0055f901cdecccb29b1e580a1c5
  languageName: node
  linkType: hard

"btoa@npm:^1.2.1":
  version: 1.2.1
  resolution: "btoa@npm:1.2.1"
  bin:
    btoa: bin/btoa.js
  checksum: 10c0/557b9682e40a68ae057af1b377e28884e6ff756ba0f499fe0f8c7b725a5bfb5c0d891604ac09944dbe330c9d43fb3976fef734f9372608d0d8e78a30eda292ae
  languageName: node
  linkType: hard

"buffer@npm:^6.0.3":
  version: 6.0.3
  resolution: "buffer@npm:6.0.3"
  dependencies:
    base64-js: "npm:^1.3.1"
    ieee754: "npm:^1.2.1"
  checksum: 10c0/2a905fbbcde73cc5d8bd18d1caa23715d5f83a5935867c2329f0ac06104204ba7947be098fe1317fbd8830e26090ff8e764f08cd14fefc977bb248c3487bcbd0
  languageName: node
  linkType: hard

"builtin-modules@npm:^5.0.0":
  version: 5.0.0
  resolution: "builtin-modules@npm:5.0.0"
  checksum: 10c0/bee8e74d1b949133c66a30b2e7982b3bdfe70d09f72bc4425ac9811d01d6f2f11abbe66c47aa9c977800a255155cac5a8068e0714cd2dc31867762fb309fd065
  languageName: node
  linkType: hard

"cac@npm:^6.7.14":
  version: 6.7.14
  resolution: "cac@npm:6.7.14"
  checksum: 10c0/4ee06aaa7bab8981f0d54e5f5f9d4adcd64058e9697563ce336d8a3878ed018ee18ebe5359b2430eceae87e0758e62ea2019c3f52ae6e211b1bd2e133856cd10
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10c0/01f2134e1bd7d3ab68be851df96c8d63b492b1853b67f2eecb2c37bb682d37cb70bb858a16f2f0554d3c0071be6dfe21456a1ff6fa4b7eed996570d6a25ffe9c
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.0, call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10c0/47bd9901d57b857590431243fea704ff18078b16890a6b3e021e12d279bbf211d039155e27d7566b374d49ee1f8189344bac9833dec7a20cdec370506361c938
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.7, call-bind@npm:^1.0.8":
  version: 1.0.8
  resolution: "call-bind@npm:1.0.8"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.0"
    es-define-property: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.4"
    set-function-length: "npm:^1.2.2"
  checksum: 10c0/a13819be0681d915144467741b69875ae5f4eba8961eb0bf322aab63ec87f8250eb6d6b0dcbb2e1349876412a56129ca338592b3829ef4343527f5f18a0752d4
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2, call-bound@npm:^1.0.3, call-bound@npm:^1.0.4":
  version: 1.0.4
  resolution: "call-bound@npm:1.0.4"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    get-intrinsic: "npm:^1.3.0"
  checksum: 10c0/f4796a6a0941e71c766aea672f63b72bc61234c4f4964dc6d7606e3664c307e7d77845328a8f3359ce39ddb377fed67318f9ee203dea1d47e46165dcf2917644
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 10c0/fff92277400eb06c3079f9e74f3af120db9f8ea03bad0e84d9aede54bbe2d44a56cccb5f6cf12211f93f52306df87077ecec5b712794c5a9b5dac6d615a3f301
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001688":
  version: 1.0.30001706
  resolution: "caniuse-lite@npm:1.0.30001706"
  checksum: 10c0/b502d0a509611fd5b009e1123d482e983696984b6b749c3f485fd8d02cc58376c59cf0bb15f22fa2d337da104970edd27dd525d4663cebc728e26ac4adedff0d
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001718":
  version: 1.0.30001723
  resolution: "caniuse-lite@npm:1.0.30001723"
  checksum: 10c0/e019503061759b96017c4d27ddd7ca1b48533eabcd0431b51d2e3156f99f6b031075e46c279c0db63424cdfc874bba992caec2db51b922a0f945e686246886f6
  languageName: node
  linkType: hard

"canvg@npm:^3.0.11":
  version: 3.0.11
  resolution: "canvg@npm:3.0.11"
  dependencies:
    "@babel/runtime": "npm:^7.12.5"
    "@types/raf": "npm:^3.4.0"
    core-js: "npm:^3.8.3"
    raf: "npm:^3.4.1"
    regenerator-runtime: "npm:^0.13.7"
    rgbcolor: "npm:^1.0.1"
    stackblur-canvas: "npm:^2.0.0"
    svg-pathdata: "npm:^6.0.3"
  checksum: 10c0/03ea5b69e97a4a476ef5fc8455d0da2718bd68cd8ace3e42718176bf4b7dfd80eb1d0eba47dcd04b34ed9a0c5c8ed849ead0f364dad0e35369254a45d5e098c4
  languageName: node
  linkType: hard

"cfb@npm:~1.2.1":
  version: 1.2.2
  resolution: "cfb@npm:1.2.2"
  dependencies:
    adler-32: "npm:~1.3.0"
    crc-32: "npm:~1.2.0"
  checksum: 10c0/87f6d9c3878268896ed6ca29dfe32a2aa078b12d0f21d8405c95911b74ab6296823d7312bbf5e18326d00b16cc697f587e07a17018c5edf7a1ba31dd5bc6da36
  languageName: node
  linkType: hard

"chai@npm:^5.2.0":
  version: 5.2.0
  resolution: "chai@npm:5.2.0"
  dependencies:
    assertion-error: "npm:^2.0.1"
    check-error: "npm:^2.1.1"
    deep-eql: "npm:^5.0.1"
    loupe: "npm:^3.1.0"
    pathval: "npm:^2.0.0"
  checksum: 10c0/dfd1cb719c7cebb051b727672d382a35338af1470065cb12adb01f4ee451bbf528e0e0f9ab2016af5fc1eea4df6e7f4504dc8443f8f00bd8fb87ad32dc516f7d
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0, chalk@npm:^4.1.0":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/4a3fef5cc34975c898ffe77141450f679721df9dde00f6c304353fa9c8b571929123b26a0e4617bde5018977eb655b31970c297b91b63ee83bb82aeb04666880
  languageName: node
  linkType: hard

"check-error@npm:^2.1.1":
  version: 2.1.1
  resolution: "check-error@npm:2.1.1"
  checksum: 10c0/979f13eccab306cf1785fa10941a590b4e7ea9916ea2a4f8c87f0316fc3eab07eabefb6e587424ef0f88cbcd3805791f172ea739863ca3d7ce2afc54641c7f0e
  languageName: node
  linkType: hard

"cheerio-select@npm:^2.1.0":
  version: 2.1.0
  resolution: "cheerio-select@npm:2.1.0"
  dependencies:
    boolbase: "npm:^1.0.0"
    css-select: "npm:^5.1.0"
    css-what: "npm:^6.1.0"
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
    domutils: "npm:^3.0.1"
  checksum: 10c0/2242097e593919dba4aacb97d7b8275def8b9ec70b00aa1f43335456870cfc9e284eae2080bdc832ed232dabb9eefcf56c722d152da4a154813fb8814a55d282
  languageName: node
  linkType: hard

"cheerio@npm:^1.0.0":
  version: 1.0.0
  resolution: "cheerio@npm:1.0.0"
  dependencies:
    cheerio-select: "npm:^2.1.0"
    dom-serializer: "npm:^2.0.0"
    domhandler: "npm:^5.0.3"
    domutils: "npm:^3.1.0"
    encoding-sniffer: "npm:^0.2.0"
    htmlparser2: "npm:^9.1.0"
    parse5: "npm:^7.1.2"
    parse5-htmlparser2-tree-adapter: "npm:^7.0.0"
    parse5-parser-stream: "npm:^7.1.2"
    undici: "npm:^6.19.5"
    whatwg-mimetype: "npm:^4.0.0"
  checksum: 10c0/d0e16925d9c36c879edfaef1c0244c866375a4c7b8d6ccd7ae0ad42da7d26263ea1a3c17b9a1aa5965918deeff2d40ac2e7223824f8e6eca972df3b81316a09f
  languageName: node
  linkType: hard

"chokidar@npm:^4.0.0":
  version: 4.0.3
  resolution: "chokidar@npm:4.0.3"
  dependencies:
    readdirp: "npm:^4.0.1"
  checksum: 10c0/a58b9df05bb452f7d105d9e7229ac82fa873741c0c40ddcc7bb82f8a909fbe3f7814c9ebe9bc9a2bef9b737c0ec6e2d699d179048ef06ad3ec46315df0ebe6ad
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10c0/43925b87700f7e3893296c8e9c56cc58f926411cce3a6e5898136daaf08f08b9a8eb76d37d3267e707d0dcc17aed2e2ebdf5848c0c3ce95cf910a919935c1b10
  languageName: node
  linkType: hard

"ci-info@npm:^4.2.0":
  version: 4.2.0
  resolution: "ci-info@npm:4.2.0"
  checksum: 10c0/37a2f4b6a213a5cf835890eb0241f0d5b022f6cfefde58a69e9af8e3a0e71e06d6ad7754b0d4efb9cd2613e58a7a33996d71b56b0d04242722e86666f3f3d058
  languageName: node
  linkType: hard

"classcat@npm:^5.0.3, classcat@npm:^5.0.4":
  version: 5.0.5
  resolution: "classcat@npm:5.0.5"
  checksum: 10c0/ff8d273055ef9b518529cfe80fd0486f7057a9917373807ff802d75ceb46e8f8e148f41fa094ee7625c8f34642cfaa98395ff182d9519898da7cbf383d4a210d
  languageName: node
  linkType: hard

"classnames@npm:2.5.1":
  version: 2.5.1
  resolution: "classnames@npm:2.5.1"
  checksum: 10c0/afff4f77e62cea2d79c39962980bf316bacb0d7c49e13a21adaadb9221e1c6b9d3cdb829d8bb1b23c406f4e740507f37e1dcf506f7e3b7113d17c5bab787aa69
  languageName: node
  linkType: hard

"clean-regexp@npm:^1.0.0":
  version: 1.0.0
  resolution: "clean-regexp@npm:1.0.0"
  dependencies:
    escape-string-regexp: "npm:^1.0.5"
  checksum: 10c0/fd9c7446551b8fc536f95e8a286d431017cd4ba1ec2e53997ec9159385e9c317672f6dfc4d49fdb97449fdb53b0bacd0a8bab9343b8fdd2e46c7ddf6173d0db7
  languageName: node
  linkType: hard

"clone-deep@npm:^4.0.0":
  version: 4.0.1
  resolution: "clone-deep@npm:4.0.1"
  dependencies:
    is-plain-object: "npm:^2.0.4"
    kind-of: "npm:^6.0.2"
    shallow-clone: "npm:^3.0.0"
  checksum: 10c0/637753615aa24adf0f2d505947a1bb75e63964309034a1cf56ba4b1f30af155201edd38d26ffe26911adaae267a3c138b344a4947d39f5fc1b6d6108125aa758
  languageName: node
  linkType: hard

"clone-stats@npm:^1.0.0":
  version: 1.0.0
  resolution: "clone-stats@npm:1.0.0"
  checksum: 10c0/bb1e05991e034e1eb104173c25bb652ea5b2b4dad5a49057a857e00f8d1da39de3bd689128a25bab8cbdfbea8ae8f6066030d106ed5c299a7d92be7967c50217
  languageName: node
  linkType: hard

"clone@npm:^2.1.2":
  version: 2.1.2
  resolution: "clone@npm:2.1.2"
  checksum: 10c0/ed0601cd0b1606bc7d82ee7175b97e68d1dd9b91fd1250a3617b38d34a095f8ee0431d40a1a611122dcccb4f93295b4fdb94942aa763392b5fe44effa50c2d5e
  languageName: node
  linkType: hard

"clsx@npm:^1.1.1":
  version: 1.2.1
  resolution: "clsx@npm:1.2.1"
  checksum: 10c0/34dead8bee24f5e96f6e7937d711978380647e936a22e76380290e35486afd8634966ce300fc4b74a32f3762c7d4c0303f442c3e259f4ce02374eb0c82834f27
  languageName: node
  linkType: hard

"clsx@npm:^2.0.0":
  version: 2.1.1
  resolution: "clsx@npm:2.1.1"
  checksum: 10c0/c4c8eb865f8c82baab07e71bfa8897c73454881c4f99d6bc81585aecd7c441746c1399d08363dc096c550cceaf97bd4ce1e8854e1771e9998d9f94c4fe075839
  languageName: node
  linkType: hard

"codepage@npm:~1.15.0":
  version: 1.15.0
  resolution: "codepage@npm:1.15.0"
  checksum: 10c0/2455b482302cb784b46dea60a8ee83f0c23e794bdd979556bdb107abe681bba722af62a37f5c955ff4efd68fdb9688c3986e719b4fd536c0e06bb25bc82abea3
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10c0/37e1150172f2e311fe1b2df62c6293a342ee7380da7b9cfdba67ea539909afbd74da27033208d01d6d5cfc65ee7868a22e18d7e7648e004425441c0f8a15a7d7
  languageName: node
  linkType: hard

"color-name@npm:^1.0.0, color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10c0/a1a3f914156960902f46f7f56bc62effc6c94e84b2cae157a526b1c1f74b677a47ec602bf68a61abfa2b42d15b7c5651c6dbe72a43af720bc588dff885b10f95
  languageName: node
  linkType: hard

"color-string@npm:^1.9.0":
  version: 1.9.1
  resolution: "color-string@npm:1.9.1"
  dependencies:
    color-name: "npm:^1.0.0"
    simple-swizzle: "npm:^0.2.2"
  checksum: 10c0/b0bfd74c03b1f837f543898b512f5ea353f71630ccdd0d66f83028d1f0924a7d4272deb278b9aef376cacf1289b522ac3fb175e99895283645a2dc3a33af2404
  languageName: node
  linkType: hard

"color@npm:^4.0.0":
  version: 4.2.3
  resolution: "color@npm:4.2.3"
  dependencies:
    color-convert: "npm:^2.0.1"
    color-string: "npm:^1.9.0"
  checksum: 10c0/7fbe7cfb811054c808349de19fb380252e5e34e61d7d168ec3353e9e9aacb1802674bddc657682e4e9730c2786592a4de6f8283e7e0d3870b829bb0b7b2f6118
  languageName: node
  linkType: hard

"colors@npm:^1.4.0":
  version: 1.4.0
  resolution: "colors@npm:1.4.0"
  checksum: 10c0/9af357c019da3c5a098a301cf64e3799d27549d8f185d86f79af23069e4f4303110d115da98483519331f6fb71c8568d5688fa1c6523600044fd4a54e97c4efb
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 10c0/0dbb829577e1b1e839fa82b40c07ffaf7de8a09b935cadd355a73652ae70a88b4320db322f6634a4ad93424292fa80973ac6480986247f1734a1137debf271d5
  languageName: node
  linkType: hard

"commander@npm:2":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: 10c0/74c781a5248c2402a0a3e966a0a2bba3c054aad144f5c023364be83265e796b20565aa9feff624132ff629aa64e16999fa40a743c10c12f7c61e96a794b99288
  languageName: node
  linkType: hard

"commander@npm:7":
  version: 7.2.0
  resolution: "commander@npm:7.2.0"
  checksum: 10c0/8d690ff13b0356df7e0ebbe6c59b4712f754f4b724d4f473d3cc5b3fdcf978e3a5dc3078717858a2ceb50b0f84d0660a7f22a96cdc50fb877d0c9bb31593d23a
  languageName: node
  linkType: hard

"commander@npm:^12.1.0":
  version: 12.1.0
  resolution: "commander@npm:12.1.0"
  checksum: 10c0/6e1996680c083b3b897bfc1cfe1c58dfbcd9842fd43e1aaf8a795fbc237f65efcc860a3ef457b318e73f29a4f4a28f6403c3d653d021d960e4632dd45bde54a9
  languageName: node
  linkType: hard

"commander@npm:^9.0.0":
  version: 9.5.0
  resolution: "commander@npm:9.5.0"
  checksum: 10c0/5f7784fbda2aaec39e89eb46f06a999e00224b3763dc65976e05929ec486e174fe9aac2655f03ba6a5e83875bd173be5283dc19309b7c65954701c02025b3c1d
  languageName: node
  linkType: hard

"compute-scroll-into-view@npm:^3.1.0":
  version: 3.1.1
  resolution: "compute-scroll-into-view@npm:3.1.1"
  checksum: 10c0/59761ed62304a9599b52ad75d0d6fbf0669ee2ab7dd472fdb0ad9da36628414c014dea7b5810046560180ad30ffec52a953d19297f66a1d4f3aa0999b9d2521d
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 10c0/c996b1cfdf95b6c90fee4dae37e332c8b6eb7d106430c17d538034c0ad9a1630cb194d2ab37293b1bdd4d779494beee7786d586a50bd9376fd6f7bcc2bd4c98f
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 10c0/8f2f7a27a1a011cc6cc88cc4da2d7d0cfa5ee0369508baae3d98c260bb3ac520691464e5bbe4ae7cdf09860c1d69ecc6f70c63c6e7c7f7e3f18ec08484dc7d9b
  languageName: node
  linkType: hard

"cookie@npm:^1.0.1":
  version: 1.0.2
  resolution: "cookie@npm:1.0.2"
  checksum: 10c0/fd25fe79e8fbcfcaf6aa61cd081c55d144eeeba755206c058682257cb38c4bd6795c6620de3f064c740695bb65b7949ebb1db7a95e4636efb8357a335ad3f54b
  languageName: node
  linkType: hard

"copy-to-clipboard@npm:^3.3.1":
  version: 3.3.3
  resolution: "copy-to-clipboard@npm:3.3.3"
  dependencies:
    toggle-selection: "npm:^1.0.6"
  checksum: 10c0/3ebf5e8ee00601f8c440b83ec08d838e8eabb068c1fae94a9cda6b42f288f7e1b552f3463635f419af44bf7675afc8d0390d30876cf5c2d5d35f86d9c56a3e5f
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.41.0":
  version: 3.43.0
  resolution: "core-js-compat@npm:3.43.0"
  dependencies:
    browserslist: "npm:^4.25.0"
  checksum: 10c0/923804c16faf91bacb747a697640a907cb2a3e63078d467a75eb7ea4187d62d36347a94e5826d1b36739012e81a2ea435922cc8bd8e228fa68efaf00a9ce94af
  languageName: node
  linkType: hard

"core-js@npm:^3.6.0, core-js@npm:^3.8.3":
  version: 3.41.0
  resolution: "core-js@npm:3.41.0"
  checksum: 10c0/a29ed0b7fe81acf49d04ce5c17a1947166b1c15197327a5d12f95bbe84b46d60c3c13de701d808f41da06fa316285f3f55ce5903abc8d5642afc1eac4457afc8
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 10c0/90a0e40abbddfd7618f8ccd63a74d88deea94e77d0e8dbbea059fa7ebebb8fbb4e2909667fe26f3a467073de1a542ebe6ae4c73a73745ac5833786759cd906c9
  languageName: node
  linkType: hard

"crc-32@npm:~1.2.0, crc-32@npm:~1.2.1":
  version: 1.2.2
  resolution: "crc-32@npm:1.2.2"
  bin:
    crc32: bin/crc32.njs
  checksum: 10c0/11dcf4a2e77ee793835d49f2c028838eae58b44f50d1ff08394a610bfd817523f105d6ae4d9b5bef0aad45510f633eb23c903e9902e4409bed1ce70cb82b9bf0
  languageName: node
  linkType: hard

"cross-fetch@npm:4.0.0":
  version: 4.0.0
  resolution: "cross-fetch@npm:4.0.0"
  dependencies:
    node-fetch: "npm:^2.6.12"
  checksum: 10c0/386727dc4c6b044746086aced959ff21101abb85c43df5e1d151547ccb6f338f86dec3f28b9dbddfa8ff5b9ec8662ed2263ad4607a93b2dc354fb7fe3bbb898a
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10c0/053ea8b2135caff68a9e81470e845613e374e7309a47731e81639de3eaeb90c3d01af0e0b44d2ab9d50b43467223b88567dfeb3262db942dc063b9976718ffc1
  languageName: node
  linkType: hard

"css-line-break@npm:^2.1.0":
  version: 2.1.0
  resolution: "css-line-break@npm:2.1.0"
  dependencies:
    utrie: "npm:^1.0.2"
  checksum: 10c0/b2222d99d5daf7861ecddc050244fdce296fad74b000dcff6bdfb1eb16dc2ef0b9ffe2c1c965e3239bd05ebe9eadb6d5438a91592fa8648d27a338e827cf9048
  languageName: node
  linkType: hard

"css-select@npm:^5.1.0":
  version: 5.1.0
  resolution: "css-select@npm:5.1.0"
  dependencies:
    boolbase: "npm:^1.0.0"
    css-what: "npm:^6.1.0"
    domhandler: "npm:^5.0.2"
    domutils: "npm:^3.0.1"
    nth-check: "npm:^2.0.1"
  checksum: 10c0/551c60dba5b54054741032c1793b5734f6ba45e23ae9e82761a3c0ed1acbb8cfedfa443aaba3a3c1a54cac12b456d2012a09d2cd5f0e82e430454c1b9d84d500
  languageName: node
  linkType: hard

"css-what@npm:^6.1.0":
  version: 6.1.0
  resolution: "css-what@npm:6.1.0"
  checksum: 10c0/a09f5a6b14ba8dcf57ae9a59474722e80f20406c53a61e9aedb0eedc693b135113ffe2983f4efc4b5065ae639442e9ae88df24941ef159c218b231011d733746
  languageName: node
  linkType: hard

"css.escape@npm:^1.5.1":
  version: 1.5.1
  resolution: "css.escape@npm:1.5.1"
  checksum: 10c0/5e09035e5bf6c2c422b40c6df2eb1529657a17df37fda5d0433d722609527ab98090baf25b13970ca754079a0f3161dd3dfc0e743563ded8cfa0749d861c1525
  languageName: node
  linkType: hard

"cssstyle@npm:^4.2.1":
  version: 4.4.0
  resolution: "cssstyle@npm:4.4.0"
  dependencies:
    "@asamuzakjp/css-color": "npm:^3.2.0"
    rrweb-cssom: "npm:^0.8.0"
  checksum: 10c0/6a9f7e32679abb959643ac169abfc920f5d32bbf4d61cd34b49a683e4dd2f42036cba6f503c75396a760f2872070e2def02cf2046850c43263f7ecb75f8aa80a
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 10c0/80c089d6f7e0c5b2bd83cf0539ab41474198579584fa10d86d0cafe0642202343cbc119e076a0b1aece191989477081415d66c9fefbf3c957fc2fc4b7009f248
  languageName: node
  linkType: hard

"d3-array@npm:1 - 2":
  version: 2.12.1
  resolution: "d3-array@npm:2.12.1"
  dependencies:
    internmap: "npm:^1.0.0"
  checksum: 10c0/7eca10427a9f113a4ca6a0f7301127cab26043fd5e362631ef5a0edd1c4b2dd70c56ed317566700c31e4a6d88b55f3951aaba192291817f243b730cb2352882e
  languageName: node
  linkType: hard

"d3-array@npm:2 - 3, d3-array@npm:2.10.0 - 3, d3-array@npm:2.5.0 - 3, d3-array@npm:3, d3-array@npm:^3.2.0":
  version: 3.2.4
  resolution: "d3-array@npm:3.2.4"
  dependencies:
    internmap: "npm:1 - 2"
  checksum: 10c0/08b95e91130f98c1375db0e0af718f4371ccacef7d5d257727fe74f79a24383e79aba280b9ffae655483ffbbad4fd1dec4ade0119d88c4749f388641c8bf8c50
  languageName: node
  linkType: hard

"d3-axis@npm:3":
  version: 3.0.0
  resolution: "d3-axis@npm:3.0.0"
  checksum: 10c0/a271e70ba1966daa5aaf6a7f959ceca3e12997b43297e757c7b945db2e1ead3c6ee226f2abcfa22abbd4e2e28bd2b71a0911794c4e5b911bbba271328a582c78
  languageName: node
  linkType: hard

"d3-brush@npm:3":
  version: 3.0.0
  resolution: "d3-brush@npm:3.0.0"
  dependencies:
    d3-dispatch: "npm:1 - 3"
    d3-drag: "npm:2 - 3"
    d3-interpolate: "npm:1 - 3"
    d3-selection: "npm:3"
    d3-transition: "npm:3"
  checksum: 10c0/07baf00334c576da2f68a91fc0da5732c3a5fa19bd3d7aed7fd24d1d674a773f71a93e9687c154176f7246946194d77c48c2d8fed757f5dcb1a4740067ec50a8
  languageName: node
  linkType: hard

"d3-chord@npm:3":
  version: 3.0.1
  resolution: "d3-chord@npm:3.0.1"
  dependencies:
    d3-path: "npm:1 - 3"
  checksum: 10c0/baa6013914af3f4fe1521f0d16de31a38eb8a71d08ff1dec4741f6f45a828661e5cd3935e39bd14e3032bdc78206c283ca37411da21d46ec3cfc520be6e7a7ce
  languageName: node
  linkType: hard

"d3-cloud@npm:^1.2.7":
  version: 1.2.7
  resolution: "d3-cloud@npm:1.2.7"
  dependencies:
    d3-dispatch: "npm:^1.0.3"
  checksum: 10c0/9a1eb9b1854d5f54b9aba9afe1edfeca14f2849e6176aa6fcee51def62fd825c802e9b2cf37d8b3d51868c894558596fbe5a0bd956d9bf065b55decb95892e10
  languageName: node
  linkType: hard

"d3-color@npm:1 - 3, d3-color@npm:3":
  version: 3.1.0
  resolution: "d3-color@npm:3.1.0"
  checksum: 10c0/a4e20e1115fa696fce041fbe13fbc80dc4c19150fa72027a7c128ade980bc0eeeba4bcf28c9e21f0bce0e0dbfe7ca5869ef67746541dcfda053e4802ad19783c
  languageName: node
  linkType: hard

"d3-contour@npm:4":
  version: 4.0.2
  resolution: "d3-contour@npm:4.0.2"
  dependencies:
    d3-array: "npm:^3.2.0"
  checksum: 10c0/98bc5fbed6009e08707434a952076f39f1cd6ed8b9288253cc3e6a3286e4e80c63c62d84954b20e64bf6e4ededcc69add54d3db25e990784a59c04edd3449032
  languageName: node
  linkType: hard

"d3-delaunay@npm:6":
  version: 6.0.4
  resolution: "d3-delaunay@npm:6.0.4"
  dependencies:
    delaunator: "npm:5"
  checksum: 10c0/57c3aecd2525664b07c4c292aa11cf49b2752c0cf3f5257f752999399fe3c592de2d418644d79df1f255471eec8057a9cc0c3062ed7128cb3348c45f69597754
  languageName: node
  linkType: hard

"d3-dispatch@npm:1 - 3, d3-dispatch@npm:3":
  version: 3.0.1
  resolution: "d3-dispatch@npm:3.0.1"
  checksum: 10c0/6eca77008ce2dc33380e45d4410c67d150941df7ab45b91d116dbe6d0a3092c0f6ac184dd4602c796dc9e790222bad3ff7142025f5fd22694efe088d1d941753
  languageName: node
  linkType: hard

"d3-dispatch@npm:^1.0.3":
  version: 1.0.6
  resolution: "d3-dispatch@npm:1.0.6"
  checksum: 10c0/6302554a019e2d75d4e3dc7e8757a00b4b12ac2a2952bccc66e4478ccd170f425e2b6a9443118d5feadcd2439f33582b63c7925e832104ff1978cadea2a30dc2
  languageName: node
  linkType: hard

"d3-drag@npm:2 - 3, d3-drag@npm:3, d3-drag@npm:^3.0.0":
  version: 3.0.0
  resolution: "d3-drag@npm:3.0.0"
  dependencies:
    d3-dispatch: "npm:1 - 3"
    d3-selection: "npm:3"
  checksum: 10c0/d2556e8dc720741a443b595a30af403dd60642dfd938d44d6e9bfc4c71a962142f9a028c56b61f8b4790b65a34acad177d1263d66f103c3c527767b0926ef5aa
  languageName: node
  linkType: hard

"d3-dsv@npm:1 - 3, d3-dsv@npm:3":
  version: 3.0.1
  resolution: "d3-dsv@npm:3.0.1"
  dependencies:
    commander: "npm:7"
    iconv-lite: "npm:0.6"
    rw: "npm:1"
  bin:
    csv2json: bin/dsv2json.js
    csv2tsv: bin/dsv2dsv.js
    dsv2dsv: bin/dsv2dsv.js
    dsv2json: bin/dsv2json.js
    json2csv: bin/json2dsv.js
    json2dsv: bin/json2dsv.js
    json2tsv: bin/json2dsv.js
    tsv2csv: bin/dsv2dsv.js
    tsv2json: bin/dsv2json.js
  checksum: 10c0/10e6af9e331950ed258f34ab49ac1b7060128ef81dcf32afc790bd1f7e8c3cc2aac7f5f875250a83f21f39bb5925fbd0872bb209f8aca32b3b77d32bab8a65ab
  languageName: node
  linkType: hard

"d3-ease@npm:1 - 3, d3-ease@npm:3":
  version: 3.0.1
  resolution: "d3-ease@npm:3.0.1"
  checksum: 10c0/fec8ef826c0cc35cda3092c6841e07672868b1839fcaf556e19266a3a37e6bc7977d8298c0fcb9885e7799bfdcef7db1baaba9cd4dcf4bc5e952cf78574a88b0
  languageName: node
  linkType: hard

"d3-fetch@npm:3":
  version: 3.0.1
  resolution: "d3-fetch@npm:3.0.1"
  dependencies:
    d3-dsv: "npm:1 - 3"
  checksum: 10c0/4f467a79bf290395ac0cbb5f7562483f6a18668adc4c8eb84c9d3eff048b6f6d3b6f55079ba1ebf1908dabe000c941d46be447f8d78453b2dad5fb59fb6aa93b
  languageName: node
  linkType: hard

"d3-force@npm:3":
  version: 3.0.0
  resolution: "d3-force@npm:3.0.0"
  dependencies:
    d3-dispatch: "npm:1 - 3"
    d3-quadtree: "npm:1 - 3"
    d3-timer: "npm:1 - 3"
  checksum: 10c0/220a16a1a1ac62ba56df61028896e4b52be89c81040d20229c876efc8852191482c233f8a52bb5a4e0875c321b8e5cb6413ef3dfa4d8fe79eeb7d52c587f52cf
  languageName: node
  linkType: hard

"d3-format@npm:1 - 3, d3-format@npm:3":
  version: 3.1.0
  resolution: "d3-format@npm:3.1.0"
  checksum: 10c0/049f5c0871ebce9859fc5e2f07f336b3c5bfff52a2540e0bac7e703fce567cd9346f4ad1079dd18d6f1e0eaa0599941c1810898926f10ac21a31fd0a34b4aa75
  languageName: node
  linkType: hard

"d3-geo@npm:3":
  version: 3.1.1
  resolution: "d3-geo@npm:3.1.1"
  dependencies:
    d3-array: "npm:2.5.0 - 3"
  checksum: 10c0/d32270dd2dc8ac3ea63e8805d63239c4c8ec6c0d339d73b5e5a30a87f8f54db22a78fb434369799465eae169503b25f9a107c642c8a16c32a3285bc0e6d8e8c1
  languageName: node
  linkType: hard

"d3-hierarchy@npm:3":
  version: 3.1.2
  resolution: "d3-hierarchy@npm:3.1.2"
  checksum: 10c0/6dcdb480539644aa7fc0d72dfc7b03f99dfbcdf02714044e8c708577e0d5981deb9d3e99bbbb2d26422b55bcc342ac89a0fa2ea6c9d7302e2fc0951dd96f89cf
  languageName: node
  linkType: hard

"d3-interpolate@npm:1 - 3, d3-interpolate@npm:1.2.0 - 3, d3-interpolate@npm:3":
  version: 3.0.1
  resolution: "d3-interpolate@npm:3.0.1"
  dependencies:
    d3-color: "npm:1 - 3"
  checksum: 10c0/19f4b4daa8d733906671afff7767c19488f51a43d251f8b7f484d5d3cfc36c663f0a66c38fe91eee30f40327443d799be17169f55a293a3ba949e84e57a33e6a
  languageName: node
  linkType: hard

"d3-path@npm:1":
  version: 1.0.9
  resolution: "d3-path@npm:1.0.9"
  checksum: 10c0/e35e84df5abc18091f585725b8235e1fa97efc287571585427d3a3597301e6c506dea56b11dfb3c06ca5858b3eb7f02c1bf4f6a716aa9eade01c41b92d497eb5
  languageName: node
  linkType: hard

"d3-path@npm:1 - 3, d3-path@npm:3, d3-path@npm:^3.1.0":
  version: 3.1.0
  resolution: "d3-path@npm:3.1.0"
  checksum: 10c0/dc1d58ec87fa8319bd240cf7689995111a124b141428354e9637aa83059eb12e681f77187e0ada5dedfce346f7e3d1f903467ceb41b379bfd01cd8e31721f5da
  languageName: node
  linkType: hard

"d3-polygon@npm:3":
  version: 3.0.1
  resolution: "d3-polygon@npm:3.0.1"
  checksum: 10c0/e236aa7f33efa9a4072907af7dc119f85b150a0716759d4fe5f12f62573018264a6cbde8617fbfa6944a7ae48c1c0c8d3f39ae72e11f66dd471e9b5e668385df
  languageName: node
  linkType: hard

"d3-quadtree@npm:1 - 3, d3-quadtree@npm:3":
  version: 3.0.1
  resolution: "d3-quadtree@npm:3.0.1"
  checksum: 10c0/18302d2548bfecaef788152397edec95a76400fd97d9d7f42a089ceb68d910f685c96579d74e3712d57477ed042b056881b47cd836a521de683c66f47ce89090
  languageName: node
  linkType: hard

"d3-random@npm:3":
  version: 3.0.1
  resolution: "d3-random@npm:3.0.1"
  checksum: 10c0/987a1a1bcbf26e6cf01fd89d5a265b463b2cea93560fc17d9b1c45e8ed6ff2db5924601bcceb808de24c94133f000039eb7fa1c469a7a844ccbf1170cbb25b41
  languageName: node
  linkType: hard

"d3-sankey@npm:^0.12.3":
  version: 0.12.3
  resolution: "d3-sankey@npm:0.12.3"
  dependencies:
    d3-array: "npm:1 - 2"
    d3-shape: "npm:^1.2.0"
  checksum: 10c0/261debb01a13269f6fc53b9ebaef174a015d5ad646242c23995bf514498829ab8b8f920a7873724a7494288b46bea3ce7ebc5a920b745bc8ae4caa5885cf5204
  languageName: node
  linkType: hard

"d3-scale-chromatic@npm:3":
  version: 3.1.0
  resolution: "d3-scale-chromatic@npm:3.1.0"
  dependencies:
    d3-color: "npm:1 - 3"
    d3-interpolate: "npm:1 - 3"
  checksum: 10c0/9a3f4671ab0b971f4a411b42180d7cf92bfe8e8584e637ce7e698d705e18d6d38efbd20ec64f60cc0dfe966c20d40fc172565bc28aaa2990c0a006360eed91af
  languageName: node
  linkType: hard

"d3-scale@npm:4":
  version: 4.0.2
  resolution: "d3-scale@npm:4.0.2"
  dependencies:
    d3-array: "npm:2.10.0 - 3"
    d3-format: "npm:1 - 3"
    d3-interpolate: "npm:1.2.0 - 3"
    d3-time: "npm:2.1.1 - 3"
    d3-time-format: "npm:2 - 4"
  checksum: 10c0/65d9ad8c2641aec30ed5673a7410feb187a224d6ca8d1a520d68a7d6eac9d04caedbff4713d1e8545be33eb7fec5739983a7ab1d22d4e5ad35368c6729d362f1
  languageName: node
  linkType: hard

"d3-selection@npm:2 - 3, d3-selection@npm:3, d3-selection@npm:^3.0.0":
  version: 3.0.0
  resolution: "d3-selection@npm:3.0.0"
  checksum: 10c0/e59096bbe8f0cb0daa1001d9bdd6dbc93a688019abc97d1d8b37f85cd3c286a6875b22adea0931b0c88410d025563e1643019161a883c516acf50c190a11b56b
  languageName: node
  linkType: hard

"d3-shape@npm:3":
  version: 3.2.0
  resolution: "d3-shape@npm:3.2.0"
  dependencies:
    d3-path: "npm:^3.1.0"
  checksum: 10c0/f1c9d1f09926daaf6f6193ae3b4c4b5521e81da7d8902d24b38694517c7f527ce3c9a77a9d3a5722ad1e3ff355860b014557b450023d66a944eabf8cfde37132
  languageName: node
  linkType: hard

"d3-shape@npm:^1.2.0":
  version: 1.3.7
  resolution: "d3-shape@npm:1.3.7"
  dependencies:
    d3-path: "npm:1"
  checksum: 10c0/548057ce59959815decb449f15632b08e2a1bdce208f9a37b5f98ec7629dda986c2356bc7582308405ce68aedae7d47b324df41507404df42afaf352907577ae
  languageName: node
  linkType: hard

"d3-time-format@npm:2 - 4, d3-time-format@npm:4":
  version: 4.1.0
  resolution: "d3-time-format@npm:4.1.0"
  dependencies:
    d3-time: "npm:1 - 3"
  checksum: 10c0/735e00fb25a7fd5d418fac350018713ae394eefddb0d745fab12bbff0517f9cdb5f807c7bbe87bb6eeb06249662f8ea84fec075f7d0cd68609735b2ceb29d206
  languageName: node
  linkType: hard

"d3-time@npm:1 - 3, d3-time@npm:2.1.1 - 3, d3-time@npm:3":
  version: 3.1.0
  resolution: "d3-time@npm:3.1.0"
  dependencies:
    d3-array: "npm:2 - 3"
  checksum: 10c0/a984f77e1aaeaa182679b46fbf57eceb6ebdb5f67d7578d6f68ef933f8eeb63737c0949991618a8d29472dbf43736c7d7f17c452b2770f8c1271191cba724ca1
  languageName: node
  linkType: hard

"d3-timer@npm:1 - 3, d3-timer@npm:3":
  version: 3.0.1
  resolution: "d3-timer@npm:3.0.1"
  checksum: 10c0/d4c63cb4bb5461d7038aac561b097cd1c5673969b27cbdd0e87fa48d9300a538b9e6f39b4a7f0e3592ef4f963d858c8a9f0e92754db73116770856f2fc04561a
  languageName: node
  linkType: hard

"d3-transition@npm:2 - 3, d3-transition@npm:3":
  version: 3.0.1
  resolution: "d3-transition@npm:3.0.1"
  dependencies:
    d3-color: "npm:1 - 3"
    d3-dispatch: "npm:1 - 3"
    d3-ease: "npm:1 - 3"
    d3-interpolate: "npm:1 - 3"
    d3-timer: "npm:1 - 3"
  peerDependencies:
    d3-selection: 2 - 3
  checksum: 10c0/4e74535dda7024aa43e141635b7522bb70cf9d3dfefed975eb643b36b864762eca67f88fafc2ca798174f83ca7c8a65e892624f824b3f65b8145c6a1a88dbbad
  languageName: node
  linkType: hard

"d3-zoom@npm:3, d3-zoom@npm:^3.0.0":
  version: 3.0.0
  resolution: "d3-zoom@npm:3.0.0"
  dependencies:
    d3-dispatch: "npm:1 - 3"
    d3-drag: "npm:2 - 3"
    d3-interpolate: "npm:1 - 3"
    d3-selection: "npm:2 - 3"
    d3-transition: "npm:2 - 3"
  checksum: 10c0/ee2036479049e70d8c783d594c444fe00e398246048e3f11a59755cd0e21de62ece3126181b0d7a31bf37bcf32fd726f83ae7dea4495ff86ec7736ce5ad36fd3
  languageName: node
  linkType: hard

"d3@npm:^7.9.0":
  version: 7.9.0
  resolution: "d3@npm:7.9.0"
  dependencies:
    d3-array: "npm:3"
    d3-axis: "npm:3"
    d3-brush: "npm:3"
    d3-chord: "npm:3"
    d3-color: "npm:3"
    d3-contour: "npm:4"
    d3-delaunay: "npm:6"
    d3-dispatch: "npm:3"
    d3-drag: "npm:3"
    d3-dsv: "npm:3"
    d3-ease: "npm:3"
    d3-fetch: "npm:3"
    d3-force: "npm:3"
    d3-format: "npm:3"
    d3-geo: "npm:3"
    d3-hierarchy: "npm:3"
    d3-interpolate: "npm:3"
    d3-path: "npm:3"
    d3-polygon: "npm:3"
    d3-quadtree: "npm:3"
    d3-random: "npm:3"
    d3-scale: "npm:4"
    d3-scale-chromatic: "npm:3"
    d3-selection: "npm:3"
    d3-shape: "npm:3"
    d3-time: "npm:3"
    d3-time-format: "npm:4"
    d3-timer: "npm:3"
    d3-transition: "npm:3"
    d3-zoom: "npm:3"
  checksum: 10c0/3dd9c08c73cfaa69c70c49e603c85e049c3904664d9c79a1a52a0f52795828a1ff23592dc9a7b2257e711d68a615472a13103c212032f38e016d609796e087e8
  languageName: node
  linkType: hard

"data-urls@npm:^5.0.0":
  version: 5.0.0
  resolution: "data-urls@npm:5.0.0"
  dependencies:
    whatwg-mimetype: "npm:^4.0.0"
    whatwg-url: "npm:^14.0.0"
  checksum: 10c0/1b894d7d41c861f3a4ed2ae9b1c3f0909d4575ada02e36d3d3bc584bdd84278e20709070c79c3b3bff7ac98598cb191eb3e86a89a79ea4ee1ef360e1694f92ad
  languageName: node
  linkType: hard

"data-view-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-buffer@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 10c0/7986d40fc7979e9e6241f85db8d17060dd9a71bd53c894fa29d126061715e322a4cd47a00b0b8c710394854183d4120462b980b8554012acc1c0fa49df7ad38c
  languageName: node
  linkType: hard

"data-view-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 10c0/f8a4534b5c69384d95ac18137d381f18a5cfae1f0fc1df0ef6feef51ef0d568606d970b69e02ea186c6c0f0eac77fe4e6ad96fec2569cc86c3afcc7475068c55
  languageName: node
  linkType: hard

"data-view-byte-offset@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-byte-offset@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.1"
  checksum: 10c0/fa7aa40078025b7810dcffc16df02c480573b7b53ef1205aa6a61533011005c1890e5ba17018c692ce7c900212b547262d33279fde801ad9843edc0863bf78c4
  languageName: node
  linkType: hard

"date-fns@npm:^4.1.0":
  version: 4.1.0
  resolution: "date-fns@npm:4.1.0"
  checksum: 10c0/b79ff32830e6b7faa009590af6ae0fb8c3fd9ffad46d930548fbb5acf473773b4712ae887e156ba91a7b3dc30591ce0f517d69fd83bd9c38650fdc03b4e0bac8
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.4":
  version: 4.4.0
  resolution: "debug@npm:4.4.0"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/db94f1a182bf886f57b4755f85b3a74c39b5114b9377b7ab375dc2cfa3454f09490cc6c30f829df3fc8042bc8b8995f6567ce5cd96f3bc3688bd24027197d9de
  languageName: node
  linkType: hard

"debug@npm:^2.2.0":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: "npm:2.0.0"
  checksum: 10c0/121908fb839f7801180b69a7e218a40b5a0b718813b886b7d6bdb82001b931c938e2941d1e4450f33a1b1df1da653f5f7a0440c197f29fbf8a6e9d45ff6ef589
  languageName: node
  linkType: hard

"debug@npm:^4.4.1":
  version: 4.4.1
  resolution: "debug@npm:4.4.1"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/d2b44bc1afd912b49bb7ebb0d50a860dc93a4dd7d946e8de94abc957bb63726b7dd5aa48c18c2386c379ec024c46692e15ed3ed97d481729f929201e671fcd55
  languageName: node
  linkType: hard

"decimal.js@npm:^10.5.0":
  version: 10.5.0
  resolution: "decimal.js@npm:10.5.0"
  checksum: 10c0/785c35279df32762143914668df35948920b6c1c259b933e0519a69b7003fc0a5ed2a766b1e1dda02574450c566b21738a45f15e274b47c2ac02072c0d1f3ac3
  languageName: node
  linkType: hard

"deep-eql@npm:^5.0.1":
  version: 5.0.2
  resolution: "deep-eql@npm:5.0.2"
  checksum: 10c0/7102cf3b7bb719c6b9c0db2e19bf0aa9318d141581befe8c7ce8ccd39af9eaa4346e5e05adef7f9bd7015da0f13a3a25dcfe306ef79dc8668aedbecb658dd247
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: 10c0/7f0ee496e0dff14a573dc6127f14c95061b448b87b995fc96c017ce0a1e66af1675e73f1d6064407975bc4ea6ab679497a29fff7b5b9c4e99cb10797c1ad0b4c
  languageName: node
  linkType: hard

"deepmerge@npm:^4.0.0":
  version: 4.3.1
  resolution: "deepmerge@npm:4.3.1"
  checksum: 10c0/e53481aaf1aa2c4082b5342be6b6d8ad9dfe387bc92ce197a66dea08bd4265904a087e75e464f14d1347cf2ac8afe1e4c16b266e0561cc5df29382d3c5f80044
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1, define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: "npm:^1.0.0"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.0.1"
  checksum: 10c0/dea0606d1483eb9db8d930d4eac62ca0fa16738b0b3e07046cddfacf7d8c868bbe13fa0cb263eb91c7d0d527960dc3f2f2471a69ed7816210307f6744fe62e37
  languageName: node
  linkType: hard

"define-properties@npm:^1.1.3, define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.0"
    object-keys: "npm:^1.1.1"
  checksum: 10c0/88a152319ffe1396ccc6ded510a3896e77efac7a1bfbaa174a7b00414a1747377e0bb525d303794a47cf30e805c2ec84e575758512c6e44a993076d29fd4e6c3
  languageName: node
  linkType: hard

"delaunator@npm:5":
  version: 5.0.1
  resolution: "delaunator@npm:5.0.1"
  dependencies:
    robust-predicates: "npm:^3.0.2"
  checksum: 10c0/3d7ea4d964731c5849af33fec0a271bc6753487b331fd7d43ccb17d77834706e1c383e6ab8fda0032da955e7576d1083b9603cdaf9cbdfd6b3ebd1fb8bb675a5
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 10c0/d758899da03392e6712f042bec80aa293bbe9e9ff1b2634baae6a360113e708b91326594c8a486d475c69d6259afb7efacdc3537bfcda1c6c648e390ce601b19
  languageName: node
  linkType: hard

"dequal@npm:^2.0.3":
  version: 2.0.3
  resolution: "dequal@npm:2.0.3"
  checksum: 10c0/f98860cdf58b64991ae10205137c0e97d384c3a4edc7f807603887b7c4b850af1224a33d88012009f150861cbee4fa2d322c4cc04b9313bee312e47f6ecaa888
  languageName: node
  linkType: hard

"detect-libc@npm:^1.0.3":
  version: 1.0.3
  resolution: "detect-libc@npm:1.0.3"
  bin:
    detect-libc: ./bin/detect-libc.js
  checksum: 10c0/4da0deae9f69e13bc37a0902d78bf7169480004b1fed3c19722d56cff578d16f0e11633b7fbf5fb6249181236c72e90024cbd68f0b9558ae06e281f47326d50d
  languageName: node
  linkType: hard

"doctrine@npm:^2.1.0":
  version: 2.1.0
  resolution: "doctrine@npm:2.1.0"
  dependencies:
    esutils: "npm:^2.0.2"
  checksum: 10c0/b6416aaff1f380bf56c3b552f31fdf7a69b45689368deca72d28636f41c16bb28ec3ebc40ace97db4c1afc0ceeb8120e8492fe0046841c94c2933b2e30a7d5ac
  languageName: node
  linkType: hard

"dom-accessibility-api@npm:^0.5.9":
  version: 0.5.16
  resolution: "dom-accessibility-api@npm:0.5.16"
  checksum: 10c0/b2c2eda4fae568977cdac27a9f0c001edf4f95a6a6191dfa611e3721db2478d1badc01db5bb4fa8a848aeee13e442a6c2a4386d65ec65a1436f24715a2f8d053
  languageName: node
  linkType: hard

"dom-accessibility-api@npm:^0.6.3":
  version: 0.6.3
  resolution: "dom-accessibility-api@npm:0.6.3"
  checksum: 10c0/10bee5aa514b2a9a37c87cd81268db607a2e933a050074abc2f6fa3da9080ebed206a320cbc123567f2c3087d22292853bdfdceaffdd4334ffe2af9510b29360
  languageName: node
  linkType: hard

"dom-serializer@npm:^2.0.0":
  version: 2.0.0
  resolution: "dom-serializer@npm:2.0.0"
  dependencies:
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.2"
    entities: "npm:^4.2.0"
  checksum: 10c0/d5ae2b7110ca3746b3643d3ef60ef823f5f078667baf530cec096433f1627ec4b6fa8c072f09d079d7cda915fd2c7bc1b7b935681e9b09e591e1e15f4040b8e2
  languageName: node
  linkType: hard

"domelementtype@npm:^2.3.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: 10c0/686f5a9ef0fff078c1412c05db73a0dce096190036f33e400a07e2a4518e9f56b1e324f5c576a0a747ef0e75b5d985c040b0d51945ce780c0dd3c625a18cd8c9
  languageName: node
  linkType: hard

"domhandler@npm:^5.0.2, domhandler@npm:^5.0.3":
  version: 5.0.3
  resolution: "domhandler@npm:5.0.3"
  dependencies:
    domelementtype: "npm:^2.3.0"
  checksum: 10c0/bba1e5932b3e196ad6862286d76adc89a0dbf0c773e5ced1eb01f9af930c50093a084eff14b8de5ea60b895c56a04d5de8bbc4930c5543d029091916770b2d2a
  languageName: node
  linkType: hard

"dompurify@npm:^3.2.4":
  version: 3.2.4
  resolution: "dompurify@npm:3.2.4"
  dependencies:
    "@types/trusted-types": "npm:^2.0.7"
  dependenciesMeta:
    "@types/trusted-types":
      optional: true
  checksum: 10c0/6be56810fb7ad2776155c8fc2967af5056783c030094362c7d0cf1ad13f2129cf922d8eefab528a34bdebfb98e2f44b306a983ab93aefb9d6f24c18a3d027a05
  languageName: node
  linkType: hard

"dompurify@npm:^3.2.6":
  version: 3.2.6
  resolution: "dompurify@npm:3.2.6"
  dependencies:
    "@types/trusted-types": "npm:^2.0.7"
  dependenciesMeta:
    "@types/trusted-types":
      optional: true
  checksum: 10c0/c8f8e5b0879a0d93c84a2e5e78649a47d0c057ed0f7850ca3d573d2cca64b84fb1ff85bd4b20980ade69c4e5b80ae73011340f1c2ff375c7ef98bb8268e1d13a
  languageName: node
  linkType: hard

"domutils@npm:^3.0.1, domutils@npm:^3.1.0":
  version: 3.2.2
  resolution: "domutils@npm:3.2.2"
  dependencies:
    dom-serializer: "npm:^2.0.0"
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
  checksum: 10c0/47938f473b987ea71cd59e59626eb8666d3aa8feba5266e45527f3b636c7883cca7e582d901531961f742c519d7514636b7973353b648762b2e3bedbf235fada
  languageName: node
  linkType: hard

"downshift@npm:9.0.9":
  version: 9.0.9
  resolution: "downshift@npm:9.0.9"
  dependencies:
    "@babel/runtime": "npm:^7.24.5"
    compute-scroll-into-view: "npm:^3.1.0"
    prop-types: "npm:^15.8.1"
    react-is: "npm:18.2.0"
    tslib: "npm:^2.6.2"
  peerDependencies:
    react: ">=16.12.0"
  checksum: 10c0/9102d498286e7ec41d0036aa753c3865096d307e91b56e5eb466e0f3c54b13cfcb900f91bae50e079ab0888eb00fd46dcd83b78ec126a8dd3ea86d94a26685cc
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.0, dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 10c0/199f2a0c1c16593ca0a145dbf76a962f8033ce3129f01284d48c45ed4e14fea9bbacd7b3610b6cdc33486cef20385ac054948fefc6272fcce645c09468f93031
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10c0/26f364ebcdb6395f95124fda411f63137a4bfb5d3a06453f7f23dfe52502905bd84e0488172e0f9ec295fdc45f05c23d5d91baf16bd26f0fe9acd777a188dc39
  languageName: node
  linkType: hard

"echarts-for-react@npm:^3.0.2":
  version: 3.0.3
  resolution: "echarts-for-react@npm:3.0.3"
  dependencies:
    fast-deep-equal: "npm:^3.1.3"
    size-sensor: "npm:^1.0.1"
  peerDependencies:
    echarts: ^3.0.0 || ^4.0.0 || ^5.0.0
    react: ^15.0.0 || >=16.0.0
  checksum: 10c0/7ab4ed2bc34e9e862c997891d44c90d36d0703d5b74cbb8427543ee9519b70c91c9a01d651f59cfcc4f33ae5444cdc560b5c89d1983fe39d67fc5b0dbba7f282
  languageName: node
  linkType: hard

"echarts@npm:^5.6.0":
  version: 5.6.0
  resolution: "echarts@npm:5.6.0"
  dependencies:
    tslib: "npm:2.3.0"
    zrender: "npm:5.6.1"
  checksum: 10c0/6d6a2ee88534d1ff0433e935c542237b9896de1c94959f47ebc7e0e9da26f59bf11c91ed6fc135b62ad2786c779ee12bc536fa481e60532dad5b6a2f5167e9ea
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.160":
  version: 1.5.170
  resolution: "electron-to-chromium@npm:1.5.170"
  checksum: 10c0/138c6d293b310729f7550f6d36ae4b2bdd0453220e8b11d8780395cf38c9ae816b1f30a3978d790f1951cfa89e7c7911af4a779094c33ae167c89c7ba6751834
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.73":
  version: 1.5.120
  resolution: "electron-to-chromium@npm:1.5.120"
  checksum: 10c0/807c88e912748665a1eacd356672df88ce1ee4e6476c2bef084ee9e7ba184d7a94512a5f98218ab6deab920e78cbaec40f6cba75277c381fcc294e538a25d06d
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10c0/b6053ad39951c4cf338f9092d7bfba448cdfd46fe6a2a034700b149ac9ffbc137e361cbd3c442297f86bed2e5f7576c1b54cc0a6bf8ef5106cc62f496af35010
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10c0/af014e759a72064cf66e6e694a7fc6b0ed3d8db680427b021a89727689671cefe9d04151b2cad51dbaf85d5ba790d061cd167f1cf32eb7b281f6368b3c181639
  languageName: node
  linkType: hard

"encoding-sniffer@npm:^0.2.0":
  version: 0.2.0
  resolution: "encoding-sniffer@npm:0.2.0"
  dependencies:
    iconv-lite: "npm:^0.6.3"
    whatwg-encoding: "npm:^3.1.1"
  checksum: 10c0/b312e0d67f339bec44e021e5210ee8ee90d7b8f9975eb2c79a36fd467eb07709e88dcf62ee20f62ee0d74a13874307d99557852a2de9b448f1e3fb991fc68257
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10c0/36d938712ff00fe1f4bac88b43bcffb5930c1efa57bbcdca9d67e1d9d6c57cfb1200fb01efe0f3109b2ce99b231f90779532814a81370a1bd3274a0f58585039
  languageName: node
  linkType: hard

"ensure-posix-path@npm:^1.1.0":
  version: 1.1.1
  resolution: "ensure-posix-path@npm:1.1.1"
  checksum: 10c0/17133fad88bac9b76e5a0690192d5c7bd6f08bdef618e2c1c0c1fcd3b0960f298a4226af5fe6401e729fc09534d0bb68b9e6f388e92d8a140a9d4a61a97e9641
  languageName: node
  linkType: hard

"ensure-type@npm:^1.5.0":
  version: 1.5.1
  resolution: "ensure-type@npm:1.5.1"
  checksum: 10c0/5ae2bfaa99a72c1f2169525878e300e9b6a68fdcf1cd8e359b799d9cb2265b6bf5dafb5c07dbf66abbba3a216c950bbc1280ff1515059ef28681e4d88fd75ee8
  languageName: node
  linkType: hard

"entities@npm:^4.2.0, entities@npm:^4.5.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: 10c0/5b039739f7621f5d1ad996715e53d964035f75ad3b9a4d38c6b3804bb226e282ffeae2443624d8fdd9c47d8e926ae9ac009c54671243f0c3294c26af7cc85250
  languageName: node
  linkType: hard

"entities@npm:^6.0.0":
  version: 6.0.1
  resolution: "entities@npm:6.0.1"
  checksum: 10c0/ed836ddac5acb34341094eb495185d527bd70e8632b6c0d59548cbfa23defdbae70b96f9a405c82904efa421230b5b3fd2283752447d737beffd3f3e6ee74414
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10c0/285325677bf00e30845e330eec32894f5105529db97496ee3f598478e50f008c5352a41a30e5e72ec9de8a542b5a570b85699cd63bd2bc646dbcb9f311d83bc4
  languageName: node
  linkType: hard

"eol@npm:^0.9.1":
  version: 0.9.1
  resolution: "eol@npm:0.9.1"
  checksum: 10c0/5a6654ca1961529429f4eab4473e6d9351969f25baa30de7232e862c6c5f9037fc0ff044a526fe9cdd6ae65bb1b0db7775bf1d4f342f485c10c34b1444bfb7ab
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10c0/b642f7b4dd4a376e954947550a3065a9ece6733ab8e51ad80db727aaae0817c2e99b02a97a3d6cecc648a97848305e728289cf312d09af395403a90c9d4d8a66
  languageName: node
  linkType: hard

"es-abstract@npm:^1.17.5, es-abstract@npm:^1.23.2, es-abstract@npm:^1.23.3, es-abstract@npm:^1.23.5, es-abstract@npm:^1.23.6, es-abstract@npm:^1.23.9":
  version: 1.23.9
  resolution: "es-abstract@npm:1.23.9"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.2"
    arraybuffer.prototype.slice: "npm:^1.0.4"
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    data-view-buffer: "npm:^1.0.2"
    data-view-byte-length: "npm:^1.0.2"
    data-view-byte-offset: "npm:^1.0.1"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    es-set-tostringtag: "npm:^2.1.0"
    es-to-primitive: "npm:^1.3.0"
    function.prototype.name: "npm:^1.1.8"
    get-intrinsic: "npm:^1.2.7"
    get-proto: "npm:^1.0.0"
    get-symbol-description: "npm:^1.1.0"
    globalthis: "npm:^1.0.4"
    gopd: "npm:^1.2.0"
    has-property-descriptors: "npm:^1.0.2"
    has-proto: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    internal-slot: "npm:^1.1.0"
    is-array-buffer: "npm:^3.0.5"
    is-callable: "npm:^1.2.7"
    is-data-view: "npm:^1.0.2"
    is-regex: "npm:^1.2.1"
    is-shared-array-buffer: "npm:^1.0.4"
    is-string: "npm:^1.1.1"
    is-typed-array: "npm:^1.1.15"
    is-weakref: "npm:^1.1.0"
    math-intrinsics: "npm:^1.1.0"
    object-inspect: "npm:^1.13.3"
    object-keys: "npm:^1.1.1"
    object.assign: "npm:^4.1.7"
    own-keys: "npm:^1.0.1"
    regexp.prototype.flags: "npm:^1.5.3"
    safe-array-concat: "npm:^1.1.3"
    safe-push-apply: "npm:^1.0.0"
    safe-regex-test: "npm:^1.1.0"
    set-proto: "npm:^1.0.0"
    string.prototype.trim: "npm:^1.2.10"
    string.prototype.trimend: "npm:^1.0.9"
    string.prototype.trimstart: "npm:^1.0.8"
    typed-array-buffer: "npm:^1.0.3"
    typed-array-byte-length: "npm:^1.0.3"
    typed-array-byte-offset: "npm:^1.0.4"
    typed-array-length: "npm:^1.0.7"
    unbox-primitive: "npm:^1.1.0"
    which-typed-array: "npm:^1.1.18"
  checksum: 10c0/1de229c9e08fe13c17fe5abaec8221545dfcd57e51f64909599a6ae896df84b8fd2f7d16c60cb00d7bf495b9298ca3581aded19939d4b7276854a4b066f8422b
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0, es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 10c0/3f54eb49c16c18707949ff25a1456728c883e81259f045003499efba399c08bad00deebf65cccde8c0e07908c1a225c9d472b7107e558f2a48e28d530e34527c
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10c0/0a61325670072f98d8ae3b914edab3559b6caa980f08054a3b872052640d91da01d38df55df797fcc916389d77fc92b8d5906cf028f4db46d7e3003abecbca85
  languageName: node
  linkType: hard

"es-iterator-helpers@npm:^1.2.1":
  version: 1.2.1
  resolution: "es-iterator-helpers@npm:1.2.1"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.6"
    es-errors: "npm:^1.3.0"
    es-set-tostringtag: "npm:^2.0.3"
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.6"
    globalthis: "npm:^1.0.4"
    gopd: "npm:^1.2.0"
    has-property-descriptors: "npm:^1.0.2"
    has-proto: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    internal-slot: "npm:^1.1.0"
    iterator.prototype: "npm:^1.1.4"
    safe-array-concat: "npm:^1.1.3"
  checksum: 10c0/97e3125ca472d82d8aceea11b790397648b52c26d8768ea1c1ee6309ef45a8755bb63225a43f3150c7591cffc17caf5752459f1e70d583b4184370a8f04ebd2f
  languageName: node
  linkType: hard

"es-module-lexer@npm:^1.7.0":
  version: 1.7.0
  resolution: "es-module-lexer@npm:1.7.0"
  checksum: 10c0/4c935affcbfeba7fb4533e1da10fa8568043df1e3574b869385980de9e2d475ddc36769891936dbb07036edb3c3786a8b78ccf44964cd130dedc1f2c984b6c7b
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10c0/65364812ca4daf48eb76e2a3b7a89b3f6a2e62a1c420766ce9f692665a29d94fe41fe88b65f24106f449859549711e4b40d9fb8002d862dfd7eb1c512d10be0c
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.0.3, es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10c0/ef2ca9ce49afe3931cb32e35da4dcb6d86ab02592cfc2ce3e49ced199d9d0bb5085fc7e73e06312213765f5efa47cc1df553a6a5154584b21448e9fb8355b1af
  languageName: node
  linkType: hard

"es-shim-unscopables@npm:^1.0.2":
  version: 1.1.0
  resolution: "es-shim-unscopables@npm:1.1.0"
  dependencies:
    hasown: "npm:^2.0.2"
  checksum: 10c0/1b9702c8a1823fc3ef39035a4e958802cf294dd21e917397c561d0b3e195f383b978359816b1732d02b255ccf63e1e4815da0065b95db8d7c992037be3bbbcdb
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-to-primitive@npm:1.3.0"
  dependencies:
    is-callable: "npm:^1.2.7"
    is-date-object: "npm:^1.0.5"
    is-symbol: "npm:^1.0.4"
  checksum: 10c0/c7e87467abb0b438639baa8139f701a06537d2b9bc758f23e8622c3b42fd0fdb5bde0f535686119e446dd9d5e4c0f238af4e14960f4771877cf818d023f6730b
  languageName: node
  linkType: hard

"es-toolkit@npm:^1.27.0":
  version: 1.33.0
  resolution: "es-toolkit@npm:1.33.0"
  dependenciesMeta:
    "@trivago/prettier-plugin-sort-imports@4.3.0":
      unplugged: true
    prettier-plugin-sort-re-exports@0.0.1:
      unplugged: true
  checksum: 10c0/4c8dea3167a813070812e5c3f827fb677b4729b622c209cfad68dd5b449a008df6f3b515e675a4a8519618f52b87fe1d157c320668be871165f934a15c1d2f37
  languageName: node
  linkType: hard

"esbuild@npm:^0.25.0":
  version: 0.25.1
  resolution: "esbuild@npm:0.25.1"
  dependencies:
    "@esbuild/aix-ppc64": "npm:0.25.1"
    "@esbuild/android-arm": "npm:0.25.1"
    "@esbuild/android-arm64": "npm:0.25.1"
    "@esbuild/android-x64": "npm:0.25.1"
    "@esbuild/darwin-arm64": "npm:0.25.1"
    "@esbuild/darwin-x64": "npm:0.25.1"
    "@esbuild/freebsd-arm64": "npm:0.25.1"
    "@esbuild/freebsd-x64": "npm:0.25.1"
    "@esbuild/linux-arm": "npm:0.25.1"
    "@esbuild/linux-arm64": "npm:0.25.1"
    "@esbuild/linux-ia32": "npm:0.25.1"
    "@esbuild/linux-loong64": "npm:0.25.1"
    "@esbuild/linux-mips64el": "npm:0.25.1"
    "@esbuild/linux-ppc64": "npm:0.25.1"
    "@esbuild/linux-riscv64": "npm:0.25.1"
    "@esbuild/linux-s390x": "npm:0.25.1"
    "@esbuild/linux-x64": "npm:0.25.1"
    "@esbuild/netbsd-arm64": "npm:0.25.1"
    "@esbuild/netbsd-x64": "npm:0.25.1"
    "@esbuild/openbsd-arm64": "npm:0.25.1"
    "@esbuild/openbsd-x64": "npm:0.25.1"
    "@esbuild/sunos-x64": "npm:0.25.1"
    "@esbuild/win32-arm64": "npm:0.25.1"
    "@esbuild/win32-ia32": "npm:0.25.1"
    "@esbuild/win32-x64": "npm:0.25.1"
  dependenciesMeta:
    "@esbuild/aix-ppc64":
      optional: true
    "@esbuild/android-arm":
      optional: true
    "@esbuild/android-arm64":
      optional: true
    "@esbuild/android-x64":
      optional: true
    "@esbuild/darwin-arm64":
      optional: true
    "@esbuild/darwin-x64":
      optional: true
    "@esbuild/freebsd-arm64":
      optional: true
    "@esbuild/freebsd-x64":
      optional: true
    "@esbuild/linux-arm":
      optional: true
    "@esbuild/linux-arm64":
      optional: true
    "@esbuild/linux-ia32":
      optional: true
    "@esbuild/linux-loong64":
      optional: true
    "@esbuild/linux-mips64el":
      optional: true
    "@esbuild/linux-ppc64":
      optional: true
    "@esbuild/linux-riscv64":
      optional: true
    "@esbuild/linux-s390x":
      optional: true
    "@esbuild/linux-x64":
      optional: true
    "@esbuild/netbsd-arm64":
      optional: true
    "@esbuild/netbsd-x64":
      optional: true
    "@esbuild/openbsd-arm64":
      optional: true
    "@esbuild/openbsd-x64":
      optional: true
    "@esbuild/sunos-x64":
      optional: true
    "@esbuild/win32-arm64":
      optional: true
    "@esbuild/win32-ia32":
      optional: true
    "@esbuild/win32-x64":
      optional: true
  bin:
    esbuild: bin/esbuild
  checksum: 10c0/80fca30dd0f21aec23fdfab34f0a8d5f55df5097dd7f475f2ab561d45662c32ee306f5649071cd1a0ba0614b164c48ca3dc3ee1551a4daf204b8af90e4d893f5
  languageName: node
  linkType: hard

"escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 10c0/ced4dd3a78e15897ed3be74e635110bbf3b08877b0a41be50dcb325ee0e0b5f65fc2d50e9845194d7c4633f327e2e1c6cce00a71b617c5673df0374201d67f65
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 10c0/a968ad453dd0c2724e14a4f20e177aaf32bb384ab41b674a8454afe9a41c5e6fe8903323e0a1052f56289d04bd600f81278edf140b0fcc02f5cac98d0f5b5371
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 10c0/9497d4dd307d845bd7f75180d8188bb17ea8c151c1edbf6b6717c100e104d629dc2dfb687686181b0f4b7d732c7dfdc4d5e7a8ff72de1b0ca283a75bbb3a9cd9
  languageName: node
  linkType: hard

"eslint-config-prettier@npm:10.1.8":
  version: 10.1.8
  resolution: "eslint-config-prettier@npm:10.1.8"
  peerDependencies:
    eslint: ">=7.0.0"
  bin:
    eslint-config-prettier: bin/cli.js
  checksum: 10c0/e1bcfadc9eccd526c240056b1e59c5cd26544fe59feb85f38f4f1f116caed96aea0b3b87868e68b3099e55caaac3f2e5b9f58110f85db893e83a332751192682
  languageName: node
  linkType: hard

"eslint-plugin-react@npm:7.37.5":
  version: 7.37.5
  resolution: "eslint-plugin-react@npm:7.37.5"
  dependencies:
    array-includes: "npm:^3.1.8"
    array.prototype.findlast: "npm:^1.2.5"
    array.prototype.flatmap: "npm:^1.3.3"
    array.prototype.tosorted: "npm:^1.1.4"
    doctrine: "npm:^2.1.0"
    es-iterator-helpers: "npm:^1.2.1"
    estraverse: "npm:^5.3.0"
    hasown: "npm:^2.0.2"
    jsx-ast-utils: "npm:^2.4.1 || ^3.0.0"
    minimatch: "npm:^3.1.2"
    object.entries: "npm:^1.1.9"
    object.fromentries: "npm:^2.0.8"
    object.values: "npm:^1.2.1"
    prop-types: "npm:^15.8.1"
    resolve: "npm:^2.0.0-next.5"
    semver: "npm:^6.3.1"
    string.prototype.matchall: "npm:^4.0.12"
    string.prototype.repeat: "npm:^1.0.0"
  peerDependencies:
    eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7
  checksum: 10c0/c850bfd556291d4d9234f5ca38db1436924a1013627c8ab1853f77cac73ec19b020e861e6c7b783436a48b6ffcdfba4547598235a37ad4611b6739f65fd8ad57
  languageName: node
  linkType: hard

"eslint-plugin-unicorn@npm:59.0.1":
  version: 59.0.1
  resolution: "eslint-plugin-unicorn@npm:59.0.1"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.25.9"
    "@eslint-community/eslint-utils": "npm:^4.5.1"
    "@eslint/plugin-kit": "npm:^0.2.7"
    ci-info: "npm:^4.2.0"
    clean-regexp: "npm:^1.0.0"
    core-js-compat: "npm:^3.41.0"
    esquery: "npm:^1.6.0"
    find-up-simple: "npm:^1.0.1"
    globals: "npm:^16.0.0"
    indent-string: "npm:^5.0.0"
    is-builtin-module: "npm:^5.0.0"
    jsesc: "npm:^3.1.0"
    pluralize: "npm:^8.0.0"
    regexp-tree: "npm:^0.1.27"
    regjsparser: "npm:^0.12.0"
    semver: "npm:^7.7.1"
    strip-indent: "npm:^4.0.0"
  peerDependencies:
    eslint: ">=9.22.0"
  checksum: 10c0/79f369bac47d788433578a9079392b8ed78bb3d262bc5535f06250173f044b16cc3cd7bf87974d4d27bbdf93b17583a1497a27a0bda2bcaca56c6624ada689d9
  languageName: node
  linkType: hard

"eslint-scope@npm:^8.4.0":
  version: 8.4.0
  resolution: "eslint-scope@npm:8.4.0"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^5.2.0"
  checksum: 10c0/407f6c600204d0f3705bd557f81bd0189e69cd7996f408f8971ab5779c0af733d1af2f1412066b40ee1588b085874fc37a2333986c6521669cdbdd36ca5058e0
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 10c0/92708e882c0a5ffd88c23c0b404ac1628cf20104a108c745f240a13c332a11aac54f49a22d5762efbffc18ecbc9a580d1b7ad034bf5f3cc3307e5cbff2ec9820
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^4.2.0":
  version: 4.2.0
  resolution: "eslint-visitor-keys@npm:4.2.0"
  checksum: 10c0/2ed81c663b147ca6f578312919483eb040295bbab759e5a371953456c636c5b49a559883e2677112453728d66293c0a4c90ab11cab3428cf02a0236d2e738269
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^4.2.1":
  version: 4.2.1
  resolution: "eslint-visitor-keys@npm:4.2.1"
  checksum: 10c0/fcd43999199d6740db26c58dbe0c2594623e31ca307e616ac05153c9272f12f1364f5a0b1917a8e962268fdecc6f3622c1c2908b4fcc2e047a106fe6de69dc43
  languageName: node
  linkType: hard

"eslint@npm:9.33.0":
  version: 9.33.0
  resolution: "eslint@npm:9.33.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.2.0"
    "@eslint-community/regexpp": "npm:^4.12.1"
    "@eslint/config-array": "npm:^0.21.0"
    "@eslint/config-helpers": "npm:^0.3.1"
    "@eslint/core": "npm:^0.15.2"
    "@eslint/eslintrc": "npm:^3.3.1"
    "@eslint/js": "npm:9.33.0"
    "@eslint/plugin-kit": "npm:^0.3.5"
    "@humanfs/node": "npm:^0.16.6"
    "@humanwhocodes/module-importer": "npm:^1.0.1"
    "@humanwhocodes/retry": "npm:^0.4.2"
    "@types/estree": "npm:^1.0.6"
    "@types/json-schema": "npm:^7.0.15"
    ajv: "npm:^6.12.4"
    chalk: "npm:^4.0.0"
    cross-spawn: "npm:^7.0.6"
    debug: "npm:^4.3.2"
    escape-string-regexp: "npm:^4.0.0"
    eslint-scope: "npm:^8.4.0"
    eslint-visitor-keys: "npm:^4.2.1"
    espree: "npm:^10.4.0"
    esquery: "npm:^1.5.0"
    esutils: "npm:^2.0.2"
    fast-deep-equal: "npm:^3.1.3"
    file-entry-cache: "npm:^8.0.0"
    find-up: "npm:^5.0.0"
    glob-parent: "npm:^6.0.2"
    ignore: "npm:^5.2.0"
    imurmurhash: "npm:^0.1.4"
    is-glob: "npm:^4.0.0"
    json-stable-stringify-without-jsonify: "npm:^1.0.1"
    lodash.merge: "npm:^4.6.2"
    minimatch: "npm:^3.1.2"
    natural-compare: "npm:^1.4.0"
    optionator: "npm:^0.9.3"
  peerDependencies:
    jiti: "*"
  peerDependenciesMeta:
    jiti:
      optional: true
  bin:
    eslint: bin/eslint.js
  checksum: 10c0/1e1f60d2b62d9d65553e9af916a8dccf00eeedd982103f35bf58c205803907cb1fda73ef595178d47384ea80d8624a182b63682a6b15d8387e9a5d86904a2a2d
  languageName: node
  linkType: hard

"espree@npm:^10.0.1":
  version: 10.3.0
  resolution: "espree@npm:10.3.0"
  dependencies:
    acorn: "npm:^8.14.0"
    acorn-jsx: "npm:^5.3.2"
    eslint-visitor-keys: "npm:^4.2.0"
  checksum: 10c0/272beeaca70d0a1a047d61baff64db04664a33d7cfb5d144f84bc8a5c6194c6c8ebe9cc594093ca53add88baa23e59b01e69e8a0160ab32eac570482e165c462
  languageName: node
  linkType: hard

"espree@npm:^10.4.0":
  version: 10.4.0
  resolution: "espree@npm:10.4.0"
  dependencies:
    acorn: "npm:^8.15.0"
    acorn-jsx: "npm:^5.3.2"
    eslint-visitor-keys: "npm:^4.2.1"
  checksum: 10c0/c63fe06131c26c8157b4083313cb02a9a54720a08e21543300e55288c40e06c3fc284bdecf108d3a1372c5934a0a88644c98714f38b6ae8ed272b40d9ea08d6b
  languageName: node
  linkType: hard

"esprima-next@npm:^5.7.0":
  version: 5.8.4
  resolution: "esprima-next@npm:5.8.4"
  bin:
    esparse: bin/esparse.js
    esvalidate: bin/esvalidate.js
  checksum: 10c0/eed0f04655e2084d727455f7f8a4f66ff8e948e2bc61eb9e08b98b5c9c4a7eb87d40a574b4264893a8ed57b18ac3425f7fbbe543265f257abe9b328f42b1e39a
  languageName: node
  linkType: hard

"esquery@npm:^1.5.0, esquery@npm:^1.6.0":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: "npm:^5.1.0"
  checksum: 10c0/cb9065ec605f9da7a76ca6dadb0619dfb611e37a81e318732977d90fab50a256b95fee2d925fba7c2f3f0523aa16f91587246693bc09bc34d5a59575fe6e93d2
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: "npm:^5.2.0"
  checksum: 10c0/81a37116d1408ded88ada45b9fb16dbd26fba3aadc369ce50fcaf82a0bac12772ebd7b24cd7b91fc66786bf2c1ac7b5f196bc990a473efff972f5cb338877cf5
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0, estraverse@npm:^5.3.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 10c0/1ff9447b96263dec95d6d67431c5e0771eb9776427421260a3e2f0fdd5d6bd4f8e37a7338f5ad2880c9f143450c9b1e4fc2069060724570a49cf9cf0312bd107
  languageName: node
  linkType: hard

"estree-walker@npm:^3.0.3":
  version: 3.0.3
  resolution: "estree-walker@npm:3.0.3"
  dependencies:
    "@types/estree": "npm:^1.0.0"
  checksum: 10c0/c12e3c2b2642d2bcae7d5aa495c60fa2f299160946535763969a1c83fc74518ffa9c2cd3a8b69ac56aea547df6a8aac25f729a342992ef0bbac5f1c73e78995d
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 10c0/9a2fe69a41bfdade834ba7c42de4723c97ec776e40656919c62cbd13607c45e127a003f05f724a1ea55e5029a4cf2de444b13009f2af71271e42d93a637137c7
  languageName: node
  linkType: hard

"expect-type@npm:^1.2.1":
  version: 1.2.1
  resolution: "expect-type@npm:1.2.1"
  checksum: 10c0/b775c9adab3c190dd0d398c722531726cdd6022849b4adba19dceab58dda7e000a7c6c872408cd73d665baa20d381eca36af4f7b393a4ba60dd10232d1fb8898
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 10c0/d9d3e1eafa21b78464297df91f1776f7fbaa3d5e3f7f0995648ca5b89c069d17055033817348d9f4a43d1c20b0eab84f75af6991751e839df53e4dfd6f22e844
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10c0/40dedc862eb8992c54579c66d914635afbec43350afbbe991235fdcb4e3a8d5af1b23ae7e79bef7d4882d0ecee06c3197488026998fb19f72dc95acff1d1b1d0
  languageName: node
  linkType: hard

"fast-equals@npm:^4.0.3":
  version: 4.0.3
  resolution: "fast-equals@npm:4.0.3"
  checksum: 10c0/87fd2609c945ee61e9ed4d041eb2a8f92723fc02884115f67e429dd858d880279e962334894f116b3e9b223f387d246e3db5424ae779287849015ddadbf5ff27
  languageName: node
  linkType: hard

"fast-fifo@npm:^1.3.2":
  version: 1.3.2
  resolution: "fast-fifo@npm:1.3.2"
  checksum: 10c0/d53f6f786875e8b0529f784b59b4b05d4b5c31c651710496440006a398389a579c8dbcd2081311478b5bf77f4b0b21de69109c5a4eabea9d8e8783d1eb864e4c
  languageName: node
  linkType: hard

"fast-glob@npm:^3.3.2":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.8"
  checksum: 10c0/f6aaa141d0d3384cf73cbcdfc52f475ed293f6d5b65bfc5def368b09163a9f7e5ec2b3014d80f733c405f58e470ee0cc451c2937685045cddcdeaa24199c43fe
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 10c0/7f081eb0b8a64e0057b3bb03f974b3ef00135fbf36c1c710895cd9300f13c94ba809bb3a81cf4e1b03f6e5285610a61abbd7602d0652de423144dfee5a389c9b
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 10c0/111972b37338bcb88f7d9e2c5907862c280ebf4234433b95bc611e518d192ccb2d38119c4ac86e26b668d75f7f3894f4ff5c4982899afced7ca78633b08287c4
  languageName: node
  linkType: hard

"fastq@npm:^1.13.0, fastq@npm:^1.6.0":
  version: 1.19.1
  resolution: "fastq@npm:1.19.1"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 10c0/ebc6e50ac7048daaeb8e64522a1ea7a26e92b3cee5cd1c7f2316cdca81ba543aa40a136b53891446ea5c3a67ec215fbaca87ad405f102dd97012f62916905630
  languageName: node
  linkType: hard

"fdir@npm:^6.4.4, fdir@npm:^6.4.6":
  version: 6.4.6
  resolution: "fdir@npm:6.4.6"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 10c0/45b559cff889934ebb8bc498351e5acba40750ada7e7d6bde197768d2fa67c149be8ae7f8ff34d03f4e1eb20f2764116e56440aaa2f6689e9a4aa7ef06acafe9
  languageName: node
  linkType: hard

"fflate@npm:^0.8.1, fflate@npm:^0.8.2":
  version: 0.8.2
  resolution: "fflate@npm:0.8.2"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"file-entry-cache@npm:^8.0.0":
  version: 8.0.0
  resolution: "file-entry-cache@npm:8.0.0"
  dependencies:
    flat-cache: "npm:^4.0.0"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: 10c0/b75b691bbe065472f38824f694c2f7449d7f5004aa950426a2c28f0306c60db9b880c0b0e4ed819997ffb882d1da02cfcfc819bddc94d71627f5269682edf018
  languageName: node
  linkType: hard

"find-up-simple@npm:^1.0.1":
  version: 1.0.1
  resolution: "find-up-simple@npm:1.0.1"
  checksum: 10c0/ad34de157b7db925d50ff78302fefb28e309f3bc947c93ffca0f9b0bccf9cf1a2dc57d805d5c94ec9fc60f4838f5dbdfd2a48ecd77c23015fa44c6dd5f60bc40
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: "npm:^6.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10c0/062c5a83a9c02f53cdd6d175a37ecf8f87ea5bbff1fdfb828f04bfa021441bc7583e8ebc0872a4c1baab96221fb8a8a275a19809fb93fbc40bd69ec35634069a
  languageName: node
  linkType: hard

"flat-cache@npm:^4.0.0":
  version: 4.0.1
  resolution: "flat-cache@npm:4.0.1"
  dependencies:
    flatted: "npm:^3.2.9"
    keyv: "npm:^4.5.4"
  checksum: 10c0/2c59d93e9faa2523e4fda6b4ada749bed432cfa28c8e251f33b25795e426a1c6dbada777afb1f74fcfff33934fdbdea921ee738fcc33e71adc9d6eca984a1cfc
  languageName: node
  linkType: hard

"flatpickr@npm:4.6.13":
  version: 4.6.13
  resolution: "flatpickr@npm:4.6.13"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9, flatted@npm:^3.3.3":
  version: 3.3.3
  resolution: "flatted@npm:3.3.3"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.15.6":
  version: 1.15.9
  resolution: "follow-redirects@npm:1.15.9"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 10c0/5829165bd112c3c0e82be6c15b1a58fa9dcfaede3b3c54697a82fe4a62dd5ae5e8222956b448d2f98e331525f05d00404aba7d696de9e761ef6e42fdc780244f
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3, for-each@npm:^0.3.5":
  version: 0.3.5
  resolution: "for-each@npm:0.3.5"
  dependencies:
    is-callable: "npm:^1.2.7"
  checksum: 10c0/0e0b50f6a843a282637d43674d1fb278dda1dd85f4f99b640024cfb10b85058aac0cc781bf689d5fe50b4b7f638e91e548560723a4e76e04fe96ae35ef039cee
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: "npm:^7.0.6"
    signal-exit: "npm:^4.0.1"
  checksum: 10c0/8986e4af2430896e65bc2788d6679067294d6aee9545daefc84923a0a4b399ad9c7a3ea7bd8c0b2b80fdf4a92de4c69df3f628233ff3224260e9c1541a9e9ed3
  languageName: node
  linkType: hard

"form-data@npm:^4.0.4":
  version: 4.0.4
  resolution: "form-data@npm:4.0.4"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    es-set-tostringtag: "npm:^2.1.0"
    hasown: "npm:^2.0.2"
    mime-types: "npm:^2.1.12"
  checksum: 10c0/373525a9a034b9d57073e55eab79e501a714ffac02e7a9b01be1c820780652b16e4101819785e1e18f8d98f0aee866cc654d660a435c378e16a72f2e7cac9695
  languageName: node
  linkType: hard

"frac@npm:~1.1.2":
  version: 1.1.2
  resolution: "frac@npm:1.1.2"
  checksum: 10c0/640740eb58b590eb38c78c676955bee91cd22d854f5876241a15c49d4495fa53a84898779dcf7eca30aabfe1c1a4a705752b5f224934257c5dda55c545413ba7
  languageName: node
  linkType: hard

"fs-extra@npm:^11.2.0":
  version: 11.3.0
  resolution: "fs-extra@npm:11.3.0"
  dependencies:
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 10c0/5f95e996186ff45463059feb115a22fb048bdaf7e487ecee8a8646c78ed8fdca63630e3077d4c16ce677051f5e60d3355a06f3cd61f3ca43f48cc58822a44d0a
  languageName: node
  linkType: hard

"fs-extra@npm:^8.0.1, fs-extra@npm:^8.1.0":
  version: 8.1.0
  resolution: "fs-extra@npm:8.1.0"
  dependencies:
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^4.0.0"
    universalify: "npm:^0.1.0"
  checksum: 10c0/259f7b814d9e50d686899550c4f9ded85c46c643f7fe19be69504888e007fcbc08f306fae8ec495b8b998635e997c9e3e175ff2eeed230524ef1c1684cc96423
  languageName: node
  linkType: hard

"fs-merger@npm:^3.2.1":
  version: 3.2.1
  resolution: "fs-merger@npm:3.2.1"
  dependencies:
    broccoli-node-api: "npm:^1.7.0"
    broccoli-node-info: "npm:^2.1.0"
    fs-extra: "npm:^8.0.1"
    fs-tree-diff: "npm:^2.0.1"
    walk-sync: "npm:^2.2.0"
  checksum: 10c0/54a2a5ebf5cecaadff329752a9f67c1025f9f12afcdea97bf44b1625283ead2379bcefadab9d4ee96e15eb5870c97ac34e60eba2982c91e4bc508ed35a31d154
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/63e80da2ff9b621e2cb1596abcb9207f1cf82b968b116ccd7b959e3323144cce7fb141462200971c38bbf2ecca51695069db45265705bed09a7cd93ae5b89f94
  languageName: node
  linkType: hard

"fs-mkdirp-stream@npm:^2.0.1":
  version: 2.0.1
  resolution: "fs-mkdirp-stream@npm:2.0.1"
  dependencies:
    graceful-fs: "npm:^4.2.8"
    streamx: "npm:^2.12.0"
  checksum: 10c0/57d25f59a15acd7a1c5d0c9fc0fee08f9e1224a3010e21eecedf1e6d42672b3e377d10ea41cf8fc86ceb2651601648156af615fd18216318435be48031001ec8
  languageName: node
  linkType: hard

"fs-tree-diff@npm:^2.0.1":
  version: 2.0.1
  resolution: "fs-tree-diff@npm:2.0.1"
  dependencies:
    "@types/symlink-or-copy": "npm:^1.2.0"
    heimdalljs-logger: "npm:^0.1.7"
    object-assign: "npm:^4.1.0"
    path-posix: "npm:^1.0.0"
    symlink-or-copy: "npm:^1.1.8"
  checksum: 10c0/3e5dd4007a24b90a135a1f58be63b03e6c265f15ce2b9f987bf966d04bac3697931aa0329a575ebe5e57ddb44112644b581ce82ef1183fa1e914cee410e8e9ef
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 10c0/444cf1291d997165dfd4c0d58b69f0e4782bfd9149fd72faa4fe299e68e0e93d6db941660b37dd29153bf7186672ececa3b50b7e7249477b03fdf850f287c948
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2, fsevents@npm:~2.3.3":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/a1f0c44595123ed717febbc478aa952e47adfc28e2092be66b8ab1635147254ca6cfe1df792a8997f22716d4cbafc73309899ff7bfac2ac3ad8cf2e4ecc3ec60
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>, fsevents@patch:fsevents@npm%3A~2.3.3#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10c0/d8680ee1e5fcd4c197e4ac33b2b4dce03c71f4d91717292785703db200f5c21f977c568d28061226f9b5900cbcd2c84463646134fd5337e7925e0942bc3f46d5
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.6, function.prototype.name@npm:^1.1.8":
  version: 1.1.8
  resolution: "function.prototype.name@npm:1.1.8"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    functions-have-names: "npm:^1.2.3"
    hasown: "npm:^2.0.2"
    is-callable: "npm:^1.2.7"
  checksum: 10c0/e920a2ab52663005f3cbe7ee3373e3c71c1fb5558b0b0548648cdf3e51961085032458e26c71ff1a8c8c20e7ee7caeb03d43a5d1fa8610c459333323a2e71253
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: 10c0/33e77fd29bddc2d9bb78ab3eb854c165909201f88c75faa8272e35899e2d35a8a642a15e7420ef945e1f64a9670d6aa3ec744106b2aa42be68ca5114025954ca
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: 10c0/782aba6cba65b1bb5af3b095d96249d20edbe8df32dbf4696fd49be2583faf676173bf4809386588828e4dd76a3354fcbeb577bab1c833ccd9fc4577f26103f8
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.4, get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6, get-intrinsic@npm:^1.2.7, get-intrinsic@npm:^1.3.0":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10c0/52c81808af9a8130f581e6a6a83e1ba4a9f703359e7a438d1369a5267a25412322f03dcbd7c549edaef0b6214a0630a28511d7df0130c93cfd380f4fa0b5b66a
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.0, get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/9224acb44603c5526955e83510b9da41baf6ae73f7398875fba50edc5e944223a89c4a72b070fcd78beb5f7bdda58ecb6294adc28f7acfc0da05f76a2399643c
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.1.0":
  version: 1.1.0
  resolution: "get-symbol-description@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/d6a7d6afca375779a4b307738c9e80dbf7afc0bdbe5948768d54ab9653c865523d8920e670991a925936eb524b7cb6a6361d199a760b21d0ca7620194455aa4b
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 10c0/cab87638e2112bee3f839ef5f6e0765057163d39c66be8ec1602f3823da4692297ad4e972de876ea17c44d652978638d2fd583c6713d0eb6591706825020c9ee
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: "npm:^4.0.3"
  checksum: 10c0/317034d88654730230b3f43bb7ad4f7c90257a426e872ea0bf157473ac61c99bf5d205fad8f0185f989be8d2fa6d3c7dce1645d99d545b6ea9089c39f838e7f8
  languageName: node
  linkType: hard

"glob-stream@npm:^8.0.0":
  version: 8.0.2
  resolution: "glob-stream@npm:8.0.2"
  dependencies:
    "@gulpjs/to-absolute-glob": "npm:^4.0.0"
    anymatch: "npm:^3.1.3"
    fastq: "npm:^1.13.0"
    glob-parent: "npm:^6.0.2"
    is-glob: "npm:^4.0.3"
    is-negated-glob: "npm:^1.0.0"
    normalize-path: "npm:^3.0.0"
    streamx: "npm:^2.12.5"
  checksum: 10c0/ba93be61b85d2143d2b21f35caaf5cb8450c5b72fa377353afabb4922862368be2366c45a998662eaa0098957bf1cc599e546a975767a32758c259093f5b069e
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10, glob@npm:^10.3.7, glob@npm:^10.4.1":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10c0/19a9759ea77b8e3ca0a43c2f07ecddc2ad46216b786bb8f993c445aee80d345925a21e5280c7b7c6c59e860a0154b84e4b2b60321fea92cd3c56b4a7489f160e
  languageName: node
  linkType: hard

"glob@npm:^7.1.3":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.1.1"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 10c0/65676153e2b0c9095100fe7f25a778bf45608eeb32c6048cf307f579649bcc30353277b3b898a3792602c65764e5baa4f643714dfbdfd64ea271d210c7a425fe
  languageName: node
  linkType: hard

"globals@npm:16.2.0, globals@npm:^16.0.0":
  version: 16.2.0
  resolution: "globals@npm:16.2.0"
  checksum: 10c0/c2b3ea163faa6f8a38076b471b12f4bda891f7df7f7d2e8294fb4801d735a51a73431bf4c1696c5bf5dbca5e0a0db894698acfcbd3068730c6b12eef185dea25
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 10c0/758f9f258e7b19226bd8d4af5d3b0dcf7038780fb23d82e6f98932c44e239f884847f1766e8fa9cc5635ccb3204f7fa7314d4408dd4002a5e8ea827b4018f0a1
  languageName: node
  linkType: hard

"globals@npm:^14.0.0":
  version: 14.0.0
  resolution: "globals@npm:14.0.0"
  checksum: 10c0/b96ff42620c9231ad468d4c58ff42afee7777ee1c963013ff8aabe095a451d0ceeb8dcd8ef4cbd64d2538cef45f787a78ba3a9574f4a634438963e334471302d
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.4":
  version: 1.0.4
  resolution: "globalthis@npm:1.0.4"
  dependencies:
    define-properties: "npm:^1.2.1"
    gopd: "npm:^1.0.1"
  checksum: 10c0/9d156f313af79d80b1566b93e19285f481c591ad6d0d319b4be5e03750d004dde40a39a0f26f7e635f9007a3600802f53ecd85a759b86f109e80a5f705e01846
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1, gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 10c0/50fff1e04ba2b7737c097358534eacadad1e68d24cccee3272e04e007bed008e68d2614f3987788428fd192a5ae3889d08fb2331417e4fc4a9ab366b2043cead
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.10, graceful-fs@npm:^4.2.11, graceful-fs@npm:^4.2.6, graceful-fs@npm:^4.2.8":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10c0/386d011a553e02bc594ac2ca0bd6d9e4c22d7fa8cfbfc448a6d148c59ea881b092db9dbe3547ae4b88e55f1b01f7c4a2ecc53b310c042793e63aa44cf6c257f2
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: 10c0/e951259d8cd2e0d196c72ec711add7115d42eb9a8146c8eeda5b8d3ac91e5dd816b9cd68920726d9fd4490368e7ed86e9c423f40db87e2d8dfafa00fa17c3a31
  languageName: node
  linkType: hard

"gulp-sort@npm:^2.0.0":
  version: 2.0.0
  resolution: "gulp-sort@npm:2.0.0"
  dependencies:
    through2: "npm:^2.0.1"
  checksum: 10c0/86aeb1d4222c0bec79a1cb4579a56ad18e507f708d13bba58e8497bd2341ffc9e0deed4d2f0ddc132b4d0a23f0e78e5673477ded6ea7e85c4974ef0447606e58
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.2":
  version: 1.1.0
  resolution: "has-bigints@npm:1.1.0"
  checksum: 10c0/2de0cdc4a1ccf7a1e75ffede1876994525ac03cc6f5ae7392d3415dd475cd9eee5bceec63669ab61aa997ff6cceebb50ef75561c7002bed8988de2b9d1b40788
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10c0/2e789c61b7888d66993e14e8331449e525ef42aac53c627cc53d1c3334e768bcb6abdc4f5f0de1478a25beec6f0bd62c7549058b7ac53e924040d4f301f02fd1
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0, has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: "npm:^1.0.0"
  checksum: 10c0/253c1f59e80bb476cf0dde8ff5284505d90c3bdb762983c3514d36414290475fe3fd6f574929d84de2a8eec00d35cf07cb6776205ff32efd7c50719125f00236
  languageName: node
  linkType: hard

"has-proto@npm:^1.2.0":
  version: 1.2.0
  resolution: "has-proto@npm:1.2.0"
  dependencies:
    dunder-proto: "npm:^1.0.0"
  checksum: 10c0/46538dddab297ec2f43923c3d35237df45d8c55a6fc1067031e04c13ed8a9a8f94954460632fd4da84c31a1721eefee16d901cbb1ae9602bab93bb6e08f93b95
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: 10c0/dde0a734b17ae51e84b10986e651c664379018d10b91b6b0e9b293eddb32f0f069688c841fb40f19e9611546130153e0a2a48fd7f512891fb000ddfa36f5a20e
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: "npm:^1.0.3"
  checksum: 10c0/a8b166462192bafe3d9b6e420a1d581d93dd867adb61be223a17a8d6dad147aa77a8be32c961bb2f27b3ef893cae8d36f564ab651f5e9b7938ae86f74027c48c
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10c0/3769d434703b8ac66b209a4cca0737519925bbdb61dd887f93a16372b14694c63ff4e797686d87c90f08168e81082248b9b028bad60d4da9e0d1148766f56eb9
  languageName: node
  linkType: hard

"heimdalljs-logger@npm:^0.1.10, heimdalljs-logger@npm:^0.1.7":
  version: 0.1.10
  resolution: "heimdalljs-logger@npm:0.1.10"
  dependencies:
    debug: "npm:^2.2.0"
    heimdalljs: "npm:^0.2.6"
  checksum: 10c0/f67da38fde8a399f826f1327c735133eea752e1626c9668694624c321eb715d97b39b244bf420a6dca595f78609f1bd604b33d6d39446a8c985dd02131a22645
  languageName: node
  linkType: hard

"heimdalljs@npm:^0.2.6":
  version: 0.2.6
  resolution: "heimdalljs@npm:0.2.6"
  dependencies:
    rsvp: "npm:~3.2.1"
  checksum: 10c0/20c9d9cce7983683a6423720387af0701de8c50660734899bf68a2d862535414e463ac69fd6423875ab3ace8f83ae4b490f18e047c5b3db8e5ab64da1b7aedc3
  languageName: node
  linkType: hard

"html-encoding-sniffer@npm:^4.0.0":
  version: 4.0.0
  resolution: "html-encoding-sniffer@npm:4.0.0"
  dependencies:
    whatwg-encoding: "npm:^3.1.1"
  checksum: 10c0/523398055dc61ac9b34718a719cb4aa691e4166f29187e211e1607de63dc25ac7af52ca7c9aead0c4b3c0415ffecb17326396e1202e2e86ff4bca4c0ee4c6140
  languageName: node
  linkType: hard

"html-escaper@npm:^2.0.0":
  version: 2.0.2
  resolution: "html-escaper@npm:2.0.2"
  checksum: 10c0/208e8a12de1a6569edbb14544f4567e6ce8ecc30b9394fcaa4e7bb1e60c12a7c9a1ed27e31290817157e8626f3a4f29e76c8747030822eb84a6abb15c255f0a0
  languageName: node
  linkType: hard

"html-parse-stringify@npm:^3.0.1":
  version: 3.0.1
  resolution: "html-parse-stringify@npm:3.0.1"
  dependencies:
    void-elements: "npm:3.1.0"
  checksum: 10c0/159292753d48b84d216d61121054ae5a33466b3db5b446e2ffc093ac077a411a99ce6cbe0d18e55b87cf25fa3c5a86c4d8b130b9719ec9b66623259000c72c15
  languageName: node
  linkType: hard

"html-to-image@npm:1.11.11":
  version: 1.11.11
  resolution: "html-to-image@npm:1.11.11"
  checksum: 10c0/0b6349221ad253dfca01d165c589d44341e942faf0273aab28c8b7d86ff2922d3e8e6390f57bf5ddaf6bac9a3b590a8cdaa77d52a363354796dd0e0e05eb35d2
  languageName: node
  linkType: hard

"html2canvas@npm:^1.0.0-rc.5":
  version: 1.4.1
  resolution: "html2canvas@npm:1.4.1"
  dependencies:
    css-line-break: "npm:^2.1.0"
    text-segmentation: "npm:^1.0.3"
  checksum: 10c0/6de86f75762b00948edf2ea559f16da0a1ec3facc4a8a7d3f35fcec59bb0c5970463478988ae3d9082152e0173690d46ebf4082e7ac803dd4817bae1d355c0db
  languageName: node
  linkType: hard

"htmlparser2@npm:^9.1.0":
  version: 9.1.0
  resolution: "htmlparser2@npm:9.1.0"
  dependencies:
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
    domutils: "npm:^3.1.0"
    entities: "npm:^4.5.0"
  checksum: 10c0/394f6323efc265bbc791d8c0d96bfe95984e0407565248521ab92e2dc7668e5ceeca7bc6ed18d408b9ee3b25032c5743368a4280d280332d782821d5d467ad8f
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 10c0/ce1319b8a382eb3cbb4a37c19f6bfe14e5bb5be3d09079e885e8c513ab2d3cd9214902f8a31c9dc4e37022633ceabfc2d697405deeaf1b8f3552bb4ed996fdfc
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0, http-proxy-agent@npm:^7.0.2":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10c0/4207b06a4580fb85dd6dff521f0abf6db517489e70863dca1a0291daa7f2d3d2d6015a57bd702af068ea5cf9f1f6ff72314f5f5b4228d299c0904135d2aef921
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1, https-proxy-agent@npm:^7.0.6":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10c0/f729219bc735edb621fa30e6e84e60ee5d00802b8247aac0d7b79b0bd6d4b3294737a337b93b86a0bd9e68099d031858a39260c976dc14cdbba238ba1f8779ac
  languageName: node
  linkType: hard

"i18next-browser-languagedetector@npm:^8.1.0":
  version: 8.2.0
  resolution: "i18next-browser-languagedetector@npm:8.2.0"
  dependencies:
    "@babel/runtime": "npm:^7.23.2"
  checksum: 10c0/4fcb6ec316e0fd4a10eee67a8d1e3d7e1407f14d5bed98978c50ed6f1853f5d559dc18ea7fd4b2de445ac0a4ed44df5b38f0b31b89b9ac883f99050d59ffec82
  languageName: node
  linkType: hard

"i18next-http-backend@npm:^3.0.2":
  version: 3.0.2
  resolution: "i18next-http-backend@npm:3.0.2"
  dependencies:
    cross-fetch: "npm:4.0.0"
  checksum: 10c0/2a08b018e4924fefae6ba4c09dfd1ca3aa892f2442779d77d2db52b086af7afe7fbb6f178743d7e02014ea12a711ad20fc03fb55c01bd80f1bc97ba2b3c6bc29
  languageName: node
  linkType: hard

"i18next-parser@npm:^9.3.0":
  version: 9.3.0
  resolution: "i18next-parser@npm:9.3.0"
  dependencies:
    "@babel/runtime": "npm:^7.25.0"
    broccoli-plugin: "npm:^4.0.7"
    cheerio: "npm:^1.0.0"
    colors: "npm:^1.4.0"
    commander: "npm:^12.1.0"
    eol: "npm:^0.9.1"
    esbuild: "npm:^0.25.0"
    fs-extra: "npm:^11.2.0"
    gulp-sort: "npm:^2.0.0"
    i18next: "npm:^23.5.1 || ^24.2.0"
    js-yaml: "npm:^4.1.0"
    lilconfig: "npm:^3.1.3"
    rsvp: "npm:^4.8.5"
    sort-keys: "npm:^5.0.0"
    typescript: "npm:^5.0.4"
    vinyl: "npm:^3.0.0"
    vinyl-fs: "npm:^4.0.0"
  bin:
    i18next: bin/cli.js
  checksum: 10c0/dd9de4d6812da662eaefafcaf6dae9c88d7e98c9907f784257056408bb22ac5ae23659bbfdf975452bfc35595914e280de0ef7c9f313cbd1e4cdb12dd0dadc1e
  languageName: node
  linkType: hard

"i18next-scanner@npm:4.6.0":
  version: 4.6.0
  resolution: "i18next-scanner@npm:4.6.0"
  dependencies:
    acorn: "npm:^8.0.4"
    acorn-jsx: "npm:^5.3.1"
    acorn-stage3: "npm:^4.0.0"
    acorn-walk: "npm:^8.0.0"
    chalk: "npm:^4.1.0"
    clone-deep: "npm:^4.0.0"
    commander: "npm:^9.0.0"
    deepmerge: "npm:^4.0.0"
    ensure-type: "npm:^1.5.0"
    eol: "npm:^0.9.1"
    esprima-next: "npm:^5.7.0"
    gulp-sort: "npm:^2.0.0"
    i18next: "npm:*"
    lodash: "npm:^4.0.0"
    parse5: "npm:^6.0.0"
    sortobject: "npm:^4.0.0"
    through2: "npm:^4.0.0"
    vinyl: "npm:^3.0.0"
    vinyl-fs: "npm:^4.0.0"
  bin:
    i18next-scanner: bin/cli.js
  checksum: 10c0/e04567f98ab04802a66be0ebf5edc375481e6503ed7f027064a6f968f1e87a97e23bf6db96e98150897c3acb37341dd3342bf73e4f28a1f88ceaed69b7170602
  languageName: node
  linkType: hard

"i18next@npm:*":
  version: 25.0.1
  resolution: "i18next@npm:25.0.1"
  dependencies:
    "@babel/runtime": "npm:^7.26.10"
  peerDependencies:
    typescript: ^5
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/2f380658472e6697c2133240c2bbffbacefdc49ef7224f00823816f7dcb7977047ad54bfb3dd49e2bd27cf66cc3fb77aa04141328b2efe83c58c0430fce0cdf7
  languageName: node
  linkType: hard

"i18next@npm:^23.5.1 || ^24.2.0":
  version: 24.2.3
  resolution: "i18next@npm:24.2.3"
  dependencies:
    "@babel/runtime": "npm:^7.26.10"
  peerDependencies:
    typescript: ^5
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/7ac11a67d618ec714beef303aa497c1249bf5f1977dd3ebe9ca2673dfa6cadbba9e2d39ec1337688903ae3866ce9c1bc22cd6b265e66cce54c5db3a9bbedd390
  languageName: node
  linkType: hard

"i18next@npm:^25.2.1":
  version: 25.2.1
  resolution: "i18next@npm:25.2.1"
  dependencies:
    "@babel/runtime": "npm:^7.27.1"
  peerDependencies:
    typescript: ^5
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/9e8817bdb930c15467ba31edfd834dcfc0a8979bb04f089b8249547a5c50ded820c1cce224563fc1dfe0a6f51e67e0e2d5231fb80a50fd7bd95320f120dfd0ae
  languageName: node
  linkType: hard

"iconv-lite@npm:0.6, iconv-lite@npm:0.6.3, iconv-lite@npm:^0.6.2, iconv-lite@npm:^0.6.3":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10c0/98102bc66b33fcf5ac044099d1257ba0b7ad5e3ccd3221f34dd508ab4070edff183276221684e1e0555b145fce0850c9f7d2b60a9fcac50fbb4ea0d6e845a3b1
  languageName: node
  linkType: hard

"ict-ui-carbon@workspace:.":
  version: 0.0.0-use.local
  resolution: "ict-ui-carbon@workspace:."
  dependencies:
    "@auth0/auth0-react": "npm:^2.3.0"
    "@bufbuild/protobuf": "npm:^2.5.1"
    "@carbon/charts-react": "npm:^1.23.10"
    "@carbon/icons-react": "npm:^11.60.0"
    "@carbon/react": "npm:^1.83.0"
    "@dagrejs/dagre": "npm:^1.1.4"
    "@eslint/js": "npm:9.33.0"
    "@tanstack/react-query": "npm:^5.79.0"
    "@tanstack/react-table": "npm:^8.21.3"
    "@testing-library/dom": "npm:10.4.1"
    "@testing-library/jest-dom": "npm:6.6.4"
    "@testing-library/react": "npm:16.3.0"
    "@testing-library/user-event": "npm:14.6.1"
    "@types/luxon": "npm:3.7.1"
    "@types/node": "npm:22.15.31"
    "@types/react": "npm:19.1.8"
    "@types/react-dom": "npm:19.1.6"
    "@types/react-grid-layout": "npm:1.3.5"
    "@uidotdev/usehooks": "npm:^2.4.1"
    "@vitejs/plugin-react": "npm:4.5.2"
    "@vitest/coverage-v8": "npm:3.2.4"
    "@vitest/ui": "npm:3.2.4"
    axios: "npm:^1.9.0"
    babel-plugin-react-compiler: "npm:19.0.0-beta-ebf51a3-20250411"
    echarts: "npm:^5.6.0"
    echarts-for-react: "npm:^3.0.2"
    eslint: "npm:9.33.0"
    eslint-config-prettier: "npm:10.1.8"
    eslint-plugin-react: "npm:7.37.5"
    eslint-plugin-unicorn: "npm:59.0.1"
    globals: "npm:16.2.0"
    i18next: "npm:^25.2.1"
    i18next-browser-languagedetector: "npm:^8.1.0"
    i18next-http-backend: "npm:^3.0.2"
    i18next-parser: "npm:^9.3.0"
    i18next-scanner: "npm:4.6.0"
    jsdom: "npm:26.1.0"
    jspdf: "npm:^3.0.1"
    luxon: "npm:^3.6.1"
    openapi-fetch: "npm:^0.14.0"
    openapi-react-query: "npm:^0.5.0"
    prettier: "npm:3.6.2"
    react: "npm:19.1.0"
    react-complex-tree: "npm:^2.6.0"
    react-dom: "npm:19.1.0"
    react-grid-layout: "npm:^1.5.1"
    react-i18next: "npm:^15.5.2"
    react-icons: "npm:^5.5.0"
    react-is: "npm:19.1.0"
    react-router: "npm:^7.6.1"
    reactflow: "npm:^11.11.4"
    sass: "npm:1.89.2"
    single-spa: "npm:^6.0.3"
    single-spa-react: "npm:^6.0.2"
    typescript: "npm:5.8.3"
    typescript-eslint: "npm:8.34.0"
    vite: "npm:6.3.5"
    vite-plugin-single-spa: "npm:1.0.0"
    vitest: "npm:3.2.0"
    xlsx: "npm:^0.18.5"
    zustand: "npm:^5.0.5"
  languageName: unknown
  linkType: soft

"ieee754@npm:^1.2.1":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 10c0/b0782ef5e0935b9f12883a2e2aa37baa75da6e66ce6515c168697b42160807d9330de9a32ec1ed73149aea02e0d822e572bca6f1e22bdcbd2149e13b050b17bb
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 10c0/f9f652c957983634ded1e7f02da3b559a0d4cc210fca3792cb67f1b153623c9c42efdc1c4121af171e295444459fc4a9201101fb041b1104a3c000bccb188337
  languageName: node
  linkType: hard

"ignore@npm:^7.0.0":
  version: 7.0.5
  resolution: "ignore@npm:7.0.5"
  checksum: 10c0/ae00db89fe873064a093b8999fe4cc284b13ef2a178636211842cceb650b9c3e390d3339191acb145d81ed5379d2074840cf0c33a20bdbd6f32821f79eb4ad5d
  languageName: node
  linkType: hard

"immutable@npm:^5.0.2":
  version: 5.0.3
  resolution: "immutable@npm:5.0.3"
  checksum: 10c0/3269827789e1026cd25c2ea97f0b2c19be852ffd49eda1b674b20178f73d84fa8d945ad6f5ac5bc4545c2b4170af9f6e1f77129bc1cae7974a4bf9b04a9cdfb9
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1":
  version: 3.3.1
  resolution: "import-fresh@npm:3.3.1"
  dependencies:
    parent-module: "npm:^1.0.0"
    resolve-from: "npm:^4.0.0"
  checksum: 10c0/bf8cc494872fef783249709385ae883b447e3eb09db0ebd15dcead7d9afe7224dad7bd7591c6b73b0b19b3c0f9640eb8ee884f01cfaf2887ab995b0b36a0cbec
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10c0/8b51313850dd33605c6c9d3fd9638b714f4c4c40250cff658209f30d40da60f78992fb2df5dabee4acf589a6a82bbc79ad5486550754bd9ec4e3fc0d4a57d6a6
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 10c0/1e1904ddb0cb3d6cce7cd09e27a90184908b7a5d5c21b92e232c93579d314f0b83c246ffb035493d0504b1e9147ba2c9b21df0030f48673fba0496ecd698161f
  languageName: node
  linkType: hard

"indent-string@npm:^5.0.0":
  version: 5.0.0
  resolution: "indent-string@npm:5.0.0"
  checksum: 10c0/8ee77b57d92e71745e133f6f444d6fa3ed503ad0e1bcd7e80c8da08b42375c07117128d670589725ed07b1978065803fa86318c309ba45415b7fe13e7f170220
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: "npm:^1.3.0"
    wrappy: "npm:1"
  checksum: 10c0/7faca22584600a9dc5b9fca2cd5feb7135ac8c935449837b315676b4c90aa4f391ec4f42240178244b5a34e8bede1948627fda392ca3191522fc46b34e985ab2
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:^2.0.3, inherits@npm:^2.0.4, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 10c0/4e531f648b29039fb7426fb94075e6545faa1eb9fe83c29f0b6d9e7263aceb4289d2d4557db0d428188eeb449cc7c5e77b0a0b2c4e248ff2a65933a0dee49ef2
  languageName: node
  linkType: hard

"internal-slot@npm:^1.1.0":
  version: 1.1.0
  resolution: "internal-slot@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    hasown: "npm:^2.0.2"
    side-channel: "npm:^1.1.0"
  checksum: 10c0/03966f5e259b009a9bf1a78d60da920df198af4318ec004f57b8aef1dd3fe377fbc8cce63a96e8c810010302654de89f9e19de1cd8ad0061d15be28a695465c7
  languageName: node
  linkType: hard

"internmap@npm:1 - 2":
  version: 2.0.3
  resolution: "internmap@npm:2.0.3"
  checksum: 10c0/8cedd57f07bbc22501516fbfc70447f0c6812871d471096fad9ea603516eacc2137b633633daf432c029712df0baefd793686388ddf5737e3ea15074b877f7ed
  languageName: node
  linkType: hard

"internmap@npm:^1.0.0":
  version: 1.0.1
  resolution: "internmap@npm:1.0.1"
  checksum: 10c0/60942be815ca19da643b6d4f23bd0bf4e8c97abbd080fb963fe67583b60bdfb3530448ad4486bae40810e92317bded9995cc31411218acc750d72cd4e8646eee
  languageName: node
  linkType: hard

"invariant@npm:^2.2.3":
  version: 2.2.4
  resolution: "invariant@npm:2.2.4"
  dependencies:
    loose-envify: "npm:^1.0.0"
  checksum: 10c0/5af133a917c0bcf65e84e7f23e779e7abc1cd49cb7fdc62d00d1de74b0d8c1b5ee74ac7766099fb3be1b05b26dfc67bab76a17030d2fe7ea2eef867434362dfc
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10c0/331cd07fafcb3b24100613e4b53e1a2b4feab11e671e655d46dc09ee233da5011284d09ca40c4ecbdfe1d0004f462958675c224a804259f2f78d2465a87824bc
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.4, is-array-buffer@npm:^3.0.5":
  version: 3.0.5
  resolution: "is-array-buffer@npm:3.0.5"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/c5c9f25606e86dbb12e756694afbbff64bc8b348d1bc989324c037e1068695131930199d6ad381952715dad3a9569333817f0b1a72ce5af7f883ce802e49c83d
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.3.1":
  version: 0.3.2
  resolution: "is-arrayish@npm:0.3.2"
  checksum: 10c0/f59b43dc1d129edb6f0e282595e56477f98c40278a2acdc8b0a5c57097c9eff8fe55470493df5775478cf32a4dc8eaf6d3a749f07ceee5bc263a78b2434f6a54
  languageName: node
  linkType: hard

"is-async-function@npm:^2.0.0":
  version: 2.1.1
  resolution: "is-async-function@npm:2.1.1"
  dependencies:
    async-function: "npm:^1.0.0"
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.1"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10c0/d70c236a5e82de6fc4d44368ffd0c2fee2b088b893511ce21e679da275a5ecc6015ff59a7d7e1bdd7ca39f71a8dbdd253cf8cce5c6b3c91cdd5b42b5ce677298
  languageName: node
  linkType: hard

"is-bigint@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-bigint@npm:1.1.0"
  dependencies:
    has-bigints: "npm:^1.0.2"
  checksum: 10c0/f4f4b905ceb195be90a6ea7f34323bf1c18e3793f18922e3e9a73c684c29eeeeff5175605c3a3a74cc38185fe27758f07efba3dbae812e5c5afbc0d2316b40e4
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.2.1":
  version: 1.2.2
  resolution: "is-boolean-object@npm:1.2.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/36ff6baf6bd18b3130186990026f5a95c709345c39cd368468e6c1b6ab52201e9fd26d8e1f4c066357b4938b0f0401e1a5000e08257787c1a02f3a719457001e
  languageName: node
  linkType: hard

"is-builtin-module@npm:^5.0.0":
  version: 5.0.0
  resolution: "is-builtin-module@npm:5.0.0"
  dependencies:
    builtin-modules: "npm:^5.0.0"
  checksum: 10c0/9561cdb92f7548df9403fa501f7d456bc90b9f49b547ce8935c5333b2316ea9ec3cbee3b972f2a98f041a9e2534a27465307fc45155a8ba793d9fdc9b7008aae
  languageName: node
  linkType: hard

"is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 10c0/ceebaeb9d92e8adee604076971dd6000d38d6afc40bb843ea8e45c5579b57671c3f3b50d7f04869618242c6cee08d1b67806a8cb8edaaaf7c0748b3720d6066f
  languageName: node
  linkType: hard

"is-core-module@npm:^2.13.0":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: "npm:^2.0.2"
  checksum: 10c0/898443c14780a577e807618aaae2b6f745c8538eca5c7bc11388a3f2dc6de82b9902bcc7eb74f07be672b11bbe82dd6a6edded44a00cb3d8f933d0459905eedd
  languageName: node
  linkType: hard

"is-data-view@npm:^1.0.1, is-data-view@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-data-view@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    is-typed-array: "npm:^1.1.13"
  checksum: 10c0/ef3548a99d7e7f1370ce21006baca6d40c73e9f15c941f89f0049c79714c873d03b02dae1c64b3f861f55163ecc16da06506c5b8a1d4f16650b3d9351c380153
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.5, is-date-object@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-date-object@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/1a4d199c8e9e9cac5128d32e6626fa7805175af9df015620ac0d5d45854ccf348ba494679d872d37301032e35a54fc7978fba1687e8721b2139aea7870cafa2f
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10c0/5487da35691fbc339700bbb2730430b07777a3c21b9ebaecb3072512dfd7b4ba78ac2381a87e8d78d20ea08affb3f1971b4af629173a6bf435ff8a4c47747912
  languageName: node
  linkType: hard

"is-finalizationregistry@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-finalizationregistry@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10c0/818dff679b64f19e228a8205a1e2d09989a98e98def3a817f889208cfcbf918d321b251aadf2c05918194803ebd2eb01b14fc9d0b2bea53d984f4137bfca5e97
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10c0/bb11d825e049f38e04c06373a8d72782eee0205bda9d908cc550ccb3c59b99d750ff9537982e01733c1c94a58e35400661f57042158ff5e8f3e90cf936daf0fc
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.10":
  version: 1.1.0
  resolution: "is-generator-function@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.0"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10c0/fdfa96c8087bf36fc4cd514b474ba2ff404219a4dd4cfa6cf5426404a1eed259bdcdb98f082a71029a48d01f27733e3436ecc6690129a7ec09cb0434bee03a2a
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10c0/17fb4014e22be3bbecea9b2e3a76e9e34ff645466be702f1693e8f1ee1adac84710d0be0bd9f967d6354036fd51ab7c2741d954d6e91dae6bb69714de92c197a
  languageName: node
  linkType: hard

"is-map@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-map@npm:2.0.3"
  checksum: 10c0/2c4d431b74e00fdda7162cd8e4b763d6f6f217edf97d4f8538b94b8702b150610e2c64961340015fe8df5b1fcee33ccd2e9b62619c4a8a3a155f8de6d6d355fc
  languageName: node
  linkType: hard

"is-negated-glob@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-negated-glob@npm:1.0.0"
  checksum: 10c0/f9d4fb2effd7a6d0e4770463e4cf708fbff2d5b660ab2043e5703e21e3234dfbe9974fdd8c08eb80f9898d5dd3d21b020e8d07fce387cd394a79991f01cd8d1c
  languageName: node
  linkType: hard

"is-number-object@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-number-object@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/97b451b41f25135ff021d85c436ff0100d84a039bb87ffd799cbcdbea81ef30c464ced38258cdd34f080be08fc3b076ca1f472086286d2aa43521d6ec6a79f53
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 10c0/b4686d0d3053146095ccd45346461bc8e53b80aeb7671cc52a4de02dbbf7dc0d1d2a986e2fe4ae206984b4d34ef37e8b795ebc4f4295c978373e6575e295d811
  languageName: node
  linkType: hard

"is-plain-obj@npm:^4.0.0":
  version: 4.1.0
  resolution: "is-plain-obj@npm:4.1.0"
  checksum: 10c0/32130d651d71d9564dc88ba7e6fda0e91a1010a3694648e9f4f47bb6080438140696d3e3e15c741411d712e47ac9edc1a8a9de1fe76f3487b0d90be06ac9975e
  languageName: node
  linkType: hard

"is-plain-object@npm:^2.0.4":
  version: 2.0.4
  resolution: "is-plain-object@npm:2.0.4"
  dependencies:
    isobject: "npm:^3.0.1"
  checksum: 10c0/f050fdd5203d9c81e8c4df1b3ff461c4bc64e8b5ca383bcdde46131361d0a678e80bcf00b5257646f6c636197629644d53bd8e2375aea633de09a82d57e942f4
  languageName: node
  linkType: hard

"is-potential-custom-element-name@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-potential-custom-element-name@npm:1.0.1"
  checksum: 10c0/b73e2f22bc863b0939941d369486d308b43d7aef1f9439705e3582bfccaa4516406865e32c968a35f97a99396dac84e2624e67b0a16b0a15086a785e16ce7db9
  languageName: node
  linkType: hard

"is-regex@npm:^1.2.1":
  version: 1.2.1
  resolution: "is-regex@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10c0/1d3715d2b7889932349241680032e85d0b492cfcb045acb75ffc2c3085e8d561184f1f7e84b6f8321935b4aea39bc9c6ba74ed595b57ce4881a51dfdbc214e04
  languageName: node
  linkType: hard

"is-set@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-set@npm:2.0.3"
  checksum: 10c0/f73732e13f099b2dc879c2a12341cfc22ccaca8dd504e6edae26484bd5707a35d503fba5b4daad530a9b088ced1ae6c9d8200fd92e09b428fe14ea79ce8080b7
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.4":
  version: 1.0.4
  resolution: "is-shared-array-buffer@npm:1.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10c0/65158c2feb41ff1edd6bbd6fd8403a69861cf273ff36077982b5d4d68e1d59278c71691216a4a64632bd76d4792d4d1d2553901b6666d84ade13bba5ea7bc7db
  languageName: node
  linkType: hard

"is-string@npm:^1.0.7, is-string@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-string@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/2f518b4e47886bb81567faba6ffd0d8a8333cf84336e2e78bf160693972e32ad00fe84b0926491cc598dee576fdc55642c92e62d0cbe96bf36f643b6f956f94d
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.4, is-symbol@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-symbol@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10c0/f08f3e255c12442e833f75a9e2b84b2d4882fdfd920513cf2a4a2324f0a5b076c8fd913778e3ea5d258d5183e9d92c0cd20e04b03ab3df05316b049b2670af1e
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.13, is-typed-array@npm:^1.1.14, is-typed-array@npm:^1.1.15":
  version: 1.1.15
  resolution: "is-typed-array@npm:1.1.15"
  dependencies:
    which-typed-array: "npm:^1.1.16"
  checksum: 10c0/415511da3669e36e002820584e264997ffe277ff136643a3126cc949197e6ca3334d0f12d084e83b1994af2e9c8141275c741cf2b7da5a2ff62dd0cac26f76c4
  languageName: node
  linkType: hard

"is-valid-glob@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-valid-glob@npm:1.0.0"
  checksum: 10c0/73aef3a2dc218b677362c876d1bc69699e10cfb50ecae6ac5fa946d7f5bb783721e81d9383bd120e4fb7bcfaa7ebe1edab0b707fd93051cc6e04f90f02d689b6
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-weakmap@npm:2.0.2"
  checksum: 10c0/443c35bb86d5e6cc5929cd9c75a4024bb0fff9586ed50b092f94e700b89c43a33b186b76dbc6d54f3d3d09ece689ab38dcdc1af6a482cbe79c0f2da0a17f1299
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2, is-weakref@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-weakref@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10c0/8e0a9c07b0c780949a100e2cab2b5560a48ecd4c61726923c1a9b77b6ab0aa0046c9e7fb2206042296817045376dee2c8ab1dabe08c7c3dfbf195b01275a085b
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.3":
  version: 2.0.4
  resolution: "is-weakset@npm:2.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/6491eba08acb8dc9532da23cb226b7d0192ede0b88f16199e592e4769db0a077119c1f5d2283d1e0d16d739115f70046e887e477eb0e66cd90e1bb29f28ba647
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: 10c0/4199f14a7a13da2177c66c31080008b7124331956f47bca57dd0b6ea9f11687aa25e565a2c7a2b519bc86988d10398e3049a1f5df13c9f6b7664154690ae79fd
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: 10c0/18b5be6669be53425f0b84098732670ed4e727e3af33bc7f948aac01782110eb9a18b3b329c5323bcdd3acdaae547ee077d3951317e7f133bff7105264b3003d
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10c0/228cfa503fadc2c31596ab06ed6aa82c9976eec2bfd83397e7eaf06d0ccf42cd1dfd6743bf9aeb01aebd4156d009994c5f76ea898d2832c1fe342da923ca457d
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10c0/9ec257654093443eb0a528a9c8cbba9c0ca7616ccb40abd6dde7202734d96bb86e4ac0d764f0f8cd965856aacbff2f4ce23e730dc19dfb41e3b0d865ca6fdcc7
  languageName: node
  linkType: hard

"isobject@npm:^3.0.1":
  version: 3.0.1
  resolution: "isobject@npm:3.0.1"
  checksum: 10c0/03344f5064a82f099a0cd1a8a407f4c0d20b7b8485e8e816c39f249e9416b06c322e8dec5b842b6bb8a06de0af9cb48e7bc1b5352f0fadc2f0abac033db3d4db
  languageName: node
  linkType: hard

"istanbul-lib-coverage@npm:^3.0.0, istanbul-lib-coverage@npm:^3.2.2":
  version: 3.2.2
  resolution: "istanbul-lib-coverage@npm:3.2.2"
  checksum: 10c0/6c7ff2106769e5f592ded1fb418f9f73b4411fd5a084387a5410538332b6567cd1763ff6b6cadca9b9eb2c443cce2f7ea7d7f1b8d315f9ce58539793b1e0922b
  languageName: node
  linkType: hard

"istanbul-lib-report@npm:^3.0.0, istanbul-lib-report@npm:^3.0.1":
  version: 3.0.1
  resolution: "istanbul-lib-report@npm:3.0.1"
  dependencies:
    istanbul-lib-coverage: "npm:^3.0.0"
    make-dir: "npm:^4.0.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/84323afb14392de8b6a5714bd7e9af845cfbd56cfe71ed276cda2f5f1201aea673c7111901227ee33e68e4364e288d73861eb2ed48f6679d1e69a43b6d9b3ba7
  languageName: node
  linkType: hard

"istanbul-lib-source-maps@npm:^5.0.6":
  version: 5.0.6
  resolution: "istanbul-lib-source-maps@npm:5.0.6"
  dependencies:
    "@jridgewell/trace-mapping": "npm:^0.3.23"
    debug: "npm:^4.1.1"
    istanbul-lib-coverage: "npm:^3.0.0"
  checksum: 10c0/ffe75d70b303a3621ee4671554f306e0831b16f39ab7f4ab52e54d356a5d33e534d97563e318f1333a6aae1d42f91ec49c76b6cd3f3fb378addcb5c81da0255f
  languageName: node
  linkType: hard

"istanbul-reports@npm:^3.1.7":
  version: 3.1.7
  resolution: "istanbul-reports@npm:3.1.7"
  dependencies:
    html-escaper: "npm:^2.0.0"
    istanbul-lib-report: "npm:^3.0.0"
  checksum: 10c0/a379fadf9cf8dc5dfe25568115721d4a7eb82fbd50b005a6672aff9c6989b20cc9312d7865814e0859cd8df58cbf664482e1d3604be0afde1f7fc3ccc1394a51
  languageName: node
  linkType: hard

"iterator.prototype@npm:^1.1.4":
  version: 1.1.5
  resolution: "iterator.prototype@npm:1.1.5"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.6"
    get-proto: "npm:^1.0.0"
    has-symbols: "npm:^1.1.0"
    set-function-name: "npm:^2.0.2"
  checksum: 10c0/f7a262808e1b41049ab55f1e9c29af7ec1025a000d243b83edf34ce2416eedd56079b117fa59376bb4a724110690f13aa8427f2ee29a09eec63a7e72367626d0
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10c0/6acc10d139eaefdbe04d2f679e6191b3abf073f111edf10b1de5302c97ec93fffeb2fdd8681ed17f16268aa9dd4f8c588ed9d1d3bffbbfa6e8bf897cbb3149b9
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 10c0/e248708d377aa058eacf2037b07ded847790e6de892bbad3dac0abba2e759cb9f121b00099a65195616badcb6eca8d14d975cb3e89eb1cfda644756402c8aeed
  languageName: node
  linkType: hard

"js-tokens@npm:^9.0.1":
  version: 9.0.1
  resolution: "js-tokens@npm:9.0.1"
  checksum: 10c0/68dcab8f233dde211a6b5fd98079783cbcd04b53617c1250e3553ee16ab3e6134f5e65478e41d82f6d351a052a63d71024553933808570f04dbf828d7921e80e
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/184a24b4eaacfce40ad9074c64fd42ac83cf74d8c8cd137718d456ced75051229e5061b8633c3366b8aada17945a7a356b337828c19da92b51ae62126575018f
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 10c0/4f907fb78d7b712e11dea8c165fe0921f81a657d3443dde75359ed52eb2b5d33ce6773d97985a089f09a65edd80b11cb75c767b57ba47391fee4c969f7215c96
  languageName: node
  linkType: hard

"jsdom@npm:26.1.0":
  version: 26.1.0
  resolution: "jsdom@npm:26.1.0"
  dependencies:
    cssstyle: "npm:^4.2.1"
    data-urls: "npm:^5.0.0"
    decimal.js: "npm:^10.5.0"
    html-encoding-sniffer: "npm:^4.0.0"
    http-proxy-agent: "npm:^7.0.2"
    https-proxy-agent: "npm:^7.0.6"
    is-potential-custom-element-name: "npm:^1.0.1"
    nwsapi: "npm:^2.2.16"
    parse5: "npm:^7.2.1"
    rrweb-cssom: "npm:^0.8.0"
    saxes: "npm:^6.0.0"
    symbol-tree: "npm:^3.2.4"
    tough-cookie: "npm:^5.1.1"
    w3c-xmlserializer: "npm:^5.0.0"
    webidl-conversions: "npm:^7.0.0"
    whatwg-encoding: "npm:^3.1.1"
    whatwg-mimetype: "npm:^4.0.0"
    whatwg-url: "npm:^14.1.1"
    ws: "npm:^8.18.0"
    xml-name-validator: "npm:^5.0.0"
  peerDependencies:
    canvas: ^3.0.0
  peerDependenciesMeta:
    canvas:
      optional: true
  checksum: 10c0/5b14a5bc32ce077a06fb42d1ab95b1191afa5cbbce8859e3b96831c5143becbbcbf0511d4d4934e922d2901443ced2cdc3b734c1cf30b5f73b3e067ce457d0f4
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2, jsesc@npm:^3.1.0":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 10c0/531779df5ec94f47e462da26b4cbf05eb88a83d9f08aac2ba04206508fc598527a153d08bd462bae82fc78b3eaa1a908e1a4a79f886e9238641c4cdefaf118b1
  languageName: node
  linkType: hard

"jsesc@npm:~3.0.2":
  version: 3.0.2
  resolution: "jsesc@npm:3.0.2"
  bin:
    jsesc: bin/jsesc
  checksum: 10c0/ef22148f9e793180b14d8a145ee6f9f60f301abf443288117b4b6c53d0ecd58354898dc506ccbb553a5f7827965cd38bc5fb726575aae93c5e8915e2de8290e1
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 10c0/0d1c91569d9588e7eef2b49b59851f297f3ab93c7b35c7c221e288099322be6b562767d11e4821da500f3219542b9afd2e54c5dc573107c1126ed1080f8e96d7
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 10c0/108fa90d4cc6f08243aedc6da16c408daf81793bf903e9fd5ab21983cda433d5d2da49e40711da016289465ec2e62e0324dcdfbc06275a607fe3233fde4942ce
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: 10c0/cb168b61fd4de83e58d09aaa6425ef71001bae30d260e2c57e7d09a5fd82223e2f22a042dedaab8db23b7d9ae46854b08bb1f91675a8be11c5cffebef5fb66a5
  languageName: node
  linkType: hard

"json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 10c0/5a04eed94810fa55c5ea138b2f7a5c12b97c3750bc63d11e511dcecbfef758003861522a070c2272764ee0f4e3e323862f386945aeb5b85b87ee43f084ba586c
  languageName: node
  linkType: hard

"jsonfile@npm:^4.0.0":
  version: 4.0.0
  resolution: "jsonfile@npm:4.0.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 10c0/7dc94b628d57a66b71fb1b79510d460d662eb975b5f876d723f81549c2e9cd316d58a2ddf742b2b93a4fa6b17b2accaf1a738a0e2ea114bdfb13a32e5377e480
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
    universalify: "npm:^2.0.0"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 10c0/4f95b5e8a5622b1e9e8f33c96b7ef3158122f595998114d1e7f03985649ea99cb3cd99ce1ed1831ae94c8c8543ab45ebd044207612f31a56fd08462140e46865
  languageName: node
  linkType: hard

"jspdf@npm:^3.0.1":
  version: 3.0.1
  resolution: "jspdf@npm:3.0.1"
  dependencies:
    "@babel/runtime": "npm:^7.26.7"
    atob: "npm:^2.1.2"
    btoa: "npm:^1.2.1"
    canvg: "npm:^3.0.11"
    core-js: "npm:^3.6.0"
    dompurify: "npm:^3.2.4"
    fflate: "npm:^0.8.1"
    html2canvas: "npm:^1.0.0-rc.5"
  dependenciesMeta:
    canvg:
      optional: true
    core-js:
      optional: true
    dompurify:
      optional: true
    html2canvas:
      optional: true
  checksum: 10c0/db900f44d9cdf2c51bd5a7ffe9c55711aff68e7751a61291cc2451f06cfa1e268ecd5947c37e0f9097c999d751e3cdd1560f5a4a242376530100654c70970eda
  languageName: node
  linkType: hard

"jsx-ast-utils@npm:^2.4.1 || ^3.0.0":
  version: 3.3.5
  resolution: "jsx-ast-utils@npm:3.3.5"
  dependencies:
    array-includes: "npm:^3.1.6"
    array.prototype.flat: "npm:^1.3.1"
    object.assign: "npm:^4.1.4"
    object.values: "npm:^1.1.6"
  checksum: 10c0/a32679e9cb55469cb6d8bbc863f7d631b2c98b7fc7bf172629261751a6e7bc8da6ae374ddb74d5fbd8b06cf0eb4572287b259813d92b36e384024ed35e4c13e1
  languageName: node
  linkType: hard

"keyv@npm:^4.5.4":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: "npm:3.0.1"
  checksum: 10c0/aa52f3c5e18e16bb6324876bb8b59dd02acf782a4b789c7b2ae21107fab95fab3890ed448d4f8dba80ce05391eeac4bfabb4f02a20221342982f806fa2cf271e
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.2":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 10c0/61cdff9623dabf3568b6445e93e31376bee1cdb93f8ba7033d86022c2a9b1791a1d9510e026e6465ebd701a6dd2f7b0808483ad8838341ac52f003f512e0b4c4
  languageName: node
  linkType: hard

"lead@npm:^4.0.0":
  version: 4.0.0
  resolution: "lead@npm:4.0.0"
  checksum: 10c0/71d2509b3c921dc74c47561a3c7bf0b76ecb530af178c3e0f469f3bdf20940ca08bcb4f18bbcfde0619706c1e550d3ba67ea187407722304db8fd3bc13a4405d
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:~0.4.0"
  checksum: 10c0/effb03cad7c89dfa5bd4f6989364bfc79994c2042ec5966cb9b95990e2edee5cd8969ddf42616a0373ac49fac1403437deaf6e9050fbbaa3546093a59b9ac94e
  languageName: node
  linkType: hard

"lilconfig@npm:^3.1.3":
  version: 3.1.3
  resolution: "lilconfig@npm:3.1.3"
  checksum: 10c0/f5604e7240c5c275743561442fbc5abf2a84ad94da0f5adc71d25e31fa8483048de3dcedcb7a44112a942fed305fd75841cdf6c9681c7f640c63f1049e9a5dcc
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: "npm:^5.0.0"
  checksum: 10c0/d3972ab70dfe58ce620e64265f90162d247e87159b6126b01314dd67be43d50e96a50b517bce2d9452a79409c7614054c277b5232377de50416564a77ac7aad3
  languageName: node
  linkType: hard

"lodash-es@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash-es@npm:4.17.21"
  checksum: 10c0/fb407355f7e6cd523a9383e76e6b455321f0f153a6c9625e21a8827d10c54c2a2341bd2ae8d034358b60e07325e1330c14c224ff582d04612a46a4f0479ff2f2
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: 10c0/402fa16a1edd7538de5b5903a90228aa48eb5533986ba7fa26606a49db2572bf414ff73a2c9f5d5fd36b31c46a5d5c7e1527749c07cbcf965ccff5fbdf32c506
  languageName: node
  linkType: hard

"lodash@npm:^4.0.0, lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: 10c0/d8cbea072bb08655bb4c989da418994b073a608dffa608b09ac04b43a791b12aeae7cd7ad919aa4c925f33b48490b5cfe6c1f71d827956071dae2e7bb3a6b74c
  languageName: node
  linkType: hard

"loose-envify@npm:^1.0.0, loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: "npm:^3.0.0 || ^4.0.0"
  bin:
    loose-envify: cli.js
  checksum: 10c0/655d110220983c1a4b9c0c679a2e8016d4b67f6e9c7b5435ff5979ecdb20d0813f4dec0a08674fcbdd4846a3f07edbb50a36811fd37930b94aaa0d9daceb017e
  languageName: node
  linkType: hard

"loupe@npm:^3.1.0, loupe@npm:^3.1.3":
  version: 3.1.3
  resolution: "loupe@npm:3.1.3"
  checksum: 10c0/f5dab4144254677de83a35285be1b8aba58b3861439ce4ba65875d0d5f3445a4a496daef63100ccf02b2dbc25bf58c6db84c9cb0b96d6435331e9d0a33b48541
  languageName: node
  linkType: hard

"loupe@npm:^3.1.4":
  version: 3.2.0
  resolution: "loupe@npm:3.2.0"
  checksum: 10c0/f572fd9e38db8d36ae9eede305480686e310d69bc40394b6842838ebc6c3860a0e35ab30182f33606ab2d8a685d9ff6436649269f8218a1c3385ca329973cb2c
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0, lru-cache@npm:^10.4.3":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10c0/ebd04fbca961e6c1d6c0af3799adcc966a1babe798f685bb84e6599266599cd95d94630b10262f5424539bc4640107e8a33aa28585374abf561d30d16f4b39fb
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: "npm:^3.0.2"
  checksum: 10c0/89b2ef2ef45f543011e38737b8a8622a2f8998cddf0e5437174ef8f1f70a8b9d14a918ab3e232cb3ba343b7abddffa667f0b59075b2b80e6b4d63c3de6127482
  languageName: node
  linkType: hard

"luxon@npm:^3.6.1":
  version: 3.7.1
  resolution: "luxon@npm:3.7.1"
  checksum: 10c0/f83bc23a4c09da9111bc2510d2f5346e1ced4938379ebff13e308fece2ea852eb6e8b9ed8053b8d82e0fce05d5dd46e4cd64d8831ca04cfe32c0954b6f087258
  languageName: node
  linkType: hard

"lz-string@npm:^1.5.0":
  version: 1.5.0
  resolution: "lz-string@npm:1.5.0"
  bin:
    lz-string: bin/bin.js
  checksum: 10c0/36128e4de34791838abe979b19927c26e67201ca5acf00880377af7d765b38d1c60847e01c5ec61b1a260c48029084ab3893a3925fd6e48a04011364b089991b
  languageName: node
  linkType: hard

"magic-string@npm:^0.30.17":
  version: 0.30.17
  resolution: "magic-string@npm:0.30.17"
  dependencies:
    "@jridgewell/sourcemap-codec": "npm:^1.5.0"
  checksum: 10c0/16826e415d04b88378f200fe022b53e638e3838b9e496edda6c0e086d7753a44a6ed187adc72d19f3623810589bf139af1a315541cd6a26ae0771a0193eaf7b8
  languageName: node
  linkType: hard

"magicast@npm:^0.3.5":
  version: 0.3.5
  resolution: "magicast@npm:0.3.5"
  dependencies:
    "@babel/parser": "npm:^7.25.4"
    "@babel/types": "npm:^7.25.4"
    source-map-js: "npm:^1.2.0"
  checksum: 10c0/a6cacc0a848af84f03e3f5bda7b0de75e4d0aa9ddce5517fd23ed0f31b5ddd51b2d0ff0b7e09b51f7de0f4053c7a1107117edda6b0732dca3e9e39e6c5a68c64
  languageName: node
  linkType: hard

"make-dir@npm:^4.0.0":
  version: 4.0.0
  resolution: "make-dir@npm:4.0.0"
  dependencies:
    semver: "npm:^7.5.3"
  checksum: 10c0/69b98a6c0b8e5c4fe9acb61608a9fbcfca1756d910f51e5dbe7a9e5cfb74fca9b8a0c8a0ffdf1294a740826c1ab4871d5bf3f62f72a3049e5eac6541ddffed68
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10c0/c40efb5e5296e7feb8e37155bde8eb70bc57d731b1f7d90e35a092fde403d7697c56fb49334d92d330d6f1ca29a98142036d6480a12681133a0a1453164cb2f0
  languageName: node
  linkType: hard

"matcher-collection@npm:^2.0.0":
  version: 2.0.1
  resolution: "matcher-collection@npm:2.0.1"
  dependencies:
    "@types/minimatch": "npm:^3.0.3"
    minimatch: "npm:^3.0.2"
  checksum: 10c0/409aad220000e2041672f900883ec66ffdd04814b133b428a8d35e055495fc09bb9024ca6ad7a63ebe6ed9e480e01db02c3edf3587ae1ba2627727a3d896ff96
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 10c0/7579ff94e899e2f76ab64491d76cf606274c874d8f2af4a442c016bd85688927fcfca157ba6bf74b08e9439dc010b248ce05b96cc7c126a354c3bae7fcb48b7f
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 10c0/254a8a4605b58f450308fc474c82ac9a094848081bf4c06778200207820e5193726dc563a0d2c16468810516a5c97d9d3ea0ca6585d23c58ccfff2403e8dbbeb
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.5, micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: "npm:^3.0.3"
    picomatch: "npm:^2.3.1"
  checksum: 10c0/166fa6eb926b9553f32ef81f5f531d27b4ce7da60e5baf8c021d043b27a388fb95e46a8038d5045877881e673f8134122b59624d5cecbd16eb50a42e7a6b5ca8
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10c0/0557a01deebf45ac5f5777fe7740b2a5c309c6d62d40ceab4e23da9f821899ce7a900b7ac8157d4548ddbb7beffe9abc621250e6d182b0397ec7f10c7b91a5aa
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10c0/82fb07ec56d8ff1fc999a84f2f217aa46cb6ed1033fefaabd5785b9a974ed225c90dc72fff460259e66b95b73648596dbcc50d51ed69cdf464af2d237d3149b2
  languageName: node
  linkType: hard

"min-indent@npm:^1.0.0, min-indent@npm:^1.0.1":
  version: 1.0.1
  resolution: "min-indent@npm:1.0.1"
  checksum: 10c0/7e207bd5c20401b292de291f02913230cb1163abca162044f7db1d951fa245b174dc00869d40dd9a9f32a885ad6a5f3e767ee104cf278f399cb4e92d3f582d5c
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.2, minimatch@npm:^3.0.4, minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10c0/0262810a8fc2e72cca45d6fd86bd349eee435eb95ac6aa45c9ea2180e7ee875ef44c32b55b5973ceabe95ea12682f6e3725cbb63d7a2d1da3ae1163c8b210311
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/de96cf5e35bdf0eab3e2c853522f98ffbe9a36c37797778d2665231ec1f20a9447a7e567cb640901f89e4daaa95ae5d70c65a9e8aa2bb0019b6facbc3c0575ed
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/5167e73f62bb74cc5019594709c77e6a742051a647fe9499abf03c71dca75515b7959d67a764bdc4f8b361cf897fbf25e2d9869ee039203ed45240f48b9aa06e
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/a3147b2efe8e078c9bf9d024a0059339c5a09c5b1dded6900a219c218cc8b1b78510b62dae556b507304af226b18c3f1aeb1d48660283602d5b6586c399eed5c
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/2a51b63feb799d2bb34669205eee7c0eaf9dce01883261a5b77410c9408aa447e478efd191b4de6fc1101e796ff5892f8443ef20d9544385819093dbb32d36bd
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/cbda57cea20b140b797505dc2cac71581a70b3247b84480c1fed5ca5ba46c25ecc25f68bfc9e6dcb1a6e9017dab5c7ada5eab73ad4f0a49d84e35093e0c643f2
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/298f124753efdc745cfe0f2bdfdd81ba25b9f4e753ca4a2066eb17c821f25d48acea607dfc997633ee5bf7b6dfffb4eee4f2051eb168663f0b99fad2fa4829cb
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/a114746943afa1dbbca8249e706d1d38b85ed1298b530f5808ce51f8e9e941962e2a5ad2e00eae7dd21d8a4aae6586a66d4216d1a259385e9d0358f0c1eba16c
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10c0/b0fd20bb9fb56e5fa9a8bfac539e8915ae07430a619e4b86ff71f5fc757ef3924b23b2c4230393af1eda647ed3d75739e4e0acb250a6b1eb277cf7f8fe449557
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.1
  resolution: "minizlib@npm:3.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
    rimraf: "npm:^5.0.5"
  checksum: 10c0/82f8bf70da8af656909a8ee299d7ed3b3372636749d29e105f97f20e88971be31f5ed7642f2e898f00283b68b701cc01307401cdc209b0efc5dd3818220e5093
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10c0/9f2b975e9246351f5e3a40dcfac99fcd0baa31fbfab615fe059fb11e51f10e4803c63de1f384c54d656e4db31d000e4767e9ef076a22e12a641357602e31d57d
  languageName: node
  linkType: hard

"mktemp@npm:~0.4.0":
  version: 0.4.0
  resolution: "mktemp@npm:0.4.0"
  checksum: 10c0/c3777c23492673668a82838f41485531b7b3c9a4430e9daef469371092f9a8d4df84cab05af07c8ab08a407466baf348566c5fd9da702d6f9d917526a6c02809
  languageName: node
  linkType: hard

"mrmime@npm:^2.0.0":
  version: 2.0.1
  resolution: "mrmime@npm:2.0.1"
  checksum: 10c0/af05afd95af202fdd620422f976ad67dc18e6ee29beb03dd1ce950ea6ef664de378e44197246df4c7cdd73d47f2e7143a6e26e473084b9e4aa2095c0ad1e1761
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 10c0/f8fda810b39fd7255bbdc451c46286e549794fcc700dc9cd1d25658bbc4dc2563a5de6fe7c60f798a16a60c6ceb53f033cb353f493f0cf63e5199b702943159d
  languageName: node
  linkType: hard

"ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10c0/d924b57e7312b3b63ad21fc5b3dc0af5e78d61a1fc7cfb5457edaf26326bf62be5307cc87ffb6862ef1c2b33b0233cdb5d4f01c4c958cc0d660948b65a287a48
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.11, nanoid@npm:^3.3.8":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10c0/40e7f70b3d15f725ca072dfc4f74e81fcf1fbb02e491cf58ac0c79093adc9b0a73b152bcde57df4b79cd097e13023d7504acb38404a4da7bc1cd8e887b82fe0b
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 10c0/f5f9a7974bfb28a91afafa254b197f0f22c684d4a1731763dda960d2c8e375b36c7d690e0d9dc8fba774c537af14a7e979129bca23d88d052fbeb9466955e447
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10c0/4c559dd52669ea48e1914f9d634227c561221dd54734070791f999c52ed0ff36e437b2e07d5c1f6e32909fc625fe46491c16e4a8f0572567d4dd15c3a4fda04b
  languageName: node
  linkType: hard

"node-addon-api@npm:^7.0.0":
  version: 7.1.1
  resolution: "node-addon-api@npm:7.1.1"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/fb32a206276d608037fa1bcd7e9921e177fe992fc610d098aa3128baca3c0050fc1e014fa007e9b3874cf865ddb4f5bd9f43ccb7cbbbe4efaff6a83e920b17e9
  languageName: node
  linkType: hard

"node-fetch@npm:^2.6.12":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: "npm:^5.0.0"
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/b55786b6028208e6fbe594ccccc213cab67a72899c9234eb59dba51062a299ea853210fcf526998eaa2867b0963ad72338824450905679ff0fa304b8c5093ae8
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.1.0
  resolution: "node-gyp@npm:11.1.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    glob: "npm:^10.3.10"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10c0/c38977ce502f1ea41ba2b8721bd5b49bc3d5b3f813eabfac8414082faf0620ccb5211e15c4daecc23ed9f5e3e9cc4da00e575a0bcfc2a95a069294f2afa1e0cd
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 10c0/52a0dbd25ccf545892670d1551690fe0facb6a471e15f2cfa1b20142a5b255b3aa254af5f59d6ecb69c2bec7390bc643c43aa63b13bf5e64b6075952e716b1aa
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: "npm:^3.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10c0/62e9ea70c7a3eb91d162d2c706b6606c041e4e7b547cbbb48f8b3695af457dd6479904d7ace600856bf923dd8d1ed0696f06195c8c20f02ac87c1da0e1d315ef
  languageName: node
  linkType: hard

"normalize-path@npm:3.0.0, normalize-path@npm:^3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 10c0/e008c8142bcc335b5e38cf0d63cfd39d6cf2d97480af9abdbe9a439221fd4d749763bab492a8ee708ce7a194bb00c9da6d0a115018672310850489137b3da046
  languageName: node
  linkType: hard

"now-and-later@npm:^3.0.0":
  version: 3.0.0
  resolution: "now-and-later@npm:3.0.0"
  dependencies:
    once: "npm:^1.4.0"
  checksum: 10c0/9ed96bae9f4bf66c01704a59aa5b6a8aa26bd65445133a08a2b867470c1705ae746f7261e4676b2ae6fc9dce0dc778055b816218bdeb1efbf610e0c95a83711b
  languageName: node
  linkType: hard

"nth-check@npm:^2.0.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: "npm:^1.0.0"
  checksum: 10c0/5fee7ff309727763689cfad844d979aedd2204a817fbaaf0e1603794a7c20db28548d7b024692f953557df6ce4a0ee4ae46cd8ebd9b36cfb300b9226b567c479
  languageName: node
  linkType: hard

"nwsapi@npm:^2.2.16":
  version: 2.2.20
  resolution: "nwsapi@npm:2.2.20"
  checksum: 10c0/07f4dafa3186aef7c007863e90acd4342a34ba9d44b22f14f644fdb311f6086887e21c2fc15efaa826c2bc39ab2bc841364a1a630e7c87e0cb723ba59d729297
  languageName: node
  linkType: hard

"object-assign@npm:^4.1.0, object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: 10c0/1f4df9945120325d041ccf7b86f31e8bcc14e73d29171e37a7903050e96b81323784ec59f93f102ec635bcf6fa8034ba3ea0a8c7e69fa202b87ae3b6cec5a414
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 10c0/d7f8711e803b96ea3191c745d6f8056ce1f2496e530e6a19a0e92d89b0fa3c76d910c31f0aa270432db6bd3b2f85500a376a83aaba849a8d518c8845b3211692
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: 10c0/b11f7ccdbc6d406d1f186cdadb9d54738e347b2692a14439ca5ac70c225fa6db46db809711b78589866d47b25fc3e8dee0b4c722ac751e11180f9380e3d8601d
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.4, object.assign@npm:^4.1.7":
  version: 4.1.7
  resolution: "object.assign@npm:4.1.7"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
    has-symbols: "npm:^1.1.0"
    object-keys: "npm:^1.1.1"
  checksum: 10c0/3b2732bd860567ea2579d1567525168de925a8d852638612846bd8082b3a1602b7b89b67b09913cbb5b9bd6e95923b2ae73580baa9d99cb4e990564e8cbf5ddc
  languageName: node
  linkType: hard

"object.entries@npm:^1.1.9":
  version: 1.1.9
  resolution: "object.entries@npm:1.1.9"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.1.1"
  checksum: 10c0/d4b8c1e586650407da03370845f029aa14076caca4e4d4afadbc69cfb5b78035fd3ee7be417141abdb0258fa142e59b11923b4c44d8b1255b28f5ffcc50da7db
  languageName: node
  linkType: hard

"object.fromentries@npm:^2.0.8":
  version: 2.0.8
  resolution: "object.fromentries@npm:2.0.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/cd4327e6c3369cfa805deb4cbbe919bfb7d3aeebf0bcaba291bb568ea7169f8f8cdbcabe2f00b40db0c20cd20f08e11b5f3a5a36fb7dd3fe04850c50db3bf83b
  languageName: node
  linkType: hard

"object.values@npm:^1.1.6, object.values@npm:^1.2.1":
  version: 1.2.1
  resolution: "object.values@npm:1.2.1"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/3c47814fdc64842ae3d5a74bc9d06bdd8d21563c04d9939bf6716a9c00596a4ebc342552f8934013d1ec991c74e3671b26710a0c51815f0b603795605ab6b2c9
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: "npm:1"
  checksum: 10c0/5d48aca287dfefabd756621c5dfce5c91a549a93e9fdb7b8246bc4c4790aa2ec17b34a260530474635147aeb631a2dcc8b32c613df0675f96041cbb8244517d0
  languageName: node
  linkType: hard

"openapi-fetch@npm:^0.14.0":
  version: 0.14.0
  resolution: "openapi-fetch@npm:0.14.0"
  dependencies:
    openapi-typescript-helpers: "npm:^0.0.15"
  checksum: 10c0/d23155edde61837a25896a2b201c890649ec8d16b37c3920dee940290ef6a66bd1b6f1df5ac68ec203f32d485ce16f7cf3557c13c183299e2e321d0a2eb62691
  languageName: node
  linkType: hard

"openapi-react-query@npm:^0.5.0":
  version: 0.5.0
  resolution: "openapi-react-query@npm:0.5.0"
  dependencies:
    openapi-typescript-helpers: "npm:^0.0.15"
  peerDependencies:
    "@tanstack/react-query": ^5.25.0
    openapi-fetch: ^0.14.0
  checksum: 10c0/74d9e90813ffd648206d5479fa3caa6824bb598fa03d60b8e77a5bb63feb85257b9dbb23ccf2f7dd0e56936283aec43a73ccc7b6a2b7db9aa01669ae779a6f7c
  languageName: node
  linkType: hard

"openapi-typescript-helpers@npm:^0.0.15":
  version: 0.0.15
  resolution: "openapi-typescript-helpers@npm:0.0.15"
  checksum: 10c0/5eb68d487b787e3e31266470b1a310726549dd45a1079655ab18066ab291b0b3c343fdf629991013706a2329b86964f8798d56ef0272b94b931fe6c19abd7a88
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: "npm:^0.1.3"
    fast-levenshtein: "npm:^2.0.6"
    levn: "npm:^0.4.1"
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:^0.4.0"
    word-wrap: "npm:^1.2.5"
  checksum: 10c0/4afb687a059ee65b61df74dfe87d8d6815cd6883cb8b3d5883a910df72d0f5d029821f37025e4bccf4048873dbdb09acc6d303d27b8f76b1a80dd5a7d5334675
  languageName: node
  linkType: hard

"own-keys@npm:^1.0.1":
  version: 1.0.1
  resolution: "own-keys@npm:1.0.1"
  dependencies:
    get-intrinsic: "npm:^1.2.6"
    object-keys: "npm:^1.1.1"
    safe-push-apply: "npm:^1.0.0"
  checksum: 10c0/6dfeb3455bff92ec3f16a982d4e3e65676345f6902d9f5ded1d8265a6318d0200ce461956d6d1c70053c7fe9f9fe65e552faac03f8140d37ef0fdd108e67013a
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: "npm:^0.1.0"
  checksum: 10c0/9db675949dbdc9c3763c89e748d0ef8bdad0afbb24d49ceaf4c46c02c77d30db4e0652ed36d0a0a7a95154335fab810d95c86153105bb73b3a90448e2bb14e1a
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: "npm:^3.0.2"
  checksum: 10c0/2290d627ab7903b8b70d11d384fee714b797f6040d9278932754a6860845c4d3190603a0772a663c8cb5a7b21d1b16acb3a6487ebcafa9773094edc3dfe6009a
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10c0/46091610da2b38ce47bcd1d8b4835a6fa4e832848a6682cf1652bc93915770f4617afc844c10a77d1b3e56d2472bb2d5622353fa3ead01a7f42b04fc8e744a5c
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10c0/62ba2785eb655fec084a257af34dbe24292ab74516d6aecef97ef72d4897310bc6898f6c85b5cd22770eaa1ce60d55a0230e150fb6a966e3ecd6c511e23d164b
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: "npm:^3.0.0"
  checksum: 10c0/c63d6e80000d4babd11978e0d3fee386ca7752a02b035fd2435960ffaa7219dc42146f07069fb65e6e8bf1caef89daf9af7535a39bddf354d78bf50d8294f556
  languageName: node
  linkType: hard

"parse5-htmlparser2-tree-adapter@npm:^7.0.0":
  version: 7.1.0
  resolution: "parse5-htmlparser2-tree-adapter@npm:7.1.0"
  dependencies:
    domhandler: "npm:^5.0.3"
    parse5: "npm:^7.0.0"
  checksum: 10c0/e5a4e0b834c84c9e244b5749f8d007f4baaeafac7a1da2c54be3421ffd9ef8fdec4f198bf55cda22e88e6ba95e9943f6ed5aa3ae5900b39972ebf5dc8c3f4722
  languageName: node
  linkType: hard

"parse5-parser-stream@npm:^7.1.2":
  version: 7.1.2
  resolution: "parse5-parser-stream@npm:7.1.2"
  dependencies:
    parse5: "npm:^7.0.0"
  checksum: 10c0/e236c61000d38ecad369e725a48506b051cebad8abb00e6d4e8bff7aa85c183820fcb45db1559cc90955bdbbdbd665ea94c41259594e74566fff411478dc7fcb
  languageName: node
  linkType: hard

"parse5@npm:^6.0.0":
  version: 6.0.1
  resolution: "parse5@npm:6.0.1"
  checksum: 10c0/595821edc094ecbcfb9ddcb46a3e1fe3a718540f8320eff08b8cf6742a5114cce2d46d45f95c26191c11b184dcaf4e2960abcd9c5ed9eb9393ac9a37efcfdecb
  languageName: node
  linkType: hard

"parse5@npm:^7.0.0, parse5@npm:^7.1.2":
  version: 7.2.1
  resolution: "parse5@npm:7.2.1"
  dependencies:
    entities: "npm:^4.5.0"
  checksum: 10c0/829d37a0c709215a887e410a7118d754f8e1afd7edb529db95bc7bbf8045fb0266a7b67801331d8e8d9d073ea75793624ec27ce9ff3b96862c3b9008f4d68e80
  languageName: node
  linkType: hard

"parse5@npm:^7.2.1":
  version: 7.3.0
  resolution: "parse5@npm:7.3.0"
  dependencies:
    entities: "npm:^6.0.0"
  checksum: 10c0/7fd2e4e247e85241d6f2a464d0085eed599a26d7b0a5233790c49f53473232eb85350e8133344d9b3fd58b89339e7ad7270fe1f89d28abe50674ec97b87f80b5
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 10c0/8c0bd3f5238188197dc78dced15207a4716c51cc4e3624c44fc97acf69558f5ebb9a2afff486fe1b4ee148e0c133e96c5e11a9aa5c48a3006e3467da070e5e1b
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 10c0/127da03c82172a2a50099cddbf02510c1791fc2cc5f7713ddb613a56838db1e8168b121a920079d052e0936c23005562059756d653b7c544c53185efe53be078
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10c0/748c43efd5a569c039d7a00a03b58eecd1d75f3999f5a28303d75f521288df4823bc057d8784eb72358b2895a05f29a070bc9f1f17d28226cc4e62494cc58c4c
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 10c0/11ce261f9d294cc7a58d6a574b7f1b935842355ec66fba3c3fd79e0f036462eaf07d0aa95bb74ff432f9afef97ce1926c720988c6a7451d8a584930ae7de86e1
  languageName: node
  linkType: hard

"path-posix@npm:^1.0.0":
  version: 1.0.0
  resolution: "path-posix@npm:1.0.0"
  checksum: 10c0/00fbadb9b60fb513f316f92e0b5535e55d832f4f20067586d151f6d7bed57178dec31b1a0f514694500a9a1f2b69798c066a3cdcf0b0289cfee63e39845bfd02
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10c0/32a13711a2a505616ae1cc1b5076801e453e7aae6ac40ab55b388bb91b9d0547a52f5aaceff710ea400205f18691120d4431e520afbe4266b836fadede15872d
  languageName: node
  linkType: hard

"pathe@npm:^2.0.3":
  version: 2.0.3
  resolution: "pathe@npm:2.0.3"
  checksum: 10c0/c118dc5a8b5c4166011b2b70608762e260085180bb9e33e80a50dcdb1e78c010b1624f4280c492c92b05fc276715a4c357d1f9edc570f8f1b3d90b6839ebaca1
  languageName: node
  linkType: hard

"pathval@npm:^2.0.0":
  version: 2.0.0
  resolution: "pathval@npm:2.0.0"
  checksum: 10c0/602e4ee347fba8a599115af2ccd8179836a63c925c23e04bd056d0674a64b39e3a081b643cc7bc0b84390517df2d800a46fcc5598d42c155fe4977095c2f77c5
  languageName: node
  linkType: hard

"performance-now@npm:^2.1.0":
  version: 2.1.0
  resolution: "performance-now@npm:2.1.0"
  checksum: 10c0/22c54de06f269e29f640e0e075207af57de5052a3d15e360c09b9a8663f393f6f45902006c1e71aa8a5a1cdfb1a47fe268826f8496d6425c362f00f5bc3e85d9
  languageName: node
  linkType: hard

"picocolors@npm:1.1.1, picocolors@npm:^1.0.0, picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: 10c0/e2e3e8170ab9d7c7421969adaa7e1b31434f789afb9b3f115f6b96d91945041ac3ceb02e9ec6fe6510ff036bcc0bf91e69a1772edc0b707e12b19c0f2d6bcf58
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 10c0/26c02b8d06f03206fc2ab8d16f19960f2ff9e81a658f831ecb656d8f17d9edc799e8364b1f4a7873e89d9702dff96204be0fa26fe4181f6843f040f819dac4be
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "picomatch@npm:4.0.2"
  checksum: 10c0/7c51f3ad2bb42c776f49ebf964c644958158be30d0a510efd5a395e8d49cb5acfed5b82c0c5b365523ce18e6ab85013c9ebe574f60305892ec3fa8eee8304ccc
  languageName: node
  linkType: hard

"pluralize@npm:^8.0.0":
  version: 8.0.0
  resolution: "pluralize@npm:8.0.0"
  checksum: 10c0/2044cfc34b2e8c88b73379ea4a36fc577db04f651c2909041b054c981cd863dd5373ebd030123ab058d194ae615d3a97cfdac653991e499d10caf592e8b3dc33
  languageName: node
  linkType: hard

"possible-typed-array-names@npm:^1.0.0":
  version: 1.1.0
  resolution: "possible-typed-array-names@npm:1.1.0"
  checksum: 10c0/c810983414142071da1d644662ce4caebce890203eb2bc7bf119f37f3fe5796226e117e6cca146b521921fa6531072674174a3325066ac66fce089a53e1e5196
  languageName: node
  linkType: hard

"postcss@npm:^8.5.3":
  version: 8.5.3
  resolution: "postcss@npm:8.5.3"
  dependencies:
    nanoid: "npm:^3.3.8"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10c0/b75510d7b28c3ab728c8733dd01538314a18c52af426f199a3c9177e63eb08602a3938bfb66b62dc01350b9aed62087eabbf229af97a1659eb8d3513cec823b3
  languageName: node
  linkType: hard

"postcss@npm:^8.5.5":
  version: 8.5.6
  resolution: "postcss@npm:8.5.6"
  dependencies:
    nanoid: "npm:^3.3.11"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10c0/5127cc7c91ed7a133a1b7318012d8bfa112da9ef092dddf369ae699a1f10ebbd89b1b9f25f3228795b84585c72aabd5ced5fc11f2ba467eedf7b081a66fad024
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: 10c0/b00d617431e7886c520a6f498a2e14c75ec58f6d93ba48c3b639cf241b54232d90daa05d83a9e9b9fef6baa63cb7e1e4602c2372fea5bc169668401eb127d0cd
  languageName: node
  linkType: hard

"prettier@npm:3.6.2":
  version: 3.6.2
  resolution: "prettier@npm:3.6.2"
  bin:
    prettier: bin/prettier.cjs
  checksum: 10c0/488cb2f2b99ec13da1e50074912870217c11edaddedeadc649b1244c749d15ba94e846423d062e2c4c9ae683e2d65f754de28889ba06e697ac4f988d44f45812
  languageName: node
  linkType: hard

"pretty-format@npm:^27.0.2":
  version: 27.5.1
  resolution: "pretty-format@npm:27.5.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
    ansi-styles: "npm:^5.0.0"
    react-is: "npm:^17.0.1"
  checksum: 10c0/0cbda1031aa30c659e10921fa94e0dd3f903ecbbbe7184a729ad66f2b6e7f17891e8c7d7654c458fa4ccb1a411ffb695b4f17bbcd3fe075fabe181027c4040ed
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10c0/bbe5edb944b0ad63387a1d5b1911ae93e05ce8d0f60de1035b218cdcceedfe39dbd2c697853355b70f1a090f8f58fe90da487c85216bf9671f9499d1a897e9e3
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 10c0/bec089239487833d46b59d80327a1605e1c5287eaad770a291add7f45fda1bb5e28b38e0e061add0a1d0ee0984788ce74fa394d345eed1c420cacf392c554367
  languageName: node
  linkType: hard

"promise-map-series@npm:^0.3.0":
  version: 0.3.0
  resolution: "promise-map-series@npm:0.3.0"
  checksum: 10c0/dc69dc6efc26f0325a42df873552fc5d6007a4738ec23af8e84432e581ff77d4b26345fe028faba5a8f153c8ba47a37ba33a58e329c6d04cafb3c397fbc09aab
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10c0/9c7045a1a2928094b5b9b15336dcd2a7b1c052f674550df63cc3f36cd44028e5080448175b6f6ca32b642de81150f5e7b1a98b728f15cb069f2dd60ac2616b96
  languageName: node
  linkType: hard

"prop-types@npm:15.x, prop-types@npm:^15.8.1":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: "npm:^1.4.0"
    object-assign: "npm:^4.1.1"
    react-is: "npm:^16.13.1"
  checksum: 10c0/59ece7ca2fb9838031d73a48d4becb9a7cc1ed10e610517c7d8f19a1e02fa47f7c27d557d8a5702bec3cfeccddc853579832b43f449e54635803f277b1c78077
  languageName: node
  linkType: hard

"proxy-from-env@npm:^1.1.0":
  version: 1.1.0
  resolution: "proxy-from-env@npm:1.1.0"
  checksum: 10c0/fe7dd8b1bdbbbea18d1459107729c3e4a2243ca870d26d34c2c1bcd3e4425b7bcc5112362df2d93cc7fb9746f6142b5e272fd1cc5c86ddf8580175186f6ad42b
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0, punycode@npm:^2.3.1":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: 10c0/14f76a8206bc3464f794fb2e3d3cc665ae416c01893ad7a02b23766eb07159144ee612ad67af5e84fa4479ccfe67678c4feb126b0485651b302babf66f04f9e9
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 10c0/900a93d3cdae3acd7d16f642c29a642aea32c2026446151f0778c62ac089d4b8e6c986811076e1ae180a694cedf077d453a11b58ff0a865629a4f82ab558e102
  languageName: node
  linkType: hard

"quick-temp@npm:^0.1.8":
  version: 0.1.8
  resolution: "quick-temp@npm:0.1.8"
  dependencies:
    mktemp: "npm:~0.4.0"
    rimraf: "npm:^2.5.4"
    underscore.string: "npm:~3.3.4"
  checksum: 10c0/3e8277a2e37db8e101fdefd59dc62050681b46d82f0c617eb700e2ef54f1c4985eff79985a24ef61834fd4c758050dbecfec7f34ed7caa6717f96de03ed9063c
  languageName: node
  linkType: hard

"raf@npm:^3.4.1":
  version: 3.4.1
  resolution: "raf@npm:3.4.1"
  dependencies:
    performance-now: "npm:^2.1.0"
  checksum: 10c0/337f0853c9e6a77647b0f499beedafea5d6facfb9f2d488a624f88b03df2be72b8a0e7f9118a3ff811377d534912039a3311815700d2b6d2313f82f736f9eb6e
  languageName: node
  linkType: hard

"react-complex-tree@npm:^2.6.0":
  version: 2.6.0
  resolution: "react-complex-tree@npm:2.6.0"
  peerDependencies:
    react: ">=16.0.0"
  checksum: 10c0/320d7d2d4c6364b62102f2c8a29dc484a9c1cf098f35580d5c9f0eaf18228929a31e235840cbfe8e79ddae990496915d19c5d5ac535e9e7d33c939065a92b48a
  languageName: node
  linkType: hard

"react-dom@npm:19.1.0":
  version: 19.1.0
  resolution: "react-dom@npm:19.1.0"
  dependencies:
    scheduler: "npm:^0.26.0"
  peerDependencies:
    react: ^19.1.0
  checksum: 10c0/3e26e89bb6c67c9a6aa86cb888c7a7f8258f2e347a6d2a15299c17eb16e04c19194e3452bc3255bd34000a61e45e2cb51e46292392340432f133e5a5d2dfb5fc
  languageName: node
  linkType: hard

"react-draggable@npm:^4.0.3, react-draggable@npm:^4.4.5":
  version: 4.4.6
  resolution: "react-draggable@npm:4.4.6"
  dependencies:
    clsx: "npm:^1.1.1"
    prop-types: "npm:^15.8.1"
  peerDependencies:
    react: ">= 16.3.0"
    react-dom: ">= 16.3.0"
  checksum: 10c0/1e8cf47414a8554caa68447e5f27749bc40e1eabb4806e2dadcb39ab081d263f517d6aaec5231677e6b425603037c7e3386d1549898f9ffcc98a86cabafb2b9a
  languageName: node
  linkType: hard

"react-fast-compare@npm:^3.2.2":
  version: 3.2.2
  resolution: "react-fast-compare@npm:3.2.2"
  checksum: 10c0/0bbd2f3eb41ab2ff7380daaa55105db698d965c396df73e6874831dbafec8c4b5b08ba36ff09df01526caa3c61595247e3269558c284e37646241cba2b90a367
  languageName: node
  linkType: hard

"react-grid-layout@npm:^1.5.1":
  version: 1.5.1
  resolution: "react-grid-layout@npm:1.5.1"
  dependencies:
    clsx: "npm:^2.0.0"
    fast-equals: "npm:^4.0.3"
    prop-types: "npm:^15.8.1"
    react-draggable: "npm:^4.4.5"
    react-resizable: "npm:^3.0.5"
    resize-observer-polyfill: "npm:^1.5.1"
  peerDependencies:
    react: ">= 16.3.0"
    react-dom: ">= 16.3.0"
  checksum: 10c0/32df5e44bc7485f12be0994722b76d74bd672bfa85ffd69a47bccefd6a03114a01781a9c5cbb93e65d4146573e30bcad738d711581667d934336196215d2d736
  languageName: node
  linkType: hard

"react-i18next@npm:^15.5.2":
  version: 15.5.3
  resolution: "react-i18next@npm:15.5.3"
  dependencies:
    "@babel/runtime": "npm:^7.27.6"
    html-parse-stringify: "npm:^3.0.1"
  peerDependencies:
    i18next: ">= 23.2.3"
    react: ">= 16.8.0"
    typescript: ^5
  peerDependenciesMeta:
    react-dom:
      optional: true
    react-native:
      optional: true
    typescript:
      optional: true
  checksum: 10c0/495d98a738e966cd655a378d953e45cfd1a810ccfaf4894a63c37b7a9043e69b55c5e14044d54290c91512e7fa37835c7e4637bfb8d68b6fc88bc493a5ffd9ee
  languageName: node
  linkType: hard

"react-icons@npm:^5.5.0":
  version: 5.5.0
  resolution: "react-icons@npm:5.5.0"
  peerDependencies:
    react: "*"
  checksum: 10c0/a24309bfc993c19cbcbfc928157e53a137851822779977b9588f6dd41ffc4d11ebc98b447f4039b0d309a858f0a42980f6bfb4477fb19f9f2d1bc2e190fcf79c
  languageName: node
  linkType: hard

"react-is@npm:18.2.0":
  version: 18.2.0
  resolution: "react-is@npm:18.2.0"
  checksum: 10c0/6eb5e4b28028c23e2bfcf73371e72cd4162e4ac7ab445ddae2afe24e347a37d6dc22fae6e1748632cd43c6d4f9b8f86dcf26bf9275e1874f436d129952528ae0
  languageName: node
  linkType: hard

"react-is@npm:19.1.0":
  version: 19.1.0
  resolution: "react-is@npm:19.1.0"
  checksum: 10c0/b6c6cadd172d5d39f66d493700d137a5545c294a62ce0f8ec793d59794c97d2bed6bad227626f16bd0e90004ed7fdc8ed662a004e6edcf5d2b7ecb6e3040ea6b
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: 10c0/33977da7a5f1a287936a0c85639fec6ca74f4f15ef1e59a6bc20338fc73dc69555381e211f7a3529b8150a1f71e4225525b41b60b52965bda53ce7d47377ada1
  languageName: node
  linkType: hard

"react-is@npm:^17.0.1":
  version: 17.0.2
  resolution: "react-is@npm:17.0.2"
  checksum: 10c0/2bdb6b93fbb1820b024b496042cce405c57e2f85e777c9aabd55f9b26d145408f9f74f5934676ffdc46f3dcff656d78413a6e43968e7b3f92eea35b3052e9053
  languageName: node
  linkType: hard

"react-refresh@npm:^0.17.0":
  version: 0.17.0
  resolution: "react-refresh@npm:0.17.0"
  checksum: 10c0/002cba940384c9930008c0bce26cac97a9d5682bc623112c2268ba0c155127d9c178a9a5cc2212d560088d60dfd503edd808669a25f9b377f316a32361d0b23c
  languageName: node
  linkType: hard

"react-resizable@npm:^3.0.5":
  version: 3.0.5
  resolution: "react-resizable@npm:3.0.5"
  dependencies:
    prop-types: "npm:15.x"
    react-draggable: "npm:^4.0.3"
  peerDependencies:
    react: ">= 16.3"
  checksum: 10c0/cfe50aa6efb79e0aa09bd681a5beab2fcd1186737c4952eb4c3974ed9395d5d263ccd1130961d06b8f5e24c8f544dd2967b5c740ce68719962d1771de7bdb350
  languageName: node
  linkType: hard

"react-router@npm:^7.6.1":
  version: 7.6.2
  resolution: "react-router@npm:7.6.2"
  dependencies:
    cookie: "npm:^1.0.1"
    set-cookie-parser: "npm:^2.6.0"
  peerDependencies:
    react: ">=18"
    react-dom: ">=18"
  peerDependenciesMeta:
    react-dom:
      optional: true
  checksum: 10c0/c8ef65f2a378f38e3cba900d67fa2b80a41c1c3925102875ee07c12faa01ea40991cb3fbefaf3ff6914e724c755732e3d7dec2b1bdef09e0fddd00fccc85a06a
  languageName: node
  linkType: hard

"react@npm:19.1.0":
  version: 19.1.0
  resolution: "react@npm:19.1.0"
  checksum: 10c0/530fb9a62237d54137a13d2cfb67a7db6a2156faed43eecc423f4713d9b20c6f2728b026b45e28fcd72e8eadb9e9ed4b089e99f5e295d2f0ad3134251bdd3698
  languageName: node
  linkType: hard

"reactflow@npm:^11.11.4":
  version: 11.11.4
  resolution: "reactflow@npm:11.11.4"
  dependencies:
    "@reactflow/background": "npm:11.3.14"
    "@reactflow/controls": "npm:11.2.14"
    "@reactflow/core": "npm:11.11.4"
    "@reactflow/minimap": "npm:11.7.14"
    "@reactflow/node-resizer": "npm:2.2.14"
    "@reactflow/node-toolbar": "npm:1.3.14"
  peerDependencies:
    react: ">=17"
    react-dom: ">=17"
  checksum: 10c0/5faf9895db29d39c190b78a6113a4f97430d576086efeb3570deec81035559297823dd9ca96b9dd7daeb17159a12ac9117e399e71696485e6ccf43e9f98c1f35
  languageName: node
  linkType: hard

"readable-stream@npm:3, readable-stream@npm:^3.4.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: "npm:^2.0.3"
    string_decoder: "npm:^1.1.1"
    util-deprecate: "npm:^1.0.1"
  checksum: 10c0/e37be5c79c376fdd088a45fa31ea2e423e5d48854be7a22a58869b4e84d25047b193f6acb54f1012331e1bcd667ffb569c01b99d36b0bd59658fb33f513511b7
  languageName: node
  linkType: hard

"readable-stream@npm:~2.3.6":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: "npm:~1.0.0"
    inherits: "npm:~2.0.3"
    isarray: "npm:~1.0.0"
    process-nextick-args: "npm:~2.0.0"
    safe-buffer: "npm:~5.1.1"
    string_decoder: "npm:~1.1.1"
    util-deprecate: "npm:~1.0.1"
  checksum: 10c0/7efdb01f3853bc35ac62ea25493567bf588773213f5f4a79f9c365e1ad13bab845ac0dae7bc946270dc40c3929483228415e92a3fc600cc7e4548992f41ee3fa
  languageName: node
  linkType: hard

"readdirp@npm:^4.0.1":
  version: 4.1.2
  resolution: "readdirp@npm:4.1.2"
  checksum: 10c0/60a14f7619dec48c9c850255cd523e2717001b0e179dc7037cfa0895da7b9e9ab07532d324bfb118d73a710887d1e35f79c495fa91582784493e085d18c72c62
  languageName: node
  linkType: hard

"redent@npm:^3.0.0":
  version: 3.0.0
  resolution: "redent@npm:3.0.0"
  dependencies:
    indent-string: "npm:^4.0.0"
    strip-indent: "npm:^3.0.0"
  checksum: 10c0/d64a6b5c0b50eb3ddce3ab770f866658a2b9998c678f797919ceb1b586bab9259b311407280bd80b804e2a7c7539b19238ae6a2a20c843f1a7fcff21d48c2eae
  languageName: node
  linkType: hard

"reflect.getprototypeof@npm:^1.0.6, reflect.getprototypeof@npm:^1.0.9":
  version: 1.0.10
  resolution: "reflect.getprototypeof@npm:1.0.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.9"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.7"
    get-proto: "npm:^1.0.1"
    which-builtin-type: "npm:^1.2.1"
  checksum: 10c0/7facec28c8008876f8ab98e80b7b9cb4b1e9224353fd4756dda5f2a4ab0d30fa0a5074777c6df24e1e0af463a2697513b0a11e548d99cf52f21f7bc6ba48d3ac
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.13.7":
  version: 0.13.11
  resolution: "regenerator-runtime@npm:0.13.11"
  checksum: 10c0/12b069dc774001fbb0014f6a28f11c09ebfe3c0d984d88c9bced77fdb6fedbacbca434d24da9ae9371bfbf23f754869307fb51a4c98a8b8b18e5ef748677ca24
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.14.0":
  version: 0.14.1
  resolution: "regenerator-runtime@npm:0.14.1"
  checksum: 10c0/1b16eb2c4bceb1665c89de70dcb64126a22bc8eb958feef3cd68fe11ac6d2a4899b5cd1b80b0774c7c03591dc57d16631a7f69d2daa2ec98100e2f29f7ec4cc4
  languageName: node
  linkType: hard

"regexp-tree@npm:^0.1.27":
  version: 0.1.27
  resolution: "regexp-tree@npm:0.1.27"
  bin:
    regexp-tree: bin/regexp-tree
  checksum: 10c0/f636f44b4a0d93d7d6926585ecd81f63e4ce2ac895bc417b2ead0874cd36b337dcc3d0fedc63f69bf5aaeaa4340f36ca7e750c9687cceaf8087374e5284e843c
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.3":
  version: 1.5.4
  resolution: "regexp.prototype.flags@npm:1.5.4"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-errors: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    set-function-name: "npm:^2.0.2"
  checksum: 10c0/83b88e6115b4af1c537f8dabf5c3744032cb875d63bc05c288b1b8c0ef37cbe55353f95d8ca817e8843806e3e150b118bc624e4279b24b4776b4198232735a77
  languageName: node
  linkType: hard

"regjsparser@npm:^0.12.0":
  version: 0.12.0
  resolution: "regjsparser@npm:0.12.0"
  dependencies:
    jsesc: "npm:~3.0.2"
  bin:
    regjsparser: bin/parser
  checksum: 10c0/99d3e4e10c8c7732eb7aa843b8da2fd8b647fe144d3711b480e4647dc3bff4b1e96691ccf17f3ace24aa866a50b064236177cb25e6e4fbbb18285d99edaed83b
  languageName: node
  linkType: hard

"remove-trailing-separator@npm:^1.1.0":
  version: 1.1.0
  resolution: "remove-trailing-separator@npm:1.1.0"
  checksum: 10c0/3568f9f8f5af3737b4aee9e6e1e8ec4be65a92da9cb27f989e0893714d50aa95ed2ff02d40d1fa35e1b1a234dc9c2437050ef356704a3999feaca6667d9e9bfc
  languageName: node
  linkType: hard

"replace-ext@npm:^2.0.0":
  version: 2.0.0
  resolution: "replace-ext@npm:2.0.0"
  checksum: 10c0/52cb1006f83c5f07ef2c76b070c58bdeca1b67beded57d60593d1af8cd8ee731501d0433645cea8e9a4bf57a7018f47c9a3928c0463496cad1946fa85907aa47
  languageName: node
  linkType: hard

"resize-observer-polyfill@npm:^1.5.1":
  version: 1.5.1
  resolution: "resize-observer-polyfill@npm:1.5.1"
  checksum: 10c0/5e882475067f0b97dc07e0f37c3e335ac5bc3520d463f777cec7e894bb273eddbfecb857ae668e6fb6881fd6f6bb7148246967172139302da50fa12ea3a15d95
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: 10c0/8408eec31a3112ef96e3746c37be7d64020cda07c03a920f5024e77290a218ea758b26ca9529fd7b1ad283947f34b2291c1c0f6aa0ed34acfdda9c6014c8d190
  languageName: node
  linkType: hard

"resolve-options@npm:^2.0.0":
  version: 2.0.0
  resolution: "resolve-options@npm:2.0.0"
  dependencies:
    value-or-function: "npm:^4.0.0"
  checksum: 10c0/108f22186cad8748f1f0263944702a9949a12074e49442827845a52048f9156290781ceab8aee3e26ad868347266746704ee59a83a8f2fe2ce35228d054e325e
  languageName: node
  linkType: hard

"resolve@npm:^2.0.0-next.5":
  version: 2.0.0-next.5
  resolution: "resolve@npm:2.0.0-next.5"
  dependencies:
    is-core-module: "npm:^2.13.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/a6c33555e3482ea2ec4c6e3d3bf0d78128abf69dca99ae468e64f1e30acaa318fd267fb66c8836b04d558d3e2d6ed875fe388067e7d8e0de647d3c21af21c43a
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^2.0.0-next.5#optional!builtin<compat/resolve>":
  version: 2.0.0-next.5
  resolution: "resolve@patch:resolve@npm%3A2.0.0-next.5#optional!builtin<compat/resolve>::version=2.0.0-next.5&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.13.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/78ad6edb8309a2bfb720c2c1898f7907a37f858866ce11a5974643af1203a6a6e05b2fa9c53d8064a673a447b83d42569260c306d43628bff5bb101969708355
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10c0/59933e8501727ba13ad73ef4a04d5280b3717fd650408460c987392efe9d7be2040778ed8ebe933c5cbd63da3dcc37919c141ef8af0a54a6e4fca5a2af177bfe
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.1.0
  resolution: "reusify@npm:1.1.0"
  checksum: 10c0/4eff0d4a5f9383566c7d7ec437b671cc51b25963bd61bf127c3f3d3f68e44a026d99b8d2f1ad344afff8d278a8fe70a8ea092650a716d22287e8bef7126bb2fa
  languageName: node
  linkType: hard

"rgbcolor@npm:^1.0.1":
  version: 1.0.1
  resolution: "rgbcolor@npm:1.0.1"
  checksum: 10c0/13af06c523351bac2854b85a22d1dfafd9310efd898e9bd96c8706f9aa09a3ddc8392ab00ae03d12950782164a97677f21834ffd84ffebf76ae106add319f956
  languageName: node
  linkType: hard

"rimraf@npm:^2.5.4":
  version: 2.7.1
  resolution: "rimraf@npm:2.7.1"
  dependencies:
    glob: "npm:^7.1.3"
  bin:
    rimraf: ./bin.js
  checksum: 10c0/4eef73d406c6940927479a3a9dee551e14a54faf54b31ef861250ac815172bade86cc6f7d64a4dc5e98b65e4b18a2e1c9ff3b68d296be0c748413f092bb0dd40
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: "npm:^7.1.3"
  bin:
    rimraf: bin.js
  checksum: 10c0/9cb7757acb489bd83757ba1a274ab545eafd75598a9d817e0c3f8b164238dd90eba50d6b848bd4dcc5f3040912e882dc7ba71653e35af660d77b25c381d402e8
  languageName: node
  linkType: hard

"rimraf@npm:^5.0.5":
  version: 5.0.10
  resolution: "rimraf@npm:5.0.10"
  dependencies:
    glob: "npm:^10.3.7"
  bin:
    rimraf: dist/esm/bin.mjs
  checksum: 10c0/7da4fd0e15118ee05b918359462cfa1e7fe4b1228c7765195a45b55576e8c15b95db513b8466ec89129666f4af45ad978a3057a02139afba1a63512a2d9644cc
  languageName: node
  linkType: hard

"robust-predicates@npm:^3.0.2":
  version: 3.0.2
  resolution: "robust-predicates@npm:3.0.2"
  checksum: 10c0/4ecd53649f1c2d49529c85518f2fa69ffb2f7a4453f7fd19c042421c7b4d76c3efb48bc1c740c8f7049346d7cb58cf08ee0c9adaae595cc23564d360adb1fde4
  languageName: node
  linkType: hard

"rollup@npm:^4.34.9, rollup@npm:^4.40.0":
  version: 4.43.0
  resolution: "rollup@npm:4.43.0"
  dependencies:
    "@rollup/rollup-android-arm-eabi": "npm:4.43.0"
    "@rollup/rollup-android-arm64": "npm:4.43.0"
    "@rollup/rollup-darwin-arm64": "npm:4.43.0"
    "@rollup/rollup-darwin-x64": "npm:4.43.0"
    "@rollup/rollup-freebsd-arm64": "npm:4.43.0"
    "@rollup/rollup-freebsd-x64": "npm:4.43.0"
    "@rollup/rollup-linux-arm-gnueabihf": "npm:4.43.0"
    "@rollup/rollup-linux-arm-musleabihf": "npm:4.43.0"
    "@rollup/rollup-linux-arm64-gnu": "npm:4.43.0"
    "@rollup/rollup-linux-arm64-musl": "npm:4.43.0"
    "@rollup/rollup-linux-loongarch64-gnu": "npm:4.43.0"
    "@rollup/rollup-linux-powerpc64le-gnu": "npm:4.43.0"
    "@rollup/rollup-linux-riscv64-gnu": "npm:4.43.0"
    "@rollup/rollup-linux-riscv64-musl": "npm:4.43.0"
    "@rollup/rollup-linux-s390x-gnu": "npm:4.43.0"
    "@rollup/rollup-linux-x64-gnu": "npm:4.43.0"
    "@rollup/rollup-linux-x64-musl": "npm:4.43.0"
    "@rollup/rollup-win32-arm64-msvc": "npm:4.43.0"
    "@rollup/rollup-win32-ia32-msvc": "npm:4.43.0"
    "@rollup/rollup-win32-x64-msvc": "npm:4.43.0"
    "@types/estree": "npm:1.0.7"
    fsevents: "npm:~2.3.2"
  dependenciesMeta:
    "@rollup/rollup-android-arm-eabi":
      optional: true
    "@rollup/rollup-android-arm64":
      optional: true
    "@rollup/rollup-darwin-arm64":
      optional: true
    "@rollup/rollup-darwin-x64":
      optional: true
    "@rollup/rollup-freebsd-arm64":
      optional: true
    "@rollup/rollup-freebsd-x64":
      optional: true
    "@rollup/rollup-linux-arm-gnueabihf":
      optional: true
    "@rollup/rollup-linux-arm-musleabihf":
      optional: true
    "@rollup/rollup-linux-arm64-gnu":
      optional: true
    "@rollup/rollup-linux-arm64-musl":
      optional: true
    "@rollup/rollup-linux-loongarch64-gnu":
      optional: true
    "@rollup/rollup-linux-powerpc64le-gnu":
      optional: true
    "@rollup/rollup-linux-riscv64-gnu":
      optional: true
    "@rollup/rollup-linux-riscv64-musl":
      optional: true
    "@rollup/rollup-linux-s390x-gnu":
      optional: true
    "@rollup/rollup-linux-x64-gnu":
      optional: true
    "@rollup/rollup-linux-x64-musl":
      optional: true
    "@rollup/rollup-win32-arm64-msvc":
      optional: true
    "@rollup/rollup-win32-ia32-msvc":
      optional: true
    "@rollup/rollup-win32-x64-msvc":
      optional: true
    fsevents:
      optional: true
  bin:
    rollup: dist/bin/rollup
  checksum: 10c0/a14a16ee5433f9eddfe803ed1a3f4528e3e96f746e55bf88c5482f9a60a4ad61f507b59f46d5d9c8dc98bb7983483e0c94b760ae37c02157eba9da5665c1641b
  languageName: node
  linkType: hard

"rrweb-cssom@npm:^0.8.0":
  version: 0.8.0
  resolution: "rrweb-cssom@npm:0.8.0"
  checksum: 10c0/56f2bfd56733adb92c0b56e274c43f864b8dd48784d6fe946ef5ff8d438234015e59ad837fc2ad54714b6421384141c1add4eb569e72054e350d1f8a50b8ac7b
  languageName: node
  linkType: hard

"rsvp@npm:^4.8.5":
  version: 4.8.5
  resolution: "rsvp@npm:4.8.5"
  checksum: 10c0/7978f01060a48204506a8ebe15cdbd468498f5ae538b1d7ee3e7630375ba7cb2f98df2f596c12d3f4d5d5c21badc1c6ca8009f5142baded8511609a28eabd19a
  languageName: node
  linkType: hard

"rsvp@npm:~3.2.1":
  version: 3.2.1
  resolution: "rsvp@npm:3.2.1"
  checksum: 10c0/7c075134976d0e95710ed62f87eaf01fa7dc1068357c9988f224e53509ac152ddeb1781cfd7784f13e2cc084c8dee71ad12317b758687b7ea107af2f8588704d
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: 10c0/200b5ab25b5b8b7113f9901bfe3afc347e19bb7475b267d55ad0eb86a62a46d77510cb0f232507c9e5d497ebda569a08a9867d0d14f57a82ad5564d991588b39
  languageName: node
  linkType: hard

"rw@npm:1":
  version: 1.3.3
  resolution: "rw@npm:1.3.3"
  checksum: 10c0/b1e1ef37d1e79d9dc7050787866e30b6ddcb2625149276045c262c6b4d53075ddc35f387a856a8e76f0d0df59f4cd58fe24707e40797ebee66e542b840ed6a53
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.1.3":
  version: 1.1.3
  resolution: "safe-array-concat@npm:1.1.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    has-symbols: "npm:^1.1.0"
    isarray: "npm:^2.0.5"
  checksum: 10c0/43c86ffdddc461fb17ff8a17c5324f392f4868f3c7dd2c6a5d9f5971713bc5fd755667212c80eab9567595f9a7509cc2f83e590ddaebd1bd19b780f9c79f9a8d
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: 10c0/780ba6b5d99cc9a40f7b951d47152297d0e260f0df01472a1b99d4889679a4b94a13d644f7dbc4f022572f09ae9005fa2fbb93bbbd83643316f365a3e9a45b21
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10c0/6501914237c0a86e9675d4e51d89ca3c21ffd6a31642efeba25ad65720bce6921c9e7e974e5be91a786b25aa058b5303285d3c15dbabf983a919f5f630d349f3
  languageName: node
  linkType: hard

"safe-push-apply@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-push-apply@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    isarray: "npm:^2.0.5"
  checksum: 10c0/831f1c9aae7436429e7862c7e46f847dfe490afac20d0ee61bae06108dbf5c745a0de3568ada30ccdd3eeb0864ca8331b2eef703abd69bfea0745b21fd320750
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex-test@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-regex: "npm:^1.2.1"
  checksum: 10c0/f2c25281bbe5d39cddbbce7f86fca5ea9b3ce3354ea6cd7c81c31b006a5a9fff4286acc5450a3b9122c56c33eba69c56b9131ad751457b2b4a585825e6a10665
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10c0/7e3c8b2e88a1841c9671094bbaeebd94448111dd90a81a1f606f3f67708a6ec57763b3b47f06da09fc6054193e0e6709e77325415dc8422b04497a8070fa02d4
  languageName: node
  linkType: hard

"sass@npm:1.89.2":
  version: 1.89.2
  resolution: "sass@npm:1.89.2"
  dependencies:
    "@parcel/watcher": "npm:^2.4.1"
    chokidar: "npm:^4.0.0"
    immutable: "npm:^5.0.2"
    source-map-js: "npm:>=0.6.2 <2.0.0"
  dependenciesMeta:
    "@parcel/watcher":
      optional: true
  bin:
    sass: sass.js
  checksum: 10c0/752ccc7581b0c6395f63918116c20924e99943a86d79e94f5c4a0d41b1e981fe1f0ecd1ee82fff21496f81dbc91f68fb35a498166562ec8ec53e7aad7c3dbd9d
  languageName: node
  linkType: hard

"saxes@npm:^6.0.0":
  version: 6.0.0
  resolution: "saxes@npm:6.0.0"
  dependencies:
    xmlchars: "npm:^2.2.0"
  checksum: 10c0/3847b839f060ef3476eb8623d099aa502ad658f5c40fd60c105ebce86d244389b0d76fcae30f4d0c728d7705ceb2f7e9b34bb54717b6a7dbedaf5dad2d9a4b74
  languageName: node
  linkType: hard

"scheduler@npm:^0.26.0":
  version: 0.26.0
  resolution: "scheduler@npm:0.26.0"
  checksum: 10c0/5b8d5bfddaae3513410eda54f2268e98a376a429931921a81b5c3a2873aab7ca4d775a8caac5498f8cbc7d0daeab947cf923dbd8e215d61671f9f4e392d34356
  languageName: node
  linkType: hard

"semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: 10c0/e3d79b609071caa78bcb6ce2ad81c7966a46a7431d9d58b8800cfa9cb6a63699b3899a0e4bcce36167a284578212d9ae6942b6929ba4aa5015c079a67751d42d
  languageName: node
  linkType: hard

"semver@npm:^7.3.5, semver@npm:^7.5.3, semver@npm:^7.6.0, semver@npm:^7.7.1":
  version: 7.7.1
  resolution: "semver@npm:7.7.1"
  bin:
    semver: bin/semver.js
  checksum: 10c0/fd603a6fb9c399c6054015433051bdbe7b99a940a8fb44b85c2b524c4004b023d7928d47cb22154f8d054ea7ee8597f586605e05b52047f048278e4ac56ae958
  languageName: node
  linkType: hard

"set-cookie-parser@npm:^2.6.0":
  version: 2.7.1
  resolution: "set-cookie-parser@npm:2.7.1"
  checksum: 10c0/060c198c4c92547ac15988256f445eae523f57f2ceefeccf52d30d75dedf6bff22b9c26f756bd44e8e560d44ff4ab2130b178bd2e52ef5571bf7be3bd7632d9a
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.2":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.4"
    gopd: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/82850e62f412a258b71e123d4ed3873fa9377c216809551192bb6769329340176f109c2eeae8c22a8d386c76739855f78e8716515c818bcaef384b51110f0f3c
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "set-function-name@npm:2.0.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    functions-have-names: "npm:^1.2.3"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/fce59f90696c450a8523e754abb305e2b8c73586452619c2bad5f7bf38c7b6b4651895c9db895679c5bef9554339cf3ef1c329b66ece3eda7255785fbe299316
  languageName: node
  linkType: hard

"set-proto@npm:^1.0.0":
  version: 1.0.0
  resolution: "set-proto@npm:1.0.0"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/ca5c3ccbba479d07c30460e367e66337cec825560b11e8ba9c5ebe13a2a0d6021ae34eddf94ff3dfe17a3104dc1f191519cb6c48378b503e5c3f36393938776a
  languageName: node
  linkType: hard

"shallow-clone@npm:^3.0.0":
  version: 3.0.1
  resolution: "shallow-clone@npm:3.0.1"
  dependencies:
    kind-of: "npm:^6.0.2"
  checksum: 10c0/7bab09613a1b9f480c85a9823aebec533015579fa055ba6634aa56ba1f984380670eaf33b8217502931872aa1401c9fcadaa15f9f604d631536df475b05bcf1e
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10c0/a41692e7d89a553ef21d324a5cceb5f686d1f3c040759c50aab69688634688c5c327f26f3ecf7001ebfd78c01f3c7c0a11a7c8bfd0a8bc9f6240d4f40b224e4e
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10c0/1dbed0726dd0e1152a92696c76c7f06084eb32a90f0528d11acd764043aacf76994b2fb30aa1291a21bd019d6699164d048286309a278855ee7bec06cf6fb690
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
  checksum: 10c0/644f4ac893456c9490ff388bf78aea9d333d5e5bfc64cfb84be8f04bf31ddc111a8d4b83b85d7e7e8a7b845bc185a9ad02c052d20e086983cf59f0be517d9b3d
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
  checksum: 10c0/010584e6444dd8a20b85bc926d934424bd809e1a3af941cace229f7fdcb751aada0fb7164f60c2e22292b7fa3c0ff0bce237081fd4cdbc80de1dc68e95430672
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
    side-channel-map: "npm:^1.0.1"
  checksum: 10c0/71362709ac233e08807ccd980101c3e2d7efe849edc51455030327b059f6c4d292c237f94dc0685031dd11c07dd17a68afde235d6cf2102d949567f98ab58185
  languageName: node
  linkType: hard

"side-channel@npm:^1.1.0":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
    side-channel-list: "npm:^1.0.0"
    side-channel-map: "npm:^1.0.1"
    side-channel-weakmap: "npm:^1.0.2"
  checksum: 10c0/cb20dad41eb032e6c24c0982e1e5a24963a28aa6122b4f05b3f3d6bf8ae7fd5474ef382c8f54a6a3ab86e0cac4d41a23bd64ede3970e5bfb50326ba02a7996e6
  languageName: node
  linkType: hard

"siginfo@npm:^2.0.0":
  version: 2.0.0
  resolution: "siginfo@npm:2.0.0"
  checksum: 10c0/3def8f8e516fbb34cb6ae415b07ccc5d9c018d85b4b8611e3dc6f8be6d1899f693a4382913c9ed51a06babb5201639d76453ab297d1c54a456544acf5c892e34
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10c0/41602dce540e46d599edba9d9860193398d135f7ff72cab629db5171516cfae628d21e7bfccde1bbfdf11c48726bc2a6d1a8fb8701125852fbfda7cf19c6aa83
  languageName: node
  linkType: hard

"simple-swizzle@npm:^0.2.2":
  version: 0.2.2
  resolution: "simple-swizzle@npm:0.2.2"
  dependencies:
    is-arrayish: "npm:^0.3.1"
  checksum: 10c0/df5e4662a8c750bdba69af4e8263c5d96fe4cd0f9fe4bdfa3cbdeb45d2e869dff640beaaeb1ef0e99db4d8d2ec92f85508c269f50c972174851bc1ae5bd64308
  languageName: node
  linkType: hard

"single-spa-react@npm:^6.0.2":
  version: 6.0.2
  resolution: "single-spa-react@npm:6.0.2"
  dependencies:
    browserslist-config-single-spa: "npm:^1.0.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: "*"
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/f1241cdec61c92538724b67c046e2c38db6a218e302cdd3052270f7a57817ee37b8b188e8383db816e057a93e5c51849e87bef2b293cae8f42683ec8dadfb910
  languageName: node
  linkType: hard

"single-spa@npm:^6.0.3":
  version: 6.0.3
  resolution: "single-spa@npm:6.0.3"
  checksum: 10c0/11e2419d54456831d6e745397ab632d65d3d6d82e1480f0732ac1c2989cfa7acf8ffcbb90f6a78efba2510ab6db99880ca82bb4d4f207b03f82a863e0a32f442
  languageName: node
  linkType: hard

"sirv@npm:^3.0.1":
  version: 3.0.1
  resolution: "sirv@npm:3.0.1"
  dependencies:
    "@polka/url": "npm:^1.0.0-next.24"
    mrmime: "npm:^2.0.0"
    totalist: "npm:^3.0.0"
  checksum: 10c0/7cf64b28daa69b15f77b38b0efdd02c007b72bb3ec5f107b208ebf59f01b174ef63a1db3aca16d2df925501831f4c209be6ece3302b98765919ef5088b45bf80
  languageName: node
  linkType: hard

"size-sensor@npm:^1.0.1":
  version: 1.0.2
  resolution: "size-sensor@npm:1.0.2"
  checksum: 10c0/2cb020cd54ae7bea76a0600ba82c1a09daf51e8bec6f96355e17b4806eb24003626e3349d6cb091f821ed5fbba193f59ca59f0ea7f6dd8b75d7c20b96c465c26
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10c0/a16775323e1404dd43fabafe7460be13a471e021637bc7889468eb45ce6a6b207261f454e4e530a19500cc962c4cc5348583520843b363f4193cee5c00e1e539
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10c0/5d2c6cecba6821389aabf18728325730504bf9bb1d9e342e7987a5d13badd7a98838cc9a55b8ed3cb866ad37cc23e1086f09c4d72d93105ce9dfe76330e9d2a6
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.4
  resolution: "socks@npm:2.8.4"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 10c0/00c3271e233ccf1fb83a3dd2060b94cc37817e0f797a93c560b9a7a86c4a0ec2961fb31263bdd24a3c28945e24868b5f063cd98744171d9e942c513454b50ae5
  languageName: node
  linkType: hard

"sort-keys@npm:^5.0.0":
  version: 5.1.0
  resolution: "sort-keys@npm:5.1.0"
  dependencies:
    is-plain-obj: "npm:^4.0.0"
  checksum: 10c0/fdb7aeb02368ad91b2ea947b59f3c95d80f8c71bbcb5741ebd55852994f54a129af3b3663b280951566fe5897de056428810dbb58c61db831e588c0ac110f2b0
  languageName: node
  linkType: hard

"sortobject@npm:^4.0.0":
  version: 4.17.0
  resolution: "sortobject@npm:4.17.0"
  checksum: 10c0/4d02e6bdf179c47bf1b24829a46d9c52be274766aad8bf7ad68834e7f5630891c9bfd4403616cc9077cfe7b9d7da8c05421827063801f6ec722a358e57836423
  languageName: node
  linkType: hard

"source-map-js@npm:>=0.6.2 <2.0.0, source-map-js@npm:^1.2.0, source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 10c0/7bda1fc4c197e3c6ff17de1b8b2c20e60af81b63a52cb32ec5a5d67a20a7d42651e2cb34ebe93833c5a2a084377e17455854fee3e21e7925c64a51b6a52b0faf
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.1, sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10c0/09270dc4f30d479e666aee820eacd9e464215cdff53848b443964202bf4051490538e5dd1b42e1a65cf7296916ca17640aebf63dae9812749c7542ee5f288dec
  languageName: node
  linkType: hard

"ssf@npm:~0.11.2":
  version: 0.11.2
  resolution: "ssf@npm:0.11.2"
  dependencies:
    frac: "npm:~1.1.2"
  checksum: 10c0/c3fd24a90dc37a9dc5c4154cb4121e27507c33ebfeee3532aaf03625756b2c006cf79c0a23db0ba16c4a6e88e1349455327867e03453fc9d54b32c546bc18ca6
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/caddd5f544b2006e88fa6b0124d8d7b28208b83c72d7672d5ade44d794525d23b540f3396108c4eb9280dcb7c01f0bef50682f5b4b2c34291f7c5e211fd1417d
  languageName: node
  linkType: hard

"stackback@npm:0.0.2":
  version: 0.0.2
  resolution: "stackback@npm:0.0.2"
  checksum: 10c0/89a1416668f950236dd5ac9f9a6b2588e1b9b62b1b6ad8dff1bfc5d1a15dbf0aafc9b52d2226d00c28dffff212da464eaeebfc6b7578b9d180cef3e3782c5983
  languageName: node
  linkType: hard

"stackblur-canvas@npm:^2.0.0":
  version: 2.7.0
  resolution: "stackblur-canvas@npm:2.7.0"
  checksum: 10c0/df290d0629056d5bb43d37548d0b24cb8593c79d742650e68489abf61013db578c9980724c2508bb738d107204f2e2494ab94c3cf69d6b725caa9c63b8c7e272
  languageName: node
  linkType: hard

"std-env@npm:^3.9.0":
  version: 3.9.0
  resolution: "std-env@npm:3.9.0"
  checksum: 10c0/4a6f9218aef3f41046c3c7ecf1f98df00b30a07f4f35c6d47b28329bc2531eef820828951c7d7b39a1c5eb19ad8a46e3ddfc7deb28f0a2f3ceebee11bab7ba50
  languageName: node
  linkType: hard

"stream-composer@npm:^1.0.2":
  version: 1.0.2
  resolution: "stream-composer@npm:1.0.2"
  dependencies:
    streamx: "npm:^2.13.2"
  checksum: 10c0/00b7c63e67dffa1f7d7149f47072e61e3e788aa1221a6116cac0186f387650816927e41b0934e615f47fec6d8d9c5b93cc85952748ed0238975090dfabf17fa7
  languageName: node
  linkType: hard

"streamx@npm:^2.12.0, streamx@npm:^2.12.5, streamx@npm:^2.13.2, streamx@npm:^2.14.0":
  version: 2.22.0
  resolution: "streamx@npm:2.22.0"
  dependencies:
    bare-events: "npm:^2.2.0"
    fast-fifo: "npm:^1.3.2"
    text-decoder: "npm:^1.1.0"
  dependenciesMeta:
    bare-events:
      optional: true
  checksum: 10c0/f5017998a5b6360ba652599d20ef308c8c8ab0e26c8e5f624f0706f0ea12624e94fdf1ec18318124498529a1b106a1ab1c94a1b1e1ad6c2eec7cb9c8ac1b9198
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10c0/1e525e92e5eae0afd7454086eed9c818ee84374bb80328fc41217ae72ff5f065ef1c9d7f72da41de40c75fa8bb3dee63d92373fd492c84260a552c636392a47b
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/ab9c4264443d35b8b923cbdd513a089a60de339216d3b0ed3be3ba57d6880e1a192b70ae17225f764d7adbf5994e9bb8df253a944736c15a0240eff553c678ca
  languageName: node
  linkType: hard

"string.prototype.matchall@npm:^4.0.12":
  version: 4.0.12
  resolution: "string.prototype.matchall@npm:4.0.12"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.6"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.6"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    internal-slot: "npm:^1.1.0"
    regexp.prototype.flags: "npm:^1.5.3"
    set-function-name: "npm:^2.0.2"
    side-channel: "npm:^1.1.0"
  checksum: 10c0/1a53328ada73f4a77f1fdf1c79414700cf718d0a8ef6672af5603e709d26a24f2181208144aed7e858b1bcc1a0d08567a570abfb45567db4ae47637ed2c2f85c
  languageName: node
  linkType: hard

"string.prototype.repeat@npm:^1.0.0":
  version: 1.0.0
  resolution: "string.prototype.repeat@npm:1.0.0"
  dependencies:
    define-properties: "npm:^1.1.3"
    es-abstract: "npm:^1.17.5"
  checksum: 10c0/94c7978566cffa1327d470fd924366438af9b04b497c43a9805e476e2e908aa37a1fd34cc0911156c17556dab62159d12c7b92b3cc304c3e1281fe4c8e668f40
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.10":
  version: 1.2.10
  resolution: "string.prototype.trim@npm:1.2.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-data-property: "npm:^1.1.4"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-object-atoms: "npm:^1.0.0"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/8a8854241c4b54a948e992eb7dd6b8b3a97185112deb0037a134f5ba57541d8248dd610c966311887b6c2fd1181a3877bffb14d873ce937a344535dabcc648f8
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.9":
  version: 1.0.9
  resolution: "string.prototype.trimend@npm:1.0.9"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/59e1a70bf9414cb4c536a6e31bef5553c8ceb0cf44d8b4d0ed65c9653358d1c64dd0ec203b100df83d0413bbcde38b8c5d49e14bc4b86737d74adc593a0d35b6
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.8":
  version: 1.0.8
  resolution: "string.prototype.trimstart@npm:1.0.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/d53af1899959e53c83b64a5fd120be93e067da740e7e75acb433849aa640782fb6c7d4cd5b84c954c84413745a3764df135a8afeb22908b86a835290788d8366
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: "npm:~5.2.0"
  checksum: 10c0/810614ddb030e271cd591935dcd5956b2410dd079d64ff92a1844d6b7588bf992b3e1b69b0f4d34a3e06e0bd73046ac646b5264c1987b20d0601f81ef35d731d
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: "npm:~5.1.0"
  checksum: 10c0/b4f89f3a92fd101b5653ca3c99550e07bdf9e13b35037e9e2a1c7b47cec4e55e06ff3fc468e314a0b5e80bfbaf65c1ca5a84978764884ae9413bec1fc6ca924e
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10c0/1ae5f212a126fe5b167707f716942490e3933085a5ff6c008ab97ab2f272c8025d3aa218b7bd6ab25729ca20cc81cddb252102f8751e13482a5199e873680952
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10c0/a198c3762e8832505328cbf9e8c8381de14a4fa50a4f9b2160138158ea88c0f5549fb50cb13c651c3088f47e63a108b34622ec18c0499b6c8c3a5ddf6b305ac4
  languageName: node
  linkType: hard

"strip-indent@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-indent@npm:3.0.0"
  dependencies:
    min-indent: "npm:^1.0.0"
  checksum: 10c0/ae0deaf41c8d1001c5d4fbe16cb553865c1863da4fae036683b474fa926af9fc121e155cb3fc57a68262b2ae7d5b8420aa752c97a6428c315d00efe2a3875679
  languageName: node
  linkType: hard

"strip-indent@npm:^4.0.0":
  version: 4.0.0
  resolution: "strip-indent@npm:4.0.0"
  dependencies:
    min-indent: "npm:^1.0.1"
  checksum: 10c0/6b1fb4e22056867f5c9e7a6f3f45922d9a2436cac758607d58aeaac0d3b16ec40b1c43317de7900f1b8dd7a4107352fa47fb960f2c23566538c51e8585c8870e
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 10c0/9681a6257b925a7fa0f285851c0e613cc934a50661fa7bb41ca9cbbff89686bb4a0ee366e6ecedc4daafd01e83eee0720111ab294366fe7c185e935475ebcecd
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/afb4c88521b8b136b5f5f95160c98dee7243dc79d5432db7efc27efb219385bbc7d9427398e43dd6cc730a0f87d5085ce1652af7efbe391327bc0a7d0f7fc124
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 10c0/6c4032340701a9950865f7ae8ef38578d8d7053f5e10518076e6554a9381fa91bd9c6850193695c141f32b21f979c985db07265a758867bac95de05f7d8aeb39
  languageName: node
  linkType: hard

"svg-pathdata@npm:^6.0.3":
  version: 6.0.3
  resolution: "svg-pathdata@npm:6.0.3"
  checksum: 10c0/1ba4ad2fa81e86df37d6e78d3be9e664bbedf97773b725a863a85db384285be32dc37d9c0d61e477d89594ee95b967d2c53d6bee2d76420aab670ab4124a38b9
  languageName: node
  linkType: hard

"symbol-tree@npm:^3.2.4":
  version: 3.2.4
  resolution: "symbol-tree@npm:3.2.4"
  checksum: 10c0/dfbe201ae09ac6053d163578778c53aa860a784147ecf95705de0cd23f42c851e1be7889241495e95c37cabb058edb1052f141387bef68f705afc8f9dd358509
  languageName: node
  linkType: hard

"symlink-or-copy@npm:^1.1.8, symlink-or-copy@npm:^1.2.0, symlink-or-copy@npm:^1.3.1":
  version: 1.3.1
  resolution: "symlink-or-copy@npm:1.3.1"
  checksum: 10c0/0d3842c359ce56991e912623fb75e76843e71a828c72f16024d717c842954c84086d90776738a6f0d5a4314f14b56580cfa48facba6b26b4da06aa7e80595931
  languageName: node
  linkType: hard

"tabbable@npm:^6.0.0, tabbable@npm:^6.2.0":
  version: 6.2.0
  resolution: "tabbable@npm:6.2.0"
  checksum: 10c0/ced8b38f05f2de62cd46836d77c2646c42b8c9713f5bd265daf0e78ff5ac73d3ba48a7ca45f348bafeef29b23da7187c72250742d37627883ef89cbd7fa76898
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10c0/d4679609bb2a9b48eeaf84632b6d844128d2412b95b6de07d53d8ee8baf4ca0857c9331dfa510390a0727b550fd543d4d1a10995ad86cdf078423fbb8d99831d
  languageName: node
  linkType: hard

"teex@npm:^1.0.1":
  version: 1.0.1
  resolution: "teex@npm:1.0.1"
  dependencies:
    streamx: "npm:^2.12.5"
  checksum: 10c0/8df9166c037ba694b49d32a49858e314c60e513d55ac5e084dbf1ddbb827c5fa43cc389a81e87684419c21283308e9d68bb068798189c767ec4c252f890b8a77
  languageName: node
  linkType: hard

"test-exclude@npm:^7.0.1":
  version: 7.0.1
  resolution: "test-exclude@npm:7.0.1"
  dependencies:
    "@istanbuljs/schema": "npm:^0.1.2"
    glob: "npm:^10.4.1"
    minimatch: "npm:^9.0.4"
  checksum: 10c0/6d67b9af4336a2e12b26a68c83308c7863534c65f27ed4ff7068a56f5a58f7ac703e8fc80f698a19bb154fd8f705cdf7ec347d9512b2c522c737269507e7b263
  languageName: node
  linkType: hard

"text-decoder@npm:^1.1.0":
  version: 1.2.3
  resolution: "text-decoder@npm:1.2.3"
  dependencies:
    b4a: "npm:^1.6.4"
  checksum: 10c0/569d776b9250158681c83656ef2c3e0a5d5c660c27ca69f87eedef921749a4fbf02095e5f9a0f862a25cf35258379b06e31dee9c125c9f72e273b7ca1a6d1977
  languageName: node
  linkType: hard

"text-segmentation@npm:^1.0.3":
  version: 1.0.3
  resolution: "text-segmentation@npm:1.0.3"
  dependencies:
    utrie: "npm:^1.0.2"
  checksum: 10c0/8b9ae8524e3a332371060d0ca62f10ad49a13e954719ea689a6c3a8b8c15c8a56365ede2bb91c322fb0d44b6533785f0da603e066b7554d052999967fb72d600
  languageName: node
  linkType: hard

"through2@npm:^2.0.1":
  version: 2.0.5
  resolution: "through2@npm:2.0.5"
  dependencies:
    readable-stream: "npm:~2.3.6"
    xtend: "npm:~4.0.1"
  checksum: 10c0/cbfe5b57943fa12b4f8c043658c2a00476216d79c014895cef1ac7a1d9a8b31f6b438d0e53eecbb81054b93128324a82ecd59ec1a4f91f01f7ac113dcb14eade
  languageName: node
  linkType: hard

"through2@npm:^4.0.0":
  version: 4.0.2
  resolution: "through2@npm:4.0.2"
  dependencies:
    readable-stream: "npm:3"
  checksum: 10c0/3741564ae99990a4a79097fe7a4152c22348adc4faf2df9199a07a66c81ed2011da39f631e479fdc56483996a9d34a037ad64e76d79f18c782ab178ea9b6778c
  languageName: node
  linkType: hard

"tinybench@npm:^2.9.0":
  version: 2.9.0
  resolution: "tinybench@npm:2.9.0"
  checksum: 10c0/c3500b0f60d2eb8db65250afe750b66d51623057ee88720b7f064894a6cb7eb93360ca824a60a31ab16dab30c7b1f06efe0795b352e37914a9d4bad86386a20c
  languageName: node
  linkType: hard

"tinyexec@npm:^0.3.2":
  version: 0.3.2
  resolution: "tinyexec@npm:0.3.2"
  checksum: 10c0/3efbf791a911be0bf0821eab37a3445c2ba07acc1522b1fa84ae1e55f10425076f1290f680286345ed919549ad67527d07281f1c19d584df3b74326909eb1f90
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.13, tinyglobby@npm:^0.2.14":
  version: 0.2.14
  resolution: "tinyglobby@npm:0.2.14"
  dependencies:
    fdir: "npm:^6.4.4"
    picomatch: "npm:^4.0.2"
  checksum: 10c0/f789ed6c924287a9b7d3612056ed0cda67306cd2c80c249fd280cf1504742b12583a2089b61f4abbd24605f390809017240e250241f09938054c9b363e51c0a6
  languageName: node
  linkType: hard

"tinypool@npm:^1.1.0":
  version: 1.1.1
  resolution: "tinypool@npm:1.1.1"
  checksum: 10c0/bf26727d01443061b04fa863f571016950888ea994ba0cd8cba3a1c51e2458d84574341ab8dbc3664f1c3ab20885c8cf9ff1cc4b18201f04c2cde7d317fff69b
  languageName: node
  linkType: hard

"tinyrainbow@npm:^2.0.0":
  version: 2.0.0
  resolution: "tinyrainbow@npm:2.0.0"
  checksum: 10c0/c83c52bef4e0ae7fb8ec6a722f70b5b6fa8d8be1c85792e829f56c0e1be94ab70b293c032dc5048d4d37cfe678f1f5babb04bdc65fd123098800148ca989184f
  languageName: node
  linkType: hard

"tinyspy@npm:^4.0.3":
  version: 4.0.3
  resolution: "tinyspy@npm:4.0.3"
  checksum: 10c0/0a92a18b5350945cc8a1da3a22c9ad9f4e2945df80aaa0c43e1b3a3cfb64d8501e607ebf0305e048e3c3d3e0e7f8eb10cea27dc17c21effb73e66c4a3be36373
  languageName: node
  linkType: hard

"tldts-core@npm:^6.1.86":
  version: 6.1.86
  resolution: "tldts-core@npm:6.1.86"
  checksum: 10c0/8133c29375f3f99f88fce5f4d62f6ecb9532b106f31e5423b27c1eb1b6e711bd41875184a456819ceaed5c8b94f43911b1ad57e25c6eb86e1fc201228ff7e2af
  languageName: node
  linkType: hard

"tldts@npm:^6.1.32":
  version: 6.1.86
  resolution: "tldts@npm:6.1.86"
  dependencies:
    tldts-core: "npm:^6.1.86"
  bin:
    tldts: bin/cli.js
  checksum: 10c0/27ae7526d9d78cb97b2de3f4d102e0b4321d1ccff0648a7bb0e039ed54acbce86bacdcd9cd3c14310e519b457854e7bafbef1f529f58a1e217a737ced63f0940
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10c0/487988b0a19c654ff3e1961b87f471702e708fa8a8dd02a298ef16da7206692e8552a0250e8b3e8759270f62e9d8314616f6da274734d3b558b1fc7b7724e892
  languageName: node
  linkType: hard

"to-through@npm:^3.0.0":
  version: 3.0.0
  resolution: "to-through@npm:3.0.0"
  dependencies:
    streamx: "npm:^2.12.5"
  checksum: 10c0/9b1a6eb85ceff159db21678b7d9aec1d8b99a63dae01ce95b074df1f37f9d92e3ed7d5284f394917a079dda37d53f8eeef8fc74ef506b97cc35629925f29b464
  languageName: node
  linkType: hard

"toggle-selection@npm:^1.0.6":
  version: 1.0.6
  resolution: "toggle-selection@npm:1.0.6"
  checksum: 10c0/f2cf1f2c70f374fd87b0cdc8007453ba9e981c4305a8bf4eac10a30e62ecdfd28bca7d18f8f15b15a506bf8a7bfb20dbe3539f0fcf2a2c8396c1a78d53e1f179
  languageName: node
  linkType: hard

"topojson-client@npm:^3.1.0":
  version: 3.1.0
  resolution: "topojson-client@npm:3.1.0"
  dependencies:
    commander: "npm:2"
  bin:
    topo2geo: bin/topo2geo
    topomerge: bin/topomerge
    topoquantize: bin/topoquantize
  checksum: 10c0/da2acba268cbf4d002483d5d81452e0d797b2fff6041fafb1d420e58973fa780a6f42041ce4c2677376ab977e5e1732b89c42a2db3c334a34f6c47f4d94b3eaa
  languageName: node
  linkType: hard

"totalist@npm:^3.0.0":
  version: 3.0.1
  resolution: "totalist@npm:3.0.1"
  checksum: 10c0/4bb1fadb69c3edbef91c73ebef9d25b33bbf69afe1e37ce544d5f7d13854cda15e47132f3e0dc4cafe300ddb8578c77c50a65004d8b6e97e77934a69aa924863
  languageName: node
  linkType: hard

"tough-cookie@npm:^5.1.1":
  version: 5.1.2
  resolution: "tough-cookie@npm:5.1.2"
  dependencies:
    tldts: "npm:^6.1.32"
  checksum: 10c0/5f95023a47de0f30a902bba951664b359725597d8adeabc66a0b93a931c3af801e1e697dae4b8c21a012056c0ea88bd2bf4dfe66b2adcf8e2f42cd9796fe0626
  languageName: node
  linkType: hard

"tr46@npm:^5.1.0":
  version: 5.1.1
  resolution: "tr46@npm:5.1.1"
  dependencies:
    punycode: "npm:^2.3.1"
  checksum: 10c0/ae270e194d52ec67ebd695c1a42876e0f19b96e4aca2ab464ab1d9d17dc3acd3e18764f5034c93897db73421563be27c70c98359c4501136a497e46deda5d5ec
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 10c0/047cb209a6b60c742f05c9d3ace8fa510bff609995c129a37ace03476a9b12db4dbf975e74600830ef0796e18882b2381fb5fb1f6b4f96b832c374de3ab91a11
  languageName: node
  linkType: hard

"ts-api-utils@npm:^2.1.0":
  version: 2.1.0
  resolution: "ts-api-utils@npm:2.1.0"
  peerDependencies:
    typescript: ">=4.8.4"
  checksum: 10c0/9806a38adea2db0f6aa217ccc6bc9c391ddba338a9fe3080676d0d50ed806d305bb90e8cef0276e793d28c8a929f400abb184ddd7ff83a416959c0f4d2ce754f
  languageName: node
  linkType: hard

"tslib@npm:2.3.0":
  version: 2.3.0
  resolution: "tslib@npm:2.3.0"
  checksum: 10c0/a845aed84e7e7dbb4c774582da60d7030ea39d67307250442d35c4c5dd77e4b44007098c37dd079e100029c76055f2a362734b8442ba828f8cc934f15ed9be61
  languageName: node
  linkType: hard

"tslib@npm:^2.6.2, tslib@npm:^2.8.0, tslib@npm:^2.8.1":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: 10c0/9c4759110a19c53f992d9aae23aac5ced636e99887b51b9e61def52611732872ff7668757d4e4c61f19691e36f4da981cd9485e869b4a7408d689f6bf1f14e62
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: "npm:^1.2.1"
  checksum: 10c0/7b3fd0ed43891e2080bf0c5c504b418fbb3e5c7b9708d3d015037ba2e6323a28152ec163bcb65212741fa5d2022e3075ac3c76440dbd344c9035f818e8ecee58
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-buffer@npm:1.0.3"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 10c0/1105071756eb248774bc71646bfe45b682efcad93b55532c6ffa4518969fb6241354e4aa62af679ae83899ec296d69ef88f1f3763657cdb3a4d29321f7b83079
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-byte-length@npm:1.0.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 10c0/6ae083c6f0354f1fce18b90b243343b9982affd8d839c57bbd2c174a5d5dc71be9eb7019ffd12628a96a4815e7afa85d718d6f1e758615151d5f35df841ffb3e
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.4":
  version: 1.0.4
  resolution: "typed-array-byte-offset@npm:1.0.4"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.15"
    reflect.getprototypeof: "npm:^1.0.9"
  checksum: 10c0/3d805b050c0c33b51719ee52de17c1cd8e6a571abdf0fffb110e45e8dd87a657e8b56eee94b776b13006d3d347a0c18a730b903cf05293ab6d92e99ff8f77e53
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.7":
  version: 1.0.7
  resolution: "typed-array-length@npm:1.0.7"
  dependencies:
    call-bind: "npm:^1.0.7"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.0.1"
    is-typed-array: "npm:^1.1.13"
    possible-typed-array-names: "npm:^1.0.0"
    reflect.getprototypeof: "npm:^1.0.6"
  checksum: 10c0/e38f2ae3779584c138a2d8adfa8ecf749f494af3cd3cdafe4e688ce51418c7d2c5c88df1bd6be2bbea099c3f7cea58c02ca02ed438119e91f162a9de23f61295
  languageName: node
  linkType: hard

"typescript-eslint@npm:8.34.0":
  version: 8.34.0
  resolution: "typescript-eslint@npm:8.34.0"
  dependencies:
    "@typescript-eslint/eslint-plugin": "npm:8.34.0"
    "@typescript-eslint/parser": "npm:8.34.0"
    "@typescript-eslint/utils": "npm:8.34.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/20c748b714267836bf47b9ed71b02ab256083d889528857732d559bf85ba4924c60623eb158fe0f5704bb75d9f20fbb54bc79b4cb978883093c6071a484fc390
  languageName: node
  linkType: hard

"typescript@npm:5.8.3, typescript@npm:^5.0.4":
  version: 5.8.3
  resolution: "typescript@npm:5.8.3"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/5f8bb01196e542e64d44db3d16ee0e4063ce4f3e3966df6005f2588e86d91c03e1fb131c2581baf0fb65ee79669eea6e161cd448178986587e9f6844446dbb48
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A5.8.3#optional!builtin<compat/typescript>, typescript@patch:typescript@npm%3A^5.0.4#optional!builtin<compat/typescript>":
  version: 5.8.3
  resolution: "typescript@patch:typescript@npm%3A5.8.3#optional!builtin<compat/typescript>::version=5.8.3&hash=5786d5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/39117e346ff8ebd87ae1510b3a77d5d92dae5a89bde588c747d25da5c146603a99c8ee588c7ef80faaf123d89ed46f6dbd918d534d641083177d5fac38b8a1cb
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.1.0":
  version: 1.1.0
  resolution: "unbox-primitive@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-bigints: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    which-boxed-primitive: "npm:^1.1.1"
  checksum: 10c0/7dbd35ab02b0e05fe07136c72cb9355091242455473ec15057c11430129bab38b7b3624019b8778d02a881c13de44d63cd02d122ee782fb519e1de7775b5b982
  languageName: node
  linkType: hard

"underscore.string@npm:~3.3.4":
  version: 3.3.6
  resolution: "underscore.string@npm:3.3.6"
  dependencies:
    sprintf-js: "npm:^1.1.1"
    util-deprecate: "npm:^1.0.2"
  checksum: 10c0/172319c7ef5436587e6f0fb5afe0b2e6b50f723a4070c7fb3454c5dfa5398ed3b7042c049eac2826bdd44d37cbd16b2b965d0ccc1597fde354b1d3a846fd4a39
  languageName: node
  linkType: hard

"undici-types@npm:~6.21.0":
  version: 6.21.0
  resolution: "undici-types@npm:6.21.0"
  checksum: 10c0/c01ed51829b10aa72fc3ce64b747f8e74ae9b60eafa19a7b46ef624403508a54c526ffab06a14a26b3120d055e1104d7abe7c9017e83ced038ea5cf52f8d5e04
  languageName: node
  linkType: hard

"undici@npm:^6.19.5":
  version: 6.21.2
  resolution: "undici@npm:6.21.2"
  checksum: 10c0/799bbc02b77dda9b6b12d56d2620a3a4d4cf087908d6a548acc3ce32f21b5c27467f75c2c4b30fab281daf341210be3d685e8fe99854288de541715ae5735027
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10c0/38ae681cceb1408ea0587b6b01e29b00eee3c84baee1e41fd5c16b9ed443b80fba90c40e0ba69627e30855570a34ba8b06702d4a35035d4b5e198bf5a64c9ddc
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10c0/d324c5a44887bd7e105ce800fcf7533d43f29c48757ac410afd42975de82cc38ea2035c0483f4de82d186691bf3208ef35c644f73aa2b1b20b8e651be5afd293
  languageName: node
  linkType: hard

"universalify@npm:^0.1.0":
  version: 0.1.2
  resolution: "universalify@npm:0.1.2"
  checksum: 10c0/e70e0339f6b36f34c9816f6bf9662372bd241714dc77508d231d08386d94f2c4aa1ba1318614f92015f40d45aae1b9075cd30bd490efbe39387b60a76ca3f045
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: 10c0/73e8ee3809041ca8b818efb141801a1004e3fc0002727f1531f4de613ea281b494a40909596dae4a042a4fb6cd385af5d4db2e137b1362e0e91384b828effd3a
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.1, update-browserslist-db@npm:^1.1.3":
  version: 1.1.3
  resolution: "update-browserslist-db@npm:1.1.3"
  dependencies:
    escalade: "npm:^3.2.0"
    picocolors: "npm:^1.1.1"
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 10c0/682e8ecbf9de474a626f6462aa85927936cdd256fe584c6df2508b0df9f7362c44c957e9970df55dfe44d3623807d26316ea2c7d26b80bb76a16c56c37233c32
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: 10c0/4ef57b45aa820d7ac6496e9208559986c665e49447cb072744c13b66925a362d96dd5a46c4530a6b8e203e5db5fe849369444440cb22ecfc26c679359e5dfa3c
  languageName: node
  linkType: hard

"use-sync-external-store@npm:^1.2.2":
  version: 1.4.0
  resolution: "use-sync-external-store@npm:1.4.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10c0/ec011a5055962c0f6b509d6e78c0b143f8cd069890ae370528753053c55e3b360d3648e76cfaa854faa7a59eb08d6c5fb1015e60ffde9046d32f5b2a295acea5
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:^1.0.2, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 10c0/41a5bdd214df2f6c3ecf8622745e4a366c4adced864bc3c833739791aeeeb1838119af7daed4ba36428114b5c67dcda034a79c882e97e43c03e66a4dd7389942
  languageName: node
  linkType: hard

"utrie@npm:^1.0.2":
  version: 1.0.2
  resolution: "utrie@npm:1.0.2"
  dependencies:
    base64-arraybuffer: "npm:^1.0.2"
  checksum: 10c0/eaffe645bd81a39e4bc3abb23df5895e9961dbdd49748ef3b173529e8b06ce9dd1163e9705d5309a1c61ee41ffcb825e2043bc0fd1659845ffbdf4b1515dfdb4
  languageName: node
  linkType: hard

"value-or-function@npm:^4.0.0":
  version: 4.0.0
  resolution: "value-or-function@npm:4.0.0"
  checksum: 10c0/1ac6f3ce4c2d811f9fb99a50a69df1d3960376cd1d8fa89106f746a251cb7a0bccb62199972c00beecb5f4911b7a65465b6624d198108ca90dc95cfbf1643230
  languageName: node
  linkType: hard

"vinyl-contents@npm:^2.0.0":
  version: 2.0.0
  resolution: "vinyl-contents@npm:2.0.0"
  dependencies:
    bl: "npm:^5.0.0"
    vinyl: "npm:^3.0.0"
  checksum: 10c0/b50ddf02c48fa5f89fc14bce470a375cfe74ffd6f8081836ee22f3b731e37bf1ef56761eea73377037325c79784ddc5b90000f8bddd418b87b75ea3f6320f16b
  languageName: node
  linkType: hard

"vinyl-fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "vinyl-fs@npm:4.0.0"
  dependencies:
    fs-mkdirp-stream: "npm:^2.0.1"
    glob-stream: "npm:^8.0.0"
    graceful-fs: "npm:^4.2.11"
    iconv-lite: "npm:^0.6.3"
    is-valid-glob: "npm:^1.0.0"
    lead: "npm:^4.0.0"
    normalize-path: "npm:3.0.0"
    resolve-options: "npm:^2.0.0"
    stream-composer: "npm:^1.0.2"
    streamx: "npm:^2.14.0"
    to-through: "npm:^3.0.0"
    value-or-function: "npm:^4.0.0"
    vinyl: "npm:^3.0.0"
    vinyl-sourcemap: "npm:^2.0.0"
  checksum: 10c0/c68a86ec8b48e60c5b31a881d2f39cebfe104518a1895f38ba658cfd890673a6cfa5d55de58eb15264fd2333e5b26099989e1de4e14aa6a8ad0f04529ff46a34
  languageName: node
  linkType: hard

"vinyl-sourcemap@npm:^2.0.0":
  version: 2.0.0
  resolution: "vinyl-sourcemap@npm:2.0.0"
  dependencies:
    convert-source-map: "npm:^2.0.0"
    graceful-fs: "npm:^4.2.10"
    now-and-later: "npm:^3.0.0"
    streamx: "npm:^2.12.5"
    vinyl: "npm:^3.0.0"
    vinyl-contents: "npm:^2.0.0"
  checksum: 10c0/073f3f7dac1fcbf75a5ef22dac1ad18a6a299a761ff1b897455177403141935a1a909fec4540434e5b6344f9d25b962efe49fce5e82fd9e3219d4865e7688e9a
  languageName: node
  linkType: hard

"vinyl@npm:^3.0.0":
  version: 3.0.0
  resolution: "vinyl@npm:3.0.0"
  dependencies:
    clone: "npm:^2.1.2"
    clone-stats: "npm:^1.0.0"
    remove-trailing-separator: "npm:^1.1.0"
    replace-ext: "npm:^2.0.0"
    teex: "npm:^1.0.1"
  checksum: 10c0/566ab41a84c1a167023a07cb94110d40a07835a5239297e3266a11b0e9c522969a1e623ec2a4f9b05ac7fef596cf47685b410359e5b2ca00bd77400657808816
  languageName: node
  linkType: hard

"vite-node@npm:3.2.0":
  version: 3.2.0
  resolution: "vite-node@npm:3.2.0"
  dependencies:
    cac: "npm:^6.7.14"
    debug: "npm:^4.4.1"
    es-module-lexer: "npm:^1.7.0"
    pathe: "npm:^2.0.3"
    vite: "npm:^5.0.0 || ^6.0.0 || ^7.0.0-0"
  bin:
    vite-node: vite-node.mjs
  checksum: 10c0/1df5577190f154b65cedaee396ec1306cbba1714240a91f53024fcbba045efb8e11f2748cddd18216b627a58190070756f7fc118d00a899d24e7c6921c41caca
  languageName: node
  linkType: hard

"vite-plugin-single-spa@npm:1.0.0":
  version: 1.0.0
  resolution: "vite-plugin-single-spa@npm:1.0.0"
  peerDependencies:
    vite: ^5.0.0 || ^6.0.0
  checksum: 10c0/adcea9efee581e1bb4863346d895b7c4db9bcf09b61c746e5dfa68468b164435ae75e07db1624aa4cbf4af76207867d72861a6723927e81c6d5c0a5085f0cb04
  languageName: node
  linkType: hard

"vite@npm:6.3.5":
  version: 6.3.5
  resolution: "vite@npm:6.3.5"
  dependencies:
    esbuild: "npm:^0.25.0"
    fdir: "npm:^6.4.4"
    fsevents: "npm:~2.3.3"
    picomatch: "npm:^4.0.2"
    postcss: "npm:^8.5.3"
    rollup: "npm:^4.34.9"
    tinyglobby: "npm:^0.2.13"
  peerDependencies:
    "@types/node": ^18.0.0 || ^20.0.0 || >=22.0.0
    jiti: ">=1.21.0"
    less: "*"
    lightningcss: ^1.21.0
    sass: "*"
    sass-embedded: "*"
    stylus: "*"
    sugarss: "*"
    terser: ^5.16.0
    tsx: ^4.8.1
    yaml: ^2.4.2
  dependenciesMeta:
    fsevents:
      optional: true
  peerDependenciesMeta:
    "@types/node":
      optional: true
    jiti:
      optional: true
    less:
      optional: true
    lightningcss:
      optional: true
    sass:
      optional: true
    sass-embedded:
      optional: true
    stylus:
      optional: true
    sugarss:
      optional: true
    terser:
      optional: true
    tsx:
      optional: true
    yaml:
      optional: true
  bin:
    vite: bin/vite.js
  checksum: 10c0/df70201659085133abffc6b88dcdb8a57ef35f742a01311fc56a4cfcda6a404202860729cc65a2c401a724f6e25f9ab40ce4339ed4946f550541531ced6fe41c
  languageName: node
  linkType: hard

"vite@npm:^5.0.0 || ^6.0.0 || ^7.0.0-0":
  version: 7.0.0-beta.2
  resolution: "vite@npm:7.0.0-beta.2"
  dependencies:
    esbuild: "npm:^0.25.0"
    fdir: "npm:^6.4.6"
    fsevents: "npm:~2.3.3"
    picomatch: "npm:^4.0.2"
    postcss: "npm:^8.5.5"
    rollup: "npm:^4.40.0"
    tinyglobby: "npm:^0.2.14"
  peerDependencies:
    "@types/node": ^20.19.0 || >=22.12.0
    jiti: ">=1.21.0"
    less: ^4.0.0
    lightningcss: ^1.21.0
    sass: ^1.70.0
    sass-embedded: ^1.70.0
    stylus: ">=0.54.8"
    sugarss: ^5.0.0
    terser: ^5.16.0
    tsx: ^4.8.1
    yaml: ^2.4.2
  dependenciesMeta:
    fsevents:
      optional: true
  peerDependenciesMeta:
    "@types/node":
      optional: true
    jiti:
      optional: true
    less:
      optional: true
    lightningcss:
      optional: true
    sass:
      optional: true
    sass-embedded:
      optional: true
    stylus:
      optional: true
    sugarss:
      optional: true
    terser:
      optional: true
    tsx:
      optional: true
    yaml:
      optional: true
  bin:
    vite: bin/vite.js
  checksum: 10c0/2cd51b6749171e255e478329c3f5c6fad9a3de0f36731396d1bebd77411bd93b5d835d0d8549fe707ddf5fb0e0c2f562e29b04d095d73408e4d340464182d140
  languageName: node
  linkType: hard

"vitest@npm:3.2.0":
  version: 3.2.0
  resolution: "vitest@npm:3.2.0"
  dependencies:
    "@types/chai": "npm:^5.2.2"
    "@vitest/expect": "npm:3.2.0"
    "@vitest/mocker": "npm:3.2.0"
    "@vitest/pretty-format": "npm:^3.2.0"
    "@vitest/runner": "npm:3.2.0"
    "@vitest/snapshot": "npm:3.2.0"
    "@vitest/spy": "npm:3.2.0"
    "@vitest/utils": "npm:3.2.0"
    chai: "npm:^5.2.0"
    debug: "npm:^4.4.1"
    expect-type: "npm:^1.2.1"
    magic-string: "npm:^0.30.17"
    pathe: "npm:^2.0.3"
    picomatch: "npm:^4.0.2"
    std-env: "npm:^3.9.0"
    tinybench: "npm:^2.9.0"
    tinyexec: "npm:^0.3.2"
    tinyglobby: "npm:^0.2.14"
    tinypool: "npm:^1.1.0"
    tinyrainbow: "npm:^2.0.0"
    vite: "npm:^5.0.0 || ^6.0.0 || ^7.0.0-0"
    vite-node: "npm:3.2.0"
    why-is-node-running: "npm:^2.3.0"
  peerDependencies:
    "@edge-runtime/vm": "*"
    "@types/debug": ^4.1.12
    "@types/node": ^18.0.0 || ^20.0.0 || >=22.0.0
    "@vitest/browser": 3.2.0
    "@vitest/ui": 3.2.0
    happy-dom: "*"
    jsdom: "*"
  peerDependenciesMeta:
    "@edge-runtime/vm":
      optional: true
    "@types/debug":
      optional: true
    "@types/node":
      optional: true
    "@vitest/browser":
      optional: true
    "@vitest/ui":
      optional: true
    happy-dom:
      optional: true
    jsdom:
      optional: true
  bin:
    vitest: vitest.mjs
  checksum: 10c0/ac9d0e7b37926d813244b3d597e1f367f3dea2dd8a4d5a641d569236fd92f420a8ef6afb7e03495c5b4b8a51b8782bba64d7bbccd7611899d65525b506c389e8
  languageName: node
  linkType: hard

"void-elements@npm:3.1.0":
  version: 3.1.0
  resolution: "void-elements@npm:3.1.0"
  checksum: 10c0/0b8686f9f9aa44012e9bd5eabf287ae0cde409b9a2854c5a2335cb83920c957668ac5876e3f0d158dd424744ac411a7270e64128556b451ed3bec875ef18534d
  languageName: node
  linkType: hard

"w3c-xmlserializer@npm:^5.0.0":
  version: 5.0.0
  resolution: "w3c-xmlserializer@npm:5.0.0"
  dependencies:
    xml-name-validator: "npm:^5.0.0"
  checksum: 10c0/8712774c1aeb62dec22928bf1cdfd11426c2c9383a1a63f2bcae18db87ca574165a0fbe96b312b73652149167ac6c7f4cf5409f2eb101d9c805efe0e4bae798b
  languageName: node
  linkType: hard

"walk-sync@npm:^2.2.0":
  version: 2.2.0
  resolution: "walk-sync@npm:2.2.0"
  dependencies:
    "@types/minimatch": "npm:^3.0.3"
    ensure-posix-path: "npm:^1.1.0"
    matcher-collection: "npm:^2.0.0"
    minimatch: "npm:^3.0.4"
  checksum: 10c0/45fe284ffa28440f0d3d0a136b3c3fe2a0f55bf207db22c481eea9e7ab7cef6d820491485d76e9f1af9dab7489c6d7a0efbd1ebf45b43dbf871f046f0b4760bd
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: 10c0/5612d5f3e54760a797052eb4927f0ddc01383550f542ccd33d5238cfd65aeed392a45ad38364970d0a0f4fea32e1f4d231b3d8dac4a3bdd385e5cf802ae097db
  languageName: node
  linkType: hard

"webidl-conversions@npm:^7.0.0":
  version: 7.0.0
  resolution: "webidl-conversions@npm:7.0.0"
  checksum: 10c0/228d8cb6d270c23b0720cb2d95c579202db3aaf8f633b4e9dd94ec2000a04e7e6e43b76a94509cdb30479bd00ae253ab2371a2da9f81446cc313f89a4213a2c4
  languageName: node
  linkType: hard

"whatwg-encoding@npm:^3.1.1":
  version: 3.1.1
  resolution: "whatwg-encoding@npm:3.1.1"
  dependencies:
    iconv-lite: "npm:0.6.3"
  checksum: 10c0/273b5f441c2f7fda3368a496c3009edbaa5e43b71b09728f90425e7f487e5cef9eb2b846a31bd760dd8077739c26faf6b5ca43a5f24033172b003b72cf61a93e
  languageName: node
  linkType: hard

"whatwg-mimetype@npm:^4.0.0":
  version: 4.0.0
  resolution: "whatwg-mimetype@npm:4.0.0"
  checksum: 10c0/a773cdc8126b514d790bdae7052e8bf242970cebd84af62fb2f35a33411e78e981f6c0ab9ed1fe6ec5071b09d5340ac9178e05b52d35a9c4bcf558ba1b1551df
  languageName: node
  linkType: hard

"whatwg-url@npm:^14.0.0, whatwg-url@npm:^14.1.1":
  version: 14.2.0
  resolution: "whatwg-url@npm:14.2.0"
  dependencies:
    tr46: "npm:^5.1.0"
    webidl-conversions: "npm:^7.0.0"
  checksum: 10c0/f746fc2f4c906607d09537de1227b13f9494c171141e5427ed7d2c0dd0b6a48b43d8e71abaae57d368d0c06b673fd8ec63550b32ad5ed64990c7b0266c2b4272
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: "npm:~0.0.3"
    webidl-conversions: "npm:^3.0.0"
  checksum: 10c0/1588bed84d10b72d5eec1d0faa0722ba1962f1821e7539c535558fb5398d223b0c50d8acab950b8c488b4ba69043fd833cc2697056b167d8ad46fac3995a55d5
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.1.0, which-boxed-primitive@npm:^1.1.1":
  version: 1.1.1
  resolution: "which-boxed-primitive@npm:1.1.1"
  dependencies:
    is-bigint: "npm:^1.1.0"
    is-boolean-object: "npm:^1.2.1"
    is-number-object: "npm:^1.1.1"
    is-string: "npm:^1.1.1"
    is-symbol: "npm:^1.1.1"
  checksum: 10c0/aceea8ede3b08dede7dce168f3883323f7c62272b49801716e8332ff750e7ae59a511ae088840bc6874f16c1b7fd296c05c949b0e5b357bfe3c431b98c417abe
  languageName: node
  linkType: hard

"which-builtin-type@npm:^1.2.1":
  version: 1.2.1
  resolution: "which-builtin-type@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    function.prototype.name: "npm:^1.1.6"
    has-tostringtag: "npm:^1.0.2"
    is-async-function: "npm:^2.0.0"
    is-date-object: "npm:^1.1.0"
    is-finalizationregistry: "npm:^1.1.0"
    is-generator-function: "npm:^1.0.10"
    is-regex: "npm:^1.2.1"
    is-weakref: "npm:^1.0.2"
    isarray: "npm:^2.0.5"
    which-boxed-primitive: "npm:^1.1.0"
    which-collection: "npm:^1.0.2"
    which-typed-array: "npm:^1.1.16"
  checksum: 10c0/8dcf323c45e5c27887800df42fbe0431d0b66b1163849bb7d46b5a730ad6a96ee8bfe827d078303f825537844ebf20c02459de41239a0a9805e2fcb3cae0d471
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-collection@npm:1.0.2"
  dependencies:
    is-map: "npm:^2.0.3"
    is-set: "npm:^2.0.3"
    is-weakmap: "npm:^2.0.2"
    is-weakset: "npm:^2.0.3"
  checksum: 10c0/3345fde20964525a04cdf7c4a96821f85f0cc198f1b2ecb4576e08096746d129eb133571998fe121c77782ac8f21cbd67745a3d35ce100d26d4e684c142ea1f2
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.16, which-typed-array@npm:^1.1.18":
  version: 1.1.19
  resolution: "which-typed-array@npm:1.1.19"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    for-each: "npm:^0.3.5"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/702b5dc878addafe6c6300c3d0af5983b175c75fcb4f2a72dfc3dd38d93cf9e89581e4b29c854b16ea37e50a7d7fca5ae42ece5c273d8060dcd603b2404bbb3f
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10c0/66522872a768b60c2a65a57e8ad184e5372f5b6a9ca6d5f033d4b0dc98aff63995655a7503b9c0a2598936f532120e81dd8cc155e2e92ed662a2b9377cc4374f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10c0/e556e4cd8b7dbf5df52408c9a9dd5ac6518c8c5267c8953f5b0564073c66ed5bf9503b14d876d0e9c7844d4db9725fb0dcf45d6e911e17e26ab363dc3965ae7b
  languageName: node
  linkType: hard

"why-is-node-running@npm:^2.3.0":
  version: 2.3.0
  resolution: "why-is-node-running@npm:2.3.0"
  dependencies:
    siginfo: "npm:^2.0.0"
    stackback: "npm:0.0.2"
  bin:
    why-is-node-running: cli.js
  checksum: 10c0/1cde0b01b827d2cf4cb11db962f3958b9175d5d9e7ac7361d1a7b0e2dc6069a263e69118bd974c4f6d0a890ef4eedfe34cf3d5167ec14203dbc9a18620537054
  languageName: node
  linkType: hard

"window-or-global@npm:^1.0.1":
  version: 1.0.1
  resolution: "window-or-global@npm:1.0.1"
  checksum: 10c0/55af1894c52d9a769444477d8153d953163d6e60f64a25958c09cbc632c00faadd14e32b101310a0eee47b17e540a66c8fc3a069d9c2ada32a5aff553c20f5c5
  languageName: node
  linkType: hard

"wmf@npm:~1.0.1":
  version: 1.0.2
  resolution: "wmf@npm:1.0.2"
  checksum: 10c0/3fa5806f382632cadfe65d4ef24f7a583b0c0720171edb00e645af5248ad0bb6784e8fcee1ccd9f475a1a12a7523e2512e9c063731fbbdae14dc469e1c033d93
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: 10c0/e0e4a1ca27599c92a6ca4c32260e8a92e8a44f4ef6ef93f803f8ed823f486e0889fc0b93be4db59c8d51b3064951d25e43d434e95dc8c960cc3a63d65d00ba20
  languageName: node
  linkType: hard

"word@npm:~0.3.0":
  version: 0.3.0
  resolution: "word@npm:0.3.0"
  checksum: 10c0/c6da2a9f7a0d81a32fa6768a638d21b153da2be04f94f3964889c7cc1365d74b6ecb43b42256c3f926cd59512d8258206991c78c21000c3da96d42ff1238b840
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/d15fc12c11e4cbc4044a552129ebc75ee3f57aa9c1958373a4db0292d72282f54373b536103987a4a7594db1ef6a4f10acf92978f79b98c49306a4b58c77d4da
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/138ff58a41d2f877eae87e3282c0630fc2789012fc1af4d6bd626eeb9a2f9a65ca92005e6e69a75c7b85a68479fe7443c7dbe1eb8fbaa681a4491364b7c55c60
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 10c0/56fece1a4018c6a6c8e28fbc88c87e0fbf4ea8fd64fc6c63b18f4acc4bd13e0ad2515189786dd2c30d3eec9663d70f4ecf699330002f8ccb547e4a18231fc9f0
  languageName: node
  linkType: hard

"ws@npm:^8.18.0":
  version: 8.18.2
  resolution: "ws@npm:8.18.2"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10c0/4b50f67931b8c6943c893f59c524f0e4905bbd183016cfb0f2b8653aa7f28dad4e456b9d99d285bbb67cca4fedd9ce90dfdfaa82b898a11414ebd66ee99141e4
  languageName: node
  linkType: hard

"xlsx@npm:^0.18.5":
  version: 0.18.5
  resolution: "xlsx@npm:0.18.5"
  dependencies:
    adler-32: "npm:~1.3.0"
    cfb: "npm:~1.2.1"
    codepage: "npm:~1.15.0"
    crc-32: "npm:~1.2.1"
    ssf: "npm:~0.11.2"
    wmf: "npm:~1.0.1"
    word: "npm:~0.3.0"
  bin:
    xlsx: bin/xlsx.njs
  checksum: 10c0/787cfa77034a3e86fdcde21572f1011c8976f87823a5e0ee5057f13b2f6e48f17a1710732a91b8ae15d7794945c7cba8a3ca904ea7150e028260b0ab8e1158c8
  languageName: node
  linkType: hard

"xml-name-validator@npm:^5.0.0":
  version: 5.0.0
  resolution: "xml-name-validator@npm:5.0.0"
  checksum: 10c0/3fcf44e7b73fb18be917fdd4ccffff3639373c7cb83f8fc35df6001fecba7942f1dbead29d91ebb8315e2f2ff786b508f0c9dc0215b6353f9983c6b7d62cb1f5
  languageName: node
  linkType: hard

"xmlchars@npm:^2.2.0":
  version: 2.2.0
  resolution: "xmlchars@npm:2.2.0"
  checksum: 10c0/b64b535861a6f310c5d9bfa10834cf49127c71922c297da9d4d1b45eeaae40bf9b4363275876088fbe2667e5db028d2cd4f8ee72eed9bede840a67d57dab7593
  languageName: node
  linkType: hard

"xtend@npm:~4.0.1":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: 10c0/366ae4783eec6100f8a02dff02ac907bf29f9a00b82ac0264b4d8b832ead18306797e283cf19de776538babfdcb2101375ec5646b59f08c52128ac4ab812ed0e
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 10c0/c66a5c46bc89af1625476f7f0f2ec3653c1a1791d2f9407cfb4c2ba812a1e1c9941416d71ba9719876530e3340a99925f697142989371b72d93b9ee628afd8c1
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10c0/2286b5e8dbfe22204ab66e2ef5cc9bbb1e55dfc873bbe0d568aa943eb255d131890dfd5bf243637273d31119b870f49c18fcde2c6ffbb7a7a092b870dc90625a
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10c0/a499c81ce6d4a1d260d4ea0f6d49ab4da09681e32c3f0472dee16667ed69d01dae63a3b81745a24bd78476ec4fcf856114cb4896ace738e01da34b2c42235416
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: 10c0/dceb44c28578b31641e13695d200d34ec4ab3966a5729814d5445b194933c096b7ced71494ce53a0e8820685d1d010df8b2422e5bf2cdea7e469d97ffbea306f
  languageName: node
  linkType: hard

"zrender@npm:5.6.1":
  version: 5.6.1
  resolution: "zrender@npm:5.6.1"
  dependencies:
    tslib: "npm:2.3.0"
  checksum: 10c0/dc1cc570054640cbd8fbb7b92e6252f225319522bfe3e8dc8bf02cc02d414e00a4c8d0a6f89bfc9d96e5e9511fdca94dd3d06bf53690df2b2f12b0fc560ac307
  languageName: node
  linkType: hard

"zustand@npm:^4.4.1":
  version: 4.5.7
  resolution: "zustand@npm:4.5.7"
  dependencies:
    use-sync-external-store: "npm:^1.2.2"
  peerDependencies:
    "@types/react": ">=16.8"
    immer: ">=9.0.6"
    react: ">=16.8"
  peerDependenciesMeta:
    "@types/react":
      optional: true
    immer:
      optional: true
    react:
      optional: true
  checksum: 10c0/55559e37a82f0c06cadc61cb08f08314c0fe05d6a93815e41e3376130c13db22a5017cbb0cd1f018c82f2dad0051afe3592561d40f980bd4082e32005e8a950c
  languageName: node
  linkType: hard

"zustand@npm:^5.0.5":
  version: 5.0.7
  resolution: "zustand@npm:5.0.7"
  peerDependencies:
    "@types/react": ">=18.0.0"
    immer: ">=9.0.6"
    react: ">=18.0.0"
    use-sync-external-store: ">=1.2.0"
  peerDependenciesMeta:
    "@types/react":
      optional: true
    immer:
      optional: true
    react:
      optional: true
    use-sync-external-store:
      optional: true
  checksum: 10c0/8582cb7fb8251f69c88c9f804b15964cc32692e9e11842cd59c6592a59f72a3141a8c794ac42eefdb6c06401d687cf5a70fc7041e54c39b25892b77a361defe4
  languageName: node
  linkType: hard
