export default {
  createOldCatalogs: false,

  keepRemoved: [
    /menu(\..+)?/
  ],

  defaultValue: (_locale, _namespace, key, value) => {
    if (!value || value === "") return key
    return value
  },

  lexers: {
    default: ['JsxLexer'],
  },

  locales: ['en', 'fr'],

  output: './public/locales/$LOCALE/translation.json',

  input: ['./src/**/*.{js,jsx,ts,tsx}'],

  sort: true,

  verbose: true,
};
