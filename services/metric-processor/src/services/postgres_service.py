"""
This module contains the PostgresService class for interacting with PostgreSQL databases.

The PostgresService class provides methods to connect to and interact with tenant-specific
PostgreSQL databases. Each tenant has its own database, and this service manages those
connections.
"""

from contextlib import contextmanager
from typing import Optional, Dict, Any, List

import psycopg2
from psycopg2 import pool
from psycopg2.extras import RealDictCursor

import structlog

logger = structlog.get_logger()


class PostgresService:
    """
    A service class to interact with PostgreSQL databases.

    Attributes:
        connection_pool (psycopg2.pool.SimpleConnectionPool): A connection pool for the database.
        tenant (str): The tenant identifier for this database connection.
    """

    def __init__(self, secret_data: Dict[str, str], tenant: str):
        """
        Initializes the PostgresService with connection details.

        Args:
            secret_data (dict): A dictionary containing PostgreSQL connection details.
            tenant (str): The tenant identifier from the message attributes.
        """
        self.tenant = tenant
        self.connection_pool = None
        self._initialize_pool(secret_data)

    def _initialize_pool(self, secret_data: Dict[str, str]):
        """
        Initializes the connection pool with the provided secret data.

        Args:
            secret_data (dict): A dictionary containing PostgreSQL connection details.
        """
        try:
            host = secret_data["host"]
            user = secret_data["username"]
            password = secret_data["password"]
            port = secret_data["port"]

            # Log connection parameters (masking password)
            logger.debug(
                "Initializing PostgreSQL connection pool",
                host=host,
                user=user,
                port=port,
                tenant=self.tenant,
            )

            # Create connection parameters
            conn_params = {
                "host": host,
                "user": user,
                "password": password,
                "port": port,
                "dbname": self.tenant,  # TODO: We will need to change this to dataset_id in the future
                "connect_timeout": 10,
            }

            # Log the actual connection attempt
            logger.debug(
                "Attempting to create connection pool",
                conn_params={**conn_params, "password": "***"},
                tenant=self.tenant,
            )

            # Optimized pool size for infrequent config loading
            # Configs are cached in memory, so database connections are rarely needed
            # We may need to increase the max connections in the future depending on how many
            # concurrent facilities and events we are processing at a given time.
            self.connection_pool = pool.SimpleConnectionPool(
                1,  # Minimum connections (config loading is infrequent)
                10,  # Maximum connections (small pool for occasional cache reloads)
                **conn_params,
            )

            # Test the connection
            with self.get_connection() as conn:
                logger.debug("Successfully connected to PostgreSQL", tenant=self.tenant)
                # Test the connection with a simple query
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")

        except Exception as e:
            logger.exception(
                "An unexpected error occurred while initializing the postgresconnection pool",
                error=str(e),
                error_type=type(e).__name__,
                tenant=self.tenant,
            )
            raise

    @contextmanager
    def get_connection(self):
        """
        Gets a connection from the pool and ensures it's returned.

        Yields:
            psycopg2.extensions.connection: A database connection from the pool.
        """
        if not self.connection_pool:
            raise ConnectionError("Connection pool not initialized")

        conn = None
        try:
            conn = self.connection_pool.getconn()
            logger.debug("Got connection from pool", tenant=self.tenant)
            yield conn
        except Exception as e:
            logger.error(
                "Error using connection",
                error=str(e),
                error_type=type(e).__name__,
                tenant=self.tenant,
            )
            raise
        finally:
            if conn:
                self.connection_pool.putconn(conn)
                logger.debug("Returned connection to pool", tenant=self.tenant)

    def is_connected(self) -> bool:
        """
        Checks if the PostgreSQL connection pool is initialized.

        Returns:
            bool: True if connected, False otherwise.
        """
        return self.connection_pool is not None

    def close(self):
        """
        Closes all connections in the pool.
        """
        if self.connection_pool:
            self.connection_pool.closeall()
            self.connection_pool = None
            logger.info("Closed all connections in pool", tenant=self.tenant)

    def execute_query(self, query: str, params: Optional[dict] = None) -> Any:
        """
        Executes a query and returns the results.

        Args:
            query (str): The SQL query to execute.
            params (dict, optional): Parameters for the query.

        Returns:
            Any: The query results as a list of dictionaries.
        """
        with self.get_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                # Format query for logging by removing extra whitespace
                formatted_query = " ".join(
                    line.strip() for line in query.split("\n") if line.strip()
                )
                logger.debug(
                    "Executing query",
                    query=formatted_query,
                    params=params,
                    tenant=self.tenant,
                )
                try:
                    cur.execute(query, params)
                    if cur.description:  # If the query returns results
                        results = cur.fetchall()
                        logger.debug(
                            "Query executed successfully",
                            row_count=len(results),
                            tenant=self.tenant,
                        )
                        return results
                    return None
                except Exception as e:
                    logger.error(
                        "Error executing query",
                        exc_info=True,
                        query=formatted_query,
                        params=params,
                        tenant=self.tenant,
                        error=str(e),
                    )
                    raise

    def get_metric_configs(
        self, tenant: str, facility_id: str, fact_type: str
    ) -> List[Dict]:
        """
        Get metric configurations for a specific fact type, combining default and custom configs.
        Selects all configs that are enabled for the facility and fact type.

        Selects based off of the following logic:
        - If a default config is enabled for a facility explicitly as true
            or the default config has not enabled value for that facility.
        - If a custom config is explicitly enabled for a facility in the custom table.
        - If a custom config is not explicitly enabled for a facility in the custom table,
            but the default config enabled value for that facility is true.

        Args:
            tenant: The tenant ID
            facility_id: The facility ID
            fact_type: The fact type

        Returns:
            List of metric configurations
        """
        try:
            logger.debug(
                "Getting metric configs from postgres",
                tenant=tenant,
                facility_id=facility_id,
                fact_type=fact_type,
            )

            query = """
                WITH custom_configs AS (
                    -- Get all enabled custom configs for this facility
                    SELECT -- We use jsonb to store the config data so that we can join between the two schemas
                        to_jsonb(c.*) as config_data,
                        c.metric_config_name
                    FROM process_flow.custom_metric_configurations c
                    WHERE c.facility_id = %(facility_id)s
                      AND c.fact_type = %(fact_type)s
                      AND c.enabled = TRUE
                ),
                default_configs AS (
                    -- Get enabled defaults when no custom exists OR custom is disabled
                    SELECT
                        to_jsonb(d.*) as config_data,
                        d.metric_config_name
                    FROM process_flow.default_metric_configurations d
                    LEFT JOIN process_flow.custom_metric_configurations c
                        ON d.metric_config_name = c.metric_config_name
                        AND c.facility_id = %(facility_id)s
                        AND c.fact_type = %(fact_type)s
                    WHERE d.fact_type = %(fact_type)s
                    -- Only get default where no custom config exists or the custom config is disabled
                      AND (c.metric_config_name IS NULL OR c.enabled IS NOT TRUE)  -- No custom OR custom disabled
                      OR d.enabled->%(facility_id)s = 'true'  -- Explicitly enabled for facility
                ),
                final_configs AS (
                    SELECT config_data FROM custom_configs
                    UNION ALL
                    SELECT config_data FROM default_configs
                )
                SELECT config_data FROM final_configs;
            """
            # Parameters: use a dictionary for named parameters
            params = {"fact_type": fact_type, "facility_id": facility_id}

            # Execute query
            results = self.execute_query(query, params)
            # Convert RealDictRow to regular dict
            # As a list we can loop through each row
            return [dict(row) for row in results] if results else []

        except Exception as e:
            logger.error(
                "Error getting metric configs",
                exc_info=True,
                tenant=tenant,
                facility_id=facility_id,
                fact_type=fact_type,
                error=str(e),
            )
            raise

    def update_metric_config_active(
        self,
        metric_config,
        facility_id: str,
        config_loader=None,
    ) -> None:
        """
        Updates the active status of a metric config in the database.

        This method first checks the cache to see if the config is already marked as active.
        If it is, the database update is skipped to avoid unnecessary writes.

        Args:
            metric_config: The metric configuration object (extracts name, is_custom, fact_type from this)
            facility_id: The facility ID where the config was used
            config_loader: ConfigLoader instance to check cache before updating database
        """
        try:
            # Extract values from metric_config object
            metric_config_name = metric_config.metric_config_name
            fact_type = metric_config.fact_type
            is_custom = getattr(metric_config, "is_custom", None)
            active = True  # Always mark as active when this method is called

            # Check cache first to avoid unnecessary database updates
            if config_loader and fact_type:
                if config_loader.is_config_active_in_cache(
                    self.tenant, facility_id, fact_type, metric_config_name
                ):
                    logger.debug(
                        "Config already active in cache, skipping database update",
                        metric_config_name=metric_config_name,
                        facility_id=facility_id,
                        fact_type=fact_type,
                        tenant=self.tenant,
                    )
                    return

            logger.debug(
                "Updating metric config active status",
                metric_config_name=metric_config_name,
                facility_id=facility_id,
                active=active,
                is_custom=is_custom,
                tenant=self.tenant,
            )

            if is_custom is True:
                # Update custom config table
                self._update_custom_config_active(
                    metric_config_name, facility_id, active
                )
            else:
                # Default to updating default config table (covers is_custom=False and None)
                self._update_default_config_active(
                    metric_config_name, facility_id, active
                )

            # Update cache to reflect the new active status
            if config_loader and fact_type:
                config_loader.mark_config_active_in_cache(
                    self.tenant, facility_id, fact_type, metric_config_name
                )

            logger.debug(
                "Successfully updated metric config active status",
                metric_config_name=metric_config_name,
                facility_id=facility_id,
                active=active,
                is_custom=is_custom,
                tenant=self.tenant,
            )

        except (psycopg2.Error, ConnectionError, ValueError) as e:
            # Log database/connection errors but don't raise - this shouldn't break metric processing
            logger.error(
                "Failed to update metric config active status",
                metric_config_name=metric_config_name,
                facility_id=facility_id,
                active=active,
                is_custom=is_custom,
                tenant=self.tenant,
                error=str(e),
                exc_info=True,
            )
        except Exception as e:
            # Log unexpected errors and re-raise - these might indicate serious issues
            logger.error(
                "Unexpected error updating metric config active status",
                metric_config_name=metric_config_name,
                facility_id=facility_id,
                active=active,
                is_custom=is_custom,
                tenant=self.tenant,
                error=str(e),
                exc_info=True,
            )
            raise

    def _update_custom_config_active(
        self, metric_config_name: str, facility_id: str, active: bool
    ) -> None:
        """Updates active status for custom metric configurations."""
        query = """
            UPDATE process_flow.custom_metric_configurations
            SET
                active = %(active)s,
                updated_at = NOW()
            WHERE metric_config_name = %(metric_config_name)s
              AND facility_id = %(facility_id)s
        """

        params = {
            "metric_config_name": metric_config_name,
            "facility_id": facility_id,
            "active": active,
        }

        self.execute_query(query, params)

    def _update_default_config_active(
        self, metric_config_name: str, facility_id: str, active: bool
    ) -> None:
        """Updates active status for default metric configurations using hstore."""
        # For default configs, we update the hstore 'active' field
        query = """
            UPDATE process_flow.default_metric_configurations
            SET
                active = COALESCE(active, ''::hstore) || hstore(%(facility_id)s, %(active_str)s),
                updated_at = NOW()
            WHERE metric_config_name = %(metric_config_name)s
        """

        params = {
            "metric_config_name": metric_config_name,
            "facility_id": facility_id,
            "active_str": str(active).lower(),  # Convert boolean to string for hstore
        }

        self.execute_query(query, params)
