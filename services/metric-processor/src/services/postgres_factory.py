"""
This module contains the PostgresFactory class for managing PostgreSQL service instances.

The PostgresFactory class provides methods to create and manage PostgresService instances
for different tenants, handling connection pooling and secret management.
"""

import os
import json
from pathlib import Path

import structlog
from dotenv import load_dotenv
from google.cloud import secretmanager

from .postgres_service import PostgresService

# Load environment variables from the correct .env file
env_path = (
    Path(__file__).parents[2] / ".env"
)  # Go up two directories to find .env in services/metric-processor/
load_dotenv(dotenv_path=env_path)


logging = structlog.get_logger()


class PostgresFactory:
    """
    A factory class to create and manage PostgresService instances for different tenants.
    """

    _instances = {}

    @classmethod
    def get_instance(cls, tenant: str) -> PostgresService:
        """
        Gets or creates a PostgresService instance for the specified tenant.

        Args:
            tenant (str): The tenant identifier.

        Returns:
            PostgresService: A PostgresService instance for the tenant.
        """
        if tenant not in cls._instances:
            secret_data = cls._fetch_secret()
            if secret_data:
                logging.debug(
                    "Creating PostgreSQL connection for tenant",
                    tenant=tenant,
                    host=secret_data.get("host"),
                    port=secret_data.get("port"),
                )
                instance = PostgresService(secret_data, tenant)
                if instance.is_connected():
                    cls._instances[tenant] = instance
        return cls._instances.get(tenant)

    @classmethod
    def _fetch_secret(cls) -> dict:
        """
        Fetches PostgreSQL connection secrets from environment variables.
        For local development, uses POSTGRES_ prefixed environment variables.
        For cloud deployment, uses the GCP_POSTGRES_SECRET_NAME environment variable
        which contains the pre-fetched secret JSON.

        Returns:
            dict: A dictionary containing PostgreSQL connection details with normalized keys:
                - 'host': The host for the PostgreSQL database.
                - 'username': The username for PostgreSQL authentication.
                - 'password': The password for PostgreSQL authentication.
                - 'port': The port for the PostgreSQL database.
        """
        if os.getenv("LOCALDEV", "false").lower() == "true":
            # Use environment variables for local development
            return {
                "host": os.getenv("POSTGRES_HOST"),
                "username": os.getenv("POSTGRES_USER"),
                "password": os.getenv("POSTGRES_PASSWORD"),
                "port": os.getenv("POSTGRES_PORT"),
            }

        try:
            secret_json = os.getenv("GCP_POSTGRES_SECRET_NAME")
            if not secret_json:
                raise ValueError(
                    "GCP_POSTGRES_SECRET_NAME environment variable must be set"
                )

            return json.loads(secret_json)
        except json.JSONDecodeError:
            logging.exception(
                "Failed to decode JSON from GCP_POSTGRES_SECRET_NAME environment variable"
            )
        except ValueError as e:
            logging.exception(f"Invalid secret configuration: {e}")
        except Exception as e:
            logging.exception(
                f"Unexpected error reading secret from environment variable: {e}"
            )
        return None

    @classmethod
    def close_instance(cls, tenant: str):
        """
        Closes and removes a PostgresService instance for the specified tenant.

        Args:
            tenant (str): The tenant identifier.
        """
        if tenant in cls._instances:
            cls._instances[tenant].close()
            del cls._instances[tenant]

    @classmethod
    def clear_cache(cls):
        """
        Closes and removes all PostgresService instances.
        """
        for tenant in list(cls._instances.keys()):
            cls.close_instance(tenant)
