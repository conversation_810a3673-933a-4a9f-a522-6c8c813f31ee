"""
This module handles loading and managing metric configurations from Postgres and memory.
It provides functionality to load and manage configs per tenant/facility/fact_type.
It is implemented as a singleton to ensure config caching is shared across all requests
within a CloudRun instance.
"""

from typing import Dict

import psycopg2
import structlog
from pydantic import ValidationError

from src.schemas import config_schema
from src.services.postgres_factory import PostgresFactory

logger = structlog.get_logger(__name__)


class ConfigLoader:
    """
    Handles loading and managing metric configurations.

    This class is responsible for:
    1. Loading configs from Postgres for each tenant/facility/fact_type
    2. Managing configs in memory
    3. Handling config validation

    This is implemented as a singleton to ensure config caching is shared across all requests
    within a CloudRun instance.
    """

    # =============== Singleton Implementation ===============
    _instance = None
    _initialized = False

    def __new__(cls):
        """
        Returns the singleton instance of the ConfigLoader class,
        creating it if it doesn't exist.
        """
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """
        Initialize the ConfigLoader class.
        Sets up the configs dictionary on first initialization only.
        """
        if not self._initialized:
            # Structure: configs[tenant_id][facility_id][fact_type][metric_config_name] = config
            self.configs = {}
            ConfigLoader._initialized = True

    @classmethod
    def reset(cls) -> None:
        """
        Reset the singleton instance.
        This should only be used in testing or when absolutely necessary.
        """
        cls._instance = None
        cls._initialized = False

    # =============== Public Interface ===============

    def get_config_by_fact_type(
        self,
        tenant: str,
        facility: str,
        fact_type: str,
        clear_fact_type_metric_config_cache: bool = False,
    ) -> Dict[str, config_schema.MetricConfig]:
        """
        Get metric configurations for a specific fact type, using in-memory cache if available.

        This method implements a three-level caching strategy:
        1. **Cache Check**: First checks if configs are already cached for the specific
            tenant/facility/fact_type combination.
        2. **Cache Hit**: Returns cached configs immediately (no database query).
        3. **Cache Miss**: Queries Postgres and stores results in cache for future requests.

        **Cache Structure**: Configs are cached per tenant/facility/fact_type combination.
        This means that even if the same tenant and fact_type are requested for different
        facilities, each facility will have its own cache entry and may trigger
        separate Postgres queries.

        **Cache Key**: configs[tenant][facility][fact_type] = {metric_config_name: MetricConfig}

        Args:
            tenant: The tenant identifier
            facility: The facility identifier
            fact_type: The type of fact to process
            clear_fact_type_metric_config_cache: If True, clears the cache for this specific
            combination before loading.
            This is useful when the config for a fact type has changed and we want to reload
            the configs.

        Returns:
            Dictionary mapping metric_config_name to MetricConfig objects

        Raises:
            ConnectionError: If database connection fails
            ValueError/KeyError: If configuration data is invalid
            ValidationError: If config validation fails
        """
        # If clear_fact_type_metric_config_cache is True, clear the fact type metric config cache
        if clear_fact_type_metric_config_cache:
            self.clear_cache_by_fact_type(tenant, facility, fact_type)

        # Check in-memory cache first
        cached_config = self._get_cached_config(tenant, facility, fact_type)
        if cached_config is not None:
            logger.debug(
                f"Cache HIT for fact type {fact_type} in {tenant}/{facility}",
                tenant=tenant,
                facility=facility,
                fact_type=fact_type,
                config_count=len(cached_config),
            )
            return cached_config

        logger.debug(
            f"Cache MISS for fact type {fact_type} in {tenant}/{facility}",
            tenant=tenant,
            facility=facility,
            fact_type=fact_type,
        )

        # Else load from Postgres
        return self._load_config_from_postgres(tenant, facility, fact_type)

    def clear_cache(self) -> None:
        """
        Clear the entire config cache in runtime.
        This should be called when configs need to be reloaded.
        """
        self.configs = {}
        logger.info("Config cache cleared")

    def clear_cache_by_facility(self, tenant: str, facility: str) -> None:
        """
        Clear the config cache for all fact types in a specific facility.
        """
        if tenant in self.configs and facility in self.configs[tenant]:
            del self.configs[tenant][facility]
            logger.info(
                "Config cache cleared for facility",
                tenant=tenant,
                facility=facility,
            )
        else:
            logger.warning(
                "Config cache not found for facility",
                tenant=tenant,
                facility=facility,
            )

    def clear_cache_by_fact_type(
        self, tenant: str, facility: str, fact_type: str
    ) -> None:
        """
        Clear the config cache for a specific fact type in a specific facility.
        """
        if (
            tenant in self.configs
            and facility in self.configs[tenant]
            and fact_type in self.configs[tenant][facility]
        ):
            del self.configs[tenant][facility][fact_type]
            logger.info(
                "Config cache cleared for fact type",
                tenant=tenant,
                facility=facility,
                fact_type=fact_type,
            )
        else:
            logger.warning(
                "Config cache not found for fact type",
                tenant=tenant,
                facility=facility,
                fact_type=fact_type,
            )

    # =============== Private Implementation ===============

    def _load_config_from_postgres(
        self, tenant: str, facility: str, fact_type: str
    ) -> Dict[str, config_schema.MetricConfig]:
        """Load configuration from Postgres and cache it."""
        logger.debug(
            f"Loading metric configs for fact type {fact_type} "
            f"from Postgres for {tenant}/{facility}",
            tenant=tenant,
            facility=facility,
            fact_type=fact_type,
        )

        # Get the Postgres service instance for the tenant
        postgres_service = PostgresFactory.get_instance(tenant)
        if not postgres_service:
            error_msg = (
                f"Failed to get Postgres service for {tenant}/{facility}/{fact_type}"
            )
            logger.error(
                error_msg,
                tenant=tenant,
                facility=facility,
                fact_type=fact_type,
            )
            raise ConnectionError(error_msg)

        try:
            metric_config_rows = postgres_service.get_metric_configs(
                tenant=tenant,
                facility_id=facility,
                fact_type=fact_type,
            )

            # If no metric configs are found, log a warning and cache empty result
            if not metric_config_rows:
                logger.warning(
                    f"No metric configs found for fact type {fact_type} "
                    f"in Postgres {tenant}/{facility}",
                    tenant=tenant,
                    facility=facility,
                    fact_type=fact_type,
                )
                # Cache the empty result to prevent repeated database queries
                empty_result = {}
                self.configs.setdefault(tenant, {}).setdefault(facility, {})[
                    fact_type
                ] = empty_result
                logger.debug(
                    f"Cached EMPTY result for fact type {fact_type} in {tenant}/{facility}",
                    tenant=tenant,
                    facility=facility,
                    fact_type=fact_type,
                )
                return empty_result

            # Create the fact_type metrics dict from the metric config rows
            fact_type_metrics_dict = self._create_fact_type_metrics_dict_from_rows(
                metric_config_rows, tenant, facility, fact_type
            )

            # Store in facilitycache
            self.configs.setdefault(tenant, {}).setdefault(facility, {})[
                fact_type
            ] = fact_type_metrics_dict

            return fact_type_metrics_dict

        except (psycopg2.Error, psycopg2.OperationalError) as e:
            logger.error(
                f"Database error getting metric configs for fact type {fact_type} "
                f"from Postgres for {tenant}/{facility}",
                exc_info=True,
                facility=facility,
                fact_type=fact_type,
                tenant=tenant,
                error_type=type(e).__name__,
            )
            raise
        except (ValueError, KeyError) as e:
            logger.error(
                f"Configuration error getting metric configs for fact type {fact_type} "
                f"from Postgres for {tenant}/{facility}",
                exc_info=True,
                facility=facility,
                fact_type=fact_type,
                tenant=tenant,
                error_type=type(e).__name__,
            )
            raise

    def _get_cached_config(
        self, tenant: str, facility: str, fact_type: str
    ) -> Dict[str, config_schema.MetricConfig]:
        """Get configuration from cache if available."""
        if (
            tenant in self.configs
            and facility in self.configs[tenant]
            and fact_type in self.configs[tenant][facility]
        ):
            logger.debug(
                f"Returning cached config for fact type {fact_type} "
                f"in Postgres for {tenant}/{facility}",
                tenant=tenant,
                facility=facility,
                fact_type=fact_type,
            )
            return self.configs[tenant][facility][fact_type]
        return None

    def _create_fact_type_metrics_dict_from_rows(
        self, metric_config_rows: list, tenant: str, facility: str, fact_type: str
    ) -> Dict[str, config_schema.MetricConfig]:
        """Process metric config rows and build the fact_type metrics dict."""
        fact_type_metrics_dict = {}

        for row in metric_config_rows:
            try:
                config_data = row.get("config_data", row)
                metric_config = config_schema.MetricConfig.model_validate(config_data)
                # Store the metric config in the fact_type_metrics_dict
                fact_type_metrics_dict[metric_config.metric_config_name] = metric_config
            except (ValueError, KeyError, ValidationError) as e:
                logger.error(
                    "Configuration validation error processing metric config row "
                    "for fact type {fact_type} in Postgres {tenant}/{facility}",
                    exc_info=True,
                    row=row,
                    tenant=tenant,
                    facility=facility,
                    fact_type=fact_type,
                    error_type=type(e).__name__,
                )
                continue
            except (TypeError, AttributeError) as e:
                logger.error(
                    "Unexpected error processing metric config row "
                    "for fact type {fact_type} in Postgres {tenant}/{facility}",
                    exc_info=True,
                    row=row,
                    tenant=tenant,
                    facility=facility,
                    fact_type=fact_type,
                    error_type=type(e).__name__,
                )
                continue

        return fact_type_metrics_dict
