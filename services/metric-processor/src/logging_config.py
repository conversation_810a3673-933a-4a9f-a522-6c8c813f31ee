"""Logging configuration for the metric processor service."""

import os
import logging
import sys
import structlog


def get_log_level():
    # Use INFO log level except for local development which uses DEBUG.
    log_level = logging.INFO
    if os.getenv("LOCALDEV", "false").lower() == "true":
        log_level = logging.DEBUG
    return log_level


def setup_logging():
    log_level = get_log_level()
    is_localdev = os.getenv("LOCALDEV", "false").lower() == "true"

    # Set up basic logging for structlog to integrate with standard logging.
    logging.basicConfig(
        level=log_level,
        format="%(message)s",
        stream=sys.stdout,
    )

    # Choose the renderer based on environment
    if is_localdev:
        # Colorized pretty output for local development
        structlog.configure(
            processors=[
                structlog.dev.set_exc_info,
                structlog.processors.add_log_level,
                structlog.processors.format_exc_info,
                structlog.threadlocal.merge_threadlocal,
                structlog.dev.ConsoleRenderer(
                    colors=True, pad_level=False, pad_event=5
                ),  # Pretty, colorized logs
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.make_filtering_bound_logger(log_level),
            cache_logger_on_first_use=True,
        )
    else:
        # JSON output for deployed GCP cloudruns
        structlog.configure(
            processors=[
                structlog.stdlib.add_log_level,
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.dict_tracebacks,
                structlog.threadlocal.merge_threadlocal,
                structlog.processors.JSONRenderer(),  # Structured JSON logs
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.make_filtering_bound_logger(log_level),
            cache_logger_on_first_use=True,
        )


# Call the setup function to configure logging at the start.
setup_logging()

# Use structlog for logging
logger = structlog.get_logger("metric_processor")
logger.info("Logger initialized", log_level=get_log_level())
