#!/usr/bin/env python3
"""
Simple script to run unit tests with discover functionality.
"""
import os
import sys
import unittest


def main():
    """Run unit tests with discover functionality."""
    try:
        # Run unit tests with discover
        loader = unittest.TestLoader()
        start_dir = "src/tests/unit_tests"
        
        # Check if the directory exists
        if not os.path.exists(start_dir):
            print(f"Error: Test directory '{start_dir}' not found!")
            sys.exit(1)
        
        suite = loader.discover(start_dir, pattern="test_*.py")
        
        # Check if any tests were found
        if not suite.countTestCases():
            print(f"Warning: No test files found in '{start_dir}'")
            return 0
        
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        # Exit with appropriate code
        return 0 if result.wasSuccessful() else 1
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Make sure all dependencies are installed: poetry install")
        return 1
    except Exception as e:
        print(f"Unexpected error running tests: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
