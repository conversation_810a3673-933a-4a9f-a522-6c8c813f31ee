# Running `ict-metric-processor` Locally

## Prerequisites

1. Install Dependencies:

   - Ensure you have poetry installed:
     `brew install poetry`
   - Install using poetry
     `poetry install --with docs`
   - Black Formatter for python files: <https://marketplace.visualstudio.com/items?itemName=ms-python.black-formatter>

2. Install Docker.

3. Create a `.env` file in the `ict-metric-processor` directory to store environment variables that the application will use.

   - Open a text editor and create a new file named `.env` in the current directory.
   - Copy and paste the values from `.env.example` to your `.env` file. The connection information for Redis and Neo4J should match the default values but you can modify if needed.
   - In your `.env` file, make sure of the following:
     1. Set `REDIS_HOST` to `redis`.
     2. Ensure `NEO4J_URI` is set to `bolt://neo4j:7687`.
     3. Set `NEO4J_USERNAME` TO `neo4j`.
     4. Set `NEO4J_PASSWORD` to any non-empty string other than `neo4j`.

4. Docker Compose Configuration:\*\*
   - The `docker-compose.yml` file is located in the root directory of `ict-metric-processor`. This file is used to build and run a Docker container that includes Redis, Neo4j, and the metric processor.

### Generate Documentation

1. Serve the documentation on localhost
   `mkdocs serve`
2. To learn the configuration schema, navigate to:
   `/api/configurations`

## Run Locally in a Docker Container

For development purposes, we run this project using Docker Compose. We have sample testing data in the `/src/tests/data/` that we send to the Metric Processor container using `curl`. The data sent by `curl` consists of an array of `1-N fact messages`, and some meta data like `Tenant`, `Facility`, and `Fact Type`.

`main.py` does the work of receiving those message batches, and then parsing and sending each message on to the Metric Processor. Similar to what the `dev-ict-d-etl-insights_pubsub_to_bigquery` CloudRun does in GCP.

The Metric Processor then processes each message, and updates Neo4j and/or Redis accordingly.

### Steps to Run

1. **Build and Start the Docker Containers:**

   - Navigate to the root directory of metric-processor`.
   - Make sure the docker network `control_tower_network` exists with `docker network ls`. If not, create it with `docker network create control_tower_network`
   - Run the following command to build and start the containers:
     `docker compose up`
     - If you'd like it to watch for updates, add the watch flag `docker compose up --watch`
   - This will create 3 containers. The Metric Processor, a Redis server, and a Neo4j DB.
   - Then run the API locally with `docker compose -f docker-compose.metric-processor.yml up` from services/api. We need the API since we store our metric configs in the postgres database.

2. **Run Metric Processor with Test Data**:

   - Run all test data through the metric processor using the script in `tools/run_all_data.sh`:

     ```bash
     ./tools/run_all_data.sh
     ```

   - This script will run test data for various scenarios:
     - Miniload Test
     - Multishuttle Test
     - Shuttle Test
     - Workstations Test
     - Connection Test
   - Each tenant must have a folder located in `src/tests/data/` which matches the name of the tenant
   - Individual data files are placed in the `src/tests/data/<tenant-name>/` folder

3. **Query Neo4j**

   - Visit the Neo4j Browser GUI at [http://localhost:7474/](http://localhost:7474/).
   - Log in to the database using your credentials.
   - Run the following Cypher query to display all nodes:
   - `MATCH (n) RETURN n`

4. **Check metrics generated in Redis**

   - Attach shell to Redis Server container (Docker VS Code extension is recommended way)
   - Open Redis Client: `redis-cli`
   - Check all KEYS that were generated: `KEYS *`
   - **Alternative**: Use [Redis Insight](https://redis.io/insight/) for a visual interface to browse and manage your Redis data
   - You can alternatively use the Redis Insight app to view the data by connecting to `redis://localhost:6379`

## Running Tests

Our test suite consists of three types of tests:

- **Config Tests**: Tests our custom configuration language and verifies the expected results of each metric configuration
- **Unit Tests**: Traditional unit tests for testing individual components and functions
- **Integration Tests**: Tests integration with external services like PostgreSQL and Neo4j

### Unit Tests

- Run all unit tests with: `poetry run test` from the ict-metric-processor root
- This will discover and run all test files in the unit_tests directory

## Creating New Default Configs

Our default configs are located at: `services/api/libs/api/ict-api-schema/src/default-data/process-flow/metric-configs`

- Each file contains configs for a specific fact type
- To add a new config, add it to the appropriate file
- To add a new fact type, create a new file
