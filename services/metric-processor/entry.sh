#!/bin/bash

# Source .env file if it exists
if [ -f .env ]; then
  set -a  # automatically export all variables
  source <(grep -v '^#' .env | grep -v '^$')
  set +a  # stop automatically exporting
fi

# Convert ENV variable to lowercase if it exists
if [[ $ENV ]]; then
  ENV=$(echo "$ENV" | tr '[:upper:]' '[:lower:]')
fi

# Set default port if not provided
PORT=${PORT:-8080}

# Start Cloud SQL Proxy if CLOUD_SQL_CONN_NAME is set (for cloud environments)
if [[ -n "$CLOUD_SQL_CONN_NAME" ]]; then
    echo "Starting Cloud SQL Proxy for connection: $CLOUD_SQL_CONN_NAME"
    /usr/app/cloud-sql-proxy --private-ip --port 5432 $CLOUD_SQL_CONN_NAME &
    sleep 5
    echo "Cloud SQL Proxy started"
fi

# If ENV is not set or is set to "dev", run the application in debugging mode
if [[ -z "$ENV" || "$ENV" = "dev" ]]; then
    echo "debugging..."
    gunicorn --bind 0.0.0.0:$PORT  --timeout 120 "src.main:app"
else
    # For any other environment, run the application normally
    gunicorn --bind 0.0.0.0:$PORT --timeout 120 "src.main:app"
fi
