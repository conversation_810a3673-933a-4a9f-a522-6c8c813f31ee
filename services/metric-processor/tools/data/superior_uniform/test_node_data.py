from generate_timestamps import update_timestamp

# Test data for a fact type that doesn't exist in the database
# This should trigger the empty config caching and return 200 (ACK) without processing

messages = [
    {
        "messageId": "test-nonexistent-fact-type",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "test_fact_type",  # This fact type doesn't exist in the database
            "description": "Test message for non-existent fact type to verify empty config caching",
            "row_hash": "test-1",
        },
        "data": {
            "test_field": "test_value",
            "event_timestamp_utc": update_timestamp(100),
            "some_code": "TEST123",
        },
    },
    {
        "messageId": "test-nonexistent-fact-type",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "test_fact_type",  # Same non-existent fact type
            "description": "Second test message to verify caching prevents repeated DB queries",
            "row_hash": "test-2",
        },
        "data": {
            "test_field": "another_test_value",
            "event_timestamp_utc": update_timestamp(200),
            "some_code": "TEST456",
        },
    },
    {
        "messageId": "test-nonexistent-fact-type",
        "attributes": {
            "tenant_id": "superior_uniform",
            "facility_id": "superioruniform_eudoraar",
            "event_type": "test_fact_type",  # Third message with same fact type
            "description": "Third test message to further verify caching behavior",
            "row_hash": "test-3",
        },
        "data": {
            "test_field": "third_test_value",
            "event_timestamp_utc": update_timestamp(300),
            "some_code": "TEST789",
        },
    },
]
