# Dashboarding Standards

> Standards repo - the "base" for all dashboarding.

This repo is designed to be a place that can be pulled from via CI/CD (when applicable) or manually to start from a standard deployment.

## Structure
The structure of the repo should look as such.
 - lowercase names
 - no spaces in names
 - subsystem-type should be something easily determined such as `dms` or `operator`

```
/common
    /dashboard-icons
        /README.md
    /functions
        /README.md
/<subsystem-type>
    /views
    /functions
    /workbooks
    README.md
```

### common
This should be anything that is used across multiple tenants and is not specific to one.

### subsystem-type
This should be a logical feature/component/grouping of functionality.

Examples:
 - autostore
 - dms
 - operator

#### README.md
This explains how each view/function/workbook is typically used, deployed, and the customization points.