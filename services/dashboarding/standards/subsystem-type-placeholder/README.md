# Subsystem Type Standard

> Standards location for `subsystem-type`.

This explains how each view/function/workbook is typically used, deployed, and the customization points.

## Functions

### placeholder_f
This function does XYZ and returns 123. 

## Views

### placeholder_v
This view selects off of table ABC over the timeframe of 30 days.\
Returns a recordset with columns:
 - Column A
 - Column B

## Workbooks

### placeholder_twb
Standards workbook for `subsystem-type` includes the following dashboards:
 - Dashboard 1
 - Dashboard 2

## Deployment Steps

1. Deploy `placeholder_f`
2. Deploy `placeholder_v`
3. Update and deploy `placeholder_twb`