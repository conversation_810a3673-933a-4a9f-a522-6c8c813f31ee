# Tenant Deployment
> Source of truth, or should be, of what is deployed by a given tenant and facility.

## Structure
The structure of the repo should look as such.
 - lowercase names
 - no spaces in names

```
/<tenant>
    /<facility>
        /schema
            /functions
            /views
            README.md
        /workbooks
            README.md
```
### Schema
Schema (views, functions, tables, etc) that is deployed on a tenant/facility level.

### Workbooks
Workbooks that are deployed at a tenant/facility level.