# Tenant Facility Workbooks

> Deployed (or about to be) workbooks for a specific tenant and facility.
> 
> Some of the changes may only be "datasource" name, these should still be called out as such.

## Table of Contents
- [Tenant Facility Workbooks](#tenant-facility-workbooks)
  - [Table of Contents](#table-of-contents)
  - [Workbook 1](#workbook-1)
    - [Datasources](#datasources)
    - [Worksheets](#worksheets)
      - [Worksheet1](#worksheet1)
      - [Worksheet2](#worksheet2)
    - [Parameters](#parameters)
    - [Calculations](#calculations)
      - [New Calc Example](#new-calc-example)
    - [Filters](#filters)
    - [Base Removed / Unused](#base-removed--unused)
    - [Other Changes / Customizations](#other-changes--customizations)


## Workbook 1

### Datasources
 - View ABCD_V
   - What this view does.

### Worksheets
  
#### Worksheet1
>  What this worksheet does. Taken from standards.

```
Example
##### Operator Statistics Overview

- Added `Shift by Metric`
- Added `Workstation` Filter
- Added `Shift` (Superior Shift) Filter

##### CARD - Inoperator Percentage

- Removed Blocking / Starved Percentage
- Renamed to Starved Percentage

##### Operator Workstation Stats

- Added `Shift` (Superior Shift)

```

#### Worksheet2
>  What this worksheet does. New worksheet.

 - KPIs on this dashboard:
   - ABCD
     - The throughput metric of equipment 1234.
   - EFGH
     - The fault rate of the area.
   - IJKL / Hour
     - The rate of picking in the area.


### Parameters
```
Examples:

[Param - Donor Container Minutes] = 0.211
[Param - Expected Qty Per Hour]   = 200
[Param - GTP Container Minutes]   = 0.411
[Param - Pack Line Qty Minutes]   = 0.0627
```

### Calculations
> Calculated fields that were added that are not in the datasource.

#### New Calc Example
This calculation termines the picking worktypes.
```
    // New Calculated Field
    [Work_Type] = "PICKALTR" 
    OR [Work_Type] = "PICK"
    OR [Work_Type] = "PICKSNBX"
    Or [Work_Type] = "SNBX"
```

### Filters
> Filters added to the dashboards - typically across all of them.

- Workstation Filter Example

### Base Removed / Unused
> Removed/hid calculations from standards.

- Metric ABC
- Dimension EFG

### Other Changes / Customizations
> Anything not covered above.
