# Setup

## Overview

This repository includes a suite of end-to-end API tests that have been written for 'Intelligent Control Tower' API using the Playwright test framework for Node.js.
This framework is designed to automate web application testing with ease. It includes an easy-to-use API, support for parallel test execution, and a reporting system.
To learn more about Playwright and how it can be used for your own testing needs, visit the official [Playwright documentation](https://playwright.dev/).

## Prerequisites

To utilize the Playwright framework, you will need to have [Node.js](https://nodejs.org/en) installed, preferably version 14 or higher and [Git](https://git-scm.com/).
You can confirm the version of Node.js installed on your system by running the command

```bash
  node -v
```

Follow the steps below to get started with the Test Automation Framework:

## Install

```bash
  yarn install
```

## Configuration

In the root of this project, add a `.env` file.  
 Refer to the `example.env`. for an example.

## Running Tests

### Local

- Run tests

```bash
yarn test
```
