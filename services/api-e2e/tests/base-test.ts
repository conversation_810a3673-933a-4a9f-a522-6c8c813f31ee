import Container from 'typedi';
import { APIRequestContext, test as base } from '@playwright/test';
import { UserRole, ApiEnvironmentUrl, ApiDateFilter, ApiDateFilterOption, getDateFilter } from '../src/types';
import { AuthTokenService, UserTokenService } from 'src/services/auth';
import { ConfigurationService } from 'src/services/configuration/configuration-service';
import { Defaults } from 'src/defaults';
export type TestOptions = {
  baseUrl: ApiEnvironmentUrl;
  useUserWithRole: UserRole;
  api: APIRequestContext;
  dateFilterOption: ApiDateFilterOption;
  dateFilter: ApiDateFilter;
  selectedFacilityId: string | undefined;
};

export const test = base.extend<TestOptions>({
  baseUrl: [Container.get(ConfigurationService).env.apiUrl, { option: true }],
  dateFilterOption: ['yesterday', { option: true }],
  useUserWithRole: ['ct_user', { option: true }],
  selectedFacilityId: [Defaults.FACILITY_ID, { option: true }],

  /**
   *
   */
  dateFilter: async ({ dateFilterOption }, use) => {
    const dateFilter = getDateFilter(dateFilterOption);
    await use(dateFilter);
  },

  /**
   *
   */
  api: async ({ playwright, baseUrl, useUserWithRole, selectedFacilityId }, use) => {
    const token = await getUserToken(useUserWithRole);
    const httpHeaders = {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    };

    if (selectedFacilityId) {
      console.log(`setting ict-facility-id to ${selectedFacilityId}`);
      httpHeaders['ict-facility-id'] = selectedFacilityId;
    }

    const requestContext = await playwright.request.newContext({
      baseURL: baseUrl,
      extraHTTPHeaders: httpHeaders,
      ignoreHTTPSErrors: true,
    });
    await use(requestContext);
    await requestContext.dispose();
  },
});

export const expect = test.expect;

let CurrentUserRole: UserRole;
async function getUserToken(userRole: UserRole): Promise<string> {
  if (userRole !== CurrentUserRole) {
    const userService = new UserTokenService(userRole);
    const token = await userService.getUserToken();
    Container.set(AuthTokenService, new AuthTokenService(token));
    CurrentUserRole = userRole;
  }

  return Container.get(AuthTokenService).token;
}
