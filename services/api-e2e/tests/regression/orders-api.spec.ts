import { expect, test } from '../base-test';

test.describe('Orders API', async () => {
  test.describe('Orders Progress', async () => {
    test('Should return orders progress', async ({ api, dateFilter }) => {
      const result = await api.get('orders/progress', {
        params: dateFilter,
      });

      expect([200, 204]).toContain(result.status());
      if (result.status() === 200) {
        const data = await result.json();
        expect(data).toHaveProperty('orderProgressPercentage');
        expect(typeof data.orderProgressPercentage).toBe('number');
      }
    });

    test('Should support custom facility selection', async ({ api, dateFilter }) => {
      const result = await api.get('orders/progress', {
        params: dateFilter,
        headers: {
          'ict-facility-id': 'superior_uniform#eudoraar',
        },
      });

      expect([200, 204]).toContain(result.status());

      if (result.status() === 200) {
        const data = await result.json();
        expect(data).toHaveProperty('orderProgressPercentage');
        expect(typeof data.orderProgressPercentage).toBe('number');
      }
    });

    test('Should return unauthorized for an invalid facility id', async ({ api, dateFilter }) => {
      const result = await api.get('orders/progress', {
        params: dateFilter,
        headers: {
          'ict-facility-id': 'invalid_facility',
        },
      });

      expect(result.status()).toEqual(401);
    });
  });
  test.describe('Orders Outstanding', async () => {
    test('Should return correct data', async ({ api, dateFilter }) => {
      const result = await api.get('orders/fulfillment-outstanding', {
        params: dateFilter,
      });
      expect([200, 204]).toContain(result.status());
      if (result.status() === 200) {
        const data = await result.json();
        expect(data).toHaveProperty('incompletedTotal');
        expect(typeof data.incompletedTotal).toBe('number');
      }
    });
  });
  test.describe('Orders Fulfillment', async () => {
    test('Should return correct data', async ({ api, dateFilter }) => {
      const result = await api.get('orders/customer/fulfillment', {
        params: dateFilter,
      });
      expect([200, 204]).toContain(result.status());
      if (result.status() === 200) {
        const data = await result.json();
        expect(data).toHaveProperty('projectedOrderFulfillmentPercentage');
        expect(typeof data.projectedOrderFulfillmentPercentage).toBe('number');
      }
    });
  });
  test.describe('Orders Pick Cycle Count', async () => {
    test('Should return pick order cycle time data for the timeframe', async ({ api, dateFilter }) => {
      const result = await api.get('orders/pick/cycle-count', {
        params: dateFilter,
      });
      expect([200, 204]).toContain(result.status());
      if (result.status() === 200) {
        const data = await result.json();
        expect(data).toHaveProperty('cycleCount');
        expect(typeof data.cycleCount).toBe('number');
      }
    });
  });
});
