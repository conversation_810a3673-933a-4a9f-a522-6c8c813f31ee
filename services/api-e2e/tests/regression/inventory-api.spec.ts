import { expect, test } from '../base-test';

test.describe('Inventory API', async () => {
  test.describe('Inventory Storage Utilization', async () => {
    test('Should return correct data', async ({ api, dateFilter }) => {
      const result = await api.get('inventory/storage/utilization', {
        params: dateFilter,
      });
      expect([200, 204]).toContain(result.status());
      if (result.status() === 200) {
        const data = await result.json();
        expect(data).toHaveProperty('utilizationPercentage');
        expect(typeof data.utilizationPercentage).toBe('number');
      }
    });
  });
  test.describe('Inventory Stock Distribution Under Percentage Series', async () => {
    test.use({ dateFilterOption: 'last 7 days' });
    test('Should return correct data', async ({ api, dateFilter }) => {
      const result = await api.get('inventory/wms/stock/distribution/under/percentage/series', {
        params: dateFilter,
      });
      expect([200, 204]).toContain(result.status());
      if (result.status() === 200) {
        const data = await result.json();
        expect(data).toHaveProperty('underInventory');
        expect(data.underInventory.length).toBeGreaterThan(0);
        for await (const underInventory of data.underInventory) {
          expect.soft(isNaN(Date.parse(underInventory.name))).toBeFalsy();
          expect.soft(typeof underInventory.value).toBe('number');
          expect.soft(typeof underInventory.isValidPercentage).toBe('boolean');
        }
      }
    });
  });
  test.describe('Inventory Stock Distribution No Percentage Series', async () => {
    test.use({ dateFilterOption: 'last 7 days' });
    test('Should return correct data', async ({ api, dateFilter }) => {
      const result = await api.get('inventory/wms/stock/distribution/no/percentage/series', {
        params: dateFilter,
      });
      expect([200, 204]).toContain(result.status());
      if (result.status() === 200) {
        const data = await result.json();
        expect(data).toHaveProperty('noInventory');
        expect(data.noInventory.length).toBeGreaterThan(0);
        for await (const underInventory of data.noInventory) {
          expect.soft(isNaN(Date.parse(underInventory.name))).toBeFalsy();
          expect.soft(typeof underInventory.value).toBe('number');
          expect.soft(typeof underInventory.isValidPercentage).toBe('boolean');
        }
      }
    });
  });
  test.describe('Inventory Stock Distribution Over Percentage Series', async () => {
    test.use({ dateFilterOption: 'last 7 days' });
    test('Should return correct data', async ({ api, dateFilter }) => {
      const result = await api.get('inventory/wms/stock/distribution/over/percentage/series', {
        params: dateFilter,
      });
      expect([200, 204]).toContain(result.status());
      if (result.status() === 200) {
        const data = await result.json();
        expect(data).toHaveProperty('overInventory');
        expect(data.overInventory.length).toBeGreaterThan(0);
        for await (const underInventory of data.overInventory) {
          expect.soft(isNaN(Date.parse(underInventory.name))).toBeFalsy();
          expect.soft(typeof underInventory.value).toBe('number');
          expect.soft(typeof underInventory.isValidPercentage).toBe('boolean');
        }
      }
    });
  });
  test.describe('Inventory Stock Distribution At Percentage Series', async () => {
    test.use({ dateFilterOption: 'last 7 days' });
    test('Should return correct data', async ({ api, dateFilter }) => {
      const result = await api.get('inventory/wms/stock/distribution/at/percentage/series', {
        params: dateFilter,
      });
      expect([200, 204]).toContain(result.status());
      if (result.status() === 200) {
        const data = await result.json();
        expect(data).toHaveProperty('atInventory');
        expect(data.atInventory.length).toBeGreaterThan(0);
        for await (const underInventory of data.atInventory) {
          expect.soft(isNaN(Date.parse(underInventory.name))).toBeFalsy();
          expect.soft(typeof underInventory.value).toBe('number');
          expect.soft(typeof underInventory.isValidPercentage).toBe('boolean');
        }
      }
    });
  });
  test.describe('Inventory Replenishment Details', async () => {
    test.use({ dateFilterOption: 'last 7 days' });
    test('[@C3659378] - Test that First Shift data is between 0700 to 1529 local time', async ({ api, dateFilter }) => {
      const result = await api.get('/inventory/replenishment/details', {
        params: dateFilter,
      });
      expect([200, 204]).toContain(result.status());
      if (result.status() === 200) {
        const data = await result.json();
        expect(data).toHaveProperty('shiftTimes');
        expect.soft(data.shiftTimes.first_startTime).toBe('7:00');
        expect.soft(data.shiftTimes.first_endTime).toBe('15:29');
      }
    });
    test('[@C3659379] - Test that Second Shift data is between 1530 to 2359 local time', async ({ api, dateFilter }) => {
      const result = await api.get('/inventory/replenishment/details', {
        params: dateFilter,
      });
      expect([200, 204]).toContain(result.status());
      if (result.status() === 200) {
        const data = await result.json();
        expect(data).toHaveProperty('shiftTimes');
        expect.soft(data.shiftTimes.second_startTime).toBe('15:30');
        expect.soft(data.shiftTimes.second_endTime).toBe('23:59');
      }
    });
    test('[@C3659380] - Test that Third Shift data is between 0000 to 0659 local time', async ({ api, dateFilter }) => {
      const result = await api.get('/inventory/replenishment/details', {
        params: dateFilter,
      });
      expect([200, 204]).toContain(result.status());
      if (result.status() === 200) {
        const data = await result.json();
        expect(data).toHaveProperty('shiftTimes');
        expect.soft(data.shiftTimes.third_startTime).toBe('00:00');
        expect.soft(data.shiftTimes.third_endTime).toBe('06:59');
      }
    });
  });
});
