import { Defaults } from 'src/defaults';
import { expect, test as setup } from '../base-test';

setup.describe(() => {
  setup.use({ useUserWithRole: 'ct_configurator' });
  setup('Seed Facility Maps', async ({ api }) => {
    // prepare...
    const facilityMapSetting = {
      name: 'facility-maps',
      group: 'facility-maps',
      description: 'Array of Facility Map Definitions',
      dataType: 'json',
      value: [
        {
          id: Defaults.FACILITY_ID,
          name: 'E<PERSON>ra, AR',
          default: true,
          dataset: 'superior_uniform',
        },
      ],
      levelToUpdate: 'tenant',
    };

    const prepareResult = await api.put('config/settings', { data: facilityMapSetting });
    expect([200, 201]).toContain(prepareResult.status());
  });
});
