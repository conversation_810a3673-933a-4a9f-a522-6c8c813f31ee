import { expect, test } from '../base-test';

test.describe('Ai api', async () => {
  test.describe('GET - [ai/healthcheck]', async () => {
    test(`verify endpoint can be accessed by a ct user.`, async ({ api }) => {
      const result = await api.get('ai/healthcheck');
      expect(result.status()).toEqual(200);
    });
    test(`verify it returns the expected data structure.`, async ({ api }) => {
      const result = await api.get('ai/healthcheck');
      const data = await result.json();

      expect(data.status).toBeDefined();
      expect(data.sha).toBeDefined();
    });
  });
});
