import { expect, test } from '../base-test';

const serviceEndpoints = [
  'ai',
  'config',
  'data-explorer',
  'diagnostics',
  'equipment',
  'inventory',
  'operators',
  'orders',
  'workstation',
  'availability',
];

test.describe.only('smoke test', async () => {
  serviceEndpoints.forEach((endpoint) => {
    test.describe(`Get - ${endpoint}`, async () => {
      test(`/healthcheck`, async ({ api }) => {
        const result = await api.get(`${endpoint}/healthcheck`);
        expect(result.status()).toEqual(200);
        const data = await result.json();

        expect(result.status()).toEqual(200);
        expect(data.status).toBeDefined();
        expect(data.sha).toBeDefined();
      });
      test(`/healthcheck/full`, async ({ api }) => {
        const result = await api.get(`${endpoint}/healthcheck/full`);
        const data = await result.json();

        expect(result.status()).toEqual(200);
        expect(data.status).toBeDefined();
        expect(data.sha).toBeDefined();
      });
    });
  });
});
