# Test Examples

## How to use a user with a needed ct role.

```js
import { expect, test } from './base-test';
test.describe('GET - [config/curated-tables/list] with role ct_engineer', async () => {
  test.use({ useUserWithRole: 'ct_engineers' });
  test(`verify endpoint can be be accessed by a ct user.`, async ({ api }) => {
    const result = await api.get('config/curated-tables/list');
    expect(result.status()).toEqual(200);
    expect(result.statusText()).toEqual('OK');
  });
});
```
