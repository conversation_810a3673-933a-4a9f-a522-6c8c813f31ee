export enum AppConfigSettingSource {
  default = 'default',
  tenant = 'tenant',
  facility = 'facility',
  user = 'user',
}

export interface AppConfigSettingItem {
  id: string;
  name: string;
  group: string | null;
  description?: string | null;
  dataType: 'json' | 'string' | 'number' | 'boolean';
  value?: unknown;
  source?: AppConfigSettingSource;
  tags?: { [key: string]: string } | null;
  parentSetting?: string | null;
}
