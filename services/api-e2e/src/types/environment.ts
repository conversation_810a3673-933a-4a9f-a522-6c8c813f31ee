export const Environments = ['local-dev', 'preview', 'local', 'dev', 'stage', 'prod'] as const;
export type EnvironmentName = (typeof Environments)[number];

export const IctUrls = [
  'http://localhost:4200',
  'https://dev.ict.dematic.dev',
  'https://stage.ict.dematic.dev',
  'https://ict.dematic.cloud',
] as const;
export type UiEnvironmentUrl = (typeof IctUrls)[number];

export const IctApiEndpoints = [
  'http://localhost:8080',
  'https://api.dev.ict.dematic.dev',
  'https://api.stage.ict.dematic.dev',
  'https://api.ict.dematic.cloud',
] as const;
export type ApiEnvironmentUrl = (typeof IctApiEndpoints)[number];

export type EnvironmentConfig = {
  name: EnvironmentName;
  baseUrl: UiEnvironmentUrl;
  apiUrl: ApiEnvironmentUrl;
};

export const Local: EnvironmentConfig = {
  name: 'local',
  baseUrl: 'http://localhost:4200',
  apiUrl: 'http://localhost:8080',
} as const;

export const LocalDev: EnvironmentConfig = {
  name: 'local-dev',
  baseUrl: 'https://dev.ict.dematic.dev',
  apiUrl: 'http://localhost:8080',
} as const;

export const Dev: EnvironmentConfig = {
  name: 'dev',
  baseUrl: 'https://dev.ict.dematic.dev',
  apiUrl: 'https://api.dev.ict.dematic.dev',
} as const;

export const Stage: EnvironmentConfig = {
  name: 'stage',
  baseUrl: 'https://stage.ict.dematic.dev',
  apiUrl: 'https://api.stage.ict.dematic.dev',
} as const;

export const Prod: EnvironmentConfig = {
  name: 'prod',
  baseUrl: 'https://ict.dematic.cloud',
  apiUrl: 'https://api.ict.dematic.cloud',
} as const;

export const EnvironmentConfigs = [Local, LocalDev, Dev, Stage, Prod];

export type Environment = typeof Local | typeof LocalDev | typeof Dev | typeof Stage | typeof Prod;

export function getEnv(name: EnvironmentName) {
  const found = EnvironmentConfigs.find((env) => env.name === name);
  if (found) return found;

  throw new Error(`Unknown env: ${name}!`);
}

export function setPreviewEnv(url: string): EnvironmentConfig {
  return {
    name: 'preview',
    baseUrl: url as UiEnvironmentUrl,
    apiUrl: 'https://api.ict.dematic.cloud',
  } as const;
}
