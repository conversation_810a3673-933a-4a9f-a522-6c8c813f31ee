import { DateTime } from 'luxon';

export const ApiDateFilterOptions = ['today', 'yesterday', 'last 7 days'] as const;

export type ApiDateFilterOption = (typeof ApiDateFilterOptions)[number];

export type ApiDateFilter = {
  start_date: string;
  end_date: string;
};

export function getDateFilter(option: ApiDateFilterOption): ApiDateFilter {
  switch (option) {
    case 'today':
      return {
        start_date: DateTime.now().startOf('day').toISO(),
        end_date: DateTime.now().endOf('day').toISO(),
      };
    case 'yesterday':
      return {
        start_date: DateTime.now().minus({ days: 1 }).startOf('day').toISO(),
        end_date: DateTime.now().minus({ days: 1 }).endOf('day').toISO(),
      };
    case 'last 7 days':
      return {
        start_date: DateTime.now().minus({ days: 7 }).startOf('day').toISO(),
        end_date: DateTime.now().endOf('day').toISO(),
      };
  }
}
