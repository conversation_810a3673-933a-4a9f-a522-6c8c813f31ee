import 'reflect-metadata';
import Container, { Service } from 'typedi';
import { User, UserRole } from 'src/types';
import { TokenSet } from 'auth0';
import { Browser, chromium, Page, Response } from '@playwright/test';
import { Login } from './login';
import { IConfigurationService, ConfigurationService } from '../configuration';

@Service()
export class UserTokenService {
  private config: IConfigurationService;
  private user: User;

  constructor(userRole: UserRole) {
    this.config = Container.get(ConfigurationService);
    this.user = this.config.users.getUserByRole(userRole);
  }

  public async getUserToken(): Promise<TokenSet> {
    const browser: Browser = await chromium.launch();
    const context = await browser.newContext();
    const page: Page = await context.newPage();
    const app = new Login(page, this.config);
    await app.goTo();
    const tokenResponse = page.waitForResponse('**/oauth/token', { timeout: 90000 });
    console.log(`User Log In: `, this.user.email);

    await app.login(this.user, this.user.org);

    const token = await this.interceptToken(tokenResponse);
    await browser.close();
    return token;
  }

  private async interceptToken(tokenResponse: Promise<Response>): Promise<TokenSet> {
    const response = await tokenResponse;
    const body = await response.body();
    const token = JSON.parse(body.toString()) as TokenSet;
    return token;
  }
}
