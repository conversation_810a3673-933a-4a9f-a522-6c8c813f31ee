import { Locator, <PERSON> } from '@playwright/test';
import { User, Tenant } from '../../../types';
import { IConfigurationService } from 'src/services/configuration';

export class Login {
  private config: IConfigurationService;
  public page: Page;
  private readonly WAIT_FOR_LOGIN_TIMEOUT = 30000;
  public username: <PERSON><PERSON><PERSON>;
  public email: <PERSON><PERSON><PERSON>;
  public password: <PERSON>cator;
  public loginButton: Locator;
  public signupButton: Locator;
  public errorMessage: Locator;

  constructor(page: Page, config: IConfigurationService) {
    this.config = config;
    this.page = page;
    this.username = this.page.locator(`input[id='username']`);
    this.email = this.page.locator(`input[id='email']`);
    this.password = this.page.locator(`input[id='password']`);
    this.loginButton = this.page.getByRole('button', {
      name: 'Continue',
      exact: true,
    });
    this.signupButton = this.page.getByRole('link', {
      name: 'Sign up',
      exact: true,
    });
    this.errorMessage = this.page.locator(`span[id='error-element-password']`);
  }

  public async login(user: User, organization?: Tenant) {
    await this.username.pressSequentially(user.email);
    await this.password.pressSequentially(user.password);
    await this.loginButton.click();
    if (organization) {
      await this.chooseOrganization(organization);
    }
  }

  public async chooseOrganization(organization: Tenant) {
    const button = this.page.getByRole('button', {
      name: `${organization}`,
      exact: true,
    });

    if (await button.isVisible({ timeout: 1500 })) {
      await button.waitFor({ state: 'visible' });
      await button.click();
    }
  }

  public async goTo(): Promise<void> {
    await this.page.goto(this.config.env.baseUrl);
    await this.page.waitForTimeout(3000);
  }
}
