import { UserRole as Role } from 'src/types';
import { User } from 'src/types';

export class UserManager {
  private usersByRole: Map<Role, User> = new Map();

  constructor(ctUser: User, ctEngineer: User, ctConfigurator) {
    // Initialize users
    this.usersByRole.set('ct_user', ctUser);
    this.usersByRole.set('ct_engineers', ctEngineer);
    this.usersByRole.set('ct_configurator', ctConfigurator);
  }

  getUserByRole(role: Role): User {
    const user = this.usersByRole.get(role);
    if (user !== undefined) {
      return user;
    }
    throw new Error(`no use with role of ${role} is available!`);
  }
}
