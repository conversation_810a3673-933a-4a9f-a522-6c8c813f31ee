import { z } from 'zod';
import { Environments } from '../../types/environment';

export const TestEnvironmentSchema = z.object({
  ICT_ENV: z.enum(Environments),
  ICT_CT_USER_USER_EMAIL: z.string().email(),
  ICT_CT_USER_USER_PWD: z.string(),
  ICT_CT_ENGINEER_USER_EMAIL: z.string().email(),
  ICT_CT_ENGINEER_USER_PWD: z.string(),
});

export type TestEnvironmentConfiguration = z.infer<typeof TestEnvironmentSchema>;
