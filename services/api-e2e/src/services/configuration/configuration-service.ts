import 'reflect-metadata';
import { Environment, getEnv, EnvironmentName, setPreviewEnv } from '../../types/environment';
import { Service } from 'typedi';
import * as dotenv from 'dotenv';
import path from 'path';
import { TestEnvironmentSchema } from './types';
import { User } from 'src/types';
import { UserManager } from './user-manager';

const CONFIGURATION_FILE = path.join(process.cwd(), '.env');

export interface IConfigurationService {
  env: Environment;
  users: UserManager;
}

@Service()
export class ConfigurationService implements IConfigurationService {
  constructor() {
    dotenv.config({ path: CONFIGURATION_FILE });
    const res = TestEnvironmentSchema.safeParse(process.env);
    if (!res.success) {
      for (let index = 0; index < res.error.errors.length; index++) {
        const er = res.error.errors[index];
        console.error(`[${er.path}  ${er.message}]`);
      }

      throw new Error(`Test Configuration file ${CONFIGURATION_FILE} is not valid!  See errors Above:`);
    }

    // cache the requested environment...
    const requestEnv: EnvironmentName = res.data.ICT_ENV as EnvironmentName;

    // if this is a CI preview env we need to construct the env with the preview url.
    if (requestEnv === 'preview') {
      this.env = setPreviewEnv(process.env.ICT_BASE_URL);
    } else {
      this.env = getEnv(res.data.ICT_ENV);
    }

    const ctUser: User = {
      email: process.env.ICT_CT_USER_USER_EMAIL,
      password: process.env.ICT_CT_USER_USER_PWD,
      org: 'Superior Uniform',
      role: 'ct_user',
    };

    const ctEngineer: User = {
      email: process.env.ICT_CT_ENGINEER_USER_EMAIL,
      password: process.env.ICT_CT_ENGINEER_USER_PWD,
      org: 'Superior Uniform',
      role: 'ct_engineers',
    };

    const ctConfigurator: User = {
      email: process.env.ICT_CT_CONFIGURATOR_USER_EMAIL,
      password: process.env.ICT_CT_CONFIGURATOR_USER_PWD,
      org: 'Superior Uniform',
      role: 'ct_engineers',
    };

    this.users = new UserManager(ctUser, ctEngineer, ctConfigurator);
  }

  public env: Environment;
  public users: UserManager;
}
