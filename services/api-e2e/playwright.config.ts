import { defineConfig, ReporterDescription } from '@playwright/test';
import path from 'path';

export default defineConfig({
  testDir: './tests',
  timeout: 60000 * 3,
  fullyParallel: true,
  workers: process.env.CI ? 8 : 2,
  reportSlowTests: { max: 60000 * 5, threshold: 60000 * 6 },
  retries: process.env.CI ? 3 : 0,
  expect: {
    timeout: 30000,
  },
  globalSetup: './scripts/global-setup.ts',
  reporter: configureReporters(),
  use: {
    actionTimeout: 30000,
    headless: true,
    navigationTimeout: 60000 * 3,
  },
  projects: [
    {
      name: 'setup',
      testMatch: 'tests/regression/setup.spec.ts',
      testDir: './tests/regression',
    },
    {
      name: 'smoke',
      testDir: 'tests/smoke',
    },
    {
      name: 'regression',
      testDir: 'tests/regression',
      dependencies: ['setup'],
    },
  ],
});

function configureReporters(): ReporterDescription[] {
  const reporters: ReporterDescription[] = [];
  reporters.push([
    'allure-playwright',
    {
      resultsDir: path.join(process.cwd(), 'results', 'allure', 'allure-results'),
      detail: true,
      suiteTitle: true,
      links: {
        jira: {
          urlTemplate: (v) => `https://jira.dematic.net/browse/${v}`,
        },
      },
    },
  ]);
  reporters.push(['html', { open: 'never', outputFolder: './results/html' }]);
  reporters.push(['junit', { outputFile: './results/junit/ict-test-results.xml' }]);
  reporters.push(['list']);
  return reporters;
}
