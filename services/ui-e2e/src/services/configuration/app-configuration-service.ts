import { Response } from '@playwright/test';
import fs from 'fs';

const CACHED_FACILITY_CONFIG_FILE = './facility-config.json';

export class AppConfigurationService {
  private static _instance: AppConfigurationService;

  private constructor() {}

  public static get instance(): AppConfigurationService {
    if (!this._instance) {
      this._instance = new AppConfigurationService();
    }
    return this._instance;
  }

  public async interceptFacilityConfig(response: Promise<Response>): Promise<void> {
    const res = await response;
    if (res.status() !== 200) {
      throw new Error(`Facility config failed with status ${res.status()}`);
    }
    const json = await res.json();
    if (!json) {
      throw new Error(`Facility config is empty`);
    }
    this.setFacilityConfig(json);
  }

  public getFacilityConfig() {
    const rawData = fs.readFileSync(CACHED_FACILITY_CONFIG_FILE, 'utf8');
    return JSON.parse(rawData);
  }

  private setFacilityConfig(json) {
    fs.writeFile(CACHED_FACILITY_CONFIG_FILE, JSON.stringify(json), (err) => {
      if (err) console.log('Error writing facility config cached file:', err);
    });
  }
}
