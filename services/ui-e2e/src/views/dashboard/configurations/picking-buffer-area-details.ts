import { DashboardDefinition, WidgetId, WidgetLibrary } from '@ui-adk/types/dashboard';
import { WidgetType } from '@ui-adk/components/widgets/widget-types';
import { BarChartWidgetId, PieChartWidgetId } from '@ui-adk/components/widgets/widget-id';
import { BarChartWidgetDefinition, PieChartWidgetDefinition, WidgetDefinition } from '@ui-adk/components/widgets/widget-definition';

/**
 *  define the kpi's for the picking buffer area details page.
 */

const barChartWidgets: Map<BarChartWidgetId, WidgetDefinition> = new Map<BarChartWidgetId, BarChartWidgetDefinition>();
barChartWidgets.set('bar-chart-faults-by-aisle', {
  type: 'bar-chart',
  testId: 'bar-chart-faults',
  title: 'By Aisle',
});
barChartWidgets.set('bar-chart-faults-by-level', {
  type: 'bar-chart',
  testId: 'bar-chart-faults',
  title: 'By Level',
});
barChartWidgets.set('bar-chart-faults-by-device-type', {
  type: 'bar-chart',
  testId: 'bar-chart-faults',
  title: 'By Device Type',
});
barChartWidgets.set('bar-chart-faults-by-device-id', {
  type: 'bar-chart',
  testId: 'bar-chart-faults',
  title: 'By Device ID',
});
barChartWidgets.set('bar-chart-faults-by-status', {
  type: 'bar-chart',
  testId: 'bar-chart-faults',
  title: 'By Status',
});

const pieChartWidgets: Map<PieChartWidgetId, WidgetDefinition> = new Map<PieChartWidgetId, PieChartWidgetDefinition>();
pieChartWidgets.set('pie-chart-faults-by-status', {
  type: 'pie-chart',
  testId: 'pie-chart-faults',
  title: 'By Status',
});

const widgets: WidgetLibrary = new Map<WidgetType, Map<WidgetId, WidgetDefinition>>();
widgets.set('bar-chart', barChartWidgets);
widgets.set('pie-chart', pieChartWidgets);

export const picking_buffer_area_details: DashboardDefinition = {
  id: 'ict-picking-buffer-area-details',
  url: 'ict-picking-buffer-area-details',
  title: 'Picking Buffer Area Details',
  widgets,
  apiEndpoints: ['equipment/faults/grouped/count/series'],
};
