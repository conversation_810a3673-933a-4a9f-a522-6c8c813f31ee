import { DataGridWidgetId, WorkstationKpiWWidgetId } from '@ui-adk/components/widgets/widget-id';
import {
  DataGridWidgetDefinition,
  WorkstationKpiWidgetDefinition,
  WidgetDefinition,
} from '@ui-adk/components/widgets/widget-definition';
import { WidgetType } from '@ui-adk/components/widgets/widget-types';
import { DashboardDefinition, WidgetLibrary } from '@ui-adk/types/dashboard';

/**
 *  define the kpi's for the workstation overview page.
 */
const kpiWidgets: Map<WorkstationKpiWWidgetId, WidgetDefinition> = new Map<
  WorkstationKpiWWidgetId,
  WorkstationKpiWidgetDefinition
>();
kpiWidgets.set('total-stations', {
  type: 'total-stations',
  testId: 'total-stations',
  title: 'Total Stations',
});
kpiWidgets.set('station-performance', {
  type: 'station-performance',
  testId: 'station-performance',
  title: 'Station Performance',
});
kpiWidgets.set('logged-in-operators', {
  type: 'logged-in-operators',
  testId: 'logged-in-operators',
  title: 'Logged in Operators',
});
kpiWidgets.set('station-health', {
  type: 'station-health',
  testId: 'station-health',
  title: 'Station Health',
});

const dataTableWidget: Map<DataGridWidgetId, WidgetDefinition> = new Map<DataGridWidgetId, DataGridWidgetDefinition>();
dataTableWidget.set('workstation-list', {
  type: 'data-grid',
  testId: 'workstation-list',
  title: 'Workstation List',
  columns: [
    'Workstation',
    'Status',
    'Work Mode',
    'Workflow Status',
    'Operator ID',
    'Active Time',
    'Starved Time',
    'Idle Time',
    'Blocked Time',
    'Lines/Hour',
    'Quantity/Hour',
    'Weighted Quantity/Hour',
    'Donor Totes/Hour',
    'Order Totes/Hour',
  ],
});

const widgets: WidgetLibrary = new Map<WidgetType, Map<WorkstationKpiWWidgetId, WidgetDefinition>>();
widgets.set('kpi', kpiWidgets);
widgets.set('data-grid', dataTableWidget);

export const ict_workstation_overview: DashboardDefinition = {
  id: 'ict-workstation-overview',
  url: 'ict-workstation-overview',
  title: 'Workstation Overview',
  widgets,
  apiEndpoints: [
    'workstation/metrics/health',
    'workstation/metrics/operators',
    'workstation/metrics/performance',
    'workstation/metrics/summary',
    'workstation/list',
  ],
};
