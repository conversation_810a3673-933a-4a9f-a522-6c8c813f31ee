import { AbstractElementBase } from '@ui-adk/components';
import { Locator, Page } from '@playwright/test';
import { DataGridWidget } from '@ui-adk/components/data-grid/data-grid';
import { AdviceDetails } from '@ui-adk/views/dashboard/configurations/inbound-overview/types/advice-details';
import { InlineLoading } from '@ui-adk/components/carbon/inline-loading';

export class AdviceDetailsDrawer extends AbstractElementBase {
  private errorLoading: Locator;
  private inlineLoading: InlineLoading;

  constructor(page: Page, locator: Locator) {
    super(page, locator);
    this.datagrid = new DataGridWidget(page, this.locator);
    this.errorLoading = this.locator.locator('p');
    this.inlineLoading = new InlineLoading(page, this.locator);
  }

  public datagrid: DataGridWidget;

  public async getErrorLoadingAdviceDetails(): Promise<string> {
    return await this.errorLoading.innerText();
  }

  public async getDetailsLabel(label: AdviceDetails): Promise<Locator> {
    return this.locator.locator('p[class*="_infoLabel"]', { hasText: label });
  }

  public async waitForLoad(): Promise<void> {
    await this.waitForLocator(this.inlineLoading.locator, 'detached');
  }
}
