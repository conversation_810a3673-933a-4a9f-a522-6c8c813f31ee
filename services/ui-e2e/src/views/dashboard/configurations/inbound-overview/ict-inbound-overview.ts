import { DataGridWidgetId, KpiWidgetId } from '@ui-adk/components/widgets/widget-id';
import { DataGridWidgetDefinition, KpiWidgetDefinition, WidgetDefinition } from '@ui-adk/components/widgets/widget-definition';
import { WidgetType } from '@ui-adk/components/widgets/widget-types';
import { DashboardDefinition, WidgetLibrary } from '@ui-adk/types/dashboard';

/**
 *  define the kpi's for the inbound overview page.
 */
const kpiWidgets: Map<KpiWidgetId, WidgetDefinition> = new Map<KpiWidgetId, KpiWidgetDefinition>();
kpiWidgets.set('kpi-inventory-advices-in-progress', {
  type: 'kpi',
  testId: 'kpi-inventory-advices-in-progress',
  title: 'In Progress Advices',
});
kpiWidgets.set('kpi-inventory-advices-outstanding', {
  type: 'kpi',
  testId: 'kpi-inventory-advices-outstanding',
  title: 'Outstanding Advices',
});
kpiWidgets.set('kpi-inventory-advices-finished', {
  type: 'kpi',
  testId: 'kpi-inventory-advices-finished',
  title: 'Finished Advices',
});
kpiWidgets.set('kpi-inventory-advices-cycle-time', {
  type: 'kpi',
  testId: 'kpi-inventory-advices-cycle-time',
  title: 'Advice Cycle Time',
});

const dataTableWidget: Map<DataGridWidgetId, WidgetDefinition> = new Map<DataGridWidgetId, DataGridWidgetDefinition>();
dataTableWidget.set('advice-list', {
  type: 'data-grid',
  testId: 'advice-list',
  title: 'Advice List',
  columns: [
    'Advice ID',
    'Advice Status',
    'Owner ID',
    'Advice Type',
    'Supplier ID',
    'Created Time',
    'Start Time',
    'Finish Time',
    'Advice Lines',
    'Delivered Lines',
    'Overdelivered Lines',
    'Underdelivered Lines',
  ],
});

const widgets: WidgetLibrary = new Map<WidgetType, Map<KpiWidgetId, WidgetDefinition>>();
widgets.set('kpi', kpiWidgets);
widgets.set('data-grid', dataTableWidget);

export const ict_inbound_overview: DashboardDefinition = {
  id: 'ict-inbound-overview',
  url: 'ict-inbound-overview',
  title: 'Inbound Overview',
  widgets,
  apiEndpoints: [
    'inventory/advices/list',
    'inventory/advices/outstanding',
    'inventory/advices/finished',
    'inventory/advices/in-progress',
    'inventory/advices/cycle-time',
    'inventory/advices/finished',
  ],
};
