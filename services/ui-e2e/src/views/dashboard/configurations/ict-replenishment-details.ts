import { ComboChartWidgetId, KpiChartWidgetId } from '@ui-adk/components/widgets/widget-id';
import { WidgetDefinition } from '@ui-adk/components/widgets/widget-definition';
import { DashboardDefinition, WidgetId, WidgetLibrary } from '@ui-adk/types/dashboard';
import { WidgetType } from '@ui-adk/components/widgets/widget-types';

/**
 *  define the kpi's for the replenishment details page.
 */

const kpiChartWidgets: Map<KpiChartWidgetId, WidgetDefinition> = new Map<KpiChartWidgetId, WidgetDefinition>();
kpiChartWidgets.set('kpi-chart-daily-replenishments', {
  type: 'kpi-chart',
  testId: 'kpi-chart-daily-replenishments',
  title: 'Daily Replenishments',
});
kpiChartWidgets.set('kpi-chart-daily-cycle-times', {
  type: 'kpi-chart',
  testId: 'kpi-chart-daily-cycle-times',
  title: 'Daily Cycle Time',
});
kpiChartWidgets.set('kpi-chart-daily-pending-orders', {
  type: 'kpi-chart',
  testId: 'kpi-chart-daily-pending-orders',
  title: 'Daily Pending Orders',
});

const comboChartWidgets: Map<ComboChartWidgetId, WidgetDefinition> = new Map<ComboChartWidgetId, WidgetDefinition>();
comboChartWidgets.set('combo-chart-daily-replenishments-by-shift', {
  type: 'combo-chart',
  testId: 'combo-chart-daily-replenishments-by-shift',
  title: 'Daily Replenishments by Shift',
});
comboChartWidgets.set('combo-chart-replenishment-task-type-data', {
  type: 'combo-chart',
  testId: 'combo-chart-replenishment-task-type-data',
  title: 'Daily Replenishments by Type',
});

const widgets: WidgetLibrary = new Map<WidgetType, Map<WidgetId, WidgetDefinition>>();
widgets.set('kpi-chart', kpiChartWidgets);
widgets.set('combo-chart', comboChartWidgets);

export const ict_replenishment_details: DashboardDefinition = {
  id: 'ict-replenishment-details',
  url: 'ict-replenishment-details',
  title: 'Replenishment Details',
  widgets,
  apiEndpoints: ['inventory/replenishment/details', 'inventory/replenishment/task-type-series'],
};
