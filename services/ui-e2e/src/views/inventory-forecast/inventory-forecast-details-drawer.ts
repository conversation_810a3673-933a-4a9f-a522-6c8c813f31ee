import { AbstractElementBase, TabList } from '@ui-adk/components';
import { Locator, Page } from '@playwright/test';
import { OrdersTab } from '@ui-adk/views/inventory-forecast/components/orders-tab';
import { DetailsDrawerTabs } from '@ui-adk/views/inventory-forecast/types/details-drawer-tabs';
import { ForecastingTab } from '@ui-adk/views/inventory-forecast/components/forecasting-tab';

export class InventoryForecastDetailsDrawer extends AbstractElementBase {
  constructor(page: Page, locator: Locator) {
    super(page, locator);
    this.tabs = new TabList(page, this.locator);
    this.ordersTab = new OrdersTab(page, this.locator);
    this.forecastingTab = new ForecastingTab(page, this.locator);
  }

  public tabs: TabList;
  public ordersTab: OrdersTab;
  public forecastingTab: ForecastingTab;

  public async openTab(tabName: DetailsDrawerTabs): Promise<void> {
    const tab = await this.tabs.getTabByText(tabName);
    await tab.click();
  }
}
