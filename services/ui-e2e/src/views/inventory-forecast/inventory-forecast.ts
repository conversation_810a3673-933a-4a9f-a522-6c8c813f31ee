import { Locator, Page } from '@playwright/test';
import { ITestConfigurationService } from '@ui-adk/services/configuration/test-configuration-service';
import { AbstractViewBase } from '../abstract-view-base';
import { AppViewBarHeader } from '@ui-adk/app/layout/app-view-bar-header';
import { DataGridWidget } from '@ui-adk/components/data-grid/data-grid';
import { ApiRoute } from '@ui-adk/types/api-routes';
import { InventoryForecastDetailsDrawer } from '@ui-adk/views/inventory-forecast/inventory-forecast-details-drawer';
import { Drawer } from '@ui-adk/components/drawer/drawer';

const InventoryListApiRoutes: ApiRoute[] = [] as const;

/**
 * Represents the Inventory Forecast view.
 */
export class InventoryForecast extends AbstractViewBase {
  private header: AppViewBarHeader;
  private dataUpdateTimestamp: Locator;
  private analysisPerformedTimestamp: Locator;

  constructor(page: Page, config: ITestConfigurationService) {
    super(page, page.getByTestId('ict-inventory-forecast'), config, 'ict-inventory-forecast', InventoryListApiRoutes);
    this.locator = page.getByTestId('ict-inventory-forecast');
    this.header = new AppViewBarHeader(page);
    this.datagrid = new DataGridWidget(page, this.locator);
    this.drawer = new Drawer(this.page, InventoryForecastDetailsDrawer);
    this.dataUpdateTimestamp = this.locator.getByTestId('ict-inventory-forecast-data-updated-timestamp');
    this.analysisPerformedTimestamp = this.locator.getByTestId('ict-inventory-forecast-data-sku-forecast-timestamp');
  }

  public datagrid: DataGridWidget;
  public drawer: Drawer<InventoryForecastDetailsDrawer>;

  public async clickSKULink(cellValue: string): Promise<boolean> {
    const columnName = 'SKU';
    const colIndex = await this.datagrid.table.getColumnIndex(columnName);
    const colValues = await this.datagrid.table.getColumn(colIndex);
    const valueIndex = colValues.indexOf(cellValue);
    if (valueIndex !== -1) {
      const cellLocator = this.datagrid.table.rows.nth(valueIndex).locator('a.cds--link');
      await cellLocator.click();
      return true;
    }
    return false;
  }

  public async getDataUpdatedTimeStamp(): Promise<string> {
    const currentValue = await this.dataUpdateTimestamp.innerText();
    const timeValue = currentValue.replace('Inventory Data Updated', '').trim();
    return timeValue;
  }

  public async getAnalysisPerformedTimeStamp(): Promise<string> {
    const currentValue = await this.analysisPerformedTimestamp.innerText();
    const timeValue = currentValue.replace('SKU Forecast Performed', '').trim();
    return timeValue;
  }

  /**
   * Get the title for the current dashboard.
   */
  public async getTitle(): Promise<string> {
    return await this.header.getTitle();
  }

  /**
   * Wait for the dashboard to be fully loaded.
   */
  public override async waitUntilLoaded(): Promise<void> {
    await super.waitUntilLoaded();
    await this.locator.waitFor();
  }
}
