import { Locator, <PERSON> } from '@playwright/test';
import { KeyValuePairCard } from '@ui-adk/components/key-value-pair-card/key-value-pair-card';
import { LoadingView } from '@ui-adk/components/loading-view/loading-view';
import { TabPanel } from '@ui-adk/components/carbon/tab-panel';

export class ForecastingTab extends TabPanel {
  private _loader: LoadingView;

  constructor(page: Page, locator: Locator) {
    super(page, locator);
    this._loader = new LoadingView(page, this.locator);
  }

  public async waitForTabLoad(): Promise<void> {
    await this._loader.waitForInvisible();
  }

  public async getForecastingMetrics(): Promise<KeyValuePairCard> {
    return new KeyValuePairCard(this.page, this.locator.getByTestId('key-value-pair-card'));
  }
}
