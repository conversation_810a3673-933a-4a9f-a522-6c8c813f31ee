import { Locator, <PERSON> } from '@playwright/test';
import { Heading } from '@ui-adk/components/web-components';
import { KeyValuePairCard } from '@ui-adk/components/key-value-pair-card/key-value-pair-card';
import { LoadingView } from '@ui-adk/components/loading-view/loading-view';
import { TabPanel } from '@ui-adk/components/carbon/tab-panel';

export class OrdersTab extends TabPanel {
  private _openOrders: Heading;
  private _loader: LoadingView;
  private _noOrdersError: Locator;

  constructor(page: Page, locator: Locator) {
    super(page, locator);
    this._openOrders = new Heading(page, this.locator, 3);
    this._loader = new LoadingView(page, this.locator);
    this._noOrdersError = this.locator.locator('p');
  }

  public async getTotalOrders(): Promise<string> {
    return await this._openOrders.getText();
  }

  public async getNoOrderError(): Promise<string> {
    return await this._noOrdersError.innerText();
  }

  public async waitForTabLoad(): Promise<void> {
    await this._loader.waitForInvisible();
  }

  public async getOpenOrders(): Promise<KeyValuePairCard[]> {
    const orders: KeyValuePairCard[] = [];
    const ordersCount = await this.locator.getByTestId('key-value-pair-card').count();
    for (let i = 0; i < ordersCount; i++) {
      const order = new KeyValuePairCard(this.page, this.locator.getByTestId('key-value-pair-card').nth(i));
      orders.push(order);
    }
    return orders;
  }
}
