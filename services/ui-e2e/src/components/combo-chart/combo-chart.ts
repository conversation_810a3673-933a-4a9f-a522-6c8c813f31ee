import { AbstractElementBase } from '@ui-adk/components';
import { Locator, Page } from '@playwright/test';
import { Tooltip } from '@ui-adk/components/carbon/tooltip';
import { ChartType } from '@ui-adk/components/types/chart-type';

/**
 * UI wrapper for the carbon ComboChart component.
 */
export class Combo<PERSON>hart extends AbstractElementBase {
  constructor(page: Page, locator: Locator) {
    super(page, locator.locator('div.cds--chart-holder'));
    this.tooltip = new Tooltip(page, this.locator);
    this.legendItem = this.locator.locator('.legend-item');
    this.axisLeft = this.locator.locator('g[aria-label="left axis"]');
    this.axisBottom = this.locator.locator('g[aria-label="bottom axis"]');
  }

  public tooltip: Tooltip;
  public legendItem: Locator;
  public axisLeft: Locator;
  public axisBottom: Locator;

  public chart(type: ChartType): Locator {
    switch (type) {
      case 'scatter':
        return this.locator.locator('[aria-label*="scatter"]');
      case 'stacked-bar':
        return this.locator.locator('[aria-label*="stacked bar"]');
      default:
        throw new Error(`Unsupported ChartType: ${type}`);
    }
  }

  public async getLegendItems(): Promise<string[]> {
    const items = await this.legendItem.all();
    return Promise.all(items.map(async (item) => (await item.innerText())?.trim() || ''));
  }
}
