import { Locator, <PERSON> } from '@playwright/test';
import { Header } from '@ui-adk/components/carbon/header';
import { AbstractWidgetComponent } from '../abstract-widget-base';

/**
 * UI wrapper for the the dashboard workstation station health widget component.
 */
export class StationHealthWidget extends AbstractWidgetComponent {
  constructor(page: Page, locator: Locator) {
    super(page, locator, 'station-health');
    this.header = new Header(page, this.locator.getByTestId('widget-heading'));
  }

  public header: Header;
}
