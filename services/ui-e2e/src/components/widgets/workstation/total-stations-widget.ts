import { Locator, <PERSON> } from '@playwright/test';
import { Header } from '@ui-adk/components/carbon/header';
import { AbstractWidgetComponent } from '../abstract-widget-base';

/**
 * UI wrapper for the dashboard workstation total stations widget component.
 */
export class TotalStationsWidget extends AbstractWidgetComponent {
  private _totalWorkstations: Locator;
  private _activeWorkstationCount: Locator;
  private _inactiveWorkstationCount: Locator;
  private _pickingWorkstationModeCount: Locator;
  private _pickingWorkstationModeLabel: Locator;
  private _otherWorkstationModeCount: Locator;
  private _otherWorkstationModeLabel: Locator;

  constructor(page: Page, locator: Locator) {
    super(page, locator, 'total-stations');
    this.header = new Header(page, this.locator.getByTestId('widget-heading'));
    this._totalWorkstations = this.locator.getByTestId('total-workstations');
    this._activeWorkstationCount = this.locator.getByTestId('labeled-value-Active-value');
    this._inactiveWorkstationCount = this.locator.getByTestId('labeled-value-Inactive-value');
    this._pickingWorkstationModeCount = this.locator.getByTestId('labeled-value-Picking-value');
    this._pickingWorkstationModeLabel = this.locator.getByTestId('labeled-value-Picking-label');
    this._otherWorkstationModeCount = this.locator.getByTestId('labeled-value-Other-value');
    this._otherWorkstationModeLabel = this.locator.getByTestId('labeled-value-Other-label');
  }

  public header: Header;

  public async getWorkstationCount() {
    return await this._totalWorkstations.innerText();
  }

  public async getWorkstationActiveCount() {
    return await this._activeWorkstationCount.innerText();
  }

  public async getWorkstationInActiveCount() {
    return await this._inactiveWorkstationCount.innerText();
  }

  public async getWorkstationModeLabelByType(type: string): Promise<string> {
    const labelLocator = this.locator.getByTestId(`labeled-value-${type}-label`);
    return await labelLocator.innerText();
  }

  public async getWorkstationModeCountByType(type: string): Promise<string> {
    const valueLocator = this.locator.getByTestId(`labeled-value-${type}-value`);
    return await valueLocator.innerText();
  }
}
