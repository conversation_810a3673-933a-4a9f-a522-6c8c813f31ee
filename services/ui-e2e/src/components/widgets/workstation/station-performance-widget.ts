import { Locator, <PERSON> } from '@playwright/test';
import { Header } from '@ui-adk/components/carbon/header';
import { AbstractWidgetComponent } from '../abstract-widget-base';

/**
 * UI wrapper for the the dashboard workstation station performance widget component.
 */
export class StationPerformanceWidget extends AbstractWidgetComponent {
  constructor(page: Page, locator: Locator) {
    super(page, locator, 'station-performance');
    this.header = new Header(page, this.locator.getByTestId('widget-heading'));
  }

  public header: Header;
}
