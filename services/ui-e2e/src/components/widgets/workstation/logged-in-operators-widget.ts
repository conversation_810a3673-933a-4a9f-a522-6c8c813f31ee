import { Locator, <PERSON> } from '@playwright/test';
import { Header } from '@ui-adk/components/carbon/header';
import { AbstractWidgetComponent } from '../abstract-widget-base';

/**
 * UI wrapper for the the dashboard workstation logged in operators widget component.
 */
export class LoggedInOperatorsWidget extends AbstractWidgetComponent {
  private _totalOperators: Locator;
  private _linesPerHourActual: Locator;
  private _linesPerHourTarget: Locator;

  constructor(page: Page, locator: Locator) {
    super(page, locator, 'logged-in-operators');
    this.header = new Header(page, this.locator.getByTestId('widget-heading'));

    this._totalOperators = this.locator.getByTestId('total-operators');
    this._linesPerHourActual = this.locator.getByTestId('labeled-value-Actual-value');
    this._linesPerHourTarget = this.locator.getByTestId('labeled-value-Expected-value');
  }

  public header: Header;

  public async getActualPicksPerHour() {
    return await this._linesPerHourActual.innerText();
  }

  public async getTargetPicksPerHour() {
    return (await this._linesPerHourTarget.innerText()).split(' ')[1];
  }

  public async getTotalOperators() {
    return await this._totalOperators.innerText();
  }
}
