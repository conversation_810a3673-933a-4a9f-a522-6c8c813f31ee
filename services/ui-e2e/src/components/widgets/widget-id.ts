const _KpiWidgetIds = [
  'kpi-facility-orders-shipped',
  'kpi-orders-shipped',
  'kpi-orders-fulfillment-outstanding',
  'kpi-orders-facility-outstanding',
  'kpi-orders-projected-fulfillment',
  'kpi-inventory-storage-utilization',
  'kpi-orders-pick-cycle-counts',
  'kpi-orders-progress',
  'kpi-facility-orders-progress',
  'kpi-orders-facility-estimated-completion',
  'kpi-orders-lines-progress',
  'kpi-operators-active',
  'kpi-orders-lines-progress',
  'kpi-facility-orders-lines-progress',
  'kpi-orders-throughput-rate',
  'kpi-facility-orders-throughput-rate',
  'kpi-orders-customer-line-throughput-rate',
  'kpi-orders-facility-line-throughput-rate',
  'kpi-facility-orders-cycle-time',
  'kpi-orders-customer-line-progress',
  'kpi-orders-customer-cycle-time',
  'kpi-inventory-advices-in-progress',
  'kpi-inventory-advices-outstanding',
  'kpi-inventory-advices-finished',
  'kpi-inventory-advices-cycle-time',
] as const;
export type KpiWidgetId = (typeof _KpiWidgetIds)[number];

const _KpiChartWidgetIds = [
  'kpi-chart-inventory-wms-stock-distribution-at-percentage',
  'kpi-chart-inventory-wms-stock-distribution-no-percentage',
  'kpi-chart-inventory-wms-stock-distribution-over-percentage',
  'kpi-chart-inventory-wms-stock-distribution-under-percentage',
  'kpi-chart-daily-replenishments',
  'kpi-chart-daily-cycle-times',
  'kpi-chart-daily-pending-orders',
] as const;
export type KpiChartWidgetId = (typeof _KpiChartWidgetIds)[number];

const _BarChartWidgetIds = [
  'bar-chart-faults-by-aisle',
  'bar-chart-faults-by-level',
  'bar-chart-faults-by-device-type',
  'bar-chart-faults-by-device-id',
  'bar-chart-faults-by-status',
] as const;
export type BarChartWidgetId = (typeof _BarChartWidgetIds)[number];

const _PieChartWidgetIds = ['pie-chart-faults-by-status'] as const;
export type PieChartWidgetId = (typeof _PieChartWidgetIds)[number];

const _DataGridIds = ['advice-list', 'workstation-list', 'curated-data-table'] as const;
export type DataGridWidgetId = (typeof _DataGridIds)[number];

const _ComboChartWidgetIds = ['combo-chart-daily-replenishments-by-shift', 'combo-chart-replenishment-task-type-data'] as const;
export type ComboChartWidgetId = (typeof _ComboChartWidgetIds)[number];

export type WorkstationKpiWWidgetId = 'total-stations' | 'station-performance' | 'logged-in-operators' | 'station-health';

export type WidgetId =
  | KpiWidgetId
  | KpiChartWidgetId
  | DataGridWidgetId
  | WorkstationKpiWWidgetId
  | 'outbound-overview-areas'
  | BarChartWidgetId
  | PieChartWidgetId
  | ComboChartWidgetId;
