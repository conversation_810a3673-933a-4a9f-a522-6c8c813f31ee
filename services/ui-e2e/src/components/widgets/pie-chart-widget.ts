import { Locator, <PERSON> } from '@playwright/test';
import { Header } from '@ui-adk/components/carbon/header';
import { AbstractWidgetComponent } from './abstract-widget-base';

/**
 * UI wrapper for the dashboard Pie Chart widget component.
 */
export class PieChartWidget extends AbstractWidgetComponent {
  constructor(page: Page, locator: Locator) {
    super(page, locator, 'pie-chart');
    this.header = new Header(page, this.locator.getByTestId('widget-heading'));
  }

  public header: Header;
}
