import { AbstractElementBase } from '@ui-adk/components';
import { Locator, Page } from '@playwright/test';

/**
 * UI wrapper for the carbon Tooltip component.
 */
export class Tooltip extends AbstractElementBase {
  constructor(page: Page, locator: Locator) {
    super(page, locator.locator('div.cds--cc--tooltip:not(.hidden)'));
  }

  public getDatapointTooltipByLabel(label: string): Locator {
    return this.locator.locator('li .datapoint-tooltip').filter({
      has: this.page.locator('.label p', { hasText: label }),
    });
  }
}
