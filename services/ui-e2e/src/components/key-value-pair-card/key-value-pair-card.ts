import { AbstractElementBase } from '@ui-adk/components';
import { Locator, Page } from '@playwright/test';

/**
 * UI wrapper for the ICT custom Key Value Pair Card component.
 */
export class KeyValuePairCard extends AbstractElementBase {
  private _heading: Locator;
  private _subHeading: Locator;
  private _keyValueRow: Locator;

  constructor(page: Page, locator: Locator) {
    super(page, locator);
    this._heading = locator.getByTestId('card-heading');
    this._subHeading = locator.getByTestId('card-subheading');
    this._keyValueRow = locator.locator('[class*="_keyValueRow"]');
  }

  public async getHeading() {
    return await this._heading.innerText();
  }

  public async getSubHeading() {
    return await this._subHeading.innerText();
  }

  public async getKeyValuePairs(): Promise<Map<string, string>> {
    const keyValueMap = new Map<string, string>();
    const count = await this._keyValueRow.count();
    for (let i = 0; i < count; i++) {
      const row = this._keyValueRow.nth(i);
      const keyElement = row.locator('[class*="_key"]');
      const valueElement = row.locator('[class*="_value"]');
      const key = (await keyElement.innerText())?.trim();
      const value = (await valueElement.innerText())?.trim();
      if (key !== undefined && value !== undefined) {
        keyValueMap.set(key, value);
      }
    }
    return keyValueMap;
  }
}
