import { Locator, Page } from '@playwright/test';
import { AbstractElementBase, Header } from '@ui-adk/components';

export type Content<T> = new (page: Page, selector: Locator) => T;

/**
 * UI wrapper for the ICT custom Drawer component.
 */

export class Drawer<T> extends AbstractElementBase {
  private closeButton: Locator;
  constructor(page: Page, content: Content<T>) {
    super(page, page.getByTestId('drawer'));

    this.header = new Header(page, this.locator.getByTestId('drawer-title'));
    this.closeButton = this.locator.getByTestId('close-button');
    this.content = new content(page, this.locator);
  }

  public header: Header;
  public content: T;

  public async closeDetailsDrawer() {
    await this.closeButton.click();
    await this.waitForLocator(this.locator, 'detached');
  }
}
