import { ApiEndpoint, ApiRoute } from '@ui-adk/types/api-routes';

export type DashboardTestData = {
  data: unknown;
  endpoint: ApiEndpoint;
};

export default class DashboardMockData {
  public static ReplenishmentDetails(): DashboardTestData {
    return {
      data: {
        averageDailyReplenishments: 487,
        averagePendingOrders: 820,
        averageCycleTimes: null,
        dailyReplenishments: [
          {
            name: '2025-06-06',
            value: 318,
          },
          {
            name: '2025-06-07',
            value: 0,
          },
          {
            name: '2025-06-08',
            value: 0,
          },
          {
            name: '2025-06-09',
            value: 528,
          },
          {
            name: '2025-06-10',
            value: 476,
          },
          {
            name: '2025-06-11',
            value: 552,
          },
          {
            name: '2025-06-12',
            value: 561,
          },
        ],
        dailyPendingOrders: [
          {
            name: '2025-06-06',
            value: 611,
          },
          {
            name: '2025-06-07',
            value: 0,
          },
          {
            name: '2025-06-08',
            value: 0,
          },
          {
            name: '2025-06-09',
            value: 919,
          },
          {
            name: '2025-06-10',
            value: 815,
          },
          {
            name: '2025-06-11',
            value: 1003,
          },
          {
            name: '2025-06-12',
            value: 753,
          },
        ],
        dailyCycleTimes: [
          {
            name: '2025-06-06',
            value: 0,
          },
          {
            name: '2025-06-07',
            value: 0,
          },
          {
            name: '2025-06-08',
            value: 0,
          },
          {
            name: '2025-06-09',
            value: 0,
          },
          {
            name: '2025-06-10',
            value: 0,
          },
          {
            name: '2025-06-11',
            value: 0,
          },
          {
            name: '2025-06-12',
            value: 0,
          },
        ],
        shiftData: [
          {
            firstShift: [
              {
                name: '2025-06-06',
                value: 248,
              },
              {
                name: '2025-06-07',
                value: 0,
              },
              {
                name: '2025-06-08',
                value: 0,
              },
              {
                name: '2025-06-09',
                value: 267,
              },
              {
                name: '2025-06-10',
                value: 275,
              },
              {
                name: '2025-06-11',
                value: 252,
              },
              {
                name: '2025-06-12',
                value: 368,
              },
            ],
            secondShift: [
              {
                name: '2025-06-06',
                value: 45,
              },
              {
                name: '2025-06-07',
                value: 0,
              },
              {
                name: '2025-06-08',
                value: 0,
              },
              {
                name: '2025-06-09',
                value: 253,
              },
              {
                name: '2025-06-10',
                value: 180,
              },
              {
                name: '2025-06-11',
                value: 196,
              },
              {
                name: '2025-06-12',
                value: 155,
              },
            ],
            thirdShift: [
              {
                name: '2025-06-06',
                value: 25,
              },
              {
                name: '2025-06-07',
                value: 0,
              },
              {
                name: '2025-06-08',
                value: 0,
              },
              {
                name: '2025-06-09',
                value: 8,
              },
              {
                name: '2025-06-10',
                value: 21,
              },
              {
                name: '2025-06-11',
                value: 104,
              },
              {
                name: '2025-06-12',
                value: 38,
              },
            ],
          },
        ],
        shiftTimes: {
          first_endTime: '15:29',
          third_endTime: '06:59',
          second_endTime: '23:59',
          first_startTime: '7:00',
          third_startTime: '00:00',
          second_startTime: '15:30',
        },
      },
      endpoint: {
        path: `inventory/replenishment/details` as ApiRoute,
      },
    };
  }

  public static ReplenishmentTaskTypeSeries(): DashboardTestData {
    return {
      data: {
        taskTypeData: [
          {
            demand: [
              {
                value: 152,
                name: '2025-06-06',
              },
              {
                value: 0,
                name: '2025-06-07',
              },
              {
                value: 0,
                name: '2025-06-08',
              },
              {
                value: 285,
                name: '2025-06-09',
              },
              {
                value: 263,
                name: '2025-06-10',
              },
              {
                value: 313,
                name: '2025-06-11',
              },
              {
                value: 252,
                name: '2025-06-12',
              },
            ],
            topOff: [
              {
                value: 146,
                name: '2025-06-06',
              },
              {
                value: 0,
                name: '2025-06-07',
              },
              {
                value: 0,
                name: '2025-06-08',
              },
              {
                value: 217,
                name: '2025-06-09',
              },
              {
                value: 190,
                name: '2025-06-10',
              },
              {
                value: 211,
                name: '2025-06-11',
              },
              {
                value: 272,
                name: '2025-06-12',
              },
            ],
            relocation: [
              {
                value: 20,
                name: '2025-06-06',
              },
              {
                value: 0,
                name: '2025-06-07',
              },
              {
                value: 0,
                name: '2025-06-08',
              },
              {
                value: 26,
                name: '2025-06-09',
              },
              {
                value: 23,
                name: '2025-06-10',
              },
              {
                value: 28,
                name: '2025-06-11',
              },
              {
                value: 37,
                name: '2025-06-12',
              },
            ],
          },
        ],
      },
      endpoint: {
        path: `inventory/replenishment/task-type-series` as ApiRoute,
      },
    };
  }
}
