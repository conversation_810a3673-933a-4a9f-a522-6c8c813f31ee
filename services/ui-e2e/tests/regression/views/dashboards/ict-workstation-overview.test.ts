import { expect } from '@playwright/test';
import { TestGroup } from '@ui-tests/test-groups';
import { test } from '@ui-tests/fixtures/regression-fixture';
import { IctMenu } from '@ui-adk/ict-menu';
import { TotalStationsWidget, LoggedInOperatorsWidget } from '@ui-adk/components/widgets/workstation';
import { DataGridWidget } from '@ui-adk/components/data-grid/data-grid';
import { DataGridWidgetDefinition } from '@ui-adk/components/widgets/widget-definition';
import { ict_workstation_overview } from '@ui-adk/views/dashboard/configurations/ict-workstation-overview';
import { getDateFilter } from '@ui-tests/utils/date-util';

const dashboardDefinition = ict_workstation_overview;

test.describe(`ict-workstation-overview - ${TestGroup.Regression}`, async () => {
  test.use({ dashboardId: 'workstation-overview' });

  test(`[@C3523159] - Verify Workstation Overview page header.`, async ({ dashboard }) => {
    expect(await dashboard.getTitle()).toEqual(dashboardDefinition.title);
  });

  test(`[@C3691631] - Verify dashboard route:`, async ({ dashboard }) => {
    expect(await dashboard.getUrl()).toContain(dashboardDefinition.url);
  });

  test(`[@C3691632] - Verify user can navigate via menu`, async ({ controlTower, dashboard }) => {
    expect(await controlTower.sideMenu.isVisible()).toBeTruthy();
    await controlTower.sideMenu.selectItem(IctMenu.OperationsVisibility.Workstation.WorkstationOverview);
    expect(await dashboard.getTitle()).toEqual(dashboardDefinition.title);
  });

  test(`[@C3530859] - Verify Logged in Operators shows total number of logged in operators at workstation.`, async ({
    dashboard,
    api,
  }) => {
    const kpiCard = new LoggedInOperatorsWidget(dashboard.page, dashboard.locator);
    expect(kpiCard.isVisible()).toBeTruthy();
    const response = await api.get('./workstation/metrics/operators');
    if (response.status() === 200) {
      const data = await response.json();
      const totalOperators = await kpiCard.getTotalOperators();
      expect(parseInt(totalOperators, 10)).toBe(data.totalOperators);
    }
  });

  test(`[@C3530860] - Verify Logged in Operators shows the actual number of lines per hour for all active workstations.`, async ({
    dashboard,
    api,
  }) => {
    const kpiCard = new LoggedInOperatorsWidget(dashboard.page, dashboard.locator);
    expect(kpiCard.isVisible()).toBeTruthy();
    const response = await api.get('./workstation/metrics/operators');
    if (response.status() === 200) {
      const data = await response.json();
      const actualLinesPerHour = await kpiCard.getActualPicksPerHour();
      expect(parseInt(actualLinesPerHour, 10)).toBe(data.actualLinesPerHour);
    }
  });

  test(`[@C3530861] - Verify Logged in Operators shows shows the expected number of lines per hour for all active workstations.`, async ({
    dashboard,
    api,
  }) => {
    const kpiCard = new LoggedInOperatorsWidget(dashboard.page, dashboard.locator);
    expect(kpiCard.isVisible()).toBeTruthy();
    const response = await api.get('./workstation/metrics/operators');
    if (response.status() === 200) {
      const data = await response.json();
      const targetLinesPerHour = await kpiCard.getTargetPicksPerHour();
      expect(parseInt(targetLinesPerHour, 10)).toBe(data.targetLinesPerHour);
    }
  });

  test(`[@C3574276] - Test that the Total Total Stations card shows the total stations.`, async ({ dashboard, api }) => {
    const kpiCard = new TotalStationsWidget(dashboard.page, dashboard.locator);
    expect(kpiCard.isVisible()).toBeTruthy();
    const response = await api.get('./workstation/metrics/summary', { params: getDateFilter('today') });
    if (response.status() === 200) {
      const data = await response.json();
      const totalStations = await kpiCard.getWorkstationCount();
      expect(parseInt(totalStations, 10)).toBe(data.totalWorkstations);
    }
  });

  test(`[@C3536497] - Test that the Total Total Stations card Stations card shows the total Active and Inactive stations.`, async ({
    dashboard,
    api,
  }) => {
    const kpiCard = new TotalStationsWidget(dashboard.page, dashboard.locator);
    expect(kpiCard.isVisible()).toBeTruthy();
    const response = await api.get('./workstation/metrics/summary', { params: getDateFilter('today') });
    if (response.status() === 200) {
      const data = await response.json();
      const activeStations = await kpiCard.getWorkstationActiveCount();
      const inactiveStations = await kpiCard.getWorkstationInActiveCount();
      expect(parseInt(activeStations, 10)).toBe(data.status.find((el) => el.type === 'Active').count);
      expect(parseInt(inactiveStations, 10)).toBe(data.status.find((el) => el.type === 'Inactive').count);
    }
  });

  test(`[@C3536499] - Test that the Total Stations shows the number of stations in either Picking or Other modes.`, async ({
    api,
    dashboard,
  }) => {
    const kpiCard = new TotalStationsWidget(dashboard.page, dashboard.locator);
    expect(kpiCard.isVisible()).toBeTruthy();
    const response = await api.get('./workstation/metrics/summary', { params: getDateFilter('today') });
    if (response.status() === 200) {
      const data = await response.json();
      for (const mode of data.workstationMode) {
        const actualLabel = await kpiCard.getWorkstationModeLabelByType(mode.type);
        const actualValue = await kpiCard.getWorkstationModeCountByType(mode.type);
        expect.soft(actualLabel).toBe(mode.type);
        expect.soft(parseInt(actualValue, 10)).toBe(mode.count);
      }
    }
  });

  test(`[@C3562113] - Test that the number of stations in the Total Stations card matches the number of stations in the Workstation Stations table.`, async ({
    api,
    dashboard,
  }) => {
    const kpiCard = new TotalStationsWidget(dashboard.page, dashboard.locator);
    expect(kpiCard.isVisible()).toBeTruthy();
    const response = await api.get('./workstation/list', { params: getDateFilter('today') });
    if (response.status() === 200) {
      const data = await response.json();
      const workstationCount = await kpiCard.getWorkstationCount();
      expect.soft(parseInt(workstationCount, 10)).toBe(data.workstationList.length);
    }
  });

  test(`[@C3574281] - Verify Workstation List Table is visible.`, async ({ dashboard }) => {
    const dataTableConfig: DataGridWidgetDefinition = dashboardDefinition.widgets
      .get('data-grid')
      .get('workstation-list') as DataGridWidgetDefinition;
    const dataTable = new DataGridWidget(dashboard.page, dashboard.locator.getByTestId(dataTableConfig.testId));
    expect(await dataTable.isVisible()).toBeTruthy();
    expect(await dataTable.getHeading()).toEqual(dataTableConfig.title);
  });
});
