import { expect } from '@playwright/test';
import { TestGroup } from '@ui-tests/test-groups';
import { test } from '@ui-tests/fixtures/regression-fixture';
import { IctMenu } from '@ui-adk/ict-menu';
import { ChartWidgetDefinition, KpiChartWidgetDefinition } from '@ui-adk/components/widgets/widget-definition';
import { ict_replenishment_details } from '@ui-adk/views/dashboard/configurations/ict-replenishment-details';
import { ComboChartWidget } from '@ui-adk/components';
import { KpiChartWidget } from '@ui-adk/components/widgets/kpi-chart-widget';
import { IctApiSimulator } from '@ui-tests/utils/ict-api-simulator';
import TestData from '@ui-tests/regression/views/dashboards/data/test-data';

const dashboardDefinition = ict_replenishment_details;

test.describe(`Replenishment Details`, async () => {
  test.use({ dashboardId: 'replenishment-details' });

  test.describe(`Replenishment Details - Mock data`, async () => {
    test(`[@C3659299] - Test that selecting Replenishment Details menu option takes user to Replenishment Details screen - ${TestGroup.Regression}`, async ({
      dashboard,
    }) => {
      expect(await dashboard.getUrl()).toContain(dashboardDefinition.url);
    });
    test(`[@C3659298] - Test that Replenishment Details menu option is available under Inventory Menu group - ${TestGroup.Regression}`, async ({
      controlTower,
      dashboard,
    }) => {
      expect(await controlTower.sideMenu.isVisible()).toBeTruthy();
      await controlTower.sideMenu.selectItem(IctMenu.OperationsVisibility.ReplenishmentDetails);
      expect(await dashboard.getTitle()).toEqual(dashboardDefinition.title);
    });
    test(`[@C3659371] - Test that there is KPI card for Daily Replenishments - ${TestGroup.Regression}`, async ({ dashboard }) => {
      const kpiChart = dashboardDefinition.widgets
        .get('kpi-chart')
        .get('kpi-chart-daily-replenishments') as KpiChartWidgetDefinition;
      const kpiChartWidget = await dashboard.getWidget<KpiChartWidget>(kpiChart);
      expect(kpiChartWidget.locator).toBeVisible();
      expect(await kpiChartWidget.header.getTitle()).toEqual(kpiChart.title);
    });
    test(`[@C3659372] - Test that there is a KPI card for Order Cycle Time - ${TestGroup.Regression}`, async ({ dashboard }) => {
      const kpiChart = dashboardDefinition.widgets.get('kpi-chart').get('kpi-chart-daily-cycle-times') as KpiChartWidgetDefinition;
      const kpiChartWidget = await dashboard.getWidget<KpiChartWidget>(kpiChart);
      expect(kpiChartWidget.locator).toBeVisible();
      expect(await kpiChartWidget.header.getTitle()).toEqual(kpiChart.title);
    });
    test(`[@C3659373] - Test that there is a KPI card for Daily Pending Orders - ${TestGroup.Regression}`, async ({
      dashboard,
    }) => {
      const kpiChart = dashboardDefinition.widgets
        .get('kpi-chart')
        .get('kpi-chart-daily-pending-orders') as KpiChartWidgetDefinition;
      const kpiChartWidget = await dashboard.getWidget<KpiChartWidget>(kpiChart);
      expect(kpiChartWidget.locator).toBeVisible();
      expect(await kpiChartWidget.header.getTitle()).toEqual(kpiChart.title);
    });
    test(`[@C3659374] - Test that there is a chart for Daily Replenishments by Type - ${TestGroup.Regression}`, async ({
      dashboard,
    }) => {
      const comboChart = dashboardDefinition.widgets
        .get('combo-chart')
        .get('combo-chart-replenishment-task-type-data') as ChartWidgetDefinition;
      const comboChartWidget = await dashboard.getWidget<ComboChartWidget>(comboChart);
      expect(comboChartWidget.locator).toBeVisible();
      expect(await comboChartWidget.header.getTitle()).toEqual(comboChart.title);
    });
    test(`[@C3659375] - Test that there is a chart for Daily Replenishments by Shift - ${TestGroup.Regression}`, async ({
      dashboard,
    }) => {
      const comboChart = dashboardDefinition.widgets
        .get('combo-chart')
        .get('combo-chart-daily-replenishments-by-shift') as ChartWidgetDefinition;
      const comboChartWidget = await dashboard.getWidget<ComboChartWidget>(comboChart);
      expect(comboChartWidget.locator).toBeVisible();
      expect(await comboChartWidget.header.getTitle()).toEqual(comboChart.title);
    });
    test(`[@C3659376] - Test that chart for Daily Replenishments by Type shows data for Demand, Top Off, and Relocation - ${TestGroup.Regression}`, async ({
      dashboard,
    }) => {
      const mockApi = new IctApiSimulator(dashboard.page);
      const taskTypeData = TestData.ReplenishmentTaskTypeSeries();
      await mockApi.mockResponse(taskTypeData.endpoint, 200, taskTypeData.data);
      const comboChart = dashboardDefinition.widgets
        .get('combo-chart')
        .get('combo-chart-replenishment-task-type-data') as ChartWidgetDefinition;
      const comboChartWidget = await dashboard.getWidget<ComboChartWidget>(comboChart);
      await comboChartWidget.waitForLoad();
      expect(await comboChartWidget.comboChart.getLegendItems()).toEqual(['Demand', 'Top Off', 'Relocation']);
    });
    test(`[@C3659377] - Test that chart for Daily Replenishments by Shift shows data for First, Second, and Third shifts - ${TestGroup.Regression}`, async ({
      dashboard,
    }) => {
      const mockApi = new IctApiSimulator(dashboard.page);
      const replenishmentDetails = TestData.ReplenishmentDetails();
      await mockApi.mockResponse(replenishmentDetails.endpoint, 200, replenishmentDetails.data);
      const comboChart = dashboardDefinition.widgets
        .get('combo-chart')
        .get('combo-chart-daily-replenishments-by-shift') as ChartWidgetDefinition;
      const comboChartWidget = await dashboard.getWidget<ComboChartWidget>(comboChart);
      await comboChartWidget.waitForLoad();
      expect(await comboChartWidget.comboChart.getLegendItems()).toEqual(['First Shift', 'Second Shift', 'Third Shift']);
    });
    test(`[@C3659794] - Test that hovering over a data point on Daily Replenishments card shows data point details - ${TestGroup.Charts}`, async ({
      dashboard,
    }) => {
      const mockApi = new IctApiSimulator(dashboard.page);
      const replenishmentDetails = TestData.ReplenishmentDetails();
      await mockApi.mockResponse(replenishmentDetails.endpoint, 200, replenishmentDetails.data);
      const kpiChart = dashboardDefinition.widgets
        .get('kpi-chart')
        .get('kpi-chart-daily-replenishments') as KpiChartWidgetDefinition;
      const kpiChartWidget = await dashboard.getWidget<KpiChartWidget>(kpiChart);
      await dashboard.waitUntilLoaded();
      await kpiChartWidget.comboChart.chart('scatter').locator('circle').first().hover();
      await expect(kpiChartWidget.comboChart.tooltip.getDatapointTooltipByLabel('Date')).toBeVisible();
      await expect(kpiChartWidget.comboChart.tooltip.getDatapointTooltipByLabel('Replenishments')).toBeVisible();
      await expect(kpiChartWidget.comboChart.tooltip.getDatapointTooltipByLabel('Group')).toBeVisible();
    });
    test(`[@C3659795] - Test that hovering over a data point on Order Cycle Time card shows data point details - ${TestGroup.Charts}`, async ({
      dashboard,
    }) => {
      const mockApi = new IctApiSimulator(dashboard.page);
      const replenishmentDetails = TestData.ReplenishmentDetails();
      await mockApi.mockResponse(replenishmentDetails.endpoint, 200, replenishmentDetails.data);
      const kpiChart = dashboardDefinition.widgets.get('kpi-chart').get('kpi-chart-daily-cycle-times') as KpiChartWidgetDefinition;
      const kpiChartWidget = await dashboard.getWidget<KpiChartWidget>(kpiChart);
      await dashboard.waitUntilLoaded();
      await kpiChartWidget.comboChart.chart('scatter').locator('circle').first().hover();
      await expect(kpiChartWidget.comboChart.tooltip.getDatapointTooltipByLabel('Date')).toBeVisible();
      await expect(kpiChartWidget.comboChart.tooltip.getDatapointTooltipByLabel('Minutes')).toBeVisible();
      await expect(kpiChartWidget.comboChart.tooltip.getDatapointTooltipByLabel('Group')).toBeVisible();
    });
    test(`[@C3659796] - Test that hovering over a data point on Daily Pending Orders card shows data point details - ${TestGroup.Charts}`, async ({
      dashboard,
    }) => {
      const mockApi = new IctApiSimulator(dashboard.page);
      const replenishmentDetails = TestData.ReplenishmentDetails();
      await mockApi.mockResponse(replenishmentDetails.endpoint, 200, replenishmentDetails.data);
      const kpiChart = dashboardDefinition.widgets
        .get('kpi-chart')
        .get('kpi-chart-daily-pending-orders') as KpiChartWidgetDefinition;
      const kpiChartWidget = await dashboard.getWidget<KpiChartWidget>(kpiChart);
      await dashboard.waitUntilLoaded();
      await kpiChartWidget.comboChart.chart('scatter').locator('circle').first().hover();
      await expect(kpiChartWidget.comboChart.tooltip.getDatapointTooltipByLabel('Date')).toBeVisible();
      await expect(kpiChartWidget.comboChart.tooltip.getDatapointTooltipByLabel('Orders')).toBeVisible();
      await expect(kpiChartWidget.comboChart.tooltip.getDatapointTooltipByLabel('Group')).toBeVisible();
    });
    test(`[@C3659798] - Test that hovering over a data point on Daily Replenishments by Shift Chart shows data point details - ${TestGroup.Charts}`, async ({
      dashboard,
    }) => {
      const mockApi = new IctApiSimulator(dashboard.page);
      const replenishmentDetails = TestData.ReplenishmentDetails();
      await mockApi.mockResponse(replenishmentDetails.endpoint, 200, replenishmentDetails.data);
      const comboChart = dashboardDefinition.widgets
        .get('combo-chart')
        .get('combo-chart-daily-replenishments-by-shift') as ChartWidgetDefinition;
      const comboChartWidget = await dashboard.getWidget<ComboChartWidget>(comboChart);
      await comboChartWidget.waitForLoad();
      await comboChartWidget.comboChart.chart('scatter').first().locator('circle').first().hover();
      await expect(comboChartWidget.comboChart.tooltip.getDatapointTooltipByLabel('Date')).toBeVisible();
      await expect(comboChartWidget.comboChart.tooltip.getDatapointTooltipByLabel('Replenishments')).toBeVisible();
      await expect(comboChartWidget.comboChart.tooltip.getDatapointTooltipByLabel('Group')).toBeVisible();
    });
    test(`[@C3659799] - Test that hovering over a data point on Daily Replenishments by Type Chart shows data point details - ${TestGroup.Charts}`, async ({
      dashboard,
    }) => {
      const mockApi = new IctApiSimulator(dashboard.page);
      const taskTypeData = TestData.ReplenishmentTaskTypeSeries();
      await mockApi.mockResponse(taskTypeData.endpoint, 200, taskTypeData.data);
      const comboChart = dashboardDefinition.widgets
        .get('combo-chart')
        .get('combo-chart-replenishment-task-type-data') as ChartWidgetDefinition;
      const comboChartWidget = await dashboard.getWidget<ComboChartWidget>(comboChart);
      await comboChartWidget.waitForLoad();
      await comboChartWidget.comboChart.chart('stacked-bar').locator('path').first().hover();
      await expect(comboChartWidget.comboChart.tooltip.getDatapointTooltipByLabel('Date')).toBeVisible();
      await expect(comboChartWidget.comboChart.tooltip.getDatapointTooltipByLabel('Replenishments')).toBeVisible();
      await expect(comboChartWidget.comboChart.tooltip.getDatapointTooltipByLabel('Group')).toBeVisible();
    });
  });

  test.describe(`Replenishment Details - Live Data`, async () => {
    test.use({ useMockApi: false });
    test(`[@C3659786] - Test that selecting 7 Days will update and show data on the page for the last 7 days - ${TestGroup.Charts}`, async ({
      dashboard,
    }) => {
      await dashboard.selectDateRange('last7days');
      await dashboard.waitUntilLoaded();
      const kpiChart = dashboardDefinition.widgets
        .get('kpi-chart')
        .get('kpi-chart-daily-replenishments') as KpiChartWidgetDefinition;
      const kpiChartWidget = await dashboard.getWidget<KpiChartWidget>(kpiChart);
      await kpiChartWidget.waitForLoad();
      const dataRange = await kpiChartWidget.comboChart.axisBottom.locator('g.tick:visible').count();
      expect(dataRange).toBe(7);
    });
    test(`[@C3659792] - Test that selecting 14 Days will update and show data on the page for the last 14 days - ${TestGroup.Charts}`, async ({
      dashboard,
    }) => {
      await dashboard.selectDateRange('last14days');
      const kpiChart = dashboardDefinition.widgets.get('kpi-chart').get('kpi-chart-daily-cycle-times') as KpiChartWidgetDefinition;
      const kpiChartWidget = await dashboard.getWidget<KpiChartWidget>(kpiChart);
      await kpiChartWidget.waitForLoad();
      const dataRange = await kpiChartWidget.comboChart.axisBottom.locator('g.tick:visible').count();
      expect(dataRange).toBe(14);
    });
    test(`[@C3659793] - Test that selecting 30 Days will update and show data on the page for the last 30 days - ${TestGroup.Charts}`, async ({
      dashboard,
    }) => {
      await dashboard.selectDateRange('last30days');
      await dashboard.waitUntilLoaded();
      const kpiChart = dashboardDefinition.widgets
        .get('kpi-chart')
        .get('kpi-chart-daily-pending-orders') as KpiChartWidgetDefinition;
      const kpiChartWidget = await dashboard.getWidget<KpiChartWidget>(kpiChart);
      await kpiChartWidget.waitForLoad();
      const dataRange = await kpiChartWidget.comboChart.axisBottom.locator('g.tick:visible').count();
      expect(dataRange).toBe(30);
    });
  });
});
