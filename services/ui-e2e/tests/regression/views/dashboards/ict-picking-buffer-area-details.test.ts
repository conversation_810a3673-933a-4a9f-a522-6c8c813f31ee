import { expect } from '@playwright/test';
import { TestGroup } from '@ui-tests/test-groups';
import { test } from '@ui-tests/fixtures/regression-fixture';
import { BarChartWidgetDefinition, PieChartWidgetDefinition } from '@ui-adk/components/widgets/widget-definition';
import { IctMenu } from '@ui-adk/ict-menu';
import { picking_buffer_area_details } from '@ui-adk/views/dashboard/configurations/picking-buffer-area-details';
import { BarChartWidget } from '@ui-adk/components/widgets/bar-chart-widget';
import { PieChartWidget } from '@ui-adk/components/widgets/pie-chart-widget';

const dashboardDefinition = picking_buffer_area_details;

test.describe(`ict-picking-buffer-area-details - ${TestGroup.Regression}`, async () => {
  test.use({ dashboardId: 'picking-buffer-area-details' });

  test(`Verify dashboard title:`, async ({ dashboard }) => {
    expect(await dashboard.getTitle()).toEqual(dashboardDefinition.title);
  });

  test(`Verify dashboard route:`, async ({ dashboard }) => {
    expect(await dashboard.getUrl()).toContain(dashboardDefinition.url);
  });

  test(`Verify user can navigate via menu`, async ({ controlTower, dashboard }) => {
    expect(await controlTower.sideMenu.isVisible()).toBeTruthy();
    await controlTower.sideMenu.selectItem(IctMenu.OperationsVisibility.PickingBufferAreaDetails);
    expect(await dashboard.getTitle()).toEqual(dashboardDefinition.title);
  });

  test(`Verify dashboard Bar Chart widgets: `, async ({ dashboard }) => {
    const barChartIds = Array.from(dashboardDefinition.widgets.get('bar-chart').keys());
    for (let i = 0; i < barChartIds.length; i++) {
      const barChartDef = dashboardDefinition.widgets.get('bar-chart').get(barChartIds[i]) as BarChartWidgetDefinition;
      const barChartWidget = await dashboard.getWidget<BarChartWidget>(barChartDef);
      expect(barChartWidget.locator.nth(i)).toBeVisible();
      const title = await barChartWidget.locator.nth(i).getByTestId('widget-heading').innerText();
      expect(title).toEqual(barChartDef.title);
    }
  });

  test(`Verify dashboard Pie Chart widget: `, async ({ dashboard }) => {
    const pieChartId = dashboardDefinition.widgets.get('pie-chart');
    const pieChartDef = dashboardDefinition.widgets
      .get('pie-chart')
      .get(pieChartId.keys().next().value) as PieChartWidgetDefinition;
    const pieChartWidget = await dashboard.getWidget<PieChartWidget>(pieChartDef);
    expect(pieChartWidget.locator.last()).toBeVisible();
    const title = await pieChartWidget.locator.last().getByTestId('widget-heading').innerText();
    expect(title).toEqual(pieChartDef.title);
  });
});
