import { expect } from '@playwright/test';
import { TestGroup } from '@ui-tests/test-groups';
import { test } from '@ui-tests/fixtures/regression-fixture';
import { getRandomItem, getValueByKeyPrefix } from '@ui-tests/utils/utils';
import { IctApiSimulator } from '@ui-tests/utils/ict-api-simulator';
import InventoryForecastMockData from '@ui-tests/regression/views/inventory-forecast/data/test-data';

test.describe(`ict-inventory-forecast - ${TestGroup.Regression}`, async () => {
  test.afterEach(async ({ inventoryForecast }) => {
    await inventoryForecast.page.unrouteAll({ behavior: 'ignoreErrors' });
  });

  test(`[@C3565757] - Test that the Inventory Forecast SKU table's details drawer shows the following columns: Locations, Orders, and Forecasting.`, async ({
    inventoryForecast,
  }) => {
    const skuList = await inventoryForecast.datagrid.table.getColumn(await inventoryForecast.datagrid.table.getColumnIndex('SKU'));
    await inventoryForecast.clickSKULink(getRandomItem<string>(skuList));
    await inventoryForecast.waitUntilLoaded();
    const tabs = await inventoryForecast.drawer.content.tabs.getTabs();
    const tabsName = await Promise.all(tabs.map((tab) => tab.getText()));
    expect(tabsName).toEqual(['Locations', 'Orders', 'Forecasting']);
  });
  test(`[@C3581277] - Test that the Orders tab in the Inventory Forecast's SKU list details drawers shows the SKU ID's order information.`, async ({
    inventoryForecast,
  }) => {
    const sku = 'DUB1005DBM';
    const mockApi = new IctApiSimulator(inventoryForecast.page);
    const listData = InventoryForecastMockData.List();
    await mockApi.mockResponse(listData.endpoint, 200, listData.data);
    await inventoryForecast.datagrid.table.waitForTable();
    const resultsData = InventoryForecastMockData.Orders(sku);
    await mockApi.mockResponse(resultsData.endpoint, 200, resultsData.data);
    await inventoryForecast.datagrid.table.waitForTable();
    await inventoryForecast.clickSKULink(sku);
    await inventoryForecast.drawer.content.openTab('Orders');
    await inventoryForecast.drawer.content.ordersTab.waitForTabLoad();
    const order = (await inventoryForecast.drawer.content.ordersTab.getOpenOrders())[0];
    const orderEntry = await order.getKeyValuePairs();
    expect.soft(await order.getHeading()).toBe(resultsData.data.data[0].orderId);
    expect.soft(orderEntry.get('Priority')).toBe(resultsData.data.data[0].priority);
    expect.soft(parseInt(orderEntry.get('Order Lines'), 10)).toBe(resultsData.data.data[0].orderLines);
    expect.soft(orderEntry.get('Allocation Date').split('\n')[0]).toBe(resultsData.data.data[0].allocationDate.split('T')[0]);
    expect.soft(orderEntry.get('Ship Date')).toBe(resultsData.data.data[0].shipDate.split('T')[0]);
    expect.soft(parseInt(getValueByKeyPrefix(orderEntry, 'Allocated Quantity'), 10)).toBe(resultsData.data.data[0].allocatedQty);
  });
  test(`[@C3581204] - Test that the Orders tab in the Inventory Forecast's SKU list details drawer shows "No order data available for this SKU." when the sku has no order info.`, async ({
    inventoryForecast,
  }) => {
    const sku = 'DUB1005DBM';
    const mockApi = new IctApiSimulator(inventoryForecast.page);
    const listData = InventoryForecastMockData.List();
    await mockApi.mockResponse(listData.endpoint, 200, listData.data);
    await inventoryForecast.datagrid.table.waitForTable();
    await mockApi.mockResponse(InventoryForecastMockData.Orders(sku).endpoint, 204);
    await inventoryForecast.datagrid.table.waitForTable();
    const skuList = await inventoryForecast.datagrid.table.getColumn(await inventoryForecast.datagrid.table.getColumnIndex('SKU'));
    await inventoryForecast.clickSKULink(getRandomItem<string>(skuList));
    await inventoryForecast.drawer.content.openTab('Orders');
    await inventoryForecast.drawer.content.ordersTab.waitForTabLoad();
    const noOrderDataMsg = await inventoryForecast.drawer.content.ordersTab.getNoOrderError();
    expect(noOrderDataMsg).toBe('Error loading orders for this SKU.');
  });
  test(`[@C3625423], [@C3625426] - Test that all of the metrics on the Forecasting tab of Inventory Forecast are rounded to the nearest whole number.`, async ({
    inventoryForecast,
  }) => {
    const sku = 'DUB1005DBM';
    const mockApi = new IctApiSimulator(inventoryForecast.page);
    const listData = InventoryForecastMockData.List();
    await mockApi.mockResponse(listData.endpoint, 200, listData.data);
    await inventoryForecast.datagrid.table.waitForTable();
    const resultsData = InventoryForecastMockData.Forecasting(sku);
    await mockApi.mockResponse(resultsData.endpoint, 200, resultsData.data);
    await inventoryForecast.datagrid.table.waitForTable();
    await inventoryForecast.clickSKULink(sku);
    await inventoryForecast.drawer.content.openTab('Forecasting');
    await inventoryForecast.drawer.content.forecastingTab.waitForTabLoad();
    const forecastingMetrics = await inventoryForecast.drawer.content.forecastingTab.getForecastingMetrics();
    const metrics = await forecastingMetrics.getKeyValuePairs();
    expect.soft(metrics.get('Error Rate')).toBe(`Standard Deviation = ${Math.round(resultsData.data.confidence)} eaches`);
    expect.soft(getValueByKeyPrefix(metrics, 'Short-term Demand')).toBe(`${Math.round(resultsData.data.shortTermDaily)} eaches`);
    expect.soft(getValueByKeyPrefix(metrics, 'Long-term Demand')).toBe(`${Math.round(resultsData.data.longTermDaily)} eaches`);
    expect.soft(metrics.get('Non-Zero Demand')).toBe(`${Math.round(resultsData.data.nonZeroDemand)}%`);
    expect.soft(metrics.get('Zero Demand Intermittency')).toBe(`${Math.round(resultsData.data.zeroDemandIntermittency)} days`);
  });
});
