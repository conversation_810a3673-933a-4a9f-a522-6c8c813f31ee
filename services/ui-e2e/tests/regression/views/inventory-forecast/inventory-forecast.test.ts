import { expect } from '@playwright/test';
import { TestGroup } from '@ui-tests/test-groups';
import { test } from '@ui-tests/fixtures/regression-fixture';
import { IctMenu } from '@ui-adk/ict-menu';
import { isDate } from '@ui-tests/utils/utils';

test.describe(`ict-inventory-forecast - ${TestGroup.Regression}`, async () => {
  test(`Verify url:`, async ({ inventoryForecast }) => {
    expect(await inventoryForecast.getUrl()).toContain('ict-inventory-forecast');
  });
  test('[@C3552402] - Inventory Forecast in the left navigation opens the Inventory Forecast Page.', async ({
    inventoryForecast,
    controlTower,
  }) => {
    expect(await controlTower.sideMenu.isVisible()).toBeTruthy();
    await controlTower.sideMenu.selectItem(IctMenu.AdvancedOrchestration.InventoryForecast);
    await inventoryForecast.waitUntilLoaded();
    expect(await inventoryForecast.getTitle()).toEqual('Inventory Forecast');
  });
  test(`[@C3581176] - Test that the SKU Forecast and Data Analysis Timestamps show on the top right of the Inventory Forecast page.`, async ({
    inventoryForecast,
  }) => {
    const dataUpdatedTimeStamp = await inventoryForecast.getDataUpdatedTimeStamp();
    const analysisPerformedTimeStamp = await inventoryForecast.getAnalysisPerformedTimeStamp();
    expect.soft(isDate(dataUpdatedTimeStamp)).toBeTruthy();
    expect.soft(isDate(analysisPerformedTimeStamp)).toBeTruthy();
  });
  test('[@C3592776] - Test that the Inventory Forecast list is set to sort by Forward Pick Tomorrow by default.', async ({
    inventoryForecast,
  }) => {
    const sortDirection = await inventoryForecast.datagrid.table.getColumnSortDirection('Forward Pick Tomorrow');
    expect(sortDirection).toEqual('ascending');
  });
});
