import { test } from '@ui-tests/fixtures/regression-fixture';
import { TestGroup } from '@ui-tests/test-groups';
import { expect } from '@playwright/test';
import { IctMenu } from '@ui-adk/ict-menu';

test.describe(`Menu Group - System Availability - ${TestGroup.Regression}`, async () => {
  test(`[@C3665846] - Test that System Availability is in the navigation drawer`, async ({ controlTower }) => {
    expect(await controlTower.sideMenu.itemExists(IctMenu.SystemAvailability)).toBeTruthy();
  });
  test(`[@C3669013] - Test that Fault Tracking is under System Availability in the navigation drawer`, async ({ controlTower }) => {
    expect(await controlTower.sideMenu.itemExists(IctMenu.SystemAvailability.FaultTracking)).toBeTruthy();
  });
});
