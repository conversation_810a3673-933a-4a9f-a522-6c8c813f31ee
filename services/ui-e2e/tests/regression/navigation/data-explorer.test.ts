import { test } from '@ui-tests/fixtures/regression-fixture';
import { TestGroup } from '@ui-tests/test-groups';
import { expect } from '@playwright/test';
import { IctMenu } from '@ui-adk/ict-menu';

test.describe(`Menu Group - Data Explorer - ${TestGroup.Regression}`, async () => {
  test(`[@C3449155] - Test that Data Explorer is in the navigation drawer`, async ({ controlTower }) => {
    expect(await controlTower.sideMenu.itemExists(IctMenu.DataExplorer)).toBeTruthy();
  });
});
