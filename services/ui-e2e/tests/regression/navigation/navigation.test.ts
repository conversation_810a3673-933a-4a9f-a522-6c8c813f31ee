import { test } from '@ui-tests/fixtures/regression-fixture';
import { TestGroup } from '@ui-tests/test-groups';
import { expect } from '@playwright/test';
import { IctMenu } from '@ui-adk/ict-menu';

test.describe(`Navigation Menu - ${TestGroup.Regression}`, async () => {
  test(`[@C3248405] - Test that the navigation drawer is available on the left side of the screen`, async ({ controlTower }) => {
    await expect(controlTower.sideMenu.locator).toBeVisible();
  });
  test(`[@C3293735] - Test that the menu options in the left nav have icons`, async ({ controlTower }) => {
    const menuGroupItems = Object.keys(IctMenu) as Array<keyof typeof IctMenu>;
    for (const key of menuGroupItems) {
      const item = IctMenu[key];
      expect
        .soft(await controlTower.sideMenu.itemHasIcon(item), `Menu item '${item.title}' does not have a visible icon.`)
        .toBeTruthy();
    }
  });
});
