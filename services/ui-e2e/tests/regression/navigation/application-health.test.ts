import { test } from '@ui-tests/fixtures/regression-fixture';
import { TestGroup } from '@ui-tests/test-groups';
import { expect } from '@playwright/test';
import { IctMenu } from '@ui-adk/ict-menu';

test.describe(`Menu Group - Application Health - ${TestGroup.Regression}`, async () => {
  test(`[@C3423130] - Test that the navigation drawer shows Application Health`, async ({ controlTower }) => {
    expect(await controlTower.sideMenu.itemExists(IctMenu.ApplicationHealth)).toBeTruthy();
  });
  test(`[@C3679673] - Test that Curated Data is under Application Health in the navigation drawer`, async ({ controlTower }) => {
    expect(await controlTower.sideMenu.itemExists(IctMenu.ApplicationHealth.CuratedData)).toBeTruthy();
  });
});
