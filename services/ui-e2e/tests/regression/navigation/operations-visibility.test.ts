import { test } from '@ui-tests/fixtures/regression-fixture';
import { TestGroup } from '@ui-tests/test-groups';
import { expect } from '@playwright/test';
import { IctMenu } from '@ui-adk/ict-menu';

test.describe(`Menu Group - Operations Visibility - ${TestGroup.Regression}`, async () => {
  test(`[@C3257067] - Test that the navigation drawer contains Operations Visibility`, async ({ controlTower }) => {
    expect(await controlTower.sideMenu.itemExists(IctMenu.OperationsVisibility)).toBeTruthy();
  });
  test(`[@C3351380] - Test that Outbound Overview is under Operations Visibility in the navigation drawer`, async ({
    controlTower,
  }) => {
    expect(await controlTower.sideMenu.itemExists(IctMenu.OperationsVisibility.OutboundOverview)).toBeTruthy();
  });
  test(`[@C3522896] - Test that Workstation Overview is under Operations Visibility in the navigation drawer`, async ({
    controlTower,
  }) => {
    expect(await controlTower.sideMenu.itemExists(IctMenu.OperationsVisibility.Workstation.WorkstationOverview)).toBeTruthy();
  });
  test(`[@C3351381] - Test that Inbound Overview is under Operations Visibility in the navigation drawer`, async ({
    controlTower,
  }) => {
    expect(await controlTower.sideMenu.itemExists(IctMenu.OperationsVisibility.InboundOverview)).toBeTruthy();
  });
  test(`[@C3621758] - Test that Inventory is under Operations Visibility in the navigation drawer`, async ({ controlTower }) => {
    expect(await controlTower.sideMenu.itemExists(IctMenu.OperationsVisibility.Inventory)).toBeTruthy();
  });
  test(`[@C3351382] - Test that Inventory Overview is under Operations Visibility/Inventory in the navigation drawer`, async ({
    controlTower,
  }) => {
    expect(await controlTower.sideMenu.itemExists(IctMenu.OperationsVisibility.Inventory.InventoryOverview)).toBeTruthy();
  });
  test(`[@C3679659] - Test that Container List is under Operations Visibility/Inventory in the navigation drawer`, async ({
    controlTower,
  }) => {
    expect(await controlTower.sideMenu.itemExists(IctMenu.OperationsVisibility.Inventory.ContainerList)).toBeTruthy();
  });
  test(`[@C3679666] - Test that Inventory List is under Operations Visibility/Inventory in the navigation drawer`, async ({
    controlTower,
  }) => {
    expect(await controlTower.sideMenu.itemExists(IctMenu.OperationsVisibility.Inventory.InventoryList)).toBeTruthy();
  });
  test(`[@C3679667] - Test that Replenishment Details is under Operations Visibility/Inventory in the navigation drawer`, async ({
    controlTower,
  }) => {
    expect(await controlTower.sideMenu.itemExists(IctMenu.OperationsVisibility.Inventory.ReplenishmentDetails)).toBeTruthy();
  });
  test(`[@C3679668] - Test that Picking Buffer Area Details is under Operations Visibility in the navigation drawer`, async ({
    controlTower,
  }) => {
    expect(await controlTower.sideMenu.itemExists(IctMenu.OperationsVisibility.PickingBufferAreaDetails)).toBeTruthy();
  });
});
