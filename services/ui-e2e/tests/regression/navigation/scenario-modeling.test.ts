import { test } from '@ui-tests/fixtures/regression-fixture';
import { TestGroup } from '@ui-tests/test-groups';
import { expect } from '@playwright/test';
import { IctMenu } from '@ui-adk/ict-menu';

test.describe(`Menu Group - Scenario Modeling - ${TestGroup.Regression}`, async () => {
  test(`[@C3648025] - Test that the navigation drawer shows Scenario Modeling`, async ({ controlTower }) => {
    expect(await controlTower.sideMenu.itemExists(IctMenu.ScenarioModeling)).toBeTruthy();
  });
  test(`[@C3648026] - Test that Simulation is under Scenario Modeling in the navigation drawer`, async ({ controlTower }) => {
    expect(await controlTower.sideMenu.itemExists(IctMenu.ScenarioModeling.Simulation)).toBeTruthy();
  });
});
