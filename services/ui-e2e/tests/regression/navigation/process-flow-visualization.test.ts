import { test } from '@ui-tests/fixtures/regression-fixture';
import { TestGroup } from '@ui-tests/test-groups';
import { expect } from '@playwright/test';
import { IctMenu } from '@ui-adk/ict-menu';

test.describe(`Menu Group - Facility Process Flow - ${TestGroup.Regression}`, async () => {
  test(`[@C3421575] - Test that the navigation drawer shows Process Flow Visualization`, async ({ controlTower }) => {
    expect(await controlTower.sideMenu.itemExists(IctMenu.ProcessFlowVisualization)).toBeTruthy();
  });
  test(`[@C3679672] - Test that Facility Process Flow is under Process Flow Visualization in the navigation drawer`, async ({
    controlTower,
  }) => {
    expect(await controlTower.sideMenu.itemExists(IctMenu.ProcessFlowVisualization.FacilityProcessFlow)).toBeTruthy();
  });
});
