import { test } from '@ui-tests/fixtures/regression-fixture';
import { TestGroup } from '@ui-tests/test-groups';
import { expect } from '@playwright/test';
import { IctMenu } from '@ui-adk/ict-menu';

test.describe(`Menu Group - Advanced Orchestration - ${TestGroup.Regression}`, async () => {
  test(`[@C3421579] - Test that the navigation drawer shows Advanced Orchestration`, async ({ controlTower }) => {
    expect(await controlTower.sideMenu.itemExists(IctMenu.AdvancedOrchestration)).toBeTruthy();
  });
  test(`[@C3552391] - Test that Inventory Forecast is under Advanced Orchestration in the navigation drawer`, async ({
    controlTower,
  }) => {
    expect(await controlTower.sideMenu.itemExists(IctMenu.AdvancedOrchestration.InventoryForecast)).toBeTruthy();
  });
  test(`[@C3552392] - Test that File Upload is under Advanced Orchestration in the navigation drawer`, async ({ controlTower }) => {
    expect(await controlTower.sideMenu.itemExists(IctMenu.AdvancedOrchestration.FileUpload)).toBeTruthy();
  });
});
