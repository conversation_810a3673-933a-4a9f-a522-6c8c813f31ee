import { test } from '@ui-tests/fixtures/regression-fixture';
import { TestGroup } from '@ui-tests/test-groups';
import { expect } from '@playwright/test';
import { ControlTower } from '@ui-adk/app';
import Container from 'typedi';
import { TestConfigurationService } from '@ui-adk/services/configuration';

test.describe(`Login Screen. User Signin. - ${TestGroup.Login} ${TestGroup.Regression}`, async () => {
  test(`[@C3246408] - Test that user can log in`, async ({ page, logIn, user, tenant }) => {
    const controlTower = new ControlTower(page, Container.get(TestConfigurationService));
    await logIn.goTo();
    await logIn.login(user, tenant);
    const currentTenant = await controlTower.getTenantName();
    expect(currentTenant).toEqual(tenant);
  });
  test(`[Test-Case @C3246413] [Test-Case @C3246430] - Test that user can log out`, async ({ page, logIn, user, tenant }) => {
    const controlTower = new ControlTower(page, Container.get(TestConfigurationService));
    await logIn.goTo();
    await logIn.login(user, tenant);
    await controlTower.logout();
    const result = await logIn.exists();
    expect(result).toBeTruthy();
  });
  test(`[Test-Case @C3246416] - Test that unauthorized user cannot log in`, async ({ logIn }) => {
    await logIn.goTo();
    await logIn.login(Container.get(TestConfigurationService).unauthorizedUser);
    const result = await logIn.getErrorMessage();
    expect(result.trim()).toBe('Wrong email or password');
  });
  test(`[Test-Case @C3246417] - Test that user with valid email (or login ID) must enter password in order to log in`, async ({
    logIn,
    user,
  }) => {
    await logIn.goTo();
    await logIn.username.pressSequentially(user.email, { delay: 50 });
    await logIn.loginButton.click();
    const result = await logIn.exists();
    expect(result).toBeTruthy();
  });
  test(`[Test-Case @C3246423], [Test-Case @C3459474] - Test that reloading the login screen after user logs out does not log the user in again`, async ({
    page,
    logIn,
    user,
    tenant,
  }) => {
    const controlTower = new ControlTower(page, Container.get(TestConfigurationService));
    await logIn.goTo();
    await logIn.login(user, tenant);
    await controlTower.logout();
    await logIn.goTo();
    const result = await logIn.exists();
    expect(result).toBeTruthy();
  });
  test(`[Test-Case @C3247437] - Test that a message is displayed when user enters invalid password`, async ({ logIn, user }) => {
    await logIn.goTo();
    await logIn.username.pressSequentially(user.email, { delay: 50 });
    await logIn.password.pressSequentially(`${user.password}_invalid`, { delay: 50 });
    await logIn.loginButton.click();
    const result = await logIn.getErrorMessage();
    expect(result.trim()).toBe('Wrong email or password');
  });
  test(`[Test-Case @C3247438] - Test that a message is displayed when user enters invalid user name`, async ({ logIn, user }) => {
    await logIn.goTo();
    await logIn.username.pressSequentially(`${user.email}-invalid`, { delay: 50 });
    await logIn.password.pressSequentially(user.password, { delay: 50 });
    await logIn.loginButton.click();
    const result = await logIn.getErrorMessage();
    expect(result.trim()).toBe('Wrong email or password');
  });
  test(`[Test-Case @C3560635] - Test that the logged in user's email address shows in the user profile`, async ({
    page,
    logIn,
    user,
    tenant,
  }) => {
    const controlTower = new ControlTower(page, Container.get(TestConfigurationService));
    await logIn.goTo();
    await logIn.login(user, tenant);
    const userEmail = await controlTower.getUserEmail();
    expect(userEmail).toEqual(user.email);
  });
  // TODO: Re-enable once ICT-7423 is fixed.
  test.skip(`[Test-Case @C3560636] - Test that email address shows in the user profile when the user switches organizations`, async ({
    page,
    logIn,
    user,
    tenant,
  }) => {
    const controlTower = new ControlTower(page, Container.get(TestConfigurationService));
    await logIn.goTo();
    await logIn.login(user, tenant);
    expect(await controlTower.getUserEmail()).toEqual(user.email);
    await controlTower.switchOrganization('Dematic Solution Center');
    expect(await controlTower.getUserEmail()).toEqual(user.email);
    await controlTower.switchOrganization(tenant);
    expect(await controlTower.getUserEmail()).toEqual(user.email);
  });
});
