import { test } from '@ui-tests/fixtures/regression-fixture';
import { TestGroup } from '@ui-tests/test-groups';
import { expect } from '@playwright/test';
import { AuthenticationService } from '@ui-adk/services/authentication';
import { jwtDecode, JwtPayload } from 'jwt-decode';

test.describe(`Multi Facility Access - ${TestGroup.Regression}`, async () => {
  test(`[@C3664451] - Test that user role(s) show in the Account panel`, async ({ controlTower }) => {
    const token = await AuthenticationService.instance.getCachedToken();
    const payload = jwtDecode<JwtPayload>(token.access_token);
    const roles = payload['https://ict.dematic.cloud/roles'] as string[];
    const userRoles = (await controlTower.getUserRoles()).map((role) => role.replaceAll(' ', '_').toLowerCase());
    for (const role of userRoles) {
      expect(roles, `Expected role "${role}" to be present`).toContain(role);
    }
  });

  test(`[@C3664634], [@C3664452] - Test that the tenant the user is logged in to shows correctly in the site's header`, async ({
    controlTower,
    tenant,
  }) => {
    const currentTenant = await controlTower.getTenantName();
    expect(currentTenant).toBe(tenant);
  });
});
