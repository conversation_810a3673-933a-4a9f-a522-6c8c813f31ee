import { DateTime } from 'luxon';
import { AppConfigurationService } from '@ui-adk/services/configuration/app-configuration-service';

export const ApiDateFilterOptions = ['today', 'yesterday', 'last 7 days', 'last 14 days', 'last 30 days'] as const;

export type ApiDateFilterOption = (typeof ApiDateFilterOptions)[number];

export type ApiDateFilter = {
  start_date: string;
  end_date: string;
};

export function getDateFilter(option: ApiDateFilterOption): ApiDateFilter {
  const timezone = AppConfigurationService.instance.getFacilityConfig()?.find((s) => s.name === 'site-time-zone').value;
  const now = DateTime.now().setZone(timezone);
  switch (option) {
    case 'today':
      return {
        start_date: now.startOf('day').toJSDate().toISOString(),
        end_date: now.endOf('day').toJSDate().toISOString(),
      };
    case 'yesterday':
      return {
        start_date: now.minus({ days: 1 }).startOf('day').toJSDate().toISOString(),
        end_date: now.minus({ days: 1 }).endOf('day').toJSDate().toISOString(),
      };
    case 'last 7 days':
      return {
        start_date: now.minus({ days: 7 }).startOf('day').toJSDate().toISOString(),
        end_date: now.endOf('day').toJSDate().toISOString(),
      };
    case 'last 14 days':
      return {
        start_date: now.minus({ days: 14 }).startOf('day').toJSDate().toISOString(),
        end_date: now.endOf('day').toJSDate().toISOString(),
      };
    case 'last 30 days':
      return {
        start_date: now.minus({ days: 30 }).startOf('day').toJSDate().toISOString(),
        end_date: now.endOf('day').toJSDate().toISOString(),
      };
  }
}
