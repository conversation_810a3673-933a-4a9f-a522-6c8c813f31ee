# Helpful docs:
# - Main page: https://docs.gitlab.com/ee/user/project/codeowners/
# - Syntax: https://docs.gitlab.com/ee/user/project/codeowners/reference.html
# - Defining more specific owners for already matched paths:
#     https://docs.gitlab.com/ee/user/project/codeowners/#define-more-specific-owners-for-more-specifically-defined-files-or-directories

# [rule-name][number of required approvers]
# path/to/code/requiring/approvals @user @group

# Key Patterns:
# ** = matches zero or more directories
# **/infrastructure/** = matches any infrastructure folder and all its contents
# **/*.gitlab-ci.yml = matches any gitlab-ci config file anywhere
# services/ui/**/packages/** = matches packages in UI services
# **/infrastructure/ would match: infrastructure/readme.md or some/path/infrastructure/config.txt
# **/infrastructure/ would NOT match: infrastructure/Configs/dev.tfvars
# **/infrastructure/** matches ALL of the above, including nested files like infrastructure/Configs/dev.tfvars

[UI-Approvals][1]
services/ui/                              @dematic/teams/swrd-ict-ui
# Exclude dependency files from UI approval requirements (allows Renovate auto-merge)
!services/ui/**/package.json
!services/ui/**/package-lock.json
!services/ui/**/yarn.lock
!services/ui/**/pnpm-lock.yaml

[API-Approvals][1]
services/api/apps/                        @dematic/teams/swrd-ict-api
services/api/cloudrun/                    @dematic/teams/swrd-ict-api
services/api/libs/                        @dematic/teams/swrd-ict-api
services/api/sql/                         @dematic/teams/swrd-ict-api
# Exclude dependency files from API approval requirements (allows Renovate auto-merge)
!services/api/**/package.json
!services/api/**/package-lock.json
!services/api/**/yarn.lock
!services/api/**/pnpm-lock.yaml
# Exclude Docker files from API approval requirements (allows Renovate auto-merge)
!services/api/**/Dockerfile
!services/api/**/docker-compose*.yml
!services/api/Dockerfile

[DevOps-Approvals][2]
**/infrastructure/                        @dematic/teams/swrd-ict-infra
**/*.gitlab-ci.yml                        @dematic/teams/swrd-ict-infra
# Exclude Docker-related files from DevOps approval requirements (allows Renovate auto-merge)
!**/Dockerfile
!**/docker-compose*.yml

# Optional approval from docs team on all .md files
^[General Documentation]
**/*.md                                   @dematic/controltower
**/docs-for-backstage/                    @dematic/controltower

[MetricProcessor-Approvals][1]
services/metric-processor/                @dematic/teams/swrd-ict-metric-processor

