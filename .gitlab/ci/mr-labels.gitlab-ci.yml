.add-label: &add-label
  - |
    if [ -n "$MR_LABEL" ]; then curl --header "PRIVATE-TOKEN: ${GROUP_ACCESS_TOKEN}" --request PUT --data "add_labels=${MR_LABEL}" \
    "https://gitlab.com/api/v4/projects/${CI_PROJECT_ID}/merge_requests/${CI_MERGE_REQUEST_IID}"; fi

add-label-infra:
  stage: .pre
  variables:
    MR_LABEL: "infrastructure"
  script:
    - *add-label
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - services/api/infrastructure/**/*
        - services/ui/infrastructure/**/*
        - infrastructure/**/*
      when: always

add-label-components-ui:
  stage: .pre
  variables:
    MR_LABEL: "ui"
  script:
    - *add-label
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - services/ui/**/*
      when: always

add-label-components-api:
  stage: .pre
  variables:
    MR_LABEL: "api"
  script:
    - *add-label
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - services/api/**/*
      when: always

add-label-cicd:
  stage: .pre
  variables:
    MR_LABEL: "cicd"
  script:
    - *add-label
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - .gitlab/**/*
        - .gitlab-ci.yml
      when: always
