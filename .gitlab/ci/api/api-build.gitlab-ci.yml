include:
  - local: .gitlab/ci/includes/shared.gitlab-ci.yml

.build-api: &build-api
  - echo -e "section_start:`date +%s`:mise-build[collapsed=true]\r\e[0Kmise build"
  - mise run build
  - echo -e "section_end:`date +%s`:mise-build\r\e[0K"

.run-api-unit-tests: &run-api-unit-tests
  - echo -e "section_start:`date +%s`:mise-lint[collapsed=true]\r\e[0Kmise lint"
  - mise run lint
  - echo -e "section_end:`date +%s`:mise-lint\r\e[0K"
  - echo -e "section_start:`date +%s`:mise-coverage[collapsed=true]\r\e[0Kmise coverage"
  - mise run "test:coverage"
  - echo -e "section_end:`date +%s`:mise-coverage\r\e[0K"

.if-dev-commit-contains-sdk-changes: &if-dev-commit-contains-sdk-changes
  if: '$CI_COMMIT_BRANCH == $DEV_BRANCH'
  changes:
    - $API_PROJECT_ROOT/apps/api/**/docs/openapi.yaml
    - $API_PROJECT_ROOT/libs/shared/ict-sdk-foundations/**/*
    - $API_PROJECT_ROOT/docs/control-tower-openapi.yaml

.if-dev-mr-pipeline: &if-dev-mr-pipeline
  if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $DEV_BRANCH'

.api-base:
  extends: .common-base
  before_script:
    - cd $API_PROJECT_ROOT

api:build:
  stage: build
  extends:
    - .api-base
  allow_failure: false
  needs: []
  # before_script:
  #   - export MISE_DEFAULT_CONFIG_FILENAME="${PROJECT_ROOT}/.mise.toml"
  script:
    - *build-api
  coverage: '/All files\s+\|\s+\d+\.\d+\s+\|\s+\d+\.\d+\s+\|\s+\d+\.\d+\s+\|\s+(\d+\.\d+)\s+.*/'
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME'

api:unit-test:
  stage: test
  extends:
    - .api-base
  allow_failure: false
  needs: [api:build]
  coverage: '/All files\s+\|\s+\d+\.\d+\s+\|\s+\d+\.\d+\s+\|\s+\d+\.\d+\s+\|\s+(\d+\.\d+)\s+.*/'
  script:
    - *run-api-unit-tests
  rules:
    - <<: *if-dev-mr-pipeline

api:publish-client-sdk:
  stage: deploy
  environment: $TARGET_ENVIRONMENT
  interruptible: false
  extends:
    - .api-base
  script:
    - mise run "sdk:publish"
  rules:
    - <<: *if-dev-commit-contains-sdk-changes