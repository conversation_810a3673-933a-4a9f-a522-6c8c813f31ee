include:
  - local: .gitlab/ci/includes/shared.gitlab-ci.yml

variables:
  RELEASE_IMAGE: "registry.gitlab.com/gitlab-org/release-cli:latest"
  DIND_IMAGE: "us-docker.pkg.dev/ict-o-registry/ict-o-registry/ops-dind:2024-01-24"

.if-prod-commit-pipeline: &if-prod-commit-pipeline
  if: '$CI_COMMIT_BRANCH == $PROD_BRANCH'

.configure-git: &configure-git
  - echo "Configuring Git..."
  - git config --global user.email "<EMAIL>"
  - git config --global user.name "GitLab CICD"

.commit-and-tag-version: &commit-and-tag-version
  - echo "Creating a release version..."
  - npx --yes commit-and-tag-version --skip.commit

.push-tag: &push-tag
  - new_version=$(git describe --tags --abbrev=0)
  - echo "New version - $new_version"
  - git remote set-url origin "https://gitlab-ci-token:$<EMAIL>/${CI_PROJECT_PATH}.git"
  - git push origin $new_version
  - echo "TAG=$new_version" >> variables.env

.release-job-base:
  extends: .common-base
  variables:
    GIT_STRATEGY: clone
  before_script:
    - !reference [.common-base, before_script]
    - *configure-git

service-release:create-and-push-tag:
  stage: deploy
  allow_failure: false
  extends: .release-job-base
  script:
    - *commit-and-tag-version
    - *push-tag
  artifacts:
    paths:
      - CHANGELOG.md
    reports:
      dotenv: variables.env
  rules:
    - <<: *if-prod-commit-pipeline

service-release:create-release:
  stage: deploy
  allow_failure: false
  image: $RELEASE_IMAGE
  needs:
    - job: service-release:create-and-push-tag
      artifacts: true
  script:
    - echo "Creating release for $TAG"
  release:
    name: "Release $TAG"
    description: CHANGELOG.md
    tag_name: "$TAG"
    ref: $CI_COMMIT_SHA
  rules:
    - <<: *if-prod-commit-pipeline
