include:
  - local: .gitlab/ci/includes/shared.gitlab-ci.yml
  - local: .gitlab/ci/sast/*.gitlab-ci.yml
  - project: dematic/controltower/ict-gitlab-templates
    file: Scanners/sonarqube-template.yml

.if-contains-api-changes: &if-contains-api-changes
  changes:
    - $API_PROJECT_ROOT/**/*

.if-dev-commit-pipeline: &if-dev-commit-pipeline
  if: '$CI_COMMIT_BRANCH == $DEV_BRANCH'

.if-dev-mr-pipeline: &if-dev-mr-pipeline
  if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'

sonar-qube-scan:
  extends:
    - .sonar-qube_base_job
    - .scan-base
  allow_failure: false

node-license-scan:
  extends:
    - .scan-base
    - .common-base
  script:
    - npx npm-license-crawler --start services --onlyDirectDependencies --csv open_source_software.csv
    - echo "Node license scan completed."
  artifacts:
    paths:
      - open_source_software.csv
  rules:
    - <<: *if-dev-commit-pipeline
    - <<: *if-dev-mr-pipeline

validate_mr_title:
  stage: validate
  script:
    - echo "Validating the merge request title format is complete."
    - echo "Merge request title $CI_MERGE_REQUEST_TITLE"
    - |
      if [[ "$CI_MERGE_REQUEST_TITLE" =~ /^(?i)((build|chore|ci|docs|feat|fix|perf|refactor|revert|style|test){1}(\([\w\-\.]+\))?(!)?: ICT-\d+ ([\w\- ]+)([\s\S]*)|merge.*branch.*|merged.*|revert.*|v.*)/gm ]]; then
        echo "Merge request title does not follow the convention. Please use the format: <type>(<scope>): ICT-<number> <description>"
        exit 1 
      fi
  rules:
    - if: '$CI_MERGE_REQUEST_DRAFT == "false"'
      when: always
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
