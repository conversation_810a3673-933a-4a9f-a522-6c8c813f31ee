debug:echo-variables:
  stage: .pre
  script:
    # gitlab pipeline variables...
    - echo " ---- gitlab pipeline variables ----"
    - echo $CI_PIPELINE_SOURCE
    - echo $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
    - echo $CI_COMMIT_BRANCH
    - echo $CI_COMMIT_DESCRIPTION
    - echo $CI_COMMIT_REF_PROTECTED
    - echo $CI_ENVIRONMENT_ACTION
    - echo $CI_MERGE_REQUEST_EVENT_TYPE
    - echo $CI_MERGE_REQUEST_APPROVED
    - echo $CI_MERGE_REQUEST_SOURCE_BRANCH_PROTECTED
    - echo $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME
    - echo " ----- workflow configuration -----"
    # merge request pipeline...
    - echo $ICT_ENVIRONMENT
    - echo $IS_ICT_MERGE_REQUEST_PIPELINE
    - echo $IS_ICT_DEV_MERGE_REQUEST_PIPELINE
    - echo $IS_ICT_STAGE_MERGE_REQUEST_PIPELINE
    - echo $IS_ICT_PROD_MERGE_REQUEST_PIPELINE
    - echo $TEST_SUITE_BASE_URL
    - echo $TEST_SUITE_DOWNSTREAM_UI_URL
    - echo $IS_ICT_DEPLOY_PIPELINE
    - echo $IS_ICT_DEPLOY_DEV_PIPELINE
    - echo $IS_ICT_DEPLOY_STAGE_PIPELINE
    - echo $IS_ICT_DEPLOY_PROD_PIPELINE
    - echo $ICT_RELEASE_NOTES_SOURCE_BRANCH
    - echo $ICT_RELEASE_NOTES_TARGET_BRANCH    
    - echo $TEST_ICT_UI_URL
    - echo $TEST_ICT_ENV
    - echo $TEST_ICT_API_URL
    - echo $TEST_API_ENV
    - echo $TEST_API_DOWNSTREAM_ENV
    - echo $TARGET_ENVIRONMENT
    - echo $TARGET_LANDING_ZONE
    - echo " ----- workflow configuration -----"
    - echo $IS_ICT_TEST_REGRESSION_SCHEDULED_PIPELINE
    # Test Job request pipeline...
    - echo $IS_ICT_REQUESTED_TEST_SUITE_RUN_PIPELINE
    - echo $SUITE
    # Scheduled Regression test Job pipeline...
    - echo $ICT_TEST_ENV
