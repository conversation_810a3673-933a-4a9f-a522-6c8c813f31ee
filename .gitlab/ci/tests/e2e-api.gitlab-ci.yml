variables:  
  TEST_DOCKER_IMAGE: mcr.microsoft.com/playwright:v1.45.1-jammy

.if-scheduled-regression-test: &if-scheduled-regression-test
  if: '$CI_PIPELINE_SOURCE == "schedule" && $ICT_API_TEST_ENV'

.if-stage-or-prod-mr-pipeline: &if-stage-or-prod-mr-pipeline
  if: '$CI_MERGE_REQUEST_TARGET_BRANCH_PROTECTED && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME != $DEV_BRANCH'

.if-mr-pipeline: &if-mr-pipeline
  if: '$CI_PIPELINE_SOURCE == "merge_request_event"'

.if-dev-commit: &if-dev-commit
  if: '$CI_COMMIT_BRANCH == $DEV_BRANCH'

.if-stage-commit: &if-stage-commit
  if: '$CI_COMMIT_BRANCH == $STAGE_BRANCH'

.if-prod-commit: &if-prod-commit
  if: '$CI_COMMIT_BRANCH == $PROD_BRANCH'

.api-e2e-base:
  image: $TEST_DOCKER_IMAGE  
  before_script:
  - cd $API_E2E_PROJECT_ROOT  
  - yarn install
  - yarn setup

api:e2e:build:
  stage: build
  extends:
    - .api-e2e-base
  allow_failure: false
  artifacts:
    untracked: true
  script:
    - yarn run build
  rules:
    - <<: *if-mr-pipeline

.api:e2e:run-test-base:  
  needs:
    - job: api:e2e:build
      artifacts: true
  allow_failure: true
  extends:
  - .api-e2e-base
  
api:e2e:smoke:
  stage: .post
  extends:
  - .api:e2e:run-test-base
  variables:
    ICT_ENV: $TEST_SUITE_DOWNSTREAM_UI_URL
    GITLAB_ACCESS_TOKEN: $GROUP_ACCESS_TOKEN
  script:
  - yarn test:smoke
  rules:
    - <<: [*if-stage-or-prod-mr-pipeline]

api:e2e:regression:
  stage: verify
  extends:
  - .api:e2e:run-test-base
  variables:
    ICT_ENV: $TEST_API_ENV
    GITLAB_ACCESS_TOKEN: $GROUP_ACCESS_TOKEN
  script:
  - yarn test:regression
  rules:
    - <<: *if-stage-or-prod-mr-pipeline
      
api:e2e:regression:schedule:
  stage: verify
  extends:
   - .api:e2e:run-test-base
  allow_failure: true
  variables:
    ICT_ENV: dev
  script:
    - yarn test:regression
  rules:
    - <<: *if-scheduled-regression-test
