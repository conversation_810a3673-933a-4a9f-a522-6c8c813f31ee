# include:
#   - local: .gitlab/ci/tests/e2e-ui-base.gitlab-ci.yml

# .if-dev-mr-pipeline: &if-dev-mr-pipeline
#   if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $DEV_BRANCH'

# .review-app-test-job:
#   rules:
#     - <<: *if-dev-mr-pipeline
#       changes:
#         - "$CI_PROJECT_DIR/services/ui/**/*"
#       when: never
#     - <<: *if-dev-mr-pipeline
#       when: always

# ui:e2e:review-app:smoke:
#   allow_failure: false
#   extends:
#     - .run-smoke-test
#     - .review-app-test-job
#   variables:
#     ICT_ENV: preview
#     ICT_BASE_URL: $REVIEW_APP_URL
#   needs:
#     - !reference [".run-smoke-test", "needs"]
#     - job: ui:review-app:launch
#       artifacts: true

# ui:e2e:review-app:regression:
#   allow_failure: false
#   extends:
#     - .run-regression-test
#     - .review-app-test-job
#   variables:
#     ICT_ENV: preview
#     ICT_BASE_URL: $REVIEW_APP_URL
#   needs:
#     - !reference [".run-regression-test", "needs"]
#     - job: ui:e2e:review-app:smoke
#       artifacts: true
