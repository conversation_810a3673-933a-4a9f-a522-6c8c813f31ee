.if-dev-mr-pipeline: &if-dev-mr-pipeline
  if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $DEV_BRANCH'

.poetry-install: &poetry-install
  - apt-get update
  - apt-get install -y git
  - python3 -m venv .venv
  - source .venv/bin/activate
  - pip install poetry
  - poetry install

.terraform-module-publishing-base: &terraform-module-publishing-base
  image: "python:3.12-slim"
  stage: test
  allow_failure: true
  before_script:
    - cd ${CI_PROJECT_DIR}/scripts/terraform-modules
    - *poetry-install

terraform-module-publishing-test:
  extends:
    - .terraform-module-publishing-base
  script:
    - pytest --maxfail=1 --disable-warnings -v
  rules:
    - <<: *if-dev-mr-pipeline

dry-run-terraform-module-publishing:
  extends:
    - .terraform-module-publishing-base
  script:
    - python3 publish.py --merge-request-iid $CI_MERGE_REQUEST_IID --dry-run
  rules:
    - <<: *if-dev-mr-pipeline

terraform-module-publishing:
  image: us-docker.pkg.dev/ict-o-common-services-kfqd/docker-images/terragrunt:latest
  stage: build
  resource_group: publish-modules
  before_script:
    - cd ${CI_PROJECT_DIR}/scripts/terraform-modules
    - mise trust --all
    - mise install -y
    - eval "$(mise env)"
    - pip install poetry
    - poetry install
  script:
    - bash ${CI_PROJECT_DIR}/scripts/generate-terraform-docs.sh
    - python3 publish.py
  rules:
    - if: '$CI_COMMIT_BRANCH == $DEV_BRANCH'
