---
.scan-base:
    stage: scan
    needs: []
    cache: []
    allow_failure: false

.veracode-zip-prep:
    before_script:
        - rm -f repo.zip
        - INCLUDE_PATTERNS=(
          "*.asp"
          "*.cjs"
          "*.css"
          "*.ehtml"
          "*.es"
          "*.es6"
          "*.handlebars"
          "*.hbs"
          "*.hjs"
          "*.htm"
          "*.html"
          "*.jar"
          "*.java"
          "*.js"
          "*.json"
          "*.jsx"
          "*.jsp"
          "*.map"
          "*.mjs"
          "*.mustache"
          "*.php"
          "*.properties"
          "*.py"
          "*.scss"
          "*.ts"
          "*.tsx"
          "*.vue"
          "*.xhtml"
          "*.xml"
          "build.gradle"
          "package-lock.json"
          "package.json"
          "pom.xml"
          "yarn.lock"
          )
        - EXCLUDE_PATTERNS=(
          ".env"
          ".git/*"
          "*.log"
          "*.md"
          "*.sh"
          "*.spec.*"
          "*.test.*"
          "*.zip"
          "build/*"
          "coverage/*"
          "dist/*"
          "Dockerfile*"
          "node_modules/*"
          )
        - INCLUDE_STRING=$(printf " -i %s" "${INCLUDE_PATTERNS[@]}")
        - EXCLUDE_STRING=$(printf " -x %s" "${EXCLUDE_PATTERNS[@]}")
        - zip -rq repo.zip ./ $INCLUDE_STRING $EXCLUDE_STRING
