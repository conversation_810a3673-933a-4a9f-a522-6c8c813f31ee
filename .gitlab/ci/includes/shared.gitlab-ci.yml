# script for running mise-en-place to install dependencies
.mise-en-place: &mise-en-place
  - |
    echo "Configuring mise-en-place..."
    echo -e "section_start:`date +%s`:mise-init[collapsed=true]\r\e[0Kmise initialization"
    mise install --yes
    mise reshim
    echo -e "section_end:`date +%s`:mise-init\r\e[0K"

# script for authenticating with gcloud
.gcloud-login: &gcloud-login
  - curl -s https://gitlab.com/gitlab-org/incubation-engineering/mobile-devops/download-secure-files/-/raw/main/installer | bash
  - cp .secure_files/${SERVICE_ACCOUNT_KEY} ${CI_PROJECT_DIR}/key.json
  - gcloud auth activate-service-account --key-file ${CI_PROJECT_DIR}/key.json
  - export GOOGLE_APPLICATION_CREDENTIALS=${CI_PROJECT_DIR}/key.json

# common base job for all jobs
.common-base:
  before_script:
    - *mise-en-place

# base job for jobs requiring GCP authentication
.gcp-auth-base:
  before_script:
    - *mise-en-place
    - *gcloud-login
  after_script:
    # remove the key.json file after the job completes
    - rm -f ${CI_PROJECT_DIR}/key.json
