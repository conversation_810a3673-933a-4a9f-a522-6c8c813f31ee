include:
  - local: .gitlab/ci/includes/terraform-base.gitlab-ci.yml
  - local: .gitlab/ci/includes/shared.gitlab-ci.yml
  - local: .gitlab/ci/tests/e2e-ui-review-apps.gitlab-ci.yml

variables:
  TF_STATE_STORAGE_BUCKET: gs://ict-p-statefiles
  REVIEW_APP_ENVIRONMENT: review/$CI_MERGE_REQUEST_ID
  REVIEW_APP_PREFIX: review/ui/FrontEndUI
  REVIEW_APP_STATEFILE: ${TF_STATE_STORAGE_BUCKET}/${REVIEW_APP_PREFIX}/${CI_MERGE_REQUEST_ID}.tfstate
  GITLAB_API_ENVIRONMENT_ENDPOINT: https://gitlab.com/api/v4/projects/${CI_PROJECT_ID}/environments

.prepare-review-app: &prepare-review-app
  - mise run build:${TARGET_ENVIRONMENT}
  - echo "Preparing to deploy ${REVIEW_APP_ENVIRONMENT}"
  - echo "Review App URL ${REVIEW_APP_URL}"
  - echo "Review App GCS Statefile ${REVIEW_APP_STATEFILE}"

.execute-review-app-terraform-action: &execute-review-app-terraform-action
  - cd ${UI_PROJECT_ROOT}/infrastructure/${TERRAFORM_MODULE}
  - bash ${CI_PROJECT_DIR}/scripts/execute-review-app-terraform-action.sh $ACTION $CI_MERGE_REQUEST_ID

.delete-review-app-statefile: &delete-review-app-statefile
  - bash ${CI_PROJECT_DIR}/scripts/delete-statefile.sh ${REVIEW_APP_STATEFILE}

.delete-review-app-environment: &delete-review-app-environment
  - echo "Installing jq..."
  - apt install -y jq
  - bash ${CI_PROJECT_DIR}/scripts/delete-gitlab-environment.sh $REVIEW_APP_ENVIRONMENT

.if-dev-mr: &if-dev-mr
  if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $DEV_BRANCH'

.review-app-base:
  variables:
    PROJECT_ROOT: $UI_PROJECT_ROOT
    TERRAFORM_MODULE: FrontEndUI
    TF_CONFIG_FILE: $TFVAR_ROOT/ui/dev.tfvars
  before_script:
    - cd $PROJECT_ROOT

ui:review-app:launch:
  stage: deploy
  extends:
    - .gcp-auth-base
    - .review-app-base
  needs: []
  allow_failure: false
  resource_group: $REVIEW_APP_ENVIRONMENT
  variables:
    ACTION: apply
  environment:
    name: $REVIEW_APP_ENVIRONMENT
    url: $REVIEW_APP_URL
    on_stop: ui:review-app:stop
    auto_stop_in: 10 days
  before_script:
    - !reference [.review-app-base, before_script]
    - !reference [.gcp-auth-base, before_script]
  script:
    - echo "Preparing review app..."
    - *prepare-review-app
    - *execute-review-app-terraform-action
    - bash ${CI_PROJECT_DIR}/scripts/poll-site-status.sh $REVIEW_APP_URL
  rules:
    - <<: *if-dev-mr
ui:review-app:stop:
  stage: .post
  extends:
    - .gcp-auth-base
    - .review-app-base
  cache: []
  needs: []
  allow_failure: true
  resource_group: $REVIEW_APP_ENVIRONMENT
  variables:
    ACTION: destroy
  environment:
    name: $REVIEW_APP_ENVIRONMENT
    action: stop
  before_script:
    - !reference [.review-app-base, before_script]
    - !reference [.gcp-auth-base, before_script]
  script:
    - echo "Destroying terraform infrastructure and removing GitLab environment.."
    - *execute-review-app-terraform-action
    - *delete-review-app-statefile
    - *delete-review-app-environment
  rules:
    - <<: *if-dev-mr
      when: manual

