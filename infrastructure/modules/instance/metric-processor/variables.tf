variable "artifact_registry_repository" {
  description = "The artifact registry repository configuration."
  type = object({
    location   = string
    project    = string
    repository = string
  })
}

variable "cloud_run_config" {
  description = <<EOF
    The configuration for the metric processor Cloud Run service. This includes the CPU and memory limits, 
    whether or not to enable binary auth for the image, as well as scaling configuration.
  EOF

  type = object({
    enable_binary_auth = bool

    cpu_limit    = string
    memory_limit = string

    cpu_idle          = bool
    startup_cpu_boost = bool

    min_instances = number
    max_instances = number
  })
}

variable "common_resource_labels" {
  description = "The common labels that should be applied to all resources that can be labeled."
  type        = map(string)
}

variable "container_image" {
  description = "The container image to use for the metric processor Cloud Run."
  type = object({
    image_name = string
    image_tag  = string
  })
}

variable "edp_project_id" {
  description = "The project ID of the EDP project containing the silver_adi topic."
  type        = string
}

variable "enforce_unique_naming" {
  description = <<EOF
    Set this to true to use the provided 'environment` value to create a unique prefix for all
    resources in the module. This is required to allow multiple instances of the module to be
    deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  EOF

  type = bool
}

variable "environment" {
  description = <<EOF
    The name of the environment that the resources are being deployed to. This value will be 
    combined with the `naming_root` to create a unique prefix for any resources requiring a 
    globally-unique identifier. Therefore, it must be unique among Control Tower environments but 
    should be as short as possible to comply with GCP resource name length restrictions.
  EOF

  type = string
}

variable "environment_variables" {
  description = <<EOF
    The environment variables to set in the Cloud Run service. This is a map of key-value pairs
    that will be passed to the container at runtime.
  EOF

  type = map(string)
}

variable "required_roles" {
  description = "The roles that should be applied to the metricprocessor service account."
  type        = list(string)
}

variable "naming_root" {
  description = <<EOF
    The root identifier for all Control Tower resources. This value will be combined with the
    `environment` to create a unique prefix for any resources requiring a globally-unique name. 
    This value is intended to be shared across all Control Tower deployments. As of module 
    creation, this value is "ict".
  EOF

  type = string
}

variable "network_config" {
  description = <<EOF
    Configuration for the network resources to be created.
 
    Note: this module does not validate the flow log configuration beyond ensuring it is not null
    if flow logs are enabled. It is intended that the flow log configuration is passed in from the
    output of the landing zone module.
 
    See the README and variables.tf of the network submodule of the landing zone module for more
    details on the configuration
  EOF

  type = object({
    enable_flow_logs  = bool
    network_self_link = string
    subnet_ip_range   = string

    flow_log_config = optional(object({
      aggregation_interval = string
      flow_sampling        = number
      metadata             = string
    }))
  })

  validation {
    error_message = "Flow log configuration cannot be null if flow logs are enabled."
    condition = (
      var.network_config.enable_flow_logs == false ||
      (
        var.network_config.flow_log_config != {} &&
        var.network_config.flow_log_config != null
      )
    )
  }
}

variable "project_id" {
  description = "The ID of the GCP project to deploy to."
  type        = string
}

variable "region" {
  description = "The region to deploy the Cloud Run in."
  type        = string
}

variable "vpc_access_connector_config" {
  description = "The configuration for the VPC access connector."
  type = object({
    machine_type  = string
    min_instances = number
    max_instances = number
  })
}

variable "zone" {
  description = "The zone to deploy resources that require a zone."
  type        = string
}

variable "vpc_access_connector_id" {
  description = <<EOT
    (Optional) The ID of an existing VPC access connector. If provided, the Cloud Run backends will 
    use this instead of the connector created by the module.

    Note: after migration to the shared VPC, the networking resources created by this module should 
    be removed and this should become non-optional.
  EOT

  type    = string
  default = ""
}
