<!-- BEGIN_TF_DOCS-->

<!-- NOTE: 
This file is automatically generated. 
Do not modify it manually. 
If you'd like to update the docs, update the header.md file in the templates directory.
-->

# metric processor Terraform Module

<!-- TODO: Add a brief description of what this module does -->

## Table of Contents

- [Description](#description)
- [Usage](#usage)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

<!-- TODO: Describe what this module creates and manages. Include:
- What resources it creates
- What problem it solves
- Any important configuration details
- Dependencies or prerequisites

Example:
This module creates and manages the metric-processor infrastructure. This includes:

- Resource 1
- Resource 2
- Resource 3
-->

<!-- TODO: Add any additional sections that are relevant to your module, such as:
- Architecture diagrams
- Configuration examples
- Troubleshooting
- Known limitations
-->

## Usage

Basic usage of this module is as follows:

```hcl
module "metric-processor" {
  source  = "gitlab.com/dematic/control-tower-metric-processor/google"
  version = "~> 0.1.0"

  # Required variables
  artifact_registry_repository = var.artifact_registry_repository
  cloud_run_config             = var.cloud_run_config
  common_resource_labels       = var.common_resource_labels
  container_image              = var.container_image
  edp_project_id               = var.edp_project_id
  enforce_unique_naming        = var.enforce_unique_naming
  environment                  = var.environment
  environment_variables        = var.environment_variables
  naming_root                  = var.naming_root
  network_config               = var.network_config
  project_id                   = var.project_id
  region                       = var.region
  required_roles               = var.required_roles
  vpc_access_connector_config  = var.vpc_access_connector_config
  zone                         = var.zone
}
```

## Requirements

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |

## Providers

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [google_cloud_run_service_iam_policy.metricprocessor_noauth](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/cloud_run_service_iam_policy) | resource |
| [google_cloud_run_v2_service.metricprocessor](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/cloud_run_v2_service) | resource |
| [google_compute_subnetwork.metricprocessor](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_subnetwork) | resource |
| [google_project_iam_member.metricprocessor](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member) | resource |
| [google_pubsub_subscription.silver_pubsub_to_metric_processor_subscription](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/pubsub_subscription) | resource |
| [google_service_account.metricprocessor](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/service_account) | resource |
| [google_vpc_access_connector.metricprocessor](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/vpc_access_connector) | resource |
| [google_artifact_registry_docker_image.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/artifact_registry_docker_image) | data source |
| [google_iam_policy.noauth](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/iam_policy) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| artifact\_registry\_repository | The artifact registry repository configuration. | ```object({ location = string project = string repository = string })``` | n/a | yes |
| cloud\_run\_config | The configuration for the metric processor Cloud Run service. This includes the CPU and memory limits,      whether or not to enable binary auth for the image, as well as scaling configuration. | ```object({ enable_binary_auth = bool cpu_limit = string memory_limit = string cpu_idle = bool startup_cpu_boost = bool min_instances = number max_instances = number })``` | n/a | yes |
| common\_resource\_labels | The common labels that should be applied to all resources that can be labeled. | `map(string)` | n/a | yes |
| container\_image | The container image to use for the metric processor Cloud Run. | ```object({ image_name = string image_tag = string })``` | n/a | yes |
| edp\_project\_id | The project ID of the EDP project containing the silver\_adi topic. | `string` | n/a | yes |
| enforce\_unique\_naming | Set this to true to use the provided 'environment` value to create a unique prefix for all     resources in the module. This is required to allow multiple instances of the module to be     deployed in the same GCP project. Used by Review Apps to generate the preview environments. ` | `bool` | n/a | yes |
| environment | The name of the environment that the resources are being deployed to. This value will be      combined with the `naming_root` to create a unique prefix for any resources requiring a      globally-unique identifier. Therefore, it must be unique among Control Tower environments but      should be as short as possible to comply with GCP resource name length restrictions. | `string` | n/a | yes |
| environment\_variables | The environment variables to set in the Cloud Run service. This is a map of key-value pairs     that will be passed to the container at runtime. | `map(string)` | n/a | yes |
| naming\_root | The root identifier for all Control Tower resources. This value will be combined with the     `environment` to create a unique prefix for any resources requiring a globally-unique name.      This value is intended to be shared across all Control Tower deployments. As of module      creation, this value is "ict". | `string` | n/a | yes |
| network\_config | Configuration for the network resources to be created.       Note: this module does not validate the flow log configuration beyond ensuring it is not null     if flow logs are enabled. It is intended that the flow log configuration is passed in from the     output of the landing zone module.       See the README and variables.tf of the network submodule of the landing zone module for more     details on the configuration | ```object({ enable_flow_logs = bool network_self_link = string subnet_ip_range = string flow_log_config = optional(object({ aggregation_interval = string flow_sampling = number metadata = string })) })``` | n/a | yes |
| project\_id | The ID of the GCP project to deploy to. | `string` | n/a | yes |
| region | The region to deploy the Cloud Run in. | `string` | n/a | yes |
| required\_roles | The roles that should be applied to the metricprocessor service account. | `list(string)` | n/a | yes |
| vpc\_access\_connector\_config | The configuration for the VPC access connector. | ```object({ machine_type = string min_instances = number max_instances = number })``` | n/a | yes |
| zone | The zone to deploy resources that require a zone. | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| service\_url | The URL on which the deployed service is available |

<!-- END_TF_DOCS-->
<!-- BEGIN_TFVARS_DOCS-->

## Terraform TFVARS file examples

```hcl
inputs {
    
  # The artifact registry repository configuration.
  artifact_registry_repository = ""
  
  #     The configuration for the metric processor Cloud Run service. This includes the CPU and memory limits,
  #     whether or not to enable binary auth for the image, as well as scaling configuration.
  #
  cloud_run_config = ""
  
  # The common labels that should be applied to all resources that can be labeled.
  common_resource_labels = ""
  
  # The container image to use for the metric processor Cloud Run.
  container_image = ""
  
  # The project ID of the EDP project containing the silver_adi topic.
  edp_project_id = ""
  
  #     Set this to true to use the provided 'environment` value to create a unique prefix for all
  #     resources in the module. This is required to allow multiple instances of the module to be
  #     deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  #
  enforce_unique_naming = ""
  
  #     The name of the environment that the resources are being deployed to. This value will be
  #     combined with the `naming_root` to create a unique prefix for any resources requiring a
  #     globally-unique identifier. Therefore, it must be unique among Control Tower environments but
  #     should be as short as possible to comply with GCP resource name length restrictions.
  #
  environment = ""
  
  #     The environment variables to set in the Cloud Run service. This is a map of key-value pairs
  #     that will be passed to the container at runtime.
  #
  environment_variables = ""
  
  #     The root identifier for all Control Tower resources. This value will be combined with the
  #     `environment` to create a unique prefix for any resources requiring a globally-unique name.
  #     This value is intended to be shared across all Control Tower deployments. As of module
  #     creation, this value is "ict".
  #
  naming_root = ""
  
  #     Configuration for the network resources to be created.
  #  
  #     Note: this module does not validate the flow log configuration beyond ensuring it is not null
  #     if flow logs are enabled. It is intended that the flow log configuration is passed in from the
  #     output of the landing zone module.
  #  
  #     See the README and variables.tf of the network submodule of the landing zone module for more
  #     details on the configuration
  #
  network_config = ""
  
  # The ID of the GCP project to deploy to.
  project_id = ""
  
  # The region to deploy the Cloud Run in.
  region = ""
  
  # The roles that should be applied to the metricprocessor service account.
  required_roles = ""
  
  # The configuration for the VPC access connector.
  vpc_access_connector_config = ""
  
  # The zone to deploy resources that require a zone.
  zone = ""
}
```

<!-- END_TFVARS_DOCS-->
