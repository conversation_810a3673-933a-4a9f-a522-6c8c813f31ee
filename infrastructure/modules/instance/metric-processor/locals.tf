locals {

  resource_namespace = "metric-processor"
  unique_root_name   = "${var.environment}-${local.resource_namespace}"

  root_name = (var.enforce_unique_naming
    ? local.unique_root_name
    : local.resource_namespace
  )

  artifact_registry_roles = [
    "roles/artifactregistry.reader"
  ]

  # Environment variable groups
  secret_manager_secret_regex = (
    "(?P<secret_id>projects/[^/]+/secrets/[^/]+)(?:/versions/(?P<version>[^/]+))?$"
  )

  # Pre-process all environment variables to identify secrets
  _env_var_analysis = {
    for key, value in var.environment_variables :
    key => {
      value     = value
      is_secret = can(regex(local.secret_manager_secret_regex, value))

      regex_match = (
        can(regex(local.secret_manager_secret_regex, value)) ?
        regex(local.secret_manager_secret_regex, value) : null
      )
    }
  }

  # if the value matches the secret version regex, it is a secret environment variable
  secret_env_variables = {
    for key, analysis in local._env_var_analysis :
    key => {
      secret_id      = analysis.regex_match["secret_id"]
      secret_version = coalesce(analysis.regex_match["version"], "latest")
    }
    if analysis.is_secret
  }

  # if the value doesn't match the secret regex, it is a regular string environment variable
  string_env_variables = {
    for key, analysis in local._env_var_analysis :
    key => analysis.value
    if !analysis.is_secret
  }

  vpc_access_connector_id = (var.vpc_access_connector_id == ""
    ? google_vpc_access_connector.metricprocessor.id
    : var.vpc_access_connector_id
  )
}
