# Service account and IAM roles
resource "google_service_account" "metricprocessor" {
  project      = var.project_id
  account_id   = local.root_name
  display_name = "Service account for metric processor Cloud Run"
}

resource "google_project_iam_member" "metricprocessor" {
  project  = var.project_id
  for_each = toset(var.required_roles)

  role   = each.value
  member = "serviceAccount:${google_service_account.metricprocessor.email}"
}

# resource "google_project_iam_member" "registry" {
#   project  = var.artifact_registry_repository.project
#   for_each = toset(local.artifact_registry_roles)

#   role   = each.value
#   member = "serviceAccount:${google_service_account.metricprocessor.email}"
# }

# Cloud Run service
resource "google_cloud_run_v2_service" "metricprocessor" {
  project  = var.project_id
  name     = local.root_name
  location = var.region

  deletion_protection = false
  ingress             = "INGRESS_TRAFFIC_ALL"

  dynamic "binary_authorization" {
    for_each = var.cloud_run_config.enable_binary_auth ? [1] : []

    content {
      use_default = true
    }
  }

  template {
    service_account = google_service_account.metricprocessor.email

    containers {
      image = data.google_artifact_registry_docker_image.main.self_link

      resources {
        limits = {
          cpu    = var.cloud_run_config.cpu_limit
          memory = var.cloud_run_config.memory_limit
        }

        cpu_idle          = var.cloud_run_config.cpu_idle
        startup_cpu_boost = var.cloud_run_config.startup_cpu_boost
      }

      dynamic "env" {
        for_each = local.string_env_variables

        content {
          name  = env.key
          value = env.value
        }
      }

      dynamic "env" {
        for_each = local.secret_env_variables
        content {
          name = env.key

          value_source {
            secret_key_ref {
              secret  = env.value.secret_id
              version = env.value.secret_version
            }
          }
        }
      }
    }

    scaling {
      min_instance_count = var.cloud_run_config.min_instances
      max_instance_count = var.cloud_run_config.max_instances
    }

    vpc_access {
      egress    = "PRIVATE_RANGES_ONLY"
      connector = local.vpc_access_connector_id
    }
  }

}

resource "google_cloud_run_service_iam_policy" "metricprocessor_noauth" {
  project  = var.project_id
  location = var.region
  service  = google_cloud_run_v2_service.metricprocessor.name

  policy_data = data.google_iam_policy.noauth.policy_data
}

resource "google_pubsub_subscription" "silver_pubsub_to_metric_processor_subscription" {
  ack_deadline_seconds = 10
  expiration_policy {
    ttl = "2678400s"
  }
  message_retention_duration = "604800s"
  name                       = "${var.project_id}-silver-pubsub-to-metric-processor-subscription"
  project                    = var.project_id
  labels                     = var.common_resource_labels

  push_config {
    push_endpoint = google_cloud_run_v2_service.metricprocessor.uri
    oidc_token {
      service_account_email = google_service_account.metricprocessor.email
      audience              = "allUsers"
    }
  }

  retry_policy {
    minimum_backoff = "10s"
  }

  topic = "projects/${var.edp_project_id}/topics/silver_adi"
}
