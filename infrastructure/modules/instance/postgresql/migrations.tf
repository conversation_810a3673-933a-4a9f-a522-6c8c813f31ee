resource "google_service_account" "migrations" {
  project      = var.project_id
  account_id   = local.migrations_root_name
  display_name = "Service account for running TypeORM migrations"
}

resource "google_project_iam_member" "migrations" {
  project  = var.project_id
  for_each = toset(local.migration_service_account_roles)

  role   = each.value
  member = "serviceAccount:${google_service_account.migrations.email}"
}

resource "google_project_iam_member" "registry" {
  project  = var.artifact_registry_repository.project
  for_each = toset(local.artifact_registry_roles)

  role   = each.value
  member = "serviceAccount:${google_service_account.migrations.email}"
}

resource "google_cloud_run_v2_service" "migrations" {
  project  = var.project_id
  name     = local.migrations_root_name
  location = var.region

  deletion_protection = false

  dynamic "binary_authorization" {
    for_each = var.migration_cloud_run_config.enable_binary_auth ? [1] : []

    content {
      use_default = true
    }
  }

  template {
    service_account = google_service_account.migrations.email

    containers {
      image = data.google_artifact_registry_docker_image.main.self_link

      resources {
        limits = {
          cpu    = var.migration_cloud_run_config.cpu_limit
          memory = var.migration_cloud_run_config.memory_limit
        }

        cpu_idle          = var.migration_cloud_run_config.cpu_idle
        startup_cpu_boost = var.migration_cloud_run_config.startup_cpu_boost
      }

      # volume_mounts {
      #   name       = "cloudsql"
      #   mount_path = "/cloudsql"
      # }

      dynamic "env" {
        for_each = local.cloud_run_env_vars

        content {
          name  = env.key
          value = env.value
        }
      }
    }

    # volumes {
    #   name = "cloudsql"

    #   cloud_sql_instance {
    #     instances = [module.postgresql.instance_connection_name]
    #   }
    # }

    # volumes {
    #   name = "secrets"

    #   secret {
    #     secret = google_secret_manager_secret.main.id

    #   }
    # }

    scaling {
      min_instance_count = var.migration_cloud_run_config.min_instances
      max_instance_count = var.migration_cloud_run_config.max_instances
    }

    vpc_access {
      egress    = "ALL_TRAFFIC"
      connector = google_vpc_access_connector.main.id
    }
  }

  depends_on = [google_project_iam_member.migrations, google_project_iam_member.registry]
}

resource "google_cloud_run_service_iam_policy" "noauth" {
  project  = var.project_id
  location = var.region
  service  = google_cloud_run_v2_service.migrations.name

  policy_data = data.google_iam_policy.noauth.policy_data
}
