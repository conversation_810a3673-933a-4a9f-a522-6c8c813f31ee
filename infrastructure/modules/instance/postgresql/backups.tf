resource "google_storage_bucket" "main" {
  project  = var.project_id
  name     = "${local.globally_unique_root_name}-backups"
  location = var.bucket_location
  labels   = var.common_resource_labels

  uniform_bucket_level_access = true
  force_destroy               = true

  logging {
    log_bucket        = var.access_logs_bucket
    log_object_prefix = "${local.root_name}-backups"
  }
}

module "backups" {
  source  = "GoogleCloudPlatform/sql-db/google//modules/backup"
  version = "~> 25.2.0"

  project_id = var.project_id
  region     = var.region

  deletion_protection = false
  sql_instance        = module.postgresql.instance_name
  export_uri          = google_storage_bucket.main.url
  export_databases    = []
  backup_schedule     = "5 1 * * *"
  export_schedule     = "10 4 * * *"

  enable_internal_backup = true
  enable_export_backup   = true
  backup_retention_time  = 1

  depends_on = [module.postgresql]
}