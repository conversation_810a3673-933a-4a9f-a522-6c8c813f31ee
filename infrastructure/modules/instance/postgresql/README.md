<!-- BEGIN_TF_DOCS-->

<!-- NOTE: 
This file is automatically generated. 
Do not modify it manually. 
If you'd like to update the docs, update the header.md file in the templates directory.
-->

# PostgreSQL Terraform Module

## Table of Contents

- [Description](#description)
- [Components](#components)
- [Future Improvements](#future-improvements)
- [Enabling Terraform Destroy](#enabling-terraform-destroy)
- [Usage](#usage)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This module configures a Google Cloud SQL PostgreSQL service instance that can be used as a backing database for Control Tower. It creates databases for the provided tenants, sets up nightly database backups, creates a Secret Manager secret containing the connection information, and sets up a Cloud Run that uses TypeORM to populate the database(s).

## Components

This module is comprised of the following groups of components:

### Server and Databases

The `main.tf` file creates the database and supporting resources.

Resources:

- Random password paired with the provided `database_username` variable to create the database user
- A PostgreSQL CloudSQL instance
- Databases for each of the provided tenants
- Database users for each of the provided users
- A Secret Manager secret containing the connection info

### Backups

The `backups.tf` file configures backups for the server.

Resources:

- GCS bucket for the backups
- Backup schedule that runs nightly and exports the artifact(s) to the bucket

### TypeORM Migrations

The `migrations.tf` file creates the a service that uses TypeORM to push updates to the tenant database(s).

Resources:

- Service account with roles assignments:
  - required services in the Control Tower project
  - access to Artifact Registry in the core services project for pulling the Docker image
- Cloud Run service that runs the corresponding Docker image from Artifact Registry
- IAM policy that allows all traffic to invoke the CloudRun

## Future Improvements

Opportunties for improvement:

- Expose backup configuration as module variables

## Enabling Terraform Destroy

The following resource configurations are required to allow this module's resources to be successfully destroyed:

- "postgresql" Cloud SQL module:
  - `deletion_protection = false`
  - `database_deletion_policy = "ABANDON"`
  - `user_deletion_policy = "ABANDON"`
- "migrations" Cloud Run resource:
  - `deletion_protection = false`
- "backups" SQL DB Backup module:
  - `deletion_protection = false`

## Usage

Basic usage of this module is as follows:

```hcl
module "postgresql" {
  source  = "gitlab.com/dematic/control-tower-postgresql/google"
  version = "~> 0.1.0"

  # Required variables
  access_logs_bucket           = var.access_logs_bucket
  artifact_registry_repository = var.artifact_registry_repository
  bucket_location              = var.bucket_location
  common_resource_labels       = var.common_resource_labels
  database_username            = var.database_username
  databases                    = var.databases
  enforce_unique_naming        = var.enforce_unique_naming
  environment                  = var.environment
  environment_variables        = var.environment_variables
  instance_prefix              = var.instance_prefix
  migration_cloud_run_config   = var.migration_cloud_run_config
  migration_container_image    = var.migration_container_image
  naming_root                  = var.naming_root
  network_config               = var.network_config
  project_id                   = var.project_id
  region                       = var.region
  vpc_access_connector_config  = var.vpc_access_connector_config
  zone                         = var.zone
}
```

## Requirements

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |

## Providers

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |
| random | n/a |

## Modules

| Name | Source | Version |
|------|--------|---------|
| backups | GoogleCloudPlatform/sql-db/google//modules/backup | ~> 25.2.0 |
| postgresql | GoogleCloudPlatform/sql-db/google//modules/postgresql | ~> 25.2.0 |

## Resources

| Name | Type |
|------|------|
| [google_cloud_run_service_iam_policy.noauth](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/cloud_run_service_iam_policy) | resource |
| [google_cloud_run_v2_service.migrations](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/cloud_run_v2_service) | resource |
| [google_compute_subnetwork.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_subnetwork) | resource |
| [google_project_iam_member.migrations](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member) | resource |
| [google_project_iam_member.registry](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member) | resource |
| [google_secret_manager_secret.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/secret_manager_secret) | resource |
| [google_secret_manager_secret_version.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/secret_manager_secret_version) | resource |
| [google_service_account.migrations](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/service_account) | resource |
| [google_storage_bucket.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/storage_bucket) | resource |
| [google_vpc_access_connector.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/vpc_access_connector) | resource |
| [random_password.main](https://registry.terraform.io/providers/hashicorp/random/latest/docs/resources/password) | resource |
| [google_artifact_registry_docker_image.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/artifact_registry_docker_image) | data source |
| [google_iam_policy.noauth](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/iam_policy) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| access\_logs\_bucket | The name of the access logs bucket. | `string` | n/a | yes |
| artifact\_registry\_repository | The Artifact Registry repository containing the migration container image.      T | ```object({ location = string project = string repository = string })``` | n/a | yes |
| bucket\_location | The location in which to create the bucket to use for backups. Examples: us-east1, us | `string` | n/a | yes |
| common\_resource\_labels | The common labels that should be applied to all resources that can be labeled. | `map(string)` | n/a | yes |
| database\_username | The username to use for the PostgreSQL database. | `string` | n/a | yes |
| databases | A list of PostgreSQL databases to be created in the environment. | `list(string)` | n/a | yes |
| enforce\_unique\_naming | Set this to true to use the provided 'environment` value to create a unique prefix for all      resources in the module. This is required to allow multiple instances of the module to be      deployed in the same GCP project. Used by Review Apps to generate the preview environments. ` | `bool` | n/a | yes |
| environment | The name of the environment that the Control Tower instance is being deployed to. E.g., 'dev'. | `string` | n/a | yes |
| environment\_variables | The environment variables to set in the Cloud Run service. This is a map of key-value pairs      that will be passed to the container at runtime. | `map(string)` | n/a | yes |
| instance\_prefix | The name of the instance that the resources are being deployed to. This value will be      combined with the `naming_root` to create a unique prefix for any resources requiring a      globally-unique identifier. Therefore, it must be unique among Control Tower instances but      should be as short as possible to comply with GCP resource name length restrictions. | `string` | n/a | yes |
| migration\_cloud\_run\_config | The configuration for the Cloud Run service. This includes the CPU and memory limits, whether      or not to enable binary auth for the image, as well as any environment variables that should be      set on the service. | ```object({ enable_binary_auth = bool cpu_limit = number memory_limit = string cpu_idle = bool startup_cpu_boost = bool min_instances = number max_instances = number })``` | n/a | yes |
| migration\_container\_image | The container image to use for the migration Cloud Run. | ```object({ image_name = string image_tag = string })``` | n/a | yes |
| naming\_root | The root identifier for all Control Tower resources. This value will be combined with the     `environment` to create a unique prefix for any resources requiring a globally-unique name.      This value is intended to be shared across all Control Tower deployments. As of module      creation, this value is "ict". | `string` | n/a | yes |
| network\_config | Configuration for the network resources to be created.      Note: this module does not validate the flow log configuration beyond ensuring it is not null      if flow logs are enabled. It is intended that the flow log configuration is passed in from the      output of the landing zone module.      See the README and variables.tf of the network submodule of the landing zone module for more      details on the configuration | ```object({ enable_flow_logs = bool network_self_link = string subnet_ip_range = string flow_log_config = optional(object({ aggregation_interval = string flow_sampling = number metadata = string })) })``` | n/a | yes |
| project\_id | The ID of the GCP project to deploy to. | `string` | n/a | yes |
| region | The region to deploy the Cloud Run in. | `string` | n/a | yes |
| vpc\_access\_connector\_config | The configuration for the VPC access connector. | ```object({ machine_type = string min_instances = number max_instances = number })``` | n/a | yes |
| zone | The zone to deploy the database in. | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| backups | The output of the backup module, which includes the bucket name, bucket URL, and service      account used by the backup Cloud Run service. |
| connection\_string\_secret\_name | The name of the secret that contains the connection string to the PostgreSQL instance. |
| connection\_string\_secret\_version | The name of the secret that contains the connection string to the PostgreSQL instance. |
| instances | The list of instances in the PostgreSQL instance pool. |
| master\_instance | The master instance information, including connection name, DNS name, instance name, IP      address, and zone. This is used for connecting to the PostgreSQL instance. |
| migration\_cloud\_run | The output of the migration Cloud Run service, which includes the service account email,          service ID, location, name, revision, status, and URL. |

<!-- END_TF_DOCS-->
<!-- BEGIN_TFVARS_DOCS-->

## Terraform TFVARS file examples

```hcl
inputs {
    
  # The name of the access logs bucket.
  access_logs_bucket = ""
  
  #     The Artifact Registry repository containing the migration container image.
  #
  #     T
  #
  artifact_registry_repository = ""
  
  #     The location in which to create the bucket to use for backups. Examples: us-east1, us
  #
  bucket_location = ""
  
  #     The common labels that should be applied to all resources that can be labeled.
  #
  common_resource_labels = ""
  
  # The username to use for the PostgreSQL database.
  database_username = ""
  
  # A list of PostgreSQL databases to be created in the environment.
  databases = ""
  
  #     Set this to true to use the provided 'environment` value to create a unique prefix for all
  #     resources in the module. This is required to allow multiple instances of the module to be
  #     deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  #
  enforce_unique_naming = ""
  
  #     The name of the environment that the Control Tower instance is being deployed to. E.g., 'dev'.
  #
  environment = ""
  
  #     The environment variables to set in the Cloud Run service. This is a map of key-value pairs
  #     that will be passed to the container at runtime.
  #
  environment_variables = ""
  
  #     The name of the instance that the resources are being deployed to. This value will be
  #     combined with the `naming_root` to create a unique prefix for any resources requiring a
  #     globally-unique identifier. Therefore, it must be unique among Control Tower instances but
  #     should be as short as possible to comply with GCP resource name length restrictions.
  #
  instance_prefix = ""
  
  #     The configuration for the Cloud Run service. This includes the CPU and memory limits, whether
  #     or not to enable binary auth for the image, as well as any environment variables that should be
  #     set on the service.
  #
  migration_cloud_run_config = ""
  
  # The container image to use for the migration Cloud Run.
  migration_container_image = ""
  
  #     The root identifier for all Control Tower resources. This value will be combined with the
  #     `environment` to create a unique prefix for any resources requiring a globally-unique name.
  #     This value is intended to be shared across all Control Tower deployments. As of module
  #     creation, this value is "ict".
  #
  naming_root = ""
  
  #     Configuration for the network resources to be created.
  #
  #     Note: this module does not validate the flow log configuration beyond ensuring it is not null
  #     if flow logs are enabled. It is intended that the flow log configuration is passed in from the
  #     output of the landing zone module.
  #
  #     See the README and variables.tf of the network submodule of the landing zone module for more
  #     details on the configuration
  #
  network_config = ""
  
  # The ID of the GCP project to deploy to.
  project_id = ""
  
  # The region to deploy the Cloud Run in.
  region = ""
  
  # The configuration for the VPC access connector.
  vpc_access_connector_config = ""
  
  # The zone to deploy the database in.
  zone = ""
}
```

<!-- END_TFVARS_DOCS-->
