variable "access_logs_bucket" {
  description = "The name of the access logs bucket."
  type        = string
}

variable "artifact_registry_repository" {
  description = <<EOT
    The Artifact Registry repository containing the migration container image.

    T
  EOT

  type = object({
    location   = string
    project    = string
    repository = string
  })
}

variable "bucket_location" {
  description = <<EOT
    The location in which to create the bucket to use for backups. Examples: us-east1, us
  EOT

  type = string
}

variable "common_resource_labels" {
  description = <<EOT
    The common labels that should be applied to all resources that can be labeled.
  EOT

  type = map(string)
}

variable "database_username" {
  description = "The username to use for the PostgreSQL database."
  type        = string
}

variable "databases" {
  description = "A list of PostgreSQL databases to be created in the environment."
  type        = list(string)
}

variable "enforce_unique_naming" {
  description = <<EOT
    Set this to true to use the provided 'environment` value to create a unique prefix for all 
    resources in the module. This is required to allow multiple instances of the module to be 
    deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  EOT

  type = bool
}

variable "environment" {
  description = <<EOT
    The name of the environment that the Control Tower instance is being deployed to. E.g., 'dev'.
  EOT

  type = string
}

variable "environment_variables" {
  description = <<EOT
    The environment variables to set in the Cloud Run service. This is a map of key-value pairs 
    that will be passed to the container at runtime.
  EOT

  type = map(string)
}

variable "instance_prefix" {
  description = <<EOT
    The name of the instance that the resources are being deployed to. This value will be 
    combined with the `naming_root` to create a unique prefix for any resources requiring a 
    globally-unique identifier. Therefore, it must be unique among Control Tower instances but 
    should be as short as possible to comply with GCP resource name length restrictions.
  EOT

  type = string
}

variable "migration_cloud_run_config" {
  description = <<EOT
    The configuration for the Cloud Run service. This includes the CPU and memory limits, whether 
    or not to enable binary auth for the image, as well as any environment variables that should be 
    set on the service.
  EOT

  type = object({
    enable_binary_auth = bool

    cpu_limit    = number
    memory_limit = string

    cpu_idle          = bool
    startup_cpu_boost = bool

    min_instances = number
    max_instances = number
  })
}

variable "migration_container_image" {
  description = "The container image to use for the migration Cloud Run."
  type = object({
    image_name = string
    image_tag  = string
  })
}

variable "naming_root" {
  description = <<EOT
    The root identifier for all Control Tower resources. This value will be combined with the
    `environment` to create a unique prefix for any resources requiring a globally-unique name. 
    This value is intended to be shared across all Control Tower deployments. As of module 
    creation, this value is "ict".
  EOT

  type = string
}

variable "network_config" {
  description = <<EOT
    Configuration for the network resources to be created.

    Note: this module does not validate the flow log configuration beyond ensuring it is not null 
    if flow logs are enabled. It is intended that the flow log configuration is passed in from the 
    output of the landing zone module.

    See the README and variables.tf of the network submodule of the landing zone module for more 
    details on the configuration
  EOT

  type = object({
    enable_flow_logs  = bool
    network_self_link = string
    subnet_ip_range   = string

    flow_log_config = optional(object({
      aggregation_interval = string
      flow_sampling        = number
      metadata             = string
    }))
  })

  validation {
    error_message = "Flow log configuration cannot be null if flow logs are enabled."
    condition = (
      var.network_config.enable_flow_logs == false ||
      (
        var.network_config.flow_log_config != {} &&
        var.network_config.flow_log_config != null
      )
    )
  }
}

variable "project_id" {
  description = "The ID of the GCP project to deploy to."
  type        = string
}

variable "region" {
  description = "The region to deploy the Cloud Run in."
  type        = string
}

variable "vpc_access_connector_config" {
  description = "The configuration for the VPC access connector."
  type = object({
    machine_type  = string
    min_instances = number
    max_instances = number
  })
}

variable "zone" {
  description = "The zone to deploy the database in."
  type        = string
}
