# PostgreSQL Terraform Module

## Table of Contents

- [Description](#description)
- [Components](#components)
- [Future Improvements](#future-improvements)
- [Enabling Terraform Destroy](#enabling-terraform-destroy)
- [Usage](#usage)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This module configures a Google Cloud SQL PostgreSQL service instance that can be used as a backing database for Control Tower. It creates databases for the provided tenants, sets up nightly database backups, creates a Secret Manager secret containing the connection information, and sets up a Cloud Run that uses TypeORM to populate the database(s).

## Components

This module is comprised of the following groups of components:

### Server and Databases

The `main.tf` file creates the database and supporting resources.

Resources:

- Random password paired with the provided `database_username` variable to create the database user
- A PostgreSQL CloudSQL instance
- Databases for each of the provided tenants
- Database users for each of the provided users
- A Secret Manager secret containing the connection info

### Backups

The `backups.tf` file configures backups for the server.

Resources:

- GCS bucket for the backups
- Backup schedule that runs nightly and exports the artifact(s) to the bucket

### TypeORM Migrations

The `migrations.tf` file creates the a service that uses TypeORM to push updates to the tenant database(s).

Resources:

- Service account with roles assignments:
  - required services in the Control Tower project
  - access to Artifact Registry in the core services project for pulling the Docker image
- Cloud Run service that runs the corresponding Docker image from Artifact Registry
- IAM policy that allows all traffic to invoke the CloudRun

## Future Improvements

Opportunties for improvement:

- Expose backup configuration as module variables

## Enabling Terraform Destroy

The following resource configurations are required to allow this module's resources to be successfully destroyed:

- "postgresql" Cloud SQL module:
  - `deletion_protection = false`
  - `database_deletion_policy = "ABANDON"`
  - `user_deletion_policy = "ABANDON"`
- "migrations" Cloud Run resource:
  - `deletion_protection = false`
- "backups" SQL DB Backup module:
  - `deletion_protection = false`
