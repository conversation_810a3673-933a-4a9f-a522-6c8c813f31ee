locals {
  resource_namespace = "api-backend"
  unique_root_name   = "${var.instance_prefix}-${local.resource_namespace}"

  root_name = (var.enforce_unique_naming
    ? local.unique_root_name
    : local.resource_namespace
  )

  # finds the `service_type` from the backend and storage enabled backend services
  default_backend_service_type = (lookup(
    merge(var.backend_services, var.storage_enabled_backend_services),
    var.default_service_name
  ).service_type)

  default_backend_service_name = "${var.default_service_name}-${local.default_backend_service_type}"

  vpc_access_connector_id = (var.vpc_access_connector_id == ""
    ? google_vpc_access_connector.main.id
    : var.vpc_access_connector_id
  )
}
