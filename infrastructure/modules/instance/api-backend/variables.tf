variable "backend_services" {
  description = <<EOT
    The collection of standard backend services to deploy, with each service represented by an 
    object in the map. The map key is the service name, and will be used for naming the Cloud Run 
    and the supporting resources; the value is an object containing the configuration for that 
    service. See the `backend_service` sub-module for more details on the configuration options for 
    the service.
  EOT

  type = map(object({
    service_friendly_name = string
    url_map_path          = string

    container_image = object({
      image_name = string
      image_tag  = optional(string)
    })

    labels         = optional(map(string))
    region         = optional(string)
    required_roles = optional(list(string))
    service_type   = optional(string)

    basic_health_check_path = optional(string)
    enable_binary_auth      = optional(bool)
    environment_variables   = optional(map(string))
    full_health_check_path  = optional(string)
    security_policy_id      = optional(string)

    artifact_registry_repository = optional(object({
      location   = string
      project    = string
      repository = string
    }))

    backend_service_config = optional(object({
      protocol  = string
      port_name = string
      timeout   = number

      log_config = object({
        enable        = bool
        sampling_rate = optional(number)
      })
    }))

    cloud_run_config = optional(object({
      enable_binary_auth = bool

      cpu_limit         = string
      memory_limit      = string
      cpu_idle          = bool
      startup_cpu_boost = bool

      min_instances = number
      max_instances = number
    }))
  }))
}

variable "cloud_armor_config" {
  description = <<EOT
    The configuration for the Cloud Armor security policy that will be applied to the backend 
    services. Layer 7 DDoS protection is optional and can be enabled or disabled. If enabled, all 
    additional configuration options must be specified; if disabled, the optional configuration 
    options can be omitted.
  EOT

  type = object({
    module_version = string

    layer_7_ddos_config = object({
      enable = bool

      default_rule_action = optional(string)
      rule_visibility     = optional(string)
      json_parsing        = optional(string)
      log_level           = optional(string)
    })
  })
}

variable "common_config" {
  description = "The common configuration that should be applied to all Cloud Run services."

  type = object({
    security_roles = list(string)

    environment_variables = optional(map(string))
  })
}

variable "common_resource_labels" {
  description = "The common labels that should be applied to all resources that can be labeled."
  type        = map(string)
}

variable "default_config" {
  description = <<EOT
    The default configuration for the Cloud Run service. This will be used if no specific 
    configuration is provided for a service.
  EOT

  type = object({
    image_tag = optional(string)
    region    = string

    basic_health_check_path = string
    full_health_check_path  = string

    artifact_registry_repository = object({
      location   = string
      project    = string
      repository = string
    })

    backend_service_config = object({
      protocol  = string
      port_name = string
      timeout   = number

      log_config = object({
        enable        = bool
        sampling_rate = optional(number)
      })
    })

    bucket_config = optional(object({
      location                = string
      access_logs_bucket_name = string
      storage_class           = string
    }))

    cloud_run_config = object({
      enable_binary_auth = bool

      cpu_limit         = string
      memory_limit      = string
      cpu_idle          = bool
      startup_cpu_boost = bool

      min_instances = number
      max_instances = number
    })
  })
}

variable "default_service_name" {
  description = <<EOT
    The name of the service that should serve as the default backend for the API. This backend will 
    be registered as the default route on the load balancer; the value must match the key of one 
    of the services defined in the `backend_services` or `storage_enabled_backend_services` map 
    variables.
  EOT

  type = string
}

variable "enforce_unique_naming" {
  description = <<EOT
    Set this to true to use the provided 'environment` value to create a unique prefix for all 
    resources in the module. This is required to allow multiple instances of the module to be 
    deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  EOT

  type = bool
}

variable "instance_display_name" {
  description = <<EOT
    The human-readable name of the instance that the resources are being deployed to.
  EOT

  type = string
}

variable "instance_prefix" {
  description = <<EOT
    The name of the instance that the resources are being deployed to. This value will be 
    combined with the `naming_root` to create a unique prefix for any resources requiring a 
    globally-unique identifier. Therefore, it must be unique among Control Tower instances but 
    should be as short as possible to comply with GCP resource name length restrictions.
  EOT

  type = string
}

variable "load_balancing_scheme" {
  description = <<EOT
    The load balancing scheme to use for the load balancer. For an external load balancer, this can 
    be either `EXTERNAL` or `EXTERNAL_MANAGED`; for an internal load balancer, this must be either 
    `INTERNAL` or `INTERNAL_MANAGED`. In both cases, the latter `MANAGED` option is recommended for
    production use, as it provides better performance and reliability.
  EOT

  type = string

  validation {
    condition = contains(
      ["EXTERNAL", "EXTERNAL_MANAGED", "INTERNAL", "INTERNAL_MANAGED"],
      var.load_balancing_scheme
    )

    error_message = <<EOT
      The load balancing scheme must be one of: EXTERNAL, EXTERNAL_MANAGED, INTERNAL, 
      INTERNAL_MANAGED."
    EOT
  }
}

variable "monitoring_notification_email" {
  description = "The email address that monitoring notifications will be sent to."
  type        = string
}

variable "naming_root" {
  description = <<EOT
    The root identifier for all Control Tower resources. This value will be combined with the
    `environment` to create a unique prefix for any resources requiring a globally-unique name. 
    This value is intended to be shared across all Control Tower deployments. As of module 
    creation, this value is "ict".
  EOT

  type = string
}

variable "network_config" {
  description = <<EOT
    Configuration for the network resources to be created.

    Note: this module does not validate the flow log configuration beyond ensuring it is not null 
    if flow logs are enabled. It is intended that the flow log configuration is passed in from the 
    output of the landing zone module.

    See the README and variables.tf of the network submodule of the landing zone module for more 
    details on the configuration
  EOT

  type = object({
    enable_flow_logs  = bool
    network_self_link = string
    subnet_ip_range   = string

    flow_log_config = optional(object({
      aggregation_interval = string
      flow_sampling        = number
      metadata             = string
    }))
  })

  validation {
    error_message = "Flow log configuration cannot be null if flow logs are enabled."
    condition = (
      var.network_config.enable_flow_logs == false ||
      (
        var.network_config.flow_log_config != {} &&
        var.network_config.flow_log_config != null
      )
    )
  }
}

variable "project_id" {
  description = "The ID of the GCP project to deploy to."
  type        = string
}

variable "region" {
  description = "The region to deploy regional resources in."
  type        = string
}

variable "storage_enabled_backend_services" {
  description = <<EOT
    The collection of storage-enabled backend services to deploy, with each service represented by 
    an object in the map. The map key is the service name, and will be used for naming the Cloud 
    Run and the supporting resources; the value is an object containing the configuration for that 
    service. See the 'storage_enabled_backend_service' sub-module for more details on the
    configuration options for the service.
  EOT

  type = map(object({
    service_friendly_name = string
    url_map_path          = string

    container_image = object({
      image_name = string
      image_tag  = optional(string)
    })

    labels         = optional(map(string))
    region         = optional(string)
    required_roles = optional(list(string))
    service_type   = optional(string)

    basic_health_check_path = optional(string)
    enable_binary_auth      = optional(bool)
    environment_variables   = optional(map(string))
    full_health_check_path  = optional(string)
    security_policy_id      = optional(string)

    artifact_registry_repository = optional(object({
      location   = string
      project    = string
      repository = string
    }))

    backend_service_config = optional(object({
      protocol  = string
      port_name = string
      timeout   = number

      log_config = object({
        enable        = bool
        sampling_rate = optional(number)
      })
    }))

    bucket_config = optional(object({
      location                = string
      access_logs_bucket_name = string
      storage_class           = string
    }))

    bucket_objects = optional(list(object({
      name               = string
      object_source_path = string,
      detect_md5hash     = bool
    })))

    cloud_run_config = optional(object({
      enable_binary_auth = bool

      cpu_limit         = string
      memory_limit      = string
      cpu_idle          = bool
      startup_cpu_boost = bool

      min_instances = number
      max_instances = number
    }))
  }))
}

variable "vpc_access_connector_config" {
  description = "The configuration for the VPC access connector."
  type = object({
    machine_type  = string
    min_instances = number
    max_instances = number
  })
}

variable "vpc_access_connector_id" {
  description = <<EOT
    (Optional) The ID of an existing VPC access connector. If provided, the Cloud Run backends will 
    use this instead of the connector created by the module.

    Note: after migration to the shared VPC, the networking resources created by this module should 
    be removed and this should become non-optional.
  EOT

  type    = string
  default = ""
}
