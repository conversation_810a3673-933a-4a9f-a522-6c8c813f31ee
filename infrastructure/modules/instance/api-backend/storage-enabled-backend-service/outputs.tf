output "backend_service_id" {
  description = "The ID of the load balancer backend service that was created for this API service."
  value       = module.api_service.backend_service_id
}

output "basic_health_check_path" {
  description = "The endpoint for the basic health check."
  value       = module.api_service.basic_health_check_path
}

output "bucket_url" {
  description = "The URL of the Cloud Storage bucket created for this API service."
  value       = google_storage_bucket.main.url
}

output "full_health_check_path" {
  description = "The endpoint for the full health check."
  value       = module.api_service.full_health_check_path
}

output "service_account_email" {
  description = "The email of the Service Account created for the API backend"
  value       = module.api_service.service_account_email
}

output "service_friendly_name" {
  description = "The friendly name of the service exposed by the API endpoint."

  value = module.api_service.service_friendly_name
}

output "service_name" {
  description = "The name of the Cloud Run service created for this API backend"
  value       = module.api_service.service_name
}

output "url_map_path" {
  description = "The path to register for this backend service on the URL map."
  value       = module.api_service.url_map_path
}
