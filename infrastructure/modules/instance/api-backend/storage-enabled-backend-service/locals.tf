locals {
  # Resource naming
  resource_namespace        = "${var.service_name}-${var.service_type}-data"
  unique_root_name          = "${var.instance_prefix}-${local.resource_namespace}"
  globally_unique_root_name = "${var.naming_root}-${local.unique_root_name}"

  root_name = (var.enforce_unique_naming
    ? "${local.unique_root_name}-${var.service_type}"
    : "${local.resource_namespace}-${var.service_type}"
  )

  cloud_run_environment_variables = merge(
    var.environment_variables,
    {
      BUCKETNAME = google_storage_bucket.main.name
    }
  )
}
