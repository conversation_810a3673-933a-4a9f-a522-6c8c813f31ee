variable "artifact_registry_repository" {
  description = <<EOT
    The Artifact Registry repository containing the container image to deploy in the Cloud Run. 
    This includes the following fields:
    - `location`: The location of the Artifact Registry.
    - `project_id`: The project ID of the Artifact Registry.
    - `repository_id`: The repository ID of the Artifact Registry repository.
  EOT

  type = object({
    location   = string
    project    = string
    repository = string
  })
}

variable "backend_service_config" {
  description = <<EOT
    The configuration for the backend service. This includes the protocol, port name, timeout, and 
    log configuration. If `log_config.enable` is set to `true`, the `sampling_rate` must be set.

    - `protocol`: The protocol to use for the backend service (e.g., HTTP, HTTPS).
    - `port-name`: The name of the port to use for the backend service.
    - `timeout`: The timeout for requests to the backend service.
    - `log_config`: The log configuration for the backend service.
      - `enable`: Whether to enable logging for the backend service.
      - `sampling_rate`: The sampling rate for logging (optional - only required if `enable` is 
          `true`).
  EOT

  type = object({
    protocol  = string
    port_name = string
    timeout   = number

    log_config = object({
      enable        = bool
      sampling_rate = optional(number)
    })
  })

  # ensure that sampling rate is set if logging is enabled
  validation {
    condition = (
      !(var.backend_service_config.log_config.enable &&
      !contains(keys(var.backend_service_config.log_config), "sampling_rate"))
    )
    error_message = "If logging is enabled, the sampling rate must be set."
  }
}

variable "basic_health_check_path" {
  description = "The relative path of the basic health check endpoint in the container."
  type        = string
}

variable "bucket_config" {
  description = <<EOT
    The configuration for the storage bucket used by the service. This includes the location, the 
    name of the access logs bucket, and the storage class. The storage class must be one of
    `STANDARD`, `NEARLINE`, `COLDLINE`, or `ARCHIVE`.
  EOT

  type = object({
    location                = string
    access_logs_bucket_name = string
    storage_class           = string
  })

  validation {
    condition = (
      contains(["STANDARD", "NEARLINE", "COLDLINE", "ARCHIVE"],
      var.bucket_config.storage_class)
    )
    error_message = "The storage class must be one of STANDARD, NEARLINE, COLDLINE, or ARCHIVE."
  }
}

variable "bucket_objects" {
  description = <<EOT
    An optional list of objects to upload to the bucket. This is limited to resources contained in 
    the repository and available at the paths specified by `object_source_path` during Terraform 
    CI job execution.
  EOT

  type = list(object({
    name               = string
    object_source_path = string,
    detect_md5hash     = bool
  }))
  # default = {}
}

variable "cloud_run_config" {
  description = "The configuration for the Cloud Run service."
  type = object({
    enable_binary_auth = bool

    cpu_limit    = string,
    memory_limit = string,

    cpu_idle          = bool
    startup_cpu_boost = bool

    min_instances = number
    max_instances = number
  })
}

variable "common_resource_labels" {
  description = "The common labels that should be applied to all resources that can be labeled."
  type        = map(string)
}

variable "container_image" {
  description = "The Artifact Registry image to deploy."
  type = object({
    image_name = string,
    image_tag  = string,
  })
}

variable "enforce_unique_naming" {
  description = <<EOT
    Set this to true to use the provided 'environment` value to create a unique prefix for all 
    resources in the module. This is required to allow multiple instances of the module to be 
    deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  EOT

  type = bool
}

variable "environment_variables" {
  description = <<EOT
    The environment variables to set in the Cloud Run service. This is a map of key-value pairs 
    that will be passed to the container at runtime.
  EOT

  type = map(string)
}

variable "full_health_check_path" {
  description = "The relative path of the full health check endpoint in the container."
  type        = string
}

variable "instance_prefix" {
  description = <<EOT
    The name of the instance that the resources are being deployed to. This value will be 
    combined with the `naming_root` to create a unique prefix for any resources requiring a 
    globally-unique identifier. Therefore, it must be unique among Control Tower instances but 
    should be as short as possible to comply with GCP resource name length restrictions.
  EOT

  type = string
}

variable "load_balancing_scheme" {
  description = <<EOT
    The load balancing scheme to use for the load balancer. For an external load balancer, this can 
    be either `EXTERNAL` or `EXTERNAL_MANAGED`; for an internal load balancer, this must be either 
    `INTERNAL` or `INTERNAL_MANAGED`. In both cases, the latter `MANAGED` option is recommended for
    production use, as it provides better performance and reliability.
  EOT

  type = string

  validation {
    condition = contains(
      ["EXTERNAL", "EXTERNAL_MANAGED", "INTERNAL", "INTERNAL_MANAGED"],
      var.load_balancing_scheme
    )

    error_message = <<EOT
      The load balancing scheme must be one of: EXTERNAL, EXTERNAL_MANAGED, INTERNAL, 
      INTERNAL_MANAGED."
    EOT
  }
}

variable "naming_root" {
  description = <<EOT
    The root identifier for all Control Tower resources. This value will be combined with the
    `environment` to create a unique prefix for any resources requiring a globally-unique name. 
    This value is intended to be shared across all Control Tower deployments. As of module 
    creation, this value is "ict".
  EOT

  type = string
}

variable "project_id" {
  description = "The ID of the GCP project to deploy to."
  type        = string
}

variable "region" {
  description = "The region to deploy regional resources in."
  type        = string
}

variable "required_roles" {
  description = "The roles to grant the Service Account."
  type        = list(string)
}

variable "security_policy_id" {
  description = <<EOT
    The ID of the security policy to attach to the backend service. This is used for Cloud Armor 
    security policies.
  EOT

  type = string
}

variable "service_friendly_name" {
  description = <<EOT
    The friendly name of the service exposed by the API endpoint. Example: 'Equipment API'.
  EOT

  type = string
}

variable "service_name" {
  description = "The name of the service that the API endpoint exposes."
  type        = string
}

variable "service_type" {
  description = <<EOT
    The type of service the backend provides. Examples: 'api', 'proxy'. Default value is 'api'.
  EOT

  type = string

  default = "api"
}

variable "url_map_path" {
  description = "The path to register for this backend service on the URL map."
  type        = string
}

variable "vpc_access_connector_id" {
  description = <<EOT
    The ID of the VPC Access connector that the Cloud Run should use to connect to the Control 
    Tower VPC.
  EOT

  type = string
}
