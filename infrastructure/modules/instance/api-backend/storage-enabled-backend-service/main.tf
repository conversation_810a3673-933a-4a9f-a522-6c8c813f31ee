resource "google_storage_bucket" "main" {
  project  = var.project_id
  name     = local.globally_unique_root_name
  location = var.bucket_config.location

  uniform_bucket_level_access = true
  storage_class               = var.bucket_config.storage_class

  force_destroy            = true
  public_access_prevention = "enforced"

  logging {
    log_bucket        = var.bucket_config.access_logs_bucket_name
    log_object_prefix = local.root_name
  }
}

# resource "google_storage_bucket_object" "main" {
#   for_each = toset(var.bucket_objects)

#   bucket         = google_storage_bucket.main.name
#   name           = each.value.name
#   source         = each.value.object_source_path
#   detect_md5hash = each.value.detect_md5hash
# }

module "api_service" {
  source = "../backend-service"

  project_id = var.project_id
  region     = var.region

  artifact_registry_repository = var.artifact_registry_repository
  backend_service_config       = var.backend_service_config
  basic_health_check_path      = var.basic_health_check_path
  cloud_run_config             = var.cloud_run_config
  common_resource_labels       = var.common_resource_labels
  container_image              = var.container_image
  enforce_unique_naming        = var.enforce_unique_naming
  environment_variables        = local.cloud_run_environment_variables
  full_health_check_path       = var.full_health_check_path
  instance_prefix              = var.instance_prefix
  load_balancing_scheme        = var.load_balancing_scheme
  required_roles               = var.required_roles
  security_policy_id           = var.security_policy_id
  service_name                 = var.service_name
  service_friendly_name        = var.service_friendly_name
  service_type                 = var.service_type
  url_map_path                 = var.url_map_path
  vpc_access_connector_id      = var.vpc_access_connector_id

  depends_on = [google_storage_bucket.main]
}
