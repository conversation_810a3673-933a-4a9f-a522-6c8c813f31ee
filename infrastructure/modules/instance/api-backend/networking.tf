resource "google_compute_subnetwork" "main" {
  project = var.project_id
  name    = local.root_name
  network = var.network_config.network_self_link
  region  = var.region

  ip_cidr_range            = var.network_config.subnet_ip_range
  private_ip_google_access = true

  dynamic "log_config" {
    for_each = var.network_config.enable_flow_logs == true ? toset([1]) : toset([])

    content {
      aggregation_interval = var.network_config.flow_log_config.aggregation_interval
      flow_sampling        = var.network_config.flow_log_config.flow_sampling
      metadata             = var.network_config.flow_log_config.metadata
    }
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "google_vpc_access_connector" "main" {
  project = var.project_id
  name    = local.root_name
  region  = var.region

  subnet {
    name = google_compute_subnetwork.main.name
  }

  machine_type  = var.vpc_access_connector_config.machine_type
  min_instances = var.vpc_access_connector_config.min_instances
  max_instances = var.vpc_access_connector_config.max_instances

  lifecycle {
    create_before_destroy = true
  }
}
