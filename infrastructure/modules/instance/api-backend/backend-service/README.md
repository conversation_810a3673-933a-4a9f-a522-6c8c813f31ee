# Basic API Service

## Overview

This module defines the resources for creating a basic version of a Control Tower API endpoint for registration with the API application load balancer. It creates a Cloud Run using the specified container image, a Service Account for the Cloud Run to operate as with the specified role assignments, and an IAM policy that allows unauthenticated access to the Cloud Run endpoint (authorization is performed interally by Auth0).
