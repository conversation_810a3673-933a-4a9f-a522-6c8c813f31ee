resource "google_monitoring_notification_channel" "main" {
  project      = var.project_id
  type         = "email"
  display_name = "API Service Monitoring Notification Channel"

  labels = {
    email_address = var.monitoring_notification_email
  }
}

module "cloud-armor" {
  source  = "GoogleCloudPlatform/cloud-armor/google"
  version = "5.0.0"

  project_id  = var.project_id
  name        = local.root_name
  type        = "CLOUD_ARMOR"
  description = "Cloud Armor for the ${var.instance_display_name} API service"

  default_rule_action = var.cloud_armor_config.layer_7_ddos_config.default_rule_action

  json_parsing = var.cloud_armor_config.layer_7_ddos_config.json_parsing
  log_level    = var.cloud_armor_config.layer_7_ddos_config.log_level

  layer_7_ddos_defense_enable          = var.cloud_armor_config.layer_7_ddos_config.enable
  layer_7_ddos_defense_rule_visibility = var.cloud_armor_config.layer_7_ddos_config.rule_visibility

  pre_configured_rules      = {}
  security_rules            = {}
  custom_rules              = {}
  threat_intelligence_rules = {}
}

module "backend_services" {
  source = "./backend-service"

  for_each = var.backend_services

  project_id = var.project_id
  region     = coalesce(each.value.region, var.default_config.region)

  enforce_unique_naming   = var.enforce_unique_naming
  instance_prefix         = var.instance_prefix
  load_balancing_scheme   = var.load_balancing_scheme
  service_name            = each.key
  service_friendly_name   = each.value.service_friendly_name
  service_type            = each.value.service_type
  url_map_path            = each.value.url_map_path
  vpc_access_connector_id = local.vpc_access_connector_id

  artifact_registry_repository = coalesce(
    each.value.artifact_registry_repository,
    var.default_config.artifact_registry_repository
  )

  backend_service_config = coalesce(
    each.value.backend_service_config,
    var.default_config.backend_service_config
  )

  basic_health_check_path = coalesce(
    each.value.basic_health_check_path,
    var.default_config.basic_health_check_path
  )

  cloud_run_config = coalesce(
    each.value.cloud_run_config,
    var.default_config.cloud_run_config
  )

  common_resource_labels = merge(
    var.common_resource_labels,
    coalesce(each.value.labels, {})
  )

  container_image = {
    image_name = each.value.container_image.image_name
    image_tag = coalesce(
      each.value.container_image.image_tag,
      var.default_config.image_tag
    )
  }

  environment_variables = merge(
    lookup(var.common_config, "environment_variables", {}),
    coalesce(each.value.environment_variables, {})
  )

  full_health_check_path = coalesce(
    each.value.full_health_check_path,
    var.default_config.full_health_check_path
  )

  required_roles = distinct(concat(
    lookup(var.common_config, "security_roles", []),
    coalesce(each.value.required_roles, [])
  ))

  security_policy_id = coalesce(
    each.value.security_policy_id,
    module.cloud-armor.policy.id
  )
}

module "storage_enabled_backend_services" {
  source = "./storage-enabled-backend-service"

  for_each = var.storage_enabled_backend_services

  project_id = var.project_id
  region     = coalesce(each.value.region, var.default_config.region)

  bucket_objects          = each.value.bucket_objects
  enforce_unique_naming   = var.enforce_unique_naming
  instance_prefix         = var.instance_prefix
  load_balancing_scheme   = var.load_balancing_scheme
  naming_root             = var.naming_root
  service_name            = each.key
  service_friendly_name   = each.value.service_friendly_name
  service_type            = each.value.service_type
  url_map_path            = each.value.url_map_path
  vpc_access_connector_id = local.vpc_access_connector_id

  artifact_registry_repository = coalesce(
    each.value.artifact_registry_repository,
    var.default_config.artifact_registry_repository
  )

  backend_service_config = coalesce(
    each.value.backend_service_config,
    var.default_config.backend_service_config
  )

  basic_health_check_path = coalesce(
    each.value.basic_health_check_path,
    var.default_config.basic_health_check_path
  )

  bucket_config = coalesce(
    each.value.bucket_config,
    var.default_config.bucket_config
  )

  cloud_run_config = coalesce(
    each.value.cloud_run_config,
    var.default_config.cloud_run_config
  )

  common_resource_labels = merge(
    var.common_resource_labels,
    coalesce(each.value.labels, {})
  )

  container_image = {
    image_name = each.value.container_image.image_name
    image_tag = coalesce(
      each.value.container_image.image_tag,
      var.default_config.image_tag
    )
  }

  environment_variables = merge(
    lookup(var.common_config, "environment_variables", {}),
    coalesce(each.value.environment_variables, {})
  )

  full_health_check_path = coalesce(
    each.value.full_health_check_path,
    var.default_config.full_health_check_path
  )
  required_roles = distinct(concat(
    lookup(var.common_config, "security_roles", []),
    coalesce(each.value.required_roles, [])
  ))

  security_policy_id = coalesce(
    each.value.security_policy_id,
    module.cloud-armor.policy.id
  )
}
