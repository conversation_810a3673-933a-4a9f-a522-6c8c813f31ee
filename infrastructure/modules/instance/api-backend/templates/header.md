# API Backends

## Table of Contents

- [Description](#description)
- [Details](#details)
- [Sub-Modules](#sub-modules)
- [Usage](#usage)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This module is used to create the individual API backends that are registered with the API application load balancer. It defines a collection of sub-modules describing the available types, or versions, of the service, and exposes configuration blocks that allow for the configuration of 0 to many of each supported service type.

## Details

Individual APIs are configured in `map(object)` variables that correspond to the different available service types. For example, each API that requires a storage bucket would be added to the `storage_enabled_apis` variable as on object in the map, with the key corresponding to the name - like "inventory" or "equipment" - and the value being the configuration for that specific API.

The module also exposes variables for common and default configuration values. Common config values are applied to all resources, and are merged with any values specified on individual APIs. Default configuration values are used as fallbacks for any of the optional values defined for the API type. For example, a default value for CPU and memory can be set and only APIs that require more resources (or less/fewer) would have to assign a value to those properties in the object map.

## Sub-Modules

This module defines two sub-modules for creating the individual API endpoints. The `backend-service` module is the standard API definition; it creates the Cloud Run, the Service Account, and the other required resources.

The `storage-enabled-backend-service` is an extension of the `backend-service` that adds a storage bucket and then register's its name as an environment variable on the Cloud Run.
