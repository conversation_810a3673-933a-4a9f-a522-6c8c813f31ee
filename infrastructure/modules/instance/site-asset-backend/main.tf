resource "google_storage_bucket" "main" {
  project  = var.project_id
  name     = "${local.globally_unique_root_name}s"
  location = var.bucket_location

  uniform_bucket_level_access = true
  storage_class               = "STANDARD"
  force_destroy               = true

  website {
    main_page_suffix = var.website_config.main_page_suffix
    not_found_page   = var.website_config.not_found_page
  }

  dynamic "cors" {
    for_each = var.cors_configs

    content {
      origin          = cors.value["origin"]
      method          = cors.value["method"]
      response_header = cors.value["response_header"]
      max_age_seconds = cors.value["max_age_seconds"]
    }
  }

  logging {
    log_bucket        = var.access_logs_bucket
    log_object_prefix = local.root_name
  }

  labels = var.common_resource_labels
}

resource "null_resource" "main" {
  triggers = {
    file_hashes = jsonencode({
      for fn in fileset(var.app_artifact_folder, "**") :
      fn => filesha256("${var.app_artifact_folder}/${fn}")
    })
  }

  provisioner "local-exec" {
    command = <<EOT
      gcloud storage rsync --delete-unmatched-destination-objects \
        --recursive \
        --cache-control="public, max-age=60" \
        ${var.app_artifact_folder} \
        gs://${google_storage_bucket.main.name}/
    EOT
  }

  depends_on = [google_compute_backend_bucket.main]
}

resource "google_storage_bucket_iam_member" "main" {
  bucket = google_storage_bucket.main.name
  role   = "roles/storage.objectViewer"
  member = "allUsers"
}

resource "google_compute_backend_bucket" "main" {
  project     = var.project_id
  name        = "${local.root_name}s"
  bucket_name = google_storage_bucket.main.name

  custom_response_headers = [
    "X-Content-Type-Options: nosniff",
    "X-Frame-Options: SAMEORIGIN",
    "Content-Security-Policy: default-src 'self' https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; style-src 'self' 'unsafe-inline' https:; img-src 'self' data: https:; font-src 'self' data: https:; connect-src 'self' https: wss:; frame-ancestors 'self'; base-uri 'self'; form-action 'self'",
    "Strict-Transport-Security: max-age=31536000"
  ]

  enable_cdn = false
}
