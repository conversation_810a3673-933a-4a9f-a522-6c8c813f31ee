<!-- BEGIN_TF_DOCS-->

<!-- NOTE: 
This file is automatically generated. 
Do not modify it manually. 
If you'd like to update the docs, update the header.md file in the templates directory.
-->

# Site Asset Backend Terraform Module

## Table of Contents

- [Description](#description)
- [Considerations](#considerations)
- [Usage](#usage)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This module creates the resources for the backend that serves static site assets to the front-end load balancer. It creates a dedicated storage bucket, uploads the assets contained in the specified folder, and then creates the backend resource that will be registered with the load balancer.

## Considerations

UI Review Apps will be deployed to a common project, so even resources that don't need globally-unique names should use the same naming pattern as the bucket to avoid naming collisions.

## Usage

Basic usage of this module is as follows:

```hcl
module "site-asset-backend" {
  source  = "gitlab.com/dematic/control-tower-site-asset-backend/google"
  version = "~> 0.1.0"

  # Required variables
  access_logs_bucket     = var.access_logs_bucket
  app_artifact_folder    = var.app_artifact_folder
  bucket_location        = var.bucket_location
  common_resource_labels = var.common_resource_labels
  cors_configs           = var.cors_configs
  enforce_unique_naming  = var.enforce_unique_naming
  instance_prefix        = var.instance_prefix
  naming_root            = var.naming_root
  project_id             = var.project_id
  website_config         = var.website_config
}
```

## Requirements

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |

## Providers

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |
| null | n/a |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [google_compute_backend_bucket.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_backend_bucket) | resource |
| [google_storage_bucket.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/storage_bucket) | resource |
| [google_storage_bucket_iam_member.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/storage_bucket_iam_member) | resource |
| [null_resource.main](https://registry.terraform.io/providers/hashicorp/null/latest/docs/resources/resource) | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| access\_logs\_bucket | The name of the access logs bucket. | `string` | n/a | yes |
| app\_artifact\_folder | The path to the folder containing the application artifacts to deploy. | `string` | n/a | yes |
| bucket\_location | The location to deploy the bucket in. Can be a single region or a multi-region.      Examples: "us", "us-east1". | `string` | n/a | yes |
| common\_resource\_labels | The common labels that should be applied to all resources that can be labeled. | `map(string)` | n/a | yes |
| cors\_configs | The CORS configuration for the bucket. This is a list of maps, where each map contains      the following keys:       - origin: The allowed origins.       - method: The allowed methods.       - response\_header: The allowed response headers.       - max\_age\_seconds: The maximum age in seconds. | ```list(object({ origin = list(string) method = list(string) response_header = list(string) max_age_seconds = number }))``` | n/a | yes |
| enforce\_unique\_naming | Set this to true to use the provided 'environment` value to create a unique prefix for all      resources in the module. This is required to allow multiple instances of the module to be      deployed in the same GCP project. Used by Review Apps to generate the preview environments. ` | `bool` | n/a | yes |
| instance\_prefix | The name of the instance that the resources are being deployed to. This value will be      combined with the `naming_root` to create a unique prefix for any resources requiring a      globally-unique identifier. Therefore, it must be unique among Control Tower instances but      should be as short as possible to comply with GCP resource name length restrictions. | `string` | n/a | yes |
| naming\_root | The root identifier for all Control Tower resources. This value will be combined with the     `environment` to create a unique prefix for any resources requiring a globally-unique name.      This value is intended to be shared across all Control Tower deployments. As of module      creation, this value is "ict". | `string` | n/a | yes |
| project\_id | The ID of the GCP project to deploy to. | `string` | n/a | yes |
| website\_config | The website configuration for the bucket. This is a map containing the following keys:       - main\_page\_suffix: The suffix for the main page.       - not\_found\_page: The page to display when a requested page is not found. | ```object({ main_page_suffix = string not_found_page = string })``` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| backend\_service\_id | The ID of the load balancer backend created for the site asset bucket. This is used to      register the bucket backend with the front-end load balancer. |
| bucket\_name | The name of the bucket used to store site assets. |
| bucket\_url | n/a |

<!-- END_TF_DOCS-->
<!-- BEGIN_TFVARS_DOCS-->

## Terraform TFVARS file examples

```hcl
inputs {
    
  # The name of the access logs bucket.
  access_logs_bucket = ""
  
  # The path to the folder containing the application artifacts to deploy.
  app_artifact_folder = ""
  
  #     The location to deploy the bucket in. Can be a single region or a multi-region.
  #     Examples: "us", "us-east1".
  #
  bucket_location = ""
  
  #     The common labels that should be applied to all resources that can be labeled.
  #
  common_resource_labels = ""
  
  #     The CORS configuration for the bucket. This is a list of maps, where each map contains
  #     the following keys:
  #       - origin: The allowed origins.
  #       - method: The allowed methods.
  #       - response_header: The allowed response headers.
  #       - max_age_seconds: The maximum age in seconds.
  #
  cors_configs = ""
  
  #     Set this to true to use the provided 'environment` value to create a unique prefix for all
  #     resources in the module. This is required to allow multiple instances of the module to be
  #     deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  #
  enforce_unique_naming = ""
  
  #     The name of the instance that the resources are being deployed to. This value will be
  #     combined with the `naming_root` to create a unique prefix for any resources requiring a
  #     globally-unique identifier. Therefore, it must be unique among Control Tower instances but
  #     should be as short as possible to comply with GCP resource name length restrictions.
  #
  instance_prefix = ""
  
  #     The root identifier for all Control Tower resources. This value will be combined with the
  #     `environment` to create a unique prefix for any resources requiring a globally-unique name.
  #     This value is intended to be shared across all Control Tower deployments. As of module
  #     creation, this value is "ict".
  #
  naming_root = ""
  
  # The ID of the GCP project to deploy to.
  project_id = ""
  
  #     The website configuration for the bucket. This is a map containing the following keys:
  #       - main_page_suffix: The suffix for the main page.
  #       - not_found_page: The page to display when a requested page is not found.
  #
  website_config = ""
}
```

<!-- END_TFVARS_DOCS-->
