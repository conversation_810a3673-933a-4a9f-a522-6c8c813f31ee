variable "access_logs_bucket" {
  description = "The name of the access logs bucket."
  type        = string
}

variable "app_artifact_folder" {
  description = "The path to the folder containing the application artifacts to deploy."
  type        = string
}

variable "bucket_location" {
  description = <<EOT
    The location to deploy the bucket in. Can be a single region or a multi-region. 
    Examples: "us", "us-east1".
  EOT

  type = string
}

variable "common_resource_labels" {
  description = <<EOT
    The common labels that should be applied to all resources that can be labeled.
  EOT

  type = map(string)
}

variable "cors_configs" {
  description = <<EOT
    The CORS configuration for the bucket. This is a list of maps, where each map contains 
    the following keys:
      - origin: The allowed origins.
      - method: The allowed methods.
      - response_header: The allowed response headers.
      - max_age_seconds: The maximum age in seconds.
  EOT

  type = list(object({
    origin          = list(string)
    method          = list(string)
    response_header = list(string)
    max_age_seconds = number
  }))
}

variable "enforce_unique_naming" {
  description = <<EOT
    Set this to true to use the provided 'environment` value to create a unique prefix for all 
    resources in the module. This is required to allow multiple instances of the module to be 
    deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  EOT

  type = bool
}

variable "instance_prefix" {
  description = <<EOT
    The name of the instance that the resources are being deployed to. This value will be 
    combined with the `naming_root` to create a unique prefix for any resources requiring a 
    globally-unique identifier. Therefore, it must be unique among Control Tower instances but 
    should be as short as possible to comply with GCP resource name length restrictions.
  EOT

  type = string
}

variable "naming_root" {
  description = <<EOT
    The root identifier for all Control Tower resources. This value will be combined with the
    `environment` to create a unique prefix for any resources requiring a globally-unique name. 
    This value is intended to be shared across all Control Tower deployments. As of module 
    creation, this value is "ict".
  EOT

  type = string
}

variable "project_id" {
  description = "The ID of the GCP project to deploy to."
  type        = string
}

variable "website_config" {
  description = <<EOT
    The website configuration for the bucket. This is a map containing the following keys:
      - main_page_suffix: The suffix for the main page.
      - not_found_page: The page to display when a requested page is not found.
  EOT

  type = object({
    main_page_suffix = string
    not_found_page   = string
  })
}
