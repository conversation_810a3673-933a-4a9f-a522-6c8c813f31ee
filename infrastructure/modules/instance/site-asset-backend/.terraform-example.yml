# https://terraform-docs.io/
#
# This is a configuration file for terraform-docs. 
#
# You can copy this file to any directory you want to have generated terraform docs for.
# This is a good way to keep the docs up to date for a specific module.
# Just make sure you change the path in the ```hcl``` block to the correct path for your module.
#
# You can run the following command to generate the docs (from the scripts directory):
#
# ./generate_terraform_docs.sh

formatter: "markdown table"
version: "0.19.0"

content: |-
    module "site-asset-backend" {
        source  = "gitlab.com/dematic/control-tower-site-asset-backend/google"
        version = "~> 0.1.0"

    {{- if .Module.RequiredInputs }}

        # Required variables
        {{- range .Module.RequiredInputs }}
        {{ .Name }} = {{ .Name | printf "var.%s" }}
        {{- end }}

    {{- end }}

    {{- if .Module.OptionalInputs }}

        # Optional variables

        {{- range .Module.OptionalInputs }}
        {{ .Name }} = {{ .GetValue | printf "%s" }}
        {{- end }}
    {{- end }}
    }


output:
  file: ../../../examples/instance/site-asset-backend/main.tf
  mode: replace
  template: |-
    {{ .Content }}

output-values:
  enabled: false
  from: ""

sort:
  enabled: true
  by: name

settings:
  anchor: false
  color: true
  default: true
  description: true
  escape: true
  hide-empty: false
  html: false
  indent: 2
  lockfile: false
  read-comments: true
  required: true
  sensitive: true
  type: true