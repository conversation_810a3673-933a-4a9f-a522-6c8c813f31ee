# Site Asset Backend Terraform Module

## Table of Contents

- [Description](#description)
- [Considerations](#considerations)
- [Usage](#usage)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This module creates the resources for the backend that serves static site assets to the front-end load balancer. It creates a dedicated storage bucket, uploads the assets contained in the specified folder, and then creates the backend resource that will be registered with the load balancer.

## Considerations

UI Review Apps will be deployed to a common project, so even resources that don't need globally-unique names should use the same naming pattern as the bucket to avoid naming collisions.
