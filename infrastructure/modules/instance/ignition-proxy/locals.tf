locals {
  # Resource naming
  resource_namespace        = "ignition-proxy"
  unique_root_name          = "${var.instance_prefix}-${local.resource_namespace}"
  globally_unique_root_name = "${var.naming_root}-${local.unique_root_name}"

  root_name = (var.enforce_unique_naming
    ? local.unique_root_name
    : local.resource_namespace
  )

  # IAM
  proxy_service_account_roles = [
    "roles/storage.objectViewer",
    "roles/compute.networkUser",
    "roles/vpcaccess.user",
  ]

  host_service_account_scopes = [
    "https://www.googleapis.com/auth/devstorage.read_only",
    "https://www.googleapis.com/auth/logging.write",
    "https://www.googleapis.com/auth/monitoring.write",
    "https://www.googleapis.com/auth/servicecontrol",
    "https://www.googleapis.com/auth/service.management.readonly",
    "https://www.googleapis.com/auth/trace.append"
  ]

  # Zones for the host instances
  zone_count = length(data.google_compute_zones.available_zones.names)
  host_zones = [
    for i in range(var.host_config.count) :
    data.google_compute_zones.available_zones.names[i % local.zone_count]
  ]

  # Config files
  ignition_upstreams_file_name = "ignition-upstreams.tfpl"
  nginx_config_file_name       = "default-nginx.conf"

  ignition_upstreams_bucket_object = (
    "gs://${google_storage_bucket.main.name}/${google_storage_bucket_object.ignition_upstreams.name}"
  )
  nginx_config_bucket_object = (
    "gs://${google_storage_bucket.main.name}/${google_storage_bucket_object.nginx_config.name}"
  )
}
