server {
        listen 80 default_server;
        listen [::]:80 default_server;

    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    server_name _;

    underscores_in_headers on;


    # The next few location contexts define how to process resources required by the Perspective session.
    # If the longest matching prefix location has the “^~” modifier then regular expressions are not checked.
    location ^~ /data/ {
        proxy_pass https://ignition/data/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_pass_request_headers on;
        proxy_cache_bypass $http_upgrade;
    }

    location ^~ /system/ {
        proxy_pass https://ignition/system/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_pass_request_headers on;
        proxy_cache_bypass $http_upgrade;
    }

    location ^~ /res/ {
        proxy_pass https://ignition/res/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_pass_request_headers on;
        proxy_cache_bypass $http_upgrade;
    }

    location ^~ /idp/ {
        proxy_pass https://ignition/idp/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_pass_request_headers on;
        proxy_cache_bypass $http_upgrade;
    }

    location ^~ /.well-known/ {
        proxy_pass https://ignition/.well-known/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_pass_request_headers on;
        proxy_cache_bypass $http_upgrade;
    }

    # Here we are setting the protocol type, address, port, and uri (optional) that will be the destination of our proxied server:
    location / {
        proxy_pass https://ignition/data/perspective/client/demo/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_pass_request_headers on;
        proxy_cache_bypass $http_upgrade;
    }
}