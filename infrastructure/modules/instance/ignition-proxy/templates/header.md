# Ignition Proxy Backend Terraform Module

## Table of Contents

- [Description](#description)
- [Details](#details)
- [Enhancements](#enhancements)
- [Usage](#usage)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This module creates a backend for the front-end load balancer that serves as a proxy to an Ignition host. It creates a Managed Instance Group containing 1 -> n instances running the proxy service, and registers it with a load balancer backend can be connected to a Control Tower front-end load balancer.

## Details

The proxy itself is an NGINX service running on one or more VM instances. The configuration files in the `config` directory are pushed to a GCS bucket. The VMs are described in regional instance templates and managed by a regional Managed Instance Group, and include metadata startup scripts that read the files and configure NGINX on launch. Using the MIG ensures that additional proxy VMs will be distributed among the availability zones in a target region, ensuring that scaling increases both throughput and availability.

The module also creates a service account with the required role grants for the instances to use, and a subnet for the instances.

## Enhancements

- Move the config files into `environments` so that Ignition can be configured differently in each environment
