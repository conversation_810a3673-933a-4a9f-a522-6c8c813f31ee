resource "google_storage_bucket" "main" {
  project  = var.project_id
  name     = "${local.globally_unique_root_name}-artifacts"
  location = var.bucket_location

  uniform_bucket_level_access = true
  storage_class               = "STANDARD"
  force_destroy               = true
}

resource "google_storage_bucket_object" "nginx_config" {
  name   = local.nginx_config_file_name
  bucket = google_storage_bucket.main.name
  content = templatefile(
    "${path.module}/config/${local.nginx_config_file_name}",
    {}
  )
}

resource "google_storage_bucket_object" "ignition_upstreams" {
  name   = local.ignition_upstreams_file_name
  bucket = google_storage_bucket.main.name
  content = templatefile(
    "${path.module}/config/${local.ignition_upstreams_file_name}",
    {
      ignition_upstream = var.ignition_upstream,
      ignition_port     = var.ignition_port
    }
  )
}
