variable "bucket_location" {
  description = "The location to deploy the Ignition deployment asset bucket in."
  type        = string
}

variable "common_resource_labels" {
  description = <<EOT
    The common labels that should be applied to all resources that can be labeled.
  EOT

  type = map(string)
}

variable "enforce_unique_naming" {
  description = <<EOT
    Set this to true to use the provided 'environment` value to create a unique prefix for all 
    resources in the module. This is required to allow multiple instances of the module to be 
    deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  EOT

  type = bool
}

variable "host_config" {
  description = <<EOT
    The configuration for the host instance(s) created by the Managed Instance Group. This is used 
    to create the instance template for the Managed Instance Group.

    Parameters:
    - count: the number of instances that should be running
    - machine_type: the type of machine to use for the instance(s). e.g. "e2-medium"
    - disk_size: the size of the boot disk in GB
    - disk_type: the type of disk to use for the boot disk. e.g. `pd-balanced`, `pd-ssd`, etc.
    - image_family: the family of the image to use for the instance(s). e.g. "ubuntu-2204-lts"
    - image_project: the project that the image family belongs to. e.g. "ubuntu-os-cloud"
  EOT

  type = object({
    count        = number
    machine_type = string

    disk_size = number
    disk_type = string

    image_family  = string
    image_project = string
  })
}

variable "host_labels" {
  description = <<EOT
    The labels that should be applied to the VM host(s), excluding anything already defined in 
    `common_resource_labels`.
  EOT

  type = map(string)
}

variable "host_tags" {
  description = <<EOT
    The tags that should be applied to the VM host(s). This includes things like any network tags 
    used to control firewall rules, as well as any other tags that are specific to the VM host(s).
  EOT

  type = list(string)
}

variable "ignition_port" {
  description = "The port to use on the Ignition service load balancer."
}

variable "ignition_upstream" {
  description = "The upstream URL for the Ignition service."
  type        = string
}

variable "instance_prefix" {
  description = <<EOT
    The name of the instance that the resources are being deployed to. This value will be 
    combined with the `naming_root` to create a unique prefix for any resources requiring a 
    globally-unique identifier. Therefore, it must be unique among Control Tower instances but 
    should be as short as possible to comply with GCP resource name length restrictions.
  EOT

  type = string
}

variable "naming_root" {
  description = <<EOT
    The root identifier for all Control Tower resources. This value will be combined with the
    `environment` to create a unique prefix for any resources requiring a globally-unique name. 
    This value is intended to be shared across all Control Tower deployments. As of module 
    creation, this value is "ict".
  EOT

  type = string
}

variable "network_config" {
  description = <<EOT
    Configuration for the network resources to be created.

    Note: this module does not validate the flow log configuration beyond ensuring it is not null 
    if flow logs are enabled. It is intended that the flow log configuration is passed in from the 
    output of the landing zone module.

    See the README and variables.tf of the network submodule of the landing zone module for more 
    details on the configuration
  EOT

  type = object({
    enable_flow_logs  = bool
    network_self_link = string
    subnet_ip_range   = string

    flow_log_config = optional(object({
      aggregation_interval = string
      flow_sampling        = number
      metadata             = string
    }))
  })

  validation {
    error_message = "Flow log configuration cannot be null if flow logs are enabled."
    condition = (
      var.network_config.enable_flow_logs == false ||
      (
        var.network_config.flow_log_config != {} &&
        var.network_config.flow_log_config != null
      )
    )
  }
}

variable "project_id" {
  description = "The ID of the GCP project to deploy to."
  type        = string
}

variable "region" {
  description = "The region to deploy regional resources in."
  type        = string
}
