locals {
  alert_policy_name = (var.enforce_unique_naming
    ? "${var.service_friendly_name} Alert Policy - ${var.instance_display_name}"
    : "${var.service_friendly_name} Alert Policy"
  )

  basic_uptime_check_name = (var.enforce_unique_naming
    ? "${var.service_friendly_name} Basic Uptime Check - ${var.instance_display_name}"
    : "${var.service_friendly_name} Basic Uptime Check"
  )

  full_uptime_check_name = (var.enforce_unique_naming
    ? "${var.service_friendly_name} Full Uptime Check - ${var.instance_display_name}"
    : "${var.service_friendly_name} Full Uptime Check"
  )
}
