resource "google_monitoring_uptime_check_config" "basic" {
  project = var.project_id

  display_name = local.basic_uptime_check_name
  period       = var.basic_uptime_check_period
  timeout      = var.basic_uptime_check_timeout

  http_check {
    path         = "${var.service_url_map_path}/${var.basic_health_check_path}"
    port         = var.health_check_port
    use_ssl      = var.use_ssl
    validate_ssl = var.validate_ssl
  }

  monitored_resource {
    type = "uptime_url"
    labels = {
      project_id = var.project_id
      host       = var.dns_name
    }
  }
}

resource "google_monitoring_uptime_check_config" "full" {
  project = var.project_id

  display_name = local.full_uptime_check_name
  period       = var.full_uptime_check_period
  timeout      = var.full_uptime_check_timeout

  http_check {
    path         = "${var.service_url_map_path}/${var.full_health_check_path}"
    port         = var.health_check_port
    use_ssl      = var.use_ssl
    validate_ssl = var.validate_ssl
  }

  monitored_resource {
    type = "uptime_url"
    labels = {
      project_id = var.project_id
      host       = trim(var.dns_name, ".")
    }
  }
}

resource "google_monitoring_alert_policy" "main" {
  count = var.enable_alerting ? 1 : 0

  project = var.project_id

  display_name = local.alert_policy_name

  enabled  = var.enable_alerting
  combiner = "OR"

  conditions {
    display_name = "${var.service_friendly_name} Full Uptime Check Alert Condition"

    condition_threshold {
      comparison = var.alerting_condition_threshold_comparison
      duration   = var.alerting_condition_threshold_duration

      filter = <<EOF
metric.type="monitoring.googleapis.com/uptime_check/check_passed" 
  AND metric.label."check_id"="${google_monitoring_uptime_check_config.full.uptime_check_id}" 
  AND resource.type="uptime_url"
      EOF

      aggregations {
        alignment_period     = var.alerting_condition_threshold_aggregation_alignment_period
        cross_series_reducer = "REDUCE_COUNT_FALSE"
        per_series_aligner   = "ALIGN_NEXT_OLDER"

        group_by_fields = [
          "resource.label.project_id",
          "resource.label.host"
        ]
      }

      trigger {
        count = var.alerting_condition_threshold_trigger_count
      }
    }
  }
}
