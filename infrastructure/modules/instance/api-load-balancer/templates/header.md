# API Load Balancer Terraform Module

WIP

## Table of Contents

- [Description](#description)
- [Usage](#usage)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This module creates the the API load balancer. More documentation to come, but it is likely that this module will be combined with the UI load balancer to create a single load balancer per environment.
