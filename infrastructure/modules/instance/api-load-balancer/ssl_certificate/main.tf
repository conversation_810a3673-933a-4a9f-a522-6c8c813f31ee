resource "random_string" "main" {
  length  = 4
  special = false
  upper   = false

  keepers = {
    resource_base_name    = "api-lb-cert"
    a_record_dns_name     = var.a_record_dns_name
    cname_record_dns_name = var.cname_record_dns_name
  }
}

resource "google_compute_managed_ssl_certificate" "main" {
  project = var.project_id
  name    = "${random_string.main.keepers.resource_base_name}-${random_string.main.result}"

  managed {
    domains = [
      random_string.main.keepers.a_record_dns_name,
      random_string.main.keepers.cname_record_dns_name
    ]
  }

  lifecycle {
    create_before_destroy = true
  }
}
