resource "google_compute_global_address" "main" {
  project     = var.project_id
  name        = "${local.root_name}-public"
  description = "The public IP address for the ${var.instance_display_name} API load balancer"
  labels      = var.common_resource_labels
}

resource "google_compute_url_map" "main" {
  project     = var.project_id
  name        = "${local.root_name}-load-balancer"
  description = "URL map for the ${local.root_name} load balancer"

  default_url_redirect {
    https_redirect         = true
    strip_query            = false
    redirect_response_code = "MOVED_PERMANENTLY_DEFAULT"
  }

  host_rule {
    hosts        = ["*"]
    path_matcher = "main"
  }

  path_matcher {
    name            = "main"
    default_service = var.default_backend_service_id

    dynamic "path_rule" {
      for_each = var.backend_services

      content {
        service = path_rule.value.backend_service_id
        paths   = [path_rule.value.url_map_path, "${path_rule.value.url_map_path}/*"]
      }
    }
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "google_compute_ssl_policy" "main" {
  project = var.project_id
  name    = "${local.root_name}-ssl-policy"
  description = (
    "The SSL policy for the ${local.root_name} API load balancer"
  )
  profile = "CUSTOM"

  min_tls_version = var.minimum_tls_version

  custom_features = [
    "TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384",
    "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
  ]
}

module "ssl_certificate" {
  source     = "./ssl_certificate"
  project_id = var.project_id

  a_record_dns_name     = local.a_record_dns_name
  cname_record_dns_name = local.cname_record_dns_name
}

resource "google_compute_target_https_proxy" "main" {
  project     = var.project_id
  name        = local.root_name
  description = "HTTPS proxy for the ${local.root_name} load balancer"

  ssl_policy = google_compute_ssl_policy.main.id
  url_map    = google_compute_url_map.main.id

  ssl_certificates = [
    module.ssl_certificate.ssl_certificate_id
  ]
}

resource "google_compute_global_forwarding_rule" "main" {
  project     = var.project_id
  name        = local.root_name
  description = "Global forwarding rule for the ${local.root_name} load balancer"

  ip_address            = google_compute_global_address.main.address
  ip_protocol           = "TCP"
  load_balancing_scheme = var.load_balancing_scheme
  port_range            = "443"
  target                = google_compute_target_https_proxy.main.id
}
