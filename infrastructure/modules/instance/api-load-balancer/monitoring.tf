resource "google_monitoring_notification_channel" "main" {
  display_name = local.notification_channel_name
  type         = "email"
  labels = {
    email_address = var.notification_email
  }
  project = var.project_id
}

module "backend-service-monitors" {
  source = "./backend-monitor"

  project_id = var.project_id
  for_each   = var.backend_services

  dns_name              = local.cname_record_dns_name
  enable_alerting       = var.backend_monitoring_config.alerting.enabled
  enforce_unique_naming = var.enforce_unique_naming
  instance_display_name = var.instance_display_name
  instance_prefix       = var.instance_prefix
  health_check_port     = var.backend_monitoring_config.monitoring.http_check_config.port
  service_friendly_name = each.value.backend_service_friendly_name
  service_url_map_path  = each.value.url_map_path
  use_ssl               = var.backend_monitoring_config.monitoring.http_check_config.use_ssl
  validate_ssl          = var.backend_monitoring_config.monitoring.http_check_config.validate_ssl

  alerting_condition_threshold_aggregation_alignment_period = (
    var.backend_monitoring_config.alerting.condition_threshold == null
    ? null
    : var.backend_monitoring_config.alerting.condition_threshold.aggregation_alignment_period
  )
  alerting_condition_threshold_comparison = (
    var.backend_monitoring_config.alerting.condition_threshold == null
    ? null
    : var.backend_monitoring_config.alerting.condition_threshold.comparison
  )
  alerting_condition_threshold_duration = (
    var.backend_monitoring_config.alerting.condition_threshold == null
    ? null
    : var.backend_monitoring_config.alerting.condition_threshold.duration
  )
  alerting_condition_threshold_trigger_count = (
    var.backend_monitoring_config.alerting.condition_threshold == null
    ? null
    : var.backend_monitoring_config.alerting.condition_threshold.trigger_count
  )

  basic_health_check_path = each.value.basic_health_check_path
  basic_uptime_check_period = (
    var.backend_monitoring_config.monitoring.basic_uptime_check_config.period
  )
  basic_uptime_check_timeout = (
    var.backend_monitoring_config.monitoring.basic_uptime_check_config.timeout
  )

  full_health_check_path = each.value.full_health_check_path
  full_uptime_check_period = (
    var.backend_monitoring_config.monitoring.full_uptime_check_config.period
  )
  full_uptime_check_timeout = (
    var.backend_monitoring_config.monitoring.full_uptime_check_config.timeout
  )
}
