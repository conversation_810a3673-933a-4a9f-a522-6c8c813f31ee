data "google_dns_managed_zone" "main" {
  project = var.dns_config.project_id
  name    = var.dns_config.zone_name
}

# register a DNS name for the load balancer's IP address
resource "google_dns_record_set" "a_record" {
  project = data.google_dns_managed_zone.main.project
  name    = local.a_record_dns_name

  managed_zone = data.google_dns_managed_zone.main.name
  ttl          = var.dns_config.a_record_ttl
  type         = "A"

  rrdatas = [
    google_compute_global_address.main.address
  ]
}
