# UI Load Balancer Terraform Module

## Table of Contents

- [Description](#description)
- [Resource Lifecycle Management](#resource-lifecycle-management)
- [Usage](#usage)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This module creates the resources for the UI load balancer

## Resource Lifecycle Management

The following resource configurations are required to allow successful deployment and ongoing management through `tofu apply` and `tofu destroy` commands:

### SSL Certificates

An SSL certificate cannot be destroyed if it's in use, which means that operational motions that require replacement will fail. There are two steps required to get around this:

#### 1. Add the `create_before_destroy` lifecycle rule

``` yaml
lifecycle {
    create_before_destroy = true
  }
```

This causes Terraform to create the replacement before destroying the old one. However, this will fail if the resource has a static name, because the name will already be in use. This leads to the second step.

#### 2. Add a generated suffix to the SSL certificate resource name

By generating a 4-character, random string and adding it as a suffix to the resource name (in keeping with standard GCP conventions for resources managed by automation), we can ensure that the replacement cert will always have a unique name. This allows resources dependent on the old cert to switch to the new one, which in turn enables destruction of the old cert.

To ensure that we're only, and always, regenerating the random suffix if the cert must be replaced, we set `keepers` on the `random_string` for anything that causes cert regeneration. This avoids unnecessary churn, while still ensuring we can change anything and everything about the cert if needed.

``` yaml
resource "random_string" "main" {
  length  = 4
  special = false
  upper   = false

  keepers = {
    managed_domain = google_dns_record_set.main.name
  }
}
```
