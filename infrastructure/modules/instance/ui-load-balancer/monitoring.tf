resource "google_monitoring_notification_channel" "main" {
  display_name = "UI Monitoring Notification Channel - ${var.instance_display_name}"
  type         = "email"
  labels = {
    email_address = var.alert_email_address
  }
  project = var.project_id
}

resource "google_monitoring_uptime_check_config" "main" {
  project      = var.project_id
  display_name = "UI uptime check - ${var.instance_display_name}"
  timeout      = "10s"
  period       = "60s"

  http_check {
    path         = "/"
    port         = 443
    use_ssl      = true
    validate_ssl = true
  }

  monitored_resource {
    type = "uptime_url"
    labels = {
      project_id = var.project_id
      host       = trim(google_dns_record_set.a_record.name, ".")
    }
  }
}

resource "google_monitoring_alert_policy" "ui_uptime_alert" {
  project = var.project_id

  display_name = "UI Load Balancer Uptime Alert - ${var.instance_display_name}"

  enabled  = true
  combiner = "OR"

  conditions {
    display_name = "UI Load Balancer Uptime Check Failed"

    condition_threshold {
      comparison = "COMPARISON_LT"
      duration   = "300s" # 5 minutes

      filter = <<EOF
metric.type="monitoring.googleapis.com/uptime_check/check_passed" 
  AND metric.label."check_id"="${google_monitoring_uptime_check_config.main.uptime_check_id}" 
  AND resource.type="uptime_url"
      EOF

      aggregations {
        alignment_period     = "300s"
        cross_series_reducer = "REDUCE_COUNT_FALSE"
        per_series_aligner   = "ALIGN_NEXT_OLDER"

        group_by_fields = [
          "resource.label.project_id",
          "resource.label.host"
        ]
      }

      trigger {
        count = 1
      }

      threshold_value = 1.0
    }
  }

  notification_channels = [
    google_monitoring_notification_channel.main.name
  ]

  alert_strategy {
    auto_close = "604800s" # 7 days
  }

  documentation {
    content = "The UI Load Balancer uptime check has failed. This indicates that the UI application may be unavailable or experiencing issues."
  }
}

# Load Balancer Level Monitoring - Conservative Alert for High Error Rate
resource "google_monitoring_alert_policy" "ui_load_balancer_high_error_rate" {
  project = var.project_id

  display_name = "UI Load Balancer High Error Rate - ${var.instance_display_name}"

  enabled  = true
  combiner = "OR"

  conditions {
    display_name = "High HTTP 5xx Error Rate"

    condition_threshold {
      comparison = "COMPARISON_GT"
      duration   = "600s" # 10 minutes - conservative

      filter = <<EOF
metric.type="loadbalancing.googleapis.com/https/request_count" 
  AND resource.type="https_lb_rule"
  AND metric.label."response_code_class"="500"
      EOF

      aggregations {
        alignment_period     = "300s"
        cross_series_reducer = "REDUCE_SUM"
        per_series_aligner   = "ALIGN_RATE"
      }

      trigger {
        count = 1
      }

      threshold_value = 5.0 # Conservative: 5 errors per second (lower than API since UI typically has less traffic)
    }
  }

  notification_channels = [
    google_monitoring_notification_channel.main.name
  ]

  alert_strategy {
    auto_close = "86400s" # 24 hours
  }

  documentation {
    content = "The UI Load Balancer is experiencing a high rate of HTTP 5xx errors (>5/sec for 10 minutes). This indicates potential issues with the UI application or load balancer problems."
  }
}

# Load Balancer Level Monitoring - Conservative Alert for Very High Latency
resource "google_monitoring_alert_policy" "ui_load_balancer_high_latency" {
  project = var.project_id

  display_name = "UI Load Balancer High Latency - ${var.instance_display_name}"

  enabled  = true
  combiner = "OR"

  conditions {
    display_name = "Very High Request Latency"

    condition_threshold {
      comparison = "COMPARISON_GT"
      duration   = "600s" # 10 minutes - conservative

      filter = <<EOF
metric.type="loadbalancing.googleapis.com/https/total_latencies" 
  AND resource.type="https_lb_rule"
      EOF

      aggregations {
        alignment_period     = "300s"
        cross_series_reducer = "REDUCE_PERCENTILE_95"
        per_series_aligner   = "ALIGN_DELTA"
      }

      trigger {
        count = 1
      }

      threshold_value = 10000.0 # Conservative: 10 seconds p95 latency (more strict for UI)
    }
  }

  notification_channels = [
    google_monitoring_notification_channel.main.name
  ]

  alert_strategy {
    auto_close = "86400s" # 24 hours
  }

  documentation {
    content = "The UI Load Balancer is experiencing very high latency (p95 > 10s for 10 minutes). This indicates potential performance issues with the UI application or infrastructure problems."
  }
}
