resource "google_monitoring_uptime_check_config" "main" {
  project      = var.project_id
  display_name = "UI uptime check"
  timeout      = "10s"
  period       = "60s"

  http_check {
    path         = "/"
    port         = 443
    use_ssl      = true
    validate_ssl = true
  }

  monitored_resource {
    type = "uptime_url"
    labels = {
      project_id = var.project_id
      host       = trim(google_dns_record_set.a_record.name, ".")
    }
  }
}
