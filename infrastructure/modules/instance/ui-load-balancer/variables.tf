variable "alert_email_address" {
  description = "The email address to send alerts to."
  type        = string
}

variable "common_resource_labels" {
  description = <<EOT
    The common labels that should be applied to all resources that can be labeled.
  EOT

  type = map(string)
}

variable "dns_config" {
  description = "The DNS configuration for the load balancer."
  type = object({
    a_record_ttl = number
    project_id   = string
    zone_name    = string
  })
}

variable "enforce_unique_naming" {
  description = <<EOT
    Set this to true to use the provided 'environment` value to create a unique prefix for all 
    resources in the module. This is required to allow multiple instances of the module to be 
    deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  EOT

  type = bool
}

variable "instance_display_name" {
  description = <<EOT
    The human-readable name of the instance that the resources are being deployed to.
  EOT

  type = string
}

variable "instance_prefix" {
  description = <<EOT
    The name of the instance that the resources are being deployed to. This value will be 
    combined with the `naming_root` to create a unique prefix for any resources requiring a 
    globally-unique identifier. Therefore, it must be unique among Control Tower instances but 
    should be as short as possible to comply with GCP resource name length restrictions.
  EOT

  type = string
}

variable "load_balancing_scheme" {
  description = <<EOT
    The load balancing scheme to use for the load balancer. For an external load balancer, this can 
    be either `EXTERNAL` or `EXTERNAL_MANAGED`; for an internal load balancer, this must be either 
    `INTERNAL` or `INTERNAL_MANAGED`. In both cases, the latter `MANAGED` option is recommended for
    production use, as it provides better performance and reliability.
  EOT

  type = string

  validation {
    condition = contains(
      ["EXTERNAL", "EXTERNAL_MANAGED", "INTERNAL", "INTERNAL_MANAGED"],
      var.load_balancing_scheme
    )

    error_message = <<EOT
      The load balancing scheme must be one of: EXTERNAL, EXTERNAL_MANAGED, INTERNAL, 
      INTERNAL_MANAGED."
    EOT
  }
}

variable "minimum_tls_version" {
  description = <<EOT
    The minimum TLS version to use for the load balancer. This value is used to create the 
    SSL policy for the load balancer. Note: versions lower than TLS 1.2 are not supported.
  EOT

  type = string

  validation {
    condition     = contains(["TLS_1_2", "TLS_1_3"], var.minimum_tls_version)
    error_message = "The minimum TLS version must be one of: TLS_1_2, TLS_1_3."
  }
}

variable "project_id" {
  description = "The ID of the GCP project to deploy the load balancer in."
  type        = string
}

variable "proxy_service_backend_id" {
  description = <<EOT
    The ID of the backend service that exposes the regional proxy host(s) to the front-end load 
    balancer.
  EOT

  type    = string
  default = null
}

variable "region" {
  description = "The region to deploy regional resources in."
  type        = string
}

variable "site_asset_backend_id" {
  description = <<EOT
    The ID of the backend service that serves the static site assets to the front-end load balancer.
  EOT

  type = string
}

variable "use_parent_dns_name" {
  description = <<EOT
    When set to true, the module will not prepend the instance name to the DNS name. Otherwise, the
    instance name will be prepended to the DNS name.

    This can only be done for one deployment per DNS managed zone.
  EOT

  type    = bool
  default = false
}
