output "frontend_ip_address" {
  description = "The public IP address for this Control Tower instance."
  value       = google_compute_global_address.main.address
}

output "dns_names" {
  description = "The DNS name(s) registered for the UI load balancer."
  value = [
    google_dns_record_set.a_record.name,
  ]
}

# if alias_parent_domain_to_self is true, the return the alias (parent) domain name
output "instance_base_url" {
  description = "The base URL for the instance."
  value       = format("https://%s", trim(google_dns_record_set.a_record.name, "."))
}
