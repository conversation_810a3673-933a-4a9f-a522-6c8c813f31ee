resource "google_compute_global_address" "main" {
  project = var.project_id
  name    = local.root_name

  description = "The public IP address for the load balancer."
  labels      = var.common_resource_labels
}

resource "google_compute_url_map" "main" {
  project = var.project_id
  name    = "${local.root_name}-load-balancer"

  default_url_redirect {
    https_redirect         = true
    redirect_response_code = "MOVED_PERMANENTLY_DEFAULT"
    strip_query            = false
  }

  host_rule {
    hosts        = ["*"]
    path_matcher = "main"
  }

  path_matcher {
    name = "main"

    default_service = var.site_asset_backend_id

    path_rule {
      paths   = ["/local-foundation-core/"]
      service = var.site_asset_backend_id
    }

    # registers the ignition proxy backend if provided, else the site asset backend
    dynamic "path_rule" {
      for_each = toset(local.ignition_proxy_url_map_paths)

      content {
        paths = ["/${path_rule.value}", "/${path_rule.value}/*"]
        service = coalesce(
          var.proxy_service_backend_id,
          var.site_asset_backend_id
        )
      }
    }
  }
}

resource "google_compute_ssl_policy" "main" {
  project = var.project_id
  name    = local.root_name

  # To pass SSC guidelines
  profile         = "CUSTOM"
  min_tls_version = var.minimum_tls_version

  custom_features = [
    "TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384",
    "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
  ]
}

module "ssl_certificate" {
  source     = "./ssl_certificate"
  project_id = var.project_id

  a_record_dns_name = local.a_record_dns_name
}

resource "google_compute_target_https_proxy" "main" {
  project = var.project_id
  name    = local.root_name
  url_map = google_compute_url_map.main.id

  ssl_certificates = [
    module.ssl_certificate.ssl_certificate_id
  ]

  ssl_policy = google_compute_ssl_policy.main.name
}

resource "google_compute_global_forwarding_rule" "main" {
  project = var.project_id
  name    = local.root_name
  labels  = var.common_resource_labels
  target  = google_compute_target_https_proxy.main.self_link

  ip_address  = google_compute_global_address.main.id
  ip_protocol = "TCP"
  port_range  = "443"

  load_balancing_scheme = "EXTERNAL_MANAGED"
}
