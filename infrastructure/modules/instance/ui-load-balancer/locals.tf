locals {
  resource_namespace = "ui"
  unique_root_name   = "${var.instance_prefix}-${local.resource_namespace}"

  ignition_proxy_url_map_paths = [
    "data",
    "system",
    "res",
    "idp"
  ]

  root_name = (var.enforce_unique_naming
    ? local.unique_root_name
    : local.resource_namespace
  )

  # DNS
  dns_prefix = (var.use_parent_dns_name
    ? ""
    : "${var.instance_prefix}."
  )

  a_record_dns_name = "${local.dns_prefix}${data.google_dns_managed_zone.main.dns_name}"
}
