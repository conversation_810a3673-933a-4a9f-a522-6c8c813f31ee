# Retrieve Neo4j Aura API credentials from GCP Secret Manager
data "google_secret_manager_secret_version" "neo4j_client_credentials" {
  secret  = "auradb_api_client_credentials"
  project = var.project_id
  version = "latest"
}

# Authenticate with Neo4j Aura API to obtain OAuth token
data "http" "aura_token" {
  url    = "https://api.neo4j.io/oauth/token"
  method = "POST"

  request_headers = {
    Content-Type  = "application/x-www-form-urlencoded"
    Accept        = "application/json"
    Authorization = "Basic ${base64encode(join(":", [local.neo4j_credentials.CLIENT_ID, local.neo4j_credentials.CLIENT_SECRET]))}"
  }

  request_body = "grant_type=client_credentials"

  # Validate authentication response
  lifecycle {
    postcondition {
      condition     = self.status_code == 200
      error_message = "Failed to authenticate with Neo4j Aura API. Status: ${self.status_code}, Response: ${self.response_body}"
    }
  }
}
