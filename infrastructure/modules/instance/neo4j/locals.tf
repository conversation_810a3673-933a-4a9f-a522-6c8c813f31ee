locals {
  # Neo4j Aura API configuration
  aura_api_base_url = "https://api.neo4j.io/v1"

  # Parse credentials from Secret Manager
  neo4j_credentials = jsondecode(data.google_secret_manager_secret_version.neo4j_client_credentials.secret_data)

  # Extract OAuth access token
  auth_response = jsondecode(data.http.aura_token.response_body)
  access_token  = try(local.auth_response.access_token, null)
}
