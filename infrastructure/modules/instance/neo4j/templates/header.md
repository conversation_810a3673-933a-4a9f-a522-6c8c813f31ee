# Neo4j AuraDB Terraform Module

This Terraform module creates Neo4j AuraDB instances for multiple tenants using the Neo4j Aura API and securely stores their credentials in Google Cloud Secret Manager.

This module performs the following:

- First, retrieves the GCP secret "auradb_api_client_credentials" which should contain the neo4j API client credentials and tenant ID
- Those values are then used to authenticate with the neo4j API (https://api.neo4j.io/oauth/token) via the http tf module
- Then, that token is used to POST to the neo4j `/instances` endpoint to create new database instances
- The output from the POST call contains the database instance secret. For each new instance that's created, that password is used to create a secret in GCP Secret Manager containing neo4j connection details
  - The POST call should **not** be made for existing database instances, as it does not simply update the instance; it will create a new one. TF state should handle this

## Features

- Creates Neo4j AuraDB instances for each database name in the provided list using the Aura API
- Configures instances with specified memory (1GB), storage (2GB), and 1 CPU
- Stores connection credentials in Google Cloud Secret Manager in a standardized JSON format

## Documentation Links

This module uses the official Neo4j Aura API to create and manage instances. For more information:

- **Neo4j Aura API Documentation**: https://neo4j.com/docs/aura/api/overview/
- **Neo4j Aura API Specification**: https://neo4j.com/docs/aura/platform/api/specification/

## Prerequisites

1. **Neo4j Aura API Credentials**:

   - Create API credentials in the Neo4j Aura Console
   - Navigate to Account Settings > API Keys in the Aura Console
   - Create a new API key and note the Client ID and Client Secret

2. **Google Cloud Project**: A GCP project with Secret Manager API enabled

## Usage

```hcl
module "neo4j" {
  source = "../../../../../infrastructure/modules/instance/neo4j"

  # Standard Control Tower variables
  project_id             = local.project_id
  common_resource_labels = local.common_resource_labels

  neo4j_customers = [{
    database_name = "database_name"
    tenant_name   = "customer_name"
    facility_name = "facility"
  }]

  # Optional: Neo4j specific configuration
  aura_region      = "gcp-us-central1"
  instance_memory  = "1GB"
  instance_storage = "2GB"
  aura_type        = "professional-db"
}
```

## Secret Format

Each database will have its own GCP secret containing neo4j connection details in the following JSON structure:

```json
{
  "NEO4J_URI": "neo4j+s://{instanceId}.databases.neo4j.io",
  "NEO4J_USERNAME": "neo4j",
  "NEO4J_PASSWORD": "generated-secure-password",
  "AURA_INSTANCEID": "aura-instance-id",
  "AURA_INSTANCENAME": "database_instance_name"
}
```

## Authentication Setup

1. **Get Neo4j Aura API Credentials**:

   - Navigate to the Neo4j Aura service in GCP in the project you want to enable it
   - Click Enable
   - Click Manage on Provider to go to the Neo4j console
   - Log in, then take note of the tenant ID that is in the URL of the console. You will also need this for step 2
     (e.g. `https://console-preview.neo4j.io/projects/{your_tenant_id}/instances`)
   - then navigate to **Account Settings** → **API Keys**
   - Click **Generate API Key**
   - Name it something meaningful, e.g. `CT CI TF Key`
   - Click create and copy the **Client ID** and **Client Secret**

2. **Create a secret in GCP containing the neo4j API key credentials**
   - Navigate to Secret Manager in GCP
   - Create a new secret named `auradb_api_client_credentials`
   - Paste the contents of your client credentials from step #1 into the value. It must be in the following format:
   ```json
   {
     "CLIENT_SECRET": "your_api_key_client_secret",
     "CLIENT_ID": "your_api_key_client_id",
     "TENANT_ID": "your_tenant_id"
   }
   ```
   Once the above steps are completed, this module can be executed.
