# https://terraform-docs.io/
#
# This is a configuration file for terraform-docs. 
#
# You can copy this file to any directory you want to have generated terraform docs for.
# This is a good way to keep the docs up to date for a specific module.
# Just make sure you change the path in the ```hcl``` block to the correct path for your module.
#
# You can run the following command to generate the docs (from the scripts directory):
#
# ./generate_terraform_docs.sh

formatter: "markdown table"
version: "0.19.0"

header-from: templates/header.md

recursive:
  enabled: false

sections:
  hide: []
  show: []

content: |-

  <!-- NOTE: 
  This file is automatically generated. 
  Do not modify it manually. 
  If you'd like to update the docs, update the header.md file in the templates directory.
  -->

  {{ .Header }}

  ## Usage

  Basic usage of this module is as follows:

  ```hcl
  {{ include "../../../examples/instance/neo4j/main.tf" }}
  ```

  {{ .Requirements }}

  {{ .Providers }}

  {{ .Modules }}

  {{ .Resources }}

  {{ .Inputs }}

  {{ .Outputs }}

  {{ .Footer }}

output:
  file: README.md
  mode: inject
  template: |-
      <!-- BEGIN_TF_DOCS-->
      {{ .Content }}
      <!-- END_TF_DOCS-->

output-values:
  enabled: false
  from: ""

sort:
  enabled: true
  by: name

settings:
  anchor: false
  color: true
  default: true
  description: true
  escape: true
  hide-empty: false
  html: false
  indent: 2
  lockfile: false
  read-comments: true
  required: true
  sensitive: true
  type: true