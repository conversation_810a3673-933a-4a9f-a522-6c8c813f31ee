variable "aura_region" {
  description = "Neo4j Aura region for database instances"
  type        = string
  default     = "us-east1"
}

variable "aura_type" {
  description = "Neo4j Aura type"
  type        = string
  default     = "professional-db"
}

variable "common_resource_labels" {
  description = "The common labels that should be applied to all resources that can be labeled."
  type        = map(string)
}


variable "instance_memory" {
  description = "Memory allocation for each Neo4j instance"
  type        = string
  default     = "1GB"
}

variable "instance_storage" {
  description = "Storage allocation for each Neo4j instance"
  type        = string
  default     = "2GB"
}

variable "neo4j_customers" {
  description = <<EOT
    "List of customer facilities to create Neo4j instances for. 
    Generally, the database name should be '{tenant_name}_{facility_name}', however the neo4j database
    name needs to be less than or equal to 30 characters. The concatenation of {tenant_name} and {facility_name}
    needs to exactly match the tenant name and facility name coming from the EDP silver pub/sub."
  EOT

  type = list(object({
    database_name = string
    tenant_name   = string
    facility_name = string
  }))
  default = []

  validation {
    condition     = length(var.neo4j_customers) > 0
    error_message = "At least one neo4j customer must be provided."
  }

  validation {
    condition = alltrue([
      for customer in var.neo4j_customers :
      length(customer.database_name) >= 1 && length(customer.database_name) <= 30
    ])
    error_message = "Database names must be between 1 and 30 characters in length."
  }
}

variable "neo4j_version" {
  description = "Neo4j version for the database instances"
  type        = string
  default     = "5"
}

variable "project_id" {
  description = "The ID of the shared services GCP project to deploy to."
  type        = string
}
