module "cache" {
  source  = "terraform-google-modules/memorystore/google"
  version = "~> 14.0.0"

  project_id = var.project_id
  name       = local.root_name
  region     = var.region

  authorized_network      = var.network_self_link
  connect_mode            = "PRIVATE_SERVICE_ACCESS"
  transit_encryption_mode = "DISABLED"

  location_id             = var.zone
  alternative_location_id = var.alternate_zone

  labels = var.common_resource_labels

  enable_apis  = false # APIs are managed in environments/
  auth_enabled = true

  memory_size_gb = 1

  persistence_config = {
    persistence_mode    = "RDB"
    rdb_snapshot_period = "SIX_HOURS"
  }
}

resource "google_secret_manager_secret" "main" {
  project   = var.project_id
  secret_id = "${local.root_name}-auth-string"

  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "main" {
  secret = google_secret_manager_secret.main.id

  secret_data = jsonencode({
    auth_string = module.cache.auth_string
    host        = module.cache.host
    port        = module.cache.port
    type        = "redis"
  })
}
