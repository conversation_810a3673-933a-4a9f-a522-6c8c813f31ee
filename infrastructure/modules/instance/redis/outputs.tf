# TODO: this should be removed as soon as the app code has been updated to use the secret
output "auth_string" {
  description = "The Redis AUTH string used for authentication."
  value       = module.cache.auth_string
}

output "auth_string_secret_name" {
  description = "The name of the secret containing the Redis AUTH string."
  value       = google_secret_manager_secret.main.name
}

output "cache_id" {
  description = "The ID of the Redis instance."
  value       = module.cache.id
}

output "host_ip" {
  description = "The host IP address of the Redis instance."
  value       = module.cache.host
}

output "port" {
  description = "The port number of the Redis instance."
  value       = module.cache.port
}
