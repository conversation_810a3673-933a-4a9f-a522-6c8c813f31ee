# Redis Terraform Module

## Table of Contents

- [Description](#description)
- [Dependencies](#dependencies)
- [Usage](#usage)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This module configures the Google Memorystore Redis service instance used by the APIs. It also creates a Secret Manager secret containing the connection information.

## Dependencies

This module depends on the [Memorystore](https://registry.terraform.io/modules/terraform-google-modules/memorystore/google/latest) module.
