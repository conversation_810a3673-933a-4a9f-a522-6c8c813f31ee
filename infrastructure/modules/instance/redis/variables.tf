variable "alternate_zone" {
  description = "The alernative zone for the Memorystore instance."
  type        = string
}

variable "cache_name" {
  description = <<EOT
    The name to assign to the Memorystore instance. If not provided, a default of `redis` will be 
    used.
  EOT
  type        = string
  default     = "redis"
}

variable "common_resource_labels" {
  description = "The common labels that should be applied to all resources that can be labeled."
  type        = map(string)
}

variable "enforce_unique_naming" {
  description = <<EOT
    Set this to true to use the provided 'environment` value to create a unique prefix for all 
    resources in the module. This is required to allow multiple instances of the module to be 
    deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  EOT

  type = bool
}

variable "instance_prefix" {
  description = <<EOT
    The name of the instance that the resources are being deployed to. This value will be 
    combined with the `naming_root` to create a unique prefix for any resources requiring a 
    globally-unique identifier. Therefore, it must be unique among Control Tower instances but 
    should be as short as possible to comply with GCP resource name length restrictions.
  EOT

  type = string
}

variable "network_self_link" {
  description = <<EOT
    The self-link of the network to register as the Memorystore instance's authorized network.
  EOT

  type = string
}

variable "project_id" {
  description = "The ID of the GCP project to deploy to."
  type        = string
}

variable "region" {
  description = "The region to deploy regional resources in."
  type        = string
}

variable "zone" {
  description = "The zone to deploy zonal resources in."
  type        = string
}
