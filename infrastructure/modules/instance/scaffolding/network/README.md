# Network Submodule

## Description

This sub-module of the Landing Zone module creates the basic networking infrastructure for a Control Tower instance. This includes the instance's VPC network and some basic firewall rules and networking infra, but does not include any subnets - modules that need subnets should create them.

## Resources

### Cloud Router

The Cloud Router and NAT configuration are used to provide internet access to VMs that don't have public IPs. These were created in conjunction with public and private subnetting that wasn't actually used and therefore removed when these modules were created, so there's a possibility that they're not actually needed.

### Firewall

This submodule defines a couple of basic firewall rules that allow GCP services to interact with resources connected to the VPC network. These include:

- allowing traffic from the Google Identity-Aware Proxy service IP range so that IAP tunneling can be used to connect to VMs
- allowing traffic from the Google Health Check IP range so that health checks can reach the appropriate endpoint on VMs or other network-connected resources

### Flow Log Configuration

Network flow logs in GCP are configured on subnets, not at the network level; since the network submodule doesn't currently create any subnets, it doesn't actually need the flow log configuration. The configuration is passed directly from variables to outputs so that any module that creates a subnet in the VPC network can pull the flow log configuration it should use from the landing zone module outputs. This provides a simple mechanism for setting a default flow log configuration for a network.

For details about the log configuration parameters, see the [Log Config](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_subnetwork#nested_log_config) section of the Terraform subnet docs.

### Service Networking Connection

A service networking connection and a private IP address for the connection to use are created to allow GCP service instances like the Memorystore Redis cache and the Cloud SQL PostgreSQL databases to reach network-connected resources via VPC Networking.

#### Deletion Policy

The deletion policy must be set to "ABANDON" in order to allow successful destruction via Terraform, specifically when used by Cloud SQL.

For more information, see the [Deletion Policy](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/service_networking_connection.html#deletion_policy-1) section of the service networking connection docs.
