
resource "google_compute_firewall" "health_check_ingress" {
  project     = var.project_id
  name        = "allow-ingress-from-health-checks"
  description = "Allows ingress from the IP range associated with Google health checks."
  network     = google_compute_network.main.name

  allow {
    protocol = "tcp"
    ports    = ["80", "443"]
  }

  source_ranges = var.google_health_check_source_ip_ranges
}

resource "google_compute_firewall" "iap_ingress" {
  project     = var.project_id
  name        = "allow-ingress-from-iap"
  description = "Allows ingress from the IP range associated with the GCP IAP service."
  network     = google_compute_network.main.name

  allow {
    protocol = "tcp"
    ports    = ["22"]
  }

  source_ranges = var.google_iap_source_ip_ranges
}
