resource "google_compute_network" "main" {
  project      = var.project_id
  name         = "main"
  description  = "The primary VPC network for Control Tower."
  routing_mode = "GLOBAL"

  auto_create_subnetworks         = false
  delete_default_routes_on_create = false

  lifecycle {
    create_before_destroy = true
  }
}

resource "google_compute_route" "main" {
  project     = var.project_id
  name        = "internet-egress"
  description = "Route through the default internet gateway to access the internet."
  network     = google_compute_network.main.self_link

  tags             = ["egress-inet"]
  next_hop_gateway = local.default_internet_gateway
  dest_range       = "0.0.0.0/0"

  lifecycle {
    create_before_destroy = true
  }
}

resource "google_compute_global_address" "google_service_networking" {
  project     = var.project_id
  name        = "google-service-network-connect"
  description = <<EOF
    Private IP range for the private service networking connection that peers GCP services like 
    Memorystore and Cloud SQL with our VPC.
  EOF

  purpose      = "VPC_PEERING"
  address_type = "INTERNAL"
  labels       = var.common_resource_labels

  network       = google_compute_network.main.self_link
  address       = var.private_service_connect_ip_address
  prefix_length = 16 # 20 was used previously, but 16 is recommended
}

resource "google_service_networking_connection" "google_service_networking" {
  network = google_compute_network.main.self_link
  service = "servicenetworking.googleapis.com"

  reserved_peering_ranges = [google_compute_global_address.google_service_networking.name]

  # reguired for successful destroy
  deletion_policy = "ABANDON"
}
