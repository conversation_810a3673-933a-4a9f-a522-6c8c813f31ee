output "flow_log_configuration" {
  description = <<EOF
    The default flow log configuration that subnets in the VPC network should use unless 
    requirements dictate a different configuration.
    
  EOF

  value = var.flow_log_configuration
}

output "network_self_link" {
  description = "The self-link of the VPC network that was created."
  value       = google_compute_network.main.self_link
}

output "service_networking_connection_ip_range" {
  description = <<EOF
    The IP range that was reserved for the private service networking connection. This is used to 
    connect GCP services like Memorystore and Cloud SQL with our VPC.
  EOF

  value = format("%s/%s",
    google_compute_global_address.google_service_networking.address,
    google_compute_global_address.google_service_networking.prefix_length
  )
}
