variable "common_resource_labels" {
  description = "The common labels that should be applied to all resources that can be labeled."
  type        = map(string)
}

variable "enable_flow_logs" {
  description = "Whether to enable flow logs for the VPC network."
  type        = bool
}

variable "enforce_unique_naming" {
  description = <<EOT
    Set this to true to use the provided 'environment` value to create a unique prefix for all 
    resources in the module. This is required to allow multiple instances of the module to be 
    deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  EOT

  type = bool
}

variable "flow_log_configuration" {
  description = <<EOT
    The network flow log configuration that should be used by each subnet in the VPC network. This 
    value will be returned as an output of the module so that it can be used in subsequent module 
    calls that create subnets in the VPC network.
    
    See the README for more information and a link to the Terraform documentation.

    Parameters:
      - interval: The interval at which to sample packets. Valid values are INTERVAL_5_SEC, 
          INTERVAL_30_SEC, INTERVAL_1_MIN, INTERVAL_5_MIN, INTERVAL_10_MIN, and INTERVAL_15_MIN.
      - flow_sampling: The percentage of packets to sample. Valid values are between 0 and 1.
      - metadata: The metadata to include in the flow logs. Valid values are INCLUDE_ALL_METADATA, 
        INCLUDE_L7_METADATA, and INCLUDE_NO_METADATA.
  EOT

  type = object({
    aggregation_interval = string
    flow_sampling        = number
    metadata             = string
  })

  default = null

  validation {
    error_message = <<EOT
      The flow log interval must be one of INTERVAL_5_SEC, INTERVAL_30_SEC, INTERVAL_1_MIN, 
      INTERVAL_5_MIN, INTERVAL_10_MIN, or INTERVAL_15_MIN.
    EOT

    condition = var.flow_log_configuration == null || (
      var.flow_log_configuration.aggregation_interval == "INTERVAL_5_SEC" ||
      var.flow_log_configuration.aggregation_interval == "INTERVAL_30_SEC" ||
      var.flow_log_configuration.aggregation_interval == "INTERVAL_1_MIN" ||
      var.flow_log_configuration.aggregation_interval == "INTERVAL_5_MIN" ||
      var.flow_log_configuration.aggregation_interval == "INTERVAL_10_MIN" ||
      var.flow_log_configuration.aggregation_interval == "INTERVAL_15_MIN"
    )
  }

  validation {
    error_message = <<EOT
      The flow log sampling must be between 0 and 1.
    EOT

    condition = var.flow_log_configuration == null || (
      var.flow_log_configuration.flow_sampling >= 0 &&
      var.flow_log_configuration.flow_sampling <= 1
    )
  }

  validation {
    error_message = <<EOT
      The flow log metadata must be one of INCLUDE_ALL_METADATA, INCLUDE_L7_METADATA, or 
      INCLUDE_NO_METADATA.
    EOT

    condition = var.flow_log_configuration == null || (
      var.flow_log_configuration.metadata == "INCLUDE_ALL_METADATA" ||
      var.flow_log_configuration.metadata == "INCLUDE_L7_METADATA" ||
      var.flow_log_configuration.metadata == "INCLUDE_NO_METADATA"
    )
  }
}

variable "google_health_check_source_ip_ranges" {
  description = <<EOT
    The IP CIDR range(s) that should be added to the firewall rules to allow ingress from Google 
    health checks.
  EOT

  type = list(string)
}

variable "google_iap_source_ip_ranges" {
  description = <<EOT
    The IP CIDR range(s) that should be added to the firewall rules to allow ingress from the 
    Google IAP service to allow Google-identity-based authentication to resources in the network.
  EOT

  type = list(string)
}

variable "instance_display_name" {
  description = <<EOT
    The human-readable name of the instance that the resources are being deployed to.
  EOT

  type = string
}

variable "instance_prefix" {
  description = <<EOT
    The name of the instance that the resources are being deployed to. This value will be 
    combined with the `naming_root` to create a unique prefix for any resources requiring a 
    globally-unique identifier. Therefore, it must be unique among Control Tower instances but 
    should be as short as possible to comply with GCP resource name length restrictions.
  EOT

  type = string
}

variable "private_service_connect_ip_address" {
  description = <<EOT
    The base IP address to use for the private service connect that allows GCP service instances 
    like Memorystore and Cloud SQL to interact with resources on the Control Tower network.
  EOT

  type = string
}

variable "project_id" {
  description = "The ID of the project to deploy the VPC network in."
  type        = string
}

variable "region" {
  description = "The region to deploy regional resources in."
  type        = string
}
