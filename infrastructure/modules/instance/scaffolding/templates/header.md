# Scaffolding Module

WIP

## Table of Contents

- [Description](#description)
- [Usage](#usage)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This module performs configuration and creates resources required to deploy Control Tower services into a project. This includes configuration of the DNS subdomain for the environment, some basic networking, and supporting services like buckets for access logging. This module is always the first deployment in a new Control Tower environment after the project has been created.
