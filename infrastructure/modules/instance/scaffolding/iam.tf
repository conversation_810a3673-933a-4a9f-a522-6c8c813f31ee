locals {
  serverless_agent_suffix = "serverless-robot-prod.iam.gserviceaccount.com"
}

# Ensure the Cloud Run service agent can access images in the core services Artifact Registry
resource "google_project_iam_member" "cloud_run_agent" {
  project = var.artifact_registry_project_id
  role    = "roles/artifactregistry.reader"
  member = (
    "serviceAccount:service-${var.project_number}@${local.serverless_agent_suffix}"
  )

  lifecycle {
    create_before_destroy = true
  }
}
