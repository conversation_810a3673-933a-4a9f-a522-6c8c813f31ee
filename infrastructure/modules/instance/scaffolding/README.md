<!-- BEGIN_TF_DOCS-->

<!-- NOTE: 
This file is automatically generated. 
Do not modify it manually. 
If you'd like to update the docs, update the header.md file in the templates directory.
-->

# Scaffolding Module

WIP

## Table of Contents

- [Description](#description)
- [Usage](#usage)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This module performs configuration and creates resources required to deploy Control Tower services into a project. This includes configuration of the DNS subdomain for the environment, some basic networking, and supporting services like buckets for access logging. This module is always the first deployment in a new Control Tower environment after the project has been created.

## Usage

Basic usage of this module is as follows:

```hcl
module "scaffolding" {
  source  = "gitlab.com/dematic/control-tower-scaffolding/google"
  version = "~> 0.1.0"

  # Required variables
  access_logs_bucket_config = var.access_logs_bucket_config
  additional_api_identities = var.additional_api_identities
  additional_apis           = var.additional_apis
  common_resource_labels    = var.common_resource_labels
  core_services_project_id  = var.core_services_project_id
  enforce_unique_naming     = var.enforce_unique_naming
  iap_config                = var.iap_config
  instance_display_name     = var.instance_display_name
  instance_prefix           = var.instance_prefix
  naming_root               = var.naming_root
  network_config            = var.network_config
  project_config            = var.project_config
  project_id                = var.project_id
  project_number            = var.project_number
  region                    = var.region
  snapshots_bucket_config   = var.snapshots_bucket_config

  # Optional variables
  project_role_assignments   = {}
  shared_vpc_host_project_id = null
}
```

## Requirements

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |

## Providers

| Name | Version |
|------|---------|
| google | ~> 6.38.0 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| cloud\_run\_iap | ./iap | n/a |
| network | ./network | n/a |

## Resources

| Name | Type |
|------|------|
| [google_compute_shared_vpc_service_project.main](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_shared_vpc_service_project) | resource |
| [google_project_iam_member.cloud_run_agent](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member) | resource |
| [google_storage_bucket.access_logs](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/storage_bucket) | resource |
| [google_storage_bucket.snapshots](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/storage_bucket) | resource |
| [google_project.project](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/project) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| access\_logs\_bucket\_config | The configuration for the access logs storage bucket. | ```object({ is_retention_policy_locked = bool location = string retention_period_seconds = number storage_class = string })``` | n/a | yes |
| additional\_api\_identities | The list of additional APIs that use service agents that should be enabled on the project,      beyond the core set already defined in the module. This will also force the initialization of      the service agent so that they can be immediately used by other Terraform resources. | ```list(object({ api = string roles = list(string) }))``` | n/a | yes |
| additional\_apis | The list of additional APIs that should be enabled on the project beyond the default set.     This is a list of API names, such as "compute.googleapis.com", "storage.googleapis.com", etc. | `list(string)` | n/a | yes |
| common\_resource\_labels | The common labels that should be applied to all resources that can be labeled. | `map(string)` | n/a | yes |
| core\_services\_project\_id | The ID of the core services project to use for the project. | `string` | n/a | yes |
| enforce\_unique\_naming | Set this to true to use the provided 'environment` value to create a unique prefix for all      resources in the module. This is required to allow multiple instances of the module to be      deployed in the same GCP project. Used by Review Apps to generate the preview environments. ` | `bool` | n/a | yes |
| iap\_config | The configuration for the Identity-Aware Proxy (IAP) service. This includes whether IAP is      enabled; if enabled, two additional properties are required: the OAuth brand configuration, and      a list of members who should be granted permissions to impersonate the IAP service account. The      members should be in the format `<type>:<email>`, where `<type>` is the type of the IAM member      (e.g., "user", "serviceAccount", "group") and `<email>` is a valid email address. | ```object({ enable = bool members = optional(list(string), []) brand_configuration = optional(object({ application_title = string support_email = string })) })``` | n/a | yes |
| instance\_display\_name | The human-readable name of the instance that the resources are being deployed to. | `string` | n/a | yes |
| instance\_prefix | The name of the instance that the resources are being deployed to. This value will be      combined with the `naming_root` to create a unique prefix for any resources requiring a      globally-unique identifier. Therefore, it must be unique among Control Tower instances but      should be as short as possible to comply with GCP resource name length restrictions. | `string` | n/a | yes |
| naming\_root | The root identifier for all Control Tower resources. This value will be combined with the     `environment` to create a unique prefix for any resources requiring a globally-unique name.      This value is intended to be shared across all Control Tower deployments. As of module      creation, this value is "ict". | `string` | n/a | yes |
| network\_config | The configuration for the VPC network. See the README and variables.tf of the network submodule      for more details on the configuration options. | ```object({ enable_flow_logs = bool ip_addresses = object({ google_health_check_source_ip_ranges = list(string) google_iap_source_ip_ranges = list(string) private_service_connect_ip_address = string }) flow_log_config = optional(object({ aggregation_interval = string flow_sampling = number metadata = string })) })``` | n/a | yes |
| project\_config | The parameters needed to create the new project for the Control Tower instance. This includes     the billing account, folder ID, organization ID, a folder short name value to use for      project naming, any project-specific labels that should be added to common\_labels and applied      to the project. | ```object({ billing_account = string folder_id = string folder_short_name = string organization_id = string project_labels = map(string) })``` | n/a | yes |
| project\_id | The ID of the GCP project to deploy to. | `string` | n/a | yes |
| project\_number | The project number of the GCP project to deploy to. | `string` | n/a | yes |
| project\_role\_assignments | The IAM role assignments to apply to the project. This is a map of role names to a list of      member identifiers. The member identifiers can be any valid GCP IAM member identifier,      including service accounts, groups, and users. Non-authoratative (i.e., won't override      assignments defined by other modules). | `map(list(string))` | `{}` | no |
| region | The region to deploy regional resources in. | `string` | n/a | yes |
| shared\_vpc\_host\_project\_id | The ID of the shared VPC host project to use for the Control Tower instance. This is required      if the Control Tower instance is to be deployed in a shared VPC network. | `string` | `null` | no |
| snapshots\_bucket\_config | The configuration for the snapshot storage bucket. | ```object({ is_retention_policy_locked = bool location = string retention_period_seconds = number storage_class = string })``` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| access\_logs\_bucket\_name | The name of the access logs bucket. |
| network\_flow\_log\_configuration | The default flow log configuration that subnets in the VPC network should use unless      requirements dictate a different configuration. |
| network\_self\_link | The self-link of the VPC network that was created. |
| service\_networking\_connection\_ip\_range | The IP range that was reserved for the private service networking connection. This is used to      connect GCP services like Memorystore and Cloud SQL with our VPC. |
| snapshots\_bucket\_name | The name of the snapshots bucket. |

<!-- END_TF_DOCS-->
<!-- BEGIN_TFVARS_DOCS-->

## Terraform TFVARS file examples

```hcl
inputs {
    
  # The configuration for the access logs storage bucket.
  access_logs_bucket_config = ""
  
  #     The list of additional APIs that use service agents that should be enabled on the project,
  #     beyond the core set already defined in the module. This will also force the initialization of
  #     the service agent so that they can be immediately used by other Terraform resources.
  #
  additional_api_identities = ""
  
  #     The list of additional APIs that should be enabled on the project beyond the default set.
  #     This is a list of API names, such as "compute.googleapis.com", "storage.googleapis.com", etc.
  #
  additional_apis = ""
  
  # The common labels that should be applied to all resources that can be labeled.
  common_resource_labels = ""
  
  # The ID of the core services project to use for the project.
  core_services_project_id = ""
  
  #     Set this to true to use the provided 'environment` value to create a unique prefix for all
  #     resources in the module. This is required to allow multiple instances of the module to be
  #     deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  #
  enforce_unique_naming = ""
  
  #     The configuration for the Identity-Aware Proxy (IAP) service. This includes whether IAP is
  #     enabled; if enabled, two additional properties are required: the OAuth brand configuration, and
  #     a list of members who should be granted permissions to impersonate the IAP service account. The
  #     members should be in the format `<type>:<email>`, where `<type>` is the type of the IAM member
  #     (e.g., "user", "serviceAccount", "group") and `<email>` is a valid email address.
  #
  iap_config = ""
  
  #     The human-readable name of the instance that the resources are being deployed to.
  #
  instance_display_name = ""
  
  #     The name of the instance that the resources are being deployed to. This value will be
  #     combined with the `naming_root` to create a unique prefix for any resources requiring a
  #     globally-unique identifier. Therefore, it must be unique among Control Tower instances but
  #     should be as short as possible to comply with GCP resource name length restrictions.
  #
  instance_prefix = ""
  
  #     The root identifier for all Control Tower resources. This value will be combined with the
  #     `environment` to create a unique prefix for any resources requiring a globally-unique name.
  #     This value is intended to be shared across all Control Tower deployments. As of module
  #     creation, this value is "ict".
  #
  naming_root = ""
  
  #     The configuration for the VPC network. See the README and variables.tf of the network submodule
  #     for more details on the configuration options.
  #
  network_config = ""
  
  #     The parameters needed to create the new project for the Control Tower instance. This includes
  #     the billing account, folder ID, organization ID, a folder short name value to use for
  #     project naming, any project-specific labels that should be added to common_labels and applied
  #     to the project.
  #
  project_config = ""
  
  # The ID of the GCP project to deploy to.
  project_id = ""
  
  # The project number of the GCP project to deploy to.
  project_number = ""
  
  #     The IAM role assignments to apply to the project. This is a map of role names to a list of
  #     member identifiers. The member identifiers can be any valid GCP IAM member identifier,
  #     including service accounts, groups, and users. Non-authoratative (i.e., won't override
  #     assignments defined by other modules).
  #
project_role_assignments = {}

# The region to deploy regional resources in.
region = ""

#     The ID of the shared VPC host project to use for the Control Tower instance. This is required
#     if the Control Tower instance is to be deployed in a shared VPC network.
#
shared_vpc_host_project_id = ""

# The configuration for the snapshot storage bucket.
snapshots_bucket_config = ""
}
```

<!-- END_TFVARS_DOCS-->
