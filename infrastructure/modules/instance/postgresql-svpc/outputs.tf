output "backups" {
  description = <<EOF
    The output of the backup module, which includes the bucket name, bucket URL, and service 
    account used by the backup Cloud Run service.
  EOF

  value = {
    bucket_name = google_storage_bucket.main.name
    bucket_url  = google_storage_bucket.main.url

    service_account = module.backups.service_account
  }
}

output "connection_string_secret_name" {
  description = <<EOF
    The name of the secret that contains the connection string to the PostgreSQL instance.
  EOF

  value = google_secret_manager_secret.main.name
}

output "connection_string_secret_version" {
  description = <<EOF
    The name of the secret that contains the connection string to the PostgreSQL instance.
  EOF

  value = google_secret_manager_secret_version.main.name
}

output "instances" {
  description = "The list of instances in the PostgreSQL instance pool."
  value       = module.postgresql.instances
}

output "master_instance" {
  description = <<EOF
    The master instance information, including connection name, DNS name, instance name, IP 
    address, and zone. This is used for connecting to the PostgreSQL instance.
  EOF

  value = {
    connection_name = module.postgresql.instance_connection_name
    dns_name        = module.postgresql.dns_name
    instance_name   = module.postgresql.instance_name
    ip_address      = module.postgresql.instance_ip_address
    zone            = var.zone
  }
}

output "migration_cloud_run" {
  description = <<EOF
        The output of the migration Cloud Run service, which includes the service account email, 
        service ID, location, name, revision, status, and URL.
    EOF

  value = {
    id       = google_cloud_run_v2_service.migrations.id
    location = var.region
    url      = google_cloud_run_v2_service.migrations.uri

    # service_account_email = google_service_account.migrations.email
    # id                    = google_cloud_run_v2_service.migrations.id
    # location              = var.region
    # uri                   = google_cloud_run_v2_service.migrations.uri
    # service_location = module.migration_cloud_run.location
    # service_name     = module.migration_cloud_run.service_name
    # service_revision = module.migration_cloud_run.revision
    # service_status   = module.migration_cloud_run.service_status
    # service_url      = module.migration_cloud_run.service_url
    # service_account_email = google_service_account.migrations.email
  }
}
