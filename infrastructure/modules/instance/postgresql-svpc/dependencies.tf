data "google_artifact_registry_docker_image" "main" {
  project       = var.artifact_registry_repository.project
  location      = var.artifact_registry_repository.location
  repository_id = var.artifact_registry_repository.repository
  image_name = (
    "${var.migration_container_image.image_name}:${var.migration_container_image.image_tag}"
  )
}

data "google_iam_policy" "noauth" {
  binding {
    role = "roles/run.invoker"
    members = [
      "allUsers",
    ]
  }
}
