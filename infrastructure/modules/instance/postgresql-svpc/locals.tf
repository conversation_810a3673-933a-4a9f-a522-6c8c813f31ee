locals {
  # Resource naming
  resource_namespace        = var.database_instance_name
  unique_root_name          = "${var.instance_prefix}-${local.resource_namespace}"
  globally_unique_root_name = "${var.naming_root}-${local.unique_root_name}"

  root_name = (var.enforce_unique_naming
    ? local.unique_root_name
    : local.resource_namespace
  )

  migrations_root_name = var.migration_cloud_run_config.cloud_run_name

  migration_service_account_roles = [
    "roles/secretmanager.secretAccessor",
    "roles/compute.networkUser",
    "roles/vpcaccess.user",
    "roles/cloudsql.client",
  ]

  vpc_host_project_roles = [
    "roles/compute.networkUser",
    "roles/vpcaccess.user"
  ]

  # Cloud Run config
  cloud_run_env_vars = merge(
    var.environment_variables,
    {
      CLOUD_SQL_CONN_NAME      = module.postgresql.instance_connection_name
      CREATED_BY               = "terraform"
      ENVIRONMENT              = var.environment
      GCP_POSTGRES_SECRET_NAME = google_secret_manager_secret_version.main.name
      PGUSERNAME               = var.database_username
      PGPASSWORD               = random_password.main.result
      PROJECT_ID               = var.project_id
      TENANTS                  = jsonencode(var.databases)
    }
  )
}
