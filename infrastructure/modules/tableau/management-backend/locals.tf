locals {
  # Resource naming
  resource_namespace        = "tableau-mgmt"
  unique_root_name          = "${var.instance_prefix}-${local.resource_namespace}"
  globally_unique_root_name = "${var.naming_root}-${local.unique_root_name}"

  root_name = (var.enforce_unique_naming
    ? local.unique_root_name
    : local.resource_namespace
  )

  environment_variables = {
    AUTH_AUDIENCE   = var.auth_audience
    AUTH_DOMAIN     = var.auth_domain
    PROJECT_ID      = var.project_id
    TABLEAU_NODE_IP = var.primary_node_private_ip
  }

  service_account_roles = [
    "roles/compute.networkUser",
    "roles/vpcaccess.user"
  ]
}
