variable "artifact_registry_repository" {
  description = <<EOF
    The Artifact Registry repository containing the container image to deploy in the Cloud Run. 
    This includes the following fields:
    - `location`: The location of the Artifact Registry.
    - `project_id`: The project ID of the Artifact Registry.
    - `repository_id`: The repository ID of the Artifact Registry repository.
  EOF

  type = object({
    location   = string
    project    = string
    repository = string
  })
}

variable "auth_audience" {
  description = <<EOT
    The Auth0 auth audience to use for this deployment. This will be added as an environment 
    variable in the Cloud Run container. E.g., "https://dev.api.ict.dematic.dev"
  EOT

  type = string
}

variable "auth_domain" {
  description = <<EOT
    The Auth0 auth domain to use for this deployment. This will be added as an environment variable 
    in the Cloud Run container. E.g., "dev-zmogq9vd.us.auth0.com"
  EOT

  type = string
}

variable "cloud_run_config" {
  description = "The configuration for the Cloud Run service."
  type = object({
    enable_binary_auth = bool

    cpu_limit    = string,
    memory_limit = string,

    cpu_idle          = bool
    startup_cpu_boost = bool

    min_instances = number
    max_instances = number
  })
}

variable "common_resource_labels" {
  description = <<EOT
    The common labels that should be applied to all resources that can be labeled.
  EOT

  type = map(string)
}

variable "container_image" {
  description = "The Artifact Registry image to deploy."
  type = object({
    image_name = string,
    image_tag  = string,
  })
}

variable "enforce_unique_naming" {
  description = <<EOT
    Set this to true to use the provided 'environment` value to create a unique prefix for all 
    resources in the module. This is required to allow multiple instances of the module to be 
    deployed in the same GCP project. Used by Review Apps to generate the preview environments.
  EOT

  type = bool
}

variable "instance_prefix" {
  description = <<EOT
    The name of the instance that the resources are being deployed to. This value will be 
    combined with the `naming_root` to create a unique prefix for any resources requiring a 
    globally-unique identifier. Therefore, it must be unique among Control Tower instances but 
    should be as short as possible to comply with GCP resource name length restrictions.
  EOT

  type = string
}

variable "naming_root" {
  description = <<EOT
    The root identifier for all Control Tower resources. This value will be combined with the
    `environment` to create a unique prefix for any resources requiring a globally-unique name. 
    This value is intended to be shared across all Control Tower deployments. As of module 
    creation, this value is "ict".
  EOT

  type = string
}

variable "primary_node_private_ip" {
  description = "The private IP address of the primary node in the Tableau Server cluster."
  type        = string
}

variable "project_id" {
  description = "The ID of the GCP project to deploy the load balancer in."
  type        = string
}

variable "region" {
  description = "The region to deploy regional resources in."
  type        = string
}

variable "subnet_name" {
  description = "The name of the subnetwork to create the VPC access connector in."
  type        = string
}

variable "vpc_access_connector_config" {
  description = "The configuration for the VPC access connector."
  type = object({
    machine_type  = string
    min_instances = number
    max_instances = number
  })
}
