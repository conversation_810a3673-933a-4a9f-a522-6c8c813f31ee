resource "google_service_account" "main" {
  project    = var.project_id
  account_id = "${local.root_name}-sa"
}

resource "google_project_iam_member" "project" {
  project  = var.project_id
  for_each = toset(local.service_account_roles)

  role   = each.value
  member = "serviceAccount:${google_service_account.main.email}"
}

resource "google_vpc_access_connector" "main" {
  project = var.project_id
  name    = "${local.root_name}-con"
  region  = var.region

  subnet {
    name = var.subnet_name
  }

  machine_type  = var.vpc_access_connector_config.machine_type
  min_instances = var.vpc_access_connector_config.min_instances
  max_instances = var.vpc_access_connector_config.max_instances

  lifecycle {
    create_before_destroy = true
  }
}

resource "google_cloud_run_v2_service" "main" {
  project  = var.project_id
  name     = "${local.root_name}-api"
  location = var.region

  labels = var.common_resource_labels

  deletion_protection = false

  dynamic "binary_authorization" {
    for_each = var.cloud_run_config.enable_binary_auth ? [1] : []

    content {
      use_default = true
    }
  }

  template {
    service_account = google_service_account.main.email

    containers {
      image = data.google_artifact_registry_docker_image.main.self_link

      resources {
        limits = {
          cpu    = var.cloud_run_config.cpu_limit
          memory = var.cloud_run_config.memory_limit
        }

        cpu_idle          = var.cloud_run_config.cpu_idle
        startup_cpu_boost = var.cloud_run_config.startup_cpu_boost
      }

      dynamic "env" {
        for_each = local.environment_variables

        content {
          name  = env.key
          value = env.value
        }
      }
    }

    scaling {
      min_instance_count = var.cloud_run_config.min_instances
      max_instance_count = var.cloud_run_config.max_instances
    }

    vpc_access {
      egress    = "ALL_TRAFFIC"
      connector = google_vpc_access_connector.main.id
    }
  }
}


resource "google_compute_security_policy" "main" {
  project     = var.project_id
  name        = "${local.root_name}-backend-security-policy"
  description = "Security policy for the Tableau Management API backend."

  type = "CLOUD_ARMOR"

  adaptive_protection_config {
    layer_7_ddos_defense_config {
      enable          = true
      rule_visibility = "STANDARD"
    }
  }

  advanced_options_config {
    json_parsing = "STANDARD"
    log_level    = "NORMAL"

    json_custom_config {
      content_types = []
    }
  }

  rule {
    action   = "allow"
    priority = 2147483647

    match {
      versioned_expr = "SRC_IPS_V1"

      config {
        src_ip_ranges = ["*"]
      }
    }
  }
}

resource "google_compute_region_network_endpoint_group" "main" {
  project = var.project_id
  name    = "${local.root_name}-neg"

  description           = "Network endpoint group for the Tableau Management API Cloud Run."
  network_endpoint_type = "SERVERLESS"
  region                = var.region

  cloud_run {
    service = google_cloud_run_v2_service.main.name
  }
}

resource "google_compute_backend_service" "main" {
  project     = var.project_id
  name        = "${local.root_name}-backend"
  description = "Backend service for the Tableau Management API."

  load_balancing_scheme = "EXTERNAL_MANAGED"
  port_name             = "http"
  protocol              = "HTTP"
  security_policy       = google_compute_security_policy.main.self_link
  timeout_sec           = 30

  backend {
    group = google_compute_region_network_endpoint_group.main.self_link
  }

  iap {
    enabled = false
  }

  log_config {
    enable      = true
    sample_rate = 1.0
  }
}
