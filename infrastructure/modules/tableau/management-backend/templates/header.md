# API Backends

## Table of Contents

- [Description](#description)
- [Details](#details)
- [Usage](#usage)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This module creates the Tableau Management API that is used to manage authentication between Control Tower and Tableau. The module is published to the GitLab module registry, and at the time of creation will be used by the Insights team to deploy the Management API in the Tableau projects that they manage for CT.

## Details

The module creates a Cloud Run and the necessary supporting resources to configure it as a load balancer backend:

- Service account with required permissions for operations
- Network Endpoint Group and load balancer backend service to attach to the Tableau load balancer
- VPC access connector to attach to the VPC containing the Tableau Server VMs
