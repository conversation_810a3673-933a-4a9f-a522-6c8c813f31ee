# Control Tower

This repository contains infrastructure code and documentation generation tools for managing Terraform configurations.

## Documentation Generation

The repository includes a script to automatically generate and maintain Terraform documentation. This helps ensure that infrastructure code remains well-documented and up-to-date.

### generate-terraform-docs.sh

This script serves as a pre-commit hook to format and document Terraform code. It performs several important functions:

#### Features

1. **Terraform Formatting**
   - Formats all Terraform files using `tofu fmt`
   - Ensures consistent code style across the codebase

2. **Documentation Generation**
   - Generates documentation for Terraform modules using `terraform-docs`
   - Creates example configurations for modules
   - Updates README files with current variable documentation

3. **Directory Structure**
   - Processes files in:
     - `infrastructure/modules/*`
     - `infrastructure/examples/*`

#### Requirements

- `terraform-docs` must be installed
- `terragrunt` must be installed

You can install these dependencies using:

```bash
# Using mise (recommended)
mise install

# Or on MacOS
brew install terraform-docs
```

#### Usage

The script can be run directly or as a pre-commit hook through <PERSON><PERSON>:

```bash
# Run directly
./scripts/generate-terraform-docs.sh

# Show help
./scripts/generate-terraform-docs.sh --help
```

#### Configuration

The script uses several configuration files:

1. `.terraform-docs.yml`
   - Main configuration for terraform-docs
   - Controls documentation format and content
   - Can be placed in module directories for module-specific settings

2. `.terraform-example.yml`
   - Controls generation of example configurations
   - Used to create example usage in the `examples` directory

3. `.tfvars-docs.yml`
   - Specific configuration for variable documentation
   - Controls how variables are documented in README files

#### Directory Structure

```bash
infrastructure/
├── modules/           # Terraform modules
│   ├── common/       # Common infrastructure modules
│   │   └── common-services/
│   │       ├── templates/
│   │       │   └── header.md
│   │       ├── .terraform-docs.yml
│   │       ├── .terraform-example.yml
│   │       ├── .tfvars-docs.yml
│   │       ├── main.tf
│   │       ├── variables.tf
│   │       ├── outputs.tf
│   │       └── locals.tf
│   └── instance/     # Instance-specific modules
│       └── project/
│           ├── templates/
│           │   └── header.md
│           ├── .terraform-docs.yml
│           ├── .terraform-example.yml
│           ├── .tfvars-docs.yml
│           ├── main.tf
│           ├── variables.tf
│           ├── outputs.tf
│           └── locals.tf
└── examples/         # Generated example configurations
    ├── common/
    │   └── common-services/
    │       └── main.tf
    └── instance/
        └── project/
            └── main.tf
```

#### Error Handling

The script includes error checking for:

- Missing dependencies
- Invalid repository root
- Failed documentation generation
- Failed formatting

#### Contributing

When contributing to this repository:

1. The pre-commit hook (`generate-terraform-docs.sh`) will automatically:
   - Format your Terraform code using `terragrunt hcl fmt`
   - Generate and update documentation
   - Create example configurations
   - Stage and amend the commit with any generated files

2. If you're not using the pre-commit hook, you can manually run:

   ```bash
   ./scripts/generate-terraform-docs.sh
   ```

3. Make sure you have the required dependencies installed:

   ```bash
   mise install
   # or
   brew install terraform-docs
   ```

#### Troubleshooting

If you encounter issues:

1. **Missing Dependencies**

   ```bash
   # Install required tools
   mise install
   # or
   brew install terraform-docs
   ```

2. **Documentation Generation Errors**

   - Check if all required configuration files exist
   - Verify file permissions
   - Ensure proper directory structure

3. **Formatting Issues**

   - Run `terragrunt hcl fmt --all` manually to check for formatting problems
   - Review the generated documentation for syntax errors

#### Module Documentation Structure

Each Terraform module's README is automatically generated using terraform-docs. The documentation structure is controlled by:

1. **Configuration File** (`.terraform-docs.yml`)
   - Controls the documentation format and content structure
   - Specifies which sections to include
   - References template files

2. **Templates Directory**
   - Contains `header.md` with module overview and static content
   - Located in each module's `templates/` directory

3. **Generated Content**
   - Automatically includes sections for:
     - Requirements
     - Providers
     - Modules
     - Resources
     - Inputs
     - Outputs
   - Content is injected between `<!-- BEGIN_TF_DOCS-->` and `<!-- END_TF_DOCS-->` markers

4. **Example Structure**

   ```bash
   module_name/
   ├── .terraform-docs.yml
   ├── templates/
   │   ├── header.md    # Module overview and static content
   └── README.md        # Generated documentation
   ```

To modify a module's documentation:

1. Update the content in `templates/header.md`
2. Run the documentation generation script to apply changes
