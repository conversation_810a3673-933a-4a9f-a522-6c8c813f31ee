locals {
  attestation_root_name = "attestation"
}

resource "google_kms_key_ring" "attestor" {
  project  = var.project_id
  name     = local.attestation_root_name
  location = "global"
}

resource "google_kms_crypto_key" "attestor" {
  name     = local.attestation_root_name
  key_ring = google_kms_key_ring.attestor.id
  purpose  = "ASYMMETRIC_SIGN"
  labels   = var.common_resource_labels

  version_template {
    algorithm = "RSA_SIGN_PKCS1_4096_SHA512"
  }
}

data "google_kms_crypto_key_version" "attestor" {
  crypto_key = google_kms_crypto_key.attestor.id
}

resource "google_container_analysis_note" "attestor" {
  project = var.project_id
  name    = local.attestation_root_name

  attestation_authority {
    hint {
      human_readable_name = "Attestor Note for Control Tower image publishing."
    }
  }
}

resource "google_binary_authorization_attestor" "attestor" {
  project = var.project_id
  name    = local.attestation_root_name

  attestation_authority_note {
    note_reference = google_container_analysis_note.attestor.name

    public_keys {
      id = data.google_kms_crypto_key_version.attestor.id

      pkix_public_key {
        public_key_pem      = data.google_kms_crypto_key_version.attestor.public_key[0].pem
        signature_algorithm = data.google_kms_crypto_key_version.attestor.public_key[0].algorithm
      }
    }
  }
}