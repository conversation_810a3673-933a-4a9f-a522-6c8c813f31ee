# =============================================================================
# Non-prod DNS zone
# =============================================================================

# reference to the root Dematic DNS managed zone used for non-prod
data "google_dns_managed_zone" "nonprod_root" {
  project = var.domain_config.nonprod_root_zone.project_id
  name    = var.domain_config.nonprod_root_zone.name
}

locals {
  # Dematic nonprod root zone
  nonprod_root_dns_name = data.google_dns_managed_zone.nonprod_root.dns_name

  # CT nonprod subdomain
  nonprod_dns_name    = "${var.domain_config.subdomain_prefix}.${local.nonprod_root_dns_name}"
  nonprod_domain_name = trim(local.nonprod_dns_name, ".")
  nonprod_zone_name   = replace(local.nonprod_domain_name, ".", "-")

  # CT dev subdomain
  dev_dns_name    = "dev.${local.nonprod_dns_name}"
  dev_domain_name = trim(local.dev_dns_name, ".")
  dev_zone_name   = replace(local.dev_domain_name, ".", "-")

  # CT stage subdomain
  stage_dns_name    = "stage.${local.nonprod_dns_name}"
  stage_domain_name = trim(local.stage_dns_name, ".")
  stage_zone_name   = replace(local.stage_domain_name, ".", "-")
}

# create CT nonprod as a subdomain of the root Dematic nonprod zone
module "nonprod" {
  source     = "./subdomain"
  project_id = var.project_id

  common_resource_labels = var.common_resource_labels
  ns_record_ttl          = var.domain_config.ns_record_ttl
  parent_zone_name       = data.google_dns_managed_zone.nonprod_root.name
  parent_zone_project_id = data.google_dns_managed_zone.nonprod_root.project
  subdomain_prefix       = var.domain_config.subdomain_prefix
}

# create dev as a subdomain of non-prod
module "dev" {
  source     = "./subdomain"
  project_id = var.project_id

  common_resource_labels = var.common_resource_labels
  ns_record_ttl          = var.domain_config.ns_record_ttl
  parent_zone_name       = module.nonprod.zone_name
  parent_zone_project_id = module.nonprod.project_id
  subdomain_prefix       = "dev"

  depends_on = [module.nonprod]
}

# create stage as a subdomain of non-prod
module "stage" {
  source     = "./subdomain"
  project_id = var.project_id

  common_resource_labels = var.common_resource_labels
  ns_record_ttl          = var.domain_config.ns_record_ttl
  parent_zone_name       = module.nonprod.zone_name
  parent_zone_project_id = module.nonprod.project_id
  subdomain_prefix       = "stage"

  depends_on = [module.nonprod]
}

# =============================================================================
# Prod DNS zone
# =============================================================================

# reference to the root Dematic DNS managed zone used for prod
data "google_dns_managed_zone" "prod_root" {
  project = var.domain_config.prod_root_zone.project_id
  name    = var.domain_config.prod_root_zone.name
}

locals {
  # Dematic prod root zone
  prod_root_dns_name  = data.google_dns_managed_zone.prod_root.dns_name
  prod_root_zone_name = data.google_dns_managed_zone.prod_root.name

  # CT prod subdomain
  prod_dns_name    = "${var.domain_config.subdomain_prefix}.${local.prod_root_dns_name}"
  prod_domain_name = trim(local.prod_dns_name, ".")
  prod_root_name   = replace(local.prod_domain_name, ".", "-")
}

# create CT prod as a subdomain of the root Dematic prod zone
module "prod" {
  source     = "./subdomain"
  project_id = var.project_id

  common_resource_labels = var.common_resource_labels
  ns_record_ttl          = var.domain_config.ns_record_ttl
  parent_zone_name       = data.google_dns_managed_zone.prod.name
  parent_zone_project_id = data.google_dns_managed_zone.prod.project
  subdomain_prefix       = var.domain_config.subdomain_prefix
}
