# Common Services Terraform Module

## Table of Contents

- [Description](#description)
- [Components](#components)
- [DNS Management](#dns-management)
- [Artifact Registry](#artifact-registry)
- [GitLab Integration](#gitlab-integration)
- [Usage](#usage)
- [Requirements](#requirements)
- [Providers](#providers)
- [Modules](#modules)
- [Resources](#resources)
- [Inputs](#inputs)
- [Outputs](#outputs)
- [Terraform TFVARS file examples](#terraform-tfvars-file-examples)

## Description

This module creates and manages the foundational services that are shared across all Control Tower environments. It establishes the core infrastructure components required for secure, scalable deployment of Control Tower applications.

The module provisions:

- **DNS Management**: Managed zones for prod and non-prod environments
- **Artifact Registry Repositories**: Secure storage for Docker images, npm packages, Python packages, and generic artifacts
- **GitLab Integration**: Service account permissions for CI/CD pipelines

## Components

### DNS Management

The module creates managed DNS zones that serve as the parent zones for all Control Tower environments:

- **Non-Production Zone**: Hosts development and staging environments
- **Production Zone**: Hosts production environments  
- **Delegation Records**: NS records in parent Dematic zones to delegate subdomains

**DNS Hierarchy Example**:
```
dematic.dev (parent zone)
└── ict.dematic.dev (Control Tower non-prod zone)
    ├── dev-us1.ict.dematic.dev
    └── stage-us1.ict.dematic.dev

dematic.com (parent zone)
└── ict.dematic.com (Control Tower prod zone)
    └── prod-us1.ict.dematic.com
```

### Artifact Registry

Multiple repositories are created to support different artifact types:

- **Docker Repository**: Container images for all services
- **Generic Repository**: Static assets and miscellaneous artifacts

All repositories include:
- Vulnerability scanning
- Access controls
- Lifecycle management policies

### GitLab Integration

Configures service account permissions for CI/CD operations:

- **Build Permissions**: Access to Artifact Registry for pushing images
- **Deployment Permissions**: Access to deploy applications

## DNS Management

The DNS configuration creates a hierarchical structure that supports:

1. **Environment Isolation**: Separate subdomains for each environment
2. **Regional Deployment**: Support for multi-region deployments  
3. **Service Discovery**: Consistent naming for internal services
4. **Certificate Management**: Automated SSL certificate provisioning

**Zone Structure**:
- **Root Integration**: Connects to existing Dematic DNS infrastructure
- **Subdomain Delegation**: Allows Control Tower to manage its own DNS
- **Record Management**: Supports dynamic record creation for services

## Artifact Registry

The Artifact Registry setup provides:

1. **Multi-Format Support**: Different repositories for different artifact types
2. **Security Scanning**: Automatic vulnerability detection
3. **Access Control**: IAM-based permissions for repositories
4. **Regional Replication**: Multi-region availability for performance

**Repository Types**:
- **Docker**: All application container images
- **Generic**: Static assets, configuration files, documentation

## GitLab Integration

The GitLab service account configuration provides:

1. **Secure Access**: Minimal necessary permissions for CI/CD
2. **Role Separation**: Different permissions for different pipeline stages
3. **Audit Trail**: Complete record of CI/CD operations
4. **Scalability**: Support for multiple concurrent pipelines

**Permission Scopes**:
- **Artifact Registry**: Push/pull access for build artifacts
- **Cloud Run**: Deploy applications to Cloud Run
