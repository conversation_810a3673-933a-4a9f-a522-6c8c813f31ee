# TODO: add clean-up policies

resource "google_artifact_registry_repository" "docker" {
  project       = var.project_id
  repository_id = "docker-images"
  location      = var.artifact_registry_configuration.location
  format        = "docker"
  labels        = var.common_resource_labels

  vulnerability_scanning_config {
    enablement_config = "INHERITED"
  }
}

resource "google_artifact_registry_repository" "generic" {
  project       = var.project_id
  repository_id = "generic-artifacts"
  location      = var.artifact_registry_configuration.location
  format        = "generic"
  labels        = var.common_resource_labels
}

## Optional repositories

resource "google_artifact_registry_repository" "npm" {
  count = var.artifact_registry_configuration.enable_npm ? 1 : 0

  project       = var.project_id
  repository_id = "npm-packages"
  location      = var.artifact_registry_configuration.location
  format        = "npm"
  labels        = var.common_resource_labels
}

resource "google_artifact_registry_repository" "python" {
  count = var.artifact_registry_configuration.enable_python ? 1 : 0

  project       = var.project_id
  repository_id = "python-packages"
  location      = var.artifact_registry_configuration.location
  format        = "python"
  labels        = var.common_resource_labels
}

resource "google_project_iam_member" "gitlab_runner_permissions" {
  project  = var.project_id
  for_each = toset(var.gitlab_runner_permissions.roles)

  role   = each.value
  member = var.gitlab_runner_permissions.service_account
}
