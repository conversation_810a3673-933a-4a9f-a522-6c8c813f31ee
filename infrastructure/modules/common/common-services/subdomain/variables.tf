variable "common_resource_labels" {
  description = "The common labels that should be applied to all resources that can be labeled."
  type        = map(string)
}

variable "ns_record_ttl" {
  description = "The TTL for the NS records that are created for subdomain delegation."
  type        = number
}

variable "parent_zone_name" {
  description = "The name of the parent DNS managed zone."
  type        = string
}

variable "parent_zone_project_id" {
  description = "The GCP project ID where the parent DNS managed zone is located."
  type        = string
}

variable "project_id" {
  description = "The ID of the shared services GCP project to deploy to."
  type        = string
}

variable "subdomain_prefix" {
  description = <<EOT
    The prefix to use for the subdomain. This will be prepended to the parent DNS name to create 
    the desired subdomain DNS managed zone.
  EOT

  type = string
}
