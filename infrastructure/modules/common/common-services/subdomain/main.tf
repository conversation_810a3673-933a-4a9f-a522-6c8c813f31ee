# reference to the parent DNS managed zone
data "google_dns_managed_zone" "parent" {
  project = var.parent_zone_project_id
  name    = var.parent_zone_name
}

locals {
  parent_dns_name  = data.google_dns_managed_zone.parent.dns_name
  parent_zone_name = data.google_dns_managed_zone.parent.name

  subdomain_dns_name  = "${var.subdomain_prefix}.${local.parent_dns_name}"
  subdomain_name      = trim(local.subdomain_dns_name, ".")
  subdomain_root_name = replace(local.subdomain_name, ".", "-")
}

# create the DNS managed zone
resource "google_dns_managed_zone" "main" {
  project = var.project_id

  name        = local.subdomain_root_name
  description = "DNS zone for the production environment."

  dns_name   = local.subdomain_dns_name
  labels     = var.common_resource_labels
  visibility = "public"
}

# delegate the subdomain from the parent zone to the subdomain zone
resource "google_dns_record_set" "parent_ns" {
  project = data.google_dns_managed_zone.parent.project

  name = google_dns_managed_zone.main.dns_name
  type = "NS"
  ttl  = var.ns_record_ttl

  managed_zone = data.google_dns_managed_zone.parent.name
  rrdatas      = google_dns_managed_zone.main.name_servers
}
