output "attestor" {
  description = <<EOT
    The attestor configuration.

    Outputs:
    - attestor_name: The name of the attestor.
    - kms_key_ring_id: The ID of the KMS key ring used by the attestor.
  EOT

  value = {
    attestor_name   = google_binary_authorization_attestor.attestor.name
    kms_key_ring_id = google_kms_key_ring.attestor.id
  }
}

output "dev_dns_zone" {
  description = "The name and project ID of the dev DNS managed zone."
  value       = module.dev
}

output "docker_repository" {
  description = <<EOT
    The Docker repository configuration.

    Outputs:
    - project: The ID of the project where the Docker repository is located.
    - location: The location of the Artifact Registry repository. E.g., `us`, `us-central1`.
    - repository: The name of the Docker repository.
  EOT

  value = {
    project    = var.project_id
    location   = var.artifact_registry_configuration.location
    repository = google_artifact_registry_repository.docker.name
  }
}

output "generic_repository" {
  description = <<EOT
    The generic repository configuration.

    Outputs:
    - project: The ID of the project where the generic repository is located.
    - location: The location of the Artifact Registry repository. E.g., `us`, `us-central1`.
    - repository: The name of the generic repository.
  EOT

  value = {
    project    = var.project_id
    location   = var.artifact_registry_configuration.location
    repository = google_artifact_registry_repository.generic.name
  }
}

output "nonprod_dns_zone" {
  description = "The name and project ID of the nonprod DNS managed zone."
  value       = module.nonprod
}

output "npm_repository" {
  description = <<EOT
    The npm repository configuration. Null of the npm repository is not enabled.

    Outputs:
    - project: The ID of the project where the npm repository is located.
    - location: The location of the Artifact Registry repository. E.g., `us`, `us-central1`.
    - repository: The name of the npm repository, if enabled.
  EOT

  value = var.artifact_registry_configuration.enable_npm ? {
    project    = var.project_id
    location   = var.artifact_registry_configuration.location
    repository = google_artifact_registry_repository.npm[0].name
  } : null
}

output "prod_domain" {
  description = <<EOT
    The domain for the prod landing zone. This will serve as the root domain for all prod
    environments.
  EOT

  value = {
    dns_zone_name       = google_dns_managed_zone.prod.name
    dns_zone_project_id = google_dns_managed_zone.prod.project
  }
}

output "python_repository" {
  description = <<EOT
    The Python repository configuration. Null of the Python repository is not enabled.

    Outputs:
    - project: The ID of the project where the Python repository is located.
    - location: The location of the Artifact Registry repository. E.g., `us`, `us-central1`.
    - repository: The name of the Python repository, if enabled.
  EOT

  value = var.artifact_registry_configuration.enable_python ? {
    project    = var.project_id
    location   = var.artifact_registry_configuration.location
    repository = google_artifact_registry_repository.python[0].name
  } : null
}

output "stage_dns_zone" {
  description = "The name and project ID of the stage DNS managed zone."
  value       = module.stage
}
