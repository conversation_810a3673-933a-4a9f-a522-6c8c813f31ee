variable "artifact_registry_configuration" {
  description = <<EOT
    The configuration info for setting up the Artifact Registry repositories. <PERSON><PERSON> and the 
    generic site assets repositories are always enabled.

    Variables:
    - location: The location to deploy the Artifact Registry repositories. Examples: 'us', 
        'us-central1'.
    - enable_npm: Whether or not to enable the npm registry.
    - enable_python: Whether or not to enable the Python registry.
  EOT

  type = object({
    location      = string
    enable_npm    = bool
    enable_python = bool
  })
}

variable "common_resource_labels" {
  description = "The common labels that should be applied to all resources that can be labeled."
  type        = map(string)
}

variable "domain_config" {
  description = <<EOT
    The configuration for the non-prod and prod domains for CT. This will be used to create 
    two subdomains of the Dematic non-prod and prod root domains, that will serve as the parent 
    zones for CT environments.
    
    This includes:
    - `ns_record_ttl`: The TTL for the NS records used to delegate the non-prod and prod subdomains 
        from the parent zones to the CT non-prod and prod zones.
    - `subdomain_prefix`: The prefix to use for the subdomains non-prod and prod subdomains that 
        will serve as the root domains for CT.
    - `nonprod_root_zone`: The name and project ID of the root Dematic non-prod DNS managed zone.
    - `prod_root_zone`: The name and project ID of the root Dematic prod DNS managed zone.
  EOT

  type = object({
    ns_record_ttl    = number
    subdomain_prefix = string

    nonprod_root_zone = object({
      name       = string
      project_id = string
    })

    prod_root_zone = object({
      name       = string
      project_id = string
    })
  })
}

variable "gitlab_runner_permissions" {
  description = "The GitLab Runner service account, and the roles that should be granted to it."
  type = object({
    service_account = string
    roles           = list(string)
  })
}

variable "naming_root" {
  description = <<EOT
    The root identifier for all Control Tower resources. This value will be combined with the
    `environment` to create a unique prefix for any resources requiring a globally-unique name. 
    This value is intended to be shared across all Control Tower deployments. As of module 
    creation, this value is "ict".
  EOT

  type = string
}

variable "project_id" {
  description = "The ID of the shared services GCP project to deploy to."
  type        = string
}
