module "postgresql" {
  source  = "gitlab.com/dematic/control-tower-postgresql/google"
  version = "~> 0.1.0"

  # Required variables
  access_logs_bucket           = var.access_logs_bucket
  artifact_registry_repository = var.artifact_registry_repository
  bucket_location              = var.bucket_location
  common_resource_labels       = var.common_resource_labels
  database_username            = var.database_username
  databases                    = var.databases
  enforce_unique_naming        = var.enforce_unique_naming
  environment                  = var.environment
  environment_variables        = var.environment_variables
  instance_prefix              = var.instance_prefix
  migration_cloud_run_config   = var.migration_cloud_run_config
  migration_container_image    = var.migration_container_image
  naming_root                  = var.naming_root
  network_config               = var.network_config
  project_id                   = var.project_id
  region                       = var.region
  vpc_access_connector_config  = var.vpc_access_connector_config
  zone                         = var.zone
}