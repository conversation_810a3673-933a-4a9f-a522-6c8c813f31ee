module "site-asset-backend" {
  source  = "gitlab.com/dematic/control-tower-site-asset-backend/google"
  version = "~> 0.1.0"

  # Required variables
  access_logs_bucket     = var.access_logs_bucket
  app_artifact_folder    = var.app_artifact_folder
  bucket_location        = var.bucket_location
  common_resource_labels = var.common_resource_labels
  cors_configs           = var.cors_configs
  enforce_unique_naming  = var.enforce_unique_naming
  instance_prefix        = var.instance_prefix
  naming_root            = var.naming_root
  project_id             = var.project_id
  website_config         = var.website_config
}