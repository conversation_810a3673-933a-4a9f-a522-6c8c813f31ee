module "metric-processor" {
  source  = "gitlab.com/dematic/control-tower-metric-processor/google"
  version = "~> 0.1.0"

  # Required variables
  artifact_registry_repository = var.artifact_registry_repository
  cloud_run_config             = var.cloud_run_config
  common_resource_labels       = var.common_resource_labels
  container_image              = var.container_image
  edp_project_id               = var.edp_project_id
  enforce_unique_naming        = var.enforce_unique_naming
  environment                  = var.environment
  environment_variables        = var.environment_variables
  naming_root                  = var.naming_root
  network_config               = var.network_config
  project_id                   = var.project_id
  region                       = var.region
  required_roles               = var.required_roles
  vpc_access_connector_config  = var.vpc_access_connector_config
  zone                         = var.zone
}