module "ignition-proxy" {
  source  = "gitlab.com/dematic/control-tower-ignition-proxy/google"
  version = "~> 0.1.0"

  # Required variables
  bucket_location        = var.bucket_location
  common_resource_labels = var.common_resource_labels
  enforce_unique_naming  = var.enforce_unique_naming
  host_config            = var.host_config
  host_labels            = var.host_labels
  host_tags              = var.host_tags
  ignition_port          = var.ignition_port
  ignition_upstream      = var.ignition_upstream
  instance_prefix        = var.instance_prefix
  naming_root            = var.naming_root
  network_config         = var.network_config
  project_id             = var.project_id
  region                 = var.region
}