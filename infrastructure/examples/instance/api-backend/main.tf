module "api-backend" {
  source  = "gitlab.com/dematic/control-tower-api-backend/google"
  version = "~> 0.1.0"

  # Required variables
  backend_services                 = var.backend_services
  cloud_armor_config               = var.cloud_armor_config
  common_config                    = var.common_config
  common_resource_labels           = var.common_resource_labels
  default_config                   = var.default_config
  default_service_name             = var.default_service_name
  enforce_unique_naming            = var.enforce_unique_naming
  instance_display_name            = var.instance_display_name
  instance_prefix                  = var.instance_prefix
  load_balancing_scheme            = var.load_balancing_scheme
  monitoring_notification_email    = var.monitoring_notification_email
  naming_root                      = var.naming_root
  network_config                   = var.network_config
  project_id                       = var.project_id
  region                           = var.region
  storage_enabled_backend_services = var.storage_enabled_backend_services
  vpc_access_connector_config      = var.vpc_access_connector_config
}