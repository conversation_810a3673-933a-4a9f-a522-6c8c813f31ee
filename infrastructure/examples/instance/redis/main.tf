module "redis" {
  source  = "gitlab.com/dematic/control-tower-redis/google"
  version = "~> 0.1.0"

  # Required variables
  alternate_zone         = var.alternate_zone
  common_resource_labels = var.common_resource_labels
  enforce_unique_naming  = var.enforce_unique_naming
  instance_prefix        = var.instance_prefix
  network_self_link      = var.network_self_link
  project_id             = var.project_id
  region                 = var.region
  zone                   = var.zone

  # Optional variables
  cache_name = "redis"
}