const fs = require("fs");

/**
 # This hook is used by husky during pre-commit to validate the git commit
 # message. It follows a modified conventional-commit format with the addition
 # of a JIRA id (ICT-#) for the commit types feat, fix, test.
 # Detailed info: https://wiki.dematic.net/display/ICTI/Git+Commits+using+Conventional+Commits
 */

// Fetch the commit message
const commitMessagePath = process.argv[2] || ".git/COMMIT_EDITMSG";
const commitMessage = fs.readFileSync(commitMessagePath, "utf-8").trim();

// Validate against the ICT conventional-commit format
function validateCommitMessage(message) {
  // Allow any commits starting with "Merge"
  if (message && message.startsWith("Merge")) {
    return true;
  }

  // Allow conventional commits with support for ICT-# and multi-line commits
  const requiredPattern = new RegExp(
    "^(?:feat|fix|test)(?:\\([a-zA-Z0-9_-]+\\))?!?: ICT-[1-9][0-9]{0,4} [\\s\\S]+$|" +
      "^(?:docs|style|refactor|perf|build|ci|chore|revert)(?:\\([a-zA-Z0-9_-]+\\))?!?: [\\s\\S]+$",
    "m",
  );
  return requiredPattern.test(message);
}

// Validate the commit message
if (!validateCommitMessage(commitMessage)) {
  console.error(`
  🚨 Invalid commit message 🚨

  The commit message must follow conventional-commit format
  (https://wiki.dematic.net/display/ICTI/Git+Commits+using+Conventional+Commits):
  For feat, fix, test:
     <type>(<scope>): ICT-# <message>
  For docs, style, refactor, perf, build, ci, chore, revert:
     <type>(<scope>): <message>
  Your commit message was:
     ${commitMessage}`);
  process.exit(1);
}
