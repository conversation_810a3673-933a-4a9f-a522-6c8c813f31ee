#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Run lint-staged for all projects
yarn lint-staged

# Run project specific pre-commit scripts for all projects in monorepo
for precommit in $(find services -name "pre-commit.sh" -type f); do
    if [ -x "$precommit" ]; then
        # Get the directory containing the precommit hook
        precommit_dir=$(dirname "$precommit")

        # Check if there are any changes in this directory
        if git diff --cached --name-only | grep -q "^${precommit_dir}/"; then
            cd ${precommit_dir}
            echo "Executing pre-commit.sh"
            ./pre-commit.sh
        else
            echo "No changes in ${precommit_dir}, skipping..."
            continue
        fi
    else
        echo "Warning: pre-commit.sh in ${precommit_dir} does not have execute permissions"
    fi
done
