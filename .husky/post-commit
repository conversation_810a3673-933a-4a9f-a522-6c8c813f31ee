#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Post-commit hook to remind developers about SDK regeneration
# This runs after a successful commit to provide helpful feedback

echo "✅ Commit successful!"

# Check if API changes were committed and SDK might need attention
if git diff --name-only HEAD~1 HEAD | grep -E 'services/api.*(controllers?|routes?|api|tsoa\.json|\.ya?ml)'; then
    echo ""
    echo "📡 API changes detected in this commit."
    echo "The pre-commit hook should have automatically regenerated the SDK."
    echo "If you're working in the UI project, you may need to update your dependencies:"
    echo "  cd services/ui && yarn install"
    echo ""
fi
