# Terraform Documentation Templates

This directory contains template files for generating consistent Terraform module documentation. These templates are used by the `generate-terraform-docs.sh` script to automatically create the necessary configuration files for new modules.

## Template Files

### `.terraform-docs.yml.template`

Main configuration file for terraform-docs that generates the module README.md file. It includes all standard sections and uses a template header.

**Placeholders:**

- `{{EXAMPLE_PATH}}` - Replaced with the relative path to the example file

### `.terraform-example.yml.template`

Configuration file for generating example usage files. Creates a `main.tf` file in the examples directory with proper module usage.

**Placeholders:**

- `{{MODULE_NAME}}` - Replaced with the module name (last directory name)
- `{{EXAMPLE_OUTPUT_PATH}}` - Replaced with the relative path to the output example file

### `.tfvars-docs.yml.template`

Configuration file for generating tfvars documentation. Adds variable examples to the README.md file with proper indentation in the `inputs { }` block.

**Placeholders:**

- None (this file is generic)

### `templates/header.md.template`

Template for the module header documentation. Contains standard sections and placeholders for module-specific content.

**Placeholders:**

- `{{MODULE_TITLE}}` - Replaced with a title-case version of the module name
- `{{MODULE_NAME}}` - Replaced with the module name

## Script Processing Phases

The `generate-terraform-docs.sh` script now operates in three distinct phases to eliminate duplication and provide cleaner output:

### Phase 1: Module Processing

- Discovers all modules in `infrastructure/modules/*`
- Processes templates for each module (replaces placeholders)
- Updates `.terraform-docs.yml`, `.terraform-example.yml`, and `.tfvars-docs.yml`
- Preserves existing `templates/header.md` files (custom documentation)
- Exports processed module list for subsequent phases

### Phase 2: Example Generation

- Generates example files using the processed module list
- Creates `main.tf` files in `infrastructure/examples/*`
- Applies Terraform formatting
- Provides success/failure summary

### Phase 3: Documentation Generation

- Generates README.md files for all modules
- Processes tfvars documentation with proper indentation
- Provides success/failure summary

## Usage

1. Copy the template files from this directory to a new module directory
2. Run `generate-terraform-docs.sh` to process the templates and replace placeholders
3. Edit the generated `templates/header.md` file to add module-specific documentation

## Placeholder Replacement

The `generate-terraform-docs.sh` script automatically replaces the following placeholders:

| Placeholder | Description | Example |
|-------------|-------------|---------|
| `{{MODULE_NAME}}` | Last directory name | `common-services` |
| `{{MODULE_TITLE}}` | Title-case module name | `Common Services` |
| `{{EXAMPLE_PATH}}` | Relative path to example | `../../../examples/common/common-services/main.tf` |
| `{{EXAMPLE_OUTPUT_PATH}}` | Relative path for example output | `../../../examples/common/common-services/main.tf` |

## Module Directory Structure

After copying and processing templates, a module should have this structure:

```bash
infrastructure/modules/category/module-name/
├── .terraform-docs.yml
├── .terraform-example.yml
├── .tfvars-docs.yml
├── templates/
│   └── header.md
├── main.tf
├── variables.tf
├── outputs.tf
└── README.md (generated)
```

And the corresponding example:

```bash
infrastructure/examples/category/module-name/
└── main.tf (generated)
```

## Recent Improvements

### Eliminated Duplication

- **Before**: Templates were processed twice (once for examples, once for docs)
- **After**: Single template processing phase with shared module list

### Cleaner Output

- **Before**: Noisy file paths and duplicate logging
- **After**: Suppressed terraform-docs output with focused success/failure summaries

### Better Performance

- **Before**: Multiple file system scans and redundant processing
- **After**: Single module discovery with efficient array-based processing

### Enhanced tfvars Documentation

- **Before**: Content was reformatted by terraform-docs
- **After**: Proper indentation preserved in `inputs { }` blocks using post-processing
