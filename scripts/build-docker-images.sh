#!/usr/bin/env bash

set -e

# ================================
# Variables
# ================================

TAG=$(git rev-parse HEAD)

# Application
PROJECT_ID="${2:-ict-d-api}"

# Artifact Registry
REPOSITORY_LOCATION="us-docker.pkg.dev"
REPOSITORY="${1:-us-docker.pkg.dev/ict-o-common-services-kfqd/docker-images}"

# Attestation key
KEYRING_PROJECT_ID="${3:-${PROJECT_ID}}"
KEYRING_LOCATION="global"
KEYRING_ID="${KEYRING_PROJECT_ID}-attestor-key-ring"
KEY_ID="${KEYRING_PROJECT_ID}-attestor-key"
KEY_VERSION="1"

BASE_IMAGES=(
    "ict-base-shared"
    "ict-base-distroless"
    "ict-base-full"
)

SERVICE_APPS=(
    "ict-admin-api"
    "ict-ai-api"
    "ict-availability-api"
    "ict-config-api"
    "ict-data-api"
    "ict-dataexplorer-api"
    "ict-diagnostics-api"
    "ict-equipment-api"
    "ict-inventory-api"
    "ict-movement-api"
    "ict-operators-api"
    "ict-orders-api"
    "ict-simulation-api"
    "ict-tableau-management-api"
    "ict-tableau-proxy"
    "ict-typeorm-migrations-cr"
    "ict-workstation-api"
    "mock-data-api"
)

# ARTIFACTORY_AUTH_TOKEN is expected to be set in the environment
if [[ -z "${ARTIFACTORY_AUTH_TOKEN}" ]]; then
    echo "missing ARTIFACTORY_AUTH_TOKEN"
    exit 1
fi

# ================================
# Functions
# ================================

get_repo_root() {
    echo "$(git rev-parse --show-toplevel)"
}

docker_log_in() {
    gcloud auth configure-docker ${REPOSITORY_LOCATION}
    docker-credential-gcloud gcr-login
}

build_tag_push() {
    local image="$1"
    local docker_file="${2:-./Dockerfile}"
    local attest="$3"

    if [[ $GITLAB_CI ]]; then
        echo -e "section_start:$(date +%s):docker-${image}[collapsed=true]\r\e[0Kbuild image for ${image}"
    fi

    digest=$(docker build --platform linux/amd64 -t ${REPOSITORY}/${image}:${TAG} -f ${docker_file} --build-arg artifactory_token=${ARTIFACTORY_AUTH_TOKEN} --build-arg docker=${image} .)

    echo "Pushing image: ${REPOSITORY}/${image}:${TAG}"
    docker push ${REPOSITORY}/${image}:${TAG}

    # Get the digest from the gcloud command to ensure it's the same as the docker build.
    # This is the digest that will be used for attestation.
    # digest=$(gcloud artifacts docker images describe ${REPOSITORY}/${image}:${TAG} --format='value(image_summary.digest)') # TODO: after the attestation is fixed, reimplement this line
    artifact_url="${REPOSITORY}/${image}@${digest}"

    if [[ "${attest}" == "attest" ]]; then
        attest_image ${image} ${REPOSITORY} ${KEY_VERSION} ${digest}
    fi

    if [ ! -z "${RELEASE_TAG}" ]; then
        docker tag ${REPOSITORY}/${image}:${TAG} ${REPOSITORY}/${image}:${RELEASE_TAG}
        docker push ${REPOSITORY}/${image}:${RELEASE_TAG}
    fi
    if [[ ! -z "${GITLAB_CI}" ]]; then
        echo -e "section_end:$(date +%s):docker-${image}\r\e[0K"
    fi
}

attest_image() {
    local image="$1"
    local repository="${2:-${REPOSITORY}}"
    local key_version="${3:-1}"
    local digest="$4"
    local artifact_url="${repository}/${image}@${digest}"

    attestor=$(gcloud container binauthz attestors describe ${KEYRING_PROJECT_ID}-attestor --project=${KEYRING_PROJECT_ID} --format='value(name)')

    EXISTING=$(gcloud container binauthz attestations list --project="${PROJECT_ID}" \
        --artifact-url="${artifact_url}" \
        --attestor="${attestor}" \
        --format="value(name)")

    if [ ! -z "${EXISTING}" ]; then
        echo "Image is already attested... skipping attestation"
        echo "existing attestation: ${EXISTING}"
        return 0
    fi

    echo "**************************************************************"
    echo " Attesting image: ${image}"
    echo " Digest: ${digest}"
    echo " Attestor: ${attestor}"
    echo " Key version: ${key_version}"
    echo " Project ID: ${PROJECT_ID}"
    echo " Artifact URL: ${artifact_url}"
    echo
    echo -e " gcloud beta container binauthz attestations sign-and-create \\\\\\n\
    --project=\"${PROJECT_ID}\" \\\\\\n\
    --artifact-url=\"${artifact_url}\" \\\\\\n\
    --attestor=\"${attestor}\" \\\\\\n\
    --keyversion-project=\"${KEYRING_PROJECT_ID}\" \\\\\\n\
    --keyversion-location=\"global\" \\\\\\n\
    --keyversion-keyring=\"${KEYRING_PROJECT_ID}-attestor-key-ring\" \\\\\\n\
    --keyversion-key=\"${KEYRING_PROJECT_ID}-attestor-key\" \\\\\\n\
    --keyversion=${key_version}"
    echo

    gcloud beta container binauthz attestations sign-and-create \
        --project="${PROJECT_ID}" \
        --artifact-url="${artifact_url}" \
        --attestor="${attestor}" \
        --keyversion-project="${KEYRING_PROJECT_ID}" \
        --keyversion-location="global" \
        --keyversion-keyring="${KEYRING_PROJECT_ID}-attestor-key-ring" \
        --keyversion-key="${KEYRING_PROJECT_ID}-attestor-key" \
        --keyversion=${key_version}

    EXISTING=$(gcloud container binauthz attestations list --project="${PROJECT_ID}" \
        --artifact-url="${artifact_url}" \
        --attestor="${attestor}" \
        --format="value(name)")

    echo " New attestation: ${EXISTING}"
    echo "**************************************************************"
}

# ================================
# Build docker images
# ================================

docker_log_in

pushd "$(get_repo_root)/services/api/"

# Build foundation images
for image in "${BASE_IMAGES[@]}"; do
    build_tag_push ${image} ./libs/api/${image}/Dockerfile
    docker tag ${REPOSITORY}/${image}:${TAG} ${image}
done

# Build service images
for image in "${SERVICE_APPS[@]}"; do
    # build_tag_push ${image} ./apps/api/${image}/Dockerfile "attest" # TODO: after the attestation is fixed, reimplement this line
    build_tag_push ${image} ./apps/api/${image}/Dockerfile
done
