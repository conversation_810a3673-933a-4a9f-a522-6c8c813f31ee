#! /usr/bin/env bash

source $(git rev-parse --show-toplevel)/scripts/utils.sh
REPO_ROOT=$(git rev-parse --show-toplevel)

log_info "=================================================================================="
log_info "Running terragrunt before hook: $0"

log_info "Formatting HCL"

pushd $REPO_ROOT
terraform fmt --recursive
popd

pushd $REPO_ROOT/environments
terragrunt hcl format --all

log_info "Validating inputs"
terragrunt hcl validate --inputs --all
popd


log_info "Done: $0"
log_info "=================================================================================="
