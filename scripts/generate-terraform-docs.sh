#!/usr/bin/env bash

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Load utils functions
source "${SCRIPT_DIR}/utils.sh"

# ================================
# Global variables
# ================================
REPO_ROOT=$(get_repo_root) # Get git repository root directory
modules_dir="${REPO_ROOT}/infrastructure/modules"

# ================================
# Functions
# ================================

# Function to indent all lines between 'inputs {' and the next closing '}' in README.md
indent_inputs_block() {
    local readme_file="$1"
    awk '
    /inputs \{/ {print; inblock=1; next}
    inblock && /\}/ {inblock=0; print; next}
    inblock {print "  " $0; next}
    {print}
    ' "$readme_file" > "${readme_file}.tmp" && mv "${readme_file}.tmp" "$readme_file"
}

# Function to copy and process template files for a module
copy_and_process_templates() {
    local module_dir="$1"
    local templates_dir="${SCRIPT_DIR}/terraform-docs-templates"
    
    if [ ! -d "$templates_dir" ]; then
        log_error "Templates directory not found: $templates_dir"
        return 1
    fi
    
    log_info "Processing templates for module: $module_dir"
    
    # Extract module path components
    # e.g., infrastructure/modules/instance/ui-load-balancer -> category=instance, module_name=ui-load-balancer
    local relative_path="${module_dir#$modules_dir/}"
    local category=$(echo "$relative_path" | awk -F'/' '{print $1}')
    local module_name=$(basename "$module_dir")
    
    # Generate module title (convert kebab-case to Title Case)
    local module_title=$(echo "$module_name" | sed 's/-/ /g' | sed 's/\b\w/\U&/g')
    
    # Calculate paths
    local example_path="../../../examples/$category/$module_name/main.tf"
    local example_output_path="../../../examples/$category/$module_name/main.tf"
    local module_source="gitlab.com/dematic/control-tower-$module_name/google"
    
    log_info "  Category: $category"
    log_info "  Module name: $module_name"
    log_info "  Module title: $module_title"
    log_info "  Example path: $example_path"
    
    # Always copy and process .terraform-docs.yml to ensure template changes are propagated
    log_info "  Updating .terraform-docs.yml"
    sed -e "s|{{EXAMPLE_PATH}}|$example_path|g" \
        "$templates_dir/.terraform-docs.yml.template" > "$module_dir/.terraform-docs.yml"
    
    # Always copy and process .terraform-example.yml to ensure template changes are propagated
    log_info "  Updating .terraform-example.yml"
    sed -e "s|{{MODULE_NAME}}|$module_name|g" \
        -e "s|{{EXAMPLE_OUTPUT_PATH}}|$example_output_path|g" \
        "$templates_dir/.terraform-example.yml.template" > "$module_dir/.terraform-example.yml"
    
    # Always copy and process .tfvars-docs.yml to ensure template changes are propagated
    log_info "  Updating .tfvars-docs.yml"
    cp "$templates_dir/.tfvars-docs.yml.template" "$module_dir/.tfvars-docs.yml"
    
    # Create templates directory if it doesn't exist
    if [ ! -d "$module_dir/templates" ]; then
        log_info "  Creating templates directory"
        mkdir -p "$module_dir/templates"
    fi
    
    # Only create header.md if it doesn't exist (preserve custom documentation)
    if [ ! -f "$module_dir/templates/header.md" ]; then
        log_info "  Creating templates/header.md"
        sed -e "s|{{MODULE_NAME}}|$module_name|g" \
            -e "s|{{MODULE_TITLE}}|$module_title|g" \
            "$templates_dir/templates/header.md.template" > "$module_dir/templates/header.md"
    else
        log_info "  Preserving existing templates/header.md (custom documentation)"
    fi
    
    log_success "  Templates processed for $module_name"
}

# Function to copy template files to a specific module directory
copy_templates_to_module() {
    local target_module_dir="$1"
    
    if [ -z "$target_module_dir" ]; then
        log_error "Usage: copy_templates_to_module <module_directory>"
        return 1
    fi
    
    if [ ! -d "$target_module_dir" ]; then
        log_error "Module directory does not exist: $target_module_dir"
        return 1
    fi
    
    log_info "Copying template files to: $target_module_dir"
    copy_and_process_templates "$target_module_dir"
    
    log_success "Template files copied successfully!"
    log_info "Next steps:"
    log_info "1. Edit $target_module_dir/templates/header.md to add module-specific documentation"
    log_info "2. Run ./generate-terraform-docs.sh to generate documentation"
}

get_repo_root() {
    git rev-parse --show-toplevel
}

show_help() {
    log_heredoc "${TXT_GREEN}" <<EOF
Usage: generate-terraform-docs.sh [OPTIONS] [MODULE_DIR]

${TXT_CLEAR}This script generates Terraform documentation using terraform-docs and automatically
creates template files for new modules.

${TXT_YELLOW}Actions:${TXT_CLEAR}
  1. Formats terraform files using 'terraform fmt' or 'tofu fmt'
  2. Generates terraform documentation using terraform-docs
  3. Creates template files for modules missing configuration
  4. Generates example files for all modules

${TXT_YELLOW}Process:${TXT_CLEAR}
  - Automatically creates .terraform-docs.yml, .terraform-example.yml, .tfvars-docs.yml,
    and templates/header.md for modules that don't have them
  - Replaces placeholders in templates with module-specific values
  - Generates documentation for all modules in infrastructure/modules/*
  - Creates example files in infrastructure/examples/*

${TXT_YELLOW}Requirements:${TXT_CLEAR}
  • terraform-docs must be installed
  • terraform or OpenTofu must be installed

${TXT_YELLOW}Options:${TXT_CLEAR}
    -h, --help              Show this help message
    --copy-templates DIR    Copy template files to specific module directory

${TXT_YELLOW}Examples:${TXT_CLEAR}
    ./generate-terraform-docs.sh
        # Generate docs for all modules

    ./generate-terraform-docs.sh --copy-templates infrastructure/modules/common/my-module
        # Copy template files to a specific module

${TXT_YELLOW}Template Placeholders:${TXT_CLEAR}
  {{MODULE_NAME}}       - Module name (e.g., "common-services")
  {{MODULE_TITLE}}      - Title case module name (e.g., "Common Services")
  {{MODULE_SOURCE}}     - GitLab module source (e.g., "gitlab.com/dematic/control-tower-common-services/google")
  {{EXAMPLE_PATH}}      - Relative path to example file
  {{EXAMPLE_OUTPUT_PATH}} - Relative path for example output

${TXT_YELLOW}Note:${TXT_CLEAR} This script can run as a pre-commit hook or directly for development.
EOF
}

# Handle command line arguments
if [[ "${1:-}" == "-h" ]] || [[ "${1:-}" == "--help" ]] || [[ "${1:-}" == "help" ]]; then
    show_help
    exit 0
elif [[ "${1:-}" == "--copy-templates" ]]; then
    if [ -z "${2:-}" ]; then
        log_error "Error: --copy-templates requires a module directory argument"
        log_info "Usage: $0 --copy-templates <module_directory>"
        exit 1
    fi
    copy_templates_to_module "$2"
    exit 0
fi

# check if terraform-docs is installed
if ! command -v terraform-docs &>/dev/null; then
    log_heredoc "${TXT_RED}" <<EOF
=================================================================
Error: terraform-docs could not be found
Please install it by using one of the following commands:
           mise install (if you have mise installed)
           brew install terraform-docs (on MacOS)
=================================================================
EOF
    exit 1
fi

if [ -z "$REPO_ROOT" ]; then
    log_error "Error: Could not determine repository root directory"
    exit 1
fi

# Change to repository root
cd "$REPO_ROOT"

# ================================
# Process all modules (templates and discovery)
# ================================
process_all_modules() {
    log_info "======================================================================="
    log_info "Processing all modules"
    log_info "======================================================================="

    # Initialize arrays to track processed modules
    local processed_modules=()
    local module_dirs=()

    # Discover and process all modules
    while read -r tf_file; do
        module_dir=$(dirname "$tf_file")
        
        # Skip submodules (directories more than 2 levels deep in modules/)
        relative_path=${module_dir#$modules_dir/}
        depth=$(echo "$relative_path" | tr -cd '/' | wc -c)
        
        if [ "$depth" -gt 1 ]; then
            continue
        fi
        
        # Only process each directory once
        if [[ ! " ${processed_modules[@]} " =~ " ${module_dir} " ]]; then
            processed_modules+=("$module_dir")
            module_dirs+=("$module_dir")
            
            # Process templates to ensure they are up to date
            copy_and_process_templates "$module_dir"
        fi
    done < <(find "$modules_dir" -type f -name "*.tf")

    # Export the array for use in other functions
    export PROCESSED_MODULE_DIRS=("${module_dirs[@]}")
    
    log_success "Processed ${#module_dirs[@]} modules"
}

# ================================
# Generate terraform examples
# ================================
generate_terraform_examples() {
    log_info "======================================================================="
    log_info "Generating terraform examples"
    log_info "======================================================================="

    # Initialize arrays to track results
    local successful_examples=()
    local failed_examples=()

    # Generate examples from all .terraform-example.yml files
    for module_dir in "${PROCESSED_MODULE_DIRS[@]}"; do
        terraform_docs_file="$module_dir/.terraform-example.yml"
        module=$(basename "$module_dir")
        example_file=$(dirname "$module_dir/${module}" | sed 's/modules/examples/')/main.tf
        example_dir=$(dirname "$example_file")

        if [ ! -d "$example_dir" ]; then
            mkdir -p "$example_dir"
        fi

        if [ -f "$terraform_docs_file" ]; then
            # Suppress terraform-docs output and track success/failure
            if terraform-docs -c $terraform_docs_file $module_dir >$example_file 2>/dev/null; then
                # Remove any non-HCL lines (e.g., status messages) from the example file
                sed -i.bak '/updated successfully/d' "$example_file"
                rm -f "$example_file.bak"
                successful_examples+=("$example_file")
            else
                log_error "Error running terraform-docs in $module_dir"
                failed_examples+=("$example_file")
            fi
        fi
    done

    if command -v tofu &>/dev/null; then
        tofu fmt -recursive "$REPO_ROOT/infrastructure"
    elif command -v terraform &>/dev/null; then
        terraform fmt -recursive "$REPO_ROOT/infrastructure"
    else
        log_error "Error: tofu or terraform could not be found"
        exit 1
    fi

    # Report example generation results
    log_info "Example generation summary:"
    if [ ${#successful_examples[@]} -gt 0 ]; then
        log_success "  ✅ Successfully generated ${#successful_examples[@]} examples"
        for example in "${successful_examples[@]}"; do
            log_info "    - ${example#$REPO_ROOT/}"
        done
    fi
    if [ ${#failed_examples[@]} -gt 0 ]; then
        log_error "  ❌ Failed to generate ${#failed_examples[@]} examples"
        for example in "${failed_examples[@]}"; do
            log_error "    - ${example#$REPO_ROOT/}"
        done
    fi
}

# ================================
# Generate terraform docs
# ================================
generate_terraform_docs() {
    log_info "======================================================================="
    log_info "Generating terraform docs in $REPO_ROOT"
    log_info "======================================================================="

    # Initialize arrays to track results
    local successful_docs=()
    local failed_docs=()

    for module_dir in "${PROCESSED_MODULE_DIRS[@]}"; do
        # Run terraform-docs with the updated configuration
        if [ -f "$module_dir/.terraform-docs.yml" ]; then
            # Run main terraform-docs and suppress output
            if terraform-docs "$module_dir" >/dev/null 2>&1; then
                if [ -f "$module_dir/.tfvars-docs.yml" ]; then
                    # Only run tfvars docs if the first command succeeded, and there is a .tfvars-docs.yml file
                    if terraform-docs tfvars hcl "$module_dir" -c "$module_dir/.tfvars-docs.yml" >/dev/null 2>&1; then
                        indent_inputs_block "$module_dir/README.md"
                        successful_docs+=("$module_dir")
                    else
                        log_error "Error running tfvars docs in $module_dir"
                        failed_docs+=("$module_dir")
                    fi
                else
                    successful_docs+=("$module_dir")
                fi
            else
                log_error "Error running terraform-docs in $module_dir"
                failed_docs+=("$module_dir")
            fi
        else
            log_error "Failed to create .terraform-docs.yml file in $module_dir"
            failed_docs+=("$module_dir")
        fi
    done

    # Report documentation generation results
    log_info "Documentation generation summary:"
    if [ ${#successful_docs[@]} -gt 0 ]; then
        log_success "  ✅ Successfully generated docs for ${#successful_docs[@]} modules"
        for module in "${successful_docs[@]}"; do
            log_info "    - ${module#$REPO_ROOT/}"
        done
    fi
    if [ ${#failed_docs[@]} -gt 0 ]; then
        log_error "  ❌ Failed to generate docs for ${#failed_docs[@]} modules"
        for module in "${failed_docs[@]}"; do
            log_error "    - ${module#$REPO_ROOT/}"
        done
    fi

    log_success "Finished processing all directories"
}

# ================================
# Main
# ================================
process_all_modules
generate_terraform_examples
generate_terraform_docs
