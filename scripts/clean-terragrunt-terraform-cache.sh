#! /usr/bin/env bash

source $(git rev-parse --show-toplevel)/scripts/utils.sh

log_info "=================================================================================="
log_info "Running terragrunt before hook: $0"

find $(get_repo_root) -name .terragrunt-cache -type d -exec rm -rf {} +
find $(get_repo_root) -name .terraform.lock.hcl -exec rm -rf {} +
find $(get_repo_root) -name .terraform -type d -exec rm -rf {} +

log_info "Done: $0"
log_info "=================================================================================="
