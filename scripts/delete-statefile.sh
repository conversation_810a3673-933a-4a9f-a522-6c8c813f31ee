#!/usr/bin/env bash

# Set strict error handling
set -euo pipefail

# Display help information
show_help() {
    cat <<EOF
Usage: $(basename "$0") [OPTIONS] <state_file_path>

Delete Terraform state files if they are empty or within the empty threshold size.

Arguments:
    state_file_path    Full GCS path to the state file (e.g., gs://ict-p-statefiles/review/ui/FrontEndUI/1236466123.tfstate)

Options:
    -h, --help                     Show this help message
    -d, --dry-run[=BOOL]           Show what would be deleted without actually deleting
    -t, --threshold=NUM            Set custom threshold for empty file size in bytes (default: 152)
       --empty-threshold=NUM       Alternative name for threshold

Example:
    $(basename "$0") 1236466123 
    $(basename "$0") 1236466123 --dry-run
    $(basename "$0") 1236466123 --threshold=180 
    $(basename "$0") 1236466123 --threshold=180 --dry-run=true 
EOF
}

# Constants and defaults (these should be readonly)
readonly REVIEW_APP_STATEFILE=${1:-}

# Variables that will change (these should not be readonly)
DRY_RUN=false
EMPTY_THRESHOLD=152 # Default threshold in bytes
POSITIONAL_ARGS=()

# Parse command line options
while [[ $# -gt 0 ]]; do
    case $1 in
    -h | --help)
        show_help
        exit 0
        ;;
    -d | --dry-run)
        DRY_RUN=true
        shift
        ;;
    -t | --threshold | --empty-threshold)
        if [[ "$2" =~ ^[0-9]+$ ]]; then
            EMPTY_THRESHOLD="$2"
            shift 2
        else
            echo "Error: Threshold must be a number"
            exit 1
        fi
        ;;
    --threshold=* | --empty-threshold=*)
        value="${1#*=}"
        if [[ "$value" =~ ^[0-9]+$ ]]; then
            EMPTY_THRESHOLD="$value"
            shift
        else
            echo "Error: Threshold must be a number"
            exit 1
        fi
        ;;
    --dry-run=*)
        value="${1#*=}"
        case "$value" in
        [Tt][Rr][Uu][Ee] | 1 | [Yy][Ee][Ss]) DRY_RUN=true ;;
        [Ff][Aa][Ll][Ss][Ee] | 0 | [Nn][Oo]) DRY_RUN=false ;;
        *)
            echo "Error: --dry-run value must be true/false"
            exit 1
            ;;
        esac
        shift
        ;;
    -* | --*)
        echo "Error: Unknown option $1"
        exit 1
        ;;
    *)
        POSITIONAL_ARGS+=("$1") # Save positional arg
        shift
        ;;
    esac
done

# Restore positional parameters
set -- "${POSITIONAL_ARGS[@]}"

if [[ -z ${CI_PIPELINE_SOURCE:-} ]]; then
    echo "Would you like to delete the following statefile?"
    echo "${REVIEW_APP_STATEFILE}"
    read -p "Continue? (y/N): " confirm
    case "$confirm" in
    [yY][eE][sS] | [yY])
        echo "Proceeding with deletion of ${REVIEW_APP_STATEFILE}"
        ;;
    *)
        echo "Operation cancelled."
        show_help
        exit 1
        ;;
    esac
fi

# Function to delete state file
delete_state_file() {
    local file_path=$1

    if [ "$DRY_RUN" = true ]; then
        echo "[DRY RUN] Would delete: ${file_path}"
        return 0
    fi

    echo "Deleting: ${file_path}"
    gcloud storage rm --recursive --all-versions "${file_path}"
    return $?
}

# Function to get state file size
get_state_file_size() {
    local size
    size=$(gcloud storage du "$1" 2>/dev/null | cut -f 1 -d ' ')
    echo "${size:-0}" # Return 0 if file doesn't exist
}

# Main execution
echo "Checking review-app statefile..."
state_file_size=$(get_state_file_size "${REVIEW_APP_STATEFILE}")
echo "Statefile path: ${REVIEW_APP_STATEFILE}"
echo "State file size: ${state_file_size} bytes"

# Check if the state file is empty (between 0 and threshold bytes)
if [[ ${state_file_size} -gt 0 && ${state_file_size} -le ${EMPTY_THRESHOLD} ]]; then
    echo "State file is empty (size <= ${EMPTY_THRESHOLD} bytes). Proceeding with deletion..."
    delete_state_file "${REVIEW_APP_STATEFILE}"

    if [ $? -ne 0 ]; then
        echo "Error: Unable to destroy review-app statefile: ${REVIEW_APP_STATEFILE}"
        exit 1
    fi
else
    echo "Statefile is not empty, does not meet threshold ${EMPTY_THRESHOLD} bytes, or it has been destroyed on a previous run. Cleanup cancelled."
    echo "If you're sure that this file is empty and needs to be deleted for cleanup, you can run:"
    echo "   gcloud storage rm --recursive --all-versions ${REVIEW_APP_STATEFILE}"
    exit 1
fi
