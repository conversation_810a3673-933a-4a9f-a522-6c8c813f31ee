#!/usr/bin/env bash

# Set strict error handling
set -euo pipefail

cleanup() {
    echo "Performing cleanup..."
    unset TF_VAR_is_review_app
    rm -f terraform.tfvars
}

# Ensure cleanup is called on script exit
trap cleanup EXIT

# Export variables
export TF_VAR_is_review_app="true"

# Current variables
readonly ACTION=$1
readonly WORKSPACE_NAME=${2:-}
readonly TERRAFORM_MODULE=$(basename "$(pwd)")
readonly TXT_GREEN='\e[1;92m'
readonly TXT_RED='\e[1;91m'
readonly TXT_YELLOW='\e[1;93m'
readonly TXT_CLEAR='\e[0m'

# Get git repository root directory
readonly REPO_ROOT=$(git rev-parse --show-toplevel)
readonly CONFIGS_DIR=${REPO_ROOT}/infrastructure/configs/ui

usage() {
    cat <<EOF
Usage: $(basename "$0") <ACTION>

Execute Terraform/OpenTofu actions for review app environments.

ACTIONS:
    plan                    Generate and show an execution plan
    apply                   Execute the Terraform plan
    destroy                 Destroy the Terraform-managed infrastructure

Note: The script will:
- Use dev.tfvars configuration from ${CONFIGS_DIR}
- Create/select workspace automatically
- Set is_review_app=true and lifetime=ephemeral
- Use environment name format: workspace_name

Examples:
    $(basename "$0") plan 365707476
    $(basename "$0") apply 365707476
    $(basename "$0") destroy 365707476
EOF
    exit 1
}

log() {
    local level=$1
    shift
    echo -e "${level}$*${TXT_CLEAR}"
}

# Validate action
if [[ ! "${ACTION}" =~ ^(apply|destroy|plan)$ ]]; then
    log "${TXT_RED}" "Error: Invalid action '${ACTION}'. Must be 'apply', 'destroy', or 'plan'"
    usage
elif [[ ! "${WORKSPACE_NAME}" ]]; then
    log "${TXT_RED}" "Error: Missing workspace name"
    usage
fi

log "${TXT_GREEN}" "============================================================"
log "${TXT_GREEN}" "[Terraform ${ACTION} on ${TERRAFORM_MODULE}]"
log "${TXT_GREEN}" "============================================================"

# Copy configuration
if [[ ! -f ${CONFIGS_DIR}/dev.tfvars ]]; then
    log "${TXT_RED}" "Error: Configuration file ${CONFIGS_DIR}/dev.tfvars not found"
    exit 1
fi

cp ${CONFIGS_DIR}/dev.tfvars terraform.tfvars

# Initialize and select workspace
log "${TXT_YELLOW}" "Initializing Terraform..."
tofu init -reconfigure

log "${TXT_YELLOW}" "Selecting workspace ${WORKSPACE_NAME}..."
tofu workspace select -or-create "${WORKSPACE_NAME}"

# Execute the requested action
log "${TXT_YELLOW}" "Executing ${ACTION}..."
if [ "${ACTION}" = "apply" ] || [ "${ACTION}" = "destroy" ]; then
    tofu "${ACTION}" -var="env=review-app-${WORKSPACE_NAME}" -var="is_review_app=true" -var="lifetime=ephemeral" -auto-approve
else
    tofu "${ACTION}" -var="env=review-app-${WORKSPACE_NAME}" -var="is_review_app=true" -var="lifetime=ephemeral" -lock=false
fi

log "${TXT_GREEN}" "Successfully executed ${ACTION} on workspace ${WORKSPACE_NAME}"
