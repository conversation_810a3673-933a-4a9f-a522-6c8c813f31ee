from unittest.mock import MagicMock, patch

import pytest
from gitlab_manager import GitlabManager


@pytest.fixture
def manager():
    return GitlabManager(project_id="123", auth_token="token")


def test_get_semver_label_patch(manager):
    with patch.object(manager, 'get_merge_request', return_value={"labels": []}):
        assert manager.get_semver_label("1") == "patch"


def test_get_semver_label_major(manager):
    with patch.object(manager, 'get_merge_request', return_value={"labels": ["major"]}):
        assert manager.get_semver_label("1") == "major"


def test_get_variable_success(manager):
    with patch("requests.get") as mock_get:
        mock_resp = MagicMock()
        mock_resp.ok = True
        mock_resp.json.return_value = {"value": "abc"}
        mock_get.return_value = mock_resp
        assert manager.get_variable("VAR") == "abc"


def test_get_variable_failure(manager):
    with patch("requests.get") as mock_get:
        mock_resp = MagicMock()
        mock_resp.ok = False
        mock_resp.text = "error"
        mock_get.return_value = mock_resp
        assert manager.get_variable("VAR") is None


def test_set_variable_success(manager):
    with patch("requests.put") as mock_put:
        mock_resp = MagicMock()
        mock_resp.ok = True
        mock_resp.json.return_value = {"value": "abc"}
        mock_put.return_value = mock_resp
        assert manager.set_variable("VAR", "abc") == {"value": "abc"}


def test_set_variable_failure(manager):
    with patch("requests.put") as mock_put:
        mock_resp = MagicMock()
        mock_resp.ok = False
        mock_resp.text = "error"
        mock_put.return_value = mock_resp
        assert manager.set_variable("VAR", "abc") is None
