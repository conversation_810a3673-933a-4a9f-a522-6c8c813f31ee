import sys
import types
from unittest import mock

import publish
import pytest


def test_parse_args_defaults(monkeypatch):
    monkeypatch.setattr(
        sys, 'argv', ['publish.py', '--merge-request-iid', '1'])
    args = publish.parse_args()
    assert args.module_prefix == 'control-tower'
    assert args.module_system == 'google'
    assert args.module_namespace == 'dematic'
    assert args.merge_request_iid == '1'
    assert args.dry_run is False


def test_main_dry_run(monkeypatch):
    # Patch dependencies to avoid side effects
    monkeypatch.setattr(
        sys, 'argv', ['publish.py', '--merge-request-iid', '1', '--dry-run'])
    monkeypatch.setattr(publish, 'get_changed_modules',
                        lambda: {'mod': 'path'})
    monkeypatch.setattr(publish, 'GitlabManager', lambda *a,
                        **kw: mock.Mock(get_semver_label=lambda iid: 'patch'))
    monkeypatch.setattr(publish, 'TerraformModuleRegistryClient', lambda *a, **kw: mock.Mock(get_latest_module_version=lambda **
                        k: None, get_variable=lambda x: '1.2.3', set_variable=lambda x, y: None, upload_module=lambda **k: None))
    monkeypatch.setattr(publish, 'SemanticVersionCalculator', lambda v, l: mock.Mock(
        calculate_version=lambda: '1.2.4', base_major_minor_version=lambda: '1.2.0'))
    monkeypatch.setattr(publish, 'publish_module', lambda *a, **k: None)
    monkeypatch.setattr(publish, 'get_all_terraform_modules',
                        lambda x: {'mod': 'path'})
    # Should not raise
    publish.main()
