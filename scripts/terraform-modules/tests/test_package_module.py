import os
import tarfile
import tempfile
from pathlib import Path
from unittest import mock

from package_module import TerraformModulePackager


def test_get_module_files_excludes_dirs_and_files(tmp_path):
    # Setup test directory structure
    (tmp_path / ".git").mkdir()
    (tmp_path / ".terraform").mkdir()
    (tmp_path / "file1.tf").write_text("content")
    (tmp_path / "terraform.tfstate").write_text("state")
    (tmp_path / "file2.txt").write_text("other")
    packager = TerraformModulePackager(str(tmp_path), "1.0.0")
    files = packager._get_module_files()
    # Should only include file1.tf and file2.txt
    file_names = [f.name for f in files]
    assert "file1.tf" in file_names
    assert "file2.txt" in file_names
    assert "terraform.tfstate" not in file_names


def test_calculate_checksums(tmp_path):
    file_path = tmp_path / "test.tgz"
    file_path.write_bytes(b"test content")
    packager = TerraformModulePackager(str(tmp_path), "1.0.0")
    checksums = packager._calculate_checksums(str(file_path))
    assert "sha256" in checksums
    assert checksums["file_size"] == len(b"test content")


def test_package_creates_archive(tmp_path):
    # Create a dummy file
    (tmp_path / "main.tf").write_text("module content")
    packager = TerraformModulePackager(str(tmp_path), "1.0.0")
    result = packager.package()
    assert result["file_count"] == 1
    assert result["version"] == "1.0.0"
    assert os.path.exists(result["package_path"])
    # Check archive contents - files should be directly in the root (Terraform expects this)
    with tarfile.open(result["package_path"], "r:gz") as tar:
        names = tar.getnames()
        expected_file_path = "main.tf"
        assert expected_file_path in names

# Add more tests as needed
