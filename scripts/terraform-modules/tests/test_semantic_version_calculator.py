import pytest
from sematic_version_calculator import SemanticVersionCalculator


def test_patch_increment():
    svc = SemanticVersionCalculator("1.2.3", "patch")
    assert svc.calculate_version() == "1.2.4"


def test_minor_increment():
    svc = SemanticVersionCalculator("1.2.3", "minor")
    assert svc.calculate_version() == "1.3.0"


def test_major_increment():
    svc = SemanticVersionCalculator("1.2.3", "major")
    assert svc.calculate_version() == "2.0.0"


def test_invalid_increment():
    svc = SemanticVersionCalculator("1.2.3", "invalid")
    with pytest.raises(ValueError):
        svc.calculate_version()


def test_base_major_minor_version():
    svc = SemanticVersionCalculator("1.2.3", "patch")
    assert svc.base_major_minor_version() == "1.2.0"
