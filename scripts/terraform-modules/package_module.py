#!/usr/bin/env python3

import hashlib
import os
import re
import tarfile
from pathlib import Path
from typing import Dict, Optional


class TerraformModulePackager:
    """
    A class responsible for packaging Terraform modules for registry upload.
    This class handles the packaging process without any registry-specific logic.
    """

    def __init__(self, module_path: str, version: str):
        """
        Initialize the packager with a path to the Terraform module.

        Args:
            module_path (str): Path to the Terraform module directory
            version (str): Version the Terraform module will be uploaded as
        """
        if os.path.isabs(module_path):
            self.module_path = Path(module_path)
        else:
            # The path is already relative to the scripts/terraform-modules directory
            # No need to add additional relative path components
            self.module_path = Path(module_path)
        self.version = version

    def _get_module_files(self) -> list[Path]:
        """
        Get all relevant Terraform files from the module directory.
        Excludes .git, .terraform, and other temporary directories.

        Returns:
            list[Path]: List of paths to include in the package
        """
        exclude_dirs = {".git", ".terraform", ".terragrunt-cache"}
        exclude_files = {".DS_Store", "terraform.tfstate", "terraform.tfstate.backup"}

        files = []
        for item in self.module_path.rglob("*"):
            if any(excluded in item.parts for excluded in exclude_dirs):
                continue
            if item.name in exclude_files:
                continue
            if item.is_file():
                files.append(item)
        return files

    def _calculate_checksums(self, package_path: str) -> Dict[str, str]:
        """
        Calculate SHA256 checksums for the package file.

        Args:
            package_path (str): Path to the packaged module

        Returns:
            Dict[str, str]: Dictionary containing checksum information
        """
        with open(package_path, "rb") as f:
            content = f.read()
            sha256_hash = hashlib.sha256(content).hexdigest()

        return {"sha256": sha256_hash, "file_size": os.path.getsize(package_path)}

    def package(self) -> Dict:
        """Package the Terraform module into a compressed archive.

        Returns:
            Dict: Dictionary containing package information including:
                - package_path: Path to the created package
                - checksums: Dictionary of checksums
                - version: Module version
                - file_count: Number of files included
        """
        # Create archive with module name and version
        filename = f"{self.module_path.name}-{self.version}.tgz"
        files = self._get_module_files()

        try:
            with tarfile.open(filename, "w:gz") as tar:
                # Add each file to the archive with proper relative paths
                for file_path in files:
                    # Ensure the path in the archive is relative to the module root
                    relative_path = file_path.relative_to(self.module_path)
                    # Use relative path directly - Terraform expects files at root of extracted directory
                    arcname = str(relative_path)
                    tar.add(file_path, arcname=arcname)

            # Calculate checksums on the actual archive file
            checksums = self._calculate_checksums(filename)

            return {
                "package_path": filename,
                "checksums": checksums,
                "version": self.version,
                "file_count": len(files),
            }
        except (IOError, tarfile.TarError) as e:
            print(f"Error creating module package: {str(e)}")
            raise
