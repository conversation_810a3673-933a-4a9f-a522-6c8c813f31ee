#!/usr/bin/env python3


class SemanticVersionCalculator:
    """A class to calculate semantic versions for Terraform modules."""

    def __init__(
        self,
        version: str = "0.0.1",
        increment: str = "patch",
    ):
        """
        Initialize the SemanticVersionCalculator.

        Args:
            version: The current version to calculate the next semantic version on.
            increment: The version increment to apply.
        """
        self.version = version
        self.increment = increment

    def __str__(self):
        return f"SemanticVersionCalculator(version={self.version}, increment={self.increment})"

    def calculate_version(self) -> str:
        """
        Calculate the next semantic version for the module.

        Args:
            increment: The version increment to apply. One of 'major', 'minor', or 'patch'.

        Returns:
            The calculated semantic version as a string.

        Raises:
            ValueError: If the increment value is invalid.
        """
        if self.increment not in ["major", "minor", "patch"]:
            raise ValueError(f"Invalid increment value: {self.increment}")

        major, minor, patch = self.version.split(".")

        if self.increment == "major":
            major = str(int(major) + 1)
            minor = "0"
            patch = "0"
        elif self.increment == "minor":
            minor = str(int(minor) + 1)
            patch = "0"
        else:
            patch = str(int(patch) + 1)

        return f"{major}.{minor}.{patch}"

    def base_major_minor_version(self) -> str:
        """
        Calculate the base major and minor version for the module, with a patch version of 0.
        This is useful for getting the base version for a new release.

        Returns:
            The 'major.minor.0' version as a string.
        """
        return f"{self.version.split('.')[0]}.{self.version.split('.')[1]}.0"
