[project]
name = "terraform-modules"
version = "0.1.0"
description = "Automation toolkit for semantic versioning, packaging, and publishing Terraform modules to a GitLab registry, with support for label-driven version calculation and CI/CD integration."
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "colorama (>=0.4.6,<0.5.0)",
    "python-gitlab (>=5.6.0,<6.0.0)",
    "pytest (>=8.3.5,<9.0.0)",
    "requests (>=2.32.3,<3.0.0)"
]

[tool.poetry]
package-mode = false

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
