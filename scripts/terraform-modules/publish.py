#!/usr/bin/env python3

import argparse
import logging
import os
import subprocess
import sys
from typing import Dict, Optional

from colorama import Fore, Style
from gitlab_manager import GitlabManager, TerraformModuleRegistryClient
from package_module import TerraformModulePackager
from sematic_version_calculator import SemanticVersionCalculator

# Configure logging with structured format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(
        description="Package and publish a Terraform module to GitLab registry"
    )
    parser.add_argument(
        "--module-prefix",
        type=str,
        default="control-tower",
        help="Prefix to prepend to module names (e.g., 'control-tower', 'team-name')",
    )
    parser.add_argument(
        "--module-system",
        type=str,
        default="google",
        help="System type (e.g., 'google', 'aws')",
    )
    parser.add_argument(
        "--module-namespace",
        type=str,
        default="dematic",
        help="GitLab namespace for the module (e.g., 'dematic')",
    )
    parser.add_argument(
        "--project-id",
        type=str,
        default=os.environ.get("CI_PROJECT_ID"),
        help="GitLab project ID (e.g., '65672853')",
    )
    parser.add_argument(
        "--auth-token",
        type=str,
        default=os.environ.get("GROUP_ACCESS_TOKEN"),
        help="GitLab auth token (e.g., 'glrt-...')",
    )
    parser.add_argument(
        "--merge-request-iid",
        type=str,
        required=False,
        help="GitLab merge request IID (e.g., '311') - optional for post-merge runs",
    )
    parser.add_argument(
        "--modules-root-path",
        type=str,
        default="../../infrastructure/modules",
        help="Path to the root of the Terraform modules (e.g., '../../infrastructure/modules')",
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="""
            Run in dry-run mode (no changes will be made) 
            - useful for testing or running prior to merging
        """,
    )
    return parser.parse_args()


def get_infrastructure_version(
    manager: GitlabManager, label: str
) -> str:
    """
    Bump the major or minor version of the Terraform module.

    Args:
        manager: GitLab manager instance
        label: Version increment label (e.g., 'major', 'minor', 'patch')
    
    Returns:
        str: The calculated version string
    """
    logger.info(f"Getting infrastructure version with label: {label}")
    try:
        version = manager.get_variable("TERRAFORM_MODULE_VERSION")
        logger.info(f"Current TERRAFORM_MODULE_VERSION: {version}")
        
        calculated_version = SemanticVersionCalculator(version, label).calculate_version()
        logger.info(f"Calculated new version: {calculated_version}")
        
        return calculated_version
    except Exception as e:
        logger.error(f"Failed to get infrastructure version: {e}")
        raise


def get_all_terraform_modules(modules_root_path: str = "../../infrastructure/modules") -> Dict[str, str]:
    """Get all folders containing .tf files under infrastructure/modules

    Args:
        modules_root_path: Root path to search for terraform modules
        
    Returns:
        dict: Dictionary mapping module names to their full paths
        e.g. {'core-services': '../../infrastructure/modules/core/core-services'}
    """
    logger.info(f"Scanning for terraform modules in: {modules_root_path}")
    
    if not os.path.exists(modules_root_path):
        logger.error(f"Modules root path does not exist: {modules_root_path}")
        return {}
    
    terraform_modules = {}
    ignore_dirs = [".terraform", "templates"]

    for root, dirs, files in os.walk(modules_root_path):
        if any(file.endswith(".tf") for file in files):
            if not any(dir in ignore_dirs for dir in dirs):
                module_name = os.path.basename(root)
                terraform_modules[module_name] = root
                logger.debug(f"Found terraform module: {module_name} at {root}")

    logger.info(f"Found {len(terraform_modules)} terraform modules: {list(terraform_modules.keys())}")
    return terraform_modules


def get_changed_modules(modules_root_path: str = "../../infrastructure/modules") -> Dict[str, str]:
    """
    Get unique module folder names and their base paths.

    Args:
        modules_root_path: Root path to the modules directory from current working directory
        
    Returns:
        dict[str, str]: Dictionary mapping module names to their base directory paths.
        Example: {
            'instance-project': '../../infrastructure/modules/instance/project',
            'instance-network': '../../infrastructure/modules/instance/network'
        }
    """
    logger.info("Determining changed modules...")
    ignore_dirs = [".terraform", "templates"]
    ignore_files = ["infrastructure/modules/README.md"]

    # Check if we're in a GitLab CI environment
    if os.environ.get("CI"):
        logger.info("Running in GitLab CI environment")
        
        # In GitLab CI, check if we're in a merge request
        if os.environ.get("CI_MERGE_REQUEST_IID"):
            logger.info(f"Processing merge request: {os.environ.get('CI_MERGE_REQUEST_IID')}")
            
            # For merge requests, compare target and source branch SHAs
            # This shows only changes made in this branch/MR
            target_sha = os.environ.get("CI_MERGE_REQUEST_TARGET_BRANCH_SHA")
            source_sha = os.environ.get("CI_MERGE_REQUEST_SOURCE_BRANCH_SHA")

            if target_sha and source_sha:
                logger.info(f"Comparing target SHA {target_sha[:8]} with source SHA {source_sha[:8]}")
                # Compare between target and source branch
                diff_cmd = ["git", "diff", "--name-only", f"{target_sha}..{source_sha}"]
            else:
                # Fallback to comparing with the current commit
                current_sha = os.environ.get("CI_COMMIT_SHA")
                logger.warning(f"Target or source SHA not available, using current commit: {current_sha[:8] if current_sha else 'None'}")
                diff_cmd = [
                    "git",
                    "show",
                    "--name-only",
                    "--pretty=format:",
                    current_sha,
                ]
        else:
            logger.info("Not in a merge request, checking if this is a merge commit")
            current_sha = os.environ.get("CI_COMMIT_SHA")
            
            # Check if current commit is a merge commit
            try:
                parents_cmd = ["git", "cat-file", "-p", current_sha]
                parents_output = subprocess.check_output(parents_cmd).decode("utf-8")
                parent_lines = [line for line in parents_output.split('\n') if line.startswith('parent ')]
                
                if len(parent_lines) >= 2:
                    logger.info(f"Detected merge commit with {len(parent_lines)} parents")
                    # This is a merge commit, compare with first parent to get all feature branch changes
                    diff_cmd = ["git", "diff", "--name-only", f"{current_sha}^1..{current_sha}"]
                    logger.info(f"Using merge commit diff: {current_sha[:8]}^1..{current_sha[:8]}")
                else:
                    logger.info("Single parent commit, using before/current SHA comparison")
                    # Regular commit, use before/current comparison
                    before_sha = os.environ.get("CI_COMMIT_BEFORE_SHA")
                    
                    if before_sha and current_sha:
                        logger.info(f"Comparing before SHA {before_sha[:8]} with current SHA {current_sha[:8]}")
                        diff_cmd = ["git", "diff", "--name-only", f"{before_sha}..{current_sha}"]
                    else:
                        logger.warning(f"Before SHA not available, using current commit: {current_sha[:8] if current_sha else 'None'}")
                        diff_cmd = [
                            "git",
                            "show",
                            "--name-only",
                            "--pretty=format:",
                            current_sha,
                        ]
            except subprocess.CalledProcessError as e:
                logger.warning(f"Failed to check commit parents: {e}")
                # Fallback to before/current SHA comparison
                before_sha = os.environ.get("CI_COMMIT_BEFORE_SHA")
                
                if before_sha and current_sha:
                    logger.info(f"Fallback: comparing before SHA {before_sha[:8]} with current SHA {current_sha[:8]}")
                    diff_cmd = ["git", "diff", "--name-only", f"{before_sha}..{current_sha}"]
                else:
                    logger.warning(f"Fallback: using current commit: {current_sha[:8] if current_sha else 'None'}")
                    diff_cmd = [
                        "git",
                        "show",
                        "--name-only",
                        "--pretty=format:",
                        current_sha,
                    ]
    else:
        logger.info("Running in local development environment")
        # Local development - get all changes (committed, staged, and unstaged) in current branch
        try:
            merge_base = subprocess.check_output(["git", "merge-base", "HEAD", "dev"]).decode("utf-8").strip()
            logger.info(f"Merge base with dev branch: {merge_base[:8]}")
            
            # Get committed changes in branch
            committed_changes = subprocess.check_output(["git", "diff", "--name-only", f"{merge_base}..HEAD"]).decode("utf-8").strip()
            logger.debug(f"Committed changes: {len(committed_changes.split()) if committed_changes else 0} files")
            
            # Get staged changes
            staged_changes = subprocess.check_output(["git", "diff", "--name-only", "--cached"]).decode("utf-8").strip()
            logger.debug(f"Staged changes: {len(staged_changes.split()) if staged_changes else 0} files")
            
            # Get unstaged changes
            unstaged_changes = subprocess.check_output(["git", "diff", "--name-only"]).decode("utf-8").strip()
            logger.debug(f"Unstaged changes: {len(unstaged_changes.split()) if unstaged_changes else 0} files")
            
            # Combine all changes
            all_changes = []
            for changes in [committed_changes, staged_changes, unstaged_changes]:
                if changes:
                    all_changes.extend(changes.split('\n'))
            
            # Remove duplicates and create a combined output
            unique_changes = list(set(all_changes))
            diff_output = '\n'.join(unique_changes) if unique_changes else ""
            logger.info(f"Total unique changed files: {len(unique_changes)}")
            
        except subprocess.CalledProcessError as e:
            logger.warning(f"Failed to get merge-base with dev branch: {e}")
            # Fallback to comparing with working directory if merge-base fails
            diff_cmd = ["git", "diff", "--name-only", "HEAD"]
            try:
                diff_output = subprocess.check_output(diff_cmd).decode("utf-8")
                logger.info("Using fallback: comparing with HEAD")
            except subprocess.CalledProcessError as fallback_e:
                logger.error(f"Fallback git diff also failed: {fallback_e}")
                return {}

    # For CI environments, execute the diff command
    if os.environ.get("CI"):
        try:
            logger.info(f"Executing git command: {' '.join(diff_cmd)}")
            diff_output = subprocess.check_output(diff_cmd).decode("utf-8")
            logger.info(f"Git diff returned {len(diff_output.split()) if diff_output else 0} changed files")
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to get git diff: {e}")
            logger.error(f"Command that failed: {' '.join(diff_cmd)}")
            return {}

    # Get all changed .tf and .tfvars files under modules/
    all_files = [file for file in diff_output.split("\n") if file.strip()]
    logger.info(f"Processing {len(all_files)} changed files")
    
    tf_files = [
        file
        for file in all_files
        if file.startswith("infrastructure/modules/")
        and not any(dir in ignore_dirs for dir in file.split("/"))
        and not file in ignore_files
    ]
    
    logger.info(f"Found {len(tf_files)} changed files in infrastructure/modules/")
    for tf_file in tf_files:
        logger.debug(f"Changed terraform file: {tf_file}")

    # Create dictionary mapping module names to their base paths
    module_paths = {}
    for file in tf_files:
        # Split path and get the module name (last folder in path)
        # e.g., 'infrastructure/modules/core-services/main.tf' -> 'core-services'
        parts = file.split("/")
        if len(parts) >= 5:  # Ensure we have enough path components
            module_name = parts[-2]  # Just use the last directory name
            # Convert git diff path to actual filesystem path relative to working directory
            # git diff returns: infrastructure/modules/common/common-services/file.tf
            # we need: ../../infrastructure/modules/common/common-services (from scripts/terraform-modules/)
            module_relative_path = "/".join(parts[2:-1])  # Extract path after 'infrastructure/modules/'
            base_path = os.path.join(modules_root_path, module_relative_path)
            module_paths[module_name] = base_path
            logger.debug(f"Identified changed module: {module_name} at {base_path}")

    logger.info(f"Identified {len(module_paths)} changed modules: {list(module_paths.keys())}")
    return module_paths


def display_publishing_plan(modules_to_publish: Dict[str, str], semver_label: str, dry_run: bool = False):
    """Display a clear summary of modules planned for publishing.
    
    Args:
        modules_to_publish: Dictionary mapping module names to their versions
        semver_label: The semantic version label being applied
        dry_run: Whether this is a dry run
    """
    prefix = "[DRY-RUN] " if dry_run else ""
    
    print(f"\n{Fore.CYAN}{'='*80}")
    print(f"📦 {prefix}TERRAFORM MODULE PUBLISHING PLAN")
    print(f"{'='*80}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}Version Strategy:{Style.RESET_ALL} {semver_label.upper()} bump")
    print(f"{Fore.YELLOW}Modules to Publish:{Style.RESET_ALL} {len(modules_to_publish)}")
    print()
    
    if modules_to_publish:
        for i, (module_name, version) in enumerate(modules_to_publish.items(), 1):
            status_icon = "🔄" if not dry_run else "👁️"
            print(f"  {status_icon} {i:2d}. {Fore.GREEN}{module_name}{Style.RESET_ALL} → {Fore.BLUE}v{version}{Style.RESET_ALL}")
    else:
        print(f"  {Fore.YELLOW}ℹ️  No modules to publish{Style.RESET_ALL}")
    
    print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}\n")


def display_publishing_results(successful_modules: Dict[str, str], failed_modules: Dict[str, str], dry_run: bool = False):
    """Display a clear summary of publishing results.
    
    Args:
        successful_modules: Dictionary mapping successful module names to their versions
        failed_modules: Dictionary mapping failed module names to their error messages
        dry_run: Whether this was a dry run
    """
    total_modules = len(successful_modules) + len(failed_modules)
    prefix = "[DRY-RUN] " if dry_run else ""
    
    print(f"\n{Fore.CYAN}{'='*80}")
    print(f"📊 {prefix}TERRAFORM MODULE PUBLISHING RESULTS")
    print(f"{'='*80}{Style.RESET_ALL}")
    
    # Summary stats
    success_rate = (len(successful_modules) / total_modules * 100) if total_modules > 0 else 0
    print(f"{Fore.YELLOW}Total Modules:{Style.RESET_ALL} {total_modules}")
    print(f"{Fore.GREEN}✅ Successful:{Style.RESET_ALL} {len(successful_modules)}")
    print(f"{Fore.RED}❌ Failed:{Style.RESET_ALL} {len(failed_modules)}")
    print(f"{Fore.YELLOW}Success Rate:{Style.RESET_ALL} {success_rate:.1f}%")
    print()
    
    # Successful modules
    if successful_modules:
        print(f"{Fore.GREEN}✅ SUCCESSFULLY PUBLISHED:{Style.RESET_ALL}")
        for i, (module_name, version) in enumerate(successful_modules.items(), 1):
            action = "Would publish" if dry_run else "Published"
            print(f"  {i:2d}. {Fore.GREEN}{module_name}{Style.RESET_ALL} → {Fore.BLUE}v{version}{Style.RESET_ALL} ({action})")
        print()
    
    # Failed modules
    if failed_modules:
        print(f"{Fore.RED}❌ FAILED TO PUBLISH:{Style.RESET_ALL}")
        for i, (module_name, error) in enumerate(failed_modules.items(), 1):
            print(f"  {i:2d}. {Fore.RED}{module_name}{Style.RESET_ALL} → {Fore.RED}{error}{Style.RESET_ALL}")
        print()
    
    # Final status
    if failed_modules:
        print(f"{Fore.RED}🚨 PUBLISHING COMPLETED WITH ERRORS{Style.RESET_ALL}")
    else:
        emoji = "👁️" if dry_run else "🎉"
        message = "DRY-RUN COMPLETED SUCCESSFULLY" if dry_run else "ALL MODULES PUBLISHED SUCCESSFULLY"
        print(f"{Fore.GREEN}{emoji} {message}{Style.RESET_ALL}")
    
    print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}\n")


def publish_module(
    module_name: str,
    module_system: str,
    version: str,
    path: str,
    client: TerraformModuleRegistryClient,
) -> bool:
    """Publish a terraform module to the registry.
    
    Args:
        module_name: Name of the module to publish
        module_system: System type (e.g., 'google', 'aws')
        version: Version to publish
        path: Path to the module directory
        client: Registry client instance
        
    Returns:
        bool: True if successful, False otherwise
    """
    logger.info(f"Publishing module: {module_name} v{version} from {path}")
    
    try:
        # Validate module path exists
        if not os.path.exists(path):
            logger.error(f"Module path does not exist: {path}")
            return False
            
        # Package the module
        logger.info(f"Packaging module: {module_name}")
        packager = TerraformModulePackager(module_path=path, version=version)
        package_data = packager.package()
        package_path = package_data["package_path"]
        logger.info(f"Module packaged successfully: {package_path}")

        # Upload the module
        logger.info(f"Uploading module: {module_name} to registry")
        client.upload_module(
            module_name=module_name,
            module_system=module_system,
            module_version=version,
            package_path=package_path,
        )
        logger.info(f"Successfully published module: {module_name} v{version}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to publish module {module_name}: {e}")
        return False


def main() -> Optional[int]:
    """Main entry point for the module publishing script.
    
    Returns:
        Optional[int]: Exit code (0 for success, 1 for failure)
    """
    try:
        args = parse_args()
        
        logger.info(f"Starting terraform module publishing process")
        logger.info(f"Configuration: prefix={args.module_prefix}, system={args.module_system}, namespace={args.module_namespace}")
        logger.info(f"Project ID: {args.project_id}, MR IID: {args.merge_request_iid}")
        
        if args.dry_run:
            logger.info(f"Running in DRY-RUN mode - no changes will be made")
        
        # Get changed modules
        logger.info("Scanning for changed modules...")
        changed_modules = get_changed_modules(args.modules_root_path)
        
        if not changed_modules:
            logger.info("No changed modules detected")
        else:
            logger.info(f"Changed modules detected: {list(changed_modules.keys())}")
            
    except Exception as e:
        logger.error(f"Failed to initialize: {e}")
        return 1

    try:
        logger.info("Initializing GitLab manager and registry client...")
        manager = GitlabManager(project_id=args.project_id, auth_token=args.auth_token)
        
        # Determine semver label based on context
        if args.merge_request_iid:
            logger.info(f"MR context detected, getting semver label from MR {args.merge_request_iid}")
            semver_label = manager.get_semver_label(args.merge_request_iid)
        else:
            logger.info("No MR context, defaulting to patch version bump")
            semver_label = "patch"
        
        logger.info(f"Semantic version label: {semver_label}")

        client = TerraformModuleRegistryClient(
            module_namespace=args.module_namespace,
            project_id=args.project_id,
            auth_token=args.auth_token,
        )
        logger.info("Successfully initialized GitLab connections")
        
    except Exception as e:
        logger.error(f"Failed to initialize GitLab connections: {e}")
        return 1

    if semver_label in ["major", "minor"]:
        logger.info(f"Processing {semver_label} version bump - publishing ALL modules")
        
        try:
            version = get_infrastructure_version(
                manager=manager,
                label=semver_label,
            )
            
            all_modules = get_all_terraform_modules(args.modules_root_path)
            if not all_modules:
                logger.warning("No terraform modules found to publish")
                return 0
            
            # Prepare modules to publish with their versions
            modules_to_publish = {}
            for module, path in all_modules.items():
                module_name = f"{args.module_prefix}-{module}"
                modules_to_publish[module_name] = version
            
            # Display publishing plan
            display_publishing_plan(modules_to_publish, semver_label, args.dry_run)
            
            logger.info(f"Publishing {len(all_modules)} modules with version {version}")
            
            successful_modules = {}
            failed_modules = {}
            
            for module, path in all_modules.items():
                module_name = f"{args.module_prefix}-{module}"
                
                if args.dry_run:
                    logger.info(f"[DRY-RUN] Would publish {module_name} with version {version}")
                    successful_modules[module_name] = version
                else:
                    if publish_module(
                        module_name=module_name,
                        module_system=args.module_system,
                        version=version,
                        path=path,
                        client=client,
                    ):
                        successful_modules[module_name] = version
                    else:
                        failed_modules[module_name] = "Publishing failed"
            
            # Display results
            display_publishing_results(successful_modules, failed_modules, args.dry_run)
            
            logger.info(f"Module publishing summary: {len(successful_modules)} published, {len(failed_modules)} failed")
            
            if failed_modules:
                logger.error(f"Some modules failed to publish ({len(failed_modules)}/{len(all_modules)})")
                return 1
                
            logger.info(f"Successfully published all modules with version: {version}")
            
            if not args.dry_run:
                try:
                    client.set_variable("TERRAFORM_MODULE_VERSION", version)
                    logger.info(f"Updated TERRAFORM_MODULE_VERSION to {version}")
                except Exception as e:
                    logger.error(f"Failed to update TERRAFORM_MODULE_VERSION: {e}")
                    return 1
                    
        except Exception as e:
            logger.error(f"Failed during major/minor version publishing: {e}")
            return 1
    else:
        # Patch label - only publish changed modules
        logger.info(f"Processing {semver_label} version bump - publishing CHANGED modules only")
        
        if not changed_modules:
            logger.info("No changed modules to publish")
            return 0
        
        # First pass: calculate versions for all modules
        modules_to_publish = {}
        
        try:
            for module, path in changed_modules.items():
                module_name = f"{args.module_prefix}-{module}"
                logger.info(f"Calculating version for module: {module_name}")
                
                try:
                    # Get the latest version of the module
                    latest_version = client.get_latest_module_version(
                        module_name=module_name,
                        module_system=args.module_system,
                    )
                    
                    if latest_version == None:
                        # If the module is not found in the registry,
                        # use the latest major & minor version (ie. 1.2.3 -> 1.2.0)
                        # because this is probably a new module
                        base_version = client.get_variable("TERRAFORM_MODULE_VERSION")
                        logger.info(
                            f"Module {module_name} not found in registry, using base version {base_version} for calculation"
                        )
                        version = SemanticVersionCalculator(
                            base_version, semver_label
                        ).base_major_minor_version()
                        logger.info(f"Calculated new module version: {version}")
                        modules_to_publish[module_name] = version
                    else:
                        # If the module is found in the registry, bump the patch version (ie. 1.2.3 -> 1.2.4)
                        logger.info(f"Module {module_name} found in registry with version {latest_version}")
                        version = SemanticVersionCalculator(
                            latest_version, semver_label
                        ).calculate_version()
                        logger.info(f"Calculated new version: {version}")
                        modules_to_publish[module_name] = version
                        
                except Exception as e:
                    logger.error(f"Failed to calculate version for module {module_name}: {e}")
                    modules_to_publish[module_name] = f"ERROR: {str(e)}"
            
            # Display publishing plan
            display_publishing_plan(modules_to_publish, semver_label, args.dry_run)
            
            logger.info(f"Publishing {len(changed_modules)} changed modules")
            
            successful_modules = {}
            failed_modules = {}
            
            # Second pass: actually publish the modules
            for module, path in changed_modules.items():
                module_name = f"{args.module_prefix}-{module}"
                
                if module_name not in modules_to_publish:
                    failed_modules[module_name] = "Version calculation failed"
                    continue
                
                version = modules_to_publish[module_name]
                if version.startswith("ERROR:"):
                    failed_modules[module_name] = version
                    continue
                
                try:
                    if args.dry_run:
                        logger.info(f"[DRY-RUN] Would publish {module_name} with version {version}")
                        successful_modules[module_name] = version
                    else:
                        if publish_module(
                            module_name=module_name,
                            module_system=args.module_system,
                            version=version,
                            path=path,
                            client=client,
                        ):
                            successful_modules[module_name] = version
                        else:
                            failed_modules[module_name] = "Publishing failed"
                            
                except Exception as e:
                    logger.error(f"Failed to process module {module_name}: {e}")
                    failed_modules[module_name] = f"Exception: {str(e)}"
            
            # Display results
            display_publishing_results(successful_modules, failed_modules, args.dry_run)
            
            logger.info(f"Module publishing summary: {len(successful_modules)} published, {len(failed_modules)} failed")
            
            if failed_modules:
                logger.error(f"Some modules failed to publish ({len(failed_modules)}/{len(changed_modules)})")
                return 1
                
            logger.info("Successfully completed module publishing")
            
        except Exception as e:
            logger.error(f"Failed during patch version publishing: {e}")
            return 1
            
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        if exit_code:
            logger.error(f"Script completed with exit code: {exit_code}")
        else:
            logger.info("Script completed successfully")
        exit(exit_code or 0)
    except KeyboardInterrupt:
        logger.info("Script interrupted by user")
        exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        exit(1)
