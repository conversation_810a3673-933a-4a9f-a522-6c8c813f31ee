# Terraform Modules Publishing System

This directory contains a set of Python scripts for managing and publishing Terraform modules to a GitLab registry. The system provides automated versioning, packaging, and publishing capabilities for Terraform modules.

## Overview

The system is designed to:

- Automatically detect changes in Terraform modules
- Calculate semantic versions based on merge request labels
- Package Terraform modules
- Publish modules to a GitLab registry

## Components

### Core Scripts

- `publish.py`: Main script that orchestrates the module publishing process
- `gitlab_manager.py`: Handles GitLab API interactions and merge request management
- `sematic_version_calculator.py`: Calculates semantic versions based on labels
- `package_module.py`: Handles the packaging of Terraform modules

### Configuration Files

- `pyproject.toml`: Python package dependencies
- `.mise.toml`: Development environment configuration

## Usage

### Prerequisites

- Mise (activated & trusted)
- Pip
- Python 3.12
- GitLab access token with appropriate permissions
- GitLab project ID
- Terraform modules in the specified directory structure

## Poetry Setup (Alternative to pip)

You can use [Poetry](https://python-poetry.org/) to manage dependencies and virtual environments for this project.

### Installing Poetry

If you do not have Poetry installed, you can install it with:

```bash
pip install poetry
```

Or follow the [official Poetry installation guide](https://python-poetry.org/docs/#installation).

### Installing Dependencies with Poetry

From the `scripts/terraform-modules` directory, run:

```bash
poetry install
```

This will create a virtual environment (if one does not exist) and install all dependencies as specified in `pyproject.toml` and `poetry.lock`.

You can then run scripts (e.g., `python publish.py ...`) directly within this environment.

### Setup

1. Ensure you install the requirements (if you have mise)

   ```bash
   # This should also automatically create a virtual environment
   mise install
    ```

2. Skip if you did step 1, otherwise create a virtual environment:

   ```bash
   python -m venv .venv
   source .venv/bin/activate
   ```

3. Install dependencies:

   ```bash
   poetry install
   ```

### Running the Publisher

The main script can be run with various options:

```bash
python publish.py \
  --module-prefix "control-tower" \
  --module-system "google" \
  --module-namespace "dematic" \
  --project-id "YOUR_PROJECT_ID" \
  --auth-token "YOUR_AUTH_TOKEN" \
  --merge-request-iid "MR_ID" \
  --modules-root-path "../../infrastructure/modules" \
  [--dry-run]
```

### Command Line Arguments

- `--module-prefix`: Prefix for module names (default: "control-tower")
- `--module-system`: System type (e.g., "google", "aws")
- `--module-namespace`: GitLab namespace for modules
- `--project-id`: GitLab project ID
- `--auth-token`: GitLab authentication token
- `--merge-request-iid`: GitLab merge request IID
- `--modules-root-path`: Path to Terraform modules root
- `--dry-run`: Run in dry-run mode (no changes made)

## Versioning

### Semantic Versioning Overview

The system uses semantic versioning (MAJOR.MINOR.PATCH) following these rules:

- **MAJOR** version (X.0.0): Incremented when making incompatible API changes
- **MINOR** version (0.X.0): Incremented when adding functionality in a backward-compatible manner
- **PATCH** version (0.0.X): Incremented when making backward-compatible bug fixes (default if no version label is specified)

### When to Bump Versions

1. **Major Version (X.0.0)**
   - Breaking changes to the module's interface
   - Changes that require users to modify their Terraform configurations
   - Removal or renaming of required variables
   - Changes to resource naming patterns
   - Major architectural changes

2. **Minor Version (0.X.0)**
   - Adding new features that maintain backward compatibility
   - Adding new optional variables
   - Adding new outputs
   - Adding new resources without changing existing ones
   - Performance improvements

3. **Patch Version (0.0.X)**
   - Bug fixes
   - Documentation updates
   - Internal refactoring
   - Security patches
   - Minor improvements that don't affect functionality

### How Versioning Works

The versioning system automatically calculates the next version based on merge request labels:

1. **Label-based Versioning**
   - Add the appropriate label to your merge request:
     - `major`: For breaking changes
     - `minor`: For new features
     - `patch`: For bug fixes

2. **Version Calculation**
   - The system reads the current version from the module
   - Based on the merge request label, it calculates the next version:
     - `major`: Increments X in X.0.0 and resets minor and patch to 0
     - `minor`: Increments X in 0.X.0 and resets patch to 0
     - `patch`: Increments X in 0.0.X

3. **Version Storage**
   - Versions are stored in the module's metadata
   - The system maintains version history in the GitLab registry

### Example Version Bumps

```plaintext
Current Version: 1.2.3

Label: major    → Next Version: 2.0.0
Label: minor    → Next Version: 1.3.0
Label: patch    → Next Version: 1.2.4
```

## Development

### Adding New Features

1. Create a new branch for your feature
2. Implement the changes
3. Add tests if applicable
4. Update documentation
5. Create a merge request with appropriate labels

### Testing

To test changes without affecting production:

1. Ensure you have the following environment variables or pass them in as flags
   a. **GROUP_ACCESS_TOKEN** or `--auth-token glpat-******************`
   b. **CI_PROJECT_ID** or `--project-id 65672853`
   c. `--merge-reqeust-iid xxx` (can be found on your merge request, or in the URL of your MR)
2. Use the `--dry-run` flag
3. Verify the output
4. Check the simulated version calculations

Example (from the scripts/terraform-modules directory):

```bash
python publish.py --merge-request-iid 311 --project-id 65672853 --auth-token glpat-****************** --dry-run
```

## Running Tests

To run the test suite for this application, ensure you have installed the development dependencies (see requirements.txt for pytest and pytest-mock). Then, from this directory, run:

```bash
pytest
```

This will discover and run all tests in the `tests/` directory.

## Troubleshooting

Common issues and solutions:

1. Authentication errors: Verify your GitLab token has correct permissions
2. Version calculation issues: Check merge request labels
3. Module packaging errors: Verify module structure and dependencies
