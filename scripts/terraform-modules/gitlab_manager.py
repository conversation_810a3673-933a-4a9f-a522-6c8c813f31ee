#!/usr/bin/env python3

import logging
import os

import requests

logger = logging.getLogger(__name__)

class GitlabManager:

    def __init__(self, project_id: str, auth_token: str):
        self.project_id = project_id
        self.auth_token = auth_token
        self.available_version_labels = ["major", "minor", "patch"]
        self.labels = []
        self.url_base: str = "https://gitlab.com/api/v4"

    def __str__(self) -> str:
        return f"""
        GitlabManager(
            project_id={self.project_id},
            available_version_labels={self.available_version_labels},
            labels={self.labels})
        """

    def get_merge_request(self, merge_request_iid: str):
        url = f"{self.url_base}/projects/{self.project_id}/merge_requests/{merge_request_iid}"
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        response = requests.get(url, headers=headers)
        return response.json()

    def get_semver_label(self, merge_request_iid: str) -> list:
        """
        Get the semver label for a merge request.
        If no semver label exists, add 'patch' to the existing labels.
        """
        logger.info(f"Getting semver label for MR {merge_request_iid}")
        current_labels = self.get_merge_request(merge_request_iid)["labels"]
        logger.info(f"Available labels on MR: {current_labels}")
        
        semver_label = next(
            (
                label
                for label in current_labels
                if label in self.available_version_labels
            ),
            "patch",
        )
        
        logger.info(f"Selected semver label: {semver_label} {'(default)' if semver_label == 'patch' and semver_label not in current_labels else '(from MR labels)'}")
        return semver_label

    def get_variable(self, variable_name: str):
        logger.info(f"Getting GitLab variable: {variable_name}")
        url = f"{self.url_base}/projects/{self.project_id}/variables/{variable_name}"
        headers = {"PRIVATE-TOKEN": self.auth_token}
        try:
            response = requests.get(url, headers=headers)
            if response.ok:
                value = response.json()["value"]
                logger.info(f"Successfully retrieved variable {variable_name}: {value}")
                return value
            else:
                logger.error(
                    f"Failed to get variable {variable_name}: HTTP {response.status_code} - {response.text}"
                )
                return None
        except requests.exceptions.RequestException as e:
            logger.error(f"Network error getting variable {variable_name}: {e}")
            return None

    def set_variable(self, variable_name: str, value: str):
        logger.info(f"Setting GitLab variable {variable_name} to: {value}")
        url = f"{self.url_base}/projects/{self.project_id}/variables/{variable_name}"
        headers = {"PRIVATE-TOKEN": self.auth_token}
        data = {"value": value}
        response = requests.put(url, headers=headers, json=data)
        if response.ok:
            logger.info(f"Successfully set variable {variable_name}")
            return response.json()
        else:
            logger.error(f"Failed to set variable {variable_name}: HTTP {response.status_code} - {response.text}")
            return None


class TerraformModuleRegistryClient(GitlabManager):
    """
    A class for interacting with the GitLab registry.

    Args:
        module_namespace (str): The namespace of the module to interact with.
        module_name (str): The name of the module to interact with.
        module_system (str): The system of the module to interact with.
        project_id (str): The ID of the project to interact with.
        auth_token (str): The authentication token to use for the registry.

    Example:

        python terraform_module_registry.py \
            --module-namespace dematic \
            --module-name control-tower-api \
            --module-system google \
            --project-id 64468440 \
            --auth-token $CI_JOB_TOKEN

    """

    def __init__(
        self,
        module_namespace: str,
        project_id: str,
        auth_token: str,
    ):
        super().__init__(project_id, auth_token)
        self.module_namespace = module_namespace or os.environ.get("CI_PROJECT_NAME")

        self.query_path_base: str = "packages/terraform/modules/v1"
        self.upload_path_base: str = f"projects/{project_id}/packages/terraform/modules"

        self._token_headers: dict = {"PRIVATE-TOKEN": auth_token}
        self._bearer_headers: dict = {"Authorization": f"Bearer {auth_token}"}

    def __str__(self) -> str:
        return f"""
        TerraformModuleRegistryClient(
            module_namespace={self.module_namespace},
            project_id={self.project_id},
            url_base={self.url_base},
            query_path_base={self.query_path_base},
            upload_path_base={self.upload_path_base},
        )
        """

    def get_latest_module_version(self, module_name: str, module_system: str) -> str:
        """
        Get the latest version of a module from the GitLab registry.

        Args:
            module_name (str): The name of the module to get the latest version of.
            module_system (str): The system of the module to get the latest version of.

        Returns:
            str: The latest version of the module.
        """
        logger.info(f"Querying latest version for module: {self.module_namespace}/{module_name}/{module_system}")
        read_url = f"{self.url_base}/{self.query_path_base}/{self.module_namespace}/{module_name}/{module_system}"
        logger.debug(f"Registry query URL: {read_url}")

        try:
            response = requests.get(read_url, headers=self._bearer_headers)
            logger.debug(f"Registry response: HTTP {response.status_code}")
        except requests.exceptions.RequestException as e:
            logger.error(f"Network error querying module {module_name}: {e}")
            print(f"Failed to find module {module_name} from GitLab: {e}")
            exit(1)

        if response.status_code == 403:
            logger.info(f"Module {module_name} not found in registry (403 Forbidden) - likely new module")
            return None
        elif response.status_code == 404:
            logger.info(f"Module {module_name} not found in registry (404 Not Found) - likely new module")
            return None
        elif not response.ok:
            logger.error(f"Failed to query module {module_name}: HTTP {response.status_code} - {response.text}")
            return None

        version = response.json()["version"]
        logger.info(f"Latest version of {module_name}: {version}")
        return version

    def upload_module(
        self,
        module_name: str,
        module_system: str,
        module_version: str,
        package_path: str,
    ):
        """
        Upload a module to the GitLab registry.

        Args:
            module_name (str): The name of the module
            module_system (str): The system type (e.g., 'google', 'aws')
            module_version (str): The version to upload
            package_path (str): Path to the packaged module file
        """

        headers = {"PRIVATE-TOKEN": self.auth_token}
        upload_url = f"{self.url_base}/projects/{self.project_id}/packages/terraform/modules/{module_name}/{module_system}/{module_version}/file"

        with open(package_path, "rb") as file:
            response = requests.put(
                upload_url,
                headers=headers,
                data=file,
            )
            if response.ok:
                logger.info(f"Module {module_name} v{module_version} uploaded successfully to {upload_url}")
                print(f"Module uploaded successfully to {upload_url}")
                return True
            else:
                error_msg = response.json().get('message', 'Unknown error') if response.content else f"HTTP {response.status_code}"
                logger.error(f"Failed to upload module {module_name} v{module_version}: {error_msg}")
                print(f"Failed to upload module: {error_msg}")
                return False
