#!/usr/bin/env bash

show_help() {
    echo "Will poll the url for a response code."
    echo "Usage: $0 <REVIEW_APP_URL>"
    echo "  <REVIEW_APP_URL>  The URL of the review app."
    echo
    echo "  If http_code == 000, the DNS hostname is not provisioned and will wait 60 seconds to touch that url endpoint again"
    echo "  If http_code == 200, the DNS hostname is provisioned and will succeed the job"

}

# Show help if no arguments provided
if [[ "${1:-}" == "-h" ]] || [[ "${1:-}" == "--help" ]] || [[ "${1:-}" == "help" ]] || [[ -z "${1:-}" ]]; then
    show_help
    exit 1
fi

# Variables
readonly REVIEW_APP_URL=$1
readonly TXT_RED='\e[31m'
readonly TXT_GREEN='\e[32m'
readonly TXT_YELLOW='\e[33m'
readonly TXT_BLUE='\e[34m'
readonly TXT_CLEAR='\e[0m'

log() {
    local level=$1
    shift
    echo -e "${level}$*${TXT_CLEAR}"
}

function poll_site_for_statuscode() {
    log "${TXT_BLUE}" "Begin polling against $REVIEW_APP_URL"
    while true; do
        # Capture both stdout and stderr, and the exit code
        response=$(curl --write-out '%{http_code}' --silent --output /dev/null ${REVIEW_APP_URL} 2>&1)
        exit_code=$?

        # Check for SSL handshake failure (exit code 35)
        if [[ $exit_code -eq 35 ]] || [[ $response == *"SSL"*handshake*failure* ]] || [[ $response == 000 ]]; then
            log "${TXT_YELLOW}" "SSL certificate not ready yet (handshake failure)..."
            sleep 60
        elif [[ $response == 200 ]]; then
            log "${TXT_GREEN}" "DNS Hostname certificate has been provisioned."
            break
        else
            log "${TXT_RED}" "============================================================"
            log "${TXT_RED}" "Response code: $response"
            log "${TXT_RED}" "Exit code: $exit_code"
            log "${TXT_RED}" "Failure occurred provisioning DNS Hostname certificate."
            log "${TXT_RED}" "============================================================"
            sleep 60
        fi
    done
    sleep 120 # To give the site an extra 2 minutes to warm up prior to starting the test jobs
    log "${TXT_BLUE}" "Review-app is ready at ${REVIEW_APP_URL}"
}

poll_site_for_statuscode
