#!/usr/bin/env bash

# Set strict error handling
set -euo pipefail

# Constants and defaults (these should be readonly)
readonly REVIEW_APP_ENVIRONMENT=${1:-}
readonly GITLAB_API_URL="https://gitlab.com/api/v4"
readonly GITLAB_API_TOKEN=${GITLAB_API_TOKEN:-$GROUP_ACCESS_TOKEN}
readonly GITLAB_PROJECT_ID=${GITLAB_PROJECT_ID:-${CI_PROJECT_ID:-"65672853"}}

# Variables that will change (these should not be readonly)
DRY_RUN=false
WAIT_TIME=5
POSITIONAL_ARGS=()

# Display help information
show_help() {
    cat <<EOF
Usage: $(basename "$0") [OPTIONS] <environment_name>

Delete a GitLab environment after stopping it.

Arguments:
    environment_name    Name of the GitLab environment (e.g., review/feature-branch)

Options:
    -h, --help               Show this help message
    -d, --dry-run            Show what would be done without actually doing it
    -t, --token=TOKEN        GitLab API token (or use GITLAB_API_TOKEN env var)
    -p, --project-id=ID      GitLab project ID (or use GITLAB_PROJECT_ID env var)
    -w, --wait=SECONDS       Wait time between stop and delete (default: 5)
    -u, --api-url=URL        GitLab API URL (default: https://gitlab.com/api/v4)

Example:
    $(basename "$0") 
    $(basename "$0") --dry-run
    $(basename "$0") --token=glpat-xxxx --project-id=123
EOF
}

# Function to make API calls
make_api_call() {
    local method=$1
    local endpoint=$2
    local curl_cmd="curl -s -X ${method} --header \"PRIVATE-TOKEN: ${GITLAB_API_TOKEN}\" \"${endpoint}\""

    if [ "$DRY_RUN" = true ]; then
        echo "[DRY RUN] Would execute: ${curl_cmd}"
        return 0
    else
        eval "${curl_cmd}"
        return $?
    fi
}

# Parse command line options
while [[ $# -gt 0 ]]; do
    case $1 in
    -h | --help)
        show_help
        exit 0
        ;;
    -d | --dry-run)
        DRY_RUN=true
        shift
        ;;
    -t | --token)
        GITLAB_API_TOKEN="$2"
        shift 2
        ;;
    --token=*)
        GITLAB_API_TOKEN="${1#*=}"
        shift
        ;;
    -p | --project-id)
        GITLAB_PROJECT_ID="$2"
        shift 2
        ;;
    --project-id=*)
        GITLAB_PROJECT_ID="${1#*=}"
        shift
        ;;
    -w | --wait)
        WAIT_TIME="$2"
        shift 2
        ;;
    --wait=*)
        WAIT_TIME="${1#*=}"
        shift
        ;;
    -u | --api-url)
        GITLAB_API_URL="$2"
        shift 2
        ;;
    --api-url=*)
        GITLAB_API_URL="${1#*=}"
        shift
        ;;
    -* | --*)
        echo "Error: Unknown option $1"
        exit 1
        ;;
    *)
        POSITIONAL_ARGS+=("$1")
        shift
        ;;
    esac
done

# Restore positional parameters
set -- "${POSITIONAL_ARGS[@]}"

if [ -z "$GITLAB_API_TOKEN" ]; then
    echo "Error: GitLab API token is required. Provide it via --token or GITLAB_API_TOKEN environment variable"
    exit 1
fi

if [ -z "$GITLAB_PROJECT_ID" ]; then
    echo "Error: GitLab project ID is required. Provide it via --project-id or GITLAB_PROJECT_ID environment variable"
    exit 1
fi

if [ -z "$REVIEW_APP_ENVIRONMENT" ]; then
    echo "Error: GitLab environment name is required. Provide it via REVIEW_APP_ENVIRONMENT environment variable"
    exit 1
fi

API_ENDPOINT="${GITLAB_API_URL}/projects/${GITLAB_PROJECT_ID}/environments"

echo "Looking up environment: ${REVIEW_APP_ENVIRONMENT}"
ENVIRONMENT_QUERY="${API_ENDPOINT}?name=$(echo ${REVIEW_APP_ENVIRONMENT} | jq -Rr @uri)"
ENVIRONMENT=$(make_api_call "GET" "${ENVIRONMENT_QUERY}" | jq '.[]')

if [ -z "$ENVIRONMENT" ] || [ "$ENVIRONMENT" = "null" ]; then
    echo "Environment not found: ${REVIEW_APP_ENVIRONMENT}"
    exit 0
fi

ENVIRONMENT_ID=$(echo "${ENVIRONMENT}" | jq -r '.id')
echo "Found environment ID: ${ENVIRONMENT_ID}"

# Stop the environment
echo "Stopping environment..."
make_api_call "POST" "${API_ENDPOINT}/${ENVIRONMENT_ID}/stop"

if [ "$DRY_RUN" = false ]; then
    echo "Waiting ${WAIT_TIME} seconds before deletion..."
    sleep "${WAIT_TIME}"
fi

# Delete the environment
echo "Deleting environment..."
DELETE_RESULT=$(make_api_call "DELETE" "${API_ENDPOINT}/${ENVIRONMENT_ID}")

if [ $? -eq 0 ]; then
    echo "GitLab environment deleted successfully"
else
    echo "Error: Failed to delete environment ${REVIEW_APP_ENVIRONMENT}"
    exit 1
fi
