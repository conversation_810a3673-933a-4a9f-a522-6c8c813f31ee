#!/usr/bin/env bash

set -e

# =========================================
# Load Configurations
# =========================================

REPO_ROOT=$(git rev-parse --show-toplevel)
UTILITY_SCRIPT=${REPO_ROOT}/scripts/utils.sh

ACTION=""
ENVIRONMENT=""
OPTIONS=""
CONFIG=""

# Source utility script
if [[ -f "$UTILITY_SCRIPT" ]]; then
  # shellcheck source=scripts/utils.sh
  source "$UTILITY_SCRIPT"
  check_bash_version
else
  echo "Required file $UTILITY_SCRIPT not found. Exiting."
  exit 1
fi

# =========================================
# Print Usage/Help
# =========================================

show_help() {
  log_heredoc "${TXT_GREEN}" <<EOF
  ${TXT_YELLOW}Usage:${TXT_CLEAR} 
    $0 [OPTIONS]

  ${TXT_YELLOW}Note:${TXT_CLEAR} 
    Options are positional arguments.

  ${TXT_YELLOW}Options:${TXT_CLEAR}
    ACTION:       plan, apply, destroy
    ENVIRONMENT:  dev, stage, prod, ops, tableau
    CONFIG:       ${REPO_ROOT}/scripts/configs/${ENVIRONMENT}-deployment-order.conf
    -h, --help    Show this help message and exit

  ${TXT_YELLOW}Examples:${TXT_CLEAR}
  ./scripts/terragrunt.sh plan --environment dev
  ./scripts/terragrunt.sh apply --environment stage
  ./scripts/terragrunt.sh destroy --environment prod
  ./scripts/terragrunt.sh plan --environment ops
  ./scripts/terragrunt.sh apply --environment tableau

EOF
}

if [[ $# -eq 0 ]]; then
  show_help
  exit 1
fi

while [[ $# -gt 0 ]]; do
  case $1 in
  plan | apply | destroy)
    ACTION=$1
    shift
    ;;
  --environment)
    ENVIRONMENT=$2
    shift 2
    ;;
  --config)
    CONFIG=$2
    shift 2
    ;;
  --options)
    OPTIONS=$2
    shift 2
    ;;
  -h | --help)
    show_help
    exit 1
    ;;
  *)
    echo "Unknown option: $1"
    exit 1
    ;;
  esac
done

CONFIG=${CONFIG:-${REPO_ROOT}/scripts/configs/${ENVIRONMENT}-deployment-order.conf}

if [[ -z "$ACTION" ]]; then
  echo "Error: ACTION is not set"
  exit 1
fi

if [[ -z "$ENVIRONMENT" ]]; then
  echo "Error: ENVIRONMENT is not set"
  exit 1
fi

# Validate config file exists and has content
if [[ ! -f "$CONFIG" ]]; then
  echo "Error: Config file not found: $CONFIG"
  exit 1
else
  # shellcheck source=scripts/configs/dev-deployment-order.conf
  source "$CONFIG"

  # Validate that variables were properly loaded
  if [[ -z "$BASE_DIR" ]]; then
    echo "Error: BASE_DIR not set in config file: $CONFIG"
    exit 1
  fi

  if [[ ${#ORDER[@]} -eq 0 ]]; then
    echo "Error: ORDER array is empty in config file: $CONFIG"
    exit 1
  fi
fi

# =========================================
# Main Entrypoint
# =========================================

main() {
  pushd $REPO_ROOT/${BASE_DIR} >/dev/null

  for module in "${ORDER[@]}"; do
    echo "Running terragrunt plan for ${module}"
    pushd ${module} >/dev/null
    terragrunt $ACTION $OPTIONS
    popd >/dev/null
  done

  popd >/dev/null
}

main "$@"
