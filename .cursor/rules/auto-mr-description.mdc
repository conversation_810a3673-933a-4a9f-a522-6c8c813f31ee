---
description: This rule provides instructions for automatically generating merge request descriptions
globs: ["**/*"]
alwaysApply: false
---

# Auto Merge Request Description Generator

## Trigger

When the user pushes their branch to remote, automatically generate a merge request description comment.

## Instructions

When the user pushes their feature branch to remote:

1. **Analyze the current branch and recent changes:**
   - Review the current branch name and recent commits
   - Identify modified files and their purposes
   - Understand the scope of changes made

2. **Select and use the appropriate MR template:**
   - Scan the `.gitlab/merge_request_templates/` directory for available templates
   - Analyze branch name, commit messages, and changes to determine MR type:
     - Feature branches (feature/, feat/, ICT-*, new functionality) → Feature.md
     - Bug fixes (bugfix/, fix/, hotfix/, bug-related changes) → appropriate template
     - Documentation changes (docs/, documentation updates) → appropriate template
     - Infrastructure/config changes → appropriate template
   - Read the selected template file to get the latest structure
   - Populate each section with relevant, specific information about the changes
   - Replace placeholder text with actual details about this branch

## Guidelines

- **Be concise and specific** - focus only on this feature branch's changes
- **Avoid generic fluff** - don't add boilerplate text
- **Stay in scope** - only describe what was actually changed
- **Make it actionable** - testing instructions should be clear and specific
- **Format for copy-paste** - ensure the output is clean markdown that can be directly copied into GitLab

## Output Format

Present the generated description as a markdown code block that the user can easily copy and paste into their GitLab merge request description field. Include a brief note about which template was selected and why.
