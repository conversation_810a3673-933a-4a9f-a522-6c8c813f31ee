# Infrastructure Architecture

Control Tower's infrastructure is built on Google Cloud Platform using Infrastructure as Code principles with Terraform and <PERSON>grunt for deployment automation and environment management.

## Overview

The infrastructure follows a modular, reusable design that supports multiple environments and deployment scenarios while maintaining consistency and reliability across all deployments.

### Technology Stack

- **Cloud Provider**: Google Cloud Platform (GCP)
- **Infrastructure as Code**: Terraform (OpenTofu binary) + Terragrunt
- **Container Orchestration**: Google Cloud Run
- **Databases**: Cloud SQL (PostgreSQL)
- **Caching**: Redis (Cloud Memorystore)
- **Load Balancing**: Cloud Load Balancer
- **DNS**: Cloud DNS
- **Security**: Cloud IAM, Identity-Aware Proxy, Cloud Armor
- **Artifact Management**: Artifact Registry
- **Secret Management**: Secret Manager
- **Monitoring**: Cloud Monitoring

## Repository Structure

### Environment Structure

```bash
environments
├── dematicnonprod/                                    # Non-production environments
│   ├── dev/                                           # Development environment
│   │   ├── environment.hcl                            # Development environment configuration
│   │   ├── environment.variables.tf                   # Development environment variables
│   │   ├── ict-np-us1-dev/                            # Development instance
│   │   │   ├── instance.hcl                           # Instance configuration
│   │   │   ├── instance.variables.tf                  # Instance variables
│   │   │   ├── project/                               # Project-level resources
│   │   │   └── resources/                             # Resource-level configurations
│   │   └── README.md                                  # Development environment documentation
│   ├── lz.hcl                                         # Landing zone configuration
│   ├── lz.variables.tf                                # Landing zone variables
│   ├── README.md                                      # Non-production environment documentation
│   └── stage/                                         # Staging environment
│       ├── environment.hcl                            # Staging environment configuration
│       ├── environment.variables.tf                   # Staging environment variables
│       ├── ict-np-us1-stage/                          # Staging instance
│       │   ├── instance.hcl                           # Instance configuration
│       │   ├── instance.variables.tf                  # Instance variables
│       │   ├── project/                               # Project-level resources
│       │   └── resources/                             # Resource-level configurations
│       └── README.md                                  # Staging environment documentation
├── dematicops/                                        # Operations/shared services environment
│   ├── ict-o-common-services/                         # Common services instance
│   │   ├── instance.hcl                               # Instance configuration
│   │   ├── instance.variables.tf                      # Instance variables
│   │   ├── project/                                   # Project-level resources
│   │   │   ├── backend.tf                             # Backend configuration
│   │   │   ├── dependencies.tf                        # Module dependencies
│   │   │   ├── iam.tf                                 # IAM policies and roles
│   │   │   ├── locals.tf                              # Local variables
│   │   │   ├── main.tf                                # Main resource definitions
│   │   │   ├── outputs.tf                             # Output values
│   │   │   ├── provider.tf                            # Provider configuration
│   │   │   ├── terragrunt.hcl                         # Terragrunt configuration
│   │   │   └── variables.tf                           # Input variables
│   │   └── resources/                                 # Resource-level configurations
│   │       ├── backend.tf                             # Backend configuration
│   │       ├── dependencies.tf                        # Module dependencies
│   │       ├── locals.tf                              # Local variables
│   │       ├── main.tf                                # Main resource definitions
│   │       ├── outputs.tf                             # Output values
│   │       ├── provider.tf                            # Provider configuration
│   │       ├── terragrunt.hcl                         # Terragrunt configuration
│   │       └── variables.tf                           # Input variables
│   ├── lz.hcl                                         # Landing zone configuration
│   ├── lz.variables.tf                                # Landing zone variables
│   └── README.md                                      # Operations environment documentation
├── dematicprod/                                       # Production environments
│   ├── lz.hcl                                         # Landing zone configuration
│   ├── lz.variables.tf                                # Landing zone variables
│   ├── prod/                                          # Production environment
│   │   ├── environment.hcl                            # Production environment configuration
│   │   ├── environment.variables.tf                   # Production environment variables
│   │   ├── ict-p-us1/                                 # Production instance
│   │   │   ├── instance.hcl                           # Instance configuration
│   │   │   ├── instance.variables.tf                  # Instance variables
│   │   │   ├── project/                               # Project-level resources
│   │   │   └── resources/                             # Resource-level configurations
│   │   └── README.md                                  # Production environment documentation
│   └── README.md                                      # Production environment overview
├── global.hcl                                         # Global configuration settings
├── global.variables.tf                                # Global variables
├── README.md                                          # Environments documentation
└── root.hcl                                           # Root configuration
```

### Infrastructure Structure

```bash
infrastructure
├── examples/                                        # Generated example configurations
│   ├── common/                                      # Common service examples
│   │   └── common-services/                         # Common services usage examples
│   ├── data/                                        # Data module examples
│   │   └── dematic-domains/                         # Domain data usage examples
│   └── instance/                                    # Instance module examples
│       ├── api-backend/                             # API backend usage examples
│       ├── api-load-balancer/                       # API load balancer examples
│       ├── ignition-proxy/                          # Ignition proxy usage examples
│       ├── metric-processor/                        # Metric processor examples
│       ├── postgresql/                              # PostgreSQL usage examples
│       ├── redis/                                   # Redis usage examples
│       ├── scaffolding/                             # Scaffolding usage examples
│       ├── site-asset-backend/                      # Site asset backend examples
│       └── ui-load-balancer/                        # UI load balancer examples
└── modules/                                         # Reusable Terraform modules
    ├── common/                                      # Shared services modules
    │   ├── common-services/                         # Common services module
    │   └── README.md                                # Common modules documentation
    ├── data/                                        # Data-related modules
    │   ├── dematic-domains/                         # Domain data module
    │   └── README.md                                # Data modules documentation
    ├── instance/                                    # Instance-specific modules
    │   ├── api-backend/                             # API backend services module
    │   ├── api-load-balancer/                       # API load balancer module
    │   ├── ignition-proxy/                          # Ignition SCADA proxy module
    │   ├── metric-processor/                        # Metric processor module
    │   ├── postgresql/                              # PostgreSQL database module
    │   ├── redis/                                   # Redis cache module
    │   ├── scaffolding/                             # Scaffolding module
    │   ├── site-asset-backend/                      # Site asset backend module
    │   └── ui-load-balancer/                        # UI load balancer module
    └── README.md                                    # Infrastructure modules overview
```

## Infrastructure Modules

### Common Modules

#### common-services

- **Purpose**: Shared services across all environments
- **Components**:
  - DNS management (non-prod and prod zones)
  - Artifact Registry repositories (Docker, generic)

### Instance Modules

#### api-backend

- **Purpose**: Backend API service deployment
- **Features**:
  - Auto-scaling Cloud Run services
  - Health monitoring and alerting
  - Load balancing with Cloud Armor
  - VPC access connector for database connectivity
  - Storage-enabled backend services
  - Service account management

#### api-load-balancer

- **Purpose**: API load balancer configuration
- **Features**:
  - Load balancing for API services
  - Health checks and monitoring
  - Traffic distribution
  - Integration with backend services

#### ui-load-balancer

- **Purpose**: Frontend application load balancing
- **Features**:
  - HTTPS termination with SSL certificate management
  - CDN integration (not yet implemented)
  - Load balancing with health checks
  - Resource lifecycle management for SSL certificates

#### postgresql

- **Purpose**: Managed PostgreSQL database
- **Features**:
  - High availability configuration
  - Automated nightly backups to GCS
  - TypeORM migrations Cloud Run service
  - Secret Manager integration for connection info
  - Multi-tenant database support

#### redis

- **Purpose**: Managed Redis cache (Google Memorystore)
- **Features**:
  - Memory optimization
  - Secret Manager integration for connection info
  - Network security with authorized networks

#### ignition-proxy

- **Purpose**: Proxy for Ignition SCADA systems
- **Features**:
  - NGINX-based proxy service
  - Managed Instance Group for high availability
  - Cloud Armor security policy integration
  - Multi-zone deployment for scalability
  - Health checks and monitoring

#### metric-processor

- **Purpose**: Metric processing service deployment
- **Features**:
  - Cloud Run service for metric processing
  - Auto-scaling configuration
  - Integration with data pipelines

#### site-asset-backend

- **Purpose**: Static site asset serving
- **Features**:
  - Cloud Run service for static assets
  - CDN integration (not yet implemented)
  - Asset management

#### scaffolding

- **Purpose**: Infrastructure scaffolding and setup
- **Features**:
  - Initial environment setup
  - Resource scaffolding
  - Development environment preparation
  - Template-based resource creation

### Data Modules

#### dematic-domains

- **Purpose**: Data sources for root DNS managed zones
- **Features**:
  - References to existing DNS zones in the horizon-278415 project
  - Supports dev/prod and alternative zone configurations
  - No resource creation (data-only module)

## Environment Management

### Structure

Each environment follows a consistent structure with:

- `environment.hcl` for configuration
- `project/` for GCP project setup
- `resources/` for application resources

### Environment Types

- **Development** (`dematicnonprod/dev`): Development and testing environment
- **Staging** (`dematicnonprod/stage`): Pre-production testing environment  
- **Production** (`dematicprod/prod`): Live production environment
- **Operations** (`dematicops`): Shared services and operations

### Configuration Management

Configuration is managed through Terragrunt with:

- **Global settings** in `environments/global.hcl`
- **Environment-specific settings** in each environment's
