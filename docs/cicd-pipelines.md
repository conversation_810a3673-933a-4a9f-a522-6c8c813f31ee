# CI/CD Pipelines

Control Tower uses GitLab CI/CD for automated testing, building, and deployment across all environments.

## Overview

The CI/CD system is designed to:

- **Automate Testing**: Run comprehensive test suites on every change
- **Ensure Quality**: Enforce code standards and security checks
- **Enable Safe Deployments**: Provide controlled, auditable deployments
- **Support Multiple Environments**: Handle dev, staging, and production deployments
- **Manage Infrastructure**: Automate Terraform deployments with proper approvals

## Pipeline Architecture

### GitLab CI Structure

The CI/CD configuration is organized in the `.gitlab/ci/` directory:

```bash
.gitlab/ci
├── api                    # API-specific pipelines
│   └── api-build.gitlab-ci.yml
├── debug-jobs.gitlab-ci.yml
├── includes               # Shared pipeline components
│   └── shared.gitlab-ci.yml
├── infrastructure         # Infrastructure pipelines
│   ├── environments-infrastructure.gitlab-ci.yml
│   └── terraform-module-publishing.gitlab-ci.yml
├── mr-labels.gitlab-ci.yml
├── review-apps.gitlab-ci.yml
├── sast                   # Security scanning pipelines
│   ├── sast-base.gitlab-ci.yml
│   └── veracode.gitlab-ci.yml
├── service-release.gitlab-ci.yml
├── static-analysis.gitlab-ci.yml
├── tests                  # Testing pipelines
│   ├── e2e-api.gitlab-ci.yml
│   ├── e2e-ui-base.gitlab-ci.yml
│   ├── e2e-ui-review-apps.gitlab-ci.yml
│   └── e2e-ui.gitlab-ci.yml
└── ui                     # UI-specific pipelines
    └── ui-build.gitlab-ci.yml
```

### Pipeline Types

#### 1. Application Pipelines

- **API Services**: Build, test, and deploy microservices
- **UI Applications**: Build, test, and deploy frontend applications

#### 2. Infrastructure Pipelines

- **Terraform Modules**: Validate, test, and publish modules
- **Environment Deployments**: Deploy infrastructure changes

#### 3. Quality Assurance Pipelines

- **Static Analysis**: Code quality, security scanning, linting
- **Unit Tests**: Component-level testing
- **E2E Tests**: Full workflow testing

## Pipeline Stages

### Standard Pipeline Flow

```mermaid
graph LR
    A[Validate] --> B[Build]
    B --> C[Test]
    C --> D[Package]
    D --> E[Deploy Dev]
    E --> F[Deploy Staging]
    F --> G[Deploy Production]
```

### Stage Details

#### 1. Validate

- **Code Quality**: ESLint, Prettier, TypeScript compilation
- **Security Scanning**: Dependency vulnerability checks
- **Terraform Validation**: HCL format, syntax validation

#### 2. Build

- **API Services**: TypeScript compilation, Docker image creation
- **UI Applications**: Vite build process, asset optimization
- **Infrastructure**: Terraform planning and validation

#### 3. Test

- **Unit Tests**: Jest (API), Vitest (UI)
- **E2E Tests**: Playwright-based workflow testing

#### 4. Package

- **Docker Images**: Build and tag container images
- **Artifacts**: Package application artifacts
- **Terraform Modules**: Package modules for deployment

#### 5. Deploy

- **Development**: Automatic deployment on merge to `dev`
- **Staging**: Automatic deployment on schedule in pipeline Schedules [ict-orchestrator](https://gitlab.com/dematic/controltower/ict-orchestrator)
- **Production**: Manual trigger with multiple approvals

## Environment-Specific Pipelines

### Development Environment

- **Trigger**: Automatic on merge to `dev` branch
- **Approval**: Managed by CODEOWNERS

### Staging Environment

- **Trigger**: Scheduled and automatic after dev success
- **Approval**: Automatic approval by gitlab bot
- **Testing**: Full integration test suite

### Production Environment

- **Trigger**: Manual after staging validation
- **Approval**: Single approvers required

## Infrastructure Pipeline

### Terraform Module Publishing

```yaml
# Example pipeline configuration
terraform-module-publishing:
  stage: infrastructure
  script:
    - ./scripts/terraform-modules/publish.py
  only:
    - merge_requests
```

### Environment Deployment

```yaml
# Example deployment configuration
deploy-infrastructure:
  stage: deploy
  script:
    - ./scripts/terragrunt.sh apply --environment $ENVIRONMENT
```

## Security and Compliance

### Security Scanning

- **SAST**: Static Application Security Testing with Veracode and Sonarqube

## Best Practices

### Pipeline Design

1. **Keep pipelines fast**: Optimize build and test times
2. **Fail fast**: Run critical checks early in the pipeline
3. **Parallel execution**: Run independent jobs in parallel
4. **Caching**: Cache dependencies and build artifacts (Not available)
5. **Job Queuing**: Queue jobs that require being queued using `resource_group` attributes

### Security

1. **Least privilege**: Use minimal required permissions
2. **Secret management**: Use GitLab CI/CD variables for secrets
3. **Access control**: Restrict pipeline access to authorized users

### Quality Assurance

1. **Automated testing**: Comprehensive test coverage
2. **Code review**: Require peer review for all changes
3. **Static analysis**: Automated code quality checks
4. **Performance testing**: Regular performance validation

## Resources

- **GitLab CI/CD Documentation**: <https://docs.gitlab.com/ee/ci/>
- **Pipeline Configuration**:
  - `.gitlab-ci.yml`: Main CI file
  - `.gitlab/ci/**/*.gitlab-ci.yml`: Conditionally included CI files
- **Infrastructure Scripts**: `scripts/` directory
- **Environment Configurations**: `environments/` directory
