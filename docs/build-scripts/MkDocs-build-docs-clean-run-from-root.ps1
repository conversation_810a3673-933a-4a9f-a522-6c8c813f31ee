# This PowerShell script is used to test comprehensive changes by running a full build 
# when committing files to production. It requires a Python virtual environment named 
# "venv" directly under the top-level directory of this project. Run the script in PS 
# with this command: ./MkDocs-build-docs-clean.ps1.

# The script builds and serves a MkDocs site locally at the URL http://127.0.0.1:8000/. 
# To stop the script, use CTRL+C. To run the script, use the 
# command: ./MkDocs-build-docs-clean-run-from-root.ps1.

# This section prints a message and then lists the contents of the current directory. This is useful for debugging and understanding the current working directory.
Write-Output "This is the contents of the current directory..."
Get-ChildItem

# This section prints a message and then builds the MkDocs site.
# The '--clean' option removes the 'site' directory before building. This ensures a fresh build every time.
# The 'config-mkdocs.yml' file is used as the configuration file for MkDocs. This file contains settings for the MkDocs build.
Write-Output "Building the doc site..."
mkdocs -q build --clean --config-file='mkdocs.yml'

Write-Output "Serving the doc site..."

# This commented line is a verbose version of the above command for debugging. Uncomment this line if you need more detailed output from the MkDocs build.
#mkdocs build --clean --verbose --config-file='config-mkdocs.yml'

# This line serves the MkDocs site locally.
# The '--clean' option removes the 'site' directory before serving. This ensures that the served site is up-to-date.
# The 'config-mkdocs.yml' file is used as the configuration file for MkDocs.
# The '--watch' option makes MkDocs watch the 'mkdocs.yml' file for changes and rebuild the site automatically when changes are detected.
mkdocs serve --clean --config-file='mkdocs.yml' --watch='mkdocs.yml'