#!/bin/bash

# This Bash script is used to test comprehensive changes by running a full build
# when committing files to production. It requires a Python virtual environment named
# "venv" located directly under the top-level directory of this project.

# To run the script in Bash, use this command: ./MkDocs-build-docs-clean-run-from-root.sh.

# The script builds and serves a MkDocs site locally at the URL http://127.0.0.1:8000/. 
# To stop the script, use CTRL+C.

# This section prints a message and then lists the contents of the current directory.
# It's useful for debugging and understanding the current working directory. 
echo "This is the contents of the current directory..." 
ls

# This section prints a message and then builds the MkDocs site.
# The '--clean' option removes the 'site' directory before building. This ensures a fresh build every time.
# The 'mkdocs.yml' file is used as the configuration file for MkDocs. This file contains settings for the MkDocs build.
echo "Building the doc site..."
mkdocs -q build --clean --config-file='mkdocs.yml'

echo "Serving the doc site..."

# This commented line is a verbose version of the above command for debugging.
# Uncomment this line if you need more detailed output from the MkDocs build.
#mkdocs build --clean --verbose --config-file='mkdocs.yml'

# This line serves the MkDocs site locally.
# The '--clean' option removes the 'site' directory before serving. This ensures that the served site is up-to-date.
# The 'mkdocs.yml' file is used as the configuration file for MkDocs.
# The '--watch' option makes MkDocs watch the 'mkdocs.yml' file for changes and rebuild the site automatically when changes are detected.
mkdocs serve --clean --config-file='mkdocs.yml' --watch='mkdocs.yml'