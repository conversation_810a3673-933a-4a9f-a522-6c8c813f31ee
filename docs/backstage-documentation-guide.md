# Backstage Documentation Guide

This guide explains how to update and maintain documentation in the Control Tower backstage instance.

---

## Overview

Control Tower uses [Backstage](https://backstage.ops.dematic.dev/) as its developer portal and documentation platform. This guide covers how to add, update, and maintain documentation within our backstage instance.

## Backstage Instance

Our backstage instance is hosted at: **<https://backstage.ops.dematic.dev/>**

This is the central hub for all Control Tower documentation, service catalogs, and developer resources.

## Documentation Structure

### Repository Location

All backstage documentation is stored in the `docs/` directory of the control-tower repository.

### Directory Structure

```bash
docs/
├── api-services.md                                   # API services overview
├── assets/                                           # Static assets (images, icons)
├── backstage-documentation-guide.md                  # This guide
├── build-scripts/                                    # Build and deployment scripts
│   ├── MkDocs-build-docs-clean-run-from-root.ps1     # PowerShell script for Windows
│   └── MkDocs-build-docs-clean-run-from-root.sh      # Bash script for Unix/Linux
├── cicd-pipelines.md                                 # CI/CD pipeline documentation
├── development-guide.md                              # Development guide
├── diagrams/                                         # PlantUML diagrams and tools
│   ├── plantuml.bat                                  # Windows PlantUML launcher
│   ├── plantuml.jar                                  # PlantUML JAR file
│   └── Readme.md                                     # Diagrams documentation
├── environments.md                                   # Environment information
├── images/                                           # Documentation images
│   └── Readme.md                                     # Images documentation
├── infrastructure/                                   # Infrastructure documentation
│   ├── modules/                                      # Terraform module documentation
│   │   ├── api-backend.md                            # API backend module docs
│   │   ├── ignition-proxy.md                         # Ignition proxy module docs
│   │   ├── postgresql.md                             # PostgreSQL module docs
│   │   ├── redis.md                                  # Redis module docs
│   │   └── ui-load-balancer.md                       # UI load balancer module docs
│   └── modules-overview.md                           # Infrastructure modules overview
├── infrastructure.md                                 # Infrastructure documentation
├── md-section-index-files/                           # MkDocs section index files
│   └── index.md                                      # Section index template
├── mkdocs-requirements.txt                           # MkDocs Python dependencies
├── README.md                                         # Home page (pulls from root README)
├── services/                                         # Service documentation
│   ├── api-backend.md                                # API backend service
│   ├── api-e2e-testing.md                            # API E2E testing
│   ├── api-services/                                 # Individual API service docs
│   │   ├── admin-api.md                              # Admin API documentation
│   │   ├── ai-api.md                                 # AI API documentation
│   │   ├── config-api.md                             # Config API documentation
│   │   ├── dataexplorer-api.md                       # Data Explorer API documentation
│   │   ├── diagnostics-api.md                        # Diagnostics API documentation
│   │   ├── equipment-api.md                          # Equipment API documentation
│   │   ├── instrumentation.md                        # Instrumentation documentation
│   │   ├── inventory-api.md                          # Inventory API documentation
│   │   ├── mock-data-api.md                          # Mock Data API documentation
│   │   ├── operators-api.md                          # Operators API documentation
│   │   ├── orders-api.md                             # Orders API documentation
│   │   ├── simulation-api.md                         # Simulation API documentation
│   │   ├── tableau-management-api.md                 # Tableau Management API documentation
│   │   ├── tableau-proxy.md                          # Tableau Proxy documentation
│   │   ├── typeorm-migrations-cr.md                  # TypeORM Migrations API documentation
│   │   └── workstation-api.md                        # Workstation API documentation
│   ├── metric-processor.md                           # Metric processor service
│   ├── ui-e2e-testing.md                             # UI E2E testing
│   └── ui-frontend.md                                # UI frontend service
├── static/                                           # Static files for documentation
│   └── Readme.md                                     # Static files documentation
├── stylesheets/                                      # Custom CSS styles
│   └── extra.css                                     # Additional CSS styles
└── terraform-module-development.md                   # Terraform module development guide
```

**Update navigation**: Add the new service to the navigation in `mkdocs.yml`

   ```yaml
   - API Services:
       - Your New Service: services/api-services/your-new-service.md
   ```

## Create snippet to link to service documentation

   `--8<-- "services/api/apps/api/your-new-service/README.md"`

## Adding New Documentation

### For New API Services

1. **Create service README**: Write comprehensive documentation in the service directory
2. **Create snippet in markdown file**: Link it to backstage documentation
3. **Update navigation**: Add to mkdocs.yml navigation
4. **Test locally**: Build and test the documentation locally

### For New Documentation Pages

1. **Create the markdown file**: Add it to the appropriate directory in `docs/`
2. **Update navigation**: Add it to the `mkdocs.yml` navigation structure
3. **Follow naming conventions**: Use kebab-case for filenames
4. **Include proper frontmatter**: Add any necessary metadata

### Navigation Structure

The navigation is defined in `mkdocs.yml`:

```yaml
nav:
  - Control Tower: README.md
  - Development:
      - Development Guide: development-guide.md
      - API Services Architecture: api-services.md
      - Backstage Documentation Guide: backstage-documentation-guide.md
  - Services:
      - API Backend: services/api-backend.md
      - UI Frontend: services/ui-frontend.md
      - API Services:
          - Admin API: services/api-services/admin-api.md
          # ... other services
```

## Local Development and Testing

### Building Documentation Locally

```bash
# Install mkdocs and dependencies
cd docs/

# If first time...this is a one time thing per mise python creation 
# mise will automatically setup the python environment, but you need to
# Install dependencies, trust the file, and then navigate away and then back 
# again to create the environment with mise.
mise trust -y && mise install -y && cd - && cd -

# Else if you have already run mise install and have the python environment
# From the root of the repository, simply run the below command to 
# work on backstage documentation for control tower.
./MkDocs-build-docs-clean-run-from-root.sh
```

### Testing Changes

1. **Build locally**: Run `./MkDocs-build-docs-clean-run-from-root.sh` to check for errors
2. **Check navigation**: Verify all links work correctly

### Common Issues

**Navigation broken**: Verify the file path in mkdocs.yml matches the actual file location
**Build errors**: Check markdown syntax and ensure all referenced files exist

## Best Practices

### Documentation Standards

1. **Consistent Structure**: Follow the established pattern for service documentation
2. **Clear Headers**: Use proper markdown headers (# for main title, ## for sections)
3. **Code Examples**: Include relevant code examples and API endpoints

### File Organization

1. **Service documentation**: Keep service README files in their respective directories
2. **General documentation**: Place general documentation in `docs/`
3. **Consistent naming**: Use consistent naming conventions across all documentation

## Deployment

### Automatic Deployment

Documentation is automatically deployed when changes are pushed to the main branch. The backstage instance pulls from the repository and rebuilds the documentation.

## Resources

- **Backstage Instance**: <https://backstage.ops.dematic.dev/>
- **MkDocs Documentation**: <https://www.mkdocs.org/>
- **Material for MkDocs**: <https://squidfunk.github.io/mkdocs-material/>
- **Markdown Guide**: <https://www.markdownguide.org/>

## Support

For issues with backstage documentation:

1. **Check this guide**: Review the troubleshooting section
2. **Test locally**: Build and test documentation locally
3. **Contact team**: Reach out to the development team for assistance
