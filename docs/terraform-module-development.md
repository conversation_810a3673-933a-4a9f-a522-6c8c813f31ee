# Terraform Module Development

This guide covers the process of developing, documenting, and publishing Terraform modules in Control Tower.

## Overview

Control Tower uses a standardized approach for Terraform module development with automated documentation generation and publishing to GitLab registry.

## Module Structure

### Standard Module Layout

```bash
infrastructure/modules/category/module-name/
├── .terraform-docs.yml          # Documentation configuration
├── .terraform-example.yml       # Example generation config
├── .tfvars-docs.yml             # Variable documentation config
├── templates/
│   └── header.md                # Custom module documentation
├── main.tf                      # Main Terraform configuration
├── variables.tf                 # Input variables
├── outputs.tf                   # Output values
└── README.md                    # Auto-generated documentation
```

### Example Structure

```bash
infrastructure/examples/category/module-name/
└── main.tf                      # Auto-generated usage example
```

## Development Workflow

### 1. Create New Module

1. **Add Module Directory**: Create new module in `infrastructure/modules/`
2. **Copy Templates**: Copy configuration files from `scripts/terraform-docs-templates/`
3. **Implement Module**: Write Terraform configuration files
4. **Generate Documentation**: Run documentation generation script

### 2. Documentation Generation

```bash
# Generate documentation for all modules
./scripts/generate-terraform-docs.sh

# Copy templates to new module (for new module setup)
./scripts/generate-terraform-docs.sh --copy-templates infrastructure/modules/category/module-name
```

The script performs three phases:

- **Template Processing**: Updates configuration files with module-specific values
- **Example Generation**: Creates usage examples in `infrastructure/examples/`
- **Documentation Generation**: Creates README.md files with terraform-docs

### 3. Version Control & GitLab CI Variable Management

#### Semantic Versioning with Merge Request Labels

Use merge request labels for semantic versioning:

- **`major`**: Breaking changes (X.0.0)
- **`minor`**: New features (0.X.0)  
- **`patch`**: Bug fixes (0.0.X) - default if no label

#### TERRAFORM_MODULE_VERSION GitLab CI Variable

The module publishing system uses a GitLab CI variable called **`TERRAFORM_MODULE_VERSION`** to manage versioning:

- **Purpose**: Stores the base version for major/minor releases across all modules
- **Location**: Set as a project-level GitLab CI variable in the Control Tower repository
- **Usage**:
  - For `major`/`minor` bumps: All modules are published with the same version from this variable
  - For `patch` bumps: Individual modules use their existing registry version + patch increment
  - For new modules: Uses the major.minor version from this variable with patch version 0

#### Version Calculation Logic

```bash
# Major/Minor releases - ALL modules get same version
TERRAFORM_MODULE_VERSION="1.2.3" + major label → All modules published as "2.0.0"
TERRAFORM_MODULE_VERSION="1.2.3" + minor label → All modules published as "1.3.0"

# Patch releases - INDIVIDUAL module versioning
Existing module "control-tower-redis" v1.2.5 + patch → Published as "1.2.6"
New module (not in registry) + patch → Uses "1.2.0" (from TERRAFORM_MODULE_VERSION)
```

#### Managing the TERRAFORM_MODULE_VERSION Variable

The variable is automatically updated after successful major/minor releases:

- **Location**: GitLab project settings → CI/CD → Variables
- **Name**: `TERRAFORM_MODULE_VERSION`
- **Update**: Automatically set by CI pipeline after successful publishing
- **Manual Update**: Only needed for major architecture changes or version resets

### 4. Publishing

Modules are automatically published to GitLab registry:

- **Registry**: `gitlab.com/dematic/control-tower-{module-name}/google`
- **Packaging**: Creates `.tgz` files using `package_module.py`
- **Publishing**: Automated via CI/CD pipeline

## Template System

### Configuration Files

- **`.terraform-docs.yml`**: Controls README.md generation
- **`.terraform-example.yml`**: Controls example file generation
- **`.tfvars-docs.yml`**: Controls variable documentation

### Custom Documentation

- **`templates/header.md`**: Module overview and custom content
- **Placeholders**: Automatically replaced with module-specific values
- **Preservation**: Custom content is preserved during regeneration

## CI/CD Integration

### Pipeline Stages

1. **Validation**: Terragrunt HCL validation and formatting
2. **Testing**: Module publishing tests and dry-run validation
3. **Documentation**: Automatic documentation generation
4. **Publishing**: Module packaging and registry upload

### Automated Processes

- **Documentation Updates**: Generated on every merge to main branch
- **Version Calculation**: Based on merge request labels
- **Registry Publishing**: Automatic upload to GitLab registry
- **Example Generation**: Usage examples created automatically

## Best Practices

### Module Design

- **Reusability**: Design modules for multiple environments
- **Consistency**: Follow established naming conventions
- **Documentation**: Provide clear variable descriptions
- **Examples**: Include practical usage examples

### Version Management

- **Semantic Versioning**: Use proper version increments
- **Breaking Changes**: Clearly document incompatible changes
- **Deprecation**: Provide migration paths for deprecated features
- **Testing**: Validate modules in development/integration environments

### Documentation

- **Clear Descriptions**: Explain module purpose and usage
- **Variable Documentation**: Document all inputs and outputs
- **Examples**: Provide realistic usage examples
- **Requirements**: List dependencies and prerequisites

## Troubleshooting

### Common Issues

1. **Documentation Generation Fails**
   - Check terraform-docs installation
   - Verify configuration file syntax
   - Review module structure

2. **Publishing Errors**
   - Verify GitLab permissions
   - Check module naming conventions
   - Validate version format

3. **Template Issues**
   - Ensure template files are copied correctly
   - Check placeholder replacement
   - Verify file permissions

### Getting Help

- **Scripts**: Check `scripts/terraform-modules/README.md`
- **Templates**: Review `scripts/terraform-docs-templates/README.md`
- **CI/CD**: Check `.gitlab/ci/infrastructure/terraform-module-publishing.gitlab-ci.yml`
