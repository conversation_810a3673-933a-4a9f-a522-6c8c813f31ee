# Development Guide

This guide provides instructions for developing with the Control Tower monorepo.

## Prerequisites

- **Node.js 22+**: Latest LTS version
- **Yarn**: Package manager (1.22.22+)
- **Docker Desktop**: For local services
- **Git**: Version control

## Initial Setup

```bash
# Clone and setup
<NAME_EMAIL>:dematic/controltower/control-tower.git
cd control-tower
corepack enable
yarn install

# Setup API services
cd services/api
yarn run setup
```

### Environment Configuration

Create `.env.local` for environment-specific settings:

```env
ARTIFACTORY_AUTH_TOKEN=your_token_here
REDIS_REQUEST_CACHE_OVERRIDE=false
DATABASE_URL=postgresql://user:pass@localhost:5432/control_tower
```

### Database Setup

```bash
cd services/api
docker compose up
yarn db:setup
```

## Development Workflows

### Monorepo Structure

```bash
control-tower/
├── environments/         # Deployment configurations
├── infrastructure/       # Terraform modules
├── scripts/              # Utility scripts
└── services/
    ├── api/              # Nx workspace for API microservices
    ├── api-e2e/          # API end-to-end tests
    ├── metric-processor/ # Python metrics service
    ├── ui/               # React frontend
    └── ui-e2e/           # UI end-to-end tests
```

### Working with API Services

```bash
cd services/api

# Start all services
yarn run watch

# Start specific service
yarn run config:watch
yarn run orders:watch
yarn run inventory:watch
yarn run equipment:watch

# Common commands
yarn run build
yarn run test
yarn run lint
yarn run spec
```

### Working with UI

```bash
cd services/ui

# Development
yarn start
yarn build:dev
yarn build:stage
yarn build:prod

# Testing
yarn test
yarn test:coverage
yarn lint
yarn typecheck
```

## Service Development

### Creating a New API Service

1. **Create service directory:**

   ```bash
   cd services/api/apps/api
   mkdir ict-newservice-api
   ```

2. **Initialize package.json:**

   ```json
   {
     "name": "ict-newservice-api",
     "version": "1.0.0",
     "main": "src/index.ts",
     "scripts": {
       "build": "tsc",
       "watch": "tsx watch src/index.ts",
       "test": "jest",
       "lint": "eslint src/**/*.ts"
     }
   }
   ```

3. **Follow service structure:**

   ```bash
   src/
   ├── controllers/     # TSOA controllers
   ├── services/        # Business logic
   ├── stores/          # Data access
   ├── defs/           # Type definitions
   ├── test/           # Unit tests
   └── index.ts        # Entry point
   ```

### Service Architecture Patterns

#### Controller Pattern (TSOA)

```typescript
import { Controller, Get, Route, Tags } from 'tsoa';

@Route('newservice')
@Tags('NewService')
export class NewServiceController extends Controller {
  @Get('/')
  public async getStatus(): Promise<{ status: string }> {
    return { status: 'OK' };
  }
}
```

#### Service Layer

```typescript
import { Service } from 'typedi';

@Service()
export class NewService {
  public async getStatus(): Promise<{ status: string }> {
    // Business logic here
    return { status: 'OK' };
  }
}
```

## Testing

### Unit Testing

```bash
# API services
nx run ict-config-api:test
yarn test --coverage

# UI components
cd services/ui
yarn test
yarn test:coverage
```

### Integration Testing

```bash
# API integration tests
cd services/api-e2e
yarn test

# UI end-to-end tests
cd services/ui-e2e
yarn test
```

### Test Example

```typescript
import { describe, it, expect } from '@jest/globals';
import { NewService } from '../src/services/NewService';

describe('NewService', () => {
  let service: NewService;

  beforeEach(() => {
    service = new NewService();
  });

  it('should return status OK', async () => {
    const result = await service.getStatus();
    expect(result.status).toBe('OK');
  });
});
```

## Code Quality

### Linting and Formatting

```bash
# Lint specific service
nx run ict-config-api:lint

# Lint all services
yarn run lint --fix
```

### Pre-commit Hooks

```bash
yarn prepare
yarn pre-commit
```

## Database Management

### Local Development

```bash
cd services/api
docker compose up postgres
yarn db:setup
yarn db:reset
yarn db:generate
```

### Database Migrations

```bash
# Create migration
npx typeorm migration:create -n MigrationName
```

## API Documentation

### OpenAPI/Swagger

```bash
cd services/api
yarn run spec  # Generate OpenAPI specs
```

- Local: <http://localhost:8080/docs>
- Generated specs: `docs/control-tower-openapi.yaml`

### Using Bruno for API Testing

1. Install Bruno from [usebruno.com](https://www.usebruno.com/)
2. Configure custom CA certificate
3. Open collection: `/docs/bruno/ICT_API`

## Git Workflow

### Commit Convention

```bash
<type>(<scope>): <ticket-number> - <description>

# Examples:
feat(api): ICT-1234 - add new inventory endpoint
fix(ui): ICT-5678 - resolve dashboard loading issue
docs: ICT-9012 - update API documentation
```

### Branch Naming

```bash
ICT-<ticket-number>-<short-description>
# Example: ICT-1234-add-inventory-endpoint
```

### Pull Request Process

1. Create feature branch from `dev`
2. Implement changes with tests
3. Run linting and tests locally
4. Create pull request to `dev`
5. Address review feedback
6. Merge after approval

## Environment Variables

### .env Files

- `.env` - Shared non-sensitive values
- `.env.local` - Personal overrides (git ignored)
- `.env.development` - Development-specific values
- `.env.production` - Production-specific values

### Common Variables

```env
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/db

# Redis
REDIS_URL=redis://localhost:6379
REDIS_REQUEST_CACHE_OVERRIDE=false

# Authentication
JWT_SECRET=your-secret-key
AUTH_ISSUER=https://auth.example.com

# External Services
ARTIFACTORY_AUTH_TOKEN=token
GOOGLE_CLOUD_PROJECT=project-id
```

## Performance Optimization

### Caching Strategy

```typescript
import { CacheMiddleware } from '@ict/cache-middleware';

@Get('/')
@Middleware(CacheMiddleware(60)) // Cache for 60 minutes
public async getData(): Promise<Data[]> {
  return this.service.getData();
}
```

### Health Checks

```typescript
@Get('/health/basic')
public async basicHealth(): Promise<HealthStatus> {
  return { status: 'healthy' };
}

@Get('/health/full')
public async fullHealth(): Promise<DetailedHealthStatus> {
  return { 
    status: 'healthy',
    dependencies: {...}
  };
}
```

## Troubleshooting

### Common Issues

#### Docker Memory Issues

- Increase Docker memory limit to 16GB
- Use `docker system prune` to clean up

#### TypeScript Compilation Errors

- Ensure `yarn run setup` was completed
- Check for missing dependencies

#### Database Connection Issues

- Verify Docker services are running
- Check database connection strings
- Run `yarn db:setup` to initialize

#### Port Conflicts

- Check for services running on conflicting ports
- Use `docker compose down` to stop services

## Best Practices

### Code Organization

1. Follow established patterns and conventions
2. Write comprehensive tests for new features
3. Document complex business logic
4. Use TypeScript types effectively

### Security

1. Never commit secrets or tokens
2. Use environment variables for configuration
3. Validate all input data
4. Follow authentication/authorization patterns

### Performance

1. Optimize database queries
2. Use caching appropriately
3. Implement proper error handling
4. Monitor application performance
