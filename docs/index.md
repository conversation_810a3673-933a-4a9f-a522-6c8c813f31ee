<!-- 
This docs directory serves as the primary documentation source for Control Tower's Backstage developer portal.

## Purpose and Goals:

1. **Centralized Documentation Hub**: Provides a single source of truth for all Control Tower documentation that gets automatically deployed to Backstage at https://backstage.ops.dematic.dev/

2. **Developer Onboarding**: Offers comprehensive guides for new developers to understand the codebase, setup their environment, and contribute effectively

3. **Service Documentation**: Documents all API services, infrastructure components, and development workflows with clear examples and best practices

4. **Infrastructure Knowledge**: Provides detailed information about Terraform modules, deployment processes, and environment management

5. **CI/CD Documentation**: Explains the automated testing, building, and deployment pipelines used across all environments

## Target Audience:

- **Developers**: New team members and existing developers needing reference material
- **DevOps Engineers**: Infrastructure and deployment specialists
- **QA Engineers**: Testing and quality assurance team members
- **Product Managers**: Stakeholders needing technical context
- **External Contributors**: Open source contributors and partners

This documentation system ensures Control Tower's technical knowledge is accessible, maintainable, and always up-to-date.
-->

The content on this page is automatically pulled from the README in the root of the [*control tower*](https://gitlab.com/dematic/controltower/control-tower) repository located in GitLab:

---

--8<-- "README.md"
