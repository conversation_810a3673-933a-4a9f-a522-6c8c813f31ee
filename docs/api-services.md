# API Services Architecture

The Control Tower backend consists of specialized microservices designed for different operational domains.

## Service Overview

- **ict-config-api**: Configuration and settings management (Port: 8081)
- **ict-orders-api**: Order management and processing (Port: 8082)
- **ict-inventory-api**: Inventory tracking and management (Port: 8083)
- **ict-equipment-api**: Equipment monitoring and fault management (Port: 8084)
- **ict-operators-api**: Operator management and area assignments (Port: 8085)
- **ict-workstation-api**: Workstation performance and metrics (Port: 8086)
- **ict-ai-api**: AI/ML services and enterprise search (Port: 8087)
- **ict-admin-api**: Administrative functions and user management (Port: 8088)
- **ict-dataexplorer-api**: Data exploration and querying (Port: 8089)
- **ict-diagnostics-api**: System diagnostics and infrastructure monitoring (Port: 8090)
- **ict-simulation-api**: Simulation management and execution (Port: 8091)
- **ict-tableau-management-api**: Tableau integration and management (Port: 8092)
- **ict-tableau-proxy**: Tableau proxy services for data access (Port: 8094)
- **mock-data-api**: Mock data generation for development and testing (Port: 8093)
- **ict-typeorm-migrations-cr**: TypeORM migrations Cloud Run service
- **ict-instrumentation**: OpenTelemetry instrumentation library (shared)

## Architecture Patterns

### Service Structure

Each API service follows a consistent structure:

```bash
src/
├── controllers/     # TSOA controllers with route definitions
├── services/        # Business logic services
├── stores/          # Data access layer
├── defs/           # TypeScript type definitions
├── test/           # Unit tests
└── index.ts        # Service entry point
```

### Key Technologies

- **TSOA**: OpenAPI/Swagger generation and route handling
- **TypeORM**: Database ORM with PostgreSQL
- **Express**: Web framework with middleware
- **TypeDI**: Dependency injection container
- **Redis**: Request caching and session management

### Common Patterns

- **Authentication & Authorization**: JWT tokens and role-based access control
- **Error Handling**: Standardized error responses with proper HTTP status codes
- **Logging**: Structured logging with correlation IDs for request tracing
- **Caching**: Redis-based caching for frequently accessed data
- **Validation**: Request/response validation using JSON Schema and TSOA decorators

## API Gateway

NGINX serves as the API gateway (Port: 8080):

- Load balancing across service instances
- SSL termination
- Rate limiting
- Health checks

## Development Workflow

### Service Development Commands

```bash
# Build all services
yarn run build

# Watch all services for development
yarn run watch

# Run specific service
yarn run <service>:watch
nx run ict-<service>-api:watch

# Test specific service
nx run ict-<service>-api:test

# Lint specific service
nx run ict-<service>-api:lint
```

### Common Service Shortcuts

```bash
yarn run config:watch     # Configuration API
yarn run orders:watch     # Orders API
yarn run inventory:watch  # Inventory API
yarn run equipment:watch  # Equipment API
yarn run operators:watch  # Operators API
yarn run workstation:watch # Workstation API
yarn run ai:watch         # AI API
yarn run admin:watch      # Admin API
yarn run dataexplorer:watch # Data Explorer API
yarn run diagnostics:watch # Diagnostics API
yarn run simulation:watch # Simulation API
yarn run tableau-mgmt:watch # Tableau Management API
```

### Local Development

1. **Start all services with Docker:**

   ```bash
   cd services/api
   docker compose up
   ```

2. **Setup database:**

   ```bash
   yarn db:setup
   ```

3. **Access services:**
   - API Gateway: <http://localhost:8080>
   - Individual services: <http://localhost:808X> (where X is service port)

### API Documentation

Each service generates OpenAPI specifications:

- Local: <http://localhost:808X/docs>
- Generated specs: `docs/control-tower-openapi.yaml`

### Testing

- **Unit Tests**: Jest framework with mocking
- **Integration Tests**: Service-to-service communication
- **E2E Tests**: Full workflow testing with Playwright
- **API Testing**: Bruno collections for manual testing

## Monitoring & Observability

### Health Checks

Each service exposes health check endpoints:

- `/health/basic` - Basic service health
- `/health/full` - Comprehensive health including dependencies
