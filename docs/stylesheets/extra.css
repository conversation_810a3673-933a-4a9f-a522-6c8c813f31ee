
/* This is the width of the display for help content.  100% will use maximum width. */
.md-grid {
    max-width: 96%;
}

/* Color of the headings in the help content. */
.md-typeset h1, .md-typeset h2, .md-typeset h3, .md-typeset h4, .md-typeset h5, .md-typeset h6  {
    font-weight: bolder;
}


/* The "On this page" navigation title on the right side. */
.md-nav__title {

    color: rgb(155, 154, 154);
    font-weight: bold;
}

/* The topic names in the left and right navs. */
.md-nav {
    font-size: .77rem;
}

/* Customizing the table display. */
/* https://blog.ktz.me/making-mkdocs-tables-look-like-github-markdown-tables/ */

th, td {
    border: 1px solid var(--md-typeset-table-color);
    border-spacing: 0;
    border-bottom: none;
    border-left: none;
    border-top: none;
}

.md-typeset__table {
    line-height: 1;
}

.md-typeset__table table:not([class]) {
    font-size: .74rem;
    border-right: none;
}

.md-typeset__table table:not([class]) td,
.md-typeset__table table:not([class]) th {
    padding: 9px;
}


/* https://github.com/squidfunk/mkdocs-material/issues/3409 

I've added the possibility for fading out search highlighting in 07f7366 with custom CSS.
Duration can be configured.

*/
@keyframes fade {
    100% {
      background-color: transparent;
    }
  }
  .md-typeset mark[data-md-highlight] {
    animation: fade 30s forwards; /* Change time to your liking */
  }

  /* Padding for images for readability. This is good for tasks with screen captures and ordered lists*/
  .md-typeset img,.md-typeset video {
    padding: 0.6em;
}


/* These two styles shade alternate rows of the tables.  I don't think it's necessary. */

/* light mode alternating table bg colors */
/* .md-typeset__table tr:nth-child(2n) {
    background-color: #f8f8f8;
} */

/* dark mode alternating table bg colors */
/* [data-md-color-scheme="slate"] .md-typeset__table tr:nth-child(2n) {
    background-color: hsla(var(--md-hue),25%,25%,1)
} */

/* It would be nice to make the top rows of tables sticky like Confluence, but unable to figure this out ATM. */




  
  



