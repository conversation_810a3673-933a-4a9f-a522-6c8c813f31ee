# =====================================
# Common Services Variables
# =====================================

locals {
  lz_config = read_terragrunt_config(find_in_parent_folders("lz.hcl"))

  nonprod_lz_config                      = read_terragrunt_config("${get_repo_root()}/environments/dematicnonprod/lz.hcl")
  nonprod_cicd_service_account_principal = local.nonprod_lz_config.locals.cicd_service_account_principal

  prod_lz_config                      = read_terragrunt_config("${get_repo_root()}/environments/dematicprod/lz.hcl")
  prod_cicd_service_account_principal = local.prod_lz_config.locals.cicd_service_account_principal
}

inputs = {
  enforce_unique_naming    = false
  environment              = "common-services"
  environment_display_name = "common services"

  iam_bindings = {
    "roles/resourcemanager.projectIamAdmin" = [
      "group:<EMAIL>",
      local.nonprod_cicd_service_account_principal,
      local.prod_cicd_service_account_principal
    ]
    "roles/storage.bucketViewer" = [
      "group:<EMAIL>",
      "group:<EMAIL>"
    ]
    "roles/storage.objectViewer" = [
      "group:<EMAIL>",
      "group:<EMAIL>"
    ]
  }
}
