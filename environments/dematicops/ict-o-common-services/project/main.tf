module "project" {
  source  = "terraform-google-modules/project-factory/google"
  version = "~> 18.0.0"

  name                     = local.project_name
  random_project_id        = true
  random_project_id_length = 4
  deletion_policy          = "DELETE"

  org_id          = var.organization_id
  folder_id       = var.lz_folder_id
  billing_account = var.billing_account_id

  auto_create_network     = false
  default_service_account = "disable"
  activate_apis           = var.global_project_apis
  activate_api_identities = var.global_project_api_identities

  labels = local.project_labels
}

# TODO: move to a map in locals
resource "google_project_iam_member" "artifact_registry_admins" {
  project = module.project.project_id

  for_each = toset(local.artifact_registry_admins)

  role   = "roles/artifactregistry.admin"
  member = each.value
}
