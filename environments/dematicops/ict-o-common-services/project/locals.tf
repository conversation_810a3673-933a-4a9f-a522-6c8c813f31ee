locals {
  project_name = "${var.global_naming_root}-${var.lz_abbreviation}-${var.environment}"

  common_resource_labels = merge(
    var.global_resource_labels,
    var.lz_resource_labels,
    {
      environment = var.environment
    }
  )

  project_labels = merge(
    var.global_project_labels,
    var.lz_project_labels,
    local.common_resource_labels
  )

  # build this into a map
  artifact_registry_admins = [
    "group:<EMAIL>",
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>"
  ]
}
