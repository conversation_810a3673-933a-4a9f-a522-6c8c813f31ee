module "common_services" {
  source = "../../../../infrastructure/modules/common/common-services"

  project_id = data.terraform_remote_state.project.outputs.project_id

  common_resource_labels = local.common_resource_labels
  naming_root            = var.global_naming_root

  artifact_registry_configuration = {
    location      = "us"
    enable_npm    = false
    enable_python = false
  }

  domain_config = {
    ns_record_ttl    = 300
    subdomain_prefix = var.global_naming_root

    nonprod_root_zone = {
      name       = var.global_dns_config.nonprod_root_zone.name
      project_id = var.global_dns_config.nonprod_root_zone.project_id
    }

    prod_root_zone = {
      name       = var.global_dns_config.prod_root_zone.name
      project_id = var.global_dns_config.prod_root_zone.project_id
    }
  }

  gitlab_runner_permissions = {
    service_account = var.gitlab_runner_permissions.service_account
    roles           = var.gitlab_runner_permissions.roles
  }
}
