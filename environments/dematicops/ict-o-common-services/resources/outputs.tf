output "attestor" {
  description = <<EOF
    The attestor configuration.

    Outputs:
    - attestor_name: The name of the attestor.
    - kms_key_ring_id: The ID of the KMS key ring used by the attestor.
  EOF

  value = module.common_services.attestor
}

output "dev_dns_zone" {
  description = "The name and project ID of the dev DNS managed zone."
  value       = module.common_services.dev_dns_zone
}

output "docker_repository" {
  description = <<EOT
    The Docker repository configuration.

    Outputs:
    - project: The ID of the project where the Docker repository is located.
    - location: The location of the Artifact Registry repository. E.g., `us`, `us-central1`.
    - repository: The name of the Docker repository.
  EOT

  value = module.common_services.docker_repository
}


output "generic_repository" {
  description = <<EOT
    The generic repository configuration.

    Outputs:
    - project: The ID of the project where the generic repository is located.
    - location: The location of the Artifact Registry repository. E.g., `us`, `us-central1`.
    - repository: The name of the generic repository.
  EOT

  value = module.common_services.generic_repository
}

output "nonprod_dns_zone" {
  description = "The name and project ID of the nonprod DNS managed zone."
  value       = module.common_services.nonprod_dns_zone
}

output "npm_repository" {
  description = <<EOT
    The npm repository configuration. Null of the npm repository is not enabled.

    Outputs:
    - project: The ID of the project where the npm repository is located.
    - location: The location of the Artifact Registry repository. E.g., `us`, `us-central1`.
    - repository: The name of the npm repository, if enabled.
  EOT

  value = module.common_services.npm_repository
}

# output "prod_domain" {
#   description = <<EOT
#     The domain for the prod landing zone. This will serve as the root domain for all prod
#     environments.
#   EOT

#   value = module.common_services.prod_domain
# }

output "project_id" {
  description = "The ID of the project where the common services are deployed."
  value       = data.terraform_remote_state.project.outputs.project_id
}

output "python_repository" {
  description = <<EOT
    The Python repository configuration. Null of the Python repository is not enabled.

    Outputs:
    - project: The ID of the project where the Python repository is located.
    - location: The location of the Artifact Registry repository. E.g., `us`, `us-central1`.
    - repository: The name of the Python repository, if enabled.
  EOT

  value = module.common_services.python_repository
}

# output "stage_dns_zone" {
#   description = "The name and project ID of the stage DNS managed zone."
#   value       = module.common_services.stage_dns_zone
# }
