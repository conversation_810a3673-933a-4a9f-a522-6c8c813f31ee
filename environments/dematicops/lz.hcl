# =====================================
# DematicOps Landing Zone Variables
# =====================================

locals {
  cicd_service_account           = "<EMAIL>"
  cicd_service_account_principal = "serviceAccount:${local.cicd_service_account}"
}


inputs = {
  # Folder-level variables
  lz_cicd_project_id = "ict-prod-cicd"

  lz_folder_id    = "************" # DematicOps/ControlTower
  lz_folder_name  = "DematicOps"
  lz_short_name   = "ops"
  lz_abbreviation = "o"

  lz_project_apis           = []
  lz_project_api_identities = []
  lz_project_labels         = {}

  lz_resource_labels = {
    landing_zone = "ops"
  }

  iam_bindings = {
    "roles/artifactregistry.admin" = [
      "group:<EMAIL>"
    ]
    "roles/artifactregistry.reader" = [
      "group:<EMAIL>"
    ]
    "roles/dns.admin" = [
      "group:<EMAIL>"
    ]
    "roles/secretmanager.admin" = [
      local.cicd_service_account_principal
    ]
  }
}
