# Control Tower Environments

This document describes the environment structure and deployment configurations for Control Tower across different stages of the development lifecycle.

## Overview

The `environments` folder contains the live infrastructure definitions for all Control Tower environments, excluding individual Review Apps. Each environment is configured using Terragrunt with environment-specific variables and shared configuration patterns.

**WARNING**: Running `terragrunt apply` in an environment folder **will** change live infrastructure.

## Environment Structure

Control Tower supports multiple environments organized into different landing zones:

### Production Landing Zone (`dematicprod`)

- **Production (`prod`)**: Live production environment serving end users
  - Location: `dematicprod/prod/ict-p-us1/`
  - GCP Project Pattern: `ict-p-{region}`
  - High availability and redundancy configurations
  - Strict change control and deployment approvals

### Non-Production Landing Zone (`dematicnonprod`)

- **Development (`dev`)**: Primary development and testing environment
  - Location: `dematicnonprod/dev/ict-np-us1-dev/`
  - GCP Project Pattern: `ict-np-{region}-dev`
  - Continuous deployment from `dev` branch
  - Used for feature development and integration testing

- **Staging (`stage`)**: Pre-production environment for final testing
  - Location: `dematicnonprod/stage/ict-np-us1-stage/`
  - GCP Project Pattern: `ict-np-{region}-stage`
  - Production-like configuration with reduced scale
  - Used for final testing before production deployment

### Operations Landing Zone (`dematicops`)

- **Common Services**: Shared services across all environments
  - Location: `dematicops/ict-o-common-services/`
  - DNS management for all environments
  - Artifact Registry repositories
  - Shared monitoring and logging infrastructure

## Configuration Hierarchy

### Global Configuration (`global.hcl`)

Defines configuration that applies to all Control Tower deployments:

- Billing account ID
- Organization ID  
- Root DNS domains
- Common resource labels
- Terraform backend configuration

### Landing Zone Configuration (`lz.hcl`)

Each landing zone defines:

- GCP folder ID for organizing projects
- Landing zone specific settings
- CI/CD project permissions
- Network and security defaults

### Environment Configuration (`environment.hcl`)

Each environment defines:

- Environment-specific resource sizing
- Database and cache configurations
- Monitoring and alerting settings
- Application-specific variables

### Instance Configuration (`instance.hcl`)

Each instance defines:

- Instance-specific naming prefixes
- Regional deployment settings
- Service-specific overrides

## Shared TFVARS Files

The configuration uses a hierarchical approach with shared variables files at different levels:

### Global Level
- Billing account and organization IDs
- Root naming conventions
- Terraform backend configuration

### Landing Zone Level
- GCP folder assignments for projects
- CI/CD service account permissions
- Shared network and security policies

### Environment Level
- Environment-specific resource limits
- Database and cache sizing
- Monitoring thresholds

### Instance Level
- Instance-specific naming and labeling
- Regional resource placement
- Service configuration overrides

## Deployment Process

### Development Workflow
1. Changes merged to `dev` branch trigger automatic deployment to development environment
2. Manual promotion to staging environment for final testing
3. Approved changes deployed to production with manual approval gates

### Infrastructure Changes
1. Terraform changes validated in development environment
2. Infrastructure plans reviewed and approved
3. Changes applied with proper change control procedures


## Environment Access

### Development Environment
- Used by development team for daily work
- Automatic deployments from CI/CD
- Relaxed access controls for development efficiency

### Staging Environment  
- Production-like configuration
- Manual deployment approval required
- Limited access for final testing

### Production Environment
- Strict access controls and change approval
- Full monitoring and alerting
- High availability configurations

## Resource Naming

All resources follow consistent naming patterns:

- **Pattern**: `{naming_root}-{environment}-{region}-{instance_suffix}`
- **Example**: `ict-p-us1` (production), `ict-np-us1-dev` (development)
- **Globally Unique**: Resources requiring global uniqueness combine naming_root + environment + instance_prefix

## Environment-Specific Features

### Development
- Relaxed security policies for development ease
- Smaller resource allocations
- Enhanced logging for debugging
- Automatic database resets and test data loading

### Staging
- Production-equivalent security policies
- Production-like resource sizing (scaled down)
- Full monitoring and alerting testing
- Production data migration testing

### Production
- Maximum security policies
- Full resource allocations for performance
- Comprehensive monitoring and disaster recovery
- Automated backup and recovery procedures
