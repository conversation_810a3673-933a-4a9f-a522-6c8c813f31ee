module "project_permissions" {
  source  = "terraform-google-modules/iam/google//modules/projects_iam"
  version = "8.0.0"

  projects = [module.project.project_id]
  mode     = "additive"
  bindings = var.iam_bindings
}

module "shared_vpc_host_project_permissions" {
  source  = "terraform-google-modules/iam/google//modules/projects_iam"
  version = "8.0.0"

  projects = [var.lz_shared_vpc_network.project_id]
  mode     = "additive"
  bindings = local.host_project_iam_bindings
}
