locals {
  project_name = (var.use_environment_name_in_project_name
    ? "${var.global_naming_root}-${var.lz_abbreviation}-${var.instance_name}-${var.environment_name}"
    : "${var.global_naming_root}-${var.lz_abbreviation}-${var.instance_name}"
  )

  common_resource_labels = merge(
    var.global_resource_labels,
    var.lz_resource_labels,
    {
      environment = var.environment_name
      instance    = "${var.instance_name}-${var.environment_name}"
    }
  )

  # create a map of role assignments for the serverless agent for the shared VPC host project
  host_project_iam_bindings = {
    for role in var.global_shared_vpc_config.cloud_sql_agent_host_project_roles :
    role => [
      format(
        "serviceAccount:<EMAIL>",
        module.project.project_number
      )
    ]
  }

  project_apis = concat(
    var.global_project_apis,
    ["prod.n4gcp.neo4j.io"]
  )

  project_labels = merge(
    var.global_project_labels,
    var.lz_project_labels,
    local.common_resource_labels
  )
}
