locals {
  project_id     = data.terraform_remote_state.project.outputs.project_id
  project_number = data.terraform_remote_state.project.outputs.project_number

  cloud_run_environment_variables = merge(
    var.global_cloud_run_environment_variables,
    var.lz_cloud_run_environment_variables,
    {}
  )

  common_resource_labels = merge(
    var.global_resource_labels,
    var.lz_resource_labels,
    {}
  )

  default_vpc_access_connector_config = {
    machine_type  = "e2-micro"
    min_instances = 2
    max_instances = 7
  }

  instance_prefix = (var.use_environment_name_for_instance_name
    ? var.environment_name
    : var.instance_name
  )

  instance_display_name = (var.use_environment_name_for_instance_name
    ? var.environment_friendly_name
    : "${var.instance_friendly_name} (${var.environment_friendly_name})"
  )

  tenant_config = jsondecode(file(var.tenant_config_file))

  # Monitoring and alerting
  alert_email_address = "<EMAIL>"
}
