include "root" {
  path = find_in_parent_folders("root.hcl")
}

include "global" {
  path = find_in_parent_folders("global.hcl")
}

include "lz" {
  path = find_in_parent_folders("lz.hcl")
}

include "environment" {
  path = find_in_parent_folders("environment.hcl")
}

include "instance" {
  path = find_in_parent_folders("instance.hcl")
}

generate "variables" {
  path      = "variables.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
    ${file(find_in_parent_folders("instance.variables.tf"))}
    ${file(find_in_parent_folders("environment.variables.tf"))}
    ${file(find_in_parent_folders("lz.variables.tf"))}
    ${file(find_in_parent_folders("global.variables.tf"))}
  EOF
}
