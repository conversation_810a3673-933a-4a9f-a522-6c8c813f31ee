# =====================================
# Dev Environment Variables
# =====================================

locals {
  lz_config                      = read_terragrunt_config(find_in_parent_folders("lz.hcl"))
  cicd_service_account_principal = local.lz_config.locals.cicd_service_account_principal
}

inputs = {
  environment_folder_id     = "************" #DematicNonProd/ControlTower/dev
  environment_folder_name   = "dev"
  environment_friendly_name = "Dev"
  environment_name          = "dev"

  auth0_config = {
    domain   = "dev-zmogq9vd.us.auth0.com"
    audience = "https://dev.api.ict.dematic.dev"
  }

  availability_config = {
    url        = "https://system-availability-api-************.us-east1.run.app"
    project_id = "edp-d-us-east2-etl"
  }

  edp_config = {
    project_id      = "edp-d-us-east2-etl"
    pubsub_topic_id = "adi-data"
  }

  environment_iap_members = [
    "group:<EMAIL>"
  ]

  ignition_upstream_url = "stage-dematic-software.ignition.ict.dematic.dev"

  metric_processor_url = "https://dev-ict-etl-metric-processor-************.us-east1.run.app"

  aiml_config = {
    agent_search_url            = "https://dematic-chat.ops.dematic.dev"
    agent_search_audience_url   = "https://dematic-chat-************.us-central1.run.app"
    gold_questions_url          = "https://ragengine-dev.ops.dematic.dev"
    gold_questions_audience_url = "https://ragengine-************.us-central1.run.app"
  }

  postgres_databases = [
    "dematic",
    "drt_automation",
    "ict_development",
    "qa_automation",
    "superior_uniform",
    "tti",
    "acehardware"
  ]

  iam_bindings = {}
}
