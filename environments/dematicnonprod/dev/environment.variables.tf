# =====================================
# Dev Environment Variables
# =====================================

variable "auth0_config" {
  description = <<EOT
    The Auth0 configuration for the environment, including the domain and audience.
  EOT

  type = object({
    domain   = string
    audience = string
  })
}

variable "availability_config" {
  description = <<EOT
    The Availability configuration for the environment, including the url and projectId.
  EOT

  type = object({
    url        = string
    project_id = string
  })
}

variable "edp_config" {
  description = "The EDP configuration for the environment."
  type = object({
    project_id      = string
    pubsub_topic_id = string
  })
}

variable "environment_folder_id" {
  description = "The ID of the folder that instances for this environment should be created in."
  type        = string
}

variable "environment_folder_name" {
  description = "The name of the folder that instances for this environment should be created in."
  type        = string
}

variable "environment_friendly_name" {
  description = "A friendly name for the environment, used for display purposes."
  type        = string
}

variable "environment_iap_members" {
  description = <<EOT
    An optional list of members that should be granted access to the IAP for the environment. The 
    members should be prefixed with one of: ["serviceAccount:", "user:", "group:"].
  EOT

  type    = list(string)
  default = []

  validation {
    error_message = "All entries must be prefixed with 'serviceAccount:', 'user:', or 'group:'."

    condition = alltrue([
      for member in var.environment_iap_members : (
        startswith(member, "serviceAccount:") ||
        startswith(member, "user:") ||
        startswith(member, "group:")
      )
    ])
  }
}

variable "environment_name" {
  description = <<EOT
    The name of the environment that the resources are being deployed to. This value will be 
    combined with the `naming_root` to create a unique prefix for any resources requiring a 
    globally-unique identifier. Therefore, it must be unique among Control Tower environments but 
    should be as short as possible to comply with GCP resource name length restrictions.
  EOT

  type = string
}

variable "ignition_upstream_url" {
  description = "The URL of the Ignition upstream service that the proxy should connect to."
  type        = string
}

variable "metric_processor_url" {
  description = "The URL for the metric processor that instances in this environment should use."
  type        = string
}

variable "aiml_config" {
  description = <<EOT
    The AIML configuration for the environment.
  EOT

  type = object({
    gold_questions_url          = string
    gold_questions_audience_url = string
    agent_search_url            = string
    agent_search_audience_url   = string
  })
}

variable "postgres_databases" {
  description = "A list of PostgreSQL databases to be created in the environment."
  type        = list(string)
}
