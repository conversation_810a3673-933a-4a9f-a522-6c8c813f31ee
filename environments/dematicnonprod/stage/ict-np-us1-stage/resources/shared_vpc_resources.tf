
module "postgresql_svpc" {
  source = "../../../../../infrastructure/modules/instance/postgresql-svpc"

  project_id = local.project_id
  region     = var.global_default_region
  zone       = var.global_default_zone

  access_logs_bucket     = module.scaffolding.access_logs_bucket_name
  bucket_location        = "us"
  common_resource_labels = local.common_resource_labels
  database_username      = var.default_postgresql_username
  enforce_unique_naming  = var.enforce_unique_naming
  environment            = var.environment_name
  environment_variables  = local.cloud_run_environment_variables
  instance_prefix        = local.instance_prefix
  naming_root            = var.global_naming_root

  artifact_registry_repository = data.terraform_remote_state.common.outputs.docker_repository

  # databases = var.postgres_databases
  databases = [
    for tenant in local.tenant_config : tenant["metadata"]["dataset"]
  ]

  migration_container_image = {
    image_name = "ict-typeorm-migrations-cr"
    image_tag  = "latest"
  }

  migration_cloud_run_config = var.lz_default_cloud_run_config

  network_config = {
    host_project_id         = module.scaffolding.shared_vpc_connection.project_id
    network_self_link       = module.scaffolding.shared_vpc_connection.network_self_link
    vpc_access_connector_id = module.scaffolding.shared_vpc_connection.vpc_access_connector_id
  }

  depends_on = [module.scaffolding]
}


module "redis-svpc" {
  source = "../../../../../infrastructure/modules/instance/redis"

  project_id     = local.project_id
  region         = var.global_default_region
  zone           = var.global_default_zone
  alternate_zone = var.global_default_alternate_zone

  common_resource_labels = local.common_resource_labels
  enforce_unique_naming  = var.enforce_unique_naming
  instance_prefix        = local.instance_prefix
  network_self_link      = module.scaffolding.shared_vpc_connection.network_self_link

  depends_on = [module.scaffolding]
}
