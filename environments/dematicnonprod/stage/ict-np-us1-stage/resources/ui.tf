module "site_asset_backend" {
  source = "../../../../../infrastructure/modules/instance/site-asset-backend"

  project_id      = local.project_id
  bucket_location = "us"

  access_logs_bucket     = module.scaffolding.access_logs_bucket_name
  app_artifact_folder    = var.ui_app_artifact_folder
  common_resource_labels = local.common_resource_labels
  cors_configs           = local.ui_cors_configs
  enforce_unique_naming  = var.enforce_unique_naming
  instance_prefix        = local.instance_prefix
  naming_root            = var.global_naming_root
  website_config         = local.website_config
}

module "ignition_proxy" {
  source = "../../../../../infrastructure/modules/instance/ignition-proxy"

  project_id      = local.project_id
  region          = var.global_default_region
  bucket_location = "us"

  common_resource_labels = local.common_resource_labels
  enforce_unique_naming  = var.enforce_unique_naming
  host_labels            = local.common_resource_labels
  host_tags              = []
  ignition_port          = local.ignition_port
  ignition_upstream      = local.ignition_upstream
  instance_prefix        = local.instance_prefix
  naming_root            = var.global_naming_root

  host_config = {
    count        = 1
    machine_type = "e2-standard-2"

    disk_size = 50
    disk_type = "pd-balanced"

    image_family  = "ubuntu-2204-lts"
    image_project = "ubuntu-os-cloud"
  }

  network_config = {
    enable_flow_logs  = true
    network_self_link = module.scaffolding.network_self_link
    subnet_ip_range   = var.global_default_ip_ranges.ignition_proxy
    flow_log_config   = var.lz_network_flow_log_config
  }

  depends_on = [module.scaffolding]
}

module "ui_load_balancer" {
  source = "../../../../../infrastructure/modules/instance/ui-load-balancer"

  project_id = local.project_id
  region     = var.global_default_region

  alert_email_address      = local.alert_email_address
  common_resource_labels   = local.common_resource_labels
  enforce_unique_naming    = var.enforce_unique_naming
  instance_display_name    = local.instance_display_name
  instance_prefix          = local.instance_prefix
  load_balancing_scheme    = var.global_default_load_balancing_scheme
  minimum_tls_version      = var.minimum_tls_version
  proxy_service_backend_id = module.ignition_proxy.backend_service_name
  use_parent_dns_name      = var.use_parent_dns_name

  site_asset_backend_id = (
    module.site_asset_backend.backend_service_id
  )

  dns_config = {
    a_record_ttl = var.global_dns_config.default_a_record_ttl

    project_id = data.terraform_remote_state.common.outputs.stage_dns_zone.project_id
    zone_name  = data.terraform_remote_state.common.outputs.stage_dns_zone.zone_name
  }
}
