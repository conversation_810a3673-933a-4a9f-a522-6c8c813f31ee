include "root" {
  path = find_in_parent_folders("root.hcl")
}

include "global" {
  path = find_in_parent_folders("global.hcl")
}

include "lz" {
  path = find_in_parent_folders("lz.hcl")
}

include "environment" {
  path = find_in_parent_folders("environment.hcl")
}

include "instance" {
  path = find_in_parent_folders("instance.hcl")
}

generate "variables" {
  path      = "variables.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
    ${file(find_in_parent_folders("instance.variables.tf"))}
    ${file(find_in_parent_folders("environment.variables.tf"))}
    ${file(find_in_parent_folders("lz.variables.tf"))}
    ${file(find_in_parent_folders("global.variables.tf"))}
  EOF
}

# ================================
# IAM Permissions
#
# Inherited and combined from (if defined):
# - Global
# - Landing Zone
# - Environment
# - Instance
# - Project
# ================================

locals {
  global_config      = read_terragrunt_config(find_in_parent_folders("global.hcl"))
  lz_config          = read_terragrunt_config(find_in_parent_folders("lz.hcl"))
  environment_config = read_terragrunt_config(find_in_parent_folders("environment.hcl"))
  instance_config    = read_terragrunt_config(find_in_parent_folders("instance.hcl"))

  project_config_iam_bindings = {}
}

inputs = {
  iam_bindings = {
    for k in distinct(concat(
      keys(try(local.global_config.inputs.iam_bindings, {})),
      keys(try(local.lz_config.inputs.iam_bindings, {})),
      keys(try(local.environment_config.inputs.iam_bindings, {})),
      keys(try(local.instance_config.inputs.iam_bindings, {})),
      keys(try(local.project_config_iam_bindings, {}))
    )) :
    k => distinct(concat(
      lookup(try(local.global_config.inputs.iam_bindings, {}), k, []),
      lookup(try(local.lz_config.inputs.iam_bindings, {}), k, []),
      lookup(try(local.environment_config.inputs.iam_bindings, {}), k, []),
      lookup(try(local.instance_config.inputs.iam_bindings, {}), k, []),
      lookup(try(local.project_config_iam_bindings, {}), k, [])
    ))
  }
}
