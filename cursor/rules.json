{"rules": [{"name": "repository-analysis-first", "description": "ALWAYS analyze the repository for existing patterns, packages, and best practices BEFORE making any code changes", "steps": ["Search for existing implementations of similar functionality", "Check what packages/libraries are already being used", "Look for established patterns in the codebase", "Review existing utility functions and helpers", "Check for existing styling patterns and CSS classes", "Verify naming conventions and file organization", "Look for existing error handling patterns", "Check for existing state management patterns", "Review existing component structures and props", "Verify existing API patterns and data fetching approaches"]}, {"name": "minimal-changes", "description": "Make the MOST MINIMAL changes possible to achieve the desired outcome", "principles": ["Reuse existing code and patterns whenever possible", "Don't create new utilities if existing ones can be used", "Don't add new dependencies if existing ones suffice", "Don't refactor working code unless explicitly requested", "Prefer extending existing patterns over creating new ones", "Use existing CSS classes and styling approaches", "Leverage existing component structures", "Follow established naming conventions", "Use existing error handling patterns", "Maintain consistency with current codebase architecture"]}, {"name": "dependency-check", "description": "Before adding any new dependency or package, verify what's already available", "checks": ["Check package.json for existing dependencies", "Look for existing utility functions that provide similar functionality", "Search for existing patterns that solve the same problem", "Verify if the functionality already exists in the codebase", "Check if there are existing hooks or components that can be reused", "Look for existing configuration patterns", "Verify existing import patterns and module organization"]}, {"name": "pattern-consistency", "description": "Ensure all changes follow existing patterns in the codebase", "areas": ["File naming conventions", "Component structure and organization", "Import/export patterns", "Error handling approaches", "State management patterns", "Styling and CSS organization", "TypeScript type definitions", "Testing patterns and file organization", "API integration patterns", "Localization and internationalization"]}, {"name": "code-reuse", "description": "Prioritize reusing existing code over creating new implementations", "strategies": ["Search for existing utility functions before creating new ones", "Look for existing components that can be extended or reused", "Check for existing hooks that provide similar functionality", "Use existing CSS classes and styling patterns", "Leverage existing type definitions and interfaces", "Reuse existing error handling and validation patterns", "Use existing configuration and settings patterns", "Follow existing testing patterns and utilities"]}, {"name": "change-verification", "description": "Before implementing any change, verify it's the most minimal approach", "questions": ["Is this the smallest possible change to achieve the goal?", "Can I reuse existing code instead of creating new code?", "Are there existing patterns I should follow?", "Do I need to add new dependencies or can I use existing ones?", "Is this consistent with the existing codebase architecture?", "Can I extend existing functionality instead of creating new?", "Are there existing utilities or helpers I can leverage?", "Does this follow established naming and organization conventions?"]}], "enforcement": "STRICT - These rules must be followed for every code change", "priority": "HIGHEST - Repository analysis and minimal changes are the top priority"}