# This file is used for the downloadable starter files for TechDocs
# More information on the format of this file: https://backstage.io/docs/features/software-catalog/descriptor-format

# The 'apiVersion' and 'kind' fields are required and specify the version of the Backstage API and the kind of component respectively.
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: control-tower
  description: This repo contains the control tower monorepo application including api, ui, etl elements, etc.
  annotations:
    gitlab.com/project-slug: dematic/controltower/control-tower
    # The 'backstage.io/techdocs-ref' field indicates where the documentation for the component resides.
    # Indicates that the documentation resides in the same directory as the component.
    backstage.io/techdocs-ref: dir:.
  links:
    - url: https://dematic.com
      icon: dashboard
      title: Dematic website
  tags:
    - docs
    - documentation
spec:
  type: service
  lifecycle: production
  owner: controltower
