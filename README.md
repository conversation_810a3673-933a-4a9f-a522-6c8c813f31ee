## Control Tower

[![Lifecycle: stable](https://img.shields.io/badge/lifecycle-stable-brightgreen)](https://img.shields.io/badge/lifecycle-stable-brightgreen)

Control Tower is a comprehensive industrial control system monorepo that provides real-time operational visibility, analytics, and management capabilities for industrial facilities. The system enables monitoring of equipment performance, inventory tracking, order management, and operator activities through a modern web-based interface.

## Architecture Overview

Control Tower follows a modern microservices architecture with clear separation between frontend, backend, and infrastructure components:

### Core Components

- **Frontend**: React 19 application using IBM Carbon Design System with Vite build system
- **Backend**: Node.js 22+ microservices architecture with TypeScript, Express, and TSOA
- **Infrastructure**: Terraform/Terragrunt for Google Cloud Platform deployment
- **Testing**: Comprehensive E2E testing suites for both API and UI components

### Technology Stack

- **Backend**: Node.js 22+, TypeScript, Express, TSOA, TypeORM, PostgreSQL, Redis
- **Frontend**: React 19, Vite, IBM Carbon Design System, React Query, Zustand
- **Infrastructure**: <PERSON><PERSON>, <PERSON>g<PERSON>t, Google Cloud Platform
- **Testing**: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>
- **Monorepo**: Nx workspace for API services, Yarn workspaces

## Repository Structure

```bash
control-tower/
├── config/               # Configuration files
├── docs/                 # Documentation for Backstage TechDocs
├── environments/         # Environment-specific deployments
│   ├── dematicnonprod/   # Non-production environments
│   ├── dematicprod/      # Production environments
│   └── dematicops/       # Operations/shared services
├── infrastructure/       # Terraform modules and configurations
│   ├── modules/          # Reusable Terraform modules
│   └── examples/         # Example configurations
├── scripts/              # Utility scripts
└── services/             # Application services
    ├── api/              # Backend microservices (Node.js/TypeScript)
    ├── ui/               # Frontend React application
    ├── api-e2e/          # API end-to-end tests
    ├── ui-e2e/           # UI end-to-end tests
    └── metric-processor/ # Python-based metrics processing service

```

## API Services

The backend consists of specialized microservices for different operational domains:

- **ict-admin-api**: Administrative functions and user management
- **ict-ai-api**: AI/ML services and enterprise search
- **ict-config-api**: Application configuration and settings management
- **ict-dataexplorer-api**: Data exploration and analytics services
- **ict-diagnostics-api**: System diagnostics and health monitoring
- **ict-equipment-api**: Equipment monitoring and fault management
- **ict-instrumentation**: OpenTelemetry instrumentation and observability
- **ict-inventory-api**: Inventory tracking and management
- **ict-operators-api**: Operator management and area assignments
- **ict-orders-api**: Order management and processing
- **ict-simulation-api**: Simulation and modeling services
- **ict-tableau-management-api**: Tableau integration and management
- **ict-tableau-proxy**: Tableau proxy and CORS handling
- **ict-typeorm-migrations-cr**: Database migration management
- **ict-workstation-api**: Workstation performance and metrics
- **mock-data-api**: Mock data services for development and testing

## Quick Start

### Prerequisites

- Mise (recommended) - <https://mise.jdx.dev/getting-started.html>
- Node.js 22+
- Docker Desktop - <https://www.docker.com/get-started/>
- Yarn package manager

### Local Development Setup

1. **Install dependencies:**

   ```bash
   yarn install
   cd services/api
   yarn run setup
   ```

2. **Start local environment:**

   ```bash
   # Start all services with Docker
   cd services/api
   docker compose up
   
   # In separate terminal, setup database
   cd services/api
   yarn db:setup
   
   # Start UI development server
   cd services/ui
   yarn start
   ```

3. **Access the application:**
   - UI: <http://localhost:3000>
   - API Gateway: <http://localhost:8080>

### Development Commands

```bash
# Database operations (from services/api directory)
cd services/api
yarn db:setup        # Setup local database
yarn db:reset        # Reset database with fresh data
yarn db:generate     # Generate database schema

# API development (from services/api directory)
cd services/api
yarn run build       # Build all API services
yarn run watch       # Watch all API services
yarn run test        # Run all tests
yarn run lint        # Lint all services

# UI development (from services/ui directory)
cd services/ui
yarn start           # Start development server
yarn build:dev       # Development build
yarn test            # Run tests
yarn typecheck       # TypeScript type checking
```

## Key Features

### Operational Visibility

- Real-time equipment monitoring and fault detection
- Inventory tracking and accuracy reporting
- Order progress and throughput analytics
- Operator activity and area management

### System Management

- Multi-tenant configuration
- User role and permission management
- Infrastructure monitoring
- Automated deployment pipelines

## Documentation

- **API Documentation**: Available via OpenAPI/Swagger specifications
- **TechDocs**: Comprehensive documentation in `docs/`
- **Infrastructure**: Terraform module documentation in `infrastructure/modules/`
- **Development Guide**: Comprehensive development documentation in `docs/`

## Backstage Integration

This repository is configured for Backstage TechDocs with:

- Catalog registration via `catalog-info.yaml`
- MkDocs-based documentation in `docs/`
- MkDocs-based configuration in `mkdocs.yml`
- Automated documentation building and publishing

## Deployment

The system supports multiple deployment environments:

- **Development**: `dematicnonprod/dev/`
- **Staging**: `dematicnonprod/stage/`
- **Production**: `dematicprod/prod/`

Infrastructure is managed through Terraform/Terragrunt with automated CI/CD pipelines.

## Testing

Comprehensive testing strategy includes:

- **Unit Tests**: Jest for API services, Vitest for UI components
- **Integration Tests**: Service-to-service communication testing
- **E2E Tests**: Playwright for full user workflow testing
- **API Testing**: Bruno collections for manual/automated API testing

## Contributing

1. Follow the established Git commit convention (Conventional Commits)
2. Ensure all tests pass before submitting PRs
3. Update documentation for any new features
4. Follow the coding standards defined in the linting configuration

## Support

For development questions and support, refer to:

- Development documentation in `docs/`
- API service READMEs in `services/api/apps/api/`
- Infrastructure documentation in `infrastructure/modules/`

## License

Proprietary - Dematic Corporation
