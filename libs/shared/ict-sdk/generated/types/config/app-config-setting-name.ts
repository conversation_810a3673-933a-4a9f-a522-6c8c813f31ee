import {SystemSettingNames} from './system/system-setting-name';
import {DashboardSettingNames} from './dashboards/dashboard-setting-name';
import {FeatureFlagNames} from './feature-flags/feature-flag-name';
import {InventorySettingNames} from './inventory/inventory-setting-name';
import {WorkstationSettingNames} from './workstation/workstation-setting-name';
import {DataExplorerSettingNames} from './data-explorer/data-explorer-setting-name';
import {TableauSettingNames} from './tableau/tableau-setting-name';
import {OrdersSettingNames} from './orders/orders-setting-name';

// Combine all our feature settings into a single const.
export const AppConfigSettingNames = [
  ...SystemSettingNames,
  ...DashboardSettingNames,
  ...FeatureFlagNames,
  ...InventorySettingNames,
  ...OrdersSettingNames,
  ...WorkstationSettingNames,
  ...DataExplorerSettingNames,
  ...TableauSettingNames,
] as const;

export type AppConfigSettingName =
  | (typeof AppConfigSettingNames)[number]
  | (string & {});
