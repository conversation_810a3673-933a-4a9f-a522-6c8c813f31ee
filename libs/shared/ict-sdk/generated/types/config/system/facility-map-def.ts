export type FacilityMap = {
  id: string;
  name: string;
  default?: boolean;
  dataset: string;
  tableauProject?: string;
};

export const FacilityMapSchema = {
  $schema: 'http://json-schema.org/draft-07/schema#',
  title: 'facilities',
  type: 'array',
  items: {
    type: 'object',
    properties: {
      id: {
        type: 'string',
      },
      name: {
        type: 'string',
      },
      default: {
        type: 'boolean',
      },
      dataset: {
        type: 'string',
      },
      tableauProject: {
        type: 'string',
        description: 'Optional Tableau project name for this facility',
      },
    },
    required: ['id', 'name', 'dataset'],
    additionalProperties: false,
  },
};
