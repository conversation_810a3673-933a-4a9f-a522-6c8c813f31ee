export enum SecurityRoles {
  CT_ENGINEERS = 'ct_engineers',
  CT_CONFIGURATORS = 'ct_configurator',
  CT_TABLEAU_VIEWER = 'ct_tableau_viewer',
  CT_TABLEAU_EXPLORER = 'ct_tableau_explorer',
  CT_TABLEAU_ADMIN = 'ct_tableau_admin',
}

export const UserRoles = [
  'ct_engineers',
  'ct_configurator',
  'ct_tableau_viewer',
  'ct_tableau_explorer',
  'ct_tableau_admin',
] as const;

export type UserRole = (typeof UserRoles)[number];
