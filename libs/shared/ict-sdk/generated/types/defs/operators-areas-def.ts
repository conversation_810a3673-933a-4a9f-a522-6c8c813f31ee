import {AlertStatus} from './alert-status-def';
import {HealthStatus} from './health-status-def';

/**
 * DTO of the necessary base data to display a Facility Area.
 */
export interface AreaOperators {
  /**
   * Number of operators actively working in area.
   */
  activeOperators: number;
  /**
   * Lowest number of recommended operators for the area.
   */
  lowRecOperators: number;
  /**
   * Highest number of recommended operators for the area.
   */
  highRecOperators: number;
  /**
   * Max number of recommended operators for the area.
   */
  maxOperators?: number;
  /**
   * Alert status object.
   */
  alertStatus?: AlertStatus;
  /**
   * Number of orders in queue.
   */
  queuedOrders?: number;
  /**
   * Orders picked per operator per hour.
   */
  pickRate?: number;

  operatorStatus?: HealthStatus;
}
