/**
 * API Error Response format, following RFC 7807
 * https://datatracker.ietf.org/doc/html/rfc7807
 */
export interface IctApiError {
  /**
   * A short, human-readable summary of the problem type.
   */
  title: string;

  /**
   * The HTTP status code
   */
  status: number;

  /**
   * A human-readable explanation specific to this occurrence of the problem.
   */
  detail: string;

  /**
   * The timestamp when this error occurred - ISO 8601 string
   */
  timestamp?: string;

  /**
   * A URI reference that identifies the specific occurrence of the problem.
   */
  instance?: string;

  /**
   * The specific error type of this problem. This is a machine readable ID.
   */
  errorType?: string;

  /**
   * Additional attribute extensions
   */
  [attribute: string]: unknown;
}

export enum HttpStatusCodes {
  OK = 200,
  CREATED = 201,
  ACCEPTED = 202,
  NO_CONTENT = 204,
  NOT_MODIFIED = 304,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  UNPROCESSABLE = 422,
  TOO_MANY_REQUESTS = 429,
  INTERNAL_SERVER_ERROR = 500,
  NOT_IMPLEMENTED = 501,
  BAD_GATEWAY = 502,
}

export enum HttpStatusTitles {
  ACCEPTED = 'Accepted',
  BAD_GATEWAY = 'Bad Gateway',
  BAD_REQUEST = 'Bad Request',
  CONFLICT = 'Conflict',
  CREATED = 'Created',
  FORBIDDEN = 'Forbidden',
  INTERNAL_SERVER_ERROR = 'Internal Server Error',
  NO_CONTENT = 'No Content',
  NOT_FOUND = 'Not Found',
  NOT_IMPLEMENTED = 'Not Implemented',
  NOT_MODIFIED = 'Not Modified',
  OK = 'OK',
  TOO_MANY_REQUESTS = 'Too Many Requests',
  UNAUTHORIZED = 'Unauthorized',
  UNPROCESSABLE_REQUEST = 'Unprocessable Request',
}

export const DEFAULT_ERROR_MESSAGE =
  'The server encountered an unexpected condition that prevented it from fulfilling the request.';
