import {SortField} from './sort-fields';

export interface PaginatedRequest {
  /**
   * @isDateTime
   */
  start_date: Date;
  /**
   * @isDateTime
   */
  end_date: Date;
  filters?: unknown;
  /**
   * @isArray
   */
  sortFields?: SortField[];
  /**
   * @isInt
   * @minimum 0
   */
  page?: number;
  /**
   * @isInt
   * @minimum 1
   * @maximum 1000
   */
  limit?: number;
  /**
   * @isString
   */
  searchString?: string;
}
