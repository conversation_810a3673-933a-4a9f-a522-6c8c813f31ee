export interface BaseFilterType {
  type: 'multiple' | 'single';
}

export interface TreeFieldFilterObject extends BaseFilterType {
  andOr: 'AND' | 'OR';
  left: TreeFieldFilterObject | SingleFilterObject;
  right: TreeFieldFilterObject | SingleFilterObject;
}

export interface SingleFilterObject extends BaseFilterType {
  comparison: string;
  name: string;
  value: string;
}

export interface FilterObjectParamaterTypes {
  [key: string]: string[];
}

export interface FilterObjectParameters {
  [key: string]: unknown;
  counter: number;
  types?: FilterObjectParamaterTypes;
}
