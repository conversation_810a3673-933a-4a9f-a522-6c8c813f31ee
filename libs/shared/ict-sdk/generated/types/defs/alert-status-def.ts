import {HealthStatusValue} from './health-status-def';
/**
 * DTO of the necessary data to display an Alert Status.
 */
export interface AlertStatus {
  /**
   * Status of area.
   */
  status: HealthStatusValue;

  /**
   * Identifies the equipment, area, or location related to this alert
   * TODO: this field is currently optional, but once it is implemented for all endpoints it should be mandatory
   */
  identifier?: string;
}
