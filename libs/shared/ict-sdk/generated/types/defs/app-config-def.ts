import {AppConfigSetting} from '../config/index';

export interface AppConfig {
  api: AppConfigApi;
  dataUrls?: AppConfigDataUrls;
  settings?: AppConfigSettings;
  site: AppConfigSite;
  packages?: AppConfigPackage[];
  featureFlags?: AppConfigSetting[];
}

export interface AppConfigDataUrls {
  tableauProxy: string;
  tableauMovementsAndFaultsDashboard: string;
  tableauMovementsAndFaultsDetailsDashboard: string;
  tableauMovementsDashboard: string;
  tableauHistoricalMovementsAndFaultsDashboard: string;
  tableauShuttleHardwareAnalysis: string;
  mockApi: string;
  insightsSiteName: string;
}

export interface AppConfigSettings {
  logLevel: LogLevel;
}

export interface AppConfigSite {
  timezone: string;
}

export enum LogLevel {
  trace = 20,
  debug = 30,
  info = 40,
  warn = 50,
  error = 60,
  silly = 100,
  none = 1000,
}

export interface AppConfigApi {
  baseUrl: string;
  overrides?: AppConfigApiEndpointConfig[];
}

export interface AppConfigApiEndpointConfig {
  operationId: string;
  baseUrl: string;
}

/**
 * Interface used to set the menu group index (order) for the menu.
 */
export interface AppConfigPackage {
  /** The name of the package, which is used to dynamically load the package as it must match the folder name. */
  name: string;

  /** lag to set if the package is enabled. */
  enabled: boolean;

  /** an array of roles that match auth0 roles */
  requiredRoles?: string[];
}
