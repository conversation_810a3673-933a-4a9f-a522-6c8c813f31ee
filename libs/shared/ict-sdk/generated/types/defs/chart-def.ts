export interface BaseChartConfig {
  /** Min value for the y-axis */
  yMin?: number;
  /** Max value for the y-axis */
  yMax?: number;
  /** Value for the high range */
  yHighBand?: number;
  /** Value for the low range */
  yLowBand?: number;
  /** What the X-axis value should be formatted for display in the chart */
  xAxisValueFormatType?: string;
  /** What the Y-axis value should be formatted for display in the chart */
  yAxisValueFormatType?: string;
}

/** Config for a series in chart */
export interface SeriesConfig extends BaseChartConfig {
  type?: string;
  color?: string;
  icon?: string;
}

/**
 * Base chart series data
 */
export interface ChartSeriesData {
  name: string;
  value: number;
}

/**
 * Chart series data with percentage validation
 */
export interface ChartSeriesPercentageData extends ChartSeriesData {
  isValidPercentage: boolean;
}
