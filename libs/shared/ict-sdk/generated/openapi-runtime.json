{"openapi": "3.0.3", "info": {"title": "", "version": ""}, "servers": [{"url": "http://localhost:8080/"}], "paths": {"/ai/healthcheck": {"get": {"operationId": "GetAiBasicHealthCheck", "responses": {}, "parameters": []}}, "/ai/healthcheck/full": {"get": {"operationId": "GetAiFullHealthCheck", "responses": {}, "parameters": []}}, "/ai/enterprise-search": {"get": {"operationId": "GetAiEnterpriseSearch", "responses": {}, "parameters": [{"in": "query", "name": "searchText", "required": true}]}}, "/config/user-writable": {"put": {"operationId": "PutUserWritable", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/config/process-flow/metric-configs": {"get": {"operationId": "GetMetricConfigs", "responses": {}, "parameters": [{"in": "query", "name": "metricName", "required": false}, {"in": "query", "name": "metricId", "required": false}, {"in": "query", "name": "factType", "required": false}, {"in": "query", "name": "nodeName", "required": false}, {"in": "query", "name": "active", "required": false}, {"in": "query", "name": "enabled", "required": false}, {"in": "query", "name": "configType", "required": false}]}}, "/config/process-flow/metric-config": {"put": {"operationId": "PutConfigProcessFlowMetricConfig", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/config/healthcheck": {"get": {"operationId": "GetConfigBasicHealthCheck", "responses": {}, "parameters": []}}, "/config/healthcheck/full": {"get": {"operationId": "GetConfigFullHealthCheck", "responses": {}, "parameters": []}}, "/config/settings": {"get": {"operationId": "GetConfigSettings", "responses": {}, "parameters": [{"in": "query", "name": "settingId", "required": false}, {"in": "query", "name": "<PERSON><PERSON><PERSON>", "required": false}, {"in": "query", "name": "settingType", "required": false}, {"in": "query", "name": "allStoredValues", "required": false}, {"in": "query", "name": "group", "required": false}]}, "put": {"operationId": "PutConfigSettings", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/config/insert-default-config": {"post": {"operationId": "PostConfigDefaultConfig", "responses": {}, "parameters": []}}, "/config/settings/{settingId}": {"delete": {"operationId": "DeleteConfigSetting", "responses": {}, "parameters": [{"in": "path", "name": "settingId", "required": true}, {"in": "query", "name": "levelToDelete", "required": false}]}}, "/config/settings/schema": {"get": {"operationId": "getSettingSchema", "responses": {}, "parameters": [{"in": "query", "name": "settingId", "required": false}, {"in": "query", "name": "<PERSON><PERSON><PERSON>", "required": false}]}}, "/config/setting-logs": {"get": {"operationId": "GetConfigSettingLogs", "responses": {}, "parameters": [{"in": "query", "name": "setting_id", "required": true}, {"in": "query", "name": "limit", "required": false}]}}, "/config/facility-config": {"get": {"operationId": "GetConfigFacilityConfig", "responses": {}, "parameters": []}}, "/config/curated-data": {"get": {"operationId": "GetConfigCuratedData", "responses": {}, "deprecated": true, "parameters": [{"in": "query", "name": "table", "required": true}]}}, "/config/v2/curated-data": {"get": {"operationId": "GetConfigCuratedDataV2", "responses": {}, "parameters": [{"in": "query", "name": "table", "required": true}]}}, "/config/curated-tables/list": {"get": {"operationId": "GetConfigCuratedTablesList", "responses": {}, "parameters": []}}, "/config/app-config": {"get": {"operationId": "GetConfigAppConfig", "responses": {}, "parameters": [{"in": "query", "name": "config", "required": false}]}}, "/data-explorer/search": {"get": {"operationId": "GetDataExplorerSearch", "responses": {}, "parameters": [{"in": "query", "name": "searchText", "required": true}]}}, "/data-explorer/results": {"get": {"operationId": "GetDataExplorerResults", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}, {"in": "query", "name": "limit", "required": false}, {"in": "query", "name": "bookmark", "required": false}]}}, "/data-explorer/results/{resultId}": {"get": {"operationId": "GetDataExplorerResult", "responses": {}, "parameters": [{"in": "path", "name": "resultId", "required": true}]}, "delete": {"operationId": "DeleteDataExplorerResult", "responses": {}, "parameters": [{"in": "path", "name": "resultId", "required": true}]}, "put": {"operationId": "PutDataExplorerResult", "responses": {}, "parameters": [{"in": "path", "name": "resultId", "required": true}], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/data-explorer/recommendations": {"get": {"operationId": "GetDataExplorerRecommendations", "responses": {}, "parameters": []}}, "/data-explorer/gold-questions": {"get": {"operationId": "GetDematicChatGoldQuestions", "responses": {}, "parameters": []}}, "/data-explorer/healthcheck": {"get": {"operationId": "GetDataExplorerBasicHealthCheck", "responses": {}, "parameters": []}}, "/data-explorer/healthcheck/full": {"get": {"operationId": "GetDataExplorerFullHealthCheck", "responses": {}, "parameters": []}}, "/data-explorer/agentsearch": {"get": {"operationId": "GetDataExplorerAgentSearch", "responses": {}, "parameters": [{"in": "query", "name": "searchText", "required": true}, {"in": "query", "name": "sessionId", "required": false}]}}, "/diagnostics/infrastructure/list": {"get": {"operationId": "GetDiagnosticsInfrastructureList", "responses": {}, "parameters": []}}, "/diagnostics/healthcheck": {"get": {"operationId": "GetDiagnosticsBasicHealthCheck", "responses": {}, "parameters": []}}, "/diagnostics/healthcheck/full": {"get": {"operationId": "GetDiagnosticsFullHealthCheck", "responses": {}, "parameters": []}}, "/inventory/wms/stock/distribution/under/percentage/series": {"get": {"operationId": "GetInventoryWmsStockDistributionUnderPercentageSeries", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/inventory/wms/stock/distribution/over/percentage/series": {"get": {"operationId": "GetInventoryWmsStockDistributionOverPercentageSeries", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/inventory/wms/stock/distribution/no/percentage/series": {"get": {"operationId": "GetInventoryWmsStockDistributionNoPercentageSeries", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/inventory/wms/stock/distribution/at/percentage/series": {"get": {"operationId": "GetInventoryWmsStockDistributionAtPercentageSeries", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/inventory/wms/skus/list": {"post": {"operationId": "PostInventoryWmsSkusList", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/inventory/wms/skus/list/export": {"post": {"operationId": "PostInventoryWmsSkusListExport", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/inventory/upload/known-demand": {"post": {"operationId": "PostInventoryUploadKnownDemand", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"multipart/form-data": {}}}}}, "/inventory/upload/recent-activity": {"get": {"operationId": "GetInventoryUploadRecentActivity", "responses": {}, "parameters": []}}, "/inventory/storage/utilization": {"get": {"operationId": "GetInventoryStorageUtilization", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": false, "deprecated": true}, {"in": "query", "name": "end_date", "required": false, "deprecated": true}]}}, "/inventory/stock/distribution/{distributionType}/percentage/series": {"get": {"operationId": "GetInventoryStockDistributionPercentageSeries", "responses": {}, "parameters": [{"in": "path", "name": "distributionType", "required": true}, {"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/inventory/replenishment/task-type-series": {"get": {"operationId": "GetInventoryReplenishmentTaskTypeSeries", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/inventory/replenishment/details": {"get": {"operationId": "GetInventoryReplenishmentDetails", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/inventory/process-flow/areas": {"get": {"operationId": "GetInventoryProcessFlowAreas", "responses": {}, "parameters": []}}, "/inventory/process-flow/area/{areaId}": {"get": {"operationId": "GetInventoryProcessFlowAreaById", "responses": {}, "parameters": [{"in": "path", "name": "areaId", "required": true}]}}, "/inventory/process-flow/details/{type}/{elementId}": {"get": {"operationId": "GetInventoryProcessFlowGraphDetails", "responses": {}, "parameters": [{"in": "path", "name": "type", "required": true}, {"in": "path", "name": "elementId", "required": true}, {"in": "query", "name": "view", "required": false}]}}, "/inventory/process-flow/areas/{areaId}": {"put": {"operationId": "UpdateInventoryProcessFlowArea", "responses": {}, "parameters": [{"in": "path", "name": "areaId", "required": true}, {"in": "query", "name": "view", "required": false}], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/inventory/process-flow/edges/{edgeId}": {"put": {"operationId": "UpdateInventoryProcessFlowEdge", "responses": {}, "parameters": [{"in": "path", "name": "edgeId", "required": true}], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/inventory/process-flow/metrics/{metricId}": {"put": {"operationId": "UpdateInventoryProcessFlowMetric", "responses": {}, "parameters": [{"in": "path", "name": "metricId", "required": true}], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/inventory/process-flow/clear-cache": {"post": {"operationId": "ClearInventoryProcessFlowCache", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/inventory/performance/series": {"get": {"operationId": "GetInventoryPerformanceSeries", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}, {"in": "query", "name": "department_filter", "required": true}]}}, "/inventory/sku/high-impact/list": {"post": {"operationId": "PostInventorySkuHighImpactList", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/inventory/healthcheck": {"get": {"operationId": "GetInventoryBasicHealthCheck", "responses": {}, "parameters": []}}, "/inventory/healthcheck/full": {"get": {"operationId": "GetInventoryFullHealthCheck", "responses": {}, "parameters": []}}, "/inventory/handling-units/trayed": {"get": {"operationId": "GetInventoryHandlingUnitsTrayed", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/inventory/forecast/data-analysis-timestamp": {"get": {"operationId": "GetDataAnalysisTimestampData", "responses": {}, "parameters": []}}, "/inventory/forecast/list": {"post": {"operationId": "PostInventoryForecastList", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/inventory/forecast/list/export": {"post": {"operationId": "PostInventoryForecastListExport", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/inventory/forecast/{skuId}/locations": {"get": {"operationId": "GetInventoryForecastSkuLocations", "responses": {}, "parameters": [{"in": "path", "name": "skuId", "required": true}]}}, "/inventory/forecast/{skuId}/orders": {"get": {"operationId": "GetInventoryForecastSkuOrders", "responses": {}, "parameters": [{"in": "path", "name": "skuId", "required": true}]}}, "/inventory/forecast/{skuId}": {"get": {"operationId": "GetInventorySkuForecast", "responses": {}, "parameters": [{"in": "path", "name": "skuId", "required": true}]}}, "/inventory/filter": {"get": {"operationId": "GetInventoryFilter", "responses": {}, "parameters": []}}, "/inventory/facility/skus/list": {"post": {"operationId": "PostInventoryFacilitySkusList", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/inventory/facility/skus/list/export": {"post": {"operationId": "PostInventoryFacilitySkusListExport", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/inventory/containers/list": {"post": {"operationId": "PostInventoryContainersList", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/inventory/containers/list/export": {"post": {"operationId": "PostInventoryContainersListExport", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/inventory/wms/containers/list": {"post": {"operationId": "PostInventoryWMSContainersList", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/inventory/wms/containers/list/export": {"post": {"operationId": "PostInventoryWMSContainersListExport", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/inventory/container-events/list/{containerId}": {"post": {"operationId": "PostInventoryContainerEventsList", "responses": {}, "parameters": [{"in": "path", "name": "containerId", "required": true}], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/inventory/container-events/list/export/{containerId}": {"post": {"operationId": "PostInventoryContainerEventsListExport", "responses": {}, "parameters": [{"in": "path", "name": "containerId", "required": true}], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/inventory/bin-locations": {"get": {"operationId": "GetInventoryBinLocations", "responses": {}, "parameters": [{"in": "query", "name": "aisle", "required": true}, {"in": "query", "name": "level", "required": true}]}}, "/inventory/advices/outstanding": {"get": {"operationId": "GetInventoryAdvicesOutstanding", "responses": {}, "parameters": []}}, "/inventory/advices/list": {"get": {"operationId": "GetInventoryAdvicesList", "responses": {}, "parameters": []}}, "/inventory/advices/in-progress": {"get": {"operationId": "GetInventoryAdvicesInProgress", "responses": {}, "parameters": []}}, "/inventory/advices/finished": {"get": {"operationId": "GetInventoryAdvicesFinished", "responses": {}, "parameters": []}}, "/inventory/advices/{adviceId}/details": {"get": {"operationId": "GetInventoryAdvicesDetails", "responses": {}, "parameters": [{"in": "path", "name": "adviceId", "required": true}]}}, "/inventory/advices/cycle-time": {"get": {"operationId": "GetInventoryAdvicesCycleTime", "responses": {}, "parameters": []}}, "/inventory/accuracy": {"get": {"operationId": "GetInventoryAccuracy", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}, {"in": "query", "name": "area_filter", "required": false}]}}, "/inventory/container-events/{containerId}": {"get": {"operationId": "GetInventoryContainerEventsDetails", "responses": {}, "parameters": [{"in": "path", "name": "containerId", "required": true}]}}, "/inventory/wms/container-events/{containerId}": {"get": {"operationId": "GetInventoryWMSContainerEventsDetails", "responses": {}, "parameters": [{"in": "path", "name": "containerId", "required": true}]}}, "/equipment/workstation/{workstationId}/starved-blocked-time/series": {"get": {"operationId": "GetEquipmentWorkstationStarvedBlockedTimeSeries", "responses": {}, "parameters": [{"in": "path", "name": "workstationId", "required": true}]}}, "/equipment/workstation/{workstationId}/operator/activity": {"get": {"operationId": "GetEquipmentWorkstationOperatorActivity", "responses": {}, "parameters": [{"in": "path", "name": "workstationId", "required": true}]}}, "/equipment/workstation/{workstationId}/line-rates/series": {"get": {"operationId": "GetEquipmentWorkstationLineRatesSeries", "responses": {}, "parameters": [{"in": "path", "name": "workstationId", "required": true}]}}, "/equipment/workstation/{workstationId}/movements/detail": {"get": {"operationId": "GetEquipmentWorkstationMovementsDetail", "responses": {}, "parameters": [{"in": "path", "name": "workstationId", "required": true}]}}, "/equipment/workstation/{workstationId}/aisle/active-faults": {"get": {"operationId": "GetEquipmentWorkstationAisleActiveFaults", "responses": {}, "parameters": [{"in": "path", "name": "workstationId", "required": true}]}}, "/equipment/summary/workstations/{workstationId}": {"get": {"operationId": "GetEquipmentSummaryWorkstations", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}, {"in": "path", "name": "workstationId", "required": true}]}}, "/equipment/summary/areas": {"get": {"operationId": "GetEquipmentSummaryAreas", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/equipment/summary/aisles/{aisleId}": {"get": {"operationId": "GetEquipmentSummaryAisle", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}, {"in": "path", "name": "aisleId", "required": true}]}}, "/equipment/events/list": {"post": {"operationId": "PostEquipmentEventsList", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/equipment/outbound-rate": {"get": {"operationId": "GetEquipmentOutboundRate", "responses": {}, "parameters": []}}, "/equipment/healthcheck": {"get": {"operationId": "GetEquipmentBasicHealthCheck", "responses": {}, "parameters": []}}, "/equipment/healthcheck/full": {"get": {"operationId": "GetEquipmentFullHealthCheck", "responses": {}, "parameters": []}}, "/equipment/faults/status/list": {"post": {"operationId": "PostEquipmentFaultsStatusList", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/equipment/faults/series": {"get": {"operationId": "GetEquipmentFaultsSeries", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/equipment/faults/events": {"get": {"operationId": "GetEquipmentFaultsEvents", "responses": {}, "parameters": []}}, "/equipment/faults/movements/series": {"post": {"operationId": "PostEquipmentFaultsMovementsSeries", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/equipment/faults/list": {"post": {"operationId": "PostEquipmentFaultsList", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/equipment/faults/level/list": {"post": {"operationId": "PostEquipmentFaultsLevelList", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/equipment/faults/grouped/count/series": {"post": {"operationId": "getFaultGroupedByCounts", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/equipment/faults/device-id/list": {"post": {"operationId": "PostEquipmentFaultsDeviceIdsList", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/equipment/faults/device-type/list": {"post": {"operationId": "PostEquipmentFaultsDeviceTypeList", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/equipment/faults": {"get": {"operationId": "GetEquipmentFaults", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/equipment/faults/average/duration/status/series": {"post": {"operationId": "getFaultAvgDurationByStatus", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/equipment/faults/area": {"get": {"operationId": "GetEquipmentFaultsArea", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/equipment/faults/aisle/list": {"post": {"operationId": "PostEquipmentFaultsAisleList", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/equipment/faults/active/list": {"get": {"operationId": "GetEquipmentFaultsActiveList", "responses": {}, "parameters": []}}, "/operators/healthcheck": {"get": {"operationId": "GetOperatorsBasicHealthCheck", "responses": {}, "parameters": []}}, "/operators/healthcheck/full": {"get": {"operationId": "GetOperatorsFullHealthCheck", "responses": {}, "parameters": []}}, "/operators/areas": {"put": {"operationId": "PutOperatorsAreas", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/operators/active/series": {"get": {"operationId": "GetOperatorsActiveSeries", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/operators/active": {"get": {"operationId": "GetOperatorsActive", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/operators/active/areas": {"get": {"operationId": "GetOperatorsActiveAreas", "responses": {}, "parameters": []}}, "/orders/shipped": {"get": {"operationId": "GetPickOrdersShipped", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/pick/cycle-count": {"get": {"operationId": "GetOrdersPickCycleCount", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/facility/shipped": {"get": {"operationId": "GetOrdersFacilityShipped", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/facility/progress": {"get": {"operationId": "GetOrdersFacilityProgress", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/facility/completion": {"get": {"operationId": "GetOrdersFacilityEstimatedCompletion", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/customer/shipped": {"get": {"operationId": "GetOrdersCustomerShipped", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/throughput": {"get": {"operationId": "GetOrdersThroughput", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}, {"in": "query", "name": "area", "required": false}]}}, "/orders/throughput/series": {"get": {"operationId": "GetOrdersThroughputSeries", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/remaining": {"get": {"operationId": "GetOrdersRemaining", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/customer/fulfillment": {"get": {"operationId": "GetCustomerOrdersFulfillment", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/pick/progress/series": {"get": {"operationId": "GetOrdersProgressSeries", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/pick/line/throughput/series": {"get": {"operationId": "GetOrdersPickLineThroughputSeries", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/lineprogress": {"get": {"operationId": "GetOrdersLineProgress", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/performance/fulfillment": {"get": {"operationId": "GetOrdersPerformanceFulfillment", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}, {"in": "query", "name": "department_filter", "required": true}]}}, "/orders/line/throughput": {"get": {"operationId": "GetOrdersLineThroughput", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/lineprogress/series": {"get": {"operationId": "GetOrdersLineProgressSeries", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/healthcheck": {"get": {"operationId": "GetOrdersBasicHealthCheck", "responses": {}, "parameters": []}}, "/orders/healthcheck/full": {"get": {"operationId": "GetOrdersFullHealthCheck", "responses": {}, "parameters": []}}, "/orders/fulfillment-outstanding": {"get": {"operationId": "GetOrdersFulfillmentOutstanding", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/facility/outstanding": {"get": {"operationId": "GetOrdersFacilityOutstanding", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/pick/cycletime": {"get": {"operationId": "GetOrdersPickCycleTime", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/cycletime/series": {"get": {"operationId": "GetOrdersCycleTimeSeries", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/progress": {"get": {"operationId": "GetOrdersProgress", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/customer/line/throughput": {"get": {"operationId": "GetOrdersCustomerLineThroughput", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}, {"in": "query", "name": "area", "required": false}]}}, "/orders/throughput/areas": {"get": {"operationId": "GetOrdersThroughputAreas", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}, {"in": "query", "name": "area", "required": false}]}}, "/orders/lineprogress/areas": {"get": {"operationId": "GetOrdersLineProgressAreas", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/cycletime/areas": {"get": {"operationId": "GetOrdersCycleTime", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/facility/throughput": {"get": {"operationId": "GetOrdersFacilityThroughput", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/facility/fulfillment": {"get": {"operationId": "GetFacilityOrdersFulfillment", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/facility/line/throughput/series": {"get": {"operationId": "GetOrdersFacilityLineThroughputSeries", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/facility/line/progress/series": {"get": {"operationId": "GetOrderLinesFacilityProgressSeries", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/facility/line/progress": {"get": {"operationId": "GetOrdersFacilityLineProgress", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/facility/cycletime": {"get": {"operationId": "GetOrdersFacilityCycleTime", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/completion": {"get": {"operationId": "GetOrdersCompletion", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": false}]}}, "/orders/customer/throughput": {"get": {"operationId": "GetOrdersCustomerThroughput", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}, {"in": "query", "name": "area", "required": false}]}}, "/orders/customer/throughput/series": {"get": {"operationId": "GetOrdersCustomerThroughputSeries", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/customer/progress/series": {"get": {"operationId": "GetOrdersCustomerProgressSeries", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}, {"in": "query", "name": "area", "required": false}]}}, "/orders/customer/progress": {"get": {"operationId": "GetOrdersCustomerProgress", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/customer/line/throughput/series": {"get": {"operationId": "GetOrdersCustomerLineThroughputSeries", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/customer/line/progress/series": {"get": {"operationId": "GetOrderLinesCustomerProgressSeries", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}, {"in": "query", "name": "area", "required": false}]}}, "/orders/customer/line/progress": {"get": {"operationId": "GetOrdersCustomerLineProgress", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}, {"in": "query", "name": "area", "required": false}]}}, "/orders/customer/completion": {"get": {"operationId": "GetOrdersCustomerEstimatedCompletion", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/orders/customer/cycletime": {"get": {"operationId": "GetOrdersCustomerCycleTime", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}, {"in": "query", "name": "area", "required": false}]}}, "/workstation/workstations": {"get": {"operationId": "GetWorkstations", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": false}, {"in": "query", "name": "end_date", "required": false}]}}, "/workstation/series": {"get": {"operationId": "GetWorkstationSeriesData", "responses": {}, "parameters": [{"in": "query", "name": "chart", "required": true}]}}, "/workstation/orders/status": {"get": {"operationId": "GetWorkstationOrdersStatus", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/workstation/orders/list": {"post": {"operationId": "PostWorkstationOrdersList", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/workstation/orders/list/export": {"post": {"operationId": "PostWorkstationOrdersListExport", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/workstation/orders/{orderId}/picks/list": {"post": {"operationId": "PostWorkstationOrdersDetailsPicksList", "responses": {}, "parameters": [{"in": "path", "name": "orderId", "required": true}, {"in": "query", "name": "zone", "required": false}, {"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/workstation/orders/{orderId}/picks/list/export": {"post": {"operationId": "PostWorkstationOrdersDetailsPicksListExport", "responses": {}, "parameters": [{"in": "path", "name": "orderId", "required": true}, {"in": "query", "name": "zone", "required": false}, {"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/workstation/metrics/summary": {"get": {"operationId": "GetWorkstationSummary", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/workstation/metrics/operators": {"get": {"operationId": "GetWorkstationOperatorSummary", "responses": {}, "parameters": []}}, "/workstation/metrics/performance": {"get": {"operationId": "GetWorkstationPerformanceSummary", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/workstation/metrics/health": {"get": {"operationId": "GetWorkstationHealthSummary", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}]}}, "/workstation/healthcheck": {"get": {"operationId": "GetWorkstationBasicHealthCheck", "responses": {}, "parameters": []}}, "/workstation/healthcheck/full": {"get": {"operationId": "GetWorkstationFullHealthCheck", "responses": {}, "parameters": []}}, "/workstation/daily-performance/list": {"post": {"operationId": "PostWorkstationDailyPerformanceList", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/workstation/list": {"get": {"operationId": "GetWorkstationList", "responses": {}, "parameters": []}}, "/workstation/list/export": {"post": {"operationId": "PostWorkstationListExport", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/workstation/containers/list": {"post": {"operationId": "PostWorkstationContainersList", "responses": {}, "parameters": [{"in": "query", "name": "start_date", "required": true}, {"in": "query", "name": "end_date", "required": true}, {"in": "query", "name": "size", "required": true}, {"in": "query", "name": "style", "required": true}, {"in": "query", "name": "zone", "required": true}], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/simulation/job/{jobId}/{taskIndex}/{fileName}/{fileType}/output": {"get": {"operationId": "GetSimulationOutputByJobId", "responses": {}, "parameters": [{"in": "path", "name": "jobId", "required": true}, {"in": "path", "name": "taskIndex", "required": true}, {"in": "path", "name": "fileName", "required": true}, {"in": "path", "name": "fileType", "required": true}]}}, "/simulation/jobs/list": {"get": {"operationId": "GetSimulationJobs", "responses": {}, "parameters": []}}, "/simulation/healthcheck": {"get": {"operationId": "GetSimulationBasicHealthCheck", "responses": {}, "parameters": []}}, "/simulation/healthcheck/full": {"get": {"operationId": "GetSimulationFullHealthCheck", "responses": {}, "parameters": []}}, "/management/auth/trusted": {"get": {"operationId": "GetTrustedTicket", "responses": {}, "parameters": []}}, "/admin/healthcheck": {"get": {"operationId": "GetAdminBasicHealthCheck", "responses": {}, "parameters": []}}, "/admin/healthcheck/full": {"get": {"operationId": "GetAdminFullHealthCheck", "responses": {}, "parameters": []}}, "/admin/auth/resend-verification": {"post": {"operationId": "ResendEmailVerification", "responses": {}, "parameters": []}}, "/admin/auth/roles": {"get": {"operationId": "GetAssignableRoles", "responses": {}, "parameters": []}}, "/admin/auth/users/list": {"post": {"operationId": "GetUsersList", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/admin/auth/users/{id}": {"get": {"operationId": "GetUser", "responses": {}, "parameters": [{"in": "path", "name": "id", "required": true}]}}, "/admin/auth/users/{userId}/roles": {"post": {"operationId": "AssignUserRoles", "responses": {}, "parameters": [{"in": "path", "name": "userId", "required": true}], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/admin/auth/users/{userId}/roles/{roleName}": {"delete": {"operationId": "RemoveUserRole", "responses": {}, "parameters": [{"in": "path", "name": "userId", "required": true}, {"in": "path", "name": "<PERSON><PERSON><PERSON>", "required": true}]}}, "/availability/sections/list": {"get": {"operationId": "GetAvailableSections", "responses": {}, "parameters": [{"in": "query", "name": "offset", "required": false}, {"in": "query", "name": "limit", "required": false}, {"in": "query", "name": "searchTerm", "required": false}]}}, "/availability/healthcheck": {"get": {"operationId": "GetAdminBasicHealthCheck1", "responses": {}, "parameters": []}}, "/availability/healthcheck/full": {"get": {"operationId": "GetAvailabilityFullHealthCheck", "responses": {}, "parameters": []}}, "/availability/equipment/names/list": {"get": {"operationId": "GetAvailableEquipmentNames", "responses": {}, "parameters": [{"in": "query", "name": "offset", "required": false}, {"in": "query", "name": "limit", "required": false}, {"in": "query", "name": "searchTerm", "required": false}]}}, "/availability/alarms/list": {"post": {"operationId": "PostAlarmsList", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}, "/availability/alarms/{id}": {"get": {"operationId": "GetAlarmById", "responses": {}, "parameters": [{"in": "path", "name": "id", "required": true}]}}, "/availability/alarms": {"post": {"operationId": "PostAlarms", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}, "put": {"operationId": "PutAlarms", "responses": {}, "parameters": [], "requestBody": {"required": true, "content": {"application/json": {}}}}}}, "components": {"responses": {}, "parameters": {}, "requestBodies": {}, "headers": {}}}